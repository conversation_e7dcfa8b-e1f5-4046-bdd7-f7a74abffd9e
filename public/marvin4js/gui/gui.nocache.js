function gui(){var U='',R=' top: -1000px;',pb='" for "gwt:onLoadErrorFn"',nb='" for "gwt:onPropertyErrorFn"',$='");',qb='#',Xb='.cache.js',sb='/',yb='//',Ob='1217368FF753A2483776A37E82E23888',Rb='1C58A42C12BBA4BDDECDC6CE205DA602',Wb=':',Qb=':1',hb='::',T='<!doctype html>',V='<html><head><\/head><body><\/body><\/html>',kb='=',rb='?',mb='Bad handler "',Sb='C6B917D19DADDC444DA0DD27B092CAE0',S='CSS1Compat',Y='Chrome',X='DOMContentLoaded',M='DUMMY',Tb='E4004AB39023E920F3F2E5A166D166BA',Ub='EC4109B16D44C9BB78A753930C311333',Vb='EE2AFCF8D70230D6A9EFC17F59C99E25',xb='base',vb='baseUrl',H='begin',N='body',G='bootstrap',ub='clear.cache.gif',dc='common/canvas.css',gc='common/error.css',cc='common/global.css',fc='common/periodicsystem.css',ec='common/toolbar.css',jb='content',Ab='default',hc='end',Z='eval("',Pb='foobar',Jb='gecko',Kb='gecko1_8',K='gui',Nb='gui.devmode.js',wb='gui.nocache.js',gb='gui::',I='gwt.codesvr.gui=',J='gwt.codesvr=',bc='gwt/clean/clean.css',ob='gwt:onLoadErrorFn',lb='gwt:onPropertyErrorFn',ib='gwt:property',db='head',_b='href',Ib='ie6',Hb='ie8',Gb='ie9',O='iframe',tb='img',ab='javascript',P='javascript:""',Yb='link',ac='loadExternalRefs',eb='meta',cb='moduleRequested',bb='moduleStartup',Fb='msie',fb='name',zb='onLoad',Cb='opera',Q='position:absolute; width:0; height:0; border:none; left: -1000px;',Zb='rel',Eb='safari',_='script',Mb='selectingPermutation',L='startup',$b='stylesheet',W='undefined',Lb='unknown',Bb='user.agent',Db='webkit';var o=window;var p=document;r(G,H);function q(){var a=o.location.search;return a.indexOf(I)!=-1||a.indexOf(J)!=-1}
function r(a,b){if(o.__gwtStatsEvent){o.__gwtStatsEvent({moduleName:K,sessionId:o.__gwtStatsSessionId,subSystem:L,evtGroup:a,millis:(new Date).getTime(),type:b})}}
gui.__sendStats=r;gui.__moduleName=K;gui.__errFn=null;gui.__moduleBase=M;gui.__softPermutationId=0;gui.__computePropValue=null;gui.__getPropMap=null;gui.__gwtInstallCode=function(){};gui.__gwtStartLoadingFragment=function(){return null};var s=function(){return false};var t=function(){return null};__propertyErrorFunction=null;var u=o.__gwt_activeModules=o.__gwt_activeModules||{};u[K]={moduleName:K};var v;function w(){y();return v}
function x(){y();return v.getElementsByTagName(N)[0]}
function y(){if(v){return}var a=p.createElement(O);a.src=P;a.id=K;a.style.cssText=Q+R;a.tabIndex=-1;p.body.appendChild(a);v=a.contentDocument;if(!v){v=a.contentWindow.document}v.open();var b=document.compatMode==S?T:U;v.write(b+V);v.close()}
function z(k){function l(a){function b(){if(typeof p.readyState==W){return typeof p.body!=W&&p.body!=null}return /loaded|complete/.test(p.readyState)}
var c=b();if(c){a();return}function d(){if(!c){c=true;a();if(p.removeEventListener){p.removeEventListener(X,d,false)}if(e){clearInterval(e)}}}
if(p.addEventListener){p.addEventListener(X,d,false)}var e=setInterval(function(){if(b()){d()}},50)}
function m(c){function d(a,b){a.removeChild(b)}
var e=x();var f=w();var g;if(navigator.userAgent.indexOf(Y)>-1&&window.JSON){var h=f.createDocumentFragment();h.appendChild(f.createTextNode(Z));for(var i=0;i<c.length;i++){var j=window.JSON.stringify(c[i]);h.appendChild(f.createTextNode(j.substring(1,j.length-1)))}h.appendChild(f.createTextNode($));g=f.createElement(_);g.language=ab;g.appendChild(h);e.appendChild(g);d(e,g)}else{for(var i=0;i<c.length;i++){g=f.createElement(_);g.language=ab;g.text=c[i];e.appendChild(g);d(e,g)}}}
gui.onScriptDownloaded=function(a){l(function(){m(a)})};r(bb,cb);var n=p.createElement(_);n.src=k;p.getElementsByTagName(db)[0].appendChild(n)}
gui.__startLoadingFragment=function(a){return C(a)};gui.__installRunAsyncCode=function(a){var b=x();var c=w().createElement(_);c.language=ab;c.text=a;b.appendChild(c);b.removeChild(c)};function A(){var c={};var d;var e;var f=p.getElementsByTagName(eb);for(var g=0,h=f.length;g<h;++g){var i=f[g],j=i.getAttribute(fb),k;if(j){j=j.replace(gb,U);if(j.indexOf(hb)>=0){continue}if(j==ib){k=i.getAttribute(jb);if(k){var l,m=k.indexOf(kb);if(m>=0){j=k.substring(0,m);l=k.substring(m+1)}else{j=k;l=U}c[j]=l}}else if(j==lb){k=i.getAttribute(jb);if(k){try{d=eval(k)}catch(a){alert(mb+k+nb)}}}else if(j==ob){k=i.getAttribute(jb);if(k){try{e=eval(k)}catch(a){alert(mb+k+pb)}}}}}t=function(a){var b=c[a];return b==null?null:b};__propertyErrorFunction=d;gui.__errFn=e}
function B(){function e(a){var b=a.lastIndexOf(qb);if(b==-1){b=a.length}var c=a.indexOf(rb);if(c==-1){c=a.length}var d=a.lastIndexOf(sb,Math.min(c,b));return d>=0?a.substring(0,d+1):U}
function f(a){if(a.match(/^\w+:\/\//)){}else{var b=p.createElement(tb);b.src=a+ub;a=e(b.src)}return a}
function g(){var a=t(vb);if(a!=null){return a}return U}
function h(){var a=p.getElementsByTagName(_);for(var b=0;b<a.length;++b){if(a[b].src.indexOf(wb)!=-1){return e(a[b].src)}}return U}
function i(){var a=p.getElementsByTagName(xb);if(a.length>0){return a[a.length-1].href}return U}
function j(){var a=p.location;return a.href==a.protocol+yb+a.host+a.pathname+a.search+a.hash}
var k=g();if(k==U){k=h()}if(k==U){k=i()}if(k==U&&j()){k=e(p.location.href)}k=f(k);return k}
function C(a){if(a.match(/^\//)){return a}if(a.match(/^[a-zA-Z]+:\/\//)){return a}return gui.__moduleBase+a}
function D(){var f=[];var g;function h(a,b){var c=f;for(var d=0,e=a.length-1;d<e;++d){c=c[a[d]]||(c[a[d]]=[])}c[a[e]]=b}
var i=[];var j=[];function k(a){var b=j[a](),c=i[a];if(b in c){return b}var d=[];for(var e in c){d[c[e]]=e}if(__propertyErrorFunc){__propertyErrorFunc(a,d,b)}throw null}
j[zb]=function(){o.marvin={onLoadArray:[],onLoad:function(){if(!o.marvin.onLoadArray){return}for(var a=0;a<o.marvin.onLoadArray.length;a++)o.marvin.onLoadArray[a]();delete o.marvin.onLoadArray},onReady:function(a){o.marvin.onLoadArray?o.marvin.onLoadArray.push(a):a()}};return Ab};i[zb]={'default':0,foobar:1};j[Bb]=function(){var b=navigator.userAgent.toLowerCase();var c=function(a){return parseInt(a[1])*1000+parseInt(a[2])};if(function(){return b.indexOf(Cb)!=-1}())return Cb;if(function(){return b.indexOf(Db)!=-1}())return Eb;if(function(){return b.indexOf(Fb)!=-1&&p.documentMode>=9}())return Gb;if(function(){return b.indexOf(Fb)!=-1&&p.documentMode>=8}())return Hb;if(function(){var a=/msie ([0-9]+)\.([0-9]+)/.exec(b);if(a&&a.length==3)return c(a)>=6000}())return Ib;if(function(){return b.indexOf(Jb)!=-1}())return Kb;return Lb};i[Bb]={gecko1_8:0,ie6:1,ie8:2,ie9:3,opera:4,safari:5};s=function(a,b){return b in i[a]};gui.__getPropMap=function(){var a={};for(var b in i){if(i.hasOwnProperty(b)){a[b]=k(b)}}return a};gui.__computePropValue=k;o.__gwt_activeModules[K].bindings=gui.__getPropMap;r(G,Mb);if(q()){return C(Nb)}var l;try{h([Ab,Hb],Ob);h([Pb,Hb],Ob);h([Ab,Hb],Ob+Qb);h([Pb,Hb],Ob+Qb);h([Ab,Kb],Rb);h([Pb,Kb],Rb);h([Ab,Kb],Rb+Qb);h([Pb,Kb],Rb+Qb);h([Ab,Gb],Sb);h([Pb,Gb],Sb);h([Ab,Gb],Sb+Qb);h([Pb,Gb],Sb+Qb);h([Ab,Cb],Tb);h([Pb,Cb],Tb);h([Ab,Cb],Tb+Qb);h([Pb,Cb],Tb+Qb);h([Ab,Eb],Ub);h([Pb,Eb],Ub);h([Ab,Eb],Ub+Qb);h([Pb,Eb],Ub+Qb);h([Ab,Ib],Vb);h([Pb,Ib],Vb);h([Ab,Ib],Vb+Qb);h([Pb,Ib],Vb+Qb);l=f[k(zb)][k(Bb)];var m=l.indexOf(Wb);if(m!=-1){g=parseInt(l.substring(m+1),10);l=l.substring(0,m)}}catch(a){}gui.__softPermutationId=g;return C(l+Xb)}
function E(){if(!o.__gwt_stylesLoaded){o.__gwt_stylesLoaded={}}function c(a){if(!__gwt_stylesLoaded[a]){var b=p.createElement(Yb);b.setAttribute(Zb,$b);b.setAttribute(_b,C(a));p.getElementsByTagName(db)[0].appendChild(b);__gwt_stylesLoaded[a]=true}}
r(ac,H);c(bc);c(cc);c(dc);c(ec);c(fc);c(gc);r(ac,hc)}
A();gui.__moduleBase=B();u[K].moduleBase=gui.__moduleBase;var F=D();E();r(G,hc);z(F);return true}
gui.succeeded=gui();