.periodicsystem .gwt-ToggleButton,
.periodicsystem .gwt-ToggleButton-up,
.periodicsystem .gwt-ToggleButton-down,
.periodicsystem .gwt-ToggleButton-up-hovering,
.periodicsystem .gwt-ToggleButton-down-hovering,
.periodicsystem .gwt-ToggleButton-up-disabled,
.periodicsystem .gwt-ToggleButton-down-disabled{
	border-radius:0px;
	border: 0px;
	font-size: 11px;
	width: 20px;
	height: 24px;
	text-align: center;
	vertical-align: middle;
	line-height: 22px;
	font-weight: bold;
	padding: 0px;
	padding-right: 3px;
}

.emptyRow .gwt-Label{
	font-size: 4px;	
	line-height: 8px;
	visibility: hidden;
}

.emptyRow{
	font-size: 4px;
	border: 1px solid #bbb;
	border-left: 0px;
	border-right:0px;	
}
.periodicButton{
	border: 1px solid #bbb;
}

.periodicsystem{
	border-collapse: collapse;
}

.periodicsystem .gwt-ToggleButton,
.periodicsystem .gwt-ToggleButton-up{
    background: none;
}

.periodicsystem .gwt-ToggleButton-up-disabled,
.periodicsystem .gwt-ToggleButton-down-disabled{
    background: lightgray;
    color: gray;
    text-decoration: italic;
}

.periodicsystem .gwt-ToggleButton-down,
.periodicsystem .gwt-ToggleButton-down-hovering{
    background: #ddd;
}

.periodicsystem .gwt-Label { 
	padding: 0px 5px;
}

.periodicsystem td{
	text-align: center;
	vertical-align: middle;
}

.periodicsystem .listButton .gwt-ToggleButton,
.periodicsystem .listButton .gwt-ToggleButton-up,
.periodicsystem .listButton .gwt-ToggleButton-down,
.periodicsystem .listButton .gwt-ToggleButton-up-hovering,
.periodicsystem .listButton .gwt-ToggleButton-down-hovering,
.periodicsystem .listButton .gwt-ToggleButton-up-disabled,
.periodicsystem .listButton .gwt-ToggleButton-down-disabled{
	width: 62px;
	height: 22px;
}

.periodicsystem .listButton .gwt-ToggleButton,
.periodicsystem .listButton .gwt-ToggleButton-up{
    background-color: #4982D8;
    color: white;
}
.periodicsystem .listButton .gwt-ToggleButton-down,
.periodicsystem .listButton .gwt-ToggleButton-down-hovering{
    background-color: #1952A8;
}

.topGap{
	border-top: 20px solid rgba(0,0,0,0);
}


.listButton{ /* remove this style definition, to make list/not list buttons visible*/ 
	visibility: visible;
}