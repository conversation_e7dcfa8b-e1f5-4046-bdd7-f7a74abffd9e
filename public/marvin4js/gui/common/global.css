/* getting rid of Chrome's focus feedback*/
*:focus{
	outline: none;
}



table{
	border-spacing: 0px;
}


.toolbar *:focus{	
	outline: 1px solid lightgray;
}


.gwt-PopupPanel{
	border: 1px solid #999;
	-moz-box-shadow:    2px 2px 1px 1px #ccc;
	-webkit-box-shadow: 2px 2px 1px 1px #ccc;
	box-shadow:         2px 2px 1px 1px #ccc;
}



.gwt-DialogBox .Caption, 
.Caption{
	width:100%;
	border: 0px;
	
}

.Caption td{
	vertical-align: middle;
	color: #707070;
    font-weight: bold;
	font-size: 14px;
}

.CloseButton{
	float:right;
	font-size: 20px;
	color: gray;
	cursor: pointer;
}

.gwt-DialogBox{
	border: 1px solid lightgray;
	border-radius: 4px;	
	-webkit-box-shadow: 2px 2px 5px #888888;
	-moz-box-shadow: 2px 2px 5px #888888;
	box-shadow: 2px 2px 5px #888888;
}

.gwt-DialogBox .dialogMiddleLeft,
.gwt-DialogBox .dialogMiddleRight,
.gwt-DialogBox .dialogBottomLeft,
.gwt-DialogBox .dialogBottomCenter,
.gwt-DialogBox .dialogBottomRight{
	background-image: none;
	background-color: white;
}


.gwt-DialogBox .dialogTopRight, 
.gwt-DialogBox .dialogTopLeft{
	background-image: none;
	background-color: #f1f1f1;
} 

.gwt-DialogBox .dialogTopRightInner, .gwt-DialogBox .dialogBottomRightInner  {width: 10px;}


.redButton, .greenButton, .blueButton{
	color: white;
	background-image: none;
	font-weight: bold;
	text-align: center;
	min-width: 80px;
	margin-top: 10px;
	border-radius: 1px;			
}
.greenButton{
	border: 1px solid #7CB92D;
	background-color: #7CB92D;
}

.redButton{
	border: 1px solid #EC3128;
	background-color: #EC3128;
}

.blueButton{
	border: 1px solid #4982D8;
	background-color: #4982D8;	
}

.gwt-CheckBox label{vertical-align: top;}
.gwt-CheckBox input{margin:3px 5px 0px 0px;}

.marginTop, .separated{
	margin-top: 15px;
}
.marginRight{
	margin-right: 10px;
}

.marginSmallTop {
	margin-top: 4px; 
}

.expandCheckbox{
	margin-left: 10px;
}

.errorMessage {
	font-weight: bold;	
	color: red;
}

.gwt-SuggestBoxPopup{
	background-color: white;
	border-radius: 4px;
}

.hasBottomLine{
	padding-bottom: 4px;
	margin-bottom: 4px;
	border-bottom: 1px solid gray;
}

.separated{
	padding-bottom: 15px;
	border-bottom: 1px solid gray;
}

.gwt-TabBar {
    background: none repeat scroll 0% 0% white;
    border-top: 2px solid white;
}
.gwt-TabBar .gwt-TabBarItem-selected{
	background: none repeat scroll 0% 0% #e4e4e4;
}

.gwt-TabPanelBottom {
	background-color: #e4e4e4;
	border: none;
}
