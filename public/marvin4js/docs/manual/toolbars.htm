﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<!-- saved from url=(0029)http://www.helpandmanual.com/ -->
<head>
   <title>Toolbars</title>
   <meta name="generator" content="Help &amp; Manual" />
   <meta name="keywords" content="" />
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />
   <link type="text/css" href="default.css" rel="stylesheet" />
   <style type="text/css">
     body { margin: 0px; background: #FFFFFF; }
   </style>
<script type="text/javascript" src="helpman_topicinit.js"></script>
<!-- Redirect browser to frame page if page is not in the content frame. -->
<script type="text/javascript">
<!--
if (location.search.lastIndexOf("toc=0")<=0) {
  if (parent.frames.length==0) { parent.location.href="index.html?toolbars.htm"; }
  else { parent.quicksync('a1.1.2'); }
}
//-->
</script>
<script type="text/javascript" src="highlight.js"></script></head>
<body onload="highlight();">
<div id="hmpopupDiv" style="visibility:hidden; position:absolute; z-index:1000; "></div>


<table style="width:100%; border:none; border-spacing:0px; padding:5px; background:#E4F1F1">
  <tr style="vertical-align:middle">
    <td style="text-align:middle">
      <p style="text-align: center;"><span class="f_Heading1" style="font-family: 'Times New Roman'; color: #077179;">Toolbars</span></p>

    </td>
  </tr>
</table>


<!-- Placeholder for topic body. -->
<table style="width:100%;border:none;border-spacing:0px"><tr style="vertical-align:top"><td style="text-align:left;padding:5px">
<p style="text-align: justify; line-height: 1.50; margin: 14px 0px 0px 0px;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The toolbars provide buttons for accessing the majority of the features offered by Marvin for JavaScript. Hover a cursor over &nbsp;a button to see a tooltip describing the functionality of the button. The button of the currently selected tool has feedback on it to indicate its active status. Buttons for momentarily unavailable features are grayed out, and the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="toolbars.htm#clean" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Clean</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button does not appear unless server connection is available.</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 14px 0px 0px 0px;"><a name="generaltoolbar"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">General Toolbar</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">General</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar contains buttons for file management (Import, Export) and for general editing/viewing of the canvas content (Clear, Undo/Redo, Cut/Copy/Paste, Zoom, Clean, Navigate and View Settings).</span></p>
<div style="text-align: left; text-indent: 0px; line-height: 1.50; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table width="100%" cellspacing="0" cellpadding="0" border="0" style="border: none; border-spacing:0px; border-collapse: collapse;">
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="clear.png" width="34" height="34" border="0" alt="Clear" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="clear"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Clear</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Clear the whole canvas without saving its content.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="open.png" width="34" height="34" border="0" alt="Open" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="import"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Import</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Load an already existing file into Marvin for JavaScript. The supported file types</span></p>
<p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">are MDL Molfiles V2000 (.mol) and ChemAxon Marvin Documents</span></p>
<p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">(.mrv).</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="save.png" width="34" height="34" border="0" alt="Save" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="export"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Export</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Save your structures in one of the following file formats: MDL Molfiles V2000</span></p>
<p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">(.mol) or ChemAxon Marvin Documents</span></p>
<p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">(.mrv).</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="undo.png" width="34" height="34" border="0" alt="Undo" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Undo</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Revert the last commands you applied.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="redo.png" width="34" height="34" border="0" alt="Redo" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Redo</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Revert the effect of the last "Undo" command.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="cut.png" width="34" height="34" border="0" alt="Cut" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="cut"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Cut</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Place the selection on the clipboard, while removing the original structure from the canvas.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="copy.png" width="34" height="34" border="0" alt="Copy" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="copy"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Copy</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Place the selection on the clipboard, while leaving the original structure unchanged.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="paste.png" width="34" height="34" border="0" alt="Paste" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="paste"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Paste</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Insert the content of the clipboard onto the canvas.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="zoom%20in.png" width="34" height="34" border="0" alt="Zoom in" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="zoomin"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Zoom in</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Increase the zoom ratio.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="zoom%20out.png" width="34" height="34" border="0" alt="Zoom out" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="zoomout"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Zoom out</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Decrease the zoom ratio.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="zoom%20all.png" width="34" height="34" border="0" alt="Zoom All" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="zoomall"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Zoom All</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Change the zoom ratio to the optimal value to view everything on the canvas.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:top;">
<td valign="top" width="89" style="width:89px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="zoom_selection.png" width="29" height="29" border="0" alt="Zoom_Selection" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="zoomtoselection"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Zoom to Selection</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The selected object will be the center of zooming.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="clean%202d.png" width="34" height="34" border="0" alt="Clean 2D" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="clean"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Clean</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Clean the structure(s) on the canvas in 2D by recalculating the atomic coordinates.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:top;">
<td valign="middle" width="89" style="width:89px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="stereo.png" width="29" height="29" border="0" alt="stereo" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="calculatestereo"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Calculate stereo</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Determine R/S stereo labels for chiral atoms and E/Z labels for double bonds.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="view%20settings.png" width="34" height="34" border="0" alt="View Settings" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="viewsettings"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">View Settings</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Set display properties in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">View Settings</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> dialog box.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="help.png" width="34" height="34" border="0" alt="Help" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">About Marvin JS</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Displays information about the application (name, version) and a link to the User's Guide.</span></p>
</td>
</tr>
</table>
</div>
<p style="line-height: 1.50; margin: 24px 0px 0px 0px;"><a name="toolstoolbar"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Tools Toolbar</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Tools</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar offers several options for drawing or editing structures.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">There are "combo" buttons on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Tools</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar, which have a triangle in their lower right corner. When you click there, you can select a function from a list of options. Once an option is selected, the main "combo" button retains the selection.</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table width="100%" cellspacing="0" cellpadding="0" border="0" style="border: none; border-spacing:0px; border-collapse: collapse;">
<tr style="text-align:left;vertical-align:top;">
<td valign="middle" width="133" style="width:133px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="navigate.png" width="29" height="29" border="0" alt="Navigate" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="300" style="width:300px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="navigate"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Navigate</span></p>
</td>
<td valign="middle" width="1000" style="width:1000px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Move the canvas by dragging.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="133" style="width:133px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="rectangle%20selection.png" width="34" height="34" border="0" alt="Rectangle selection" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="300" style="width:300px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="selection"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Selection</span></p>
</td>
<td valign="middle" width="1000" style="width:1000px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Select whole structures or molecule fragments.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="133" style="width:133px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="delete.png" width="34" height="34" border="0" alt="delete" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="300" style="width:300px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="delete"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Delete</span></p>
</td>
<td valign="middle" width="1000" style="width:1000px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Erase one or more structures or molecule fragments.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="133" style="width:133px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="single_bond.png" width="35" height="28" border="0" alt="single_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="300" style="width:300px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="bonds"></a><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #077179; text-decoration: underline;"><a href="toolbars.htm#bonds2" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">Bonds</a></span></p>
</td>
<td valign="middle" width="1000" style="width:1000px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Open the complete list of chemical bonds including several query bond types.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="133" style="width:133px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="chain.png" width="35" height="35" border="0" alt="chain" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="300" style="width:300px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="chain"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Chain</span></p>
</td>
<td valign="middle" width="1000" style="width:1000px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Draw an alkyl chain of arbitrary length.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:top;">
<td valign="middle" width="133" style="width:133px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="abbrevgroup_1.png" width="30" height="30" border="0" alt="AbbrevGroup_1" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="300" style="width:300px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="abbreviatedgroups"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Abbreviated Groups</span></p>
</td>
<td valign="middle" width="1000" style="width:1000px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Insert abbreviated groups.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="133" style="width:133px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="increase%20charge.png" width="34" height="34" border="0" alt="Increase charge" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="300" style="width:300px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="increasecharge"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Increase Charge</span></p>
</td>
<td valign="middle" width="1000" style="width:1000px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Increase the charge of an atom with one unit (</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic;">i.e.</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> adds a positive charge to the atom).</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="133" style="width:133px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="decrease%20charge.png" width="34" height="34" border="0" alt="Decrease charge" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="300" style="width:300px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="decreasecharge"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Decrease Charge</span></p>
</td>
<td valign="middle" width="1000" style="width:1000px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Decrease the charge of an atom with one unit (</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic;">i.e.</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> adds a negative charge to the atom).</span></p>
</td>
</tr>
</table>
</div>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 24px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 13pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #077179;">&#8226;</span></td><td><a name="bonds2"></a><span class="f_ImageCaption" style="font-size: 13pt; font-family: 'Times New Roman'; color: #077179;">Bonds</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50;"><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">You can choose any type of bond by clicking on the lower right corner of the</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman';"> Bonds </span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">"combo" button in the toolbar. Among these bonds you can find several query types, too. You can also use keyboard shortcuts to choose a bond type to connect two atoms or alter an already existing bond. See the complete list of chemical bonds available in Marvin for JavaScript and their respective keyboard shortcuts in the table below.</span></p>
<div style="text-align: left; text-indent: 0px; line-height: 1.50; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table cellspacing="0" cellpadding="0" border="0" style="border: none; border-spacing:0px; border-collapse: collapse;">
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Bond symbol</span></p>
</td>
<td valign="middle" width="439" style="width:439px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Bond type</span></p>
</td>
<td valign="middle" width="151" style="width:151px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Shortcut</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:top;">
<td valign="middle" width="123" style="width:123px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="drawing.png" width="24" height="24" border="0" alt="drawing" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="drawing"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Drawing</span></p>
</td>
<td valign="middle" width="151" style="width:151px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Ctrl+D</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="single_bond.png" width="35" height="28" border="0" alt="single_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Single" bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">1</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="double_bond.png" width="35" height="28" border="0" alt="double_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Double" bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">2</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="triple_bond.png" width="34" height="29" border="0" alt="triple_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Triple" bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">3</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="aromatic_bond.png" width="35" height="28" border="0" alt="aromatic_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Aromatic" bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">4</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="coordinate_bond.png" width="35" height="30" border="0" alt="coordinate_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Coordinate" bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">-</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="single_up_bond.png" width="35" height="28" border="0" alt="single_up_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Single Up" wedge bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">5</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="single_down_bond.png" width="35" height="28" border="0" alt="single_down_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Single Down" wedge bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">6</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="single_up_down_bond.png" width="35" height="27" border="0" alt="single_up_down_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Single Up or Down" query bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">7</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="double_cis_trans_bond.png" width="35" height="30" border="0" alt="double_cis_trans_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Double Cis or Trans" query bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">-</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="unspecified_bond.png" width="35" height="30" border="0" alt="unspecified_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Double C/T or Unspecified" query bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">-</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="single_double_bond.png" width="35" height="29" border="0" alt="single_double_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Single or Double" query bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">12</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="single_aromatic_bond.png" width="35" height="30" border="0" alt="single_aromatic_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Single or Aromatic" query bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">14</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="double_aromatic_bond.png" width="35" height="29" border="0" alt="double_aromatic_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Double or Aromatic" query bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">24</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="any_bond.png" width="35" height="31" border="0" alt="any_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Any" query bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">0</span></p>
</td>
</tr>
</table>
</div>
<p style="text-align: justify; line-height: 1.50; margin: 24px 0px 0px 0px;"><a name="atomstoolbar"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Atoms Toolbar</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The elements of the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Atoms</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar provide tools for drawing and modifying atoms, including query atom types, too. </span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">The chosen atomic symbol appears on the tip of the cursor. After a left-click, this atom is put on the canvas. Note that you can also use </span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal; color: #077179; text-decoration: underline;"><a href="shortcuts.htm" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">shortcuts</a></span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;"> (atomic symbols) to choose any atom from the Periodic Table.</span></p>
<div style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table width="100%" cellspacing="0" cellpadding="0" border="0" style="border: none; border-spacing:0px; border-collapse: collapse;">
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="270" style="width:270px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="periodic%20table.png" width="34" height="34" border="0" alt="Periodic Table" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="220" style="width:220px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="periodictable"></a><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #077179; text-decoration: underline;"><a href="toolbars.htm#periodictable2" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">Periodic Table</a></span></p>
</td>
<td valign="middle" width="957" style="width:957px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">A pop-up window to select an atom from the Periodic Table</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="270" style="width:270px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="hydrogen2.png" width="17" height="14" border="0" alt="hydrogen2" style="margin:0 auto;margin:0px;">;<img src="carbon2.png" width="17" height="14" border="0" alt="carbon2" style="margin:0 auto;margin:0px;">;<img src="nitrogen2.png" width="17" height="14" border="0" alt="nitrogen2" style="margin:0 auto;margin:0px;">;<img src="oxygen2.png" width="17" height="14" border="0" alt="oxygen2" style="margin:0 auto;margin:0px;">;<img src="sulfur2.png" width="17" height="14" border="0" alt="sulfur2" style="margin:0 auto;margin:0px;">;<img src="fluorine2.png" width="17" height="14" border="0" alt="fluorine2" style="margin:0 auto;margin:0px;">;<img src="phosphorus2.png" width="17" height="14" border="0" alt="phosphorus2" style="margin:0 auto;margin:0px;">; <img src="chlorine2.png" width="17" height="14" border="0" alt="chlorine2" style="margin:0 auto;margin:0px;">; <img src="bromine2.png" width="17" height="14" border="0" alt="bromine2" style="margin:0 auto;margin:0px;">;<img src="iodine2.png" width="17" height="14" border="0" alt="iodine2" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="220" style="width:220px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="themostfrequentlyusedatoms"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Frequently used atoms</span></p>
</td>
<td valign="middle" width="957" style="width:957px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The most frequently used atoms can be reached directly from the toolbar</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:top;">
<td valign="middle" width="270" style="width:270px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="star_atom.png" width="30" height="30" border="0" alt="star_atom" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="220" style="width:220px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #007171; text-decoration: underline;"><a href="toolbars.htm#staratom" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #007171; background-color: transparent; text-decoration: underline;">Star atom</a></span></p>
</td>
<td valign="middle" width="957" style="width:957px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Draw a '*' atom (</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic;">e.g.</span><span style="font-size: 12pt; font-family: 'Times New Roman';">, for an unspecified end group in a polymer)</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="270" style="width:270px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="any.png" width="35" height="25" border="0" alt="any" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="220" style="width:220px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="queryatoms"></a><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #077179; text-decoration: underline;"><a href="toolbars.htm#queryatoms2" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">Query atoms</a></span></p>
</td>
<td valign="middle" width="957" style="width:957px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Combo" button to select a query atom type</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:top;">
<td valign="middle" width="270" style="width:270px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="r_group.png" width="28" height="24" border="0" alt="R_group" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="220" style="width:220px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><a name="r-groups"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">R-groups</span></p>
</td>
<td valign="middle" width="957" style="width:957px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Combo" button to insert an R-group</span></p>
</td>
</tr>
</table>
</div>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 24px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 13pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #077179;">&#8226;</span></td><td><a name="periodictable2"></a><span style="font-size: 13pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Periodic Table</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">This button opens the Periodic Table in a pop-up window. You can choose any atom from the Periodic Table to put it on the canvas with a left-click. You can also define </span><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #077179; text-decoration: underline;"><a href="chemical_features_in_marvin_fo.htm#atomlistsandnotlists" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">Atom Lists and NOT Lists</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> for query structures. The selected atom or list appears on the tip of the cursor, and you can place it on the canvas one or more times.</span></p>
<p style="text-align: justify; line-height: 1.50;"><img src="periodic_table.png" width="495" height="310" border="0" alt="periodic_table"></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 24px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 13pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #007171;">&#8226;</span></td><td><a name="staratom"></a><span class="f_ImageCaption" style="font-size: 13pt; font-family: 'Times New Roman'; color: #007171;">Star ('*') atom</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50;"><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">In most cases, a '*' atom stands for an unspecified end group in a polymer. More generally, it can symbolize any type of unknown or unspecified groups or molecules. Technically, it is an 'AH' type query atom (any elements including hydrogen) with an alias.</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 14px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 13pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #077179;">&#8226;</span></td><td><a name="queryatoms2"></a><span style="font-size: 13pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Query atoms</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Marvin for JavaScript offers several query atom types, which can be reached by left-clicking on the lower right corner of the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Query Atoms</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> "combo" button. See the complete list in the table below. Pseudo atoms are also available from here.</span></p>
<div style="text-align: justify; text-indent: 0px; line-height: 1.50; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table cellspacing="0" cellpadding="0" border="0" style="border: none; border-spacing:0px; border-collapse: collapse;">
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="84" style="width:84px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="any.png" width="35" height="25" border="0" alt="any" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="456" style="width:456px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Any of the elements except hydrogen</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="84" style="width:84px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="hetero.png" width="35" height="25" border="0" alt="hetero" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="456" style="width:456px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Hetero atom (any atom except carbon and hydrogen)</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="84" style="width:84px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="metal.png" width="35" height="25" border="0" alt="metal" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="456" style="width:456px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Metal atom</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="84" style="width:84px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="halogen.png" width="35" height="25" border="0" alt="halogen" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="456" style="width:456px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Halogen atom</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="84" style="width:84px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="any_or_h.png" width="35" height="25" border="0" alt="any_or_H" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="456" style="width:456px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Any of the elements including hydrogen</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="84" style="width:84px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="any_except_c.png" width="35" height="25" border="0" alt="any_except_C" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="456" style="width:456px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Any of the elements except carbon</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="84" style="width:84px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="metal_or_h.png" width="35" height="25" border="0" alt="metal_or_H" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="456" style="width:456px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Metal atom or hydrogen</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="84" style="width:84px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="halogen_or_h.png" width="35" height="25" border="0" alt="halogen_or_H" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="456" style="width:456px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Halogen atom or hydrogen</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:top;">
<td valign="middle" width="84" style="width:84px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><img src="pseudo.png" width="35" height="25" border="0" alt="pseudo" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="456" style="width:456px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Pseudo atom</span></p>
</td>
</tr>
</table>
</div>
<p style="text-align: justify; line-height: 1.50; margin: 24px 0px 0px 0px;"><a name="templatestoolbar"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Templates Toolbar</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Six generic templates are available from the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Templates</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> </span><span style="font-size: 12pt; font-family: 'Times New Roman';">toolbar</span><span style="font-size: 12pt; font-family: 'Times New Roman';">. After selecting one of these templates you can place it on the canvas with a left mouse-click. When placing it on the canvas, you can rotate the template around its center by dragging the cursor. Templates can also be connected to already existing structures by </span><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #077179; text-decoration: underline;"><a href="basic_features_of_marvin_for_j.htm#sproutdrawing" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">sprout drawing</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';">.</span></p>
<div style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 24px 0px;"><table cellspacing="0" cellpadding="0" border="0" style="border: none; border-spacing:0px; border-collapse: collapse;">
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="84" style="width:84px; height:37px; border: solid 1px #000000;"><p style="text-align: center; line-height: 1.50;"><img src="cyclohexane.png" width="35" height="28" border="0" alt="cyclohexane" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="514" style="width:514px; height:37px; border: solid 1px #000000;"><p style="text-align: center; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Cyclohexane</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="84" style="width:84px; height:37px; border: solid 1px #000000;"><p style="text-align: center; line-height: 1.50;"><img src="cyclopentane.png" width="35" height="28" border="0" alt="cyclopentane" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="514" style="width:514px; height:37px; border: solid 1px #000000;"><p style="text-align: center; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Cyclopentane</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="84" style="width:84px; height:37px; border: solid 1px #000000;"><p style="text-align: center; line-height: 1.50;"><img src="pyrrole.png" width="35" height="28" border="0" alt="pyrrole" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="514" style="width:514px; height:37px; border: solid 1px #000000;"><p style="text-align: center; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Pyrrole</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="84" style="width:84px; height:37px; border: solid 1px #000000;"><p style="text-align: center; line-height: 1.50;"><img src="cyclopentane_house.png" width="35" height="29" border="0" alt="cyclopentane_house" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="514" style="width:514px; height:37px; border: solid 1px #000000;"><p style="text-align: center; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Cyclopentane (house)</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="84" style="width:84px; height:37px; border: solid 1px #000000;"><p style="text-align: center; line-height: 1.50;"><img src="benzene.png" width="35" height="28" border="0" alt="benzene" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="514" style="width:514px; height:37px; border: solid 1px #000000;"><p style="text-align: center; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Benzene</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="84" style="width:84px; height:37px; border: solid 1px #000000;"><p style="text-align: center; line-height: 1.50;"><img src="naphthalene.png" width="35" height="26" border="0" alt="naphthalene" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="514" style="width:514px; height:37px; border: solid 1px #000000;"><p style="text-align: center; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Naphthalene</span></p>
</td>
</tr>
</table>
</div>

</td></tr></table>

</body>
</html>
