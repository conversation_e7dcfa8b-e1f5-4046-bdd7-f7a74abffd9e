﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<!-- saved from url=(0029)http://www.helpandmanual.com/ --><head>
   <title>Marvin for JavaScript User's Guide</title>
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />

   <!-- This line includes the general project style sheet (not required) -->
   <link type="text/css" href="default.css" rel="stylesheet" />

   <!-- This block defines the styles of the TOC headings, change them as needed -->
   <style type="text/css">
       body { background:#FFF; }
       .navbar   { font-size: 120%; }

       .heading1 { font-family: Times New Roman; font-weight: normal; font-size: 12pt; color: #000000; text-decoration: none; }
       .heading2 { font-family: Times New Roman; font-weight: normal; font-size: 11pt; color: #000000; text-decoration: none; }
       .heading3 { font-family: Times New Roman; font-weight: normal; font-size: 11pt; color: #000000; text-decoration: none; }
       .heading4 { font-family: Times New Roman; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading5 { font-family: Times New Roman; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading6 { font-family: Times New Roman; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }

       .hilight1 { font-family: Times New Roman; font-weight: normal; font-size: 12pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight2 { font-family: Times New Roman; font-weight: normal; font-size: 11pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight3 { font-family: Times New Roman; font-weight: normal; font-size: 11pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight4 { font-family: Times New Roman; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight5 { font-family: Times New Roman; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight6 { font-family: Times New Roman; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }

       /* TOC LIST CSS */
       #toc    { padding: 0; margin: 0 }
       #toc li { margin-top: 2px }
       #toc ul { padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0 }
       /* TOC LIST CSS */
   </style>
</head>
<body>
<p class="navtitle">Marvin for JavaScript User's Guide</p>
<p class="navbar">
<b>Contents</b>
 | <a href="marvin4js_copy_kwindex_static.html">Index</a>

</p><hr size="1" />

  <!-- Place holder for the TOC - this variable is REQUIRED! -->
  <table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="30" align="right"><span class="heading1"><img class="icon" src="cicon6.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="marvin_for_javascript.htm" target="hmcontent"><span id="s1" class="heading1">Marvin for JavaScript</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="60" align="right"><span class="heading2"><img class="icon" src="cicon6.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="layout_of_marvinsketch.htm" target="hmcontent"><span id="s1.1" class="heading2">Layout of Marvin for JavaScript</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="90" align="right"><span class="heading3"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="canvas.htm" target="hmcontent"><span id="s1.1.1" class="heading3">Canvas</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="90" align="right"><span class="heading3"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="toolbars.htm" target="hmcontent"><span id="s1.1.2" class="heading3">Toolbars</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="90" align="right"><span class="heading3"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="pop_up_menus.htm" target="hmcontent"><span id="s1.1.3" class="heading3">Pop-up Menus</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="60" align="right"><span class="heading2"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="basic_features_of_marvin_for_j.htm" target="hmcontent"><span id="s1.2" class="heading2">Basic Features of Marvin for JavaScript</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="60" align="right"><span class="heading2"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="chemical_features_in_marvin_fo.htm" target="hmcontent"><span id="s1.3" class="heading2">Chemical Features in Marvin for JavaScript</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="60" align="right"><span class="heading2"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="shortcuts.htm" target="hmcontent"><span id="s1.4" class="heading2">Shortcuts</span></a></td></tr></table>


<hr size="1" /><p style="font-size: 8pt">Copyright © 1998-2013 ChemAxon Ltd.</p>
<p style="font-size: 8pt"><a href="https://www.chemaxon.com/products/marvin/marvin-for-javascript/">http://www.chemaxon.com/marvin</a></p>
</body>
</html>
