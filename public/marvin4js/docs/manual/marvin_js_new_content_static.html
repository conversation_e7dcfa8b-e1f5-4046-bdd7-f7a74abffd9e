﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<!-- saved from url=(0029)http://www.helpandmanual.com/ --><head>
   <title>&nbsp;</title>
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />

   <!-- This line includes the general project style sheet (not required) -->
   <meta http-equiv="Content-Style-Type" content="text/css" />
   <link type="text/css" href="default.css" rel="stylesheet" />

   <!-- This block defines the styles of the TOC headings, change them as needed -->
   <style type="text/css">
       .navtitle { font-size: 14pt; font-weight: bold; margin-bottom: 16px; }
       .navbar   { font-size: 10pt; }

       .heading1 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading2 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading3 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading4 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading5 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading6 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }

       .hilight1 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight2 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight3 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight4 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight5 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight6 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }

       TD.toc { padding-bottom: 2px; padding-right: 4px }
   </style>
</head>
<body style="background: #FFFFFF; url(null) fixed no-repeat">
<p class="navtitle">&nbsp;</p>
<p class="navbar">
<b>Contents</b>
 | <a href="marvin_js_new_kwindex_static.html">Index</a>

</p><hr size="1" />

<!-- Place holder for the TOC - this variable is REQUIRED! -->
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon2.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="layout_of_marvinsketch.htm" target="hmcontent"><span id="s1" class="heading1">Layout of Marvin for JavaScript</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="40" align="right"><span class="heading2"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="general_toolbar.htm" target="hmcontent"><span id="s1.1" class="heading2">General Toolbar</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="40" align="right"><span class="heading2"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="tools_toolbar.htm" target="hmcontent"><span id="s1.2" class="heading2">Tools Toolbar</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="40" align="right"><span class="heading2"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="atoms_toolbar.htm" target="hmcontent"><span id="s1.3" class="heading2">Atoms Toolbar</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="40" align="right"><span class="heading2"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="templates_toolbar.htm" target="hmcontent"><span id="s1.4" class="heading2">Templates Toolbar</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="basic_features_of_marvinsketch.htm" target="hmcontent"><span id="s2" class="heading1">Basic Features of Marvin for JavaScript</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a href="shortcuts.htm" target="hmcontent"><span id="s3" class="heading1">Shortcuts</span></a></td></tr></table>


<hr size="1" /><p style="font-size: 8pt">© 2012 Enter your company name</p>
</body>
</html>

