﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<!-- saved from url=(0029)http://www.helpandmanual.com/ --><head>
   <title>&nbsp;</title>
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />

   <!-- This line includes the general project style sheet (not required) -->
   <meta http-equiv="Content-Style-Type" content="text/css" />
   <link type="text/css" href="default.css" rel="stylesheet" />

   <style type="text/css">
       .navtitle    { font-size: 14pt; font-weight: bold; margin-bottom: 16px; }
       .navbar      { font-size: 10pt; }
       .idxsection  { font-family: Arial,Helvetica; font-weight: bold; font-size: 14pt; color: #000000; text-decoration: none;
                      margin-top: 15px; margin-bottom: 15px; }
       .idxkeyword  { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .idxkeyword2 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .idxlink     { font-family: Arial,Helvetica; font-weight: normal; font-size: 9pt; color: #000000; text-decoration: none; }

       TABLE.idxtable { background: #F4F4F4; border-width: 1px; border-color: #000000; border-collapse: collapse;
                        filter: progid:DXImageTransform.Microsoft.Shadow(color=B0B0B0, Direction=135, Strength=4); }
       TD.idxtable    { background: #F4F4F4; }
   </style>
</head>
<body bgcolor="#FFFFFF">
<p class="navtitle">&nbsp;</p>
<p class="navbar">
<a href="marvin_js_new_content_dyn.html">Contents</a>
 | <b>Index</b>
 | <a href="marvin_js_new_ftsearch.html">Search</a>
</p><hr size="1" />
<!-- Place holder for the keyword index - this variable is REQUIRED! -->
<script type="text/javascript">
var currentdiv = null;
var canhidelinks = true;
function hmshowLinks(divID) {
   var thisdiv = document.getElementById(divID);
   canhidelinks = true;
   hmhideLinks();
   if (thisdiv) {
      thisdiv.style.display = "block";
      currentdiv = thisdiv;
      thisdiv.onmouseover = divmouseover;
      thisdiv.onmouseout = divmouseout;
      document.onmouseup = hmhideLinks;
   }
}
function divmouseover() { canhidelinks = false; }
function divmouseout() { canhidelinks = true; }
function hmhideLinks() {
   if (canhidelinks) {
      if (currentdiv) {
         currentdiv.style.display = "none";
         currentdiv.onmouseover = null;
         currentdiv.onmouseout = null;
      }
      currentdiv = null;
      document.onmouseout = null;
   }
}

</script>


</body>
</html>

