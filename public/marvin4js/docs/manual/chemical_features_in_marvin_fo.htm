﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<!-- saved from url=(0029)http://www.helpandmanual.com/ -->
<head>
   <title>Chemical Features in Marvin for JavaScript</title>
   <meta name="generator" content="Help &amp; Manual" />
   <meta name="keywords" content="" />
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />
   <link type="text/css" href="default.css" rel="stylesheet" />
   <style type="text/css">
     body { margin: 0px; background: #FFFFFF; }
   </style>
<script type="text/javascript" src="helpman_topicinit.js"></script>
<!-- Redirect browser to frame page if page is not in the content frame. -->
<script type="text/javascript">
<!--
if (location.search.lastIndexOf("toc=0")<=0) {
  if (parent.frames.length==0) { parent.location.href="index.html?chemical_features_in_marvin_fo.htm"; }
  else { parent.quicksync('a1.3'); }
}
//-->
</script>
<script type="text/javascript" src="highlight.js"></script></head>
<body onload="highlight();">
<div id="hmpopupDiv" style="visibility:hidden; position:absolute; z-index:1000; "></div>


<table style="width:100%; border:none; border-spacing:0px; padding:5px; background:#E4F1F1">
  <tr style="vertical-align:middle">
    <td style="text-align:middle">
      <p class="p_Heading1" style="text-align: center;"><span class="f_Heading1" style="font-size: 18pt; font-family: 'Times New Roman'; color: #077179;">Chemical Features in Marvin for JavaScript</span></p>

    </td>
  </tr>
</table>


<!-- Placeholder for topic body. -->
<table style="width:100%;border:none;border-spacing:0px"><tr style="vertical-align:top"><td style="text-align:left;padding:5px">
<p class="p_Heading1" style="text-align: justify; line-height: 1.50; margin: 19px 0px 0px 0px;"><a name="substructuregroupss-groups"></a><span class="f_Heading1" style="font-family: 'Times New Roman'; color: #077179;">Substructure Groups (S-groups)</span></p>
<p style="line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">S-groups or substructure groups represent an integral part of a chemical structure. Marvin for JavaScript can handle generic S-groups and predefined abbreviated groups (superatom S-groups). Structures containing S-groups can be imported and exported either in MRV or in MDL Molfile format.</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 10px 0px 0px 0px;"><a name="generics-groups"></a><span class="f_Heading1" style="font-size: 14pt; font-family: 'Times New Roman'; color: #077179;">Generic S-groups</span></p>
<p style="line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Generic S-groups usually represent whole molecules, </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic;">e.g.,</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> solvents. They are visualized between brackets, which will disappear if you remove the group status from the substructure either by using the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Ungroup</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> option of the</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"> </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="pop_up_menus.htm#grouppop-upmenu" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Group pop-up menu</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> or by editing the molecule (changing/adding/deleting atoms or bonds, modifying atom properties...).</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 10px 0px 0px 0px;"><a name="abbreviatedgroups"></a><span class="f_Heading1" style="font-size: 14pt; font-family: 'Times New Roman'; color: #077179;">Abbreviated groups (Superatom S-groups)</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">An abbreviated group can be part of a larger molecule by connecting the group to other fragments through its attachment point. Currently, only a predefined list of abbreviated groups is available in Marvin for JavaScript. Using the abbreviations of chemical functional groups and molecules enables us to create more compact structures. Alternatively, abbreviated groups can be displayed in their full, expanded forms in favor of a more detailed description of the crucial parts of the molecule.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Marvin for JavaScript offers a wide selection of abbreviated groups belonging to several chemical compound families (carbohydrates, amino acids, </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic;">etc.</span><span style="font-size: 12pt; font-family: 'Times New Roman';">). These structures are available under the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Abbreviated Groups</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button of the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Tools</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar. Selecting a group in the opening dialog, you can decide whether to use the group in its contracted or expanded form. For the latter option, tick the checkbox in the dialog window. Pressing the "OK" button closes the dialog and puts the selected group on the tip of the cursor, from where you can place it on the canvas with a left-click.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">An abbreviated group can be expanded, contracted, or ungrouped after it has been placed on the canvas. These options are available from the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="pop_up_menus.htm#grouppop-upmenu" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Group pop-up menu</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';">.</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Expanded groups are denoted by special feedback: blue background and brackets appear around the group if the cursor is over it (the green "hover-over" feedback is still visible on the respective atom or bond), and the abbreviated name is also visible. The attachment points of the groups are circled in blue:</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50;"><img src="abbrevgroup2.png" width="158" height="196" border="0" alt="AbbrevGroup2"></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">If an expanded group is "ungrouped", the feedback around it will disappear. The same action in case of a contracted group means that the group will be firstly expanded, and its feedback will disappear. </span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Contracted groups can be manipulated as atoms; while expanded abbreviated groups can be handled as molecules or fragments: they can be rotated, dragged, merged, mirrored, deleted, and cleaned in 2D.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Editing an expanded abbreviated group in any sense -</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic;"> i.e.</span><span style="font-size: 12pt; font-family: 'Times New Roman';">, changing/deleting an atom or a bond, modifying the charge or mass number of atoms - leads to the removal of the group status from the substructure.</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50; margin: 19px 0px 0px 0px;"><a name="structurecleaning"></a><span style="font-size: 16pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Structure Cleaning</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">2D cleaning of molecules can be performed using the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="toolbars.htm#clean" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Clean</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button of the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">General</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar. It calculates new 2D coordinates for the atoms on the canvas in order to get the same molecule in an evenly organized fashion.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Partial cleaning of a structure is possible, too. If you have selected fragments of molecules or certain elements of a multi-molecular structure, 2D cleaning affects only these selected objects. </span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The 2D cleaning feature is available only if the server connection is active, and the appropriate webservice is available otherwise the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Clean</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button will not appear.</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 19px 0px 0px 0px;"><a name="structurevalidation"></a><span style="font-size: 16pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Structure Validation</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Marvin for JavaScript only highlights valence errors, but does not correct them, so you should make the necessary corrections manually. In case of recognizing a valence error, Marvin for JavaScript highlights the particular atom with red background.</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 19px 0px 0px 0px;"><a name="stereoisomerism"></a><span style="font-size: 16pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Stereoisomerism</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Both optical isomerism and geometric isomers can be determined in Marvin for JavaScript. In order to assign the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic; font-weight: bold;">R</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> or </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic; font-weight: bold;">S</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> label to a chiral center and the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic; font-weight: bold;">E</span><span style="font-size: 12pt; font-family: 'Times New Rom#077179an';"> or </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic; font-weight: bold;">Z</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> label to a double bond, the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #007171; text-decoration: underline;"><a href="toolbars.htm#calculatestereo" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #007171; background-color: transparent; text-decoration: underline;">Calculate stereo</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button of the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">General</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar should be used. The </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic; font-weight: bold;">R/S</span><span style="font-size: 12pt; font-family: 'Times New Roman';">- and </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic; font-weight: bold;">E/Z</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> labels are determined </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic;">via</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> webservice.</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 10px 0px 0px 0px;"><a name="opticalisomerism"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Optical isomerism</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Optical isomers of a molecule differ in the configuration of their chiral atoms (</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic;">e.g.</span><span style="font-size: 12pt; font-family: 'Times New Roman';">, a carbon atom with four different substituents). Absolute configuration of a chiral center can be marked by </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic; font-weight: bold;">R</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> or </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic; font-weight: bold;">S</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> stereo labels. If a carbon atom has four different substituents (or three different substituents and an implicit hydrogens) and at least one of them connects to the chiral carbon with a wedge bond, the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic; font-weight: bold;">R</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> or </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic; font-weight: bold;">S</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> label can be calculated using the Cahn-Ingold-Prelog priority rules (CIP convention).</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Molecules with one chiral center have two optical isomers (enantiomers) which are the mirror images of each other, but they are not superposable. Enantiomers differ in the absolute configuration of each of their chiral centers.</span></p>
<p style="text-align: justify; line-height: 1.50;"><img src="stereo_1.png" width="264" height="153" border="0" alt="stereo_1"></p>
<p style="text-align: justify; line-height: 1.50; margin: 10px 0px 0px 0px;"><a name="geometricisomerism"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Geometric isomerism</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The reason behind geometric isomerism of alkenes is that rotation around double bonds is hindered, so the position of the double bond substituents is fixed relative to each other. When both double bonded carbon atoms have two different substituents, we can distinguish two non-identical geometric isomers, which are denoted by </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic; font-weight: bold;">E</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> and </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic; font-weight: bold;">Z</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> labels, respectively. These stereo labels are calculated by determining the priority of the four substituents using the CIP convention. If the higher ranked substituents are on the same side of the double, the molecule is the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic; font-weight: bold;">Z</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> isomers, while the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic; font-weight: bold;">E</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> isomer has the higher ranked groups on opposite sides of the double bond.</span></p>
<p style="text-align: justify; line-height: 1.50;"><img src="stereo_2.png" width="414" height="128" border="0" alt="stereo_2"></p>
<p class="p_Heading1" style="text-align: justify; line-height: 1.50; margin: 19px 0px 0px 0px;"><a name="querystructures"></a><span class="f_Heading1" style="font-family: 'Times New Roman'; color: #077179;">Query Structures</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Query structures are generalized structures representing a group of similar molecules which differ only in some structural elements. For example, all of the molecules possess the same skeleton with a halogen atom as substituent, but it is not crucial to know which halogen exactly. See more about query structures in </span><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #077179; text-decoration: underline;"><a href="http://www.chemaxon.com/jchem/doc/user/queryindex.html" target="_blank" class="weblink" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">JChem Query Guide</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';">. Using </span><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #077179; text-decoration: underline;"><a href="toolbars.htm#queryatoms2" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">query atoms</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> and </span><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #077179; text-decoration: underline;"><a href="toolbars.htm#bonds2" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">bonds</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> offered in Marvin for JavaScript different query structures can be designed. Note that atom properties (</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic;">e.g.</span><span style="font-size: 12pt; font-family: 'Times New Roman';">,</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> atomic charge) can be modified for the query atoms, too. The query atoms are available on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Atoms </span><span style="font-size: 12pt; font-family: 'Times New Roman';">toolbar, under a "combo" button.</span></p>
<p style="text-align: justify; line-height: 1.50;"><a name="pseudoatoms"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">You can define pseudo atoms in your molecules. Replacing an atom with a pseudo will change it to an "Any" type query atom. Atom properties cannot be associated with a pseudo atom, the relevant dialog is not available in case of these atoms.</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 10px 0px 0px 0px;"><a name="absolutestereoinformation"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Absolute stereo information</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The "Absolute" chiral flag on a molecule indicates that every chiral center marked with wedge bonds has a known absolute configuration, that is to say, the structure represents a single, well-defined stereoisomer. This chiral flag can be added to a molecule through the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #077179; text-decoration: underline;"><a href="pop_up_menus.htm#editpop-upmenu" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">pop-up menu</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> and its display can be turned on/off in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #077179; text-decoration: underline;"><a href="basic_features_of_marvin_for_j.htm#structuredisplayoptions" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">View Settings</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> dialog window.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Depending on the file format, a molecule which has wedge bonds without a chiral flag has either of the following meanings:</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">In MDL file types (MOL, SDF, ...): wedge bonds describe the relative configuration of chiral centers; the structure is a racemic mixture of two enantiomers.</span></td></tr></table></div><div style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 13px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.15;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">In Daylight file types (SMILES, SMARTS): wedge bonds describe the absolute configuration of chiral centers; the structure represents a single enantiomer.</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50; margin: 10px 0px 0px 0px;"><a name="enhancedstereospecifications"></a><span class="f_Heading1" style="font-size: 14pt; font-family: 'Times New Roman'; color: #077179;">Enhanced stereo specifications</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Marvin for JavaScript supports the MDL enhanced stereo representation. Enhanced stereo specifications enable us to refer to several stereoisomers of a molecule by drawing only one structure. Attaching these notations to chiral centers makes it possible to represent stereochemical information: whether we know the absolute configuration of a stereogenic center or we have information only about the relative configuration of two or more chiral atoms. The advantage of enhanced stereo representation is the most palpable when the molecule contains several chiral centers. Illustrating all or even just a few of the molecules stereoisomers can be really demanding in a case like this, but with the help of enhanced stereo specifications only one molecular structure can be enough to represent every diastereomer.</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 0px 0px 5px 0px;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Marvin for JavaScript applies four identifiers, which help us to create groups from the stereocenters. Each group label consists of an identifier, and – in some cases – of a number. Each and every stereocenter belongs only to one stereogenic group. The following identifiers can be attached to stereogenic centers:</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 5px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Off: We have no information about the configuration of the chiral atom;</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 5px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Abs: We know the absolute configuration of the chiral center;</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 5px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Or: Attaching the „Or” identifier to a stereogenic center means that we only know the configuration of the atom relative to another chiral center, but we do not have information about the absolute configuration. For a molecule with two chiral centers it either refers to the structure as drawn (</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic;">e.g.</span><span style="font-size: 12pt; font-family: 'Times New Roman';">, </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">1</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic; font-weight: bold;">R</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic;">, </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">2</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic; font-weight: bold;">R</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> configuration) or to its enantiomer, which has opposite configuration on its chiral atoms (</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">1</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic; font-weight: bold;">S</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic;">, </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">2</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic; font-weight: bold;">S</span><span style="font-size: 12pt; font-family: 'Times New Roman';">);</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 5px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">And: We use the „And” notation if we want to represent a mixture of stereoisomers which can contain specified enantiomers/diastereomers or even every optical isomer.</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">You can find a more detailed description of enhanced stereo representation along with a set of examples in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #077179; text-decoration: underline;"><a href="http://www.chemaxon.com/jchem/doc/user/query_stereochemistry.html#mdl_enhanced_stereo" target="_blank" class="weblink" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">JChem Query Guide</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';">.</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 10px 0px 0px 0px;"><a name="atomlistsandnotlists"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Atom Lists and NOT Lists</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">You can add an "Atom List" query atom to your molecule, which covers a set of atoms. The created structure produces a hit if any of the atoms in the list is found in the target. Similarly, you can use "NOT List" query atoms, too. A NOT List query atom can represent any atom that is not part of the list.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Atom Lists and NOT Lists can be defined using the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #077179; text-decoration: underline;"><a href="toolbars.htm#periodictable2" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">Periodic Table</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';">. Pressing the appropriate button on the dialog then clicking on the atoms one after the other prepares the list. Close the Periodic Table when you are ready: the list of atoms is on the cursor. </span></p>
<p style="text-align: justify; line-height: 1.50; margin: 10px 0px 0px 0px;"><a name="r-groups"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #007171;">R-groups</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">A query with an R-group represents substitution variation on the same scaffold. An R-group query can involve several derivatives which differ in one or more substituents.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Marvin for JavaScript offers the tool to create query structures containing different R-groups. Using the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #007171; text-decoration: underline;"><a href="toolbars.htm#r-groups" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #007171; background-color: transparent; text-decoration: underline;">"combo" button</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Atoms</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar, an R-group with any arbitrary number can be added to a molecule in the same way as atoms are inserted. Alternatively, you can type an R-group label as well. Numbers between 1 and 10 can be typed directly; while using the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">SPACE</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> key after "R" opens a dialog where you can give the preferred number.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="color: #000000;">&nbsp;</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="color: #000000;">&nbsp;</span></p>

</td></tr></table>

</body>
</html>
