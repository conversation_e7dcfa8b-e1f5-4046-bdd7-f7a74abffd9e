﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<!-- saved from url=(0029)http://www.helpandmanual.com/ -->
<head>
   <title>Tools Toolbar</title>
   <meta name="generator" content="Help &amp; Manual" />
   <meta name="keywords" content="" />
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="Content-Style-Type" content="text/css" />
   <link type="text/css" href="default.css" rel="stylesheet" />
<script type="text/javascript" src="helpman_topicinit.js"></script>
<!-- Redirect browser to frame page if page is not in the content frame. -->
<script type="text/javascript">
<!--
if (location.search.lastIndexOf("toc=0")<=0) {
  if (parent.frames.length==0) { parent.location.href="index.html?tools_toolbar.htm"; }
  else { parent.quicksync('a1.2'); }
}
//-->
</script>
<script type="text/javascript" src="highlight.js"></script></head>
<body style="margin: 0px 0px 0px 0px; background: #FFFFFF;" onload="highlight();">
<div id="hmpopupDiv" style="visibility:hidden; position:absolute; z-index:1000; "></div>


<table width="100%" border="0" cellspacing="0" cellpadding="5" bgcolor="#D0D0D0">
  <tr valign="middle">
    <td align="left">
      <p style="line-height: 1.50;"><span class="f_Heading1" style="font-family: 'Times New Roman';">Tools Toolbar</span></p>

    </td>
    <td align="right">
     <span style="font-size: 9pt">
     <a href="layout_of_marvinsketch.htm">Top</a>&nbsp;
     <a href="general_toolbar.htm">Previous</a>&nbsp;
     <a href="atoms_toolbar.htm">Next</a>
     </span>
    </td>
  </tr>
</table>


<!-- Placeholder for topic body. -->
<table width="100%" border="0" cellspacing="0" cellpadding="5"><tr valign="top"><td align="left">
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Tools</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar offers several options for drawing or editing structures.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">There are some "combo" buttons in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Tools</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar, which have a triangle in their lower right corner. When you click there, you can select a function from a list of options. Once an option is selected, the main "combo" button retains the selection.</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table width="100%" cellspacing="0" cellpadding="0" border="0" style="border: none; border-spacing:0px; border-collapse: collapse;">
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="133" style="width:133px; height:50px; border: solid 1px #000000;"><p style="text-align: center; text-indent: -92px; margin: 0px 0px 0px 92px;"><img src="rectangle%20selection.png" width="34" height="34" border="0" alt="Rectangle selection" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="300" style="width:300px; height:50px; border: solid 1px #000000;"><p style="text-align: center; text-indent: -92px; margin: 0px 0px 0px 92px;"><span style="font-size: 12pt; font-family: 'Times New Roman';"><a href="tools_toolbar.htm#selection">Selection</a></span></p>
</td>
<td valign="middle" width="1000" style="width:1000px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Select whole structures or molecule fragments.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="133" style="width:133px; height:50px; border: solid 1px #000000;"><p style="text-align: center; text-indent: -92px; margin: 0px 0px 0px 92px;"><img src="delete.png" width="34" height="34" border="0" alt="delete" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="300" style="width:300px; height:50px; border: solid 1px #000000;"><p style="text-align: center; text-indent: -92px; margin: 0px 0px 0px 92px;"><span style="font-size: 12pt; font-family: 'Times New Roman';"><a href="tools_toolbar.htm#delete">Delete</a></span></p>
</td>
<td valign="middle" width="1000" style="width:1000px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Erase one or more structures or molecule fragments.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="133" style="width:133px; height:50px; border: solid 1px #000000;"><p style="text-align: center; text-indent: -92px; margin: 0px 0px 0px 92px;"><img src="single_bond.png" width="35" height="28" border="0" alt="single_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="300" style="width:300px; height:50px; border: solid 1px #000000;"><p style="text-align: center; text-indent: -92px; margin: 0px 0px 0px 92px;"><span style="font-size: 12pt; font-family: 'Times New Roman';"><a href="tools_toolbar.htm#bonds">Bonds</a></span></p>
</td>
<td valign="middle" width="1000" style="width:1000px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Open the complete list of chemical bonds including several query bond types.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="133" style="width:133px; height:50px; border: solid 1px #000000;"><p style="text-align: center; text-indent: -92px; margin: 0px 0px 0px 92px;"><img src="chain.png" width="35" height="35" border="0" alt="chain" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="300" style="width:300px; height:50px; border: solid 1px #000000;"><p style="text-align: center; text-indent: -92px; margin: 0px 0px 0px 92px;"><span style="font-size: 12pt; font-family: 'Times New Roman';"><a href="tools_toolbar.htm#chain">Chain</a></span></p>
</td>
<td valign="middle" width="1000" style="width:1000px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Draw an alkyl chain of arbitrary length.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="133" style="width:133px; height:50px; border: solid 1px #000000;"><p style="text-align: center; text-indent: -92px; margin: 0px 0px 0px 92px;"><img src="increase%20charge.png" width="34" height="34" border="0" alt="Increase charge" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="300" style="width:300px; height:50px; border: solid 1px #000000;"><p style="text-align: center; text-indent: -92px; margin: 0px 0px 0px 92px;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Increase Charge</span></p>
</td>
<td valign="middle" width="1000" style="width:1000px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Increase the charge of the selected atom with one unit (i.e. adds a positive charge to the atom).</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="133" style="width:133px; height:50px; border: solid 1px #000000;"><p style="text-align: center; text-indent: -92px; margin: 0px 0px 0px 92px;"><img src="decrease%20charge.png" width="34" height="34" border="0" alt="Decrease charge" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="300" style="width:300px; height:50px; border: solid 1px #000000;"><p style="text-align: center; text-indent: -92px; margin: 0px 0px 0px 92px;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Decrease Charge</span></p>
</td>
<td valign="middle" width="1000" style="width:1000px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Decrease the charge of the selected atom with one unit (i.e. adds a negative charge to the atom).</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="133" style="width:133px; height:50px; border: solid 1px #000000;"><p style="text-align: center; text-indent: -92px; margin: 0px 0px 0px 92px;"><img src="atom%20properties.png" width="34" height="34" border="0" alt="Atom properties" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="300" style="width:300px; height:50px; border: solid 1px #000000;"><p style="text-align: center; text-indent: -92px; margin: 0px 0px 0px 92px;"><span style="font-size: 12pt; font-family: 'Times New Roman';"><a href="tools_toolbar.htm#atomproperties">Atom Properties</a></span></p>
</td>
<td valign="middle" width="1000" style="width:1000px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Set the atom properties of the selected atom (atomic symbol or number, isotope, and charge) in a popup window.</span></p>
</td>
</tr>
</table>
</div>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 24px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><a name="selection"></a><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Selection</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Clicking on the lower right corner of the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Selection</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button you can choose between two options:</span></p>
<div style="text-align: justify; text-indent: 0px; line-height: 1.50; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table cellspacing="0" cellpadding="0" border="0" style="border: none; border-spacing:0px;">
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="192" style="width:192px;"><p style="text-align: justify;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Rectangle Selection:</span></p>
</td>
<td valign="middle" width="50" style="width:50px;"><p style="text-align: justify;"><img src="rectangle%20selection.png" width="34" height="34" border="0" alt="Rectangle selection"></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="192" style="width:192px;"><p style="text-align: justify;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Freehand Selection:</span></p>
</td>
<td valign="middle" width="50" style="width:50px;"><p style="text-align: justify;"><img src="freehand%20selection.png" width="34" height="34" border="0" alt="Freehand selection"></p>
</td>
</tr>
</table>
</div>
<p style="text-align: justify; line-height: 1.50; margin: 10px 0px 0px 0px;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Using either selection mode, an atom or a bond can be selected by clicking on it. Please note that in this case the bond gets selected along with the connected atoms. If you want to select a bond without its atoms, you have to choose Freehand Selection, and select the middle of the bond. Alternatively, press the "Ctrl" button and click on the bond, while either selection tool is active.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">With Rectangle Selection, you can select an area in rectangle mode while with Freehand Selection the area swept by the red line will be selected.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The background of atoms and bonds will become green when they have been selected. In either selection mode, hovering over an atom or a bond, its background becomes green. When you click on this background, the green highlight gets lighter indicating that this part got selected. &nbsp; &nbsp; &nbsp; &nbsp;</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The whole structure can be selected by double-clicking on it. The result is the same as it would be using Rectangle Selection. Whole structures or fragments of molecules, or even several molecules can be selected at the same time.</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Separate parts of a molecule or even different structures can be selected at the same time if the "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Shift</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" button is pressed during selection.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">The "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Ctrl+A</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" keyboard shortcut can also be used to select the whole content of the canvas.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">To clear the selection, you can press the "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Esc</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" button, or simply click on a blank area on the canvas. If there is not any selection on the canvas, pressing the "Esc" button activates the Rectangle </span><span style="font-size: 12pt; font-family: 'Times New Roman';">Selection</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> tool.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 24px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><a name="delete"></a><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Delete</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">This button lets you erase atoms and/or bonds, or even parts of structures in a molecule. Once you have made a selection, you can delete it either by clicking on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Delete</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button in the toolbar or pressing the "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Del</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" button on the keyboard.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Selecting the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Delete</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> tool and clicking on an atom or a bond erases said atom or bond. Please note that deleting a bond connecting to a terminal atom deletes the terminal atom, too. In any other case the atoms are not erased when the bond between them is deleted. On the other hand, if you erase an atom, its bonds are deleted, too.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Delete</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button also works as a rectangle selection tool: click on the button and select the parts you want to erase (the selected parts are highlighted in red). When you release the left mouse button, the highlighted parts will be erased.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">You can clear the whole canvas with one click using the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"><a href="general_toolbar.htm#clear">Clear</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">General</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar or pressing the "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Ctrl+Del</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" keyboard shortcut.</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 24px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><a name="bonds"></a><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman';">Bonds</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50;"><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">You can choose any type of bonds by clicking on the lower right corner of the</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman';"> Bonds </span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">"combo" button in the toolbar. Among these bonds you can find several query bond types, too. You can also use keyboard shortcuts to choose a chemical bond type to connect two atoms or alter an already existing bond. See below the complete list of chemical bonds available in Marvin for JavaScript and their respective keyboard shortcuts:</span></p>
<div style="text-align: left; text-indent: 0px; line-height: 1.50; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table cellspacing="0" cellpadding="0" border="0" style="border: none; border-spacing:0px; border-collapse: collapse;">
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; height:27px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Bond symbol</span></p>
</td>
<td valign="middle" width="439" style="width:439px; height:27px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Bond type</span></p>
</td>
<td valign="middle" width="151" style="width:151px; height:27px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Shortcut</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; border: solid 1px #000000;"><p style="text-align: center;"><img src="single_bond.png" width="35" height="28" border="0" alt="single_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Single" bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">1</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; border: solid 1px #000000;"><p style="text-align: center;"><img src="double_bond.png" width="35" height="28" border="0" alt="double_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Double" bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">2</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; border: solid 1px #000000;"><p style="text-align: center;"><img src="triple_bond.png" width="34" height="29" border="0" alt="triple_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Triple" bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">3</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; border: solid 1px #000000;"><p style="text-align: center;"><img src="aromatic_bond.png" width="35" height="28" border="0" alt="aromatic_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Aromatic" bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">4</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; border: solid 1px #000000;"><p style="text-align: center;"><img src="coordinate_bond.png" width="35" height="30" border="0" alt="coordinate_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Coordinate" bond.</span></p>
</td>
<td valign="middle" width="151" style="width:151px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">-</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; border: solid 1px #000000;"><p style="text-align: center;"><img src="single_up_bond.png" width="35" height="28" border="0" alt="single_up_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Single Up" wedge bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">5</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; border: solid 1px #000000;"><p style="text-align: center;"><img src="single_down_bond.png" width="35" height="28" border="0" alt="single_down_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Single Down" wedge bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">6</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; border: solid 1px #000000;"><p style="text-align: center;"><img src="single_up_down_bond.png" width="35" height="27" border="0" alt="single_up_down_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Single Up or Down" query bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">7</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; border: solid 1px #000000;"><p style="text-align: center;"><img src="double_cis_trans_bond.png" width="35" height="30" border="0" alt="double_cis_trans_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Double Cis or Trans" query bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">-</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; border: solid 1px #000000;"><p style="text-align: center;"><img src="unspecified_bond.png" width="35" height="30" border="0" alt="unspecified_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Double C/T or Unspecified" query bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">-</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; border: solid 1px #000000;"><p style="text-align: center;"><img src="single_double_bond.png" width="35" height="29" border="0" alt="single_double_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Single or Double" query bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">12</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; border: solid 1px #000000;"><p style="text-align: center;"><img src="single_aromatic_bond.png" width="35" height="30" border="0" alt="single_aromatic_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Single or Aromatic" query bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">14</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; border: solid 1px #000000;"><p style="text-align: center;"><img src="double_aromatic_bond.png" width="35" height="29" border="0" alt="double_aromatic_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Double or Aromatic" query bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">24</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="123" style="width:123px; border: solid 1px #000000;"><p style="text-align: center;"><img src="any_bond.png" width="35" height="31" border="0" alt="any_bond" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="439" style="width:439px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">"Any" query bond</span></p>
</td>
<td valign="middle" width="151" style="width:151px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">0</span></p>
</td>
</tr>
</table>
</div>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 24px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><a name="chain"></a><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Chain</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Chain</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button provides an easy solution for creating alkyl chains of arbitrary length. After selecting the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Chain</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Tools</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> </span><span style="font-size: 12pt; font-family: 'Times New Roman';">toolbar</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> you can draw the alkyl chain by dragging or clicking repeatedly on the starting atom. The length of the chain is shown on the tip of the cursor. During dragging, the chain can be rotated on the canvas.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">You can create a conjugated polyene chain by pressing the "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Ctrl</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" button when chain-drawing.</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 24px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><a name="atomproperties"></a><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Atom Properties</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">When a selection contains atoms, this button can be used to open the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Atom Properties</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> window. If more than one atom is selected when the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Atom Properties</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> window is opened, the changes will be applied to all of these atoms.</span></p>
<p style="text-align: justify; line-height: 1.50;"><img src="properties.png" width="348" height="214" border="0" alt="properties"></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Here you can change the selected atom(s) by setting either new atomic symbols or atomic numbers. Note that these two boxes are synchronized. You can also use this popup window to define atomic properties, such as charge and isotope (mass number). The "OK" button closes the window, and the changes appear in the structure. If you click on the "[X]" button in the upper right corner of the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Atom Properties</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> box or press the "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Esc</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" button, the popup window gets closed without applying any changes to the selected atom.</span></p>

</td></tr></table>

</body>
</html>
