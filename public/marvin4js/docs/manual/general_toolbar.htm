﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<!-- saved from url=(0029)http://www.helpandmanual.com/ -->
<head>
   <title>General Toolbar</title>
   <meta name="generator" content="Help &amp; Manual" />
   <meta name="keywords" content="" />
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="Content-Style-Type" content="text/css" />
   <link type="text/css" href="default.css" rel="stylesheet" />
<script type="text/javascript" src="helpman_topicinit.js"></script>
<!-- Redirect browser to frame page if page is not in the content frame. -->
<script type="text/javascript">
<!--
if (location.search.lastIndexOf("toc=0")<=0) {
  if (parent.frames.length==0) { parent.location.href="index.html?general_toolbar.htm"; }
  else { parent.quicksync('a1.1'); }
}
//-->
</script>
<script type="text/javascript" src="highlight.js"></script></head>
<body style="margin: 0px 0px 0px 0px; background: #FFFFFF;" onload="highlight();">
<div id="hmpopupDiv" style="visibility:hidden; position:absolute; z-index:1000; "></div>


<table width="100%" border="0" cellspacing="0" cellpadding="5" bgcolor="#D0D0D0">
  <tr valign="middle">
    <td align="left">
      <p style="line-height: 1.50;"><span class="f_Heading1" style="font-family: 'Times New Roman';">General Toolbar</span></p>

    </td>
    <td align="right">
     <span style="font-size: 9pt">
     <a href="layout_of_marvinsketch.htm">Top</a>&nbsp;
     <a href="layout_of_marvinsketch.htm">Previous</a>&nbsp;
     <a href="tools_toolbar.htm">Next</a>
     </span>
    </td>
  </tr>
</table>


<!-- Placeholder for topic body. -->
<table width="100%" border="0" cellspacing="0" cellpadding="5"><tr valign="top"><td align="left">
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">General</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar contains buttons for file management (Import, Export) and for general editing/viewing of the canvas content (Clear, Undo/Redo, Cut/Copy/Paste, Zoom, Mirror, Clean, and View Settings).</span></p>
<p style="line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Elements of the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">General</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar:</span></p>
<div style="text-align: left; text-indent: 0px; line-height: 1.50; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table width="100%" cellspacing="0" cellpadding="0" border="0" style="border: none; border-spacing:0px; border-collapse: collapse;">
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><img src="clear.png" width="34" height="34" border="0" alt="Clear" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><a name="clear"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Clear</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Clear the whole canvas without saving its content.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><img src="open.png" width="34" height="34" border="0" alt="Open" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Import</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Load an already existing file into Marvin for JavaScript. The supported file types are MDL Molfiles V2000 (.mol extension) and ChemAxon Marvin Documents (.mrv extension).</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><img src="save.png" width="34" height="34" border="0" alt="Save" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Export</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Save your structures in one of the following file formats: MDL Molfiles V2000 (.mol extension) or ChemAxon Marvin Documents (.mrv extension).</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><img src="undo.png" width="34" height="34" border="0" alt="Undo" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><a name="undo"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Undo</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Revert the last commands you applied.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><img src="redo.png" width="34" height="34" border="0" alt="Redo" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Redo</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Revert the effect of the last "Undo" command.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><img src="cut.png" width="34" height="34" border="0" alt="Cut" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Cut</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Place the selection on the clipboard, while removing the original structure from the canvas.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><img src="copy.png" width="34" height="34" border="0" alt="Copy" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Copy</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Place the selection on the clipboard, while leaving the original structure unchanged.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><img src="paste.png" width="34" height="34" border="0" alt="Paste" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Paste</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Insert the content of the clipboard onto the canvas.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><img src="zoom%20in.png" width="34" height="34" border="0" alt="Zoom in" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><a name="zoomin"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Zoom in</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Increase the zoom ratio.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><img src="zoom%20out.png" width="34" height="34" border="0" alt="Zoom out" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Zoom out</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Decrease the zoom ratio.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><img src="zoom%20all.png" width="34" height="34" border="0" alt="Zoom All" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Zoom All</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Change the zoom ratio to the optimal value to view everything on the canvas.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><img src="mirror%20horizontally.png" width="34" height="34" border="0" alt="Mirror horizontally" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><a name="mirrorhorizontally"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Mirror Horizontally</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Mirror the selected molecule or fragment horizontally.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><img src="mirror%20vertically.png" width="34" height="34" border="0" alt="Mirror vertically" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><a name="mirrorvertically"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Mirror Vertically</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Mirror the selected molecule or fragment vertically.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><img src="clean%202d.png" width="34" height="34" border="0" alt="Clean 2D" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><a name="clean"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">Clean</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Clean the structure(s) on the canvas in 2D by recalculating the atomic coordinates.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><img src="view%20settings.png" width="34" height="34" border="0" alt="View Settings" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><a name="viewsettings"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">View Settings</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Set display properties in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">View Settings</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> dialog box.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="89" style="width:89px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><img src="help.png" width="34" height="34" border="0" alt="Help" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="193" style="width:193px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">About Marvin JS</span></p>
</td>
<td valign="middle" width="1019" style="width:1019px; height:50px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Displays information about the application (name, version) and a link to the Users Manual.</span></p>
</td>
</tr>
</table>
</div>

</td></tr></table>

</body>
</html>
