dictwords = ["basic 0 12 192 4 10 16",
	"features 0 12 192 2 16 192 4 18 18 7 18 64",
	"marvin 0 39 235 2 83 255 3 24 152 4 39 178 5 18 66 7 54 117",
	"for 0 74 255 1 10 2 2 107 255 3 24 152 4 39 178 5 47 86 6 10 1 7 99 127",
	"javascript 0 30 235 2 83 255 3 24 152 4 39 178 5 18 66 7 33 101",
	"importing 0 8 64 4 10 16",
	"and 0 137 127 1 18 48 2 134 127 4 33 25 5 18 20 7 78 119",
	"saving 0 12 96 4 10 16 7 10 32",
	"structures 0 39 126 2 66 105 4 33 29 7 47 41",
	"structure 0 69 127 2 84 86 4 26 3 5 33 75 7 26 48",
	"handles 0 4 64",
	"mdl 0 21 64 2 26 68 7 18 32",
	"molfiles 0 4 64 7 18 32",
	"v2000 0 8 64 7 18 32",
	"extension 0 8 64",
	"chemaxon 0 30 64 7 18 32",
	"documents 0 4 64 7 18 32",
	"input 0 8 64",
	"files 0 4 64",
	"however 0 15 74",
	"other 0 27 126 2 33 41",
	"file 0 33 96 2 26 4 7 33 32",
	"types 0 15 74 2 18 4 7 40 47",
	"smiles 0 15 64 2 10 4",
	"extended 0 8 64",
	"inchi 0 8 64",
	"name 0 12 96 2 10 32 5 10 8 7 10 16",
	"cml 0 8 64",
	"molfile 0 18 64 2 10 64",
	"v3000 0 8 64",
	"sdfile 0 15 64",
	"compressed 0 15 64",
	"xyz 0 8 64",
	"can 0 160 127 1 60 62 2 154 127 3 10 2 5 33 97 7 94 15",
	"also 0 42 124 1 18 10 2 10 32 5 10 64 7 40 7",
	"imported 0 8 80 2 10 64",
	"the 0 160 127 1 158 63 2 254 127 3 18 6 5 180 127 6 108 59 7 206 127",
	"appropriate 0 27 94 2 18 17 5 18 32 6 10 1",
	"webservice 0 8 64 2 18 16",
	"available 0 24 114 2 54 52 5 26 36 7 33 37",
	"press 0 21 109",
	"import 0 15 64 6 10 8 7 18 32",
	"button 0 99 127 1 47 63 2 60 53 6 10 16 7 72 111",
	"general 0 21 113 1 18 10 2 18 16 3 10 1 5 10 4 6 10 32 7 26 32",
	"toolbar 0 57 125 1 33 38 2 40 53 3 33 1 7 78 63",
	"open 0 8 64 5 33 44 7 10 8",
	"pop-up 0 12 66 2 26 100 3 10 1 4 10 16 5 123 254 7 18 3",
	"window 0 36 115 2 18 36 5 10 16 6 18 8 7 18 3",
	"dialog 0 53 123 2 54 37 5 10 16 6 26 40 7 10 16",
	"opened 0 4 64 5 10 64",
	"pressing 0 42 126 2 18 33",
	"ctrl 0 33 120 1 10 2 6 78 14 7 10 4",
	"keyboard 0 24 112 1 10 1 6 18 33 7 18 4",
	"shortcut 0 24 104 1 18 3 6 18 33 7 10 4",
	"too 0 21 91 1 10 4 2 26 22 7 18 6",
	"format 0 21 64 2 18 68",
	"with 0 85 127 1 26 12 2 94 63 5 40 75 7 40 9",
	"text 0 8 96",
	"editor 0 8 96",
	"e.g 0 8 72 2 33 86 5 10 32 7 10 2",
	"windows&#39;s 0 4 64",
	"notepad 0 4 64",
	"select 0 57 126 4 10 8 5 10 32 6 10 4 7 33 10",
	"all 0 12 67 1 10 4 2 18 10 5 10 2 6 18 6 7 10 16",
	"copy 0 21 112 4 10 8 5 10 4 6 10 4 7 18 32",
	"contents 0 8 64 1 18 24 4 10 32",
	"clipboard 0 12 112 5 18 4 7 26 48",
	"then 0 12 82 2 10 1",
	"paste 0 15 112 4 10 8 5 10 4 6 10 2 7 18 48",
	"into 0 15 120 5 18 34 7 10 32",
	"area 0 27 118",
	"top 0 4 64",
	"you 0 137 127 1 26 52 2 84 119 3 10 2 5 26 32 7 84 47",
	"from 0 57 127 2 40 114 5 18 20 7 60 43",
	"drop-down 0 12 65",
	"list 0 24 91 2 60 35 7 40 13",
	"finally 0 8 66",
	"loads 0 4 64",
	"canvas 0 83 127 1 83 254 2 26 48 3 18 6 4 10 16 5 54 23 6 26 24 7 84 59",
	"saved 0 4 64",
	"either 0 27 114 1 10 32 2 33 70",
	"document 0 8 64",
	"order 0 21 111 2 18 16 5 10 32",
	"export 0 8 64 7 18 32",
	"formats 0 15 96 7 10 32",
	"inchikey 0 4 64",
	"cdx 0 8 96",
	"skc 0 8 96",
	"has 0 21 94 2 54 46 7 10 64",
	"save 0 24 96 6 10 8 7 10 32",
	"click 0 42 117 1 10 16 7 10 8",
	"which 0 36 87 2 78 79 5 10 32 7 18 9",
	"opens 0 8 65 2 10 1 5 18 16 6 18 8 7 10 1",
	"alternatively 0 12 96 2 18 33",
	"use 0 27 91 2 26 34 7 18 6",
	"well 0 8 80 2 10 1",
	"this 0 53 126 1 26 7 2 18 6 7 18 3",
	"see 0 15 70 2 10 8 5 10 2 7 26 69",
	"default 0 24 91",
	"changed 0 8 80 1 10 4",
	"selecting 0 30 126 2 10 32 6 10 1 7 10 1",
	"option 0 21 123 2 18 96 5 10 4 7 10 8",
	"supported 0 8 32 7 10 32",
	"that 0 45 63 2 54 47 5 10 2 7 10 2",
	"push 0 4 32",
	"download 0 4 32",
	"bottom 0 4 32",
	"created 0 15 56 2 10 2",
	"link 0 4 32 7 10 16",
	"preferred 0 4 32 2 10 1",
	"route 0 4 32",
	"source 0 4 32",
	"there 0 12 34 5 33 38 6 10 16 7 18 8",
	"case 0 45 63 2 33 54 5 10 4",
	"only 0 33 46 1 10 32 2 72 62 5 54 55",
	"first 0 8 32 5 10 32",
	"carried 0 4 32",
	"out 0 4 32 1 10 8 7 18 80",
	"image 0 30 54",
	"are 0 59 63 2 99 125 3 10 4 5 40 37 7 40 105",
	"png 0 4 32",
	"jpeg 0 4 32",
	"set 0 21 42 2 18 2 7 10 16",
	"display 0 33 51 2 10 4 4 18 6 7 10 16",
	"options 0 39 51 2 10 32 4 18 12 5 26 36 7 18 24",
	"detailed 0 4 32 2 18 34",
	"elsewhere 0 4 32",
	"determine 0 4 32 7 10 16",
	"color 0 8 33",
	"background 0 15 32 2 18 48",
	"transparent 0 4 32",
	"white 0 4 32",
	"width 0 4 32",
	"height 0 4 32",
	"delete 0 27 34 4 10 8 5 26 26 7 10 8",
	"selection 0 55 36 1 10 2 2 10 32 5 60 55 6 40 18 7 40 56",
	"clicking 0 48 62 2 10 1 5 10 16 7 10 4",
	"lower 0 4 32 7 26 13",
	"right 0 12 33 7 26 13",
	"corner 0 8 33 7 26 13",
	"tools 0 21 57 1 10 32 2 10 32 3 10 1 5 26 19 7 33 26",
	"choose 0 18 54 7 33 7",
	"between 0 24 63 2 18 65",
	"two 0 36 63 2 47 14 7 10 4",
	"rectangle 0 21 32 6 10 16",
	"freehand 0 12 32",
	"using 0 42 59 1 18 36 2 66 121",
	"mode 0 18 40 1 10 16",
	"atom 0 144 63 1 10 1 2 120 127 4 18 5 5 72 48 6 26 1 7 136 11",
	"bond 0 136 63 2 47 56 5 60 41 7 120 14",
	"selected 0 107 63 1 18 1 2 26 48 5 47 19 7 33 89",
	"please 0 12 36",
	"note 0 27 61 2 10 8 7 10 2",
	"gets 0 12 33",
	"along 0 4 32 2 10 2 5 10 8",
	"connected 0 15 37 5 10 1 7 10 1",
	"atoms 0 101 63 2 124 95 3 10 1 4 10 8 5 18 24 6 10 1 7 78 23",
	"want 0 27 60 2 10 2 5 10 32",
	"without 0 15 45 2 10 4 5 10 8 7 10 32",
	"its 0 48 62 2 47 38 5 33 8 7 26 97",
	"have 0 15 34 2 47 30 7 10 8",
	"middle 0 4 32 1 10 16",
	"while 0 33 47 2 26 25 5 18 12 7 18 48",
	"tool 0 24 56 1 10 8 2 10 1 6 18 18 7 10 64",
	"active 0 18 56 1 10 8 2 10 16 5 54 45 6 10 16 7 10 64",
	"swept 0 4 32",
	"red 0 8 32 2 10 16",
	"line 0 4 32",
	"will 0 103 63 1 10 8 2 47 116 5 33 60 6 10 16 7 10 16",
	"bonds 0 33 62 2 54 76 4 10 8 5 10 8 7 54 28",
	"become 0 12 37 1 10 32",
	"green 0 18 38 2 10 32",
	"when 0 71 63 1 10 32 2 26 11 5 10 1 7 18 9",
	"they 0 24 59 2 26 88 5 10 16",
	"hovering 0 8 48",
	"over 0 33 60 2 10 32 7 10 64",
	"becomes 0 4 32",
	"highlight 0 4 32",
	"lighter 0 4 32",
	"indicating 0 15 44 5 10 2",
	"part 0 24 54 2 26 97",
	"got 0 4 32",
	"whole 0 33 38 2 10 64 5 18 9 7 18 40",
	"molecules 0 39 62 2 72 126 3 10 2 7 10 1",
	"double-clicking 0 4 32",
	"them 0 24 54 2 18 24 5 10 4",
	"result 0 12 37 5 10 16",
	"same 0 30 54 1 10 2 2 47 57 5 10 16",
	"would 0 4 32",
	"fragments 0 18 46 2 26 48 7 18 8",
	"even 0 8 32 2 18 2",
	"several 0 21 50 2 33 39 7 33 29",
	"time 0 18 50",
	"separate 0 8 40",
	"parts 0 18 36 2 10 32",
	"molecule 0 55 46 2 94 127 4 10 4 5 33 3 7 18 8",
	"different 0 12 40 2 47 25",
	"shift 0 27 62",
	"pressed 0 4 32",
	"during 0 15 46",
	"used 0 24 59 1 18 10 2 10 16 7 18 2",
	"content 0 12 48 5 10 4 7 26 48",
	"clear 0 12 32 5 10 2 7 26 32",
	"esc 0 12 33 6 10 32",
	"simply 0 4 32",
	"blank 0 4 32",
	"not 0 27 62 2 78 31 4 10 1 5 10 32 6 10 16 7 18 33",
	"any 0 30 55 2 40 23 6 26 17 7 72 7",
	"activates 0 4 32 6 18 34",
	"once 0 4 32 7 10 8",
	"made 0 8 40",
	"fragment 0 33 38 5 26 1",
	"del 0 8 32 6 18 24",
	"erases 0 4 32 5 18 10",
	"said 0 4 32",
	"deleting 0 4 32 2 18 80",
	"disappear 0 4 32 2 26 96",
	"hand 0 4 32",
	"erase 0 8 32 7 10 8",
	"deleted 0 4 32 2 10 16",
	"works 0 4 32",
	"highlighted 0 12 36",
	"release 0 8 48",
	"left 0 15 53 7 10 1",
	"mouse 0 15 46 1 26 12",
	"erased 0 4 32",
	"erasing 0 4 32",
	"objects 0 4 32 2 10 16",
	"contextual 0 8 32 5 10 8",
	"menu 0 21 34 2 26 100 5 132 62",
	"after 0 15 46 2 26 33 7 18 3",
	"right-click 0 12 34 5 40 44",
	"item 0 4 32 5 10 4",
	"one 0 45 62 1 10 1 2 54 15 5 10 1 7 47 41",
	"cut 0 8 32 4 10 8 6 10 4 7 18 32",
	"buttons 0 15 51 1 10 8 7 33 104",
	"place 0 27 62 2 10 32 7 33 49",
	"virtual 0 4 32",
	"copy-paste 0 4 32",
	"function 0 4 32 1 10 4 6 18 33 7 10 8",
	"within 0 8 36",
	"application 0 4 32 7 10 16",
	"their 0 21 27 2 26 56 5 10 16 7 18 12",
	"conventional 0 4 16",
	"shortcuts 0 4 16 4 10 1 6 32 162 7 26 6",
	"cursor 0 61 28 1 18 20 2 26 33 6 26 1 7 33 67",
	"move 0 18 22 1 18 20 7 10 8",
	"dragging 0 45 30 1 10 16 7 18 9",
	"sign 0 4 16",
	"appears 0 12 20 1 10 16 7 18 3",
	"beside 0 4 16",
	"placed 0 18 16 2 10 32",
	"new 0 48 30 2 10 16",
	"object 0 4 16 1 10 1 5 40 35 7 10 16",
	"remains 0 12 18",
	"been 0 12 28 2 10 32",
	"multiplied 0 4 16",
	"times 0 8 16 7 10 1",
	"drawing 0 36 28 2 10 4 4 40 12 6 10 2 7 33 15",
	"chemical 0 15 24 2 39 224 4 18 10 5 26 72 7 18 12",
	"draw 0 15 24 7 18 10",
	"modify 0 8 24",
	"already 0 18 28 7 26 37",
	"existing 0 24 28 7 26 37",
	"inserted 0 4 16 2 10 1",
	"most 0 4 16 2 10 2 7 18 3",
	"frequently 0 4 16 7 18 2",
	"chosen 0 12 21 7 10 2",
	"tip 0 21 28 2 10 32 7 18 3",
	"left-click 0 15 28 2 10 32 7 18 3",
	"known 0 4 16 2 10 4 6 10 1",
	"elements 0 8 16 2 18 24 3 10 8 5 47 58 6 10 1 7 40 3",
	"periodic 0 15 16 2 18 1 7 47 3",
	"table 0 12 16 2 18 1 4 10 32 7 60 7",
	"find 0 4 16 2 10 2 7 10 4",
	"choosing 0 8 18",
	"element 0 8 18",
	"atomic 0 30 27 2 10 4 7 26 18",
	"symbol 0 18 30 6 26 1 7 18 6",
	"appear 0 27 31 2 18 48 5 26 50 7 10 32",
	"closes 0 8 17 2 10 32 6 10 32",
	"automatically 0 18 21",
	"typing 0 18 24 6 10 1",
	"enter 0 4 16 1 18 3 6 18 2",
	"although 0 4 16",
	"listed 0 4 16",
	"deuterium 0 8 17 6 10 1",
	"tritium 0 8 17 6 10 1",
	"symbols 0 15 19 7 10 2",
	"respectively 0 12 21 2 10 8",
	"way 0 4 16 2 10 1",
	"non-metallic 0 4 16",
	"completed 0 4 16",
	"implicit 0 27 21 2 10 8",
	"hydrogens 0 27 21 2 10 8",
	"according 0 15 25",
	"free 0 4 16",
	"valences 0 4 16",
	"depends 0 4 16",
	"current 0 4 16",
	"view 0 15 17 1 10 2 2 10 4 7 33 48",
	"settings 0 15 17 2 10 4 7 26 48",
	"box 0 15 25 7 10 16",
	"metallic 0 4 16",
	"exceptions 0 4 16",
	"rule 0 4 16",
	"added 0 15 19 2 18 5",
	"zero 0 4 16",
	"oxidation 0 4 16",
	"state 0 4 16",
	"replaced 0 8 20",
	"above 0 4 16 5 10 16",
	"mentioned 0 8 18 5 10 16",
	"methods 0 4 16",
	"change 0 36 28 2 10 4 7 10 16",
	"simultaneously 0 4 16 5 10 4",
	"system 0 12 20",
	"instead 0 4 16 5 10 8",
	"standard 0 8 18",
	"label 0 12 18 2 40 27",
	"defining 0 8 18",
	"alias 0 8 18 7 10 1",
	"properties 0 48 19 2 26 68 4 10 4 5 26 16 7 10 16",
	"query 0 18 18 2 104 15 4 10 1 7 108 15",
	"these 0 30 27 2 47 60 5 18 34 7 18 5",
	"combo 0 12 24 2 18 5 7 47 15",
	"pseudo 0 24 18 2 26 4 7 18 1",
	"stand 0 4 16",
	"type 0 63 31 2 18 5 7 47 7",
	"through 0 4 16 2 18 36",
	"question 0 8 18 5 10 16",
	"mark 0 4 16",
	"define 0 4 16 2 10 4 7 10 1",
	"your 0 8 24 2 18 6 3 10 2 7 10 32",
	"close 0 15 22 2 10 1",
	"cannot 0 12 22 2 10 4",
	"create 0 24 24 2 26 35",
	"drag 0 4 16 4 10 4",
	"length 0 18 28 7 10 8",
	"carbon 0 18 17 2 33 24 7 18 1",
	"both 0 4 16 2 18 24",
	"ends 0 8 18",
	"grey 0 15 30",
	"colored 0 12 21",
	"ended 0 4 16",
	"key 0 24 30 2 10 1",
	"direction 0 8 24",
	"position 0 18 22 1 10 4 2 10 8",
	"just 0 4 16 2 10 2",
	"like 0 4 16 2 10 2",
	"previous 0 4 16",
	"value 0 8 18 7 10 16",
	"end 0 4 16 7 18 3",
	"activated 0 4 8",
	"given 0 12 11 5 10 8",
	"repeated 0 4 8",
	"depending 0 4 8 2 10 4",
	"currently 0 8 10 1 10 8 2 10 32 7 10 64",
	"single 0 27 12 2 18 4 5 10 2 7 47 6",
	"successive 0 8 8",
	"clicks 0 8 8",
	"single-double-triple-single 0 4 8",
	"double 0 8 8 2 47 24 7 47 22",
	"alternate 0 4 8",
	"triple 0 4 8 7 10 4",
	"directed 0 4 8",
	"down 0 18 14 7 18 4",
	"coordinate 0 4 8 7 10 4",
	"much 0 4 8",
	"faster 0 4 8",
	"feature 0 4 8 2 10 16",
	"framework 0 4 8",
	"complex 0 12 8",
	"releasing 0 8 10",
	"dots 0 12 8",
	"potential 0 4 8",
	"directions 0 4 8",
	"next 0 4 8",
	"vertices 0 4 8",
	"hexagon 0 4 8",
	"rearranged 0 4 8",
	"pentagon 0 4 8",
	"towards 0 4 8",
	"add 0 4 8 2 10 2",
	"last 0 4 8 5 10 16 7 18 32",
	"single-double-triple 0 4 8",
	"sequence 0 4 8",
	"more 0 24 15 2 47 47 5 18 65 7 26 9",
	"than 0 12 10 5 10 1",
	"every 0 21 11 2 33 6",
	"connecting 0 15 12 2 10 32",
	"templates 0 21 12 3 10 1 7 40 1",
	"alkyl 0 8 8 7 10 8",
	"conjugated 0 8 8",
	"hydrocarbon 0 4 8",
	"chains 0 4 8",
	"arbitrary 0 4 8 2 10 1 7 10 8",
	"chain 0 21 8 7 18 8",
	"start 0 8 8",
	"repeatedly 0 4 8",
	"starting 0 12 10",
	"shown 0 4 8",
	"rotated 0 18 12 2 10 16",
	"around 0 18 14 2 26 40 7 10 1",
	"desired 0 8 12",
	"orientation 0 8 12",
	"polyene 0 4 8",
	"chain-drawing 0 4 8",
	"supports 0 8 10 2 10 4",
	"substructure 0 4 8 2 33 80 4 10 2",
	"group 0 18 9 2 112 122 5 78 60 7 18 3",
	"s-group 0 4 8",
	"generic 0 4 8 2 26 64 4 10 2 7 10 1",
	"s-groups 0 4 8 2 60 64 4 26 3",
	"visualizing 0 4 8",
	"solvent 0 4 8",
	"compounds 0 4 8",
	"predefined 0 4 8 2 18 96",
	"abbreviated 0 15 8 2 78 112 4 10 1 5 33 12 7 18 8",
	"groups 0 18 10 2 99 122 4 18 3 7 26 9",
	"assist 0 4 8",
	"molecular 0 4 8 2 10 2",
	"abbreviation 0 4 8 5 10 8",
	"appearing 0 4 8",
	"decide 0 4 8 2 10 32",
	"whether 0 4 8 2 18 36",
	"contracted 0 4 8 2 33 48 5 18 12",
	"expanded 0 4 8 2 60 48 5 18 12",
	"form 0 4 8 2 10 32",
	"latter 0 4 8 2 10 32",
	"expand 0 4 8 5 26 12",
	"checkbox 0 4 8 2 10 32",
	"closing 0 4 8",
	"where 0 4 8 2 18 33",
	"put 0 12 13 7 18 3",
	"connect 0 15 14 7 10 4",
	"learn 0 8 9",
	"about 0 12 13 2 33 14 7 18 16",
	"handling 0 4 8",
	"merging 0 15 12 4 10 4",
	"cal 0 4 8",
	"merge 0 8 12",
	"helpful 0 4 8",
	"ring 0 15 12",
	"systems 0 8 8",
	"spiro 0 8 12",
	"fused 0 8 12",
	"bridged 0 4 8",
	"common 0 4 8",
	"chain-like 0 4 4",
	"substructures 0 4 4",
	"should 0 15 7 2 18 16",
	"hover 0 18 4 7 10 64",
	"crossed 0 4 4",
	"arrows 0 4 4",
	"moved 0 8 4 1 10 16",
	"until 0 4 4 1 10 1",
	"overlap 0 4 4",
	"host 0 4 4",
	"merged 0 8 4 2 10 16",
	"template 0 51 4 7 10 1",
	"distance 0 4 4",
	"sufficiently 0 4 4",
	"small 0 4 4",
	"possible 0 8 6 2 18 20",
	"points 0 8 6 2 10 32",
	"indicated 0 4 4",
	"blue 0 4 4 2 18 32",
	"circles 0 4 4",
	"sprout 0 8 4 4 10 4 7 10 1",
	"non-terminal 0 8 4",
	"sprouted 0 8 4",
	"attach 0 8 4",
	"sprouting 0 12 4",
	"symmetric 0 4 4",
	"cyclohexane 0 4 4 7 10 1",
	"cyclopentane 0 4 4 7 18 1",
	"benzene 0 4 4 7 10 1",
	"pyrrole 0 12 4 7 10 1",
	"showing 0 4 4",
	"would-be 0 4 4",
	"non-symmetric 0 4 4",
	"including 0 4 4 7 33 11",
	"naphthalene 0 4 4 7 10 1",
	"original 0 18 6 7 18 48",
	"avoid 0 4 4",
	"hold 0 8 6",
	"attaching 0 4 4 2 18 6",
	"replace 0 8 6",
	"attached 0 8 6 2 10 2",
	"template&#39;s 0 4 4",
	"connects 0 12 6 2 10 8",
	"fit 0 4 4",
	"perfectly 0 4 4",
	"holding 0 4 4",
	"mirrors 0 4 4 5 18 1",
	"side 0 12 4 2 10 8",
	"altered 0 4 4",
	"space 0 4 4 2 10 1",
	"rotate 0 8 6 4 10 4 7 10 1",
	"alter 0 4 4 7 10 4",
	"easily 0 4 4",
	"transformations 0 4 4 4 10 4",
	"dragged 0 4 4 2 10 16",
	"translated 0 4 4",
	"modifications 0 4 4",
	"but 0 8 6 2 40 26",
	"contains 0 4 4 2 10 2 5 18 16 7 10 32",
	"distorted 0 8 4",
	"due 0 4 4",
	"changes 0 15 7",
	"lengths 0 4 4",
	"angles 0 4 4",
	"positions 0 4 4",
	"fixed 0 4 4 2 10 8",
	"non-selected 0 8 6",
	"rotation 0 30 6 2 10 8",
	"was 0 4 4",
	"center 0 24 6 1 10 1 2 60 30 5 10 2 7 18 17",
	"dimensions 0 4 4",
	"pink 0 4 4",
	"circle 0 4 4",
	"dot 0 12 6",
	"pivot 0 8 6",
	"point 0 15 6 2 10 32",
	"angle 0 4 2",
	"near 0 4 2",
	"done 0 4 2",
	"increments 0 4 2",
	"continuous 0 4 2",
	"rotating 0 4 2",
	"relocated 0 4 2",
	"snapped 0 4 2",
	"back 0 8 2",
	"allows 0 4 2",
	"wish 0 4 2",
	"moving 0 4 2",
	"lets 0 4 2",
	"displayed 0 21 3 2 10 32 5 10 16",
	"rest 0 4 2 5 10 1",
	"gray 0 4 2",
	"such 0 4 2",
	"cases 0 4 2 2 10 2 5 18 48 7 10 1",
	"mirroring 0 12 2",
	"mirrored 0 4 2 2 10 16 5 10 1",
	"horizontally 0 8 2 5 18 1",
	"vertically 0 8 2 5 18 1",
	"mirror 0 12 2 2 10 8 4 10 4 5 26 1",
	"concerned 0 4 2",
	"inside 0 4 2",
	"unattached 0 4 2",
	"unselected 0 4 2",
	"remain 0 4 2",
	"inactive 0 4 2",
	"rules 0 4 2 2 10 8",
	"undoing 0 4 2",
	"redoing 0 4 2",
	"kind 0 4 2",
	"transformation 0 4 2",
	"event 0 4 2",
	"begins 0 4 2",
	"action 0 4 2 2 10 32 6 18 4",
	"undone 0 4 2",
	"step 0 4 2",
	"charges 0 8 3",
	"isotopes 0 8 3",
	"enhanced 0 18 3 2 47 6 4 10 1",
	"stereo 0 18 3 2 72 30 4 18 1 5 10 2 7 18 16",
	"specifications 0 8 3 2 26 6 4 10 1",
	"attributes 0 4 2",
	"provides 0 4 2",
	"changing 0 4 2 2 18 80",
	"setting 0 8 3",
	"following 0 12 3 2 18 6 3 10 4 5 26 24 7 10 32",
	"r-group 0 4 2 2 33 1 7 10 2",
	"relevant 0 4 2 2 10 4",
	"property 0 12 2",
	"fields 0 4 2",
	"example 0 4 2 2 10 8 5 10 16",
	"three 0 4 2 2 10 8 5 10 32",
	"charge 0 15 3 2 18 20 7 47 8",
	"previously 0 8 2",
	"defined 0 8 2 2 10 1",
	"removed 0 4 2 5 10 4",
	"remove 0 8 2 2 10 64",
	"opening 0 4 2 2 10 32",
	"context 0 4 2 5 33 114",
	"those 0 4 2",
	"editable 0 4 2",
	"permitted 0 4 2",
	"applied 0 4 2 7 10 32",
	"&#39;alias 0 4 2",
	"textbox 0 4 2",
	"unchanged 0 4 2 7 10 16",
	"exception 0 4 2",
	"notations 0 8 2 2 10 4",
	"preserve 0 4 2",
	"values 0 4 2",
	"modified 0 12 3 2 10 4",
	"later 0 4 2",
	"off 0 18 3 2 18 6",
	"absolute 0 8 3 2 66 14 4 10 1 5 18 2",
	"chiral 0 12 3 2 120 30 5 18 2 7 10 16",
	"marked 0 4 2 2 18 12",
	"wedge 0 4 2 2 40 12 7 18 4",
	"individually 0 4 2",
	"assign 0 4 2 2 10 16",
	"stereogenic 0 4 2 2 33 6",
	"identifier 0 12 3 2 18 2",
	"number 0 15 3 2 33 19",
	"accompany 0 4 2",
	"leave 0 4 1",
	"field 0 4 1",
	"empty 0 4 1 5 10 4",
	"associated 0 4 1 2 10 4",
	"upper 0 12 1",
	"index 0 12 1",
	"mass 0 4 1 2 10 16",
	"isotope 0 4 1",
	"hydrogen 0 4 1 7 47 1",
	"specific 0 4 1",
	"popup 0 4 1",
	"closed 0 4 1",
	"applying 0 4 1",
	"increase 0 4 1 7 26 24",
	"decrease 0 4 1 7 26 24",
	"offered 0 4 1 2 10 8 7 10 64",
	"here 0 4 1 7 10 1",
	"show 0 12 1",
	"flag 0 8 1 2 26 4 5 10 2",
	"visibility 0 4 1",
	"turned 0 8 1 2 10 4",
	"valence 0 8 1 2 18 16",
	"errors 0 4 1 2 10 16",
	"marking 0 4 1",
	"incorrect 0 4 1",
	"checked 0 12 1",
	"indices 0 4 1",
	"cpk 0 4 1",
	"colors 0 4 1",
	"corey-pauling-koltun 0 4 1",
	"convention 0 4 1 2 18 8",
	"halves 0 4 1",
	"receive 0 4 1",
	"labels 0 8 1 2 33 24 7 18 16",
	"visible 0 4 1 1 10 32 2 18 32",
	"necessary 0 4 1 2 10 16",
	"disable 0 4 1",
	"hetero 0 15 1 7 10 1",
	"terminal 0 8 1",
	"carbons 0 4 1",
	"basic_features_of_marvin_for_j.htm 0 6 64",
	"navigating 1 10 32",
	"modifying 1 10 32 2 18 80 7 10 2",
	"zoom 1 54 47 5 26 2 6 18 2 7 60 48",
	"ratio 1 26 37 5 10 2 7 26 16",
	"navigate 1 40 56 7 18 40",
	"scrollbars 1 10 32",
	"former 1 10 32",
	"ones 1 10 32",
	"exceed 1 10 16",
	"size 1 10 16",
	"repositioned 1 10 16",
	"regardless 1 10 8",
	"reduced 1 10 8",
	"magnified 1 10 8",
	"wheel 1 18 4",
	"zooming 1 10 4 7 10 16",
	"centered 1 10 4",
	"virtually 1 10 4",
	"sets 1 10 2 5 10 2",
	"optimal 1 10 2 5 10 2 7 10 16",
	"magnification 1 10 2 5 10 2",
	"everything 1 10 2 5 18 2 7 10 16",
	"purpose 1 10 2",
	"increases 1 10 1 5 10 2",
	"placing 1 10 1 7 10 1",
	"unavailable 1 10 1 7 10 64",
	"least 1 10 1 2 10 8",
	"canvas.htm 1 6 64",
	"represent 2 47 71",
	"integral 2 10 64",
	"handle 2 10 64",
	"superatom 2 18 64 4 10 1",
	"containing 2 18 65 5 18 12",
	"exported 2 10 64",
	"mrv 2 10 64",
	"usually 2 10 64",
	"solvents 2 10 64",
	"visualized 2 10 64",
	"brackets 2 18 96",
	"status 2 18 80 5 10 8 7 10 64",
	"ungroup 2 10 64 5 10 8",
	"editing 2 18 80 5 26 84 7 18 40",
	"adding 2 10 64",
	"larger 2 10 32",
	"attachment 2 18 32",
	"abbreviations 2 10 32",
	"functional 2 10 32",
	"enables 2 10 32",
	"compact 2 10 32",
	"full 2 10 32",
	"forms 2 10 32",
	"favor 2 10 32",
	"description 2 18 34",
	"crucial 2 18 40",
	"offers 2 18 33 7 18 17",
	"wide 2 10 32",
	"belonging 2 10 32",
	"compound 2 10 32",
	"families 2 10 32",
	"carbohydrates 2 10 32",
	"amino 2 10 32",
	"acids 2 10 32",
	"etc 2 10 32",
	"under 2 18 36",
	"tick 2 10 32",
	"puts 2 10 32",
	"ungrouped 2 18 32",
	"denoted 2 18 40",
	"special 2 10 32",
	"feedback 2 33 32 5 10 8 7 10 64",
	"hover-over 2 10 32",
	"still 2 10 32",
	"respective 2 10 32 5 10 32 7 10 4",
	"circled 2 10 32",
	"means 2 18 34",
	"firstly 2 10 32",
	"manipulated 2 10 16",
	"handled 2 10 16",
	"cleaned 2 10 16",
	"sense 2 10 16",
	"i.e 2 10 16 7 18 8",
	"leads 2 10 16",
	"removal 2 10 16",
	"cleaning 2 40 16 4 10 1",
	"performed 2 10 16",
	"clean 2 18 16 7 33 48",
	"calculates 2 10 16",
	"coordinates 2 10 16 7 10 16",
	"get 2 10 16",
	"evenly 2 10 16",
	"organized 2 10 16",
	"fashion 2 10 16",
	"partial 2 10 16",
	"certain 2 10 16",
	"multi-molecular 2 10 16",
	"affects 2 10 16",
	"server 2 10 16 7 10 32",
	"connection 2 10 16 7 10 32",
	"otherwise 2 10 16",
	"validation 2 10 16 4 10 1",
	"highlights 2 18 16",
	"does 2 10 16 7 10 32",
	"correct 2 10 16",
	"make 2 10 16 5 10 64",
	"corrections 2 10 16",
	"manually 2 10 16",
	"recognizing 2 10 16",
	"error 2 10 16",
	"particular 2 10 16",
	"stereoisomerism 2 10 16 4 10 1",
	"optical 2 40 26 4 10 1",
	"isomerism 2 33 24 4 33 1",
	"geometric 2 33 24 4 10 1",
	"isomers 2 40 24",
	"determined 2 18 16",
	"calculate 2 10 16 7 10 16",
	"via 2 10 16",
	"differ 2 33 25",
	"configuration 2 94 30",
	"four 2 33 26",
	"substituents 2 60 9",
	"calculated 2 18 8",
	"cahn-ingold-prelog 2 10 8",
	"priority 2 18 8",
	"cip 2 18 8",
	"enantiomers 2 33 14",
	"images 2 10 8",
	"each 2 40 10",
	"superposable 2 10 8",
	"centers 2 54 14",
	"reason 2 10 8",
	"behind 2 10 8",
	"alkenes 2 10 8",
	"hindered 2 10 8",
	"relative 2 33 14",
	"bonded 2 10 8",
	"distinguish 2 10 8",
	"non-identical 2 10 8",
	"determining 2 10 8",
	"higher 2 18 8",
	"ranked 2 18 8",
	"isomer 2 18 10",
	"opposite 2 18 10",
	"sides 2 10 8",
	"generalized 2 10 8",
	"representing 2 10 8",
	"similar 2 10 8",
	"some 2 18 10 5 10 4",
	"structural 2 10 8",
	"possess 2 10 8",
	"skeleton 2 10 8",
	"halogen 2 18 8 7 18 1",
	"substituent 2 10 8",
	"know 2 33 14",
	"exactly 2 10 8",
	"jchem 2 18 10",
	"guide 2 18 10 4 10 32 7 10 16",
	"designed 2 10 8",
	"replacing 2 10 4",
	"information 2 40 6 4 10 1 7 10 16",
	"indicates 2 10 4",
	"say 2 10 4",
	"represents 2 26 5 5 10 2",
	"well-defined 2 10 4 5 10 2",
	"stereoisomer 2 10 4 5 10 2",
	"meanings 2 10 4",
	"mol 2 10 4",
	"sdf 2 10 4",
	"describe 2 18 4",
	"racemic 2 10 4",
	"mixture 2 18 6",
	"daylight 2 10 4",
	"smarts 2 10 4",
	"enantiomer 2 18 6",
	"representation 2 26 6",
	"enable 2 10 4",
	"refer 2 10 4",
	"stereoisomers 2 26 6",
	"makes 2 10 4",
	"stereochemical 2 10 4",
	"advantage 2 10 4",
	"palpable 2 10 2",
	"illustrating 2 10 2",
	"few 2 10 2",
	"really 2 10 2",
	"demanding 2 10 2",
	"help 2 18 2",
	"enough 2 10 2",
	"diastereomer 2 10 2",
	"applies 2 10 2 5 10 16",
	"identifiers 2 18 2",
	"stereocenters 2 10 2",
	"consists 2 10 2",
	"stereocenter 2 10 2",
	"belongs 2 10 2",
	"abs 2 10 2",
	"another 2 10 2",
	"refers 2 10 2",
	"drawn 2 10 2",
	"notation 2 10 2",
	"contain 2 10 2",
	"specified 2 10 2",
	"diastereomers 2 10 2",
	"examples 2 10 2",
	"lists 2 33 3 4 18 1 7 18 1",
	"covers 2 10 2",
	"produces 2 10 2",
	"hit 2 10 2",
	"found 2 10 2",
	"target 2 10 2",
	"similarly 2 10 2",
	"prepares 2 10 1",
	"ready 2 10 1",
	"r-groups 2 18 1 4 10 1 7 10 2",
	"substitution 2 10 1",
	"variation 2 10 1",
	"scaffold 2 10 1",
	"involve 2 10 1",
	"derivatives 2 10 1",
	"numbers 2 10 1",
	"typed 2 10 1",
	"directly 2 10 1 7 10 2",
	"give 2 10 1",
	"chemical_features_in_marvin_fo.htm 2 6 64",
	"layout 3 16 144 4 10 32",
	"main 3 10 8 7 10 8",
	"graphical 3 10 4",
	"user 3 10 4",
	"interface 3 10 4",
	"gui 3 10 4",
	"insert 3 10 2 7 26 26",
	"onto 3 10 2 7 10 16",
	"toolbars 3 10 1 4 10 16 7 24 192",
	"menus 3 10 1 4 10 16 5 46 224",
	"layout_of_marvinsketch.htm 3 6 64",
	"user&#39;s 4 10 32 7 10 16",
	"marvin_for_javascript.htm 4 6 64",
	"efficient 5 10 64",
	"divided 5 10 32",
	"five 5 10 32",
	"classes 5 18 48",
	"edit 5 40 36",
	"need 5 10 32",
	"among 5 10 32 7 10 4",
	"upon 5 18 18",
	"many 5 10 16",
	"combination 5 10 16",
	"right-clicking 5 10 16",
	"deletes 5 18 18 6 10 16",
	"items 5 10 8",
	"displays 5 18 8 7 10 16",
	"contract 5 26 12",
	"never 5 10 4",
	"spot 5 10 4",
	"copies 5 10 4",
	"inserts 5 10 4",
	"determines 5 10 2",
	"rate 5 10 2",
	"entire 5 10 2",
	"pop_up_menus.htm 5 6 64",
	"boxes 6 10 32",
	"clears 6 10 8",
	"performs 6 18 4",
	"undo 6 10 4 7 26 32",
	"redo 6 10 4 7 18 32",
	"places 6 26 1",
	"shortcuts.htm 6 6 64",
	"provide 7 18 66",
	"accessing 7 10 64",
	"majority 7 10 64",
	"tooltip 7 10 64",
	"describing 7 10 64",
	"functionality 7 10 64",
	"indicate 7 10 64",
	"momentarily 7 10 64",
	"grayed 7 10 64",
	"unless 7 10 32",
	"management 7 10 32",
	"viewing 7 10 32",
	"load 7 10 32",
	"revert 7 18 32",
	"commands 7 10 32",
	"effect 7 10 32",
	"command 7 10 32",
	"removing 7 10 32",
	"leaving 7 10 16",
	"recalculating 7 10 16",
	"version 7 10 16",
	"triangle 7 10 8",
	"retains 7 10 8",
	"complete 7 26 13",
	"unit 7 18 8",
	"adds 7 18 8",
	"positive 7 10 8",
	"negative 7 10 8",
	"below 7 18 5",
	"aromatic 7 26 6",
	"cis 7 10 4",
	"trans 7 10 4",
	"unspecified 7 33 7",
	"reached 7 18 3",
	"star 7 18 3",
	"polymer 7 18 3",
	"stands 7 10 1",
	"generally 7 10 1",
	"symbolize 7 10 1",
	"unknown 7 10 1",
	"technically 7 10 1",
	"&#39;ah 7 10 1",
	"left-clicking 7 10 1",
	"except 7 26 1",
	"metal 7 18 1",
	"six 7 10 1",
	"mouse-click 7 10 1",
	"house 7 10 1",
	"toolbars.htm 7 6 64"];
skipwords = ["and,or,the,it,is,an,on,we,us,to,of,"];
var STR_FORM_SEARCHFOR = "Search for:";
var STR_FORM_SUBMIT_BUTTON = "Submit";
var STR_FORM_RESULTS_PER_PAGE = "Results per page:";
var STR_FORM_CATEGORY = "Category:";
var STR_FORM_CATEGORY_ALL = "All";
var STR_FORM_MATCH = "Match:";
var STR_FORM_ANY_SEARCH_WORDS = "any search words";
var STR_FORM_ALL_SEARCH_WORDS = "all search words";
var STR_NO_QUERY = "No search query entered.";
var STR_RESULTS_FOR = "Search results for:";
var STR_RESULTS_IN_ALL_CATEGORIES = "in all categories";
var STR_RESULTS_IN_CATEGORY = "in category";
var STR_POWEREDBY = "Search powered by";
var STR_NO_RESULTS = "No results";
var STR_RESULT = "result";
var STR_RESULTS = "results";
var STR_PHRASE_CONTAINS_COMMON_WORDS = "Your search query contained too many common words to return the entire set of results available. Please try again with a more specific query for better results.";
var STR_SKIPPED_FOLLOWING_WORDS = "The following word(s) are in the skip word list and have been omitted from your search:";
var STR_SKIPPED_PHRASE = "Note that you can not search for exact phrases beginning with a skipped word";
var STR_SUMMARY_NO_RESULTS_FOUND = "No results found.";
var STR_SUMMARY_FOUND_CONTAINING_ALL_TERMS = "found containing all search terms.";
var STR_SUMMARY_FOUND_CONTAINING_SOME_TERMS = "found containing some search terms.";
var STR_SUMMARY_FOUND = "found.";
var STR_PAGES_OF_RESULTS = "pages of results.";
var STR_MORETHAN = "More than";
var STR_POSSIBLY_GET_MORE_RESULTS = "You can possibly get more results searching for";
var STR_ANY_OF_TERMS = "any of the terms";
var STR_ALL_CATS = "all categories";
var STR_CAT_SUMMARY = "Refine your search by category:";
var STR_DIDYOUMEAN = "Did you mean:";
var STR_OR = "or";
var STR_RECOMMENDED = "Recommended links";
var STR_SORTEDBY_RELEVANCE = "Sorted by relevance";
var STR_SORTBY_RELEVANCE = "Sort by relevance";
var STR_SORTBY_DATE = "Sort by date";
var STR_SORTEDBY_DATE = "Sorted by date";
var STR_RESULT_TERMS_MATCHED = "Terms matched: ";
var STR_RESULT_SCORE = "Score: ";
var STR_RESULT_URL = "URL:";
var STR_RESULT_PAGES = "Result Pages:";
var STR_RESULT_PAGES_PREVIOUS = "Previous";
var STR_RESULT_PAGES_NEXT = "Next";
var STR_SEARCH_TOOK = "Search took";
var STR_SECONDS = "seconds";
var STR_MAX_RESULTS = "You have requested more results than served per query. Please try again with a more precise query.";
