﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<!-- saved from url=(0029)http://www.helpandmanual.com/ -->
<head>
   <title>Canvas</title>
   <meta name="generator" content="Help &amp; Manual" />
   <meta name="keywords" content="" />
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />
   <link type="text/css" href="default.css" rel="stylesheet" />
   <style type="text/css">
     body { margin: 0px; background: #FFFFFF; }
   </style>
<script type="text/javascript" src="helpman_topicinit.js"></script>
<!-- Redirect browser to frame page if page is not in the content frame. -->
<script type="text/javascript">
<!--
if (location.search.lastIndexOf("toc=0")<=0) {
  if (parent.frames.length==0) { parent.location.href="index.html?canvas.htm"; }
  else { parent.quicksync('a1.1.1'); }
}
//-->
</script>
<script type="text/javascript" src="highlight.js"></script></head>
<body onload="highlight();">
<div id="hmpopupDiv" style="visibility:hidden; position:absolute; z-index:1000; "></div>


<table style="width:100%; border:none; border-spacing:0px; padding:5px; background:#E4F1F1">
  <tr style="vertical-align:middle">
    <td style="text-align:middle">
      <p style="text-align: center;"><span class="f_Heading1" style="font-family: 'Times New Roman'; color: #077179;">Canvas</span></p>

    </td>
  </tr>
</table>


<!-- Placeholder for topic body. -->
<table style="width:100%;border:none;border-spacing:0px"><tr style="vertical-align:top"><td style="text-align:left;padding:5px">
<p style="line-height: 1.50; margin: 14px 0px 0px 0px;"><a name="navigatingonthecanvas"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Navigating on the canvas and modifying the zoom ratio</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">You can navigate on the canvas using either the scrollbars or the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="toolbars.htm#navigate" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Navigate</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Tools</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar. The former ones become visible only when the contents of the canvas exceed canvas size. In "Navigate" mode the canvas can be moved by dragging: if you click on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Navigate</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button the "move" cursor appears and the canvas can be repositioned. The middle mouse button can also be used to navigate on the canvas regardless of the currently active tool.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">The contents of the canvas can be reduced or magnified at will with the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="toolbars.htm#zoomout" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Zoom In/</a>Zoom Out</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> buttons on the</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"> General </span><span style="font-size: 12pt; font-family: 'Times New Roman';">toolbar</span><span style="font-size: 12pt; font-family: 'Times New Roman';">. The zoom ratio can be changed using the mouse wheel, too. (Zooming with the mouse wheel is centered at the cursor position. With this function, you can virtually "move" the canvas.) The </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="toolbars.htm#zoomall" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Zoom All</a></span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"> </span><span style="font-size: 12pt; font-family: 'Times New Roman';">button on the</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"> General</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar sets the optimal magnification to view everything on the canvas. The "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Enter</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" shortcut can also be used for this purpose. The</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"> </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #007171; text-decoration: underline;"><a href="toolbars.htm#zoomtoselection" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #007171; background-color: transparent; text-decoration: underline;">Zoom to Selection</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button on the same toolbar (or the </span><span style="font-size: 12pt; font-family: 'Times New Roman';">"</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Ctrl + Enter</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" keyboard shortcut) increases the zoom ratio placing the selected object in the center. This button is unavailable until at least one atom is selected.</span></td></tr></table></div>
</td></tr></table>

</body>
</html>
