﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<!-- saved from url=(0029)http://www.helpandmanual.com/ -->
<head>
   <title>Basic Features of Marvin for JavaScript</title>
   <meta name="generator" content="Help &amp; Manual" />
   <meta name="keywords" content="" />
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />
   <link type="text/css" href="default.css" rel="stylesheet" />
   <style type="text/css">
     body { margin: 0px; background: #FFFFFF; }
   </style>
<script type="text/javascript" src="helpman_topicinit.js"></script>
<!-- Redirect browser to frame page if page is not in the content frame. -->
<script type="text/javascript">
<!--
if (location.search.lastIndexOf("toc=0")<=0) {
  if (parent.frames.length==0) { parent.location.href="index.html?basic_features_of_marvin_for_j.htm"; }
  else { parent.quicksync('a1.2'); }
}
//-->
</script>
<script type="text/javascript" src="highlight.js"></script></head>
<body onload="highlight();">
<div id="hmpopupDiv" style="visibility:hidden; position:absolute; z-index:1000; "></div>


<table style="width:100%; border:none; border-spacing:0px; padding:5px; background:#E4F1F1">
  <tr style="vertical-align:middle">
    <td style="text-align:middle">
      <p class="p_Heading1" style="text-align: center;"><span class="f_Heading1" style="font-size: 18pt; font-family: 'Times New Roman'; color: #077179;">Basic Features of Marvin for JavaScript</span></p>

    </td>
  </tr>
</table>


<!-- Placeholder for topic body. -->
<table style="width:100%;border:none;border-spacing:0px"><tr style="vertical-align:top"><td style="text-align:left;padding:5px">
<p style="text-align: justify; line-height: 1.50; margin: 19px 0px 0px 0px;"><a name="importingandsavingstructures"></a><span class="f_Heading1" style="font-family: 'Times New Roman'; color: #077179;">Importing and Saving Structures</span></p>
<p style="line-height: 1.50;"><a name="importingastructure"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Importing a Structure</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Marvin for JavaScript handles MDL Molfiles V2000 (.mol extension) and ChemAxon Marvin Documents (.mrv extension) as input files. However, other file types (SMILES, ChemAxon Extended SMILES, InChi, Name, CML, MDL Molfile V3000, MDL SDfile, ChemAxon Compressed Molfile, ChemAxon Compressed SDfile, and XYZ) can also be imported if the appropriate webservice is available.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Press the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="toolbars.htm#import" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Import</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">General </span><span style="font-size: 12pt; font-family: 'Times New Roman';">toolbar to open the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Import</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> pop-up window. The dialog can be opened by pressing the "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Ctrl+O</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" keyboard shortcut, too. Open the structure file in *.mol or *.mrv format with a text editor (</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic;">e.g.</span><span style="font-size: 12pt; font-family: 'Times New Roman';">, Windows's Notepad), select all, and copy the file contents to the clipboard then paste it into the input area of the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Import</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> window. At the top of the window you can select the appropriate file format from a drop-down list. Finally, pressing the "Import" button loads the structure on the canvas.</span></p>
<p style="line-height: 1.50; margin: 0px 0px 10px 0px;"><img src="import.png" width="424" height="412" border="0" alt="import"></p>
<p style="line-height: 1.50; margin: 14px 0px 0px 0px;"><a name="savingastructure"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Saving a Structure</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">A structure can be saved either in ChemAxon Marvin Document format or in MDL Molfile V2000 format. In order to export in other file formats (SMILES, ChemAxon Extended SMILES, InChi, InChiKey, Name, CML, MDL Molfile V3000, MDL SDfile, ChemAxon Compressed Molfile, ChemAxon Compressed SDfile, XYZ, CDX, and SKC) the appropriate webservice has to be available.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">To save the structures on the canvas, click on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="toolbars.htm#export" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Export</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">General </span><span style="font-size: 12pt; font-family: 'Times New Roman';">toolbar which opens the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Save </span><span style="font-size: 12pt; font-family: 'Times New Roman';">dialog</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> window. Alternatively, you can use the "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Ctrl+S</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" keyboard shortcut as well. In this window you can see the canvas contents in the default ChemAxon Marvin Document format. The structure file format can be changed by selecting an option from the drop-down list of supported file formats. To save that to a file, push the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Download</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button at the bottom of the dialog window, click on the created link, and save the file with the preferred name and route. Alternatively, you can paste the structure source into a text editor and save it from there. In case of CDX and SKC file formats, only the first option can be carried out.</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 0px 0px 10px 0px;"><img src="export.png" width="438" height="451" border="0" alt="export"></p>
<p style="text-align: justify; line-height: 1.50; margin: 14px 0px 0px 0px;"><a name="savingasimage"></a><span class="f_ImageCaption" style="font-size: 14pt; font-family: 'Times New Roman'; color: #007171;">Saving as Image</span></p>
<p style="text-align: justify; line-height: 1.50;"><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">In order to save an image of the structure in Marvin for JavaScript, select "Image" in the </span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman';">Save</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;"> dialog. The supported image formats are PNG and JPEG. You can also set </span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal; color: #007171; text-decoration: underline;"><a href="basic_features_of_marvin_for_j.htm#structuredisplayoptions" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #007171; background-color: transparent; text-decoration: underline;">display options detailed elsewhere</a></span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">, determine the color of the image background (transparent or white), and set the width and height of the image.</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 14px 0px 0px 0px;"><a name="selectanddelete"></a><span class="f_Heading1" style="font-family: 'Times New Roman'; color: #077179;">Select and Delete</span></p>
<p style="line-height: 1.50;"><a name="selection"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Selection</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Clicking on the lower right corner of the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="toolbars.htm#selection" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Selection</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button of the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Tools</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar you can choose between two options:</span></p>
<div style="text-align: justify; text-indent: 0px; line-height: 1.50; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table cellspacing="0" cellpadding="0" border="0" style="border: none; border-spacing:0px;">
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="192" style="width:192px;"><p style="text-align: justify;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Rectangle Selection:</span></p>
</td>
<td valign="middle" width="50" style="width:50px;"><p style="text-align: justify;"><img src="rectangle%20selection.png" width="34" height="34" border="0" alt="Rectangle selection"></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="192" style="width:192px;"><p style="text-align: justify;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Freehand Selection:</span></p>
</td>
<td valign="middle" width="50" style="width:50px;"><p style="text-align: justify;"><img src="freehand%20selection.png" width="34" height="34" border="0" alt="Freehand selection"></p>
</td>
</tr>
</table>
</div>
<p style="text-align: justify; line-height: 1.50; margin: 10px 0px 0px 0px;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Using either selection mode, an atom or a bond can be selected by clicking on it. Please note that in this case the bond gets selected along with the connected atoms. If you want to select a bond without its atoms, you have to choose Freehand Selection, and select the middle of the bond. Alternatively, press the "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Ctrl</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" button and click on the bond, while either selection tool is active.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">With Rectangle Selection, you can select an area in rectangle mode while with Freehand Selection the area swept by the red line will be selected.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The background of atoms and bonds will become green when they are selected. In either selection mode, hovering over an atom or a bond, its background becomes green. When you click on this background, the green highlight gets lighter indicating that this part got selected. &nbsp; &nbsp; &nbsp; &nbsp;</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Whole molecules can be selected by double-clicking on them. The result is the same as it would be using Rectangle Selection. Whole structures, fragments of molecules, or even several molecules can be selected at the same time.</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Separate parts of a molecule or even different molecules can be selected at the same time if the "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Shift</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" button is pressed during selection.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">The "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Ctrl+A</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" keyboard shortcut can also be used to select the whole content of the canvas.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 14px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">To clear the selection, you can press the "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Esc</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" button, or simply click on a blank area on the canvas. If there is not any selection on the canvas, pressing the "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Esc</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" button activates the Rectangle Selection tool.</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50;"><a name="delete"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Delete</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Once you have made a selection (atom, bond, fragment, or a whole molecule), you can delete it either by clicking on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="toolbars.htm#delete" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Delete</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Tools</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar or pressing the "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Del</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" button on the keyboard.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Selecting the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Delete</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> tool and clicking on an atom or a bond erases said atom or bond. In case of deleting a bond, its atoms will not disappear from the canvas. On the other hand, if you erase an atom, its bonds are also deleted.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Delete</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button also works as a rectangle selection tool: click on the button and select the parts you want to erase (the selected parts are highlighted in red). When you release the left mouse button, the highlighted parts will be erased.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Erasing objects is also available from the contextual menu after a right-click on the atom, bond or the selected item you want to delete.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">You can clear the whole canvas with one click using the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="toolbars.htm#clear" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Clear</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">General</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar or pressing the "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Ctrl+Del</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" keyboard shortcut.</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 19px 0px 0px 0px;"><a name="cutcopyandpasteoptions"></a><span class="f_Heading1" style="font-family: 'Times New Roman'; color: #077179;">Cut, Copy, and Paste Options</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">To copy a molecule or a fragment you have to select it first. The </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="toolbars.htm#cut" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Cut</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> and </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="toolbars.htm#copy" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Copy</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> buttons on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">General</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar or in the contextual menu place the content of the selected area on a virtual clipboard. Please note that the copy-paste function is available only within the application. The content of the clipboard can be imported to the canvas with the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="toolbars.htm#paste" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Paste</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">General</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar. Their conventional shortcuts, "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Ctrl+X</span><span style="font-size: 12pt; font-family: 'Times New Roman';">", "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Ctrl+C</span><span style="font-size: 12pt; font-family: 'Times New Roman';">", and "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Ctrl+V</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" can be used, too.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Pressing the "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Ctrl</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" button and hovering the cursor over a selected area, you can move a copy of the selected structure by dragging it. When you do this, a "+" sign appears beside the cursor. A copy of the selected structure is placed on the canvas when you release the left button. Note that the new object remains selected when it has been placed on the canvas, so it can be multiplied several times.</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 19px 0px 0px 0px;"><a name="drawingchemicalstructures"></a><span class="f_Heading1" style="font-family: 'Times New Roman'; color: #077179;">Drawing Chemical Structures</span></p>
<p style="line-height: 1.50;"><a name="drawingatoms"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Drawing atoms</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">You can choose from several options to draw a new atom or to modify an already existing one:</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">An atom can be inserted by using one of the most frequently used atom </span><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #077179; text-decoration: underline;"><a href="toolbars.htm#themostfrequentlyusedatoms" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">buttons</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"><a href="toolbars.htm#atomstoolbar" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #000000; background-color: transparent; text-decoration: none;">Atoms</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar. The chosen atom appears on the tip of the cursor and you can place it on the canvas with a left-click.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Any of the known elements can be selected from the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #077179; text-decoration: underline;"><a href="toolbars.htm#periodictable" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">Periodic Table</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';">, the button of which you can find on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Atoms </span><span style="font-size: 12pt; font-family: 'Times New Roman';">toolbar. Choosing an element, its atomic symbol will appear on the tip of the cursor and you can place it on the canvas several times. At the same time, the Periodic Table window closes automatically.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Atoms can be placed on the canvas using the keyboard. By typing an atomic symbol, it will appear on the tip of the cursor, and can be placed on the canvas with a left-click or pressing "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Enter</span><span style="font-size: 12pt; font-family: 'Times New Roman';">". Although they are not listed in the Periodic Table, deuterium and tritium symbols (D and T, respectively) can also be used this way.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">When a non-metallic atom is placed on the canvas, it will be automatically completed with implicit hydrogens according to the free valences of the atom. Display of implicit hydrogens depends on the current option in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #077179; text-decoration: underline;"><a href="basic_features_of_marvin_for_j.htm#structuredisplayoptions" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">View Settings</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> dialog box. Metallic elements are exceptions to this rule, they are added to canvas in zero oxidation state.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Atoms on the canvas can be replaced using either of the above mentioned methods. If you select several atoms at the same time, you can change them simultaneously using either the keyboard or the Periodic System.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Instead of standard atomic symbols, you can use any label for atoms by defining an </span><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #077179; text-decoration: underline;"><a href="basic_features_of_marvin_for_j.htm#alias" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">atom alias</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Atom Properties</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> dialog.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 14px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #007171; text-decoration: underline;"><a href="toolbars.htm#queryatoms" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #007171; background-color: transparent; text-decoration: underline;">Query atoms</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> can be part of molecules (query structures) as well. To use these atoms, select their combo button on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Atoms </span><span style="font-size: 12pt; font-family: 'Times New Roman';">toolbar.</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"> </span><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #008080; text-decoration: underline;"><a href="chemical_features_in_marvin_fo.htm#pseudoatoms" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #008080; background-color: transparent; text-decoration: underline;">Pseudo atoms</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';">, which stand for an "Any" type query atom, can also be created through this button: click on the "question mark" in the query atom list and define the label you want to use with your pseudo atom in the dialog then close the dialog with the "OK" button and click on the atom you want to change into a pseudo atom. Note that the Atom properties dialog is not available for pseudo atoms, and atom properties cannot be added to these atoms.</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50;"><a name="drawingbonds"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Drawing bonds</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">To create a chemical bond between two atoms on the canvas </span><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #077179; text-decoration: underline;"><a href="toolbars.htm#bonds2" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">select the appropriate bond type</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"><a href="toolbars.htm#bonds" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #000000; background-color: transparent; text-decoration: none;"> using the </a></span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="toolbars.htm#bonds" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Bonds</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"><a href="toolbars.htm#bonds" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #000000; background-color: transparent; text-decoration: none;"> combo button</a> on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Tools</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar, click on one of the two atoms, and drag the bond to the other atom.</span></td></tr></table></div><div style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Selecting a bond type and clicking or dragging the cursor on the canvas will create a new bond with the default length and with carbon atoms on both ends.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">When a bond tool is active and the cursor is over an atom, a grey colored image of a bond in the selected type will appear, ended by a carbon atom. Pressing the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">"Shift"</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> key will change the bond direction. Click on the atom will place the bond in this position. You can also draw a new bond by dragging from an already existing atom. Just like in the previous case, the length of the bond is the default value, and a carbon atom is on its other end.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Bond type can be changed by selecting the new bond type from the toolbar and clicking on the bond on the canvas. The bond type can be selected by typing the appropriate </span><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #077179; text-decoration: underline;"><a href="toolbars.htm#bonds" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">shortcut</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';">, too. If the cursor is over the canvas, typing a shortcut will select the appropriate bond mode (the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Bonds</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button is activated). However, if the cursor is over an existing bond, typing a shortcut will modify the type of the given bond only. In this case the selected mode (the active button) will not change.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Note that repeated clicking on a bond will change its type depending on the currently active bond type. If the selected bond type is Single, successive clicks on a bond will change its type according to the Single-Double-Triple-Single-... order. If Double bond type is selected, successive clicks on a bond will alternate its type between Double and Triple. Clicking on a directed bond (Single Up, Single Down, Single Up or Down, or Coordinate bond) will change the bond direction.</span></td></tr></table></div><div style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Drawing molecules can be made much faster by using the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #007171; text-decoration: underline;"><a href="toolbars.htm#drawing" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #007171; background-color: transparent; text-decoration: underline;">Drawing</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> tool of the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Bonds</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> combo button. With this feature, you can create the framework of complex structures without releasing the mouse button. When you draw a bond, grey dots appear indicating potential directions for the next bond. In the default case, these dots are the vertices of a hexagon while they will be rearranged into a pentagon if you press down the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">"Shift"</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> key. Dragging the cursor towards one of these dots, you can add a new bond to your molecule. While drawing a molecule, you can change the type of the last atom by typing a new atomic symbol. Clicking on a bond when the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Drawing</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> tool is active will change the bond type according to the Single-Double-Triple sequence.</span></td></tr></table></div><div style="text-align: left; text-indent: 0px; line-height: 1.50; padding: 0px 0px 0px 0px; margin: 0px 0px 10px 0px;"><table cellspacing="0" cellpadding="0" border="0" style="border: none; border-spacing:0px;">
<tr style="text-align:left;vertical-align:top;">
<td valign="top" width="300" style="width:300px;"><p style="text-align: center;"><img src="drawing1.png" width="170" height="178" border="0" alt="drawing1" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="top" width="300" style="width:300px;"><p style="text-align: center;"><img src="drawing2.png" width="170" height="178" border="0" alt="drawing2" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="top" width="300" style="width:300px;"><p style="text-align: center;"><img src="drawing3.png" width="221" height="178" border="0" alt="drawing3" style="margin:0 auto;margin:0px;"></p>
</td>
</tr>
</table>
</div>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 14px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">When more than one bonds are selected in a structure, selecting a new bond type will change every selected bond.</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50;"><a name="drawingamolecule"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Drawing a molecule</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Molecules can be created by connecting atoms with different chemical bonds or with the use of templates from the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="toolbars.htm#templatestoolbar" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Templates</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">You can create alkyl or conjugated hydrocarbon chains of arbitrary length using the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="toolbars.htm#chain" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Chain</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Tools </span><span style="font-size: 12pt; font-family: 'Times New Roman';">toolbar. A chain can be created as a new molecule or it can start from an already existing atom on the canvas. After selecting the button you can draw the alkyl chain by dragging or clicking repeatedly on the starting atom. The length of the chain is shown on the tip of the cursor. During drawing, the new chain can be rotated around its starting atom to the desired orientation by dragging. You can create a conjugated polyene chain by pressing the "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Ctrl</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" button when chain-drawing.</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 14px 0px 0px 0px;"><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Marvin for JavaScript supports different </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #007171; text-decoration: underline;"><a href="chemical_features_in_marvin_fo.htm#substructuregroupss-groups" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #007171; background-color: transparent; text-decoration: underline;">substructure group (S-group)</a></span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"> types:</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Generic S-groups</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> can be used in chemical structures, </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic;">e.g.,</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> for visualizing solvent molecules or other compounds in a complex structure.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 10px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Predefined </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">abbreviated groups</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> can also assist you to create complex molecular structures. For selecting an abbreviated group, use the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="toolbars.htm#abbreviatedgroups" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Abbreviated Groups</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button of the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Tools</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar. </span><span style="font-size: 12pt; font-family: 'Times New Roman';">When a group abbreviation has been selected from the list of the appearing dialog box, you can decide whether to place it on the canvas in contracted or in expanded form. For the latter option set the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Expand</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> checkbox in the dialog. Closing the dialog with the "OK" button will place the group on the tip of the cursor, from where you can put it on the canvas with a left-click (connect it to an already existing structure or start a new molecule). </span><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #077179; text-decoration: underline;"><a href="chemical_features_in_marvin_fo.htm#abbreviatedgroups" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">Learn more about abbreviated groups and their handling.</a></span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 13pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #077179;">&#8226;</span></td><td><a name="mergingstructures"></a><span style="font-size: 13pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Merging structures</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">In Marvin for JavaScript you cal also merge separate fragments. Merging is helpful if you want to create ring systems (spiro, fused, or bridged ring systems: one, two, or more than two atoms in common, respectively) or to connect chain-like substructures. For merging two structures, one of them should be selected. When you hover the cursor over the selected fragment, the cursor will change to "move" type (crossed arrows) indicating that the fragment can be moved. Dragging the selected fragment until one or more of its atoms overlap with the "host" molecule atoms will merge the two structures.</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 0px 0px 10px 0px;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Templates can also be merged with a molecule on the canvas. Choose a template from the toolbar (it appears on the tip of the cursor) and move it close to the atoms of the existing structure. When the distance between them has become sufficiently small, the &nbsp;possible merging points will be indicated by blue circles. Left-click will put the template in the merged position.</span></p>
<p style="line-height: 1.50; margin: 0px 0px 10px 0px;"><img src="merge2.png" width="165" height="126" border="0" alt="merge2"> &nbsp; &nbsp; &nbsp; &nbsp;<img src="merge1.png" width="162" height="107" border="0" alt="merge1"></p>
<p style="line-height: 1.50; margin: 0px 0px 10px 0px;"><span style="width:24px">&nbsp;</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman';">Spiro ring system &nbsp; &nbsp; &nbsp; &nbsp;Fused ring system</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 13pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #077179;">&#8226;</span></td><td><a name="sproutdrawing"></a><span class="f_ImageCaption" style="font-size: 13pt; font-family: 'Times New Roman'; color: #077179;">Sprout drawing</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50;"><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">Single atoms can be connected to non-terminal atoms in a "sprouted" position. Select an atom symbol, hover the cursor over the atom to which you want to attach the new atom, and press the </span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman';">"Shift"</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;"> key to see the result of sprouting. Click on the non-terminal atom to attach the new atom to it.</span></p>
<p style="text-align: justify; line-height: 1.50;"><img src="sprout5.png" width="285" height="88" border="0" alt="sprout5"></p>
<p style="text-align: justify; line-height: 1.50;"><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">Symmetric templates (cyclohexane, cyclopentane, benzene, pyrrole) can be connected to an atom with a single bond: select the template from the </span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman';">Templates</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;"> toolbar and hover the cursor over the desired atom to see a grey colored image of the template showing its would-be position. Clicking on the atom will connect the template to the atom with a single bond: one of the implicit hydrogens of the atom will be replaced by the template. Please note that non-symmetric templates (including naphthalene) cannot be sprouted.</span></p>
<p style="line-height: 1.50; margin: 0px 0px 10px 0px;"><img src="sprout_1.png" width="214" height="138" border="0" alt="sprout_1"></p>
<p style="text-align: justify; line-height: 1.50;"><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">When sprouting, the template and the connecting single bond can be rotated around the connecting atom of the original structure by dragging.</span></p>
<p style="line-height: 1.50; margin: 0px 0px 10px 0px;"><img src="sprout2.png" width="226" height="141" border="0" alt="sprout2"></p>
<p style="text-align: justify; line-height: 1.50;"><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">If you want to avoid sprouting, hold down the "</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman';">Shift</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">" key while clicking on the selected atom. In this case the attaching atom of the template will not replace the connecting atom of the original structure.</span></p>
<p style="line-height: 1.50; margin: 0px 0px 10px 0px;"><img src="sprout4.png" width="222" height="105" border="0" alt="sprout4"></p>
<p style="text-align: justify; line-height: 1.50;"><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">A template can be attached to an already existing bond which is about the same length as the template's bond: clicking on the bond connects the template to the selected bond. The template is rotated automatically in order to fit the chosen bond perfectly.</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Courier New'; color: #000000;">o</span></td><td><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">Pressing the "</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman';">Shift</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">" button and holding the left mouse key while sprout drawing mirrors the template to the other side of the bond. The side of the bond &nbsp;to place a template can also be altered by dragging the template to the appropriate side.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Courier New'; color: #000000;">o</span></td><td><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">When the pyrrole template is selected, and you hover over a bond, pressing the </span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman';">"Space"</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;"> key will rotate the pyrrole, so you can alter the orientation of the template easily.</span></td></tr></table></div><p style="line-height: 1.50; margin: 0px 0px 14px 0px;"><img src="sprout3.png" width="505" height="111" border="0" alt="sprout3"></p>
<p style="text-align: justify; line-height: 1.50; margin: 19px 0px 0px 0px;"><a name="transformations"></a><span class="f_Heading1" style="font-family: 'Times New Roman'; color: #077179;">Transformations</span></p>
<p style="text-align: justify; line-height: 1.50;"><a name="draggingtheselectedstructure"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Dragging the selected structure</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">After selecting them, whole molecules or fragments (parts of molecules) can be moved on the canvas. When you hover the cursor over the selected structure,</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> the cursor "move" cursor will appear indicating that the highlighted part can be dragged on the canvas. If you select a whole molecule, it will be translated without any modifications, but when the selection contains only a fragment, the molecule will be distorted due to the changes in bond lengths and bond angles. Note that the positions of the atoms within the selected fragment are fixed, so only the bonds between the selected and non-selected part of the molecule will be distorted.</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 14px 0px 0px 0px;"><a name="rotationin2d"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Rotation in 2D</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">When a structure was been selected, it can be rotated around its center in two dimensions.</span></p>
<p style="line-height: 1.50;"><img src="rotate2d_1b.png" width="148" height="148" border="0" alt="rotate2D_1b"></p>
<p style="text-align: justify; line-height: 1.50;"><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">The pink circle with a dot is the center of the selected area (pivot point). The molecule can be rotated around this point by clicking on the green dot and dragging the cursor on the canvas. During dragging, you can see the angle of rotation near this green dot. By default, rotation is done in 6° increments. For continuous rotation hold the "</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman';">Shift</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">" button while rotating.</span></p>
<p style="line-height: 1.50;"><img src="rotate2d_2b.png" width="159" height="120" border="0" alt="rotate2D_2b"></p>
<p style="text-align: justify; line-height: 1.50;"><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">When a whole molecule is selected, the original center of rotation can be relocated by dragging. In this case, if the new rotation center is too close to its original position, it will be snapped back to it. However, pressing the </span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman';">"Shift"</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;"> key while dragging the center of rotation allows it to be as close to its original place as you wish. Moving the center lets you rotate the molecule around any point of the canvas. The image of the starting structure remains displayed in grey during rotation.</span></p>
<p style="line-height: 1.50; margin: 0px 0px 10px 0px;"><img src="rotate2d_3b.png" width="282" height="161" border="0" alt="rotate2D_3b"></p>
<p style="text-align: justify; line-height: 1.50;"><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">When a selected part is only a fragment which connects to the rest of the molecule with one bond only, you cannot move the pivot point (which is gray in this case). In such cases the rotation center will be that atom of the selected fragment which connects to the non-selected part of the structure.</span></p>
<p style="line-height: 1.50; margin: 0px 0px 14px 0px;"><img src="rotate2d_4b.png" width="248" height="159" border="0" alt="rotate2D_4b"></p>
<p style="text-align: justify; line-height: 1.50;"><a name="mirroringstructures"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Mirroring structures</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">M</span><span style="font-size: 12pt; font-family: 'Times New Roman';">olecules or fragments can be mirrored either horizontally or vertically using the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Mirror Horizontally</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> or </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Mirror Vertically</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> options in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #077179; text-decoration: underline;"><a href="pop_up_menus.htm#pop-upmenuelementsuponselection" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">pop-up menu</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';">. For mirroring, you should select the molecule or fragment concerned, right-click inside the selected area, and select the appropriate menu option. Mirroring is possible only for whole, unattached molecules or for fragments which connect with no more than one bond to the unselected part of the structure. In any other case, the mirror options remain inactive.</span></p>
<p style="line-height: 1.50; margin: 0px 0px 10px 0px;"><img src="mirror.png" width="684" height="323" border="0" alt="mirror"></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Finally, it has to be mentioned that the rules of undoing/redoing are the same for every kind of transformation: an event begins with pressing down the mouse button and ends with releasing it. The whole action between these two points can be undone or set back in one step.</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 19px 0px 0px 0px;"><a name="atomproperties"></a><span class="f_Heading1" style="font-family: 'Times New Roman'; color: #077179;">Atom Properties</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Currently Marvin for JavaScript supports the display of atomic charges, &nbsp;isotopes, and enhanced stereo specifications as atom properties. All of these attributes can be set in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Atom Properties</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> dialog window available from the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #077179; text-decoration: underline;"><a href="pop_up_menus.htm#atompop-upmenu" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">pop-up menu</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> after a right-click on the atom.</span></p>
<p style="text-align: justify; line-height: 1.50;"><img src="properties3.png" width="350" height="309" border="0" alt="properties3"></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The dialog provides options for changing atom type, setting new atomic symbols, or defining properties of atoms. You can choose from the following atom types: element, query, R-group, List/NOT List, or Pseudo. Choosing any of these types, only the relevant property fields appear on the dialog. For example, in case of a </span><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #008080; text-decoration: underline;"><a href="chemical_features_in_marvin_fo.htm#pseudoatoms" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #008080; background-color: transparent; text-decoration: underline;">pseudo atom</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';">, there are three properties available: pseudo, charge, and enhanced stereo. Previously defined properties can be removed by clicking on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Remove property</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">These properties can be set for several atoms at the same time, by selecting the atoms in question and then opening the Atom Properties dialog from the context menu. In this case, only those properties are editable which are permitted for every selected atom type, and the same changes will be applied to every selected atom. If one or more of the selected atoms has/have attached properties, you can delete them by using their </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Remove property</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> buttons.</span></p>
<p style="text-align: justify; line-height: 1.50;"><a name="alias"></a><span style="font-size: 12pt; font-family: 'Times New Roman';">If you use an 'Alias' for an atom, the label given in the textbox will replace the standard atomic symbol on the canvas, but the atom type remains unchanged. With the exception of enhanced stereo notations, atom properties are not displayed when an atom alias is used; however, they preserve their previously defined values and can be modified later.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The enhanced stereo notations (Off, Absolute, And, Or) can be added to chiral atoms (marked with wedge bonds) individually in order to assign them to stereogenic groups. The default value is the "Off" identifier, while in case of "Or" and "And" groups a number should accompany the identifier. If you leave the number field empty, number 1 will be associated with the group identifier automatically. </span><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #077179; text-decoration: underline;"><a href="chemical_features_in_marvin_fo.htm#enhancedstereospecifications" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">Learn more about enhanced stereo specifications in Marvin for JavaScript</a>.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The "OK" button closes the window, and the changes appear in the structure. As a result, a charge will appear as an upper right index of the given atom, while the mass number (isotope type) will be displayed as an upper left index. In case of hydrogen isotopes (deuterium and tritium), their specific atomic symbols can be used (D and T, respectively), or they can be displayed as </span><span style="font-size: 8pt; font-family: 'Times New Roman'; vertical-align: super;">2</span><span style="font-size: 12pt; font-family: 'Times New Roman';">H or </span><span style="font-size: 8pt; font-family: 'Times New Roman'; vertical-align: super;">3</span><span style="font-size: 12pt; font-family: 'Times New Roman';">H, too. If you click on the "[X]" button in the upper right corner of the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Atom Properties</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> box or press the "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Esc</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" button, the popup window gets closed without applying any changes to the selected atom.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Note that atomic charges can be modified using the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="toolbars.htm#increasecharge" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Increase Charge/</a><a href="toolbars.htm#decreasecharge" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Decrease Charge</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> buttons on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Tools </span><span style="font-size: 12pt; font-family: 'Times New Roman';">toolbar, too. </span></p>
<p style="text-align: justify; line-height: 1.50; margin: 19px 0px 0px 0px;"><a name="displayoptions"></a><span class="f_Heading1" style="font-family: 'Times New Roman'; color: #077179;">Display Options</span></p>
<p style="text-align: justify; line-height: 1.50;"><a name="structuredisplayoptions"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Structure display options</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Structure display options can be modified with the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="toolbars.htm#viewsettings" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">View Settings</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">General</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar, which opens the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">View Settings</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> dialog box. The options offered here are the following:</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Show chiral flag:</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> The visibility of the "Absolute" chiral flag can be turned on/off.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Show valence errors: </span><span style="font-size: 12pt; font-family: 'Times New Roman';">Marking of atoms with incorrect valence can be turned on/off.</span></td></tr></table></div><div style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Index atoms: </span><span style="font-size: 12pt; font-family: 'Times New Roman';">When checked, the atom indices (the order of the atoms put on the canvas) are displayed.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Use CPK colors:</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> When checked, the atoms are displayed colored according to the Corey-Pauling-Koltun convention. The two halves of a bond between two atoms will receive the color of the atom they are connected to.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Show carbon labels:</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> When checked, the carbon atom labels become visible.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Implicit H:</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> To display the automatically added necessary hydrogens on atoms, an option from the drop-down list in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">View Settings</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> window should be chosen. These options are the following:</span></td></tr></table></div><div style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table cellspacing="0" cellpadding="0" border="0" style="border: none; border-spacing:0px; border-collapse: collapse;">
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="148" style="width:148px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Off</span></p>
</td>
<td valign="middle" width="499" style="width:499px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Disable implicit hydrogens on every atom.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="148" style="width:148px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Hetero</span></p>
</td>
<td valign="middle" width="499" style="width:499px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Display implicit hydrogens on hetero atoms.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="148" style="width:148px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Hetero and Terminal</span></p>
</td>
<td valign="middle" width="499" style="width:499px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Display implicit hydrogens on hetero atoms and terminal carbons. (Default setting)</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="148" style="width:148px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">All</span></p>
</td>
<td valign="middle" width="499" style="width:499px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Display implicit hydrogens on every atom.</span></p>
</td>
</tr>
</table>
</div>

</td></tr></table>

</body>
</html>
