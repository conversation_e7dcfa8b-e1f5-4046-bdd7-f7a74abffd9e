﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<!-- saved from url=(0029)http://www.helpandmanual.com/ --><head>
   <title>&nbsp;</title>
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />

   <!-- This line includes the general project style sheet (not required) -->
   <meta http-equiv="Content-Style-Type" content="text/css" />
   <link type="text/css" href="default.css" rel="stylesheet" />

   <!-- This block defines the styles of the TOC headings, change them as needed -->
   <style type="text/css">
       .navtitle { font-size: 14pt; font-weight: bold; margin-bottom: 16px; }
       .navbar   { font-size: 10pt; }

       .heading1 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading2 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading3 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading4 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading5 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading6 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }

       .hilight1 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight2 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight3 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight4 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight5 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight6 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }

       TD.toc { padding-bottom: 2px; padding-right: 4px }
   </style>
</head>
<body style="background: #FFFFFF; url(null) fixed no-repeat" onload="parent.loadstate(document.getElementById('tree'));" onunload="parent.savestate(document.getElementById('tree'));">
<p class="navtitle">&nbsp;</p>
<p class="navbar">
<b>Contents</b>
 | <a href="marvin_js_new_kwindex_dyn.html">Index</a>
 | <a href="marvin_js_new_ftsearch.html">Search</a>
</p><hr size="1" />

<!-- Place holder for the TOC - this variable is REQUIRED! -->
<div id="tree">
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><a href="javascript:parent.toggle('div1')"><img id="i1" name="cicon1.gif:cicon2.gif" class="icon" src="cicon1.gif" border="0" alt="Icon"/></a></span></td><td class="toc" align="left"><a id="a1" href="layout_of_marvinsketch.htm" target="hmcontent" onclick="return parent.hilight('s1')" ondblclick="javascript:parent.toggle('div1')"><span id="s1" class="heading1">Layout of Marvin for JavaScript</span></a></td></tr></table>
<div id="div1" style="display:none">
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="40" align="right"><span class="heading2"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a id="a1.1" href="general_toolbar.htm" target="hmcontent" onclick="return parent.hilight('s1.1')" ondblclick="javascript:parent.toggle('div1.1')"><span id="s1.1" class="heading2">General Toolbar</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="40" align="right"><span class="heading2"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a id="a1.2" href="tools_toolbar.htm" target="hmcontent" onclick="return parent.hilight('s1.2')" ondblclick="javascript:parent.toggle('div1.2')"><span id="s1.2" class="heading2">Tools Toolbar</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="40" align="right"><span class="heading2"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a id="a1.3" href="atoms_toolbar.htm" target="hmcontent" onclick="return parent.hilight('s1.3')" ondblclick="javascript:parent.toggle('div1.3')"><span id="s1.3" class="heading2">Atoms Toolbar</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="40" align="right"><span class="heading2"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a id="a1.4" href="templates_toolbar.htm" target="hmcontent" onclick="return parent.hilight('s1.4')" ondblclick="javascript:parent.toggle('div1.4')"><span id="s1.4" class="heading2">Templates Toolbar</span></a></td></tr></table>
</div>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a id="a2" href="basic_features_of_marvinsketch.htm" target="hmcontent" onclick="return parent.hilight('s2')" ondblclick="javascript:parent.toggle('div2')"><span id="s2" class="heading1">Basic Features of Marvin for JavaScript</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a id="a3" href="shortcuts.htm" target="hmcontent" onclick="return parent.hilight('s3')" ondblclick="javascript:parent.toggle('div3')"><span id="s3" class="heading1">Shortcuts</span></a></td></tr></table>
</div>
<script type="text/javascript">
parent.preloadicons('cicon2.gif','cicon1.gif','cicon9.gif');</script>

<hr size="1" /><p style="font-size: 8pt">© 2012 Enter your company name</p>
</body>
</html>
