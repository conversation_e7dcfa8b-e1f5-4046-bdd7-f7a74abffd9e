﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<!-- saved from url=(0029)http://www.helpandmanual.com/ -->
<head>
   <title>Basic Features of Marvin for JavaScript</title>
   <meta name="generator" content="Help &amp; Manual" />
   <meta name="keywords" content="" />
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="Content-Style-Type" content="text/css" />
   <link type="text/css" href="default.css" rel="stylesheet" />
<script type="text/javascript" src="helpman_topicinit.js"></script>
<!-- Redirect browser to frame page if page is not in the content frame. -->
<script type="text/javascript">
<!--
if (location.search.lastIndexOf("toc=0")<=0) {
  if (parent.frames.length==0) { parent.location.href="index.html?basic_features_of_marvinsketch.htm"; }
  else { parent.quicksync('a2'); }
}
//-->
</script>
<script type="text/javascript" src="highlight.js"></script></head>
<body style="margin: 0px 0px 0px 0px; background: #FFFFFF;" onload="highlight();">
<div id="hmpopupDiv" style="visibility:hidden; position:absolute; z-index:1000; "></div>


<table width="100%" border="0" cellspacing="0" cellpadding="5" bgcolor="#D0D0D0">
  <tr valign="middle">
    <td align="left">
      <p style="line-height: 1.50;"><span class="f_Heading1" style="font-size: 18pt; font-family: 'Times New Roman';">Basic Features of Marvin for JavaScript</span></p>

    </td>
    <td align="right">
     <span style="font-size: 9pt">
     <a href="layout_of_marvinsketch.htm">Top</a>&nbsp;
     <a href="templates_toolbar.htm">Previous</a>&nbsp;
     <a href="shortcuts.htm">Next</a>
     </span>
    </td>
  </tr>
</table>


<!-- Placeholder for topic body. -->
<table width="100%" border="0" cellspacing="0" cellpadding="5"><tr valign="top"><td align="left">
<div style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="18"><span style="font-weight: bold; font-size: 12pt; font-family: 'Times New Roman'; color: #000000;">1.</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Importing a structure</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Marvin for JavaScript handles MDL Molfiles V2000 (.mol extension) and ChemAxon Marvin Documents (.mrv extension) as input files. Click on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Open</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">General </span><span style="font-size: 12pt; font-family: 'Times New Roman';">toolbar</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> to open the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Import</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> popup window. The dialog window can be opened by pressing "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Ctrl+O</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" keyboard shortcut, too. Open the structure file in *.mol or *.mrv format with an editor, </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic;">e.g.</span><span style="font-size: 12pt; font-family: 'Times New Roman';">,</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> Windows's Notepad, select all, and copy the file contents to the clipboard then paste it into the input area of the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Import</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> window. At the top of the window you can select the appropriate file format from a drop-down list. Finally, pressing the "Import" button loads the structure on the canvas and closes the dialog window.</span></p>
<p style="line-height: 1.50; margin: 0px 0px 10px 0px;"><img src="import.png" width="537" height="391" border="0" alt="import"></p>
<div style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 24px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="18"><span style="font-weight: bold; font-size: 12pt; font-family: 'Times New Roman'; color: #000000;">2.</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Saving a structure</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50; margin: 0px 0px 24px 0px;"><span style="font-size: 12pt; font-family: 'Times New Roman';">A structure can be saved either in ChemAxon Marvin Document format or in MDL Molfile V2000 format. To save the structures on the canvas, click on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Save</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">General </span><span style="font-size: 12pt; font-family: 'Times New Roman';">toolbar</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> which opens the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Save</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> popup window. The dialog window can be opened with the "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Ctrl+S</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" keyboard shortcut, too. In this window you can see the canvas contents in the default ChemAxon Marvin Document format. The displayed structure file format can be changed by selecting an option from the drop-down list of supported file formats. To save that to a file you need to copy its content to the clipboard and paste it into a text editor, for example.</span></p>
<div style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="18"><span style="font-weight: bold; font-size: 12pt; font-family: 'Times New Roman'; color: #000000;">3.</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Cut/Copy/Paste options</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">To copy a molecule or a fragment you have to select it first. The </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Cut</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> and </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Copy</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> buttons in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">General</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar place the content of the selected area on a virtual clipboard (available only within the application). The content of the clipboard can be imported to the canvas with the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Paste</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">General</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar. Their conventional shortcuts, "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Ctrl+X</span><span style="font-size: 12pt; font-family: 'Times New Roman';">", "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Ctrl+C</span><span style="font-size: 12pt; font-family: 'Times New Roman';">", and "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Ctrl+V</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" can be used, too.</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 0px 0px 24px 0px;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Pressing the "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Ctrl</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" button and hovering the cursor over a selected area, you can move a copy of the selected structure by dragging it. When you do this, a "+" sign appears beside the cursor. A copy of the selected structure is placed on the canvas when you release the left button. Note that the new object remains selected when it has been placed on the canvas, so it can be multiplied several times.</span></p>
<p style="line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">4. Navigating on the canvas and modifying the zoom ratio</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">You can navigate on the canvas using either the arrows at the lower right corner of the canvas (</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Scroll X</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> and </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Scroll Y</span><span style="font-size: 12pt; font-family: 'Times New Roman';">) or the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Navigate</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button between them. In "Navigate" mode the canvas can be moved by dragging: if you click on the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Navigate</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button the "move" cursor appears and the canvas can be repositioned. The </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Scroll X</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> and </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Scroll Y </span><span style="font-size: 12pt; font-family: 'Times New Roman';">arrows</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"> </span><span style="font-size: 12pt; font-family: 'Times New Roman';">work as usual:</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> &nbsp;you can either click on the respective arrow or scroll the mouse wheel while the cursor is hovered over the arrows.</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 0px 0px 24px 0px;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The content of the canvas can be reduced or magnified at will with the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"><a href="general_toolbar.htm#zoomin">Zoom In/Zoom Out</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> buttons in the</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"> General </span><span style="font-size: 12pt; font-family: 'Times New Roman';">toolbar</span><span style="font-size: 12pt; font-family: 'Times New Roman';">. The zoom ratio can be changed using the mouse wheel, too. (Zooming with the mouse wheel is centered at the cursor position. With this function, you can virtually "move" the canvas.) The </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Zoom All </span><span style="font-size: 12pt; font-family: 'Times New Roman';">button in the</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"> General</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar sets the optimal magnification to view everything on the canvas. The "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Enter</span><span style="font-size: 12pt; font-family: 'Times New Roman';">" shortcut can also be used for this purpose.</span></p>
<p style="line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">5. Drawing atoms</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">You can choose from several options to draw a new atom or to modify an already existing one:</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">An atom can be inserted by using one of the most frequently used atom <a href="atoms_toolbar.htm#themostfrequentlyusedatoms">buttons</a> in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Atoms</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar. The chosen atom appears on the tip of the cursor and you can place it on the canvas with a left-click.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Any of the known elements can be selected from the <a href="atoms_toolbar.htm#periodictable">Periodic Table</a>, the button of which you can find in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Atoms </span><span style="font-size: 12pt; font-family: 'Times New Roman';">toolbar</span><span style="font-size: 12pt; font-family: 'Times New Roman';">. Clicking an element, its atom symbol will appear on the tip of the cursor and you can place it on the canvas any times.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Atoms can be placed on the canvas using the keyboard, too. By typing an atomic symbol, it will appear on the tip of the cursor, and can be placed on the canvas with a left-click or pressing "</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Enter</span><span style="font-size: 12pt; font-family: 'Times New Roman';">". Although they are not listed in the Periodic Table, deuterium and tritium symbols (D and T, respectively) can also be used this way.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">When an atom is placed on the canvas, it will automatically be augmented with implicit hydrogens according to the free valences of the atom. Metallic elements are exceptions to this rule, they are added to the content of the canvas as free elements.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 24px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Already existing atoms can be replaced using either of the above mentioned methods. Choose an atomic symbol from the Periodic Table or the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Atoms</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar and hover the cursor over the atom on the canvas you want to change. When the atom gets a green background, left-click on it to replace it with the new atom type. Alternatively, you can change the type of an atom by hovering the cursor over it and then typing the new atomic symbol. The same result can be achieved by selecting the atoms you want to change first, and then choosing the new atom type either by clicking or by typing. If you select several atoms at the same time, you can change them simultaneously using one of the methods mentioned above.</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">6. Drawing bonds</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">To create a chemical bond between two atoms on the canvas <a href="tools_toolbar.htm#bonds">select the appropriate bond type using the </a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"><a href="tools_toolbar.htm#bonds">Bonds</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"><a href="tools_toolbar.htm#bonds"> button</a> in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Tools</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar, click on one of the two atoms, and drag the bond to the other atom.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">If you draw a new bond by clicking on or dragging from an already existing atom, the bond will end with a carbon atom, and the length of the bond will be the default value.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Selecting a bond type and dragging the cursor on the canvas will create a new bond in the desired orientation with carbon atoms on both ends.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">The type of an already existing bond can be changed by selecting the new bond type from the toolbar and clicking on the bond. The bond type can be selected by typing the appropriate <a href="tools_toolbar.htm#bonds">shortcut</a>, too. If the cursor is over the canvas, typing a shortcut will select the appropriate bond mode (the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Bonds</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button is activated). However, if the cursor is over an existing bond, typing a shortcut will modify the type of the given bond only. In this case the selected mode (the active button) will not change.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Note that repeated clicking on a bond will change its type depending on the currently active bond type. If the selected bond type is Single, successive clicks on a bond will change its type according to the Single-Double-Triple-Single-... order. If Double bond type is selected, successive clicks on a bond will alternate its type between Double and Triple. If a bond has a direction (Single Up, Single Down, Single Up or Down, and Coordinate bonds), clicking on it will change its direction.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 24px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">If you choose a new bond type when an area of the canvas is selected, the type of every bond within the selection will change to the chosen bond type.</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">7. Drawing a molecule</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Molecules can be built up by connecting atoms with different chemical bonds or with the use of templates in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"><a href="templates_toolbar.htm">Templates</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar.</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 0px 0px 10px 0px;"><span style="font-size: 12pt; font-family: 'Times New Roman';">You can create alkyl or conjugated hydrocarbon chains using the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"><a href="tools_toolbar.htm#chain">Chain</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Tools </span><span style="font-size: 12pt; font-family: 'Times New Roman';">toolbar</span><span style="font-size: 12pt; font-family: 'Times New Roman';">. A chain can be created as a new molecule or it can spread from an already existing atom. The new chain can be rotated around its starting atom to the desired orientation during drawing.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Merging structures:</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> </span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Structures can be modified by merging already existing fragments. For merging two fragments, one of them should be selected. If you hover the cursor over the selected fragment, the cursor changes to "move" type (crossed arrows) indicating that the fragment can be moved closer to the other fragment. When their distance has become sufficiently small, light-blue circle(s) appears(s) to indicate possible merging point(s).</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Merging is helpful, for example, if you want to create ring systems (spiro, fused, or bridged ring systems: one, two, and more than two atoms in common, respectively) or connect chainlike substructures.</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 0px 0px 10px 0px;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Templates can also be merged with a fragment on the canvas. Choose a template from the toolbar (it appears on the tip of the cursor) and bring it near the atoms of the existing fragment. When the distance between them has become small enough, the &nbsp;possible merging points will be indicated by a blue frame. Left-click with the mouse to fix the template in the merged position.</span></p>
<p style="line-height: 1.50; margin: 0px 0px 10px 0px;"><img src="merge2.png" width="187" height="154" border="0" alt="merge2"> &nbsp; &nbsp; &nbsp; &nbsp;<img src="merge1.png" width="174" height="115" border="0" alt="merge1"></p>
<p style="line-height: 1.50; margin: 0px 0px 10px 0px;"><span style="width:29px">&nbsp;</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman';">Spiro ring system &nbsp; &nbsp; &nbsp; &nbsp;Fused ring system</span></p>
<p style="text-align: justify; line-height: 1.50;"><a name="sproutdrawing"></a><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman';">Sprout drawing:</span></p>
<p style="text-align: justify; line-height: 1.50;"><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">Symmetric templates (cyclohexane, cyclopentane, benzene, pyrrole) can be connected to an atom with a single bond: select the template from the </span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman';">Templates</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;"> toolbar and hover the cursor over the desired atom to see a grey colored image of the template showing its would-be position. Clicking on the atom will connect the template to the atom with a single bond: one of the implicit hydrogens of the atom has been replaced by the template. Please note that non-symmetric templates (including naphthalene) cannot be sprouted.</span></p>
<p style="line-height: 1.50; margin: 0px 0px 10px 0px;"><img src="sprout1.png" width="252" height="169" border="0" alt="sprout1"></p>
<p style="text-align: justify; line-height: 1.50;"><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">When sprouting, the template and the connecting single bond can be rotated on the canvas by pressing the left mouse key and dragging the cursor.</span></p>
<p style="line-height: 1.50; margin: 0px 0px 10px 0px;"><img src="sprout2.png" width="252" height="169" border="0" alt="sprout2"></p>
<p style="text-align: justify; line-height: 1.50;"><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">If you want to avoid sprouting, hold down the "</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman';">Shift</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">" key while clicking on the selected atom. In this case the attachment point of the template will replace the selected atom in the original structure. It has similar effect as if the template had been merged to the fragment with one atom in common.</span></p>
<p style="line-height: 1.50; margin: 0px 0px 10px 0px;"><img src="sprout4.png" width="252" height="169" border="0" alt="sprout4"></p>
<p style="text-align: justify; line-height: 1.50;"><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">A template can be attached to an already existing bond which is about the same length as the template's bond: clicking on the bond connects the template to the selected bond. The template is rotated automatically in order to fit the chosen bond perfectly.</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">Pressing the "</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman';">Shift</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">" button and holding the left mouse key during sprout drawing moves the template to the other side of the bond. The appropriate side of the bond for sprout drawing a template can also be chosen by dragging the cursor.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">In case of the pyrrole template, pressing the "</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman';">Space</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">" button while sprouting rotates the template around its center, which can be particularly useful when fused rings are connected.</span></td></tr></table></div><p style="line-height: 1.50; margin: 0px 0px 24px 0px;"><img src="sprout3.png" width="476" height="133" border="0" alt="sprout3"></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">8. Drawing a query structure</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 0px 0px 24px 0px;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Query structures are generalized structures representing a group of similar molecules which differ only in some structural elements. For example, all of the molecules possess the same skeleton with a halogen atom as substituent, but it is not crucial to know which halogen exactly. See more about query structures in <a href="http://www.chemaxon.com/jchem/doc/user/queryindex.html" target="_blank" class="weblink">JChem Query Guide</a>. Using the <a href="atoms_toolbar.htm#queryatoms">query atoms</a> and </span><span style="font-size: 12pt; font-family: 'Times New Roman';"><a href="tools_toolbar.htm#bonds">bonds</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> offered in Marvin for JavaScript different query structures can be designed. Note that atom properties (e.g. atomic charge) can also be modified for any query atom in these structures.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">9. Transformation</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Dragging the selected structure</span><span style="font-size: 12pt; font-family: 'Times New Roman';">: After their selection, whole molecules or fragments (parts of molecules) can be moved on the canvas. When you hover the cursor over the selected area,</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> the cursor appears as crossed arrows indicating that the highlighted part can be dragged on the canvas. If you have selected a whole molecule, it gets translated without any modifications, but when the selection contains only a fragment, it will be distorted due to the changes in bond lengths and bond angles. Note that the positions of the atoms within the selected fragment are fixed, so the distortion concerns only the bonds between the selected and non-selected part of the molecule.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Rotation in 2D</span><span style="font-size: 12pt; font-family: 'Times New Roman';">: When a structure has been selected, it can be rotated around its center in two dimensions. In these cases a picture, similar to the one below, appears:</span></td></tr></table></div><p style="line-height: 1.50;"><img src="rotate2d_1.png" width="128" height="164" border="0" alt="rotate2D_1"></p>
<p style="text-align: justify; line-height: 1.50;"><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">The pink circle with a dot is the center of the selected area (pivot point). The molecule can be rotated around this point by clicking on the green dot and dragging the cursor on the canvas. Until the left key is pressed you can see the angle of rotation near this green dot. By default, rotation is constrained to 6° increments. For continuous rotation hold the "</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman';">Shift</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">" button while rotating.</span></p>
<p style="line-height: 1.50;"><img src="rotate2d_2.png" width="163" height="121" border="0" alt="rotate2D_2"></p>
<p style="text-align: justify; line-height: 1.50;"><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">If a whole molecule is selected, the original center of rotation can be relocated by dragging. In this case, if the new rotation center is too close to its original position, it will be snapped back to it. However, pressing the </span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman';">"Shift"</span><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;"> key while dragging the center of rotation allows it to be as close to its original place as you wish. Moving the center lets you rotate the molecule around any point of the canvas. The image of the starting structure remains displayed in grey during rotation.</span></p>
<p style="line-height: 1.50; margin: 0px 0px 10px 0px;"><img src="rotate2d_3.png" width="238" height="198" border="0" alt="rotate2D_3"></p>
<p style="text-align: justify; line-height: 1.50;"><span class="f_ImageCaption" style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: normal;">However, if you have selected a fragment which connects to the rest of the molecule with one bond only, you cannot move the pivot point (which is gray in this instance). In this case the rotation center will be that atom of the selected fragment which connects to the non-selected part of the structure.</span></p>
<p style="line-height: 1.50; margin: 0px 0px 10px 0px;"><img src="rotate2d_4.png" width="303" height="263" border="0" alt="rotate2D_4"></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 10px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Mirroring structures</span><span style="font-size: 12pt; font-family: 'Times New Roman';">: M</span><span style="font-size: 12pt; font-family: 'Times New Roman';">olecules or fragments can be mirrored either horizontally or vertically using the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"><a href="general_toolbar.htm#mirrorhorizontally">Mirror Horizontally</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> or </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"><a href="general_toolbar.htm#mirrorvertically">Mirror Vertically</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">General</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar. For mirroring, you should select the molecule or fragment concerned and click on the appropriate button.</span></td></tr></table></div><p style="line-height: 1.50; margin: 0px 0px 10px 0px;"><span style="width:48px">&nbsp;</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Mirror horizontally &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Mirror vertically</span></p>
<p style="line-height: 1.50; margin: 0px 0px 10px 0px;"><img src="mirror.png" width="586" height="271" border="0" alt="mirror"></p>
<p style="text-align: justify; line-height: 1.50; margin: 0px 0px 24px 0px;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Finally, it has to be stated that the rules of Undoing/Redoing are the same for every kind of transformation: an event begins with pressing down the mouse button and ends with releasing it. The whole action between these two points can be undone or set back in one step.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">10. Structure display options</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Structure display options can be modified with the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"><a href="general_toolbar.htm#viewsettings">View Settings</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">General</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> toolbar, which opens the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">View Settings</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> dialog box. The options offered here are the following:</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Use CPK Colors: When checked, the atoms are displayed colored according to the Corey-Pauling-Koltun convention. The two halves of a bond between two atoms will receive the color of the atom they are connected to.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Show Carbon Labels: When checked, the carbon atom labels become visible.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Index Atoms: When checked, the atom indices (the order of the atoms put on the canvas) are displayed.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman';">Implicit H: To display the automatically added necessary hydrogens on atoms, an option from the drop-down list in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">View Settings</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> window should be chosen. These options are the following:</span></td></tr></table></div><div style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table cellspacing="0" cellpadding="0" border="0" style="border: none; border-spacing:0px; border-collapse: collapse;">
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="148" style="width:148px; height:39px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Off</span></p>
</td>
<td valign="middle" width="499" style="width:499px; height:39px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Disable implicit hydrogens on every atom.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="148" style="width:148px; height:38px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Hetero</span></p>
</td>
<td valign="middle" width="499" style="width:499px; height:38px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Display implicit hydrogens on hetero atoms.</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="148" style="width:148px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Hetero and Terminal</span></p>
</td>
<td valign="middle" width="499" style="width:499px; height:37px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Display implicit hydrogens on hetero atoms and terminal carbons. (This is the default setting.)</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="148" style="width:148px; height:36px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">All</span></p>
</td>
<td valign="middle" width="499" style="width:499px; height:36px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Display implicit hydrogens on every atom.</span></p>
</td>
</tr>
</table>
</div>
<p style="text-align: justify; line-height: 1.50; margin: 24px 0px 0px 0px;"><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">11. Atom properties</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 0px 0px 24px 0px;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Currently Marvin for JavaScript supports the display of atomic charges and isotopes as atom properties. All of these attributes can be set using the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"><a href="tools_toolbar.htm#atomproperties">Atom Properties</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button in the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Tools </span><span style="font-size: 12pt; font-family: 'Times New Roman';">toolbar</span><span style="font-size: 12pt; font-family: 'Times New Roman';">. Note that atomic charges can also be modified using the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Increase Charge</span><span style="font-size: 12pt; font-family: 'Times New Roman';">/</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Decrease Charge</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> buttons in the same</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"> </span><span style="font-size: 12pt; font-family: 'Times New Roman';">toolbar</span><span style="font-size: 12pt; font-family: 'Times New Roman';">. As a result, a charge will appear as an upper right index of the given atom, while the mass number (isotope type) will be displayed as an upper left index. In case of hydrogen isotopes (deuterium and tritium), their specific atomic symbols can be used (D and T, respectively), or they can be displayed as </span><span style="font-size: 8pt; font-family: 'Times New Roman'; vertical-align: super;">2</span><span style="font-size: 12pt; font-family: 'Times New Roman';">H or </span><span style="font-size: 8pt; font-family: 'Times New Roman'; vertical-align: super;">3</span><span style="font-size: 12pt; font-family: 'Times New Roman';">H, too.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">12. Structure cleaning</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">If you have drawn a molecule you can perform a 2D cleaning on it using the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"><a href="general_toolbar.htm#clean">Clean</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> button in the toolbar. It calculates new 2D coordinates for the atoms on the canvas in order to get the same molecule in an evenly organized fashion. It is possible to clean only fragments of molecules or certain elements of a multi-molecular structure if they have been selected previously. In this case the 2D cleaning has no effect on the non-selected part of the structure.</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 0px 0px 24px 0px;"><span style="font-size: 12pt; font-family: 'Times New Roman';">This feature is available only if the server connection is active, otherwise an error message will be sent.</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">13. Error highlighting</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Marvin for JavaScript only highlights valence errors, but does not correct them, so you can make the necessary corrections manually. In case of recognizing a valence error, Marvin for JavaScript highlights the particular atom with red background.</span></p>

</td></tr></table>

</body>
</html>
