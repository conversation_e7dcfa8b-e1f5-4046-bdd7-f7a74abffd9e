﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Frameset//EN"
  "http://www.w3.org/TR/html4/frameset.dtd">
<html>
<!-- saved from url=(0029)http://www.helpandmanual.com/ -->
<head>
<title>Marvin for JavaScript User's Guide</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<script type="text/javascript" src="marvin4js_copy_navigation.js"></script>
<script type="text/javascript">
<!--
var defaulttopic="marvin_for_javascript.htm";
if (location.href.lastIndexOf("?") > 0) defaulttopic=location.href.substring(location.href.lastIndexOf("?")+1,location.href.length).replace(/:/g,"");
document.write('<frameset cols="12%,*" frameborder="1" framespacing="1">');
if (document.getElementById) {
  document.write('<frame name="hmnavigation" src="marvin4js_copy_content_dyn.html" title="Navigation frame">'); }
else {
  document.write('<frame name="hmnavigation" src="marvin4js_copy_content_static.html" title="Navigation frame">'); }
document.write('<frame name="hmcontent" src="' + defaulttopic + '" title="Content frame">');
document.write('</frameset>');
//-->
</script>
</head>
<noscript>
  <frameset cols="12%,*" frameborder="1" framespacing="1">
    <frame name="hmnavigation" src="marvin4js_copy_content_static.html" title="Navigation frame">
    <frame name="hmcontent" src="marvin_for_javascript.htm" title="Content frame">
    <noframes>
      This page requires frames<br><a href="marvin4js_copy_content_static.html">Click here to view the table of contents without frames</a>
    </noframes>
  </frameset>
</noscript>
</html>
