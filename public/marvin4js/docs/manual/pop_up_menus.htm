﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<!-- saved from url=(0029)http://www.helpandmanual.com/ -->
<head>
   <title>Pop-up Menus</title>
   <meta name="generator" content="Help &amp; Manual" />
   <meta name="keywords" content="" />
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />
   <link type="text/css" href="default.css" rel="stylesheet" />
   <style type="text/css">
     body { margin: 0px; background: #FFFFFF; }
   </style>
<script type="text/javascript" src="helpman_topicinit.js"></script>
<!-- Redirect browser to frame page if page is not in the content frame. -->
<script type="text/javascript">
<!--
if (location.search.lastIndexOf("toc=0")<=0) {
  if (parent.frames.length==0) { parent.location.href="index.html?pop_up_menus.htm"; }
  else { parent.quicksync('a1.1.3'); }
}
//-->
</script>
<script type="text/javascript" src="highlight.js"></script></head>
<body onload="highlight();">
<div id="hmpopupDiv" style="visibility:hidden; position:absolute; z-index:1000; "></div>


<table style="width:100%; border:none; border-spacing:0px; padding:5px; background:#E4F1F1">
  <tr style="vertical-align:middle">
    <td style="text-align:middle">
      <p style="text-align: center;"><span class="f_Heading1" style="font-family: 'Times New Roman'; color: #077179;">Pop-up Menus</span></p>

    </td>
  </tr>
</table>


<!-- Placeholder for topic body. -->
<table style="width:100%;border:none;border-spacing:0px"><tr style="vertical-align:top"><td style="text-align:left;padding:5px">
<p style="text-align: justify; line-height: 1.50; margin: 14px 0px 0px 0px;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The pop-up menus (or context menus) of Marvin for JavaScript can also make chemical structure editing more efficient. The pop-up menus can be opened with a right-click on the appropriate object (</span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-style: italic;">e.g.</span><span style="font-size: 12pt; font-family: 'Times New Roman';">, on an atom or on a bond). The elements of these menus can be divided up into five classes:</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="pop_up_menus.htm#atompop-upmenu" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Atom</a></span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"> </span><span style="font-size: 12pt; font-family: 'Times New Roman';">pop-up menu</span><span style="font-size: 12pt; font-family: 'Times New Roman';">;</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="pop_up_menus.htm#bondpop-upmenu" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Bond</a></span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"> </span><span style="font-size: 12pt; font-family: 'Times New Roman';">pop-up menu</span><span style="font-size: 12pt; font-family: 'Times New Roman';">;</span></td></tr></table></div><div style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="pop_up_menus.htm#grouppop-upmenu" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Group</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> pop-up menu;</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="pop_up_menus.htm#editpop-upmenu" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Edit</a></span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"> </span><span style="font-size: 12pt; font-family: 'Times New Roman';">pop-up menu</span><span style="font-size: 12pt; font-family: 'Times New Roman';">;</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179; text-decoration: underline;"><a href="pop_up_menus.htm#pop-upmenuelementsuponselection" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: bold; color: #077179; background-color: transparent; text-decoration: underline;">Selection</a></span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;"> </span><span style="font-size: 12pt; font-family: 'Times New Roman';">pop-up menu</span><span style="font-size: 12pt; font-family: 'Times New Roman';">.</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50; margin: 10px 0px 0px 0px;"><span style="font-size: 12pt; font-family: 'Times New Roman';">In the first three cases, you do not need to select an atom or a bond in order to open the respective context menu; if you right-click on the object you want to edit, the appropriate pop-up menu will appear. Among the elements of the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Edit</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> pop-up menu there are options which are available only if there is active selection on the canvas. The same applies for the last group of pop-up menu elements: they appear only upon selection. In many cases, the displayed context menu contains a combination of the elements from the classes mentioned above. Clicking on a selected atom, for example, will result in the following pop-up menu:</span></p>
<p style="text-align: justify; line-height: 1.50;"><img src="context%20menu.png" width="253" height="237" border="0" alt="context menu"></p>
<p style="text-align: justify; line-height: 1.50; margin: 14px 0px 0px 0px;"><a name="atompop-upmenu"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Atom Pop-up Menu</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The Atom pop-up menu opens by right-clicking on atom. It contains tools for editing atoms and their properties:</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Atom Properties</span><span style="font-size: 12pt; font-family: 'Times New Roman';">: opens the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; color: #077179; text-decoration: underline;"><a href="basic_features_of_marvin_for_j.htm#atomproperties" style="font-size: 12pt; font-family: 'Times New Roman'; font-style: normal; font-weight: normal; color: #077179; background-color: transparent; text-decoration: underline;">Atom Properties</a></span><span style="font-size: 12pt; font-family: 'Times New Roman';"> dialog window;</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Delete</span><span style="font-size: 12pt; font-family: 'Times New Roman';">: deletes the atom in question along with its bonds.</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50;"><a name="bondpop-upmenu"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Bond Pop-up Menu</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Right-click on a bond to open the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Bond</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> pop-up menu with the following elements:</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Delete</span><span style="font-size: 12pt; font-family: 'Times New Roman';">: erases the given bond without its atoms.</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50; margin: 14px 0px 0px 0px;"><a name="grouppop-upmenu"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Group Pop-up Menu</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Right-click on an expanded/contracted abbreviated group while its feedback is active will open the group contextual menu containing the following menu items:</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Expand</span><span style="font-size: 12pt; font-family: 'Times New Roman';">: Displays the whole structure of the chemical group instead of its abbreviated name.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Contract</span><span style="font-size: 12pt; font-family: 'Times New Roman';">: Displays the abbreviation of a chemical group.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Ungroup</span><span style="font-size: 12pt; font-family: 'Times New Roman';">: The "abbreviated group" status is removed from the group.</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">The "Expand" and "Contract" options are never active simultaneously. In case of a contracted abbreviated group, only the "Expand" option is available, while for an expanded group, only the "Contract" menu item is active.</span></p>
<p style="text-align: justify; line-height: 1.50; margin: 14px 0px 0px 0px;"><a name="editpop-upmenu"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Edit Pop-up Menu</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Right-click on an empty spot on the canvas will open the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Edit</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> pop-up menu containing general editing options. Some of them are available only if there is active selection on the canvas.</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Copy</span><span style="font-size: 12pt; font-family: 'Times New Roman';">: copies the selection to the clipboard;</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Paste</span><span style="font-size: 12pt; font-family: 'Times New Roman';">: inserts the content of the clipboard into the canvas of Marvin for JavaScript;</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Delete</span><span style="font-size: 12pt; font-family: 'Times New Roman';">: erases the selected object;</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Clear</span><span style="font-size: 12pt; font-family: 'Times New Roman';">: deletes everything on the canvas;</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Zoom All</span><span style="font-size: 12pt; font-family: 'Times New Roman';">: determines the optimal rate of magnification to see everything on the canvas;</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Zoom to Selection</span><span style="font-size: 12pt; font-family: 'Times New Roman';">: increases the zoom ratio with the selected object in the center.</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Absolute stereo (chiral)</span><span style="font-size: 12pt; font-family: 'Times New Roman';">: sets the "Absolute" chiral flag on the entire structure indicating that the molecule represents a single, well-defined stereoisomer.</span></td></tr></table></div><p style="text-align: justify; line-height: 1.50; margin: 14px 0px 0px 0px;"><a name="pop-upmenuelementsuponselection"></a><span style="font-size: 14pt; font-family: 'Times New Roman'; font-weight: bold; color: #077179;">Pop-up Menu Elements upon Selection</span></p>
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">These tools appear only in the context menu if there is active selection on the canvas. The mirror tools are active only when the selected object can be mirrored (whole molecule or fragment connected with no more than one bond to the rest of the structure).</span></p>
<div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Mirror Horizontally</span><span style="font-size: 12pt; font-family: 'Times New Roman';">: mirrors a selected molecule or fragment horizontally;</span></td></tr></table></div><div style="text-align: justify; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table border="0" cellpadding="0" cellspacing="0" style="line-height: 1.50;"><tr style="vertical-align:baseline" valign="baseline"><td width="13"><span style="font-size: 12pt; font-family: 'Arial Unicode MS', 'Lucida Sans Unicode', 'Arial'; color: #000000;">&#8226;</span></td><td><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Mirror Vertically</span><span style="font-size: 12pt; font-family: 'Times New Roman';">: mirrors a selected molecule or fragment vertically.</span></td></tr></table></div>
</td></tr></table>

</body>
</html>
