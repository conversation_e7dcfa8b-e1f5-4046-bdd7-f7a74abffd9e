﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<!-- saved from url=(0029)http://www.helpandmanual.com/ -->
<head>
   <title>Templates Toolbar</title>
   <meta name="generator" content="Help &amp; Manual" />
   <meta name="keywords" content="" />
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="Content-Style-Type" content="text/css" />
   <link type="text/css" href="default.css" rel="stylesheet" />
<script type="text/javascript" src="helpman_topicinit.js"></script>
<!-- Redirect browser to frame page if page is not in the content frame. -->
<script type="text/javascript">
<!--
if (location.search.lastIndexOf("toc=0")<=0) {
  if (parent.frames.length==0) { parent.location.href="index.html?templates_toolbar.htm"; }
  else { parent.quicksync('a1.4'); }
}
//-->
</script>
<script type="text/javascript" src="highlight.js"></script></head>
<body style="margin: 0px 0px 0px 0px; background: #FFFFFF;" onload="highlight();">
<div id="hmpopupDiv" style="visibility:hidden; position:absolute; z-index:1000; "></div>


<table width="100%" border="0" cellspacing="0" cellpadding="5" bgcolor="#D0D0D0">
  <tr valign="middle">
    <td align="left">
      <p style="line-height: 1.50;"><span class="f_Heading1" style="font-family: 'Times New Roman';">Templates Toolbar</span></p>

    </td>
    <td align="right">
     <span style="font-size: 9pt">
     <a href="layout_of_marvinsketch.htm">Top</a>&nbsp;
     <a href="atoms_toolbar.htm">Previous</a>&nbsp;
     <a href="basic_features_of_marvinsketch.htm">Next</a>
     </span>
    </td>
  </tr>
</table>


<!-- Placeholder for topic body. -->
<table width="100%" border="0" cellspacing="0" cellpadding="5"><tr valign="top"><td align="left">
<p style="text-align: justify; line-height: 1.50;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Six generic templates are available from the </span><span style="font-size: 12pt; font-family: 'Times New Roman'; font-weight: bold;">Templates</span><span style="font-size: 12pt; font-family: 'Times New Roman';"> </span><span style="font-size: 12pt; font-family: 'Times New Roman';">toolbar</span><span style="font-size: 12pt; font-family: 'Times New Roman';">. After selecting one of these templates you can place it on the canvas with a left mouse-click. When placing it on the canvas, you can rotate the template around its center by dragging the cursor. Templates can also be connected to already existing structures by <a href="basic_features_of_marvinsketch.htm#sproutdrawing">sprout drawing</a>.</span></p>
<div style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 24px 0px;"><table cellspacing="0" cellpadding="0" border="0" style="border: none; border-spacing:0px; border-collapse: collapse;">
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="84" style="width:84px; border: solid 1px #000000;"><p style="text-align: center;"><img src="cyclohexane.png" width="35" height="28" border="0" alt="cyclohexane" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="514" style="width:514px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Cyclohexane</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="84" style="width:84px; border: solid 1px #000000;"><p style="text-align: center;"><img src="cyclopentane.png" width="35" height="28" border="0" alt="cyclopentane" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="514" style="width:514px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Cyclopentane</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="84" style="width:84px; border: solid 1px #000000;"><p style="text-align: center;"><img src="pyrrole.png" width="35" height="28" border="0" alt="pyrrole" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="514" style="width:514px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Pyrrole</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="84" style="width:84px; border: solid 1px #000000;"><p style="text-align: center;"><img src="cyclopentane_house.png" width="35" height="29" border="0" alt="cyclopentane_house" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="514" style="width:514px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Cyclopentane (house)</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="84" style="width:84px; border: solid 1px #000000;"><p style="text-align: center;"><img src="benzene.png" width="35" height="28" border="0" alt="benzene" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="514" style="width:514px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Benzene</span></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:middle;">
<td valign="middle" width="84" style="width:84px; border: solid 1px #000000;"><p style="text-align: center;"><img src="naphthalene.png" width="35" height="26" border="0" alt="naphthalene" style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="middle" width="514" style="width:514px; border: solid 1px #000000;"><p style="text-align: center;"><span style="font-size: 12pt; font-family: 'Times New Roman';">Naphthalene</span></p>
</td>
</tr>
</table>
</div>

</td></tr></table>

</body>
</html>
