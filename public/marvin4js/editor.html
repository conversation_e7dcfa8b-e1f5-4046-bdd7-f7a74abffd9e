<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
	<title>Marvin for JavaScript</title>
	<link type="text/css" rel="stylesheet" href="editor.css" media="screen" />
	<script src="gui/gui.nocache.js"></script>
	<script>

		// called when marvin4js loaded
		function sketchOnLoad () {
			if(marvin.Sketch.isSupported()) {
				marvin.sketcherInstance = new marvin.Sketch("sketch");
			} else {
				alert("Cannot instantiate sketcher since current browser does not support HTML5 canvas.");
			}
		}

	</script>
</head>
<body>
	<noscript>
		<div>
			<p>Your web browser must have JavaScript enabled in order for this application to display correctly.</p>
		</div>
	</noscript>
	<div id="sketch"></div>
</body>
</html>
