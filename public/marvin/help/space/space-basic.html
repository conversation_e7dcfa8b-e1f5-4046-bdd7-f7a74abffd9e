<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>MarvinSpace Help: Features</TITLE>
	 <link rel=stylesheet type="text/css" href="../marvinmanuals.css">
</HEAD>
<BODY><H1>How To Use MarvinSpace Features</H1>
<a href="space-index.html">Table Of Contents</a>

<h2><a class="anchor" name="loading">Loading Molecules into MarvinSpace</a></h2>
<p>MarvinSpace can open molecule files saved in any of the
<a href="../formats/formats.html">supported formats </a>for viewing.
Input structures can either replace molecules already loaded into MarvinSpace,
or they can be added to exsiting structures. <br>
For further details follow <a href="space-gui.html#filemenu">this link</a>.
</p>


<h2><a class="anchor" name="manipulate">Manipulating the visible objects</a></h2>

<p>You can translate, rotate, or zoom in to or out from the molecules or other
objects seen on the canvas with mouse drag. Note, that these operation do not
affect the geometry of the structures viewed, but the view angle and direction.
For further details follow <a href="space-gui.html#eventsandactions">this link</a>.
</p>

<h2>Display options</h2>

<p>Various drawing and coloring modes are available in MarvinSpace. These
can be set for either the entire scene, for classes of components (e.g. small
molecules, proteins etc.) or for selected entities.<br>
For further details follow <a href="space-gui.html#displaymenu">this link</a>.
</p>

<h2>Table view</h2>
<p>MarvinSpace support table view by allowing the subdicision of the drawing canvas
to rectangular cells, Note, that cells have uniform sizes and cells cannot
be merged. Each cell can contain an arbitrary number and type of objects (small
molecules, proteins structures, surfaces etc.). Rows of cells as well as columns
of cells can by dynamically added to or removed from the current table.<br>
The manipulation of components displayed in cells can be done independently and
also in a synchronized manner.<br>
For further details follow <a href="space-gui.html#cellsmenu">this link</a>.
</p>


<P ALIGN=CENTER><A HREF="#">Return to Top</A></P>
<p ALIGN=CENTER>
Copyright &copy; 1998-2013
<a HREF="http://www.chemaxon.com">ChemAxon Ltd.</a>
</p>
<p ALIGN=CENTER>
<a HREF="http://www.chemaxon.com/marvin">http://www.chemaxon.com/marvin</a>
</p>

</BODY>
</HTML>
