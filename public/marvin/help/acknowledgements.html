<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2//EN">
<html>
<head>
<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
<TITLE>Acknowledgements</TITLE>
<link rel=stylesheet type="text/css" href="marvinmanuals.css">
</head>
<body>
<h1>Acknowledgements</h1>
<PERSON> uses software developed by:
<ul>
    <li><em>Aloe</em> Swing Extension Package: <code>cb.aloe.swing.tools</code> having <a href="http://www.gnu.org/licenses/lgpl.html">LGPL</a> license</li>
<li><a href="http://www.jgoodies.com/freeware/forms/">JGoodies Forms</a> is a free library.</li>
<li><a href="http://www.jgoodies.com/freeware/looks/index.html">JGoodies Looks</a> is a free library.</li>
<li><a href="http://www.objectplanet.com/easycharts/">EasyCharts</a>.</li>
<li><a href="http://www.singularsys.com/jep/">Jep - Java Math Expression Parser</a>, see <a href="http://www.singularsys.com/order/license.html">license</a>.</li>
<li><a href="http://sourceforge.net/projects/jacob-project">Jacob Java - Com Bridge</a>.</li>
<li><a href="http://www.ikvm.net">IKVM</a>, see <a href="http://weblog.ikvm.net/story.aspx/license">license</a>.</li>
<li>InChI<sup>TM</sup> Material by <a HREF="http://www.chemaxon.com/marvin/help/license/IUPACInChI.license.txt">IUPAC 2005&copy;</a></li>
<li><a HREF="http://jni-inchi.sourceforge.net/">JNI-InChI</a> is Copyright © 2006-2010, Sam Adams. JNI-InChI is free software:
        you can redistribute it and/or modify it under the terms of the GNU Lesser General Public License e as published
        by the Free Software Foundation, either version 3 of the License, or (at your option) any later version.
        (See <a HREF="http://www.gnu.org/licenses/gpl.html">GPL</a>, <a HREF="http://www.gnu.org/licenses/lgpl.html">LGPL</a> licence.)</li>
<li>Image formats:
<ul>
    <li><a HREF="http://xmlgraphics.apache.org/batik/">Apache SVG Toolkit</a>, see <a HREF="http://www.chemaxon.com/marvin/help/license/LICENSE.batik.txt">license</a></li>
    <li>JPEG Encoder by<a HREF="http://www.chemaxon.com/marvin/help/license/JpegEncoder.license.txt">
James R. Weeks and BioElectroMech&copy;</a>,
    <a HREF="http://www.chemaxon.com/marvin/help/license/JpegEncoder.IJGreadme.txt">Independent JPEG Group</a></li>
    <li><a HREF="http://www.chemaxon.com/marvin/help/formats/PngEncoder.java.txt">PNG encoder</a> by J. David Eisenberg is under <a HREF="http://www.gnu.org/licenses/lgpl.html">LGPL</a> license</li>
    <li>PPM encoder by Jef Poskanzer&copy;, see <a HREF="http://www.chemaxon.com/marvin/help/license/PpmEncoder.license.txt">license</a></li>
    <li><a HREF="http://java.freehep.org/vectorgraphics/">VectorGraphics</a> package of <a href="http://java.freehep.org/">FreeHEP Java Library</a> is under <a href="http://www.gnu.org/licenses/lgpl.html">LGPL</a> license.</li>
    <li><a HREF="http://osra.sourceforge.net">OSRA</a> software by <a HREF="http://cactus.nci.nih.gov/osra">CADD Group Chemoinformatics Tools and User Services</a></li>
	<li><a HREF="http://pdfbox.apache.org">Apache PDFBox -Java PDF Library</a></li>
</ul>
</li>
    <li>MarvinSpace is based on <a href="https://jogl.dev.java.net/">JOGL</a>, a
Java interface to OpenGL under <a href="http://www.opensource.org/licenses/bsd-license.html">
Berkeley Software Distribution (BSD) License</a></li>
    <li>MarvinSpace uses the DualThumbSlider component of <a href="http://www.gnf.org">
Genomics Institute of the Novartis Research Foundation (GNF)</a></li>
	<li>Name to structure and Structure to name use names from the
    	<a href="http://www.drugbank.ca/">DrugBank</a> database.</li>

</ul>

</body>
</html>
