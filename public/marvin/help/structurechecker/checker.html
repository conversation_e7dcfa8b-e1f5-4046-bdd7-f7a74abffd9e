<!DOCTYPE HTML>
<html lang="en">
<head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8">
    <meta name="description" content="ChemAxon's Structure Checker in MarvinSketch">
	<meta name="author" content="<PERSON><PERSON><PERSON>, Viktoria <PERSON>lfi">
	<meta name="keywords" content="Structure Checker, Marvin, fixer, automatic">
	<title>Structure Checker in MarvinSketch</title>
    <link rel="stylesheet" type="text/css" href="../structurechecker.css">
</head>
<body>
	<h1>Structure Checker in MarvinSketch</h1>
		
	<h3>Content</h3>
		<ul>
			<li><a href="#intro">Introduction</a></li>
			<li><a href="#usage">Usage in MarvinSketch</a>
				<ul>
					<li><a href="#setting">Setting <em>Structure Checker</em> configuration</a></li>
					<li><a href="#run">Start <em>Structure Checker</em></a></li>
				</ul>
			</li>
			<li><a href="#checkerlist">List of available checkers</a>
				<ul>
					<li><a href="checkerlist.html">Examples of available checkers and their fixers</a></li>
				</ul>
			</li>
			<li><a href="#links">Links</a></li>
		</ul>	
				
	<h2><a class="anchor" id="intro"></a>Introduction</h2>		
    <p><em>Structure Checker</em> is a tool for filtering <b>drawing mistakes</b> or 
	<b>special structural elements</b>. Registering a compound in a company's 
	database might limit the user in, for example, the introduction of non-natural 
	isotopes, query bonds, species with charge, etc. <em>Structure Checker</em> works
	with predefined checking elements and you can choose which ones should be 
	considered when you run a check. <em>Structure Checker</em> runs a check on the 
	molecule(s) features and lists them in a separate window with the option to 
	refresh when you make changes to your drawing. </p>
	<p> <em>Structure Checker</em>'s command line tool (<a href="structurechecker_cline.html"><code>structurechecker</code></a>), <a href="structurechecker.html">Structure Checker GUI</a>, 
	and API for batch usage requires a <em>Structure Checker</em> license. This guide 
	refers to the checkers available free in MarvinSketch as an add-on.</p>

    <h2><a class="anchor" id="usage"></a>Usage</h2>
	
	<h3><a class="anchor" id="setting"></a>Setting <em>Structure Checker</em> configuration</h3>
    <p>Before you start using <em>Structure Checker</em> in MarvinSketch, consult the
	list of available checkers in the Preferences dialog (<b>Edit</b> &gt; 
	<b>Preferences</b> &gt; <b>Checkers</b> tab).</p>
    <p>	
		<img src="images/msketch/pref_checkers.png" width="644" height="438" alt="pref_checkers"/><br/>
		The <b>default list</b> contains checkers in alphabetical order.<br>
			Two types of checkers are listed and are distinguished by the additional 'error' tag and by different icons: 
			<ul>
				<li><img src="images/msketch/defaultcheck.png" width="24" height="24" alt="error checker"/> 
				<b>Error detecting checkers</b>: Error checkers are looking for 
				chemically wrong molecule parts (e.g., valence errors, overlapping atoms);</li>
				<li><img src="images/msketch/defaultfeaturecheck.png" width="24" height="24" alt="feature checker"/> 
				<b>Feature detecting checkers</b>: Feature checkers searches the
				selected molecule characteristics which in the specific structures 
				are not considered as an error (e.g., query properties). </li>
			</ul>
			Click on the name of the checker to see additional options and select the
            needed options by clicking the tick box.	
	</p>
	
    <h4>Checking order</h4>
    <p>Some molecule properties might change after fixing an issue. You can define 
	a checking sequence by moving up or down the list elements. The order of the
	checkers may be important in case you run a <b>Fix All</b> action.<br>
	In order to <b>change the sequence of the checker items</b>, select the checker from the list and click on the up <img src="images/msketch/up.png" width="24" height="24" alt="up"/> 
	or down <img src="images/msketch/down.png" width="24" height="24" alt="down"/> 
	icon on the right side of the dialog window. </p>
        
	<h4><a id="add"></a>Add and remove checker items</h4>
    <p>To <b>discard items</b> from the checking sequence, select the checker and 
	click on the <img src="images/msketch/remove.png" width="24" height="24" alt="remove"/> 
	button.<br> For <b>adding new elements</b> 
	to the list, click on the <img src="images/msketch/add.png" width="24" height="24" alt="add"/> 
	button and select the relevant checker.</p>
     
    <h4><a id="save">Save and load checker configuration</h4>
    <p>Different checking lists might be needed for different compound families. To
    make checking simpler, you can <b>save your checking list</b> to your
    computer and open it again if you are editing the same molecule type. 
	<ul>
		<li>Click the <b>Save</b> button <img src="images/msketch/save.png" width="24" height="24" alt="save"/>
		to save your set checker configuration. </li>
		<li>Click the <b>Load</b> button <img src="images/msketch/open.png" width="24" height="24" alt="open"/>
		to open a saved configuration list from file.</li> 
		<li>You can also load remote checker configuration from 
		URL <img src="images/msketch/url.png" width="24" height="24" alt="url" />. Unless you save this 
		configuration to your computer, the loaded checker configuration cannot be modified.</li>
		<li>You can integrate custom, <a href="#external">external checkers</a> or fixers to the list of available checkers <img src="images/msketch/configure.png" width="24" height="24" alt="url" />. </li>
	</ul>
	You can see the path of currently applied configuration file at the bottom of the window.</p>
	
	<h4><a id="external"></a>External checkers</h4>
	<p>
	Custom checker and fixer implementations can be integrated into the list of available checkers in ChemAxon products via the menu of MarvinSketch > Preferences > Checkers > <img src="images/msketch/configure.png" width="24" height="24" alt="external" />. 
	</p>
	<p>
	<img src="images/msketch/external01.png" /> <br/>
	<strong>External Structure Checkers and Fixers</strong> window contains two tabs: <strong>Checkers</strong> and <strong>Fixers</strong>. 
	Add the external checker(s)/fixer(s) by pressing <img src="images/msketch/add.png" width="18" height="18" alt="plus"/> 
	button on the relevant tab. 
	</p>
	<p><img src="images/msketch/external02.png" width="480" height="88" alt="dialog"/><br>First, enter 
	the URL or browse the location of JAR file containing the particular checker/fixer in field <strong>Java Archive (JAR)</strong>. 
	If the JAR file contains more than one classes, select the appropriate class from the drop-down 
	list to add that checker or fixer to the list of external checkers or fixers; information fields 
	will be filled automatically if checker/fixer classes use annotations (e.g., Checker/Fixer ID, Help text, Description, etc.). 
	The <strong>Checker/Fixer ID</strong> will appear among the list of available checkers.    
	</p>
	<p>You can add various external checkers and/or fixers from the same or from different JAR files. 
	The integrated external checkers and fixers can be exported to or imported from a configuration XML 
	by using the appropriate button (<strong>Export</strong> or <strong>Import</strong>). This configuration XML file stores 
	the class, ID and JAR file location of the set checkers and fixers.    			
	</p>
	
	<p>
	After you have finished adding external checkers and fixers, click on <strong>OK</strong>; the newly 
	added checkers will appear among the factory checkers. These external checkers/fixers will now also be available in 
	Structurechecker GUI. 
	When the external checker/fixer uses <code>actionStringToken</code> annotation, the checker can be 
	referenced on the defined name in <code>structurechecker</code> command-line and in Chemical Terms applications as well.  
	<br>See how to <a href="#add">Add checker(s) to checker configuration</a>.
	</p>
	
	<h4>Invalid checkers</h4>
	<p><em>Structure Checker</em> configuration files may contain erroneous checker(s), i.e., Invalid Checker(s). <br/>
	<img src="images/msketch/invalid_checker.png" width="644" height="438" alt="invalid checker"/><br/>
	If the imported configuration file contains invalid checker(s), a warning message will appear informing the user about the issue.
	<br/>Remove the invalid checker(s) from the list to accept the proper configuration.
	</p>
	
		

    <h3><a class="anchor" id="run"></a>Start <em>Structure Checker</em></h3>
    <p>You can open <em>Structure Checker</em> at any stage of your work. As you
    click on the <b>Check structure</b> icon <img src="images/msketch/defaultcheck.png" width="24" height="24" alt="defaultcheck"/>
    in the toolbar, or in the <b>Structure</b> menu, or left-click the <b>Checker</b> 
	button in the statusbar, or shortcut ctrl+r, a window will appear next to the 
	MarvinSketch window. The molecule regions found by <em>Structure Checker</em> are 
	highlighted on the canvas with a light red color. If you click on any checker 
	message, the affected area changes color to darker red. If you move the cursor 
	over one of the affected region, the appropriate checker message is highlighted 
	in the checker list.</p>
	
	<h3>Recheck the structure</h3>
	<p>After you have changed the structure (including accepting a fix), the checker 
	will offer re-checking: click on the <b>Check structure again</b> in the 
	<strong>Structure Checker</strong> window. <p><img src="images/msketch/checkerwindow.png" width="" height="" alt="re-check"/></p>
	</p>
	<p>Another option is to turn on automatic re-checking: click the <b>Enable 
	automatic checking</b> at the bottom of the text. At any point you can allow <em>Structure Checker</em> 
	to fix the issues one by one or every of them by a single click. </p>

    <h3>Automatic structure checking</h3>   
	<p>Turn on <b>Automatic Check</b> to follow every drawing step you make by a 
	quick check. This option is switched on in the <b>Structure</b> menu or 
	right-clicking the <b>Checker</b> <img src="images/msketch/statusbar-checker-disabled.png" width="16" height="16" alt="inactive checker on statusbar"/> 
	button on the status bar which then becomes colored. 
		<ul>
			<li>In case of an error, a red exclamation mark appears 
			<img src="images/msketch/statusbar-checker-error.png" width="16" height="16" alt="found error report on statusbar"/>. 
			Left-clicking on this icon opens the <strong>Structure Checker</strong> window, where
			all the warnings are listed.</li>
			<li>While the molecules are scanned for checker issues, an olive question mark <img src="images/msketch/statusbar-checker-progress.png" width="16" height="16" alt="checking in progress"/> appears on the status bar.</li>
			<li>If the molecule is correct, a green check mark 
			<img src="images/msketch/statusbar-checker-enabled.png" width="18" height="18" alt="active checker on statusbar"/> 
			appears in the Checker icon on the status bar.</li>
		</ul>
	</p>
	
	
	<h2><a id="checkerlist" class="anchor"></a>List of available checkers and fixers</h2>
		<ul class="floating">
		<li><a href="checkerlist.html#abbrevgroup">Abbreviated Group</a></li>
		<li><a href="checkerlist.html#absentchiralflag">Absent Chiral Flag</a></li>
		<li><a href="checkerlist.html#absolutestereoconfiguration">Absolute Stereo Configuration</a></li>
		<li><a href="checkerlist.html#alias">Alias</a></li>
		<li><a href="checkerlist.html#aromaticityerror">Aromaticity Error</a></li>
		<li><a href="checkerlist.html#atommap">Atom Map</a></li>
		<li><a href="checkerlist.html#atomqueryproperty">Atom Query Property</a></li>
		<li><a href="checkerlist.html#atomvalue">Atom Value</a></li>
		<li><a href="checkerlist.html#atropisomer">Atropisomer</a></li>
		<li><a href="checkerlist.html#attacheddata">Attached Data</a></li>
		<li><a href="checkerlist.html#bondangle">Bond Angle</a></li>
		<li><a href="checkerlist.html#bondlength">Bond Length</a></li>
		<li><a href="checkerlist.html#chiralflag">Chiral Flag</a></li>
		<li><a href="checkerlist.html#chiralflagerror">Chiral Flag Error</a></li>		
	</ul>
	<ul class="floating">			
		<li><a href="checkerlist.html#circularrgroupreference">Circular R-group Reference</a></li>
		<li><a href="checkerlist.html#coordsystem">Coordination System Error</a></li>
		<li><a href="checkerlist.html#covalentcounterion">Covalent Counterion</a></li>
		<li><a href="checkerlist.html#crosseddoublebond">Crossed Double Bond</a></li>
		<li><a href="checkerlist.html#empty">Empty Structure</a></li>
		<li><a href="checkerlist.html#explicith">Explicit Hydrogen</a></li>
		<li><a href="checkerlist.html#explicitlp">Explicit Lone Pairs</a></li>
		<li><a href="checkerlist.html#ezdoublebond">E/Z Double Bond</a></li>
		<li><a href="checkerlist.html#isotope">Isotope</a></li>
		<li><a href="checkerlist.html#metallocene">Metallocene</a></li>
		<li><a href="checkerlist.html#missingatommap">Missing Atom Map</a></li>
		<li><a href="checkerlist.html#missingrgroupreference">Missing R-group Reference</a></li>
		<li><a href="checkerlist.html#moleculecharge">Molecule Charge</a></li>
		<li><a href="checkerlist.html#multicenter">Multicenter</a></li>
		<li><a href="checkerlist.html#multicomponent">Multicomponent</a></li>		
	</ul>
	<ul class="floating">
		<li><a href="checkerlist.html#multiplestereocenter">Multiple Stereocenter</a></li>	
		<li><a href="checkerlist.html#ocr">OCR Error</a></li>		
		<li><a href="checkerlist.html#overlappingatoms">Overlapping Atoms</a></li>
		<li><a href="checkerlist.html#overlappingbonds">Overlapping Bonds</a></li>
		<li><a href="checkerlist.html#pseudoatom">Pseudo Atom</a></li>
		<li><a href="checkerlist.html#queryatom">Query Atom</a></li>
		<li><a href="checkerlist.html#querybond">Query Bond</a></li>
		<li><a href="checkerlist.html#ratom">R-atom</a></li>
		<li><a href="checkerlist.html#rgroupattachmenterror">R-group Attachment Error</a></li>
		<li><a href="checkerlist.html#rgref">R-group Reference Error</a></li>
		<li><a href="checkerlist.html#racemate">Racemate</a></li>
		<li><a href="checkerlist.html#radical">Radical</a></li>
		<li><a href="checkerlist.html#rareelement">Rare Element</a></li>
		<li><a href="checkerlist.html#reactionmaperror">Reaction Map Error</a></li>		
		<li><a href="checkerlist.html#relativestereo">Relative Stereo</a></li>
	</ul>
	<ul class="floating">
		<li><a href="checkerlist.html#ringstrainerror">Ring Strain Error</a></li>
		<li><a href="checkerlist.html#solvent">Solvent</a></li>		
		<li><a href="checkerlist.html#staratom">Star Atom</a></li>
		<li><a href="checkerlist.html#stereocarebox">Stereo Care Box</a></li>
		<li><a href="checkerlist.html#straightdoublebond">Straight Double Bond</a></li>
		<li><a href="checkerlist.html#substructure">Substructure</a></li>
		<li><a href="checkerlist.html#3d">Three Dimension(3D)</a></li>
		<li><a href="checkerlist.html#unbalancedreaction">Unbalanced Reaction</a></li>
		<li><a href="checkerlist.html#unusedrgroupreference">Unused R-group Reference</a></li>
		<li><a href="checkerlist.html#valenceerror">Valence Error</a></li>
		<li><a href="checkerlist.html#valenceproperty">Valence Property</a></li>
		<li><a href="checkerlist.html#wedgeerror">Wedge Error</a></li>
		<li><a href="checkerlist.html#wigglybond">Wiggly Bond</a></li>
		<li><a href="checkerlist.html#wigglydoublebond">Wiggly Double Bond</a></li>
	</ul>
		<div class="clear"></div>
	
<hr />
      
<h3><a id="links" class="anchor"></a>Links</h3>
<table>
			<tr>
				<td><a href="checkerlist.html">List of available checkers</a></td>
			</tr>
			<tr>
				<td><a href="structurechecker.html"><img src="images/gui/structure_checker_16.png" /> Structure Checker GUI</a></td>
			</tr>
			<tr>
				<td><a href="structurechecker_cline.html"><img src="images/gui/structurechecker_cli16.png" /> <code>structurechecker</code> Command Line Tool</a></td>
			</tr>
			<tr>
				<td><a href="structurechecker_examples.html">Examples of structure checking in various ChemAxon products</a></td>
			</tr>
			<tr>
				<td><a href="../developer/checker.html">Structure Checker Developer Guide</a></td>
			</tr>
		</table>

<hr />	

</body>
</html>