<!DOCTYPE HTML>
<html lang="en">
<head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8">
    <meta name="description" content="List of ChemAxon's Structure Checkers">
	<meta name="author" content="<PERSON><PERSON><PERSON>, <PERSON><PERSON>">
	<meta name="keywords" content="Structure, Checker, actionstring, checker list, fixer, automatic">
	<title>List of available checkers of Structure Checker application</title>
    <link rel="stylesheet" type="text/css" href="../structurechecker.css">
</head>
<body>
<h1><a id="checkerlist" class="anchor"></a>Examples of available checkers and their fixers</h1>
<table>
	<tr>	
		<td><a class="nounderline" href="structurechecker.html"><img src="images/gui/structure_checker_16.png" /> Structure Checker GUI</a></td> 
	</tr>
	<tr>
		<td><a class="nounderline" href="checker.html"><img src="images/gui/check-structure16.png" /> Structure Checker in MarvinSketch</a></td>
	</tr>
	<tr>
		<td><a class="nounderline" href="structurechecker_cline.html"><img src="images/gui/structurechecker_cli16.png" /> <code>structurechecker</code> Command Line Tool</a></td>
	</tr>
</table>
	
<h3><a class="anchor" id="list"></a>Checkers</h3>
	<ul class="floating">
		<li><a href="#abbrevgroup">Abbreviated Group</a></li>
		<li><a href="#absentchiralflag">Absent Chiral Flag</a></li>
		<li><a href="#absolutestereoconfiguration">Absolute Stereo Configuration</a></li>
		<li><a href="#alias">Alias</a></li>
		<li><a href="#aromaticityerror">Aromaticity Error</a></li>
		<li><a href="#atommap">Atom Map</a></li>
		<li><a href="#atomqueryproperty">Atom Query Property</a></li>
		<li><a href="#atomvalue">Atom Value</a></li>
		<li><a href="#atropisomer">Atropisomer</a></li>
		<li><a href="#attacheddata">Attached Data</a></li>
		<li><a href="#bondangle">Bond Angle</a></li>
		<li><a href="#bondlength">Bond Length</a></li>
		<li><a href="#chiralflag">Chiral Flag</a></li>
		<li><a href="#chiralflagerror">Chiral Flag Error</a></li>
	</ul>
	<ul class="floating">			
		<li><a href="#circularrgroupreference">Circular R-group Reference</a></li>
		<li><a href="#coordsystem">Coordination System Error</a></li>
		<li><a href="#covalentcounterion">Covalent Counterion</a></li>
		<li><a href="#crosseddoublebond">Crossed Double-Bond</a></li>
		<li><a href="#empty">Empty Structure</a></li>
		<li><a href="#explicith">Explicit Hydrogen</a></li>
		<li><a href="#explicitlp">Explicit Lone Pairs</a></li>
		<li><a href="#ezdoublebond">E/Z Double Bond</a></li>
		<li><a href="#isotope">Isotope</a></li>
		<li><a href="#metallocene">Metallocene Error</a></li>
		<li><a href="#missingatommap">Missing Atom Map</a></li>
		<li><a href="#missingrgroupreference">Missing R-group</a></li>
		<li><a href="#moleculecharge">Molecule Charge</a></li>
		<li><a href="#multicenter">Multicenter</a></li>
		<li><a href="#multicomponent">Multicomponent</a></li>		
	</ul>
	<ul class="floating">	
		<li><a href="#multiplestereocenter">Multiple Stereocenter</a></li>	
		<li><a href="#ocr">OCR Error</a></li>		
		<li><a href="#overlappingatoms">Overlapping Atoms</a></li>
		<li><a href="#overlappingbonds">Overlapping Bonds</a></li>
		<li><a href="#pseudoatom">Pseudo Atom</a></li>
		<li><a href="#queryatom">Query Atom</a></li>
		<li><a href="#querybond">Query Bond</a></li>
		<li><a href="#ratom">R-atom</a></li>
		<li><a href="#rgroupattachmenterror">R-group Attachment Error</a></li>
		<li><a href="#rgref">R-group Reference Error</a></li>
		<li><a href="#racemate">Racemate</a></li>
		<li><a href="#radical">Radical</a></li>
		<li><a href="#rareelement">Rare Element</a></li>
		<li><a href="#reactionmaperror">Reaction Map Error</a></li>		
		<li><a href="#relativestereo">Relative Stereo</a></li>
	</ul>
	<ul class="floating">
		<li><a href="#ringstrainerror">Ring Strain Error</a></li>
		<li><a href="#solvent">Solvent</a></li>		
		<li><a href="#staratom">Star Atom</a></li>
		<li><a href="#stereocarebox">Stereo Care Box</a></li>
		<li><a href="#straightdoublebond">Straight Double Bond</a></li>
		<li><a href="#substructure">Substructure</a></li>
		<li><a href="#3d">Three Dimension(3D)</a></li>
		<li><a href="#unbalancedreaction">Unbalanced Reaction</a></li>
		<li><a href="#unusedrgroupreference">Unused R-group</a></li>
		<li><a href="#valenceerror">Valence Error</a></li>
		<li><a href="#valenceproperty">Valence Property</a></li>
		<li><a href="#wedgeerror">Wedge Error</a></li>
		<li><a href="#wigglybond">Wiggly Bond</a></li>
		<li><a href="#wigglydoublebond">Wiggly Double Bond</a></li>
	</ul>
	<div class="clear"></div>
	<h3><a class="nounderline" href="#deprecation">Deprecation</a></h3>
	<center><div class="lenia">&nbsp;</div></center>	
	<div>
	<p>Please note that in case of more than one fixer they are listed in a preset logical order of priority. </p>
	</div>
	<dl>
        <dt><a id="abbrevgroup" class="anchor"></a><h4>Abbreviated Group Checker (<code>abbrevgroup</code>)</h4></dt>
        <dd>
            <ul>
                <li>Checker searches for <a href="../sketch/sketch-chem.html#sgroups">abbreviated groups</a> in the structure <a href="#note1"><sup>*</sup></a>. Options: 
				<ul>
					<li>expanded (<code>:expanded=true</code>);</li> 
					<li>contracted (<code>:contracted=true</code>); </li>
					<li>both expanded and contracted groups (default).</li>
				</ul>
				You can specify a list of <strong>excluded</strong> abbreviated groups, i.e., checker will not find (and fix) 
				abbreviated groups that are indicated in this list. Default and custom abbreviations are also possible to set. The excluded list elements are case 
				sensitive and should be separated by commas (,). (<code>:excluded=Ph,Ala,Gly,MyGroup</code>)
				<br><img border="1px" src="images/examples/ex_abbrev_excluded01.png" width="221" height="108" alt="excluded list" />
				</li>
                <li>Fixer offers - in order of priority -:
				<ol>
					<li>ungroup abbreviated groups (<code>ungroup</code>);</li>
					<li>expand abbreviated groups (<code>expandgroup</code>);</li>
					<li>contract abbreviated groups (<code>contractgroup</code>).</li>
				</ol>
				</li>					
            </ul>
			<br>
			<b>Example</b>:
            <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
				<tr>
					<th rowspan="2" width="15%">Abbreviated Group Checker (<code>abbrevgroup</code>)</th>
					<th rowspan="2" width="22%">Warning:</th>			
					<th colspan="3">Fix</th> 
				</tr>
				<tr>
					<th width="21%">Ungroup (<code>ungroup</code>)</th>
					<th width="21%">Expand Group (<code>expandgroup</code>)</th>
					<th width="21%">Contract Group (<code>contractgroup</code>)</th>					
				</tr>
			    <tr>
                    <th align="left">Detect Expanded Groups</th>
                    <td align="center"><img src="images/examples/ex_abbrev_exp_ch.png" width="169" height="117" alt="ex_abbrev_exp_ch"/></td>
					<td align="center"><img src="images/examples/ex_abbrev_exp_f.png" width="159" height="91" alt="ex_abbrev_contr_f"/></td>
					<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="unavailable"/></td>
					<td align="center"><img src="images/examples/ex_abbrev_exp_fcont.png" width="94" height="30" alt="ex_abbrev_contr_f"/></td>
				</tr>
                <tr>
                    <th align="left">Detect Contracted Groups</th>
                    <td align="center"><img src="images/examples/ex_abbrev_contr_ch.png" width="44" height="71" alt="ex_abbrev_contr_f"/></td>
					<td align="center"><img src="images/examples/ex_abbrev_contr_f.png" width="93" height="144" alt="ex_abbrev_contr_f"/></td>
					<td align="center"><img src="images/examples/ex_abbrev_contr_fex.png" width="127" height="168" alt="ex_abbrev_contr_f"/></td>
					<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="unavailable"/></td>					
                </tr>
				<tr>
                    <th align="left">Detect All Groups</th>
                    <td align="center"><img src="images/examples/ex_abbrev_both_ch.png" width="156" height="77" alt="ex_abbrev_both_ch"/></td>
					<td align="center"><img src="images/examples/ex_abbrev_both_f.png" width="150" height="93" alt="ex_abbrev_both_f"/></td>
					<td align="center"><img src="images/examples/ex_abbrev_both_fex.png" width="155" height="101" alt="ex_abbrev_both_fex"/></td>
					<td align="center"><img src="images/examples/ex_abbrev_both_fcont.png" width="108" height="40" alt="ex_abbrev_both_fcontr"/></td>	
                </tr>
				<tr>
                    <th align="left">Detect Contracted Groups with excluded list</th>
                    <td align="center"><img src="images/examples/ex_abbrev_excluded02_ex.png" width="297" height="33" alt="ex_abbrev_exclude_ch"/></td>
					<td align="center"><img src="images/examples/ex_abbrev_excluded02_fung.png" width="272" height="150" alt="ex_abbrev_exclude_f"/></td>
					<td align="center"><img src="images/examples/ex_abbrev_excluded02_fex.png" width="316" height="156" alt="ex_abbrev_exclude_f"/></td>
					<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="ex_abbrev_both_fcontr"/></td>	
                </tr>
            </table>
			<br>
			<a id="note1"></a><sup>*</sup><b>Note</b>: <a href="../sketch/sketch-basic.html#User_defined_abbreviated_groups">
			User defined abbreviated groups</a> are also available to be applied as recognizable abbreviated 
			group set.<br>
			<b>Note</b>: If attachment point is not connected to any atom, 
			ungrouping removes attachment points of the abbreviated group and 
			adds an implicit hydrogen.
		</dd>

		<dt>
		<a href="#list">List of checkers</a>
		<a id="absentchiralflag" class="anchor"></a><h4>Absent Chiral Flag Checker (<code>absentchiralflag</code>)</h4></dt>
        <dd>
            <ul>
                <li>Checker searches for <i>Absolute</i> chiral flag attached to
				the molecule if the molecule is chiral and all stereo centers 
				are precisely marked.
				</li>
                <li>Fixer adds chiral flag (<i>Absolute</i>) to the molecule (<code>addchiralflag</code>).
				</li>					
            </ul>
			<br>
			<b>Example</b>:
            <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
				<tr>
					<th rowspan="2" width="15%">Absent Chiral Flag Checker (<code>absentchiralflag</code>)</th>
					<th rowspan="2" width="22%">Warning:</th>
					<th>Fix</th> 
				</tr>
				<tr>
					<th width="21%">Add Chiral Flag (<code>addchiralflag</code>)</th>
				</tr>
			    <tr>
                    <th align="left">Detect Absent Chiral Flag</th>
                    <td align="center"><img src="images/examples/ex_absentchiralflag_ch.png" width="199" height="97" alt="absent chiral flag checker"/></td>
					<td align="center"><img src="images/examples/ex_absentchiralflag_f.png" width="205" height="106" alt="add chiral flag"/></td>
                </tr>				
            </table>
		</dd>
		
        <dt>
		<a href="#list">List of checkers</a>
		<a id="absolutestereoconfiguration" class="anchor"></a><h4>Absolute Stereo Configuration Checker (<code>absolutestereoconfiguration</code>)</h4></dt>
            <dd>
                <ul>
                    <li>Checker searches for molecules in which all asymmetric centers have absolute stereo configuration.</li>
                    <li>Fixer: no fixer is available for this checker.</li>
                </ul>
                <br>
				<b>Example</b>: 
				<table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
						<tr>
							<th align="left" width="15%">Absolute Stereo Configuration (<code>absolutestereoconfiguration</code>)</th>
							<th>Warning:</th>
							<th>Fix</th> 
						</tr>
						<tr>
                            <th align="left">Detect Absolute Stereo Configuration</th>
                            <td align="center"><img src="images/examples/ex_absolutestereoconfig_ch.png" width="193" height="91" alt="ex_absolutestereoconfig_ch"/></td>
                            <td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no fixer"/></td>
						</tr>                    
                </table>
				
			</dd>
			
			<dt>
		<a href="#list">List of checkers</a>
		<a id="alias" class="anchor"></a><h4>Alias Checker (<code>alias</code>)</h4></dt>
            <dd>
                <ul>
                    <li>Checker searches for <b>aliases</b> in the structure.</li>
                    <li>Fixer offers:
						<ol>
							<li>converting alias to abbreviated group if the
							alias value corresponds to a group in the 
							abbreviated group list <a href="#note4"><sup>*</sup></a> (<code>aliastogroup</code>);</li>
							<li>converting alias to atom if the alias value 
							corresponds to an element (e.g., Ar) (<code>aliastoatom</code>); </li>							
							<li>removing alias (revealing the atom under the 
							alias) <a href="#note5"><sup>**</sup></a> (<code>removealias</code>); </li>
							<li>deleting the atom having alias information (<code>removeatom</code>).</li>
						</ol>
					</li>
                </ul>
                <br>
				<b>Example</b>: 
				<table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
						<tr>
							<th align="left" rowspan="2" width="15%">Alias Checker (<code>alias</code>)</th>
							<th rowspan="2" width="20">Warning:</th>
							<th colspan="4">Fix</th> 
						</tr>
						<tr>
							<th width="20">Convert Alias to Group (<code>aliastogroup</code>)</th>
							<th width="20">Convert to Atom (<code>aliastoatom</code>)</th>
							<th width="20">Remove Alias (<code>removealias</code>)</th>
							<th width="20">Delete Atom (<code>removeatom</code>)</th>
						</tr>
                        <tr>
                            <th align="left">Detect Alias</th>
                            <td align="center"><img src="images/examples/ex_alias_ch.png" width="131" height="86" alt="ex_alias_ch"/></td>
							<td align="center"><img src="images/examples/ex_alias_fcong.png" width="131" height="81" alt="convert ot group"/></td>
							<td align="center"><img src="images/examples/ex_alias_fcona.png" width="126" height="86" alt="convert to atom"/></td>
							<td align="center"><img src="images/examples/ex_alias_frem.png" width="121" height="76" alt="ex_alias_frem"/></td>
							<td align="center"><img src="images/examples/ex_alias_fdel.png" width="85" height="37" alt="ex_alias_fdel"/></td>
                        </tr>                    
                </table>
				<br>				          
                <b>Note</b>: "OMe" and "CL" were defined as alias. After 
				converting "OMe" to methoxy group, the abbreviated group is 
				linked correctly to the chain, i.e., bond through the oxygen atom.
				<br>
				<a id="note4"></a><sup>*</sup><b>Note</b>: <a href="../sketch/sketch-basic.html#User_defined_abbreviated_groups">User defined 
				abbreviated groups</a> are also available to be applied as recognizable abbreviated group set.
                <br>
				<a id="note5"></a><sup>**</sup><b>Note</b>: Alias only covers the atom, and fixer removes this alias 
				from the original atom.
				
			</dd>
			
            <dt>
			<a href="#checkerlist">List of checkers</a>
			<a id="aromaticityerror" class="anchor"></a><h4>Aromaticity Error Checker (<code>aromaticityerror</code>)</h4></dt>
            <dd>
                <ul>
                    <li>Checker searches for <b>General</b>, <b>Basic</b>, or 
					<b>Loose</b> aromatic systems that can't be dearomatized or 
					rearomatizing with the given method does not reproduce the 
					initial molecule. <a href="../sci/aromatization-doc.html">
					Details on aromaticity detection.</a></li>
                    <li>Fixer offers:
						<ol>
							<li>rearomatizing (<code>rearomatize</code>);</li>
							<li>dearomatizing the aromatic system (<code>dearomatize</code>).</li>
						</ol>
					</li>
				</ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
						<tr>
							<th align="left" rowspan="2" width="15%">Aromaticity
							Error checker (<code>aromaticity</code>)</th>
							<th rowspan="2">Warning:</th>
							<th colspan="2">Fix</th> 
						</tr>
						<tr>
							<th>Rearomatize (<code>rearomatize</code>)</th>
							<th>Dearomatize (<code>dearomatize</code>)</th>
						</tr>
					    <tr>
                            <th align="left" rowspan="2">General</th>
                            <td align="center"><img src="images/examples/ex_arom_chg.png" width="103" height="144" alt="ex_arom_ch"/></td>
							<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no"/></td>
							<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no"/></td>
						</tr>
						<tr>
							<td align="center"><img src="images/examples/ex_arom_chb2.png" width="166" height="102" alt="ex_arom_ch"/></td>
							<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no"/></td>
							<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no"/></td>
						</tr>
						<tr>
                            <th align="left" rowspan="2">Basic</th>
                            <td align="center"><img src="images/examples/ex_arom_ch.png" width="109" height="148" alt="ex_arom_ch"/></td>
							<td align="center"><img src="images/examples/ex_arom_f.png" width="135" height="180" alt="ex_arom_f"/></td>
							<td align="center"><img src="images/examples/ex_arom_f.png" width="135" height="180" alt="ex_arom_f"/></td>
						</tr>
						<tr>
							<td align="center"><img src="images/examples/ex_arom_chb2.png" width="166" height="102" alt="ex_arom_ch"/></td>
							<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no"/></td>
							<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no"/></td>
						</tr>
						<tr>
                            <th align="left" rowspan="2">Loose</th>
                            <td align="center"><img src="images/examples/ex_arom_ch.png" width="109" height="148" alt="ex_arom_ch"/></td>
							<td align="center"><img src="images/examples/ex_arom_f.png" width="135" height="180" alt="ex_arom_f"/></td>
							<td align="center"><img src="images/examples/ex_arom_f.png" width="135" height="180" alt="ex_arom_f"/></td>
						</tr>
						<tr>
							<td align="center"><img src="images/examples/ex_arom_chl.png" width="164" height="101" alt="ex_arom_ch"/></td>
							<td align="center"><img src="images/examples/ex_arom_flrea.png" width="169" height="105" alt="ex_arom_f"/></td>
							<td align="center"><img src="images/examples/ex_arom_fldea.png" width="166" height="104" alt="ex_arom_f"/></td>
						</tr>
                </table>
				<br><b>Note</b>: The 2-pyridone aromatized in general mode is 
				corrected after a checking of aromatization in basic mode.<br> 
				In this case, both rearomatization and dearomatization fix 
				yields the same structure.
            </dd>

            <dt>
			<a href="#list">List of checkers</a>
			<a id="atommap" class="anchor"></a><h4>Atom Map Checker (<code>atommap</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for atom maps.</li>
                    <li>Fixer offers removing atom maps (<code>removeatommap</code>).</li>
                </ul>
                <br><b>Example</b>:
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					
						<tr>
							<th align="left" rowspan="2" width="15%">Atom Map 
							Checker (<code>atommap</code>)</th>
							<th rowspan="2">Warning:</th>
							<th>Fix</th> 
						</tr>
						<tr>
							<th>Remove Atom Map (<code>removeatommap</code>)</th>
						</tr>
					
                        <tr>
                            <th align="left">Detect Atom Map</th>
                            <td align="center"><img src="images/examples/ex_atommap_ch.png" width="173" height="90" alt="ex_atommap_ch"/></td>
							<td align="center"><img src="images/examples/ex_atommap_f.png" width="150" height="82" alt="ex_atommap_f"/></td>
						</tr>                  
                </table>				
            </dd>
			
			<dt>
			<a href="#list">List of checkers</a>
			<a id="atomqueryproperty"></a><h4>Atom Query Property Checker (<code>atomqueryproperty</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for atoms having query properties. Tick
					options for searching:
                        <ul>
                            <li>Hydrogen Count (<code>:H=true</code>);</li>
                            <li>Connection Count (<code>:X=true</code>);</li>
                            <li>Ring Bond Count (<code>:rb=true</code>);</li>
                            <li>Ring Count (<code>:R=true</code>);</li>
                            <li>Implicit Hydrogen Count (<code>:h=true</code>);</li>
                            <li>Smallest Ring Size (<code>:r=true</code>);</li>
							<li>Aromaticity (<code>:a=true</code>);</li>
							<li>Substitution Count (<code>:s=true</code>);</li>
							<li>Unsaturation (<code>:u=true</code>);</li>
							<li>Explicit Connection Count (<code>:D=true</code>)</li>
                        </ul> atom query properties.
					</li> 
                    <li>Fixer offers removing the identified query properties (<code>removeatomqueryproperty</code>).</li>
                </ul>
				<br>
                <b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" rowspan="2" width="15%">Atom Query 
						Property Checker (<code>atomqueryproperty</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Removing query property (<code>removeatomqueryproperty</code>)</th>
					</tr>
				    <tr>
                        <th align="left">Detect Atom Query Property</th>
                        <td align="center"><img src="images/examples/ex_atomqp_ch.png" width="258" height="176" alt="example atom query property"/></td>
						<td align="center"><img src="images/examples/ex_atomqp_f.png" width="235" height="162" alt="fixed atom query property"/></td>
					</tr>
                </table>			
			</dd>
			            
			<dt>
			<a href="#list">List of checkers</a>
			<a id="atomvalue" class="anchor"></a><h4>Atom Value Checker (<code>atomvalue</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for atom values.</li>
                    <li>Fixer offers:
						<ol>
							<li>removing set atom values (<code>removeatomvalue</code>);</li> 
							<li>deleting the atom with value (<code>removeatom</code>).</li>
						</ol>
					</li>
                </ul>
				<br>
                <b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" rowspan="2" width="15%">Atom Value 
						Checker (<code>atomvalue</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th colspan="2">Fix</th> 
					</tr>
					<tr>
						<th>Remove Atom Value (<code>removeatomvalue</code>)</th>
						<th>Delete Atom (<code>removeatom</code>)</th>
					</tr>
					<tr>
                        <th align="left">Detect Atom Value</th>
                        <td align="center"><img src="images/examples/ex_atomvalue_ch.png" width="273" height="101" alt="ex_atomvalue_ch"/></td>
						<td align="center"><img src="images/examples/ex_atomvalue_frem.png" width="211" height="43" alt="ex_atomvalue_frem"/></td>
						<td align="center"><img src="images/examples/ex_atomvalue_f.png" width="196" height="86" alt="ex_atomvalue_f"/></td>
					</tr>
                </table>				
            </dd>
			
			<dt>
			<a href="#list">List of checkers</a>
			<a id="atropisomer" class="anchor"></a><h4>Atropisomer Checker (<code>atropisomer</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for stereoisomers containing two aromatic
					rings connected by a single bond with
					at least three ortho ligands. See details in <a href="../developer/core/stereochemistry/CIPStereoChemistry.html#atropisomer">documentation</a>.</li>
                    <li>Fixer: no fixer is available for this checker.</li>
                </ul>
				<br>
                <b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" width="15%">Atropisomer Checker (<code>atropisomer</code>)</th>
						<th>Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
                        <th align="left">Detect Atropisomer</th>
                        <td align="center"><img src="images/examples/ex_atropisomer_ch.png" width="220" height="230" alt="ex_atropisomer_ch"/></td>
						<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no fixer available"/></td>
					</tr>
                </table>				
            </dd>
			
            <dt>
			<a href="#list">List of checkers</a>
			<a id="attacheddata" class="anchor"></a><h4>Attached Data Checker (<code>attacheddata</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for attached data.</li>
                    <li>Fixer offers:
						<ol>
							<li>removing attached data (<code>removeattacheddata</code>);</li>
							<li>deleting the atom having attached data (<code>removeatom</code>).</li>
						</ol>
					</li>					
                </ul>
				<br>
                <b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" rowspan="2" width="15%">Attached Data Checker (<code>attacheddata</code>)</th>
						<th rowspan="2" width="25%">Warning:</th>							
						<th colspan="2">Fix</th> 
					</tr>
					<tr>
						<th width="30%">Remove Attached Data (<code>removeattacheddata</code>)</th>
						<th width="30%">Delete Atom (<code>removeatom</code>)</th>
					</tr>
					<tr>
                        <th align="left">Detect Attached Data</th>
                        <td align="center"><img src="images/examples/ex_attached_ch.png" width="288" height="72" alt="ex_attached_ch"/></td>
						<td align="center"><img src="images/examples/ex_attached_f.png" width="212" height="103" alt="ex_attached_f"/></td>
						<td align="center"><img src="images/examples/ex_attached_fdel.png" width="125" height="44" alt="ex_attached_fdel"/></td>
					</tr>
                </table>
            </dd>
			
            <dt>
			<a href="#list">List of checkers</a>
			<a id="bondangle" class="anchor"></a><h4>Bond Angle Checker (<code>bondangle</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for bonds NOT matching the following rules:
                        <table border="0">
                            <tr>
                                <td><ul><li>Triple bond angle 180&deg; (&plusmn; 0.5 rad):</li></ul></td>
                                <td><img src="images/examples/check_angle1.png" width="90" height="24" alt="check_angle1"/></td>
                            </tr>
                            <tr>
                                <td><ul><li>Double bond angle 120&deg; or 180&deg; (&plusmn; 0.5 rad):</li></ul></td>
                                <td><img src="images/examples/check_angle2.png" width="250" height="55" alt="check_angle2"/></td>
                            </tr>
                            <tr>
                                <td><ul><li>sp<sup>2</sup> atom's bond angles 120&deg;:</li></ul></td>
                                <td><img src="images/examples/check_angle3.png" width="57" height="51" alt="check_angle3"/>
                                    </td>
                            </tr>
                            <tr>
                                <td width="300"><ul><li>sp<sup>3</sup> atom's bond angles 90&deg;<br> <b>or</b> 3 bond angles 120&deg;, 4th bond of an angle of n times 15&deg;;<br> <b>or</b> a bond angle of 120&deg; and two adjacent angles equal:</li></ul></td>
                                <td><img src="images/examples/check_angle4.png" width="132" height="191" alt="check_angle4"/>
                                   </td>
                            </tr>
                        </table>
					</li>
                    <li>Fixer offers cleaning the structure by:
						<ol>
							<li>2D clean or (<code>clean</code>);</li> 
							<li>partial clean (<code>partialclean</code>).</li>
						</ol>
					</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					
						<tr>
							<th align="left" rowspan="2" width="15%">Bond Angle Checker (<code>bondangle</code>)</th>
							<th rowspan="2">Warning:</th>							
							<th>Fix</th> 
						</tr>
						<tr>
							<th>Clean (<code>clean</code>)</th>
						</tr>
					
					
                        <tr>
                            <th align="left">Detect Bond Angle errors</th>
                            <td align="center"><img src="images/examples/ex_bondangle_ch1.png" width="286" height="113" alt="ex_bondangle_ch"/></td>
							<td align="center"><img src="images/examples/ex_bondangle_f1.png" width="256" height="128" alt="ex_bondangle_f"/></td>
						</tr>
                    
                </table><br>
				<b>Note</b>: The current version of bond angle checker examines chain bonds of 2D molecules.
                Envelope-shaped cyclic compounds are considered as error. Partial clean is not available.
            </dd>
			
            <dt>
			<a href="#list">List of checkers</a>
			<a id="bondlength" class="anchor"></a><h4>Bond Length Checker (<code>bondlength</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for bond lengths different than the default setting.</li>
                    <li>Fixer offers cleaning the structure by:
						<ol>
							<li>2D clean or (<code>clean</code>);</li> 
							<li>partial clean (<code>partialclean</code>).</li>
						</ol>
					</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
                    <tr>
						<th align="left" rowspan="2" width="15%">Bond Length Checker (<code>bondlength</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Clean (<code>clean</code>)</th>
					</tr>
					<tr>
                        <th align="left">Detect short bonds</th>
                        <td align="center"><img src="images/examples/ex_bondlength_chsh.png" width="160" height="122" alt="ex_bondlength_ch"/></td>
                        <td align="center"><img src="images/examples/ex_bondlength_fb.png" width="152" height="122" alt="ex_bondlength_f"/></td>
					</tr>
					<tr>
                        <th align="left">Detect long bonds</th>
                        <td align="center"><img src="images/examples/ex_bondlength_chl.png" width="163" height="123" alt="ex_bondlength_ch"/></td>
                        <td align="center"><img src="images/examples/ex_bondlength_fb.png" width="152" height="122" alt="ex_bondlength_f"/></td>
					</tr>
					<tr>
                        <th align="left">Detect both bonds</th>
                        <td align="center"><img src="images/examples/ex_bondlength_chb.png" width="160" height="121" alt="ex_bondlength_ch"/></td>
                        <td align="center"><img src="images/examples/ex_bondlength_fb.png" width="152" height="122" alt="ex_bondlength_f"/></td>
					</tr>                  
                </table><br>
                <b>Note</b>: The current version of bond length checker examines chain bonds of 2D molecules. Partial clean is not available.
            </dd>
			
			<dt>
			<a href="#list">List of checkers</a>
			<a id="chiralflag" class="anchor"></a><h4>Chiral Flag Checker (<code>chiralflag</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for set chiral flags.</li>
                    <li>Fixer offers removing the chiral flag (<code>removechiralflag</code>).</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" rowspan="2" width="15%">Chiral Flag Checker (<code>chiralflag</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Fix Chiral Flag (<code>removechiralflag</code>)</th>
					</tr>
					<tr>
						<th align="left">Detect set chiral flag</th>
						<td align="center"><img src="images/examples/ex_chiralflag_ch.png" width="105" height="101" alt="ex_chiralflag_ch01"/></td>
						<td align="center"><img src="images/examples/ex_chiralflag_f01.png" width="102" height="82" alt="ex_chiralflag_f"/></td>
                    </tr> 
					<tr>
						<th align="left">Detect set chiral flag</th>
						<td align="center"><img src="images/examples/ex_chiralflag_ch02.png" width="203" height="106" alt="ex_chiralflag_ch02"/></td>
						<td align="center"><img src="images/examples/ex_chiralflag_f02.png" width="203" height="106" alt="ex_chiralflag_f"/></td>
                    </tr>               
                </table><br>
                <b>Note</b>: Checker will find correctly AND incorrectly set chiral flags. Incorrectly 
				set chiral flags will be find by <a href="#chiralflagerror">Chiral Flag Error Checker</a>.
			</dd>
			
			<dt>
			<a href="#list">List of checkers</a>
			<a id="chiralflagerror" class="anchor"></a><h4>Chiral Flag Error Checker (<code>chiralflagerror</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for incorrectly set chiral flags: "Absolute" flag can only be set when all chirality information is specified for the given molecule.</li>
                    <li><a id="removeinvalidchiralflag"></a>Fixer offers removing the chiral flag (<code>removeinvalidchiralflag</code>).</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" rowspan="2" width="15%">Chiral Flag Error Checker (<code>chiralflagerror</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Fix Chiral Flag (<code>removeinvalidchiralflag</code>)</th>
					</tr>
					<tr>
						<th align="left">Detect invalid chiral flag</th>
						<td align="center"><img src="images/examples/ex_chiralflag_ch01.png" width="108" height="105" alt="ex_chiralflag_ch"/></td>
						<td align="center"><img src="images/examples/ex_chiralflag_f01.png" width="102" height="82" alt="ex_chiralflag_f"/></td>
                    </tr>               
                </table>
			</dd>
			
			<dt>
			<a href="#list">List of checkers</a>
			<a id="circularrgroupreference"></a><h4>Circular R-group Reference Checker (<code>circularrgroupreference</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for series of R-group references where the R-groups refer to each other resulting in a closed loop.</li>
                    <li>Fixer: no fixer is available for this checker.</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" width="15%">Circular R-group Reference Checker (<code>circularrgroupreference</code>)</th>
						<th>Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th align="left">Detect circular R-group references</th>
						<td align="center"><img src="images/examples/ex_circrg_ch.png" width="305" height="229" alt="ex_chiralflag_ch"/></td>
						<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no fixer"/></td>
                    </tr>               
                </table>
			</dd>
            
			<dt>
			<a href="#list">List of checkers</a>
			<a id="coordsystem"></a><h4>Coordination System Error Checker (<code>coordsystem</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for errors in the use of coordinative bonds:
                        <ul><li>2 multicenters connected with a bond;</li>
                            <li>coordinated atom is connected to an atom of the same multicenter with a coordinative bond;</li>
                            <li>multicenter merged with one of its atoms.</li>
                        </ul>
					</li>
                    <li>Fixer offers deleting the wrong bond (<code>removebond</code>).</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" rowspan="2" width="15%">Coordination System Error Checker (<code>coordsystem</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Delete bond (<code>removebond</code>)</th>
					</tr>	
                    <tr>
                        <th align="left">Detect coordinative bond</th>
                        <td align="center"><img src="images/examples/ex_coord_ch.png" width="111" height="179" alt="ex_coord_ch"/></td>
						<td align="center"><img src="images/examples/ex_coord_f.png" width="105" height="187" alt="ex_coord_f"/></td>
                    </tr>    
					<tr>
                        <th align="left">Detect coordinative bond</th>
                        <td align="center"><img src="images/examples/ex_coord_ch02.png" width="78" height="85" alt="ex_coord_ch"/></td>
						<td align="center"><img src="images/examples/ex_coord_f02.png" width="79" height="80" alt="ex_coord_f"/></td>
                    </tr> 
                </table>
			</dd>
			
            <dt>
			<a href="#list">List of checkers</a>
			<a id="covalentcounterion" class="anchor"></a><h4>Covalent Counterion Checker (<code>covalentcounterion</code>)</h4></dt>
            <dd>	
				<ul>
                    <li>Checker searches for covalently bound counter ions.</li>
                    <li>Fixer offers changing the structure to the ionic form (<code>converttoionicform</code>).</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" rowspan="2" width="15%">Covalent Counterion Checker (<code>covalentcounterion</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Convert to Ionic Form (<code>converttoionicform</code>)</th>
					</tr>
                    <tr>
                        <th align="left">Detect Covalent Counterion</th>
                        <td align="center"><img src="images/examples/ex_covcounter_ch.png" width="198" height="129" alt="ex_covcounter_ch"/></td>
						<td align="center"><img src="images/examples/ex_covcounter_f.png" width="191" height="131" alt="ex_covcounter_f"/></td>
                    </tr>
                </table>
			</dd>

            <dt>
			<a href="#list">List of checkers</a>
			<a id="crosseddoublebond" class="anchor"></a><h4>Crossed Double-Bond Checker (<code>crosseddoublebond</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for crossed double bond types (molecules with unknown or unspecified configuration).</li>
                    <li><a id="converttowigglydoublebond"></a>Fixer offers changing crossed double bond to wiggly double bond type (<code>converttowigglydoublebond</code>).</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" rowspan="2" width="15%">Crossed Double Bond Checker (<code>crosseddoublebond</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Convert to Wiggly Double Bond (<code>converttowigglydoublebond</code>)</th>
					</tr>
                    <tr>
                        <th align="left">Detect crossed double bond</th>
                        <td align="center"><img src="images/examples/ex_crossed_ch.png" width="129" height="106" alt="ex_crossed_ch"/></td>
						<td align="center"><img src="images/examples/ex_crossed_f.png" width="126" height="108" alt="ex_crossed_f"/></td>
                    </tr>
                </table>
				<br>
				<b>Note</b>: Reverse action is: <a href="#wigglydouble">Wiggly Double Bond Checker</a>.
			</dd>
             
            <dt>
			<a href="#list">List of checkers</a>
			<a id="empty" class="anchor"></a><h4>Empty Structure Checker (<code>empty</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for empty structure fields (available in multiple structures files).</li>
                </ul>            
			</dd>
			
            <dt>
			<a href="#list">List of checkers</a>
			<a id="explicith" class="anchor"></a><h4>Explicit Hydrogen Checker (<code>explicith</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for explicit hydrogens. Tick options for searching:
                        <ul>                       
							<li><b>Lonely</b>: hydrogen atom without connection (<code>:lonely=true</code>)</li>
							<li><b>Isotopic</b>: hydrogen isotopes (<code>:isotopic=true</code>)</li>
							<li><b>Charged</b>: charged hydrogen atoms (<code>:charged=true</code>)</li>
							<li><b>Radical</b>: hydrogen radicals (<code>:radical=true</code>)</li>										                            
							<li><b>Mapped</b>: mapped hydrogens (<code>:mapped=true</code>)</li>
							<li><b>Wedged</b>: hydrogen connecting with wedged bond (<code>:wedged=true</code>)</li>
							<li><b>H Connected</b>: hydrogen connected to a hydrogen atom (<code>:hconnected=true</code>)</li>
							<li><b>Polymer End Group</b>: hydrogen connected to a SRU S-group (<code>:polymerendgroup=true</code>)</li>													                            
							<li><b>S-group End</b>: hydrogen connected to a Superatom S-group (<code>:sgroupend=true</code>)</li>
							<li><b>S-group</b>: hydrogen which is the only atom in a S-group (<code>:sgroup=true</code>)</li>
							<li><b>Valence Error</b>: hydrogen connected to an atom which has valence error (<code>:valenceerror=true</code>)</li>
							<li><b>Bridgehead</b>: hydrogen connected to a bridgehead atom (<code>:bridgehead=true</code>)</li>
						</ul>                         
						explicit hydrogens.
					</li>
                    <li>Fixer offers removing explicit hydrogens and the selected special atoms (<code>removeexplicith</code>).</li>
                </ul>
				<br>
                <b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" rowspan="2" width="15%">Explicit Hydrogen Checker </a> (<code>explicith</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>	
						<th>Remove Explicit Hydrogen </a> (<code>removeexplicith</code>)</th>
					</tr>
                    <tr>
                        <th align="left">Detect Explicit Hydrogen</th>
                        <td align="center"><img src="images/examples/ex_explh_ch.png" width="151" height="172" alt="ex_explh_ch"/></td>
						<td align="center"><img src="images/examples/ex_explh_f.png" width="86" height="85" alt="ex_explh_f"/></td>
                    </tr>
                </table>
			</dd>

            <dt>
			<a href="#list">List of checkers</a>
			<a id="explicitlp" class="anchor"></a><h4>Explicit Lone Pairs Checker (<code>explicitlp</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for explicitly drawn lone pairs (inserted as Special node from the Advanced tab of the Periodic Table).</li>
                    <li>Fixer offers removing the explicit lone pair (<code>removeatom</code>).</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
						<tr>
							<th align="left" rowspan="2" width="15%">Explicit Lone Pairs checker (<code>explicitlp</code>)</th>
							<th rowspan="2">Warning:</th>							
							<th>Fix</th> 
						</tr>
						<tr>	
							<th>Delete Atom (<code>removeatom</code>)</th>
						</tr>
                        <tr>
                            <th align="left">Detect Explicit Lone Pair</th>
                            <td align="center"><img src="images/examples/ex_expllp_ch.png" width="218" height="162" alt="ex_expllp_ch"/></td>
							<td align="center"><img src="images/examples/ex_expllp_f.png" width="210" height="137" alt="ex_expllp_f"/></td>
                        </tr>
                </table>
			</dd> 
			
			<dt>
			<a href="#list">List of checkers</a>
			<a id="ezdoublebond" class="anchor"></a><h4>E/Z Double Bond Checker (<code>ezdoublebond</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for marked <em>E</em> or <em>Z</em> double bond configuraion.</li>
                    <li>Fixer: no fixer is available for this checker.</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
						<tr>
							<th align="left" width="15%">E/Z Double Bond checker (<code>ezdoublebond</code>)</th>
							<th>Warning:</th>							
							<th>Fix</th> 
						</tr>
						
                        <tr>
                            <th align="left">Detect E/Z Double Bond</th>
                            <td align="center"><img src="images/examples/ex_ezdb_ch.png" width="239" height="135" alt="ex_ezdb_ch"/></td>
							<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no fixer"/></td>
                        </tr>
						
						<tr>
                            <th align="left">Detect E/Z Double Bond</th>
                            <td align="center"><img src="images/examples/ex_ezdb_ch02.png" width="174" height="127" alt="ex_ezdb_ch"/></td>
							<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no fixer"/></td>
                        </tr>
                </table>
			</dd>
			
            <dt>
			<a href="#list">List of checkers</a>
			<a id="isotope" class="anchor"></a><h4>Isotope Checker (<code>isotope</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for isotopic atoms.</li>
                    <li>Fixer offers changing the isotopic atom to the most abundant natural isotope of that element (<code>converttoelementalform</code>).</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
                    <tr>
						<th align="left" rowspan="2" width="15%">Isotope Checker (<code>isotope</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Convert to Element (<code>converttoelementalform</code>)</th>
					</tr>
                    <tr>
                        <th align="left">Detect Isotopic Atom</th>
                        <td align="center"><img src="images/examples/ex_isotope_ch.png" width="132" height="229" alt="ex_isotope_ch"/></td>
						<td align="center"><img src="images/examples/ex_isotope_f.png" width="111" height="215" alt="ex_isotope_f"/></td>
                    </tr>
                </table>
				<br>
				<b>Note</b>: Deuterium and tritium are recognized as symbols <b>D</b> and <b>T</b>, respectively as well.
			</dd>
			
            <dt>
			<a href="#list">List of checkers</a>
			<a id="metallocene" class="anchor"></a><h4>Metallocene Error Checker (<code>metallocene</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for incorrect metallocene representations.</li> 
                    <li>Fixer offers converting to a valid structure: aromatizing the rings, inserting coordination bonds, and fixing charge values (<code>fixmetallocene</code>).</li>
                </ul>
				<br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" rowspan="2" width="15%">Metallocene Error Checker (<code>metallocene</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Fix Multiple Metallocenes (<code>fixmetallocene</code>)</th>
					</tr>
		            <tr>
                        <th align="left">Detect Metallcoene</th>
                        <td align="center"><img src="images/examples/ex_metallocene_ch.png" width="306" height="166" alt="ex_metallocene_ch"/></td>
						<td align="center"><img src="images/examples/ex_metallocene_f.png" width="291" height="149" alt="ex_metallocene_f"/></td>
                    </tr>
                </table>
			</dd>


            <dt>
			<a href="#list">List of checkers</a>
			<a id="missingatommap" class="anchor"></a><h4>Missing Atom Map Checker (<code>missingatommap</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for missing atom maps.</li>
                    <li>Fixer offers mapping all atoms in the sketch (<code>mapmolecule</code>).</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" rowspan="2" width="15%">Missing Atom Map Checker (<code>missingatommap</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Map Atoms (<code>mapmolecule</code>)</th>
					</tr>
                    <tr>
                        <th align="left">Detect atoms without atom maps</th>
                        <td align="center"><img src="images/examples/ex_missingmap_ch.png" width="132" height="129" alt="ex_missingatommap_ch"/></td>
						<td align="center"><img src="images/examples/ex_missingmap_f.png" width="129" height="120" alt="ex_missingatommap_f"/></td>
                    </tr>
                </table>
			</dd>
			
			<dt>
			<a href="#list">List of checkers</a>
			<a id="missingrgroupreference" class="anchor"></a><h4>Missing R-group Checker (<code>missingrgroup</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for undefined R-groups.</li>
                    <li>Fixer: no fixer is available for this checker.</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" width="15%">Missing R-group Reference Checker (<code>missingrgroup</code>)</th>
						<th>Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
                        <th align="left">Detect undefined R-groups</th>
                        <td align="center"><img src="images/examples/ex_missingrg_ch.png" width="306" height="311" alt="ex_missingrgroup_ch"/></td>
						<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no fixer"/></td>
                    </tr>
                </table>
			</dd>

            <dt>
			<a href="#list">List of checkers</a>
			<a id="moleculecharge" class="anchor"></a><h4>Molecule Charge Checker (<code>moleculecharge</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches molecules in which total charge is not zero.</li>
                    <li>Fixer offers removing the charge by adding or removing hydrogens (<code>neutralize</code>).</li>
                </ul>
                <br><b>Example</b>: <!--removing charge along with adding a proton.-->
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" rowspan="2" width="15%">Molecule Charge Checker (<code>moleculecharge</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Neutralize (<code>neutralize</code>)</th>
					</tr>
                    <tr>
                        <th align="left">Detect Charged Molecule</th>
                        <td align="center"><img src="images/examples/ex_charge_ch.png" width="124" height="169" alt="ex_molcharge_ch"/></td>
						<td align="center"><img src="images/examples/ex_charge_f.png" width="116" height="168" alt="ex_molcharge_f"/></td>
                    </tr>
                </table>
				<br>
				<b>Note</b>: Charge is not checked in reactions. Charged molecules where no hydrogens can be removed are to be corrected manually.
			</dd>

            <dt>
			<a href="#list">List of checkers</a>
			<a id="multicenter" class="anchor"></a><h4>Multicenter Checker (<code>multicenter</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for multicenter drawing.</li>
                    <li>Fixer offers removing the multicenter from the molecule (<code>removeatom</code>).</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" rowspan="2" width="15%">Multicenter Checker (<code>multicenter</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Delete Atom (<code>removeatom</code>)</th>
					</tr>	
                    <tr>
                        <th align="left">Detect Multicenter drawing</th>
                        <td align="center"><img src="images/examples/ex_multicenter_ch.png" width="110" height="114" alt="ex_multicenter_ch"/></td>
						<td align="center"><img src="images/examples/ex_multicenter_f.png" width="108" height="115" alt="ex_multicenter_f"/></td>
                    </tr>                    
                </table>
			</dd>

            <dt>
			<a href="#list">List of checkers</a>
			<a id="multicomponent" class="anchor"></a><h4>Multicomponent Checker (<code>multicomponent</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for multiple components in the drawing.</li>
                    <li>Fixer: no fixer is available for this checker.</li>
                </ul>
				<br>
				<b>Example</b>:
				<table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" width="15%">Multicomponent Checker (<code>multicomponent</code>)</th>
						<th>Warning:</th>							
						<th width="15%">Fix</th> 
					</tr>
                    <tr>
                        <th align="left">Detect disconnected fragments</th>
                        <td align="center"><img src="images/examples/ex_multicomponent_ch.png" width="195" height="141" alt="ex_multicenter_ch"/></td>
						<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no fixer"/></td>
                    </tr>
                </table>
            </dd>
			
		<dt>
			<a href="#list">List of checkers</a>
			<a id="multiplestereocenter" class="anchor"></a><h4>Multiple Stereocenter Checker (<code>multiplestereocenter</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for molecules having multiple stereocenters.</li>
                    <li>Fixer: no fixer is available for this checker.</li>
                </ul>
				<br>
				<b>Example</b>:
				<table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" width="15%">Multiple Stereocenter Checker (<code>multiplestereocenter</code>)</th>
						<th>Warning:</th>							
						<th width="15%">Fix</th> 
					</tr>
                    <tr>
                        <th align="left">Detect Multiple Stereocenter</th>
                        <td align="center"><img src="images/examples/ex_multiplestereocenter_ch.png" width="142" height="166" alt="ex_multicenter_ch"/></td>
						<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no fixer"/></td>
                    </tr>
                </table>
            </dd>

            <dt>
			<a href="#list">List of checkers</a>
			<a id="ocr" class="anchor"></a><h4>OCR Error Checker (<code>ocr</code>)</h4></dt>
            <dd>
				<ul>
					<li>Checker searches for structures imported incorrectly from optical character recognition procedure.</li>
					<li>Fixer: no fixer is available for this checker.</li>
				</ul>
				<br>
				<b>Example</b>:
				<table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
                    <tr>
						<th align="left" width="15%">OCR Error (<code>ocr</code>)</th>
						<th>Warning:</th>							
						<th width="15%">Fix</th> 
					</tr>
                    <tr>
						<th align="left">Detecting OCR errors</th>
                        <td align="center"><img src="images/examples/ex_ocr_ch.png" width="201" height="186" alt="ex_ocr_ch"/></td>
						<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no fixer"/></td>
                    </tr>                    
                </table>
			</dd>
			
            <dt>
			<a href="#list">List of checkers</a>
			<a id="overlappingatoms" class="anchor"></a><h4>Overlapping Atoms Checker (<code>overlappingAtoms</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for overlapping atoms, i.e., atoms closer than the pre-set percentage of the default atom radius.</li>
                    <li>Fixer offers cleaning the structures by 2D clean (<code>clean</code>).</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
                	<tr>
						<th align="left" rowspan="2" width="15%">Overlapping Atoms Checker (<code>overlappingAtoms</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Clean (<code>clean</code>)</th>
					</tr>
					<tr>
                        <th align="left">Detect Overlapping Atoms</th>
                        <td align="center"><img src="images/examples/ex_overlappingatoms_ch.png" width="158" height="72" alt="ex_overlappingatoms_ch"/></td>
						<td align="center"><img src="images/examples/ex_overlappingatoms_f.png" width="160" height="86" alt="ex_overlappingatoms_f"/></td>
                    </tr>
                </table>
            </dd>

            <dt>
			<a href="#list">List of checkers</a>
			<a id="overlappingbonds" class="anchor"></a><h4>Overlapping Bonds Checker (<code>overlappingBonds</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for overlapping bonds: bonds crossing each other or bonds closer to each other than the set minimal value.</li>
                    <li>Fixer offers cleaning the structure by:
						<ol>
							<li>2D clean or (<code>clean</code>);</li> 
							<li>partial clean (<code>partialclean</code>).</li>
						</ol>
					</li>
                </ul>
                <br><b>Example</b>:
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" rowspan="2" width="15%">Overlapping Bonds Checker (<code>overlappingBonds</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Clean (<code>clean</code>)</th>
					</tr>
					<tr>
                        <th align="left">Detect Overlapping Bonds</th>
                        <td align="center"><img src="images/examples/ex_overlappingbonds_ch.png" width="212" height="100" alt="ex_overlappingbonds_ch"/></td>
						<td align="center"><img src="images/examples/ex_overlappingbonds_f.png" width="221" height="92" alt="ex_overlappingbonds_f"/></td>
                    </tr>
                </table>
				<br>
				<b>Note</b>: Bridged polycycles are cleaned as far as their structure allows it, and the overlapping bonds warning will remain.
			</dd>

            <dt>
			<a href="#list">List of checkers</a>
			<a id="pseudoatom" class="anchor"></a><h4>Pseudo Atom Checker (<code>pseudoatom</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for pseudo atoms in the molecule.</li>
                    <li>Fixer offers: 
                        <ol>
                            <li>converting to abbreviated group if the name is in the abbreviated group list (e.g., Et for ethyl) <a href="#note14"><sup>*</sup></a> (<code>pseudotogroup</code>);</li>
							<li><a id="converttocarbon"></a>converting to carbon atom (<code>converttocarbon</code>);</li>
							<li>deleting atom (<code>removeatom</code>).</li>
                        </ol>
					</li>	
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" rowspan="2" width="15%">Pseudo Atom Checker (<code>pseudoatom</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th colspan="3">Fix</th> 
					</tr>
					<tr>
						<th>Convert Pseudo Atom to Group (<code>pseudotogroup</code>)</th>
						<th>Convert to Carbon (<code>converttocarbon</code>)</th>
						<th>Delete Atom (<code>removeatom</code>)</th>
					</tr>
					<tr>
                        <th align="left">Detect Pseudo Atom</th>
                        <td align="center"><img src="images/examples/ex_pseudo_ch.png" width="157" height="78" alt="ex_pseudo_ch"/></td>
						<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="not available"/></td>
						<td align="center"><img src="images/examples/ex_pseudo_f.png" width="163" height="95" alt="ex_pseudo_f"/></td>
						<td align="center"><img src="images/examples/ex_pseudo_fdel.png" width="92" height="46" alt="ex_pseudo_f"/></td>						
					</tr>
					<tr>
						<th align="left">Detect Pseudo Atom</th>
						<td align="center"><img src="images/examples/ex_pseudo_ch2.png" width="70" height="90" alt="ex_pseudo_ch"/></td>
						<td align="center"><img src="images/examples/ex_pseudo_fgroup.png" width="64" height="83" alt="ex_pseudo_f"/></td>
						<td align="center"><img src="images/examples/ex_pseudo_fconv.png" width="64" height="77" alt="ex_pseudo_f"/></td>
						<td align="center"><img src="images/examples/ex_pseudo_fdel2.png" width="58" height="43" alt="ex_pseudo_f"/></td>
					</tr>
                </table>
				<a id="note14"></a><sup>*</sup><b>Note</b>: <a href="../sketch/sketch-basic.html#User_defined_abbreviated_groups">
			User defined abbreviated groups</a> are also available to be applied as recognizable abbreviated 
			group set.
			</dd>

            <dt>
			<a href="#list">List of checkers</a>
			<a id="queryatom" class="anchor"></a><h4>Query Atom Checker (<code>queryatom</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for query atoms and atom query properties.</li>
                    <li>Fixer offers converting query atom to carbon atom <a href="#note15"><sup>*</sup></a> (<code>converttocarbon</code>).</li>
						</ul>
					</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
                 	<tr>
						<th rowspan="2" align="left" width="15%">Query Atom Checker (<code>queryatom</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Convert to Carbon (<code>converttocarbon</code>)</th>
					</tr>
					<tr>
                        <th align="left">Find Query Atoms</th>
                        <td align="center"><img src="images/examples/ex_queryatom_ch.png" width="360" height="142" alt="ex_queryatom_ch"/></td>
						<td align="center"><img src="images/examples/ex_queryatom_fconvert.png" width="317" height="121" alt="convert to carbon"/></td>
						<td align="center"><img src="images/examples/ex_queryatom_fdel.png" width="173" height="87" alt="delete atom"/></td>
                    </tr>
                </table>
				<a id="note15"></a><sup>*</sup><b>Note</b>: Fixer &quot;Convert to Carbon&quot; will not remove 
				any atom query property found by the &quot;Query Atom&quot; checker, but converts related atoms to carbon atoms. 
				<br><b>Suggestion</b>: To overcome the unwanted conversion of atoms having atom query 
				property to carbon, apply checker &quot;<a href="#atomquery">Atom Query Property</a>&quot; and its fixer before &quot;Query Atom&quot; checker. 
			</dd>

            <dt>
			<a href="#list">List of checkers</a>
			<a id="querybond" class="anchor"></a><h4>Query Bond Checker (<code>querybond</code>)</h4></dt>
            <dd>
				<ul>
					<li>Checker searches for query bonds.</li>
                    <li><a id="converttosinglebond"></a>Fixer offers converting query bond to single bond (<code>converttosinglebond</code>).</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
                    <tr>
						<th rowspan="2" align="left" width="15%">Query Bond Checker (<code>querybond</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
                        <th align="center">Convert to Single Bond (<code>converttosinglebond</code>)</th>						
                    </tr>
					<tr>
                        <th align="left">Find Query Bonds </th>
                        <td align="center"><img src="images/examples/ex_querybond_ch.png" width="311" height="134" alt="ex_querybond_ch"/></td>
						<td align="center"><img src="images/examples/ex_querybond_fconvert.png" width="300" height="125" alt="convert to single"/></td>
					</tr>
                </table>
				<b>Note</b>: Fixer &quot;Convert to Single Bond&quot; will not effect topology query 
				bonds found by &quot;Query Bond&quot; checker. 
				
			</dd>
				
			<dt>
			<a href="#list">List of checkers</a>
			<a id="ratom" class="anchor"></a><h4>R-atom Checker (<code>ratom</code>)</h4></dt>
            <dd>
				<ul>
					<li>Checker searches for R-atoms. Tick options for searching:
                        <ul>
                            <li>All (<code>:all=true</code>);</li>
                            <li>Disconnected (<code>:disconnected=true</code>);</li>
                            <li>Generic (<code>:generic=true</code>);</li>
                            <li>Linker (<code>:linker=true</code>);</li>
                            <li>Nested R-atoms (<code>:nested=true</code>).</li>
                        </ul> 
					</li>
                    <li>Fixer: no fixer is available for this checker.</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
                    <tr>
						<th align="left" width="15%">R-atom Checker (<code>ratom</code>)</th>
						<th>Warning:</th>							
						<th width="15%">Fix</th>
					</tr>	
					<tr>
                        <th align="left">Find All R-atoms (<code>:all=true</code>)</th>
                        <td align="center"><img src="images/examples/ex_ratom_ch.png" width="327" height="123" alt="ex_ratom_ch"/></td>
						<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no fixer"/></td>
                    </tr>
					<tr>
                        <th align="left">Find Disconnected R-atoms (<code>:disconnected=true</code>)</th>
                        <td align="center"><img src="images/examples/ex_ratom_chdisc.png" width="229" height="100" alt="ex_ratom_disconnected_ch"/></td>
						<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no fixer"/></td>
                    </tr>
					<tr>
                        <th align="left">Find Generic R-atoms (<code>:generic=true</code>)</th>
                        <td align="center"><img src="images/examples/ex_ratom_chgener.png" width="85" height="89" alt="ex_ratom_generic_ch"/></td>
						<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no fixer"/></td>
                    </tr>
					<tr>
                        <th align="left">Find Linker R-atoms (<code>:linker=true</code>)</th>
                        <td align="center"><img src="images/examples/ex_ratom_chlink.png" width="89" height="182" alt="ex_ratom_linker_ch"/></td>
						<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no fixer"/></td>
                    </tr>
					<tr>
                        <th align="left">Find Nested R-atoms (<code>:nested=true</code>)</th>
                        <td align="center"><img src="images/examples/ex_ratom_chnest.png" width="298" height="155" alt="ex_ratom_linker_ch"/></td>
						<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no fixer"/></td>
                    </tr>
                </table>	
            </dd>
			
			<dt>
			<a href="#list">List of checkers</a>
			<a id="rgroupattachmenterror" class="anchor"></a><h4>R-group Attachment Error Checker (<code>rgroupattachmenterror</code>)</h4></dt>
            <dd>
				<ul>
					<li>Checker searches for missing attachment points among R-group elements. </li> 
                    <li>Fixer offers adding R-group attachment points only in unambiguous cases (<code>fixrgroupattachment</code>).</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
                    <tr>
						<th align="left" rowspan="2" width="15%">R-group Attachment Error Checker (<code>rgroupattachmenterror</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
						<th>Add attachment point (<code>fixrgroupattachment</code>)</th>
					<tr>
                        <th align="left">Find missing attachment points</th>
                        <td align="center"><img src="images/examples/ex_rgattach_ch.png" width="208" height="145" alt="ex_rgattach_ch"/></td>
						<td align="center"><img src="images/examples/ex_rgattach_f.png" width="245" height="173" alt="ex_rgattach_f"/></td>
                    </tr>
                </table>
			</dd>
			
			<dt>
			<a href="#list">List of checkers</a>
			<a id="rgref" class="anchor"></a><h4>Deprecated - R-group Reference Error Checker</h4></dt>
            <dd>
				<ul>
					<li>Checker searches for:
						<ul>
							<li>Unused R-group;</li>
							<li>Missing R-group;</li> 
							<li>Circular R-group references.</li>
						</ul>
					</li>
                    <li>Fixer: no fixer is available for this checker.</li>
                </ul>
                <br>
				<b>Note</b>: R-group Reference Error Checker has been split into three separate checkers: <a href="#circularrg">Circular R-group Reference</a>; <a href="#missingrg">Missing R-group Reference</a>, or <a href="#unusedrg">Unused R-group Reference</a> checker.
				<br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
                    <tr>
						<th align="left"  width="15%">R-group Reference Error Checker</th>
						<th>Warning:</th>							 
					</tr>
					<tr>
                        <th align="left">Find Unused R-group references</th>
                        <td align="center"><img src="images/examples/ex_rgref02_ch.png" width="236" height="140" alt="ex_rgref02_ch"/></td>
                    </tr>
					<tr>
                        <th align="left">Find Missing R-group references</th>
                        <td align="center"><img src="images/examples/ex_rgref01_ch.png" width="281" height="141" alt="ex_rgref01_ch"/></td>
                    </tr>
					<tr>
                        <th align="left">Find Circular R-group references</th>
                        <td align="center"><img src="images/examples/ex_rgref03_ch.png" width="441" height="155" alt="ex_rgref03_ch"/></td>
                    </tr>
                </table>
			</dd>
			
			<dt>
			<a href="#list">List of checkers</a>
			<a id="racemate" class="anchor"></a><h4>Racemate Checker (<code>racemate</code>)</h4></dt>
            <dd>
				<ul>
					<li>Checker searches for chiral centers without set configuration.</li>
                    <li>Fixer: no fixer is available for this checker.</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
                    <tr>
						<th align="left" width="15%">Racemate Checker (<code>racemate</code>)</th>
						<th>Warning:</th>							
						<th width="15%">Fix</th> 
					</tr>
					<tr>
                        <th align="left">Find unmarked chiral centers</th>
                        <td align="center"><img src="images/examples/ex_racemate_ch.png" width="195" height="215" alt="example racemate checker"/></td>
						<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no fixer"/></td>
                    </tr>
				</table>
			</dd>

            <dt>
			<a href="#list">List of checkers</a>
			<a id="radical" class="anchor"></a><h4>Radical Checker (<code>radical</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for radical information on molecules .</li>
                    <li>Fixer offers removing the radical information (<code>removeradical</code>).</li>
                </ul>
                <br><b>Example</b>:
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
                    <tr>
						<th align="left" rowspan="2" width="15%">Radical Checker (<code>radical</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Remove Radical (<code>removeradical</code>)</th>
					</tr>
					<tr>
                        <th align="left">Find Radical Information</th>
                        <td align="center"><img src="images/examples/ex_radical_ch.png" width="133" height="120" alt="ex_radical_ch"/></td>
						<td align="center"><img src="images/examples/ex_radical_f.png" width="118" height="105" alt="ex_radical_f"/></td>
                    </tr>
                </table>
			</dd>

            <dt>
			<a href="#list">List of checkers</a>
			<a id="rareelement" class="anchor"></a><h4>Rare Element Checker (<code>rareelement</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for rare elements in the structures. Atoms generally present in organic compounds are: H, Li, Na, K, Mg, Ca, B, C, N, O, F, Cl, Br, I, Al, P, S, Cr, Mn, Fe, Co, Ni, Cu, Zn. Any elements outside this selection are considered rare elements.</li>
					<li>Fixer: no fixer is available for this checker.</li>
				</ul>
				<br><b>Example</b>: 
				<table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
                    <tr>
						<th align="left" width="15%">Rare element Checker (<code>rareelement</code>)</th>
						<th>Warning:</th>							
						<th width="15%">Fix</th> 
					</tr>
					<tr>
                        <th align="left">Find Rare element</th>
                        <td align="center"><img src="images/examples/ex_rareelement_ch.png" width="157" height="133" alt="ex_rareelement_ch"/></td>
						<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no fixer"/></td>
                    </tr>
				</table>					
			</dd>

			<dt>
			<a href="#list">List of checkers</a>
			<a id="reactionmaperror" class="anchor"></a><h4>Reaction Map Error Checker (<code>reactionmaperror</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for incomplete reaction map.
                        A reaction with two or less atom maps (both on the reactant and the product side)
                        is considered an incompletely mapped reaction.</li>
                    <li>Fixer offers mapping the reaction (<code>mapreaction</code>).</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
                   <tr>
						<th align="left" rowspan="2" width="15%">Reaction Map Error Checker (<code>reactionmap</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Mapping (<code>mapreaction</code>)</th>
					</tr>
					<tr>
                        <th align="left">Find incomplete reaction map</th>
                        <td align="center"><img src="images/examples/ex_reactionmap_ch.png" width="384" height="63" alt="ex_reactionmap_ch"/></td>
						<td align="center"><img src="images/examples/ex_reactionmap_f.png" width="397" height="74" alt="ex_reactionmap_f"/></td>
                    </tr>
				</table>
			</dd>
			
			<dt>
			<a href="#list">List of checkers</a>
			<a id="relativestereo" class="anchor"></a><h4>Relative Stereo Checker (<code>relativestereo</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for multiple enchanced stereogenic center flags.</li>
                    <li>Fixer: no fixer is available for this checker.</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
                   <tr>
						<th align="left" width="15%">Relative Stereo Checker (<code>relativestereo</code>)</th>
						<th>Warning:</th>							
						<th>Fix</th> 
					</tr>
					
					<tr>
                        <th align="left">Find relative stereo configuration</th>
                        <td align="center"><img src="images/examples/ex_relativestereo_ch.png" width="234" height="128" alt="ex_relativestereo_ch"/></td>
						<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no fixer"/></td>
                    </tr>
				</table>
			</dd>
			
            <dt>
			<a href="#list">List of checkers</a>
			<a id="ringstrainerror" class="anchor"></a><h4>Ring Strain Error Checker (<code>ringstrainerror</code>)</h4></dt>
            <dd>
                <ul>
                    <li>Checker searches for:
						<ul>
							<li>trans double bonds in bridged rings;</li>
							<li>cumulated double bonds in rings;</li>
							<li>triple bonds in rings.</li>
						</ul>
					</li>
                    <li>Fixer: no fixer is available for this checker.</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
                    <tr>
						<th align="left" width="15%">Ring Strain Error Checker (<code>ringstrainerror</code>)</th>
						<th >Warning:</th>							
						<th width="15%">Fix</th> 
					</tr>
					<tr>
						<th align="left">Detect trans double bonds</th>
						<td align="center"><img src="images/examples/ex_ringstrain_chtrans.png" width="116" height="92" alt="ex_ringstrain_ch"/></td>
						<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no fixer"/></td>
					</tr>
					<tr>
						<th align="left">Detect cumulated double bonds</th>
						<td align="center"><img src="images/examples/ex_ringstrain_chcum.png" width="105" height="108" alt="ex_ringstrain_ch"/></td>
						<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no fixer"/></td>
					</tr>
					<tr>
						<th align="left">Detect triple bonds</th>
						<td align="center"><img src="images/examples/ex_ringstrain_chtripl.png" width="99" height="110" alt="ex_ringstrain_ch"/></td>
						<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no fixer"/></td>
					</tr>
				</table>
			</dd>

			<dt>
			<a href="#list">List of checkers</a>
			<a id="solvent" class="anchor"></a><h4>Solvent Checker (<code>solvent</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for fragments in the molecule file defined as solvent molecules if more than one fragment is present. The list is customizable and is stored in a configuration file.</li>
                    <li>Fixer offers removing solvent fragments in case only one type of solvent is present (<code>removeatom</code>).</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" rowspan="2" width="15%">Solvent Checker (<code>solvent</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Delete Atom (<code>removeatom</code>)</th>
					</tr>					
					<tr>
						<th align="left">Find Solvent Molecules</th>
						<td align="center"><img src="images/examples/ex_solvent_ch.png" width="345" height="163" alt="ex_solvent_ch"/></td>
						<td align="center"><img src="images/examples/ex_solvent_f.png" width="349" height="75" alt="ex_solvent_f"/></td>
					</tr>
				</table>
			<p>The default solvents are: water, methanol, ethanol, propan-1-ol propan-2-ol, butan-1-ol, formic acid, acetic acid, pentane, hexane, benzene, methylbenzene, ethoxyethane, trichloromathane, ethyl acetate, dichloromethane, propan-2-one, acetonitrile, N-methylacetamide, methanesulfonylmethane, oxolane, heptane, 2-mezhylpropan-2-ol, butan-2-one, cyclohexane, cycloheptane, 1,2-dichloroethane, 2-(propan-2-yloxy)propane, 1,2-dimethoxyethane, N,N-dimethylacetamide, pyridine, 1,2-dimethylbenzene, 2-methyoxy-2-methylpropane
			</dd>

            <dt>
			<a href="#list">List of checkers</a>
			<a id="staratom" class="anchor"></a><h4>Star Atom Checker (<code>staratom</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for star atoms, inserted as Special node from the Advanced tab of the Periodic Table.</li>
                    <li>Fixer offers:
						<ol>
							<li>converting star atom to carbon atom (<code>converttocarbon</code>);</li>
							<li>removing the star atom (<code>removeatom</code>).</li>
						</ol>
					</li>
                </ul>
				<br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
                    <tr>
						<th align="left" rowspan="2" width="15%">Star Atom Checker (<code>staratom</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th colspan="2">Fix</th> 
					</tr>
					<tr>
						<th>Convert to Carbon (<code>converttocarbon</code>)</th>
						<th>Delete Atom (<code>removeatom</code>)</th>
					</tr>
					<tr>
                        <th align="left">Find Star Atom</th>
                        <td align="center"><img src="images/examples/ex_staratom_ch.png" width="185" height="132" alt="ex_staratom_ch"/></td>
						<td align="center"><img src="images/examples/ex_staratom_f2.png" width="162" height="109" alt="ex_staratom_f"/></td>
						<td align="center"><img src="images/examples/ex_staratom_f.png" width="188" height="102" alt="ex_staratom_f"/></td>
                    </tr>
				</table>
			</dd>
			
			<dt>
			<a href="#list">List of checkers</a>
			<a id="stereocarebox" class="anchor"></a><h4>Stereo Care Box Checker (<code>stereocarebox</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for stereo care boxes placed on query bonds.</li>
                    <li>Fixer offers removing care boxes from bonds (<code>removestereocarebox</code>).</li>
                </ul>
				<br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
                    <tr>
						<th align="left" rowspan="2" width="15%">Stereo Care Box Checker (<code>stereocarebox</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Remove Stereo Care Box (<code>removestereocarebox</code>)</th>
					</tr>
					<tr>
                        <th align="left">Find Stereo Care Box</th>
                        <td align="center"><img src="images/examples/ex_stereocb_ch.png" width="168" height="122" alt="ex_stereocb_ch"/></td>
						<td align="center"><img src="images/examples/ex_stereocb_f.png" width="163" height="120" alt="ex_stereocb_f"/></td>
                    </tr>
				</table>
			</dd>

						<dt>
			<a href="#list">List of checkers</a>
			<a id="straightdoublebond"></a><h4>Straight Double Bond Checker (<code>straightdoublebond</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for undefined double bond stereo representations.</li>
                    <li>Fixer offers:
						<ol>
							<li>changing crossed double bond to wiggly double bond type (<code>converttowigglydoublebond</code>);</li>
							<li>changing wiggly double bond to crossed double bond type (<code>converttocrosseddoublebond</code>).</li>
						</ol>
					</li>				
				</ul>
				<br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
                    <tr>
						<th align="left" rowspan="2" width="15%">Straight Double Bond Checker (<code>straightdoublebond</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th colspan="2">Fix</th> 
					</tr>
					<tr>
						<th>Convert to Crossed Double Bond (<code>converttocrosseddoublebond</code>)</th>
						<th>Convert to Wiggly Double Bond (<code>converttowigglydoublebond</code>)</th>
					</tr>
					<tr>
                        <th align="left">Find Straight Double Bond</th>
                        <td align="center"><img src="images/examples/ex_straightdb_ch.png" width="172" height="46" alt="ex_s_ch"/></td>
						<td align="center"><img src="images/examples/ex_straightdb_fcrossed.png" width="172" height="45" alt="ex_s_f"/></td>
						<td align="center"><img src="images/examples/ex_straightdb_fwiggly.png" width="175" height="45" alt="ex_s_f"/></td>
                    </tr>
				</table>
			</dd>
			
			<dt>
			<a href="#list">List of checkers</a>
			<a id="substructure" class="anchor"></a><h4>Substructure Checker (<code>substructure:[smarts]</code>)</h4></b></dt>
            <dd>
				<ul>
					<li>Checker searches for SMARTS defined substructures. Substructure Checkers can be set individually.</li>
					<li>Fixer: when the substructure is specified as reaction SMARTS or SMIRKS, the checker 
					works as fixer as well.</li>
				</ul>
				<br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
                    <tr>
						<th align="left" rowspan="2" width="15%">Substructure Checker</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>O=N=O>>[O-][N+]=O</th>
					</tr>
                    <tr>
						<td align="left"><b>Checker setting</b>: SMARTS: O=N=O>>[O-][N+]=O</td>
						<td align="center"><img src="images/examples/ex_sub_ch.png" width="167" height="148" alt="substructure_nitro"/></td>
						<td align="center"><img src="images/examples/ex_sub_f.png" width="166" height="142" alt="substructure_fixed"/></td>
					</tr>
                </table>
			<br>
			<b>Note</b>: This checker is available only in JChem. The checker can also be accessed from MarvinSketch if MarvinSketch is run from the JChem\bin folder (e.g, c:\Program Files\ChemAxon\Jchem\bin\msketch.bat).
            </dd>
  
			<dt>
			<a href="#list">List of checkers</a>  
			<a id="3d" class="anchor"></a><h4>Three Dimension Checker (<code>3d</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for 3D (Z) coordinates.</li>
                    <li>Fixer offers:
						<ol>
							<li>cleaning the structures by 2D clean (<code>clean</code>);</li>
							<li>setting the atomic z-coordinates to zero (<code>removezcoordinate</code>). </li>
						</ol>
					</li>
                </ul>
                <br><b>Example</b>: 
                 <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" rowspan="2" width="15%">Three Dimension Checker (<code>3d</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th colspan="2">Fix</th> 
					</tr>
					<tr>
						<th>Clean (<code>clean</code>)</th>	
						<th>Remove Z-Coordinate (<code>removezcoordinate</code>)</th>												
					</tr>
                    <tr>
                        <th align="left">Detect 3D coordinates</th>
                        <td align="center"><img src="images/examples/ex_3d_ch.png" width="203" height="166" alt="ex_3d_ch"/></td>
						<td align="center"><img src="images/examples/ex_3d_f.png" width="129" height="133" alt="ex_3d_f"/></td>
						<td align="center"><img src="images/examples/ex_3d_fz.png" width="193" height="156" alt="ex_3d_fz"/></td>
                    </tr>						
                </table>
			</dd>
			
			<dt>
			<a href="#list">List of checkers</a>
			<a id="unbalancedreaction" class="anchor"></a><h4>Unbalanced Reaction Checker (<code>unbalancedreaction</code>)</h4></dt>
            <dd>
				<ul>
					<li>Checker detects reaction scheme with differing total atom numbers or atom types 
					on the two sides of the reaction arrow.</li>
					<li>Fixer: no fixer is available for this checker.</li>
				</ul>
				<br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
                    <tr>
						<th align="left" width="15%">Unbalanced Reaction Checker (<code>unbalancedreaction</code>)</th>
						<th>Warning:</th>							
						<th width="15%">Fix</th> 
					</tr>
					<tr>
						<th align="left">Detect unbalanced reaction scheme</th>
						<td align="center"><img src="images/examples/ex_unbalreaction_ch.png" width="460" height="125" alt="Unbalanced reaction"/></td>
						<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no fixer available"/></td>
                    </tr>
					<tr>
                        <th align="left">Approved reaction scheme</th>
                        <td align="center"><img src="images/examples/ex_unbalreaction_corr.png" width="519" height="116" alt="Balanced reaction"/></td>
						<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no fixer available"/></td>
                    </tr>					
                </table>
			</dd>  
			
			<dt>
			<a href="#list">List of checkers</a>
			<a id="unusedrgroupreference" class="anchor"></a><h4>Unused R-group Checker (<code>unusedrgroup</code>)</h4></dt>
            <dd>
				<ul>
					<li>Checker searches for R-group definitions that are not used.</li>
					<li>Fixer removes unused R-group definitions (<code>fixunusedrgroups</code>).</li>
				</ul>
				<br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
                    <tr>
						<th align="left" rowspan="2" width="15%">Unused R-group Reference Checker (<code>unusedrgroup</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
					<th>Remove unused R-group definitions (<code>fixunusedrgroups</code>)</th>
					</tr>
					<tr>
						<th align="left">Detect unused R-groups</th>
						<td align="center"><img src="images/examples/ex_unusedrg_ch.png" width="353" height="309" alt="Unused R-groups"/></td>
						<td align="center"><img src="images/examples/ex_unusedrg_f.png" width="350" height="216" alt="Remove unused R-group definition"/></td>
                    </tr>										
                </table>
			</dd>  

            <dt>
			<a href="#list">List of checkers</a>
			<a id="valenceerror" class="anchor"></a><h4>Valence Error Checker (<code>valenceerror</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for valence errors.  
						<ul>
							<li>Allow traditional N representation: checking this option will allow 
							the presence of pentavalent nitrogen. <a href="../sci/ValenceCalculator.html#nitrogen">Details on valence calculation of nitrogen</a> (<code>:allowTraditionalNitrogen=true</code>)</li>
						</ul>
					</li>
                    <li>Fixer offers removing explicit hydrogens if applicable (<code>fixvalence</code>).</li>
                </ul>
                <br><b>Example</b>: 
                 <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
                    <tr>
						<th align="left" rowspan="2" width="15%">Valence Error Checker (<code>valenceerror</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Fix Valence (<code>fixvalence</code>)</th>							
					</tr>
                    <tr>
                        <th align="left" rowspan="3">Detect Valence Error</th>
                        <td align="center"><img src="images/examples/ex_valence_ch.png" width="79" height="91" alt="ex_valence_ch"/></td>
						<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="no error"/></td>
                    </tr>
					<tr>
                        <td align="center"><img src="images/examples/ex_valence_chc2.png" width="129" height="112" alt="ex_valence_ch"/></td>
						<td align="center"><img src="images/examples/ex_valence_fc2.png" width="133" height="94" alt="ex_valence_f"/></td>
                    </tr>
					<tr>
                        <td align="center"><img src="images/examples/ex_valence_chc.png" width="128" height="111" alt="ex_valence_ch"/></td>
						<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="not available"/></td>
                    </tr>
					<tr>
                        <th align="left" rowspan="3">Detect Valence Error:<br>Allow traditional N representation<br>(<code>:allowTraditionalNitrogen=true</code>)</th>
                        <td align="center"><img src="images/examples/ex_valence_ch1.png" width="79" height="91" alt="ex_valence_ch"/></td>
						<td align="center"><img src="images/examples/no.png" width="24" height="24" alt="not available"/></td>
                    </tr>					
                </table>
				<br>
				<b>Note</b>: Valence errors with no removable hydrogens have to be corrected manually.
			</dd>

			<dt>
			<a href="#list">List of checkers</a>
			<a id="valenceproperty" class="anchor"></a><h4>Valence Property Checker (<code>valenceproperty</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for atoms with marked default and non-default valence properties.</li>
                    <li>Fixer offers removing the identified valence properties (<code>removevalenceproperty</code>).</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" rowspan="2" width="15%">Valence Property Checker (<code>valenceproperty</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Remove Valence Property (<code>removevalenceproperty</code>)</th>							
					</tr>
                    <tr>
                        <th align="left">Detect Marked Valence Property</th>
                        <td align="center"><img src="images/examples/ex_valenceqp_ch.png" width="87" height="135" alt="example valence property check"/></td>
						<td align="center"><img src="images/examples/ex_valenceqp_f.png" width="85" height="105" alt="fixed valence property"/></td>
                    </tr>
                </table>
			</dd>
			
            <dt>
			<a href="#list">List of checkers</a>
			<a id="wedgeerror" class="anchor"></a><h4>Wedge Error Checker (<code>wedgeerror</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for irregular wedge bonds.</li> 
                    <li>Fixer offers removing all wedge bonds and replacing them with a single bond (<code>wedgeclean</code>).</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" rowspan="2" width="15%">Wedge Error Checker (<code>wedgeerror</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Clean Wedge (<code>wedgeclean</code>)</th>							
					</tr>
                    <tr>
                        <th align="left">Detect irregular wedge bond</th>
                        <td align="center"><img src="images/examples/ex_wedge_ch.png" width="345" height="174" alt="ex_wedge_ch"/></td>
						<td align="center"><img src="images/examples/ex_wedge_f.png" width="333" height="163" alt="ex_wedge_f"/></td>
                    </tr>						
				</table>
				<br>
			    <b>Note</b>: Some symmetric structures are not recognized by the checker.
			</dd>

            <dt>
			<a href="#list">List of checkers</a>
			<a id="wigglybond" class="anchor"></a><h4>Wiggly Bond Checker (<code>wigglybond</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for wiggly bond (molecules with unknown or unspecified configuration) starting from chiral centers.</li>
                    <li>Fixer offers changing wiggly bond to single bond (<code>converttosinglebond</code>).</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" rowspan="2" width="15%">Wiggly Double Bond Checker (<code>wigglybond</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Convert to Single Bond (<code>converttosinglebond</code>)</th>							
					</tr>
                    <tr>
                        <th align="left">Detect Wiggly Bond</th>
                        <td align="center"><img src="images/examples/ex_wigglychiral_ch.png" width="124" height="104" alt="ex_wigglychiral_ch"/></td>
						<td align="center"><img src="images/examples/ex_wigglychiral_f.png" width="115" height="98" alt="ex_wigglychiral_f"/></td>
                    </tr>
                </table>			
			</dd>
			
			<dt>
			<a href="#list">List of checkers</a>
			<a id="wigglydoublebond" class="anchor"></a><h4>Wiggly Double Bond Checker (<code>wigglydoublebond</code>)</h4></dt>
            <dd>
				<ul>
                    <li>Checker searches for wiggly double bond (molecules with unknown or unspecified configuration).</li>
                    <li>Fixer offers changing wiggly double bond to crossed double bond type (<code>converttocrosseddoublebond</code>).</li>
                </ul>
                <br><b>Example</b>: 
                <table class="grid structurechecker" cellspacing="0" cellpadding="3" width="100%">
					<tr>
						<th align="left" rowspan="2" width="15%">Wiggly Double Bond Checker (<code>wigglydoublebond</code>)</th>
						<th rowspan="2">Warning:</th>							
						<th>Fix</th> 
					</tr>
					<tr>
						<th>Convert to Crossed Double Bond (<code>converttocrosseddoublebond</code>)</th>							
					</tr>
                    <tr>
                        <th align="left">Detect Wiggly Double Bond</th>
                        <td align="center"><img src="images/examples/ex_wiggly_ch.png" width="217" height="193" alt="ex_wiggly_ch"/></td>
						<td align="center"><img src="images/examples/ex_wiggly_f.png" width="226" height="192" alt="ex_wiggly_f"/></td>
                    </tr>
                </table>
			<br>
			<b>Note</b>: Reverse action: <a href="#crosseddouble">Crossed Double Bond checker</a>.
			</dd>
        </dl>
		
		<hr />
		<h3><a id="deprecation"></a>Deprecation</h3>
		<p>
		The following action strings are deprecated from version 5.12.0:
		<table class="grid">
			<tr>
				<th><a id="deprecatedchecker"></a>Deprecated checker action strings</th>
				<th>New checker action strings</th>
			</tr>
			<tr>
				<td>aromaticity</td>
				<td><a href="#aromaticityerror">aromaticityerror</a></td>
			</tr>
			<tr>
				<td>chiralflag</td>
				<td><a href="#chiralflagerror">chiralflagerror</a></td>
			</tr>
			<tr>
				<td>circularrgroup</td>
				<td><a href="#circularrgroupreference">circularrgroupreference</a></td>
			</tr>
			<tr>
				<td>missingrgroup</td>
				<td><a href="#missingrgroupreference">missingrgroupreference</a></td>
			</tr>
			<tr> 
				<td>rare</td>
				<td><a href="#rareelement">rareelement</a></td>
			</tr>
			<tr>
				<td>reactionmap</td>
				<td><a href="#reactionmaperror">reactionmaperror</a></td>
			</tr>
			<tr>
				<td>unusedrgroup</td>
				<td><a href="#unusedrgroupreference">unusedrgroupreference</a></td>
			</tr>
			<tr>
				<td>valence</td>
				<td><a href="#valenceerror">valenceerror</a></td>			
			</tr>
			<tr>
				<td>wedge</td> 
				<td><a href="#wedgeerror">wedgeerror</a></td>
			</tr>
			</table><br/>
			<table class="grid">
			<tr>
				<th><a id="deprecatedfixer"></a>Deprecated fixer action strings</th>
				<th>New fixer action strings</th>
			</tr>
			<tr> 
				<td>clearabsstereo</td>
				<td><a href="#removeinvalidchiralflag">removeinvalidchiralflag</a></td>
			</tr>
			<tr>
				<td>aliastocarbon</td>
				<td><a href="#converttocarbon">converttocarbon</a></td>
			</tr>
			<tr>
				<td>crossedtowiggly</td>
				<td><a href="#converttowigglydoublebond">converttowigglydoublebond</a></td>
			</tr>
			<tr>
				<td>converttosingle</td>
				<td><a href="#converttosinglebond">converttosinglebond</a></td>
			</tr>
		</table>
		</p>
		
		<a href="#list">List of checkers</a>
		<hr />
		<h3><a class="anchor" id="links"></a>Links</h3>
		<table>
			<tr>
				<td><a href="structurechecker.html"><img src="images/gui/structure_checker_16.png" /> Structure Checker GUI</a></td>
			</tr>
			<tr>
				<td><a href="checker.html"><img src="images/gui/check-structure16.png" /> Structure Checker in MarvinSketch</a></td>
			</tr>
			<tr>
				<td><a href="structurechecker_cline.html"><img src="images/gui/structurechecker_cli16.png" /> <code>structurechecker</code> Command Line Tool</a></td>
			</tr>
			<tr>
				<td><a href="structurechecker_examples.html">Examples of structure checking in various ChemAxon products</a></td>
			</tr>
			<tr>
				<td><a href="../developer/checker.html">Structure Checker Developer Guide</a></td>
			</tr>
		</table>
		
		<hr>		
		
	</body>
</html>