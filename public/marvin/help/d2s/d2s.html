<!-- 
    Document   : d2s
    Created on : Jan 28, 2012,
    Author     : Efi
	-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>Document to structure</TITLE>
 <link rel="stylesheet" type="text/css" href="../marvinmanuals.css">
</HEAD>
  <body>
  
  <h1>Document to Structure (d2s) Conversion</h1>
Document to Structure processes PDF, HTML, XML, text files and office file formats: DOC, DOCX, PPT, PPTX, XLS, XLSX, ODT.
It recognizes and
converts the chemical names (IUPAC, CAS, common and drug names), SMILES and InChI found in the document into
chemical structures.

<p>
d2s conversion uses the name-to-structure converter. For the supported names and current limitation, see <a href="../naming/n2s.html">"Name to Structure Conversion"</a> webpage. 
You can extend the document to structure conversion by creating a <a href="../naming/naming_customdic.html">custom dictionary file</a>.

<p>
d2s can be used via <a href="../developer/d2s.html">API</a>, <a href="#d2s_molconvert">command line application (MolConverter)</a>, or <a href="#d2s_MView">MarvinView</a>.
Text mining can also be automatized by using d2s integrated into <a href="http://www.chemaxon.com/products/workflow-tools/#knime">
Knime</a> or into <a href="http://www.chemaxon.com/products/workflow-tools/#pipelinepilot">Pipeline Pilot</a>.

  <h2>OCR and syntax correction</h2>
  Chemaxon's d2s toolkit is able to correct several simple OCR and syntax error. For instance, given the incorrect name "3-rnethyl-l-me-thoxynaphthalene", 
it automatically corrects the name to "3-methyl-1-methoxynaphthalene" and generates the corresponding structure.

	<h2><a name="d2s_MView" class="anchor">Document to Structure Conversion in MarvinView</a></h2>
    <p>Open a PDF file containing chemical names. MarvinView will display all the structures corresponding to the recognized names.
    The structures can then be saved, copy-pasted, opened in the MarvinSketch editor, ...
    </p>

<h2><a name="d2s_molconvert" class="anchor">Document to structure conversion from command line</a></h2>
As a commandline tool, you can use <a href="../applications/molconvert.html">MolConverter</a> for d2s conversion.  
Example:
<ol>
<li> Converting "test.pdf" name file to MOL file:
<pre> molconvert mol test.pdf -o test.mol</pre></li>
</ol>
<h2><a name="d2s_OLE" class="anchor">Structure conversion from OLE objects</a></h2>
D2s converts the chemical structures from OLE objects &#8211; created by various chemical sketchers such as Marvin, ChemDraw, ISIS/DRAW, SYMYX DRAW, and Accelrys Draw &#8211; embedded in office documents.

<h2><a name="d2s_OSRA" class="anchor">Chemical image recognition</a></h2>
If <a href="../formats/imageimport.html">OSRA</a> is installed on your computer, d2s will also convert the images of compounds into editable chemical structures from PDF files.
Note that structures present as vector graphics rather than bitmap are not converted, unless the <code>osraRendered</code>
format option is used.

<h2>See also</h2>
<ul>
<li><a href="../developer/d2s.html">Developer documentation for d2s</a></li>
<li><a href="http://www.chemaxon.com/marvin/examples/d2s/index.html">d2s code examples</a></li>
</ul>
  

<h2>License informations</h2>
<ul>
<li> You need the "Document to Structure" licence.</li>
</ul>

  
  
  </body>
</html>
