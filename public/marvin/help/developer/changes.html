<!DOCTYPE html>
<html>
	<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	<meta name="author" content="<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>">
	<link rel ="stylesheet" type="text/css" href="../marvinmanuals.css" title="Style">
	<title>Changes</title>
</head>
<body>

<h1>History of changes: New features and improvements, Bugfixes</h1>
<h3><a id="v6010" class="anchor"></a>June 17th, 2013: Marvin 6.0.1</h3>
<a href="../marvin4js/changes_Marvin4JS.html#v601">Marvin for JavaScript</a> 6.0.1 history of changes
<h4>New features and improvements</h4>
<ul>
	<li><strong>Image I/O</strong>
		<ul>
			<li>The following file extensions were removed from the "Files of Type" list on the Import Image dialog: *.ps, *.pdf, and "All Files". PDF files can be imported through the "File &gt; Open" menu.</li>
		</ul>
	</li>
	<li><strong>Calculation</strong> 
		<ul>
			<li><strong>Services</strong>
				<ul>
					<li>SOAP webservices: 
						<ul>
							<li>SOAP version 1.2 webservices are supported.</li>
						</ul>	
					</li>
				</ul>
			</li>
		</ul>
	</li>
</ul>
<h4>Bugfixes</h4>
<ul>
	<li><strong>Painting</strong>
		<ul>
			<li>R/S stereo labels were not displayed when there was an R-group on the canvas.</li>
			<li>S-groups, created from structures containing a position variation bond, retained the grey patch of the position variation bond when the group was contracted.</li>
		</ul>
	</li>
	<li><strong>Editing</strong>
		<ul>
			<li>An atom of an expanded S-group could not be replaced with an S-group correctly</li>
		</ul>
	</li>
	<li><strong>Clipboard handling</strong>
		<ul>
			<li>On Mac OS X, copy-paste (from MarvinSketch to, <i>e.g.</i>, TextEdit) did not transfer the atom colors. 
				<a href="https://www.chemaxon.com/forum/ftopic10144.html">Forum topic</a></li>
		</ul>
	</li>
	<li><strong>Graphical object handling</strong>
		<ul>
			<li>Formatting the head of a graphical arrow to a half-left arrow using the Document Style dialog crashed the display of the arrow.</li>
		</ul>
	</li>
	<li><strong>Import/Export</strong>
		<ul>
			<li><strong>MRV/CML</strong>
				<ul>
					<li>MRV and CML export failed with the woodstock xml processor.
					<a href="https://www.chemaxon.com/forum/ftopic10499.html">Forum topic</a></li>
				</ul>
			</li>	
			<li><strong>InChi/InChiKey</strong>
				<ul>
					<li>InChi export did not return 
					standard InChi when the absolute 
					stereo was not set in a non-chiral 
					molecule. 
					<a href="https://www.chemaxon.com/forum/ftopic10424.html">Forum topic</a></li>
				</ul>
			</li>
			<li><strong>Document to Structure</strong>
				<ul>
					<li>Documents with many consecutive names would lead to 
					extremely long processing time (appeared as hanging, unless reaching the timeout). 
					<a href="https://www.chemaxon.com/forum/ftopic10643.html">Forum topic</a></li>
				</ul>
			</li>
		</ul>
	</li>
	<li><strong>Molecule Representation</strong>
		<ul>
			<li><strong>Implicit/Explicit Hydrogen Conversion</strong>
				<ul>
					<li> Adding explicit hydrogen to an atom being part of a Multicenter S-group added the hydrogen to the S-group as well. 
					<a href="https://www.chemaxon.com/forum/ftopic10533.html">Forum topic</a></li>

				</ul>
			</li>
		</ul>
	</li>
	<li><strong>Clean 2D</strong>
		<ul>
			<li>Stereo clean created non-IUPAC standard depiction of certain tetrahedral configurations. 
		        <a href="https://www.chemaxon.com/forum/ftopic10614.html">Forum topic</a></li>
		</ul>
	</li>
	<li><strong>Data Transfer</strong>
		<ul>
			<li><strong>OLE</strong>
				<ul>
					<li>Redirecting ChemDraw OLE objects did not work.</li>
				</ul>
			</li>
		</ul>
	</li>
	<li><strong>Applet</strong>
		<ul>
			<li>Applet parameter <code>extraBonds</code> did not work therefore the bond type menu appearance could not be changed. 
				<a href="https://www.chemaxon.com/forum/ftopic10418.html">Forum topic</a></li>
		</ul>
	</li>
	<li><strong>Image I/O</strong>
		<ul>
			<li>The xml header information was always included in the generated SVG content during image export. 
				SVG export now has an option "headless" to exclude this information. 
				<a href="http://www.chemaxon.com/marvin/help/formats/svg-doc.html">Documentation</a>, 
				<a href="https://www.chemaxon.com/forum/ftopic10002.html">Forum topic</a></li>
		</ul>
	</li>
	<li><strong>Calculation</strong>
		<ul>
			<li><strong>Protonation (pKa, Major Microspecies, Isoelectric Point)</strong>
				<ul>
					<li>"Keep explicit hydrogen" option caused exception during pKa calculation.</li>
				</ul>
			</li>
			
			<li><strong>NMR (HNMR, CNMR Prediction, ...)</strong>
				<ul>
					<li>When the mouse cursor was moved over an NMR peak on the chart, the relevant 
					atom was not highlighted on NMR predictor molecule preview panel.</li>
				</ul>
			</li>
			<li><strong>Services</strong>
				<ul>
					<li>SOAP webservices: 
						<ul>
							<li>Parameters did not load from WSDL properly in SOAP version 1.1 webservices.</li>
						</ul>	
					</li>
				</ul>
			</li>
		</ul>
	</li>
</ul>

<h3><a id="v600" class="anchor"></a>May 17th, 2013: Marvin 6.0.0</h3>
<h4>New features and improvements </h4>
<ul>
	<li><strong>MarvinSketch GUI</strong>
		<ul>
			<li>The default tool in MarvinSketch is Rectangle Selection instead of Single Bond.</li> 
			<li>MarvinSketch GUI has been refreshed: Icon set has been changed in MarvinSketch. Menu items have been rearranged. Several dialogs, tooltips, menus has been renamed.</li>
			<li>A new Marvin configuration has been added to MarvinSketch. Already existing Marvin configurations have been renamed. <a href="../sketch/gui/configurations.html">Documentation</a></li>
			<li>MarvinSketch now starts with the native "Look & Feel" on Windows and on Mac OS X.</li>
		</ul>
	</li>
	<li><strong>MarvinSketch Dialog</strong>
		<ul>
			<li>At the first launch of MarvinSketch after installation, a dialog asks the user to select the desired skin for the GUI configuration. <a href="../sketch/gui/configurations.html">Documentation</a></li>
			<li>The Open dialog has been modified. Selecting molecules to load from a multistructure file happens now directly on the Open dialog.</li>
			<li>New dialog, called "Insert file", has been introduced into MarvinSketch for adding structures from new files to the canvas contents. <a href="../sketch/sketch-basic.html#open-molecule">Documentation</a></li>
			<li>The Save dialog has been extended with an Advanced panel to set saving options.</li>
		</ul>
	</li>
	<li><strong>Painting</strong>
		<ul>
			<li>Drawing quality has been improved: join of single bonds has been refined.</li>
			<li>Superatom S-group attachment points are denoted by numbers instead of asterisks.</li>
		</ul>
	</li>
	<li><strong>Editing</strong>
		<ul>
			<li>MarvinSketch GUI can handle unlimited number of attachment points on Superatom S-groups. 
				There are new menu options for adding and removing attachment points in the Atom menu and in the contextual menu. <a href="../sketch/sketch-basic.html#Sgoups_to_template">Documentation</a></li>
			<li>Bond length can be scaled. Bond length and size of other objects can be set by dragging the displayed corners of the bounding rectangle. 
					Scaling is also available on the Document Style dialog. 
					Scaling does not work when the structure has been rotated in 3D mode (with F7). <a href="../sketch/sketc-basic.html#scalling">Documentation</a></li>
		</ul>
	</li>
	<li><strong>Load/Save handling in UI</strong>
		<ul>
			<li>New loading mechanism has been introduced in MarvinSketch. New menu option, Insert file, has been added to the File menu; it appends the contents of the chosen file to the structure 
					on the canvas. <a href="../sketch/sketch-basic.html#open-molecule">Documentation</a></li>
			<li>New saving mechanism has been introduced in MarvinSketch. <a href="../sketch/sketch-basic.html#save2molformat">Documentation</a></li>
			<li>New save mechanism has been introduced for MarvinView. <a href="../view/view-basic.html">Documentation</a></li>
			<li>Reactions and structures containing R-group definitions cannot be saved either in separate files or separately in the same file. <a href="../sketch/sketch-basic.html#save2molformat">Documentation</a></li>
		</ul>
	</li>
	<li><strong>Clipboard handling</strong>
		<ul>
			<li>Images of chemical structures can be imported from the clipboard via the configured image import service or locally installed OSRA.</li>
		</ul>
	</li>
	<li><strong>Template Library</strong>
		<ul>
			<li>New template set, Organometallics, has been added to the Template Library.</li>
			<li>New amino acid templates have been added (e.g., pyrrolysine, selenocysteine, "any/unknown/undetermined aminoacid").</li>
			<li>The following amino acid templates have three attachment points: arginine, cysteine, histidine, lysine, selenocysteine, serine, threonine, tryptophan, tyrosine.</li>
		</ul>
	</li>
	<li><strong>Integration</strong>
		<ul>
			<li>The new Automapper has been introduced in MarvinSketch.</li>
		</ul>
	</li>
	
	<li><strong>MarvinSpace</strong>
		<ul>
			<li>Copy and Paste functions have been added to MarvinSpace.</li>
			<li>3D editor: Molecules can be edited in 2D in MarvinSketch, and aligned to the original 3D molecule.</li>
		</ul>
	</li>
	<li><strong>Import/Export</strong>
		<ul>
			<li> <strong>MRV/CML</strong>
				<ul>
					<li> Import and export of more than two
					Superatom S-group attachment points are
					supported. </li>
				</ul>
			</li>
			<li> <strong> MRV schema </strong>
				<ul>
					<li> New element, AttachmentPointArray, is
					introduced in format MRV to describe the
					new Superatom S-group attachment point 
					representation.
					<a href="../formats/schema/mrvSchema_6_0_0.xsd">MRV Schema</a>, 
					<a href="../formats/mrv-doc.html">Documentation</a> </li>
				</ul>
			</li>
			<li><strong>MOL, SDF, RXN, RDF</strong>
				<ul>
					<li> The import of template based 
					MDL MOL files is introduced. </li> 
					<li>Import and export of more than two
                                        Superatom S-group attachment points are
                                        supported from MDL MOL based formats. </li> 
				</ul>
			</li>
			<li><strong>SKC</strong>
				<ul>
					<li> Import and export of more than two
                                        Superatom S-group attachment points are
                                        supported. Superatom S-groups having 
					at least one crossing bond are exported in
					their expanded form. </li>
				</ul>
			</li>
			<li><strong>CDX/CDXML</strong>
				<ul>
					<li> Import and export of more than two
                                        Superatom S-group attachments point are
                                        supported. </li>
					<li> Curved lines can be imported from CDX 
					files. </li> 	
				</ul>
			</li>
			<li><strong>CXSMILES/CXSMARTS</strong>
				<ul>
					<li>  Generic, Polymer, and Mixture type 
					S-groups
					are stored in CXSMILES/CXSMARTS format. 
					<a href="../formats/cxsmiles-doc.html">Documentation</a> </li>
				</ul>
			</li>
			<li> <strong>SMILES/SMARTS</strong>
				<ul> 
					<li> In SMILES import, radicals are not added
					anymore to atoms with atomic number above 
					chlorine, except for bromine and iodine. 
					If implicit hydrogen can not be added to or 
					removed from the atom (as it is in brackets 
					in the SMILES definition), then a valence 
					property is set to correct its valence if 
					necessary. 
					<a href="../formats/smiles-doc.html">Documentation</a></li>
				</ul>
			</li>
			<li><strong>Peptide</strong> 
				<ul>
					<li> Amino acids: Cysteine, Aspartic acid, 
					Glutamic acid, Histidine, Lysine, Asparagine,
					Glutamine, Arginine, Serine, Threonine, 
					Selenocysteine, Tryptophane, and Tyrosine 
					are imported with three attachment points 
					from peptide format. </li>
					<li> New 1-Letter and 3-letter amino acid 
					codes were added: U and Sec for 
					selenocysteine; O and Pyl for pyrrolysine; 
					X and Xaa for "any/unknown/undetermined 
					amino acid"; B and Asx for "Aspartic acid 
					or Asparagine"; Z and Glx for 
					"Glutamic acid or Glutamine"; J and Xle for 
					"Leucine or Isoleucine". </li>
				</ul>
			</li>
			<li><strong>FASTA</strong>
			 	<ul>
					<li>Peptide, DNA, and RNA import from 
					FASTA format has been introduced.
					<a href="../formats/fasta-doc.html">Documentation</a> </li>
				</ul>
			</li>
			<li><strong>Name to Structure (n2s)</strong> 
				<ul> 
					<li>Custom name-to-structure webservices can now be configured to receive the 
					name in the middle of the URL, not just at the end, by using the [NAME] marker 
					in the configuration URL.</li>
					<li>More forms of ranged generic names are supported, such as "-C<sub>1-6</sub>alkyl". 
					These are found extensively as part of markush structures in patents.</li>
				</ul>
			</li>
			
			<li><strong>Document to Structure (d2s)</strong> 
				<ul> 
					<li>Text document processing has become faster (around 20% speedup for typical texts with a small proportion of chemical names).</li>
					<li>The context (surrounding text) in which names appear in the document is now available as a property of the returned structures.</li>
					<li>The <code>osraTimeout</code> option has been added to configure the maximum time to wait 
					for OSRA to convert an image to structure (the default is 20 seconds).</li>
				</ul>
			</li>
			
			<li><strong>Structure to Name(s2n)</strong> 
				<ul> 
					<li>The new format option "cas#" allows converting structures to their CAS 
					Registry Number using a public webservice.</li>
				</ul>
			</li>
		</ul>
	</li>
	<li> <strong>Molecule Representation</strong>
		<ul>
			<li><strong>S-groups </strong>
				<ul> 
					<li> Any number of attachment points can be 
					added to Superatom S-groups. </li>
					<li> S-group attachment point information 
					can be set only to atoms being part of a 
					Superatom S-group. </li>
				</ul>
			</li>
			<li><strong>Valence Check</strong>
				<ul>
					<li> Valence check does not deal with 
					free attachment points.  </li>
					<li> New valence check option is introduced 
					to allow/deny traditional forms of 
					pentavalent Nitrogen. </li>
				</ul>
			</li>
		</ul>
	</li>
	<li><strong>Calculation</strong>
		<ul>
			<li><strong>Protonation (pKa, Major Microspecies, Isoelectric Point)</strong>
			<ul>
				<li>log[%] vs. pH distribution chart is introduced in case of pKa calculation. <a href="../calculations/protonation.html">Documentation</a></li>
				<li>Major miscrospecies calculation is supported for molecules with coordinate bonds. <a href="https://www.chemaxon.com/forum/viewpost47864.html">Forum topic</a></li>
			</ul>
			</li>
			
			<li><strong>NMR (HNMR, CNMR Prediction, ...)</strong>
				<ul>
					<li>NMR predictor considers novel coupling types.</li>
					<li>Updated trainingset has improved the NMR shift prediction accuracy in both 1H and 13C NMR predictors.</li>
					<li>1H NMR calculation has become faster as no 3D conformer generation is applied.</li>
				</ul>
			</li>
			
			<li><strong>Geometry (Topology Analysis, Geometry, PSA, MSA)</strong>
				<ul>
					<li>Setting any ChemAxon supported aromatization method (general, basic, loose)
					is available in TopologyAnalyserPlugin which affects the result of many topology 
					analyser calculations (e.g., aromatic atom/bond/ring count). </li>
				</ul>
			</li>
			
			<li><strong>Chemical Terms</strong>
				<ul>
					<li>New <code>sortableFormula()</code> function is introduced. The generated 
					sortable formula can be used to sort formulas in alphabetical order. 
					<a href="../chemicalterms/EvaluatorFunctions.html#sortableformula">Documentation</a></li>
				</ul>
			</li>			
		</ul>	
	</li>
	<li><strong>cxcalc</strong>
		<ul>
			<li>pKa training library can be used without relying on user home. Use option 
			<code>--correctionlibrarypath</code> to read custom training file.</li>
			<li>New <code>sortableFormula</code> option is introduced. The generated sortable 
			formula can be used to sort formulas in alphabetical order.</li>
		</ul>
	</li>
	<li><strong>cxtrain</strong>
		<ul>
			<li>New option, <code>-p</code>, has been introduced to save the training result to custom path. </li>
		</ul>
	</li>	
	<li><strong>Automapper</strong>
		<ul>
			<li>New Automapper tool is introduced. It maps reactions more accurately. <a href="../automapper/automapper.html">Documentation</a> </li>
			<li>Automapper tool requires Standardizer license. </li>			
			<li>API documentation: <a href="https://www.chemaxon.com/marvin/help/developer/beans/api/com/chemaxon/mapper/AutoMapper.html"><code>com.chemaxon.mapper.Automapper</code></a> </li>
		</ul>
	</li>
	
	<li><strong>Structure Checker</strong>
		<ul>
			<li>"Traditional nitrogen representation allowed" option is available in Valence Error 
			Checker. <a href="../structurechecker/checkerlist.html#valenceerror">Documentation</a></li>
			<li><i>External structure checker and fixer manager</i> is integrated into Structure Checker GUI 
			application. It is accessible from the preferences menu as 
			"Checker/Fixer Manager". <a href="../structurechecker/structurechecker.html#externalchceckers">Documentation</a></li>
		</ul>
	</li>
</ul>

<h4>Known issue</h4>
<ul>
	<li><strong>MarvinSketch Applet</strong>
		<ul>
			<li>External checker classes do not load if browser uses Java SE 6. 
			Please, use Java SE 7 for successful checker class loading.</li>
		</ul>
	</li>	
</ul>

<h4>Bugfixes </h4>
<ul>
	<li><strong>MarvinSketch Menu</strong>
		<ul>
			<li>The "Recent Files" list was not updated with the new file name when the file contained multiple structures and more records were selected.</li>
		</ul>
	</li>
	<li><strong>Painting</strong>
		<ul>
			<li>There was a gap between the lines of the double bond and the single bonds when two single bonds connected to the same (non-visible) carbon atom on one end of a double bond.</li>
			<li>Electron flow arrows partially hid lone pairs in some cases.</li>
		</ul>
	</li>
	<li><strong>Clipboard handling</strong>
		<ul>
			<li>When copying and pasting between Marvin applications in Linux, an exception was thrown which froze the application where the copy operation had been performed. The exception originated from 
					the applied freehep library, which has been updated.</li>
		</ul>
	</li>
	<li><strong>MarvinSpace</strong>
		<ul>
			<li>3D alignment: MCS mode is fixed to align molecules in 3D.</li>
		</ul>
	</li>
	<li><strong>Image I/O</strong>
		<ul>
			<li>Image export did not remember the last folder used for saving.</li>
			<li>Molecule source was not saved in jpg, png, and svg files when "Save As Image" was selected. <a href="https://www.chemaxon.com/forum/ftopic10044.html">Forum topic</a></li>
		</ul>
	</li>
	<li> <strong>Import/Export</strong>
		<ul>
			<li> <strong>MOL, SDF, RXN, RDF</strong>
				<ul>
					<li> Structures that contained two S-groups
					with exactly the same atoms imported from 
					MDL MOL format could cause 
					<code>java.lang.RuntimeException</code>.
					<a href="https://www.chemaxon.com/forum/ftopic10193.html">Forum topic</a></li>
					<li> Property name was imported as its value
					from SDF file when the property name 
					contained square brackets. </li> 
					<li> Exporting a peptide sequence containing bridge between two ungrouped amino 
					acids to MOL format (V2000 or V3000), then importing it back, changed the 
					coordinates of the contracted groups. </li>
				</ul>
			</li>
			<li> <strong> CDX/CDXML </strong>
				<ul>
					<li> CDX files created by ChemDraw 9.0.1 containing abbreviated groups with no 
					attachment points was not  
					imported. </li>
					<li> ChemDraw CDX files containing textboxes
					 with Chinese characters were not imported.
					</li> 
					<li> Chemical formula-like subscript of a 
					Superatom S-group was read in reverse order 
					from CDX when the group was to the left of the fragment. </li>
					<li> Superatom S-groups not having 
					crossing bonds were read as textbox from 
					CDX files.  </li>
					<li> CDX files exported from ChemDraw 
					5.0 and containing contracted 
					Superatom S-group was not imported.</li>
					<li> CDX import read half headed arrows
					as full headed.</li>
				</ul>
			</li>
			<li><strong>CXSMILES/CXSMARTS</strong>
				<ul>
					<li> CXSMARTS export did not keep CIS/TRANS 
					information for symmetrical structures. </li>
					<li>Extended part of CXSMARTS was not 
					written correctly for any halogen query 
					atoms.  </li>
				</ul>
			</li>
			<li><strong>SMILES/SMARTS</strong>
				<ul>
					<li> Unique SMILES string generation for a 
					fragment
					could have differed depending on the fragment 
					count of the molecule </li>

				</ul>
			</li>
			<li><strong>Peptide</strong>
				<ul>
					<li> User defined custom amino acid 
					dictionary caused wrong custom amino acid 
					structure or
					<code>java.lang.ArrayIndexOutOfBoundsException</code> 
					during peptide import. </li>
				</ul>
			</li>
			<li><strong>Name to Structure (n2s)</strong> 
				<ul> 
					<li>When calling a custom name-to-structure webservice, names with special 
					characters (including space) were not URL encoded, possibly resulting in failed 
					conversions for such names.</li>
					<li>Name containing fused rings and converted to all uppercase letters were not recognized.</li>
					<li>Name to Structure could get stuck in a deadlock during the first usage when 
					that occurred concurrently in several threads.</li>
					<li>When using the "name converters" API, converters were not properly removed 
					after using thread-local converters.</li>				
				</ul>
			</li>
			
			<li><strong>Document to Structure (d2s)</strong> 
				<ul> 
					<li>When interrupted while doing OCR or OSR, document to structure logged an 
					exception about the interrupt. This caused in particular a warning dialog to 
					appear in Instant JChem during a document import.</li>
					<li>Names were not recognized when they contained a complex bracketed bond locant 
					followed by a comma, such as "2(7)" in tricyclo[6.3.1.0<sup>2,7</sup>]dodeca-2(7),3,5-triene.</li>
					<li>Scrolling back in MarvinView could have caused failures for very large documents 
					(more than 1000 structures) and names with non-ASCII characters.</li>
				</ul>
			</li>
			
			<li><strong>Structure to Name(s2n)</strong> 
				<ul> 
					<li><code>name:source</code> returned an empty name for empty structures instead of the actual stored name.</li>
					<li>The name "perimidine" was not attributed to the right structure.</li>
				</ul>
			</li>				
		</ul>
	</li>		
	<li> <strong> Molecule Representation</strong> 
		<ul>
			<li> <strong>Implicit/Explicit Hydrogen Conversion</strong>
				<ul>
					<li> Removing explicit hydrogens on some 
					larger PubChem compounds caused 
					infinite loop. </li>
				</ul>
			</li>
		</ul>
	</li>
	<li> <strong>Clean 2D </strong>
		<ul>
			<li> Partial clean misplaced the molecule when only one atom
			 was fixed.</li>
			<li> Clean 2D failed to preserve cis/trans stereo 
			information in some cases. </li>
		</ul>
	</li>
	<li><strong>Calculation</strong>
		<ul>
			<li><strong>NMR (HNMR, CNMR Prediction, ...)</strong>
				<ul> 
					<li>PDF export of NMR spectra was failed when the generated new page contained a blank line only.</li>
					<li>When displaying NMR spectra of selected tautomers, the background of the 
					tautomer preview window was black in Linux OS.</li>
				</ul>
			</li>
		</ul>
	</li>
</ul>
<h4> Deprecation </h4>
<ul>
	<li> <strong>Deprecated methods</strong>
		<ul>
			<li> <strong>Molecule Representation </strong>
				<ul>
					<li>Valence Check:  
				 <code> MoleculeGraph.VALENCE_CHECK_AMBIGUOUS,
					MoleculeGraph.ValenceCheckState,
					MoleculeGraph.setValenceCheckState(ValenceCheckState),
					MoleculeGraph.getValenceCheckState()
					MoleculeGraph.getPossibleAttachmentPoints(MolAtom)
						</code></li>
					<li>S-groups: 
				<code> SuperatomSgroup.getAttachAtoms(),
                                       SuperatomSgroup.getLegalAttachAtoms(),
                                       SuperatomSgroup.getFreeLegalAttachAtoms(),
                                       SuperatomSgroup.isFreeLegalAttachAtom(MolAtom),
                                       SuperatomSgroup.isLegalAttachment(MolAtom),
                                       SuperatomSgroup.sortXBonds(),
				       MolAtom.getAttach(),		
				       MolAtom.setAttach(int),
				       MolAtom.setAttach(int, Sgroup)
				</code></li>
					<li> Elemental Analysis:
				<code> ElementalAnalyser.setMolecule(SMolecule) </code></li>
					<li> Other deprecated methods  
			    		   <code> Molecule.exportToFormat(String), 
				   		  Molecule.exportToBinFormat(String),
				   		  Molecule exportToObject(String) 
			   </code> throws <code>java.lang.IOException</code>. </li>
				</ul>
			</li>
		
		<li><strong>Structure to Name (s2n)</strong>
		<ul>
			<li>The method 
			<code>chemaxon.marvin.io.formats.name.nameexport.IUPACNamer.generateName(Molecule)</code>
			, though not documented, was public for internal reasons, and used a non-thread-safe way 
			to receive options. It is now deprecated in favor of <code>chemaxon.formats.MolExporter(Molecule, String format)</code>
			, where format can be for instance "name" for IUPAC name, "name:t" for traditional name. 
			See https://www.chemaxon.com/marvin/help/formats/name-doc.html#export for all possible options.</li>
		</ul>
		</li>
	<li><strong>Automapper</strong>
	<ul>
		<li><code>chemaxon.marvin.modules.AutoMapper</code></li>
	</ul>
	</li>
	</ul>
	</li>
	<li> <strong> Removed methods</strong>
		<ul>
			<li> <strong>Molecule Representation</strong>
				<ul>
					<li> <code> MolAtom.setMassnoIfKnown(String),
						    MolAtom.isNobleGas(),
						    MolAtom.isArrowEnd(),
						    MolAtom.valenceCheck(),
						    MolAtom.getEdgeCount(),
						    MolAtom.getEdge(int),
						    MolAtom.getEdgeTo(MolAtom),
						    MolAtom.haveSimilarEdges(MolAtom),
				</code></li>
					<li> <code> MolBond.ARROW(), 
						    MolBond.isCoordinative(), 
						    MolBond.isArrow(),
						    MolBond.getNode1(),
						    MolBond.getNode2(),
						    MolBond.getOtherNode(MolAtom),
						    MolBond.cloneEdge(MolAtom a1, MolAtom a2)
				</code></li>
					<li> <code> MoleculeGraph.AROM_CHEMAXON, 
						    MoleculeGraph.AROM_DAYLIGHT(),
						    MoleculeGraph.setSetSeqs(int id),
						    MoleculeGraph.getAromrings(),
						    MoleculeGraph.getNonAromrings(),
						    MoleculeGraph.getAromrings(int),
						    MoleculeGraph.getNonAromrings(int),
						    MoleculeGraph.mergeFrags(),
						    MoleculeGraph.getFragIds(),
						    MoleculeGraph.getfindFragById(int fragId, MoleculeGraph frag),
						    MoleculeGraph.getNode(int),
						    MoleculeGraph.setNode(int, MolAtom),
						    MoleculeGraph.insertNode(int, MolAtom),
						    MoleculeGraph.removeNode(MolAtom),
						    MoleculeGraph.removeNode(MolAtom, int),
						    MoleculeGraph.removeNodeVector(),
						    MoleculeGraph.getEdgeCount(),
						    MoleculeGraph.getEdge(int),
						    MoleculeGraph.setEdge(int, MolBond),
						    MoleculeGraph.insertEdge(int, MolBond),
						    MoleculeGraph.insertEdgeInOrder(MolBond, MolBond[]),
						    MoleculeGraph.replaceEdge(MolBond, MolBond),
						    MoleculeGraph.removeEdge(MolBond),
						    MoleculeGraph.getEdgeArray(),
						    MoleculeGraph.getEdgeVector(),
						    MoleculeGraph.getAllEdges(),
						    MoleculeGraph.regenEdges(),
						    MoleculeGraph.sortEdgesAccordingTo(MolBond[]),
						    MoleculeGraph.mergeNodes(MolAtom that, MolAtom),
						    MoleculeGraph.mergeFrags(int, int)
						
			       </code></li>
					<li><code> Molecule.mergeFrags()</code></li>
					<li> <code> RgMolecule.setAbsStereo(boolean, int, int),
						    RgMolecule.createMol(String),
						    RgMolecule.getBtab()
			       </code></li>
					<li> <code> RxnMolecule.getBtab(),
						    RxnMolecule.getStructureCount(int),
						    RxnMolecule.getStructure(int, int),
						    RxnMolecule.addStructure(Molecule, int),
						    RxnMolecule.removeStructure(int, int)
			       </code></li>
					<li> <code> CEdge, CNode, CGraph </code></li>
					<li> <code> MDocument.setGUIProperyContainer(MPropertyContainer) </code></li>
				</ul>	
			</li>
		</ul>
	</li>
</ul>

<h3><a id="v51240" class="anchor"></a>May 6th, 2013: Marvin 5.12.4</h3>
<h4>Bugfixes </h4>
<ul>
	<li><strong>Import/Export </strong>
		<ul>
			<li><strong>MOL, SDF, RXN, RDF </strong>
				<ul>
					<li> Valid MOL file header was not recognized and therefore the file was not imported. 
<a href="https://www.chemaxon.com/forum/ftopic10483.html">Forum topic</a> </li>
 </li>
				</ul>
			</li>
		</ul>
	</li>
</ul>

<h3><a id="v51230" class="anchor"></a>April 9th, 2013: Marvin 5.12.3</h3>
<h4>Bugfixes </h4>
<ul>
	<li><strong>Import/Export </strong>
		<ul>
			<li><strong>Name to Structure (n2s)</strong> 
				<ul> 
					<li>Names with <i>ylium</i> and <i>uide</i> suffixes are now supported.</li>
				</ul>
			</li>
		</ul>
	</li>
	
	<li><strong>Calculation</strong>
		<ul>
			<li><strong>NMR (HNMR, CNMR Prediction, ...)</strong>
				<ul> 
					<li>Coupling of a nucleus with a group of magnetically equivalent nuclei was not handled properly.</li>
					<li>NMR Predictor did not consider negative coupling constant values.</li>
				</ul>
			</li>
		</ul>
	</li>
</ul>

<h3><a id="v51220" class="anchor"></a>March 28th, 2013: Marvin 5.12.2</h3>
<h4>Bugfixes </h4>
<ul>
	
	<li><strong>Molecule Representation </strong>
		<ul>	
			<li> Conversion from explicit hydrogen to implicit one
			     removed stereo centers not having explicit 
			     hydrogen ligand.
                             <a href="https://www.chemaxon.com/forum/ftopic10262.html">Forum topic</a> </li>
		</ul>
	</li>
	<li><strong>Import/Export </strong>
		<ul>
			<li><strong>SMILES/SMARTS</strong>
				<ul>
					<li> Non ring bond information were imported
					 as query strings from SMARTS. </li>
					<li> After SMARTS import, those atoms that 
					had no explicit aromatic property but 
					had aromatic bond got query aromaticity 
					property.  </li>

				</ul>
			</li>
		</ul>
	</li>
</ul>

<h3><a id="v51210" class="anchor"></a>March 20th, 2013: Marvin 5.12.1</h3>
<h4>New features and improvements </h4>
<ul>
	
	<li><strong>Import/Export </strong>
		<ul>	
			<li> <strong>CDX/CDXML </strong>
				<ul>
					<li> <i>S</i> orbitals and oval shaped <i>s</i> or <i>p</i> orbitals are imported from CDX/CDXML.  
					     <a href="https://www.chemaxon.com/forum/ftopic9926.html">Forum topic</a></li>
				</ul>
			</li>
		</ul>
	</li>
</ul>
<h4>Bugfixes</h4>
<ul>
	<li><strong>Painting</strong>
		<ul>
			<li>Charge symbol on carbon atoms was missing when the atom numbers were visible and 
				the display of carbon atom labels was turned off.</li>
			<li>When two atoms had more than one electron flow arrows between them, the electron flow arrows overlapped each other.</li>
			<li>The second electron flow arrow started from a wrong position when a single electron and an electron pair flow arrow started from an atom which had a lone pair and a radical as well.</li>
		</ul>
	</li>
	<li><strong>Editing</strong>
		<ul>
			<li>Atom Lists and NOT Lists could not be created by typing atomic symbols separated with 
				commas (<i>e.g.</i>, "f,br,cl" or "!f,br,cl").</li>
		</ul>
	</li>
	<li><strong>Import/Export </strong>
		<ul>	
			<li> <strong>MRV/CML </strong>
				<ul>
					<li>  MRV and CML export wrote out characters incorrectly 
					      which are not supported by the character set.  </li>
				</ul>
			</li>
			<li> <strong>MOL, SDF, RXN, RDF </strong>
				<ul>
					<li> SDF files having invalid header could not been imported. </li>
					<li>  Deuterium and tritium isotopes were converted to simple 
					      hydrogen atom if a molecule was exported to 
					      ChemAxon compressed MOL format (CSMOL). </li>
				</ul>
			</li>
			<li> <strong> SMILES/SMARTS </strong>
				<ul>
					<li> <code>MolExporter.exportToObject()</code> added an extra newline to SMILES. </li>
					<li> Nitrogens connecting two aromatic rings had radical after import 
					     if nitrogen was bracketed in the SMILES representation. 
					     <a href="https://www.chemaxon.com/forum/viewpost46950.html">Forum topic</a></li>
				</ul>
			</li>
			<li> <strong>InChi/InChiKey </strong>
				<ul>
					<li>Absolute stereo flag was missing during 
					   InChi export/import and InChiKey export.  
					   <a href= "https://www.chemaxon.com/forum/ftopic9655.html ">Forum topic</a></li>
					 
				</ul>
			</li>
		</ul>
	</li>
	<li><strong>Molecule Representation </strong>
		<ul>	
			<li> <strong>Valence check </strong>
				<ul>
					<li> Number of added implicit Hydrogen atoms were incorrect in some cases for positively charged sulfur atom.  </li>
				</ul>
			</li>
		</ul>
	</li>	
	<li><strong>Calculation</strong>
		<ul>
			<li><strong>Isomers (Tautomers, Stereoisomers)</strong>
				<ul>
					<li>After canonical tautomer generation, the information of "double cis or trans" bond type might have been lost in certain cases. <a href="https://www.chemaxon.com/forum/ftopic10216.html">Forum topic</a> </li>
				</ul>
			</li>
		</ul>
	</li>
</ul>

<h3><a id="v51200" class="anchor"></a>March 5th, 2013: Marvin 5.12.0</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>MarvinSketch GUI</strong>
		<ul>
			<li>Typing abbreviated group names is now case sensitive. <a href="../sketch/sketch-basic.html#abbreviatedgroups">Documentation</a></li>
		</ul>
	<li><strong>MarvinSketch Dialog</strong>
		<ul>
			<li>"Cancel" button has been added to the "Customize" dialog.</li>
		</ul>
	</li>
	
	<li><strong>Painting</strong>
		<ul>
			<li>The font size and type of circled charge symbols can be modified on the "Display" tab of the "Preferences" dialog. <a href="../sketch/gui/dialogs.html#display">Documentation</a></li>
			<li>Position variation bond has been recolored for better visibility in a projected presentation.</li>
		</ul>
	</li>
	
	<li><strong>Clipboard handling</strong>
		<ul>
			<li>When pasting unrecognized format onto the canvas, "Import as" dialog appears, and the user can choose the correct format.</li>
			<li>Structures can be copied as "Daylight SMARTS" and "ChemAxon SMARTS (CXSMARTS)" formats. <a href="../datatransfer.html#clipboard.formats">Documentation</a></li>
		</ul>
	</li>
	
	<li><strong>MarvinView GUI</strong>
		<ul>
			<li>Double-clicking on a molecule in MarvinView to open it in a separate window does not result in the disappearance of the original molecule from MarvinView while the separate window is open.</li>
			<li>The original molecule does not disappear from MarvinView while it is edited in MarvinSketch.</li>
		</ul>
	</li>
	
	<li><strong>Applet</strong>
		<ul>
			<li>New applet parameter for specifying the URL of a name recognition and import service: <a href="sketchman.html#parameters.namingWebServiceURL">namingWebServiceURL</a></li>
		</ul>
	</li>
	
	<li><strong>Import/Export</strong>
		<ul>
        	<li><strong>SKC</strong> 
				<ul> 
					<li> SKC import has been improved.</li>
				</ul>
			</li>
        	<li><strong>CDX/CDXML</strong> 
				<ul> 
					<li> Mapping of reaction agents can be imported. </li>
				</ul>
			</li>
			
			<li><strong>Name to Structure (n2s)</strong> 
				<ul> 
					<li>Initial support for Chinese IUPAC and common names has been added, e.g., 2-氨基-4-硝基苯酚.</li>
					<li>Names with extra closing brackets can be converted when OCR error correction mode is enabled.</li>
					<li>Name to structure now also works when bridge and spiro numberings are represented by using unicode superscript numbers, such as: tetracyclo[********<sup>2,6</sup>.0<sup>7,12</sup>]octadecane.</li>
					<li>Custom webservice to extend name to structure conversion - for instance using corporate IDs (e.g., ABC0001234)  or common name dictionaries in addition to the default one - is supported. <a href="../sketch/gui/dialogs.html#SaveLoad">Documentation</a></li>
					<li>Automatically fix and import spiro names where the superscript formatting has been lost, for instance, after copy-pasting. 
					As an example, trispiro[********.26.23]pentadecane will be imported as if from the correct form, 
					trispiro[*******<sup>9</sup>.2<sup>6</sup>.2<sup>3</sup>]pentadecane.  </li>
					<li>Name to structure now works on names of bridged ring compounds where the superscript formatting of ring numbering is not present.</li>
					<li>The custom dictionary can be encoded in UTF-8 - even if the system default 
					encoding is not UTF-8 - providing a byte order mark (BOM) for the file. This can 
					be used, for instance, to support dictionary in multiple languages on Windows OS.</li>
				</ul>
			</li>
			
			<li><strong>Document to Structure (d2s)</strong> 
				<ul> 
					<li>Invalid hexadecimal character entities (e.g., "&amp;#xblah") are now ignored instead of making the document processing fail.</li>
					<li>Image to structure conversion using OSRA is working on Mac OS X.</li>
					<li>Text OCR for scanned documents is now supported on Mac OS X.</li>
					<li>Documents compressed in xml.bz2 and xml.xz formats can be processed directly.</li>
					<li>The memory usage has been reduced when processing documents with several thousand hits. This improvement is in addition to the memory improvements already present in version 5.11.</li>
					<li>Option "<code>+/-groups</code>" has been added to enable (+) or disable (-) the conversion of groups and fragments, such as "ethyl", "nitro", "phenoxy", etc.</li>
					<li>OCR of scanned PDF documents has been improved on Windows OS thus reaching the same performance level as that of OCR on Mac OS X and Linux OSs.</li>
				</ul>
			</li>
			
			<li><strong>Structure to Name(s2n)</strong> 
				<ul> 
					<li>The speed of batch conversion using a timeout (which is enabled by default) has been increased by 
					about 15%; the earlier instability in case of timeout relying on 
					the deprecated Thread.stop method has been avoided.</li>
				</ul>
			</li>			
		</ul>
	</li>
	<li><strong>Molecule Representation</strong> 
		<ul> 
			<li> The size of the Molecule object has been decreased.</li> 
		</ul>
	</li>
	
	<li><strong>Data Transfer</strong>
		<ul>
			<li><strong>OLE</strong>
				<ul>
					<li>New settings are available for both administrators and end-users (from the "Edit > Preferences > MarvinOLE" menu) to preset the displayed OLE document in a maximized child window.</li>
					<li>MRV files are read and validated by DOM (document object model) in Marvin OLE.</li>
				</ul>
			</li>
		</ul>
	</li>
	
	<li><strong>Clean 3D</strong>
		<ul>
			<li> MMFF94 forcefield has been added to Generate3D. Use option &quot;[mmff94]&quot; to switch it on;
			use &quot;molconvert -H3D&quot; for detailed help.</li>
		</ul>
	</li>
	
	<li><strong>Calculation</strong>
		<ul>
			<li><strong>Isomers (Tautomers, Stereoisomers)</strong>
				<ul>
					<li>Performance improvement: Tautomerization will not apply &quot;Clean2D&quot; if 
					the output result has no 2D coordinates. <a href="https://www.chemaxon.com/forum/ftopic9829.html">Forum topic</A> </li>
					<li>Resonance structure is taken into account in dominant tautomer generation.</li>
				</ul>
			</li>
							
			<li><strong>Conformation (Conformers, Molecular Dynamics, ...)</strong>
				<ul>
					<li>New MMFF94 forcefield implementation has been introduced in Conformer Plugin and Molecular Dynamics Plugin. </li>
				</ul>
			</li>
			
			<li><strong>Services</strong>
				<ul>
					<li>Result structure of Marvin Services result panel can be copied to clipboard.</li>
				</ul>
			</li>
		</ul>
	</li>
	
	<li><strong>Structure Checker</strong>
		<ul>
			<li>New checkers have been introduced:
				<ul>
					<li><a href="../structurechecker/checkerlist.html#absolutestereoconfiguration">Absolute Stereo Configuration</a></li>
					<li><a href="../structurechecker/checkerlist.html#chiralflag">Chiral Flag</a></li>
					<li><a href="../structurechecker/checkerlist.html#ezdoublebond">E/Z Double Bond</a></li>
					<li><a href="../structurechecker/checkerlist.html#multiplestereocenter">Multiple Stereocenter</a></li>
					<li><a href="../structurechecker/checkerlist.html#relativestereo">Relative Stereo</a></li>
					<li><a href="../structurechecker/checkerlist.html#wigglybond">Wiggly Bond</a></li>
				</ul>
			</li>
			<li>Fixer of checker &quot;Query Atom&quot;: &quot;Convert to Carbon&quot; has been added.</li>
			<li>Fixer of checker &quot;Query Bond&quot;: &quot;Convert to Single Bond&quot; has been added.</li>
			<li>Excluded list of abbreviated groups can be specified in "Abbreviated group" checker, i.e., checker will not find (and fix) 
				abbreviated groups that are indicated in this list. <a href="../structurechecker/checkerlist.html#abbrevgroup">Documentation</a> </li>
			<li>Structure Checker configuration can be accessed	via URL from MarvinSketch, 
			Structure Checker application, and via Structure Checker API call. <a href="../structurechecker/checker.html#save">Documentation</a></li>
			<li>Integration of external structure checkers and fixers into ChemAxon products has become easier via MarvinSketch GUI. <a href="../structurechecker/checker.html#external">Documentation</a></li>
			<li>In case of more than one fixer, a logical order of priority has been preset. 
			The priority list of fixers can be found on page <a href="../structurechecker/checkerlist.html">List of available checkers</a>.</li>
		</ul>
	</li>

	<li><strong>API</strong>
		<ul>
			<li>New property change event to indicate when an editable MarvinView component is opened in MarvinSketch: <a href="viewman.html#propchanges">sketchInView</a>.</li>
			<li>New property change event to indicate when a new MarvinSketch or MarvinView window is opened from the viewer: <a href="viewman.html#propchanges">windowOpened</a>.</li>
			<li>New property change event to indicate when a MarvinSketch or MarvinView window, opened from the viewer, is being closed: <a href="viewman.html#propchanges">windowClosed</a>.</li>
		</ul>
	</li>
	
	<li><strong>Java Web Start</strong>
		<ul>
			<li>Name to Structure and Document to Structure are now available in the Java WebStart distribution of Marvin.</li>
		</ul>
	</li>
	
</ul>

<h4>Bugfixes</h4>
<ul>
	<li> MarvinSketch did not deal with java property initialization bug. 
             <a href="https://www.chemaxon.com/forum/ftopic9618.html ">Forum topic</a>
 	</li>
	
	<li><strong>MarvinSketch GUI</strong></li>
		<ul>
			<li>When a group abbreviation differed from an atomic symbol only in upper and lower case letters (e.g., NO and No), the abbreviated group could not be reached by typing.</li>
		</ul>
	
	<li><strong>MarvinSketch Dialog</strong>
		<ul>
			<li>Focus did not appear on the "Source" and "Import Name" dialogs with Java 7.</li>
		</ul>
	</li>
	
	<li><strong>Painting</strong>
		<ul>
			<li>The charge was not displayed in circle on the bracket of an S-group when the "Show charge in circle" was set on the "Preferences" panel.</li>
			<li>Charge label masked the adjacent bond in several cases.</li>
		</ul>
	</li>
	
	<li><strong>Editing</strong>
		<ul>
			<li>Deleting "extra" explicit hydrogens from an atom with valence error resulted in the 
			appearance of erroneous implicit hydrogens on this atom.</li>
			<li>When there were several disconnected structures on the canvas, partial clean was very slow.</li>
			<li>It was possible to create a molecule with R-group definition containing a single attachment point (and no other atoms or bonds).</li>
			<li>Sprouting did not work for Alias atoms.</li>
			<li>The two ways of deleting implicit hydrogens (with eraser directly or selecting them first then deleting) behaved differently.</li>
		</ul>
	</li>
	
	<li><strong>Load/Save handling in UI</strong>
		<ul>
			<li>"File &gt Open" dialog froze when an mrv file contained a dashed arrow and the preview pane was checked.</li>
			<li>When opening a multi molecule file and selecting a range that is open ended, a false 
			error message appeared, which stated that there were too many molecules selected to open.</li>
			<li>IUPAC name with alpha or beta signs could not be imported.</li>
			<li>MarvinView did not open when importing a file from command line failed.</li>
			<li>It was not possible to append a name to an existing .name file in MarvinSketch.</li>
		</ul>
	</li>
	
	<li><strong>Clipboard handling</strong>
		<ul>
			<li>It was not possible to drag a structure from MarvinView in "Translate or Drag" mode.</li>
			<li>Ctrl + V shortcut for pasting structures did not work in MarvinView in case of "Spreadsheet" view.</li>
		</ul>
	</li>
	
	<li><strong>Graphical object handling</strong>
		<ul>
			<li>MBracket object "BRACES" did not render correctly when the orientation was set as "SINGLE".</li>
		</ul>
	</li>
		
	<li><strong>Applet</strong>
		<ul>
			<li>After reloading an applet from the lifecycle cache, the propertyChange method was called in the javascript side regardless of the listenPropertyChange parameter.</li>
		</ul>
	</li>
	
	<li><strong>Image I/O</strong>
		<ul>
			<li>The source was not saved from MarvinView in png, jpg, and svg formats.</li>
		</ul>
	</li>
	
	<li><strong>Import/Export</strong>
		<ul>
			<li> Option -g of molconvert ignored only 
                            <code>MolFormatException</code>. </li>
        		<li><strong>MRV, CML</strong> 
				<ul> 
					<li> MRV import could not handle 
     contracted Superatom S-groups having less atoms than crossing bonds. </li>
					<li> MRV export used operating system 
                                specific line ending, '\n'. </li>
					<li> CML import failed on files that 
                                           contained non-ASCII characters. </li>
				</ul>
			</li>
        		<li><strong>MOL, SDF, RXN, RDF</strong> 
				<ul> 
					<li> Superatom S-group brackets were 
                                             imported from MDL files. </li>
				</ul>
			</li>
        		<li><strong>CXSMILES/CXSMARTS</strong> 
				<ul> 
					<li> CXSMARTS I/O lost the charge from 
                                             generic metal atom.  </li>
				</ul>
			</li>
        		<li><strong>CDX/CDXML</strong> 
				<ul> 
					<li> Reaction agent not having 
reactant and product (e.g., &gt;CC1=CC=CC=C1&gt;) was read as reactant from CDXML files. 
				</ul>
			</li>
			
			<li><strong>Name to Structure (n2s)</strong> 
				<ul> 
					<li><em>RS</em> - as the marker of racemates - was not converted properly, for instance in (2RS)-1-(Adamantan-1-ylamino)-3-phenoxypropan-2-ol. </li>
					<li>Lower CamelCase chemical names (such as 2-cyclopropylPiperazine) were not converted to chemical structures.</li>
					<li>Common names, which are also non-chemical names, were recognized as chemical names 
					during automatic format recognition, e.g., &quot;Royal Blue&quot;.</li>
					<li>E/Z bonds were sometimes set to the opposite value (in cases of non-trivial CIP rules).</li>
					<li>For some rare inputs, which are not valid names, n2s created invalid structures 
					that could be exported to mol format but could not be opened.</li>
					<li>Both &quot;phenylene&quot; and &quot;phen-1,3-ylene&quot; were wrongly converted to benzene. 
					Now all forms convert to the correct structure with two attachment points, 
					including &quot;m-phenylene&quot;, &quot;1,3-phenylene&quot;, and &quot;phen-1,3-ylene&quot; (similarly for the o-, p-, 1,2-, and 1,4- forms).</li>
				</ul>
			</li>
			
			<li><strong>Document to Structure (d2s)</strong> 
				<ul> 
					<li>Processing of multiple XML documents in concurrent threads was slow and sometimes skipped some hits.</li>
					<li>Names separated by the &quot;non-breaking space&quot; character were not recognized.</li>
					<li>Some names split by extra spaces were not found.</li>
					<li>English names were not recognized in Japanese documents when the ideographic comma character followed them.</li>
					<li>SMILES with aromatic atom after non-aromatic ones (&quot;Cc1...&quot;) and with triple(#) or explicit single(-) bonds were not recognized.</li>
					<li>Text OCR failed when the images in PDF were encoded in a certain variant of the CMYK color space.</li>
				</ul>
			</li>
			
			<li><strong>Structure to Name(s2n)</strong> 
				<ul> 
					<li>Some characteristic group names were missing when expressed as a prefix of a 
					sulfonyl group, for instance, in &quot;propanamidosulfonyl&quot;.</li> 
					<li>Names could not be generated for structures containing &quot;-[N+]≡&quot; substituent. 
					They are now named using the &quot;azaniumylidyne&quot; IUPAC name component.</li>
				</ul>
			</li>	
			
			<li><strong>Peptide</strong> 
	 			<ul>
					<li> During automatic format 
         recognition, non-latin characters were recognized as peptide sequences.
				</ul>
			</li>
		</ul>
	</li>
	<li><strong>Molecule Representation</strong> 
		<ul>
			<li> <strong> S-groups </strong>
				<ul>
					<li> Multiple S-group expanded 
                  	incorrectly when it had an embedded S-group.</li>
 				</ul>
			</li>
			
			<li><strong>S-groups/R-groups</strong>
				<ul>
					<li>S-group creation was not possible for a structure within an R-group definition 
						of a reaction; a warning message was given.</li>
				</ul>
			</li>
		</ul>
	</li>
	
	<li><strong>Clean 2D</strong>
		<ul>
			<li> Wedge clean moved non-ring wedge bond between 
                             two rings into a ring.
             <a href="https://www.chemaxon.com/forum/ftopic8669.html">Forum topic</a>
			</li>
		</ul>
	</li>
		
	<li><strong>Calculations</strong>
		<ul>
			<li><strong>Protonation (pKa, Major Microspecies, Isoelectric Point)</strong>
				<ul>
					<li>Microspecies calculation of furan returned incorrect molecular formula at low pH.</li>
				</ul>
			</li>
			
			<li><strong>Charge (Charge, Polarizability, ...)</strong>
				<ul>
					<li>Resonant charge calculation generated exception when the major form of the 
					molecule is invoked with explicit hydrogen atoms.</li>
				</ul>
			</li>
			
			<li><strong>NMR (HNMR, CNMR Prediction, ...)</strong>
				<ul>
					<li>Outdated HOSE database caused NullPointerException when predicting 1H NMR 
					spectrum for some rare diastereotpic molecules. </li>
				</ul>
			</li>
			
			<li><strong>Isomers (Tautomers, Stereoisomers)</strong>
				<ul>
					<li>D, T count in region labels of multifragment generic tautomers was computed incorrectly.</li>
					<li>TautomerizationPlugin API did not clean the explicit hydrogens in the result 
					molecule after tautomer generation which might have resulted in wrong stereo (R,S) 
					specifiaction on chiral centers. <a href="https://www.chemaxon.com/forum/viewtopic.php?p=47105">Forum topic</a></li>
				</ul>
			</li>
		
			<li><strong>Geometry (Topology Analysis, Geometry, ...)</strong>
				<ul>
					<li>The result of topological polar surface area (TPSA) plugin depended on the call order of setMolecule() and setpH() methods.</li>
				</ul>
			</li>
		</ul>
	</li>
	
	<li><strong>cxcalc</strong>
		<ul>
			<li>Calculation "ringcountofatom" returned only the ring count of atom number zero. <a href="https://www.chemaxon.com/forum/ftopic10050.html">Forum topic</a></li>
			<li>cxcalc returned irrelevant error message for calculations containing &quot;ANY&quot; bonds. <a href="https://www.chemaxon.com/forum/ftopic10095.html">Forum topic</a></li>
			<li>Thole polarizablility (<code>tpol</code>) calculation didn't interrupt when clean3D failed.</li>
		</ul>
	</li>
	
	<li><strong>Backend / Core</strong>
		<ul>
			<li>Removing explicit hydrogens on some large PubChem compounds caused infinite loop. <a href="https://www.chemaxon.com/forum/ftopic10074.html">Forum topic</a></li>
		</ul>
	</li>
	
	<li><strong>Structure Checker</strong>
		<ul>
			<li>&quot;Remove Stereo Care Box&quot; was offered as fixer for results returned by &quot;Query Bond Checker&quot;.</li>
			<li>Fixer "Expand group" not only expanded but also ungrouped the abbreviated groups when using it via <code>structurechecker</code> command-line.</li>
		</ul>
	</li>
	
	<li><strong>API</strong>
		<ul>
			<li>The default value of parameters chargeWithCircle and lonePairsAsLine has been changed. The current default value of these parameters is "false".
				On the "Preferences" dialog, the "Restore default" button sets both parameters to "false", too.</li>
		</ul>
	</li>
</ul>
<h4>Deprecation</h4>
<ul>
	<li><strong>Structure Checker</strong>
		<ul>
			<li><a href="../structurechecker/checkerlist.html#deprecation">Deprecated checker action strings</a>: aromaticity, chiralflag, circularrgroup, missingrgroup, 
			rare, reactionmap, unusedrgroup, valence, wedge.</li>
			<li><a href="../structurechecker/checkerlist.html#deprecation">Deprecated fixer action strings</a>: clearabsstereo, aliastocarbon, crossedtowiggly, converttosingle.</li>
			<li>Deprecated fixer class names: AbsentChiralFlagFixer, AbsoluteStereoFixer, 
			CrossedDoubleBondFixer, CovalentCounterionFixer, ExplicitHydrogenFixer, IsotopeFixer, 
			RgroupReferenceFixer, WigglyDoubleBondFixer. </li>
		</ul>
	</li>
</ul>

<h3><a id="v51150" class="anchor"></a>January 14th 2013: Marvin 5.11.5</h3>
<h4>Bugfixes</h4>
<ul>
	<li><strong>Datatransfer</strong>
		<ul>
			<li>Structures copied from ChemDraw, Accelrys Draw, or Symyx Draw could not be pasted onto MarvinSketch.</li>
		</ul>
	</li>
</ul>

<h3><a id="v51140" class="anchor"></a>November 19th 2012: Marvin 5.11.4</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>Datatransfer</strong>
		<ul>
		<li>The imageImportServiceURL setting also affects the behavior of clipboard operations (drag and drop, paste).</li>
		</ul>
	</li>
</ul>
<h4>Bugfixes</h4>
<ul>
	<li><strong>Painting</strong>
		<ul>
			<li>Rounding error occurred in case of nearly 180 degree bond angle and it caused overflow error when resizing the structure.</li>
		</ul>
	</li>	
			
	<li><strong>Import/Export</strong>
		<ul>
			<li> New export option, BOM, is introduced for non-binary chemical file formats 
			     in order to write out Byte Order Mark (BOM) if UTF-8 encoding is used. </li>
			<li><strong>Structure to Name (s2n)</strong>
				<ul>
					<li>Since 5.11.0, names generated for bridged cyclic structures with explicit 
					bridge head hydrogen contained a spurious &quot;hydrogenio&quot; prefix.</li>
				</ul>
			</li>
		</ul>
	</li>
	<li><strong>Clean 2D</strong>
		<ul>
			<li> Clean 2D got stuck in an infinite loop when fullerene-like molecules were cleaned. </li>
		</ul>
	</li>
	<li><strong>Calculations</strong> 
		<ul>
			<li>Atom index issue might have occurred when molecule contained explicit hydrogen.</li>
			<li><strong>Protonation (pKa, Major Microspecies, Isoelectric Point)</strong>
				<ul>
					<li>"averagemicrospeciescharge" calculation in <code>cxcalc</code> and 
					"Isoelectric Point" calculation in MarvinSketch threw "ArrayIndexOutOfBoundsException" 
					in the reported cases. <a href="https://www.chemaxon.com/forum/viewtopic.php?p=45547">Forum topic</a></li>
				</ul>
			</li>
			<li><strong>NMR (HNMR, CNMR Prediction, ...)</strong>
				<ul>
					<li>The calculated chemical shifts of equivalent atoms were not consistent in case of compounds with symmetric ring systems. <a href="http://www.chemaxon.com/forum/viewtopic.php?p=45562#45562">Forum topic</a></li>
				</ul>
			</li>
		</ul>
	</li>
</ul>

<h3><a id="v51130" class="anchor"></a>October 20th 2012: Marvin 5.11.3</h3>
<h4>Bugfixes</h4>
<ul>
	<li><strong>MView applet</strong>
		<ul>	
			<li>Atom and Bond set coloring was not working properly when Marvin View Applet was called from JavaScript.</li>
			</li>
		</ul>
	</li>	
	<li><strong>Image I/O</strong>
		<ul>	
			<li>When imageImportServiceURL startup option was not set for MarvinSketch, the same setting on the Preferences panel was lost. By default shortcuts and startup scripts do not specify this command line startup option.</li>
		</ul>
	</li>
	<li><strong>Java WebStart</strong>
		<ul>	
			<li>chemaxon-core.jar, fontbox-1.7.1.jar and pdfbox1.7.1.jar were missing.</li>
		</ul>
	</li>
	<li><strong>Calculator Plugins</strong>
		<ul>	
			<li>Tautomerization Plugin threw exception during generic tautomer generation if the input structure contained metal ions with valence error.</li>
		</ul>
	</li>
</ul>

<h3><a id="v51120" class="anchor"></a>October 12th 2012: Marvin 5.11.2</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>Image I/O</strong>
	<ul>
		<li>Recently added rendering options are now available to be set from MolPrinter API (Absolute label visibility, Peptide display type, R-group visibility, Any bond style, Lone pair rendering style, Charge rendering style).
		<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/MolPrinter.html">Documentation</a></li>
	</ul>
	</li>

	<li><strong>MSketch GUI</strong>
	<ul>
		<li>A new "<a href="https://www.chemaxon.com/marvin/help/applications/msketch.html">imageImportServiceURL=[URL]</a>" program argument was added to the MarvinSketch application.</li>
	</ul>
	</li>
	<li><strong>MSketch applet</strong>
	<ul>
		<li>A new "<a href="http://www.chemaxon.com/marvin/help/developer/sketchman.html#parameters.imageImportServiceURL">imageImportServiceURL</a>" was added as an applet parameter.</li>
	</ul>
	</li>
	<li><strong>Graphical object handling</strong>
	<ul>
		<li>When an MMidPoint object was set as an end point for an MPolyLine, getting the MMidPoint location caused a StackOverFlowError. </li>
	</ul>
	</li>

	<li><strong>Import/Export</strong>
		<ul>
			<li><strong>Document to Structure (d2s)</strong>
				<ul>
					<li>Names broken over two lines with a hyphen (-) are now recognized.</li>
					<li>Names followed by a superscript text, for instance, a reference or footnote number (e.g., "aspirin<sup>11</sup>") are now recognized. </li>
				</ul>
			</li>
			<li><strong>Name to Structure (n2s)</strong>
				<ul>
					<li>In some cases, such as "4-methylthiophenylmethyl", there is an ambiguity 
					whether "thiophenyl" refers to a compound derived from thiophene or thiophenol. 
					Name to Structure now gives priority to the thiophenol related compound 
					interpretation; though, "thiophenyl" by itself will still be supported as 
					thiophene derivatives. </li>
				</ul>
			</li>
		</ul>
	</li>
</ul>
<h4>Bugfixes</h4>
<ul>
	<li><strong>Painting</strong>
	<ul>
		<li>If R-group visibility was turned off and any of the bonds had label(s) to paint, an ArrayIndexOutOfBounds exception was thrown.</li>
	</ul>
	</li>
	<li><strong>Image I/O</strong>
	<ul>
		<li>Display parameters of charge, lone pair, peptide could not be set for molexporter. The default values were charge "in a circle", lone pair "as line", peptide "three letter format". Image copy also used these values.</li>
	</ul>
	</li>
	<li><strong>Import/Export</strong> 
		<ul>
			<li><strong>MOL, SDF, RXN, RDF</strong>
				<ul>
					<li> Aliphatic query properties of atoms with query string were not read from MDL formats.</li>
					<li> After importing Extended MOL files that contain superatom S-groups the 
					orientation of S-groups could be changed.   </li>
					<li> Atom containing both aliphatic and unsaturated query properties were exported incorrectly to MDL 
					formats. </li>
					<li> SDF import returned structure with incorrect S-group embedding. </li>
				</ul>
			</li>
			<li><strong>SMILES/SMARTS</strong>
				<ul>
					<li> SMILES T* option did not export all SDF fields, but only those which appeared in the first molecule. </li>
				</ul>
			</li>
		</ul>
	</li>
	<li><strong>Molecule Representation</strong> 
		<ul>
			<li> <strong> S-groups </strong>
				<ul>
					<li> Two superatom S-groups being each others' parents caused infinite loop. In 
					these cases, now <code>java.lang.IllegalStateException</code> is thrown. </li>
				</ul>
			</li>
			<li><strong>Valence Check</strong>
				<ul>
					<li>  Phosphorous atom in hexafluorophosphate was not accepted by Valence Check. <a href="https://www.chemaxon.com/forum/ftopic9492.html">Forum topic</a>, <a href="https://www.chemaxon.com/forum/ftopic9678.html">Forum topic</a>  </li>
				</ul>
			</li>
		</ul>
	</li>
	<li><strong>Stereochemistry</strong>
		<ul>	
			<li> Cloning of BicyclostereoDescriptor in RxnMolecules threw <code>java.lang.ArrayIndexOutOfBoundException</code>. </li>
		</ul>
	</li>
	<li><strong>Clean 2D</strong>
		<ul>	
			<li>Terminal methyl-group in phosphate-ester was cleaned incorrectly.  </li>
			<li>Clean2D could not handle condensed adamantane derivatives. <a href="https://www.chemaxon.com/forum/ftopic9569.html">Forum topic</a></li>
		</ul>
	</li>
	<li><strong>Calculations</strong>
		<ul>	
			<li><strong>Other (HBDA, Huckel Analysis, ...)</strong>
				<ul>
					<li>The <code>--pH</code> command line option did not work in hydrogen bond acceptor-donor calculation. </li>
				</ul>
			</li>
		</ul>
	</li>	

	<li><strong>Structure Checker</strong>
		<ul>	
			<li>If fixer action was not defined, default fixer was not applied in <code>structurechecker</code> command line tool. </li>
		</ul>
	</li>
</ul>

<h3><a id="v51110" class="anchor"></a>September 26th 2012: Marvin 5.11.1</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>Import/Export</strong>
		<ul>
			<li><strong>Document to Structure (d2s)</strong>
				<ul>
					<li>Startup time of document to structure has been 
					significantly reduced. This is especially important when 
					processing very short text documents, such as a single sentence or paragraph.
					The speedup in such cases can be up to 10 times faster. </li>
				</ul>
			</li>
		</ul>
	</li>
</ul>
<h4>Bugfixes</h4>
<ul>
	<li><strong>Calculations</strong>
		<ul>	
			<li><strong>NMR (HNMR, CNMR Prediction, ...)</strong>
				<ul>
					<li>NMR Prediction did not use the mixed prediction algorithm, so NMR shift predictions were less accurate.</li>
				</ul>
			</li>
		</ul>
	</li>	
	<li><strong>Chemical Terms</strong>
		<ul>	
			<li>The <code>-g</code> (ignore error) option of <b><code>evaluate</code></b> did not work when <code>-x</code> (extract) option was used. <a href="https://www.chemaxon.com/forum/viewtopic.php?t=9525">Forum topic</a></li>
		</ul>
	</li>
	<li><strong>Structure Checker</strong>
		<ul>	
			<li>WedgeCleanFixer threw ArrayIndexOutOfBoundsException when the erroneous wedge bonds were located inside an S-group. <a href="https://www.chemaxon.com/forum/ftopic9455.html">Forum topic</a></li>
		</ul>
	</li>
</ul>

<h3><a id="v51100" class="anchor"></a>September 20th 2012: Marvin 5.11.0</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>MarvinSketch GUI</strong>
		<ul>
			<li>Molecule properties like SDF fields can be added/edited/deleted in MSketch GUI.</li>
			<li>Bond properties can be added/edited/deleted in MSketch GUI.</li>
			<li>Lone pair can be displayed as a line. The option can be set from the "Preferences" panel.</li>
			<li>Charge can be displayed in a circle. The option can be set from the "Preferences" panel.</li>
			<li>An aromatic benzene template has been added to the Generic templates.</li>
		</ul>
	</li>
	
	<li><strong>Data transfer</strong>
		<ul>
			<li><strong>Clipboard handling</strong>
				<ul>
				<li>Java wrapper for <code>CXNSharedAddin</code> library has been introduced to handle tabular data transfer from/to Microsoft Office applications. </li>
				</ul>
			</li>
		</ul>
	</li>
		<li><strong>Import/Export</strong>
		<ul>
			<li><strong>CDX/CDXML</strong>
				<ul>
					<li> Graphical arrows "retrosynthetic" and "equilibrium" are imported from CDX and CDXML files. </li>
				</ul>
			</li>
			<li><strong>Name to Structure (n2s)</strong>
				<ul>
					<li>Names, such as "methyl ether", are used either to denote 
					a group (CH3-O-*) or the dimethyl ether compound (CH3-O-CH3), 
					as well. The default behavior still is to generate the 
					complete compound, but a new name option "expectGroup" has 
					been added to get the group instead.</li>
					<li>A name converter can now prevent a name from being converted (even by other lower-priority 
					converters). See the API of the <a href="https://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/naming/NameConverter.html">chemaxon.naming.NameConverter</a> class.</li>
				</ul>
			</li>
			<li><strong>Document to Structure (d2s)</strong>
				<ul>
					<li>Peptide 3 letter abbreviations are now recognized in documents, for instance ValGlySerAla 
					or Val-Gly-Ser-Ala.</li>
					<li>A new option has been added to control the maximum time allowed for processing a document: format 
					option "d2s:timeout=60" to allow a maximum of 60 seconds. Default is 0 meaning infinite timeout.</li>
					<li>A new option has been added to disable the extraction of OLE embedded structures from Office documents 
					(option name: "d2s:-ole").</li>
					<li>Structures imported from the custom name dictionary now have the "Type" property set to "custom" 
					instead of "common".</li>
					<li>Memory usage has been significantly reduced when processing large PDF documents (for instance hundreds of scanned pages).</li> 
					<li>Structure image conversion by OSRA has become faster, resulting in more structures converted 
					when previously a timeout was reached.</li>
					<li>CAS names (which typically include commas) are not converted by default by d2s anymore (they are 
					still converted by n2s). The old behavior can be activated by using the "d2s:CASnames" format option.</li>
					<li>The unwanted interpretation of some common words as chemical 
					names, that are frequent especially in patents, has been avoided. 
					For example, the 'amino' group when used in the phrase 'amino acid', 
					or 'Ser' when it is not the abbreviation of Serine but 'Ser. No.' for 'serial number'.</li>
				</ul>
			</li>	
			<li><strong>Structure to Name(s2n)</strong>
				<ul>
					<li>Multiple name generation tasks can now be run in parallel at full speed to make use of 
					multiple CPUs and cores.</li>
					<li>Multiple name import tasks now run faster, as partial locking is no longer required and has been removed.</li>
					<li>Generated names now use unicode characters when needed, for 
					instance, 6λ<sup>4</sup>-thiadispiro[5.2.5<sup>9</sup>.2<sup>6</sup>]hexadecane instead of 6$l^{4}-thiadispiro[5.2.5^{9}.2^{6}]hexadecane. 
					The pure ASCII output can be generated using the "name:ascii" format option. </li>
				</ul>
			</li>				
		</ul>
		<ul>
			<li><strong>Image I/O</strong>
				<ul>
				<li>New parameter <code>imageImportServiceURL</code> has been added to the image importer module. The URL can be set on the "Preferences" dialog or as an import option. </li>
				</ul>
			</li>
			<li><strong>VMN import</strong>			
				<ul>
				<li>VMNs of type "display" are now read.</li>			
				<li>VMN abbreviated group list has been extended: carbon chains up to 50 members have been added.</li>
				<li>AV (abnormal valence) property is now stored in atom property and not set as atom valence. The VMN valence representation will be revised in a later version.</li>
				<li>Deuterium and tritium count property is handled properly in VMN (added as D and T ligands for non-homology atoms). D and T are transposed during the import in comparison to the VMN, as this represents the correct information in the majority of the MMS database.</li>
				<li>Correcting incorrect atom types in VMNs: C0 is transformed to CHK; C1 is transformed to C; homology groups 
				in ring are  transformed to XX</li>
				<li>Attachment bond type is set to parent R-atom bond type if different.</li>
				<li>VMN import option is available for switching off corrections: <a href="http://www.chemaxon.com/marvin/help/formats/vmn-doc.html#ioptions">Documentation</a> </li>
				</ul>		
			</li>
		</ul>
		</li>
	<li><strong>Molecule Representation</strong>
		<ul>
			<li>New option, <code>MolAtom.BRIDGEATOM_H</code>, is added to <code>Hydrogenize.removeHAtoms(MoleculeGraph, int)</code> method to remove hydrogens connected to bridgehead atoms. </li>
			<li><strong>Valence Check</strong>
				<ul>
					<li> Local valence check has been introduced, which calculates the valence by 
					considering only the neighboring atoms. Although the new valence check is the default, the old one is also available. <a href="../sci/ValenceCalculator.html">Documentation</a> </li>
				</ul>
			</li>
			<li><strong>S-groups</strong>
				<ul>
					<li> ElementalAnalizerPlugin calculates Polymer Formula for molecules containing SRU or RepeatingUnit S-groups. </li>
				</ul>
			</li>
		</ul>
	</li>
	
	<li><strong>Calculations</strong>
		<ul>
			<li><strong>Charge (Charge, Polarizability, ...)</strong>
				<ul>
					<li>Dipole Moment Calculation plugin: calculates the net molecular 
					polarity. <a href="../calculations/chargegroup.html#dipole">Documentation</a></li>
				</ul>
			</li>
			
			<li><strong>NMR (CNMR Prediction, HNMR Prediction)</strong>
				<ul>
					<li>New, mixed HOSE code/CXN model has been introduced, which results in more accurate NMR shift prediction.</li>
					<li>C-F and H-F couplings are considered during NMR shift calculation.</li>
					<li>Diastereotopic protons are supported in HNMR Prediction.</li>
					<li>The tautomers of the predicted molecule are accessible via the Options &gt; Select Tautomers... 
					option of the NMR GUI. The spectra of the selected tautomers with defined percent composition 
					are added to the predicted spectrum. <a href="../calculations/nmrpredict.html#tautomers">Documentation</a></li>
				</ul>
			</li>
			<li><strong>Chemical Terms</strong>
				<ul>
					<li>"molString" and "molFormat" Chemical Terms functions are accessible under the name "molConvert" as well.</li>
				</ul>
			</li>
		</ul>
	</li>
	
	<li><strong>Structure Checker</strong>
		<ul>
			<li>New Structure Checker GUI has been introduced. Besides the redesigned GUI, new features are also available:
				<ul>
					<li>Profile management;</li>
					<li>Filters in Report view.</li>
				</ul> <a href="../structurechecker/structurechecker.html">Documentation</a>
			</li>
			<li>Structure Checker is going to be moved to the MarvinBeans package. Until it is done, the new 
			Structure Checker GUI is available in both JChem and MarvinBeans packages as <i>Structure Checker JChem</i> and <i>Structure Checker</i>, respectively.</li>
			<li><b>Substructure Checker</b> feature is accessible only in <i>Structure Checker JChem</i> until the complete relocation of Structure Checker.</li>
			<li>Improved R-group Attachment Error Checker detects R-group definitions 
			having incorrect R-group attachment orders. Fixer is not available for this feature yet. </li>	
			<li>New Explicit Hydrogen Checker options are ready for use. <a href="../structurechecker/checkerlist.html#explh">Documentation</a> </li>	
			<li>Checkers that are not recognized as internal or external checker are called: "Invalid Checkers". 
			Invalid Checkers cannot be used in valid Structure Checker configurations; 
			they need to be removed to perform proper working behavior. </li>	

		</ul>
	</li>
	
	<li><strong>Build/Installation</strong>
		<ul>
		<li>Marvin Beans and JChem installers - for Linux - bundled with Java 1.6.0_33 have been introduced. </li>
<!-- 
(Windows installers are bundled with Java 1.6.0_18)
//-->
		</ul>
	</li>
</ul>
<h4>Bugfixes</h4>
<ul>
	<li><strong>MarvinSketch GUI</strong>
		<ul>
			<li>The bond related label backgrounds hid the bond partially if the bond was almost parallel with the horizontal axis.</li>
			<li>Several mnemonics were duplicated or missing in the menu of MSketch.</li>
		</ul>
	</li>
	<li><strong>Painting</strong>
		<ul>
			<li>Bond topology representation was not visible in R-groups.</li>
		</ul>
	</li>
	<li><strong>Dialogs</strong>
		<ul>
			<li>Periodic table view 'Blocks' showed Helium color coded as a part of 'p block' instead of the correct 's block'. </li>
		</ul>
	</li>
	<li><strong>Import/Export</strong>
		<ul>
			<li><strong>Load/Save handling in UI</strong>	
				<ul>
				<li>MSketch did not overwrite the former PDF file when changes were made and it was saved as a PDF again with the same file name.</li>
				</ul>
			</li>
			<li><strong>MRV, CML</strong>
				<ul>
					<li> The string value of a query property was imported as element.  </li>
				</ul>
			</li>	
			<li><strong>MOL, SDF, RXN, RDF</strong>
				<ul>
					<li> Registry number of a molecule that is a property of an other molecule was not imported from RD file.   </li>
				</ul>
			</li>		
			<li><strong>SMILES/SMARTS</strong>
				<ul>
					<li> Cis/Trans information was not exported for bonds being in two rings.  </li>
				</ul>
			</li>		
			<li><strong>CXSMILES/CXSMARTS</strong>
				<ul>
					<li> Query pseudo atoms represented by CXSMARTS lost their valence property during import. </li>
					<li> R-group definition of an Rg-molecule described in CXSMARTS was imported as CXSMILES.   </li>
				</ul>
			</li>
			<li><strong>CDX/CDXML</strong>
				<ul>
					<li>Reaction agent was not possible to read from CDXML file.  </li>
					<li>From CDXML file containing only a product of a reaction, the product was imported as reactant. </li>
				</ul>
			</li>	
			<li><strong>Name to Structure (n2s)</strong>
				<ul>
					<li>Names ending with "group", such as: "cyclohexyl group" were not imported.</li>
					<li>Chirality specified without locant was sometimes not included in the generated structure, for 
					instance in (R)-tert-butyl 3-aminopyrrolidine-1-carboxylate.</li>
				</ul>
			</li>
			<li><strong>Document to Structure (d2s)</strong>
				<ul>
					<li>Several names separated by a comma were sometimes wrongly recognized as a single name.</li>
					<li>Document to structure was leaking some memory in a way that was problematic for extremely large documents 
					(tens of thousands of structures in a single document).</li>
					<li>When using the d2s API, waiting threads could be left behind when large documents were converted but the 
					structures were not read until the end. This is now fixed, provided the client calls <code>MolImporter.close()</code>.</li>
					<li>Document to structure was not processing text from PDFs with non-standard fonts, for instance English text 
					encoded in Japanese fonts.</li>
					<li>Certain images in PDFs were not processed correctly by OSRA during optional image-to-structure conversion.</li>
					<li>Names found in documents may have included unwanted trailing characters when the next word started with 
					an open bracket, for instance, "cholesterol [28]" instead of "cholesterol". </li>
				</ul>
			</li>	
			<li><strong>Structure to Name(s2n)</strong>
				<ul>
					<li>The group "[NH3+]-" was given traditional name "aminio", instead of the correct "ammonio".</li>
					<li>Names were generated for structures with link nodes, as if the link nodes had been absent.</li>
					<li>Incorrect names were generated for structures with one attachment point. </li>
				</ul>
			</li>			
		</ul>
	</li>
	<li><strong>Molecule Representation</strong>
		<ul>
			<li>Meitnerium appeared at a wrong position in the periodic table of MarvinSketch. </li>
			<li> Explicit hydrogen that connects to an atom having more than one wedge bond was not converted to implicit one.   </li>
			<li><strong>S-groups</strong>
				<ul>
					<li>When an expanded SuperAtom-Sgroup was inside an other S-group, its attachment points had invalid implicit hydrogen count. </li>
				</ul>
			</li>
			<li><strong>Stereochemistry</strong>
				<ul>
					<li>Stereoinformation were not recognized on cyclohexane-1,3,5-triol.  </li>
				</ul>
			</li>
		</ul>
	</li>
	<li><strong>Clean 2D</strong>
		<ul>
			<li> Calling clean 2D on a 3D molecule resulted in short bond length between a Hydrogen and an other atom</li>
			<li>Clean 2D inserted wiggly bond into a Kekule form of a 0 dimensional aromatic molecule. </li>
		</ul>
	</li>
		<li><strong>Aromatization/Dearomatization</strong>
		<ul>
			<li>Basic aromatization method aromatized rings containing sulfur atom that have four ligands. </li>
			<li>Basic aromatization method aromatized rings with -S= substructure. </li>
		</ul>	
	</li>
	
	<li><strong>Calculations</strong>
		<ul>	
			<li><strong>Isomers (Tautomer, Stereoisomers)</strong>
				<ul>
					<li>Cross bond was not handled correctly by canonical tautomer generator. </li>
				</ul>
			</li>
		</ul>
	</li>

	<li><strong>Structure Checker</strong>
		<ul>	
			<li>Abbreviated Group checker options did not work as expected: 
			checker recognized both expanded and contracted groups regardless of the selected option. </li>
		</ul>
	</li>
	<li><strong>Build/Installation</strong>
		<ul>
			<li>Desktop icons were missing from the Marvin Beans for Linux installer. </li>
		</ul>
	</li>
</ul>

<h3><a name="v51050" class="anchor"></a>October 31st, 2012: Marvin 5.10.5</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>Applet</strong>
	<ul>Class reorganization for more efficient applet loading.
	</ul>
	</li>
</ul>

<h3><a name="v51040" class="anchor"></a>September 7th, 2012: Marvin 5.10.4</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>Import/Export</strong>
		<ul>
			<li>MDocSource implements Iterable&lt;Molecule&gt;. <a href="io/import.html#import_multi">Documentation</a> </li>
		<li><strong>MOL, SDF, RXN, RDF</strong>
			<ul>
				<li> New option for MDL MOL format import, <code>Fsg</code>, has been introduced to ungroup superatom s-groups with more than two attachment points.  <a href="../formats/mol-csmol-doc.html#ioptions">Documentation</a> </li>
			</ul>
		</li>
		</ul>
	</li>
</ul>	
<h4>Bugfixes</h4>
<ul>
	<li><strong>Import/Export</strong>
		<ul>
			<li><strong>MRV</strong>
				<ul>
					<li>The string value of the query property was imported as element.</li>
				</ul>
			</li>
		</ul>	
	</li>
</ul>	
	

<h3><a name="v51030" class="anchor"></a>August 13th, 2012: Marvin 5.10.3</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>MarvinView Printing</strong>
	<ul>
	<li>Resolution of PDF printing has been increased.</li>
	</ul>
	</li>
	<li><strong>Datatransfer</strong>
	<ul>
	<li>Legacy MRV '\n\ header (5.7) format is supported in Marvin OLE.</li>
	</ul>
	</li>
</ul>	
<h3><a name="v51020" class="anchor"></a>July 27th, 2012: Marvin 5.10.2</h3>
<h4>New features and improvements</h4>
<ul>
	<li> <strong>Import/Export </strong>
		<ul> 
			<li><strong> MOL, SDF, RXN, RDF  </strong> 
				<ul>
					<li> Coordination bond is written and read from extended MOL file. </li>
				</ul>
			</li>
			<li> <strong>Name to Structure (n2S)</strong>
				<ul> 
					<li>More extensive support for conversion of CAS numbers to structures. </li>
				</ul>
			</li>
		</ul>
	</li>
	<li> <strong> Clean 2D </strong>
		<ul>
			<li> New clean option, Toff, is introduced to ignore template based cleaning. <a href="../sci/cleanoptions.html">Documentation</a>, <a href="https://www.chemaxon.com/forum/ftopic8942.html">Forum topic</a>. </li>
			<li> Coordinate bond length has been increased for a better view of its arrow representation between atom labels. </li>
		</ul>
	</li>
</ul>
<h4>Bugfixes</h4>
<ul>
	<li> <strong>MarvinView</strong>
		<ul>
			<li>MarvinView did not display the SD file property if no property values were attributed to the first entries.</li>
		</ul>
	</li>
	<li> <strong>Painting</strong>
		<ul>
			<li>The bond related label backgrounds hid the bond partially if the bond was almost parallel with the horizontal axis.</li>
		</ul>
	</li>
	<li> <strong>Editing</strong>
		<ul>
			<li>Transfer button did not appear on the "Sketch new substituent..." popup window when the 'Insert&gt;New Substituent' menu item was selected. </li>
		</ul>
	</li>
	<li> <strong>Import/Export </strong>
		<ul>
			<li> Byte Order Mark (BOM) was not written out during the export. </li>
			<li> Default line separator was not unique in MolExporter.
			<li> <strong> CDX/CDXML</strong>
				<ul> 
					<li> Empty CDX and CDXML files could not been imported. </li>
				</ul>
			</li>
			
		</ul>
	</li>
	
	<li> <strong>Molecule Representation </strong>
		<ul>
			<li> <strong> S-groups</strong>
				<ul>
					<li> Contracted Superatom S-groups drawn to MarvinSketch and having only one crossing bond had no attachment point information. </li>
				</ul>
			</li>
			<li> <strong> Stereochemistry</strong>
				<ul>
					<li> Absolute chiral flag calculation terminated with "java.lang.OutOfMemoryException" for large, symmetric molecules, e.g., fullerene derivatives. <a href="https://www.chemaxon.com/forum/ftopic9348.html">Forum topic</a> </li>
				</ul>
			</li>
		</ul>
	</li>
	<li><strong>Calculations</strong> 
		<ul>			
			<li><strong>Protonation (pKa, Major Microspecies, Isoelectric Point)</strong>
				<ul>
					<li>Major microspecies calculation threw "ArrayIndexOutOfBoundsException" in the reported case. <a href="https://www.chemaxon.com/forum/viewtopic.php?t=9347">Forum topic</a></li>
				</ul>
			</li>
		</ul>
	</li>
</ul>
<h3><a name="v51010" class="anchor"></a>July 6th, 2012: Marvin 5.10.1</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>Import/Export</strong>
		<ul>
			<li> <strong>MOL, SDF, RXN, RDF </strong>
				<ul> 
					<li> The template based coding of amino acids in MOL V3000 files are read as pseudo atoms. However, the template based structure of peptides cannot be read. </li> 
					<li> Method <code>MolFileHandler.collectFileInfo()</code> returns molecule and reaction identifiers $MFMT $MIREG, $MFMT $MEREG, $RFMT $RIREG, $RFMT $REREG, $MIREG, $MEREG, $RIREG, $REREG as well.</li>
				</ul>		
			</li>	
		</ul>
	</li>
	<li><strong>Clean 2D </strong> 
		<ul> 
			<li> An option is introduced to set explicit C-H bond length to 1.0 instead of 1.54 in case of two dimensional cleaning. <a href="../sci/cleanoptions.html">Documentation</a>, <a href="https://www.chemaxon.com/forum/ftopic9060.html">Forum topic</a>. </li>
		</ul>
	</li>
	<li><strong>Calculations</strong> 
		<ul> 
			<li>"Keep explicit hydrogens" option has been added to <b>pKa</b> plugin, <b>Major Microspecies</b> plugin, and <b>Isoelectric Point</b> plugin.  </li>
		
		<li><strong>Protonation (pKa, Major Microspecies, Isoelectric Point)</strong>
			<ul>
				<li>The speed of major microspecies calculation has been improved when "Take major tautomeric form" option is used.</li>
			</ul>
		</li>	
		<li><strong>NMR </strong>
			<ul>
				<li>The accuracy of the NMR predictor has been improved.</li>
			</ul>
		</li>		
		</ul>
	</li>
</ul>

<h4>Bugfixes</h4>
<ul>
	<li><strong>Painting</strong>
		<ul>
			<li>"Absolute" label was not visible when only contracted Sgroups contained stereo information and the "Absolute" stereo flag was set on the Molecule.</li>
			<li> Joining two product structures of a reaction erased both structures. <a href="https://www.chemaxon.com/forum/ftopic9246.html">Forum topic</a>  </li>
			<li> Joining aromatic rings drawn in Kekule form sometimes caused their aromatization.  </li>
		</ul>
	</li>
	<li><strong>Editing</strong>
		<ul>
			<li>Atom labels could disappear when the structure was cleaned in 3D, rotated then cleaned in 2D.</li>
		</ul>
	</li>
	<li><strong>Import/Export</strong>
		<ul>
			<li> Byte Order Mark (BOM) was not written out during the export. </li>
			<li> <strong> MRV/CML </strong> 
				<ul>	
					<li> Molconverter could not convert files containing multiple molecules to separate MRV/CML files. </li>
					<li> Encoding could not been set during MRV and CML export. </li>
				</ul>
			</li>
			<li> <strong>MOL, SDF, RXN, RDF </strong>
				<ul>
					<li> Bond length of a molecule imported from MOL V3000 file was short, therefore the atoms overlapped.  </li>
					<li> RD file records written after an empty structure were not read correctly. </li>
				</ul>		
			</li>	
			<li> <strong>CDX</strong>
				<ul>
					<li> CDX files containing labels without chemical structures were not read. Now, these labels are read in text boxes. </li>
				</ul>
			</li>
			<li> <strong>SKC </strong>
				<ul> 
					<li> SKC import put an R-group attachment point to Superatom S-groups. </li>
					<li> Stereo information was lost at importing a peptide from an SKC file. </li>
					<li> Wedge bond information of a contracted S-group was lost during the SKC import.  </li>
				</ul>		
			</li>	
		</ul>
	</li>
	<li><strong> Molecule Representation </strong>
		<ul>
			<li> <strong>Valence check </strong> 
				<ul>
					<li> Valence check allowed any number of bonds on carbon atoms having implicit hydrogens. </li>
				</ul>
			</li>
			<li> <strong>R-groups</strong>
				<ul> 
					<li> Method <code>RgMolecule.fuse()</code> resulted inconsistent molecule when it was invoked on RgMolecules having SuperAtom S-group in its definition. </li>
				</ul>
			</li>
			<li> <strong>Stereochemistry</strong> 
				<ul>
					<li> Nitrogen being one of the fusion points of ring systems was detected as chiral when the carbon atom at the other fusion point had four explicit ligands. </li>
				</ul>
			</li>
		</ul>
	</li>
	<li><strong>Aromatization/dearomatization </strong>
		<ul>
			<li>Dearomatization failed for porphyrin-like ring systems.</li>
		</ul>
	</li>	
	<li><strong>Calculations</strong> 
		<ul>			
			<li><strong>Charge (Charge, Polarizability, Orbital Electronegativity)</strong>
				<ul>
					<li>Charge calculation was not consistent on fragmented molecules.</li>
				</ul>
			</li>
			<li><strong>Isomers (Tautomers, Stereoisomers)</strong>
			<ul>
				<li>Different rational canonical tautomers were generated for some molecules when they came from different sources (e.g., InChi and SMILES). </li>
				<li>Tautomer distribution was not calculated for various porphyrin-like structures.</li>
			</ul>
			</li>
			<li><strong>Geometry (Topology Analysis, Geometry, PSA, MSA)</strong>
				<ul>
					<li>Negative or zero Van der Waals and Solvent Accessible molecular surface area values were generated for molecules with inconsistent chemical structures. </li>
				</ul>
			</li>			
		</ul>
	</li>
	<li><strong>Structure Checker</strong>
		<ul>
			<li>Abbreviated group checker did not identify expanded and contracted groups correctly.</li>
			<li>"substructre" action string did not work in command line.</li>
		</ul>
	</li>
</ul>

<h3><a name="v51000" class="anchor"></a>June 08th, 2012: Marvin 5.10.0</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>MarvinSketch GUI</strong>
		<ul><li>New graphical arrows: equilibrium arrow, curved arrow, dashed arrow, and crossed arrow has been introduced.  <a href="../../help/sketch/gui/menubar.html#insert">Documentation</a>, <a href="https://www.chemaxon.com/forum/viewpost40611.html">Forum topic</a></li>
			<li>The built-in abbreviated groups can be easily extended and overridden with custom abbreviated group definitions. <a href="../../help/sketch/sketch-basic.html#User_defined_abbreviated_groups">Documentation</a></li>
			<li>New display option has been introduced: peptide sequence can be visualized in 1-letter form besides the original 3-letter form.</li>
			<li>Cleaning preset is added to template sets. It can be controlled on the Template library dialog.</li>
			<li>When opening an item of the Recent File list fails, the entry is deleted.</li>
			<li>All query atoms and query properties are available from menu and toolbar by customization.</li>
			<li>Names of homology groups on "Advanced" tab of "Periodic System Dialog" and in "Template Library" have been updated. </li>
			<li>The elements of the Advanced tab on the Periodic System dialog are now available from MarvinSketch by typing as shortcuts.</li>
			<li>The picture of custom defined template appears at the mouse tooltip when the name of the structure is unknown. </li>
			<li>The name of reaction arrow: "Resonance" and graphical arrow "Two-Headed Arrow" have been changed. <a href="https://www.chemaxon.com/forum/ftopic8980.html">Forum topic</a></li>
		</ul>		
	</li>
	
	<li><strong>MarvinSketch Printing</strong>
		<ul>
			<li>The page format settings on the print dialog is now stored and do not need to be reset when starting a printing job from MarvinSketch or MarvinView. <a href="https://www.chemaxon.com/forum/ftopic8737.html">Forum topic</a></li>
		</ul>	
	</li>

	<li><strong>Import/Export</strong>
		<ul>
			<li><strong>Load/Save handling in UI</strong>	
				<ul>
					<li>Disconnected molecules can be saved separately in MSketch. <a href="../../help/sketch/sketch-basic.html#save2molformat"> Documentation.</a></li>
					<li>When saving a document opened by d2s in MView, MRV is offered as default format.</li>
				</ul>	
			</li>
			<li><strong>Structure to Name (s2n)</strong>
				<ul>
					<li>Extended and updated structure to common name dictionary.</li>
					<li>L-amino acid structures are all converted to L- traditional name.</li>
				</ul>	
			</li>
			<li><strong>Name to Structure (n2s)</strong>
				<ul>
					<li>Improved conversion of systematic names, notably names expressed as amides of acids, such as "pyridine-4-carboxylic acid (2-methoxy-ethyl)-methyl-amide".</li>
					<li>Extended and updated common name to structure dictionary.</li>
					<li>All peptide abbreviations are now consistently converted to the L- stereo form structure.</li>	
					<li>Possibility to set custom name converter plugins for n2s and d2s (e.g., converting internal company compound IDs) specific to a single conversion in a thread-safe way has been introduced (see API of the new method: <a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/naming/NameConverters.html#addThreadLocal(chemaxon.naming.NameConverter, int)">chemaxon.naming.NameConverters.addThreadLocal</a>).</li>
					<li>Fragments names such as phenyl can be converted with attachment points *C1=CC=CC=C1 |$_AP1|, using the "name:+rgroups" format option.</li>
				</ul>	
			</li>
		
			<li><strong>Document to Structure (d2s)</strong>
				<ul>
					<li>Document to Structure can be instructed to process only a subset of pages in a document using the new startPage and endPage format options.</li>
					<li>The custom dictionary format option (dict=...) is available in Document to Structure.</li> 
					<li>Possibility to set custom name converter plugins for n2s and d2s (e.g., converting internal company compound IDs) specific to a single conversion in a thread-safe way has been introduced (see API of the new method: <a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/naming/NameConverters.html#addThreadLocal(chemaxon.naming.NameConverter, int)">chemaxon.naming.NameConverters.addThreadLocal</a>).</li> 
					<li>Acronyms such as BBC are not converted anymore (as BromoBenzyl Cyanide) since they most often have a non-chemical meaning. Conversion of acronyms can be enabled if desired by using the "d2s:+acronyms" format option. </li>
					<li>Fragments names such as phenyl are converted with attachment points, such as *C1=CC=CC=C1 |$_AP1|.</li>
				</ul>	
			</li>
			
			<li><strong>DNA/RNA</strong>
				<ul>
					<li>DNA nucleic acid sequence can be imported in four ways: ACGT, A-C-G-T, dAdCdGdT or dA-dC-dG-dT. RNA nucleic acid import accepts sequences like both ACGU and A-C-G-U.  </li>
				</ul>	
			</li>
		</ul>
	</li>

	<li><strong>Molecule Representation</strong>
		<ul>
			<li><strong>Molecule/atom/bond properties</strong>
				<ul>
					<li>Similarly to MolAtom, general properties are possible to be added to MolBond. </li>
 					<li> New, simple way is provided to replace isotopes.data and elements.zip used in PeriodicSystem.java. <a href="../sketch/isotopelist.html">Documentation</a>. </li>
				</ul>
			</li>
		
			<li><strong>S-groups</strong>
				<ul>
					<li>New method is introduced to set a data S-group to behave as relative after specifying its absolute position </li>
				</ul>
			</li>
			<li><strong>R-groups</strong>
				<ul>
 					<li> A new, fast method is available to delete one or more molecules from an R-group definition.  </li> 
				</ul>
			</li>
		</ul>
		
	<li><strong>Calculations</strong>
		<ul>
			<li><strong>Isomers (tautomer., reson.)</strong>
				<ul>
					<li><i>Rational tautomer generation</i> option has been introduced. Generation of <i>All tautomers</i> and <i>Canonical tautomer</i> can take this new option into consideration. <a href="../calculations/tautomers.html">Documentation</a> </li>
					<li>Improved generic tautomer generation in case of aromatic molecules.</li>
				</ul>
			</li>
				
			<li><strong>NMR Predictor</strong>
				<ul>
					<li><i>NMR Predictor</i> is available in MarvinSketch. It accurately predicts <i><sup>13</sup>C</i> and <i><sup>1</sup>H NMR</i> spectra of standard organic molecules. See <a href="http://www.chemaxon.com/products/calculator-plugins/nmr-predictor/">Product page</a> and <a href="../calculations/nmr.html">Documentation</a> for more information.</li>
				</ul>
			</li>
			<li><strong>Services</strong>
			<ul>
				<li>LocalService allows to set URL based JAR path.</li>
			</ul>
			</li>
		</ul>	
	</li>

	<li><strong>Structure Checker</strong>
		<ul>
			<li>New checker: Absent Chiral Flag checker; when the molecule is chiral and all stereo centers are precisely marked, absolute chiral flag can be applied to the molecule. <a href="../structurechecker/checkerlist.html#absentcf" target="blank">Documentation</a></li>
			<li>R-group Reference Error Checker is deprecated and has been split into three separate checkers: 
				<ul>
					<li><a href="../structurechecker/checkerlist.html#circularrg" target="blank">Circular R-group Reference Checker</a>;</li>
					<li><a href="../structurechecker/checkerlist.html#missingrg" target="blank">Missing R-group Checker</a>;</li>
					<li><a href="../structurechecker/checkerlist.html#unusedrg" target="blank">Unused R-group Checker</a>.</li>
				</ul>
			</li>
			<li><i>Partial clean</i> fixer is available for certain structure checkers: it can fix the erroneous part of the molecule exclusively.</li>			
		</ul>
	</li>

	<li><strong>Stereochemistry</strong>
		<ul>
			<li> A new method is available that finds atoms that can become (s) or (r) stereo centers after the stereo arrangement of the bonds around the atom is specified. </li>
		</ul>
	</li>
	<li><strong>API</strong>
	<ul>
			<li>New parameter to set accessibility of special nodes in Advanced tab of Periodic System dialog: <a href="../developer/sketchman.html#parameters.disableSpecialNodes">Documentation</a>
(<a href="https://www.chemaxon.com/forum/ftopic8624.html">Forum topic</a>).</li>
		</ul>
	</li>

</ul>

<h4>Bugfixes</h4>
<ul><li><strong>MarvinSketch</strong>
		<ul>
			<li>When MarvinSketch was launched from command line with -h or --help option, the application was not terminated after printing of help message.</li>
			<li>When using MSketchPane from an application, and pop it up as a modal dialog, its children could disappear behind the main application (e.g., DialogLauncher example).</li>
		</ul>	
	</li>
	<li><strong>MarvinSketch GUI</strong>
		<ul>
			<li>Scaling bug occured on the button of the template toolbar if there was a contracted group displayed on the button.</li>
			<li>"Calculations" menu did not appear in ISIS/Draw-like configuration.</li>
		</ul>	
	</li>
	<li><strong>MarvinView GUI</strong>
		<ul>
			<li>The title of the MView application window was not updated on file open and save.</li>
		</ul>	
	</li>
	<li><strong>MarvinSpace</strong>
		<ul>
			<li>MarvinSpace: JOGL 1.1 was replaced by the most recent version 2.0. This resolves various MSpace launch and initialization issues (in particular Applet problems on MacOSX). <a href="https://www.chemaxon.com/forum/viewpost37932.html&highlight=mspace+applet+osx#37932">Forum topic</a> </li>
		</ul>	
	</li>
		
	<li><strong>Applet</strong>
		<ul>
			<li>Although a valid URL was given in sketchHelp applet parameter, Marvin applet loaded the default help by selecting Help->Help Content instead of the given URL. </li>
			<li>"My templates" became unavailable in applet after modifying it from template library. <a href="https://www.chemaxon.com/forum/ftopic8215.html">Forum topic</a>.</li>
	   </ul>
	</li>
	<li><strong>Painting</strong>
			<ul>
				<li>The rb* atomic query property was not displayed at the mouse tooltip when it was set through its keyboard shortcut: ".rb*".</li> 
			</ul>
	</li>
	<li><strong>Chemical Structure Painting</strong>
		<ul>
			<li>Positioning of R-group labels was incorrect.</li>
		</ul>
	</li>
	
	<li><strong>Datatransfer</strong>
		<ul>
			<li><strong>Clipboard handling</strong>
				<ul>
				<li>Copy as PDF used 100x100px as image metrics and not the appropriate size for the current scale in Mac OSX.</li>
				</ul>
			</li>
		</ul>
	</li>

	<li><strong>Import/Export</strong>
		<ul>
			<li><strong>MOL, SDF, RXN, RDF</strong>
				<ul>
					<li> The subscript information of the S-group generic bracket was not exported to MOL V3000 and V2000 formats. </li>
					<li> Export of compressed MOL files allowed too large coordinate values, which caused distortion in the molecule after importing it. </li> 
					<li> After importing an RDfile that contains an empty S-group, its brackets didn't appear. </li>
				</ul>
			</li>
			<li><strong>SDF</strong>
				<ul>
					<li> Data S-groups were lost after exporting it to SDF format.</li>					
				</ul>
			</li>
			
			<li><strong>SMILES, SMARTS</strong>
				<ul>
					<li> Export to abbreviated group format did not work since 5.8.  </li>
				</ul>
			</li>

			<li><strong>CXSMILES, CXSMARTS</strong>
				<ul>
					<li> CXSMILES import added radicals to atoms that have coordinate bond to a central, metal atom.   </li>
					<li> I/O of atom alias "," was not working in CXSMILES format. </li>
				</ul>
			</li>
								
			<li><strong>CML, MRV</strong>
				<ul>
					<li> Format mrv had missing closing tags at the end of the generic S-group lines. </li>
 					<li> MolExporter exported to MRV format using operating system specific line ending, '\n'. </li>
				</ul>	
			</li>

			<li><strong>CDX</strong>
				<ul>
    				<li> Standalone list atoms and labels were read as text boxes from CDX files. </li>
					<li> Component grouping was not recognized during CDX import. </li>           				
				</ul>
			</li>

                        <li> <strong>SKC</strong>
				<ul> 
					<li> During the export to SKC format, chiral flags were lost and "AND enantiomer" flags were displayed. </li>
				</ul>

			<li><strong>CDX/SKC</strong>
				<ul>
 					<li> When opening a ChemDraw .cdx file or an ISIS/Draw .skc file containing structures with abbreviated groups, the attachment point of these groups was rendered incorrectly. </li>
				</ul>
			</li>
				
			<li><strong>Structure to Name (s2n)</strong>
				<ul>
					<li>There were extraneous numbers in generated names for structures containing atom values. </li>
					<li>Generation of IUPAC names for large structures might have stopped before the timeout.</li>
				</ul>	
			</li>
				
			<li><strong>Name to Structure (n2s)</strong>
				<ul>
					<li>The n2s import option to specify the location of the custom dictionary was not thread-safe, and was not accessible in d2s.</li>
					<li>Name to structure failed to return a result on some very long names (several hundreds of characters).</li>
				</ul>	
			</li>
				
			<li><strong>Document to Structure (d2s)</strong>
				<ul>
					<li>Non-chemical data in embedded structures, for instance text labels, were not extracted from embedded documents.</li> 
					<li>Some embedded structures were not extracted from office documents. </li>
					<li>Reactions embedded in documents were not extracted.</li>
					<li>Some uppercase strings were wrongly interpreted as smiles, for instance "BOUCHON".</li>
					<li>When OSRA was called by Document to Structure, it could run even after Marvin is stopped (on Windows only).</li> 
					<li>OSRA was not working on PDFs containing monochrome images.</li>
				</ul>	
			</li>

			<li><strong>Molconvert</strong>
				<ul>
					<li>A NullPointerException appeared in the console instead of an error message when using molconvert to convert an image with skiperror parameter set without OSRA installed. </li>
				</ul>	
			</li>
		</ul>
		</li>

		<li><strong>Molecule Representation</strong>
			<ul>				
				<li><strong>Valence Check</strong>
					<ul>
						<li> Aromatic carbon cations having three ligands were handled incorrectly by the Valence check.  </li>
			            <li> False positive valence error was on aromatic rings which have sulfur ligand that is part of a ring. </li>
						<li> Removing explicit hydrogen atoms from LiH, CaH<sub>2</sub>, SH<sub>4</sub>, PH<sub>5</sub>, PbH<sub>2</sub>, PbH<sub>4</sub> did not keep the original hydrogen count.  </li>
 						<li> Aromatic carbon cations having three ligands were handled incorrectly by the Valence check. </li>
					</ul>
				</li>
				<li><strong>S-groups</strong>
					<ul>
						<li> Bracket positioning did not take the implicit hydrogen atoms into account. </li>
						<li> If data were added as "relative", the cleaning or arrange methods did not take them along with the fragment/component/molecule.</li>
                        <li> The chirality of expanded S-group's atom changed, when there wasn't enough space in the canvas for the expanded S-group. </li>  
                        <li> After expanding S-groups, atoms of the molecule could overlap.  </li> 
                        <li> Removal of explicit hydrogen being the only atom of an S-group was unsuccessful. New flag, <code> SGROUP_H,</code> has been introduced, for remove such hydrogens.  </li>
                        <li>Exception was occurred when an abbreviated group was copied then pasted. </li>
						<li> Method <code> MulticenterTransform.transformMulticenters(Molecule mol) </code> and <code>MulticenterTransform.restoreMulticenters(Molecule mol) </code> changed the number of implicit hydrogens of atoms.
                    </ul>
                </li>
				<li><strong>R-groups</strong>
					<ul>
						<li> Fragmantation of an Rg-molecule resulted inconsistent molecules. </li>
					</ul>
				</li>
			</ul>				
		</li>

		<li><strong>Clean 2D</strong>
			<ul>
				<li> In case of selection, stereoinformation of the molecule during two dimensional cleaning was lost.  </li>
			</ul>
		</li>		
		
		<li><strong>Aromatization/dearomatization </strong>
			<ul>
				<li> General aromatization worked incorrectly in case of molecules with a bond connecting 2 rings but not being part of any ring. Solving this problem, deprecated method <code> int[] getSSSRBondSet()</code>, was removed and, instead, <code>BitSet getSSSRBondSet()</code> was introduced.</li>
			</ul>
		</li>	
		<li><strong>Calculations</strong>
		<ul>
			
			<li><strong>Protonation (pKa, reson.)</strong>
				<ul>
					<li>The predicted pKa of certain CH acids were extremly low.</li>
				</ul>
			</li>				
			<li><strong>Isomers (tautomer., reson.)</strong>
				<ul>
					<li>Tautomer generation was very slow on proteins.</li>
					<li>Some deuterium atoms were removed during tautomer generation. <a href="https://www.chemaxon.com/forum/viewtopic.php?p=41331#41331">Forum topic</a> </li>
					<li>The structure of the generated tautomer was not cleaned if the input molecule contained abbreviated group(s). </li>
				</ul>
			</li>
			<li><strong>Conformation</strong>
				<ul>
					<li>Exception was thrown during conformer generation if <i>Visualize H bonds</i> option was selected.</li>
				</ul>
			</li>
		</ul>
		</li>
		<li><strong>Structure Checker</strong>
			<ul>
				<li><i>Clean Wedge</i> fixer of <i>Wedge Error</i> checker had no effect in case of wedge bond between two chiral centers.</li>
				<li>Atoms and bonds inside S-groups were not handled correctly by certain checkers.</li>
			</ul>
		</li>		

		<li><strong>Elemental Analyzer</strong>
			<ul>
				<li> Elemental analyzer calculated wrong molecular formula for molecules, that contains bond to a centroid. </li>
			</ul>
		</li>		
		</ul>
		</li>
</ul>
<h3><a name="v5940" class="anchor"></a>May 4th, 2012: Marvin 5.9.4</h3>
<h4>Bugfixes</h4>
<ul>
		<li><strong>Calculations</strong>
		<ul>
			<li>Marvin Services did not return string result in some cases.</li>
		</ul>
	</li>
	<li><strong>Backend / Core</strong>
		<ul>
			<li><strong>Import/Export</strong>
				<ul>
					<li>MolImporter.importMol(String) returned null when no molecule was recognized from the input document.</li>
				</ul>
			</li>
		</ul>
	</li>	
	<li><strong>Structure Checker</strong>
		<ul>
			<li>Custom checker implementation related fixes.</li>
		</ul>
	</li>
</ul>
	
<h3><a name="v5930" class="anchor"></a>April 20th, 2012: Marvin 5.9.3</h3>
<h4>Bugfixes</h4>
<ul>
	<li><strong>Molecule representations</strong>
	<ul>
		<li><strong>S-groups</strong>
		<ul>
			<li>The relative position of multiple data S-groups on an object during clean was not kept.</li>
		</ul>
		</li>
	</ul>
	</li>
	<li><strong>Import/Export</strong>
	<ul>
		<li><strong>MOL, SDF, RXN, RDF</strong>
		<ul>
			<li>File format "molV3000" was recognized only when it was imported from a file having "mol" extension. </li>
		</ul>
		</li>
		<li><strong>Load/Save handling in UI</strong>
		<ul>
			<li>Image export did not use the color scheme setting. <a href="https://www.chemaxon.com/forum/ftopic9017.html">Forum topic</a></li>
		</ul>
		</li>
	</ul>
	</li>
</ul>

<h3><a name="v5920" class="anchor"></a>April 6th, 2012: Marvin 5.9.2</h3>
<h4>Bugfixes</h4>
<ul>
	<li><strong>Aromatization/dearomatization</strong>
		<ul>
			<li>Aromatic ring count returned zero in those cases, where the aromatic ring contained charged carbon with two aromatic and one single bond.</li>				
		</ul>
	</li>
</ul>

<h3><a name="v5910" class="anchor"></a>March 22nd, 2012: Marvin 5.9.1</h3>

<h4>New features and improvements</h4>
<ul>
	<li><strong>Import/Export</strong>
		<ul>
			<li>CXSMILES import/export handles every special atom available in the Periodic System dialog window in MarvinSketch.</li>
			<li>DNA/RNA sequences are imported in their acidic form (i.e., instead of negative charge, an implicit hydrogen is added to the phosphoric acid ester group).</li> 
		</ul>
	</li>
		
	<li><strong>Calculations</strong>
		<ul>
			<li>Accuracy of pKa calculation for amides has been improved.</li>
			<li>Tautomerization of molecules containing nitroso group results in chemically more feasible isomers.</li>
			<li>Speed of standard tautomer generation has been improved significantly. (On average about one hundred times faster.)</li>
		</ul>
	</li>
</ul>		
		
<h4>Bugfixes</h4>
<ul>
	<li><strong>MarvinSketch GUI</strong>
	<ul>
		<li><strong>Painting</strong>
			<ul>
				<li>Displaying proteins was very slow in some cases.</li> 
			</ul>
		</li>
	</ul>
	<li><strong>Import/Export</strong>
		<ul>
			<li><strong>CDX/CDXML</strong>
				<ul>
					<li>Coordinates of graphical objects were incorrectly imported from CDX files. Currently, imported "Anonymous Alternative Groups" are placed next to the imported molecule; "Anonymous Alternative Groups" with no bonds are imported as textboxes. </li>	
                    <li> MRV attributes that contain line breaks were not exported correctly: <i>\n</i>, <i>\r</i>, <i>\r\n</i> were not escaped.   </li> 
				</ul>
			</li>
			
			<li><strong>SKC</strong>
				<ul>	
					<li>Atom and bond colors, font types, and bond thickness in SKC import and export were incorrect.</li>
					<li>SKC import was failing on some structures containing built-in abbreviations.</li>
				</ul>
			</li>
			
			<li><strong>PDB</strong>
				<ul>	
					<li>Element type in some non-standard PDB file was not recognized properly. <a href="http://www.chemaxon.com/forum/viewtopic.php?t=8733">Forum topic</a> </li> 
				</ul>
			</li>
			
			<li><strong>VMN</strong>
				<ul>	
					<li>Charge and implicit hydrogen information was not read from VMN files.</li> 
				</ul>
			</li>
						
			<li><strong>Document to Structure (d2s)</strong>
				<ul>
					<li>Reactions embedded in documents were not extracted.</li>
					<li>When OSRA was installed, a failure of OSRA prevented the rest of the document to be processed.</li>
				</ul>
			</li>
						
			<li><strong>Peptide</strong>
				<ul>
					<li>DNA sequence import resulted in RNA sequence representation because of ambiguous one letter abbreviations. Currently, abbreviations dA, dC, dG, and dT are used to import DNA, while A, C, G, and U are used to import RNA sequences.</li>
                    <li>A peptide sequence containing custom amino acids was only imported when the custom amino acid was the last one in the sequence.</li>
				</ul>
			</li>					
		</ul>
	</li>
	
	<li><strong>Aromatization/dearomatization</strong>
        <ul>
            <li> Aromatic ring count returned zero in cases where the aromatic ring contained negatively charged carbon with two aromatic and one single bond.</li>
        </ul>
    </li>
	
	<li><strong>Calculations</strong>
		<ul>
			<li><strong>Protonation (pKa, etc.)</strong>
				<ul>
					<li>CH acid type structures might have caused errors during pKa calculation.</li>
				</ul>
			</li>
			
			<li><strong>Isomers (tautomer., reson.)</strong>
				<ul>
					<li>Wiggly bonds were changed to single bonds during standard canonical tautomer generation.</li>	
					<li>Resonance structure calculation of nitrogen containing molecules has been reconsidered: 10 valence electrons around a nitrogen are only allowed for nitro groups.</li> 					
				</ul>
			</li>
		</ul>	
	</li>

	<li><strong>Backend / Core</strong>
		<ul>
			<li><strong>Import/Export</strong>
				<ul>
					<li>NullPointerException was thrown by MoleculeImporter if "d2s:+cas" or "d2s:-cas" format string was specified.</li>
				</ul>
			</li>
			
			<li><strong>Character encoding</strong>
				<ul>
					<li>Some special characters were converted incorrectly from UTF. </li>
				</ul>
			</li>
		</ul> 
	</li>	
	
</ul>

<h3><a name="v5900" class="anchor"></a>March 2nd, 2012: Marvin 5.9.0</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>MarvinSketch GUI</strong>
		<ul>
			<li>Drawing electron flow arrow in a reaction has been enabled.</li>
		</ul>	
		
	</li>

	<li><strong>MarvinView GUI</strong>
		<ul>
			<li>Document to Structure on PDF documents can display the first structures without waiting to process the whole document. </li>
		</ul>	
	</li>
	
	<li><strong>Import/Export</strong>
		<ul>
			<li><strong>CML, MRV</strong>
				<ul>
					<li> New option to MRV and CML export, :C<i>N</i>, is introduced, where 0 &lt; <i>N </i>&le; 9 and N is the length of the decimals of the exported coordinates. For example, mrv:C5 </li>
				</ul>	
			</li>
				
			<li><strong>Name to Structure (n2s)</strong>
				<ul>
					<li>Conversion of systematic names has been increased by about 5%, and increased accuracy of the generated structures. </li>
					<li>Prefix "nor" has been implemented (e.g., 17-Hydroxy-19-norpregn-4-ene).</li>
					<li>Name with stereo-markers "r" or "s" where such stereoconfiguration cannot be present are now interpreted as "R" or "S". This allows names that have been completely lowercased to  be converted correctly.</li>
				</ul>	
			</li>
		
			<li><strong>Document to Structure (d2s)</strong>
				<ul>
					<li>Document to Structure supports the following common office document formats: doc, docx, ppt, pptx, xls, xls, odt in addition to pdf, xml, html and txt.</li>
					<li>Format options have been added to d2s, to specify what input formats should be converted (for CAS, SMILES and inchi). Format option "insideTag=&lt;tag>" to restrict conversions to the content of a specific tag (for instance <body> in HTML).</li>
					<li>The type of source text used for the conversion is included in generated structures. Possibly values: systematic, generic, common (for names), smiles, InChI, CAS# (for text formats), cdx, mrv, symyx (for embedded structures in office documents).</li>
					<li>Processing speed has been increased: doubled on typical documents including chemical names, and tripled on documents with few chemical names.</li>
				</ul>	
			</li>
		</ul>
	</li>

	<li><strong>Molecule Representation</strong>
		<ul>
			<li><strong>Molecule/atom/bond properties</strong>
				<ul>
					<li>ArrayIndexOutOfBoundsException was thrown in the Parity.generateDigraph method in special circumstances.</li>
				</ul>
			</li>
		
			<li><strong>Valence Check</strong>
				<ul>
					<li> A new option is provided which enables to turn off the valence check of nitrogen, phosphorus, or boron atoms in those cases when they are in aromatic compounds. </li>
				</ul>
			</li>
		</ul>
		
	<li><strong>Calculations</strong>
		<ul>
			<li><strong>Isomers (tautomer., reson.)</strong>
				<ul>
					<li>Tautomerization can take into account the possibility of ring closure when "All tautomers" function is selected.</li>
				</ul>
			</li>
				
			<li><strong>NMR Predictor Beta</strong>
				<ul>
					<li>"Copy to Clipboard" and "Export Prediction to PDF" actions have been implemented.</li>
				</ul>
			</li>
		</ul>	
	</li>

	<li><strong>cxcalc</strong>
		<ul>
			<li>Advanced logging in cxcalc: logging errors and execution times is available.  </li>
		</ul>
	</li>

	<li><strong>Structure Checker</strong>
		<ul>
			<li>Chiral flag error checker options have been improved.</li>
			<li>Custom fixers use annotations replacing property files. Backward compatibility is ensured for both custom checkers and fixers. <a href="checker.html#annotation">Documentation</a></li>
			<li>Pattern based report file generation is available as a new option in <code>structurecheck</code> commandline.
		</ul>
	</li>

	<li><strong>MarvinSpace</strong>
		<ul>
			<li>Default aromatization method in MarvinSpace is the Basic aromatization (the same as in MarvinSketch).</li>
		</ul>
	</li>
</ul>

<h4>Bugfixes</h4>
<ul>
	<li><strong>MarvinSketch GUI</strong>
		<ul>
			<li>MarvinView 3D window could not be opened from the sketcher if MarvinView 2D window had been opened before.</li>
		</ul>	
	</li>
		
	<li><strong>Applet</strong>
		<ul>
			<li>Applet settings could influence the client side desktop application settings.</li>
	    </ul>
	</li>
		
	<li><strong>Painting</strong>
		<ul>
			<li>The query properties were wrongly painted in some cases and covered the atom labels. </li>
			<li>3D rotate action activated automatically when joining a structure in hand to a bond of a 2D structure on the canvas.</li>
			<li>Double bond was not painted in centered position in case of terminal bonds when the label of the connected atom was displayed or both of the atom labels were displayed. </li>
			<li>Single arrow type brackets were not painted.</li>
			<li>The orientation of explicit hydrogens was incorrect when explicit hydrogens were added to the (selected) upper methylene C atom of a cyclohexane. </li>
		</ul>
	</li>

	<li><strong>Import/Export</strong>
		<ul>
			<li><strong>MOL, SDF, RXN, RDF</strong>
				<ul>
					<li>Handling of special characters (-, ", space) in extended MDL MOL format IO was wrong.</li>
					<li>RDF file exported by Instant JChem was not imported correctly to ISIS/Base.</li>
				</ul>
			</li>
			
			<li><strong>SMILES, SMARTS</strong>
				<ul>
					<li> In some rare cases, the unique SMILES of a molecule depended on the numbering of the atoms.  </li>
				</ul>
			</li>
								
			<li><strong>CML, MRV</strong>
				<ul>
					<li> Parity value in CML format was stored the opposite way as the CML standard defines it. Now the correct value is stored which leads to backward inconsistency with CML files generated by ChemAxon applications in previous versions, but correct values are read from standard CML files. To avoid running into similar problems in the future, now a version information is stored both in CML and MRV formats. This information contains a version number of the file format and the application version by which the file was generated. The parity value is changed to opposite in MRV format as well to be consistent with the CML format but it does not lead to backward inconsistency in this case: parity values stored in not versioned MRV files are converted to their opposite while parity values from versioned MRV files are not.   </li> 
				</ul>	
			</li>
				
			<li><strong>PDF</strong>
				<ul>
					<li>View settings were not applied in exported pdf file.</li>
				</ul>	
			</li>	

			<li><strong>VMN</strong>
				<ul>
					<li> VNM format sometimes was recognized wrongly as mol2. </li>
				</ul>	
			</li>
	
			<li><strong>Image I/O</strong>
				<ul>
					<li>The molecule source was generated in unnecessary cases.</li>
					<li>javax.imageio left temp files open if the ImageOutputStream was not closed properly.</li>
				</ul>	
			</li>	

			<li><strong>Structure to Name (s2n)</strong>
				<ul>
					<li>The generated names for bridged and spiro ring systems were missing E/Z stereodescriptors for double bonds in large rings.</li>
					<li>Some unconventional names were generated with suffixes such as amido, sulfonyl, carbonyl, ... mentioned as prefixes. For instance 2-[(2-chlorophenyl)amido]benzoate for [O-]C(=O)C1=C(NC(=O)C2=C(Cl)C=CC=C2)C=CC=C1. The generated names is now 2-(2-chlorobenzamido)benzoate.</li>
				</ul>	
			</li>
				
			<li><strong>Name to Structure (n2s)</strong>
				<ul>
					<li>Stereochemistry was not indicated in the displayed structure when cis or trans is used to specify the configuration of the two substituents of a ring system.</li>
					<li>Cycloalkane substituents of fused ring systems were aromatized.</li>
					<li>The stereochemistry of chiral atoms in subsitutents was sometimes inverted.</li>
					<li>Name to Structure was blocked on some very long names with unbalanced parentheses.</li>
				</ul>	
			</li>
				
			<li><strong>Document to Structure (d2s)</strong>
				<ul>
					<li>Some types of smiles strings were not recognized by Document to Structure, in particular those with stereochemistry or acyclic structures with two-letter heteroatoms.</li>
				</ul>	
			</li>

			<li><strong>Peptide</strong>
				<ul>
					<li> Numbers or sign "-" in the name of custom aminoacids were not accepted during the import of peptides (exception was thrown). </li>
				</ul>	
			</li>
		</ul>
		</li>

		<li><strong>Molecule Representation</strong>
			<ul>
				<li><strong>Molecule/atom/bond properties</strong>
					<ul>
						<li> ArrayIndexOutOfBoundsException was thrown in the Parity.generateDigraph method in special circumstances.</li>
						<li> Molecule.implicitizeHydrogens() threw "java.lang.IndexOutOfBoundsException" after fragmentation if the molecule contained information in MDocument field. </li>
					</ul>
				</li>
				
				<li><strong>Valence Check</strong>
					<ul>
						<li> Valence Check gave false valence error for aromatic oxygen having +1 charge.  </li>
					</ul>
				</li>
			</ul>
		</li>		
		
		<li><strong>Structure Checker</strong>
			<ul>
				<li>Aromaticity Error checker options could not be set.</li>
			</ul>
		</li>		
	</ul>

<h4>Known issue</h4>
<ul>
	<li><strong>Structure Checker</strong>
		<ul>
			<li>R-group Reference Error Checker cannot find circular and selfreferring R-atoms and R-groups.</li>
		</ul>
	</li>	
</ul>

<h3><a name="v5830" class="anchor"></a>February 20th, 2012: Marvin 5.8.3</h3>
<h4>Bugfixes</h4>
<ul>
	<li><strong>Stereochemistry</strong>
		<ul>
			<li>Bicyclostereo information was calculated on symmetric ligands.</li>
		</ul>
	</li>
	<li><strong>Structure Checker</strong>
		<ul>
			<li>External fixers could not be applied properly in MarvinSketch.</li>
		</ul>
	</li>
</ul>


<h3><a name="v5820" class="anchor"></a>February 8th, 2012: Marvin 5.8.2</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>MarvinSketch GUI</strong>
	<ul>
	<li>Standalone mode of OLE can be set on the preferences dialog if the application is launched in administrator mode.</li>
	</ul>
	</li>
        <li><strong>Import/Export</strong>
        <ul>
          <li>Molconvert has a new option, :D,
              which is important if the molecule has parity information and 
              has 0 dimension. By default during the export, 
              a clean method is invoked on the structure and the generated 
              coordinates and wedge information are exported into 
              MRV format but NOT the parity information. 
              However, using this option coordinates and wedge information are 
              not generated but parity information is exported. </li>
          <li>Mrv files can be validated by using the XSD schema containing 
              the description of the mrv format.   </li>
          <li>Export options of cml and mrv formats can be used in 
              molconverter application.  </li>
        </ul>
        </li>
</ul>
<h4>Bugfixes</h4>
<ul>
<li><strong>MarvinSketch GUI</strong>
	<ul>
	<li>The tooltip of template groups was displayed incorrectly in some cases.</li>
	</ul>
	<li><strong>Import/Export</strong>
	<ul>
	<li>Starting the OSRA utility failed on Unix and Macintosh platforms.</li>
	</ul>
	</li>
</ul>


<h3><a name="v5810" class="anchor"></a>January 30th, 2012: Marvin 5.8.1</h3>
<h4>Bugfixes</h4>
<ul>
<li><strong>MarvinSketch GUI</strong>
	<ul>
		<li><strong>Dialogs</strong>
			<ul>
				<li>Subscripts and superscripts (\s, \S, and \n) were not enabled in Group names in version 5.7.x and 5.8.0. </li>
			</ul>	
		</li>
	</ul>
</li>

<li><strong>Import/Export</strong>
	<ul>
		<li><strong>MOL, SDF, RXN, RDF</strong>
			<ul>
				<li>The line break in the Sgroup Attachment Point information section of the Extended MOLFile (MOL V3000) format was wrongly inserted in case of longer peptide sequences containing Cysteine.</li>
			</ul>
		</li>
		<li><strong>CML, MRV</strong>
			<ul>
				<li>From version 5.8.1, molecules having parity information are exported in 2D form instead of 0D form in order to avoid incorrect parity representation.</li>
			</ul>	
		</li>		
	</ul>
</li>

<li><strong>Molecule Representation</strong>
	<ul>
		<li><strong>Valence Check</strong>
			<ul>
			<li>The aromatic nitrogen handling in case of pyridine-oxide was incorrect.</li>
			<li>In release 5.8, all amino acids of an amino acid chain were detected to have valence error if it was imported from the Edit > Source menu of MarvinSketch.</li>
			</ul>
		</li>
		<li><strong>S-Groups</strong>
			<ul>
				<li>Hydrogens attaching to sgroups were not treated as special ones and from version 5.8.1, their removal is optional.</li>
			</ul>
		</li>	
	</ul>
</li>

<li><strong>Data Transfer</strong>
	<ul>
		<li><strong>OLE</strong>
			<ul>
				<li>Symyx content conversion (by redirecting) crashed Marvin OLE.</li>
			</ul>
		</li>
	</ul>
</li>

<li><strong>Calculations</strong>
	<ul>
		<li>The hydrogen count was incorrect in case of position variation and metallocene-like structures.</li>
	</ul>	
</li>

<li><strong>Structure Checker</strong>
	<ul>
		<li>"neutralize" fixer did not work in structurcheck command line application; "null" output was generated.</li>
	</ul>
</li>
</ul>

<h3><a NAME="v5800" class="anchor"></a>January 11th, 2012: Marvin 5.8.0</h3>
<h4>New features and improvements</h4>
<ul>
    <li><strong>MarvinSketch GUI</strong>
	<ul>
		<li>Group types on "group creation" dialog can be configured.</li>
		<li>The icon of manual atom mapping has been changed.</li>
		<li>A new radical electron setting and the corresponding toolbar icon group has been introduced, solely for educational purposes: Up to four radical electrons can be defined for an atom, and it is arranged evenly in the empty space around the atom label.</li>
		<li>Recent file list length has been increased up to 50. (The default is 25.)</li>
	</ul>
    </li>
	<li><strong>MarvinView GUI</strong>
	<ul>
		<li>Atom mark and atom highlight display can be enabled/disabled.</li>		
	</ul>
    </li>	
	<li><strong>Import/Export</strong>
	<ul>
		<li><strong>Structure to Name (s2n)</strong>
			<ul>
			<li>New name format option: "source" has been introduced to  print the names present in the source molecule data. For instance, molconvert name:source input.sdf.</li>
			</ul>
		</li>		
	    <li><strong>Name to Structure (n2s)</strong>
		<ul>
			<li>n2s handles stereochemistry of cyclic monosaccharides.</li>
			<li>Extended support for common names: n2s can convert 123 thousand common names &#8211; including synonyms &#8211; corresponding to 34 thousand structures instead of the previous 63 and 28 thousand, respectively.</li>
		</ul>
		</li>
		<li><strong>Document to Structure (d2s)</strong>
		<ul>
			<li>Automatic text OCR (optical character recognition) has been added to support document to structure conversion of scanned (non searchable) PDF documents.</li>
		</ul>
		<li><strong>MRV</strong>
			<ul>
			<li>The information on the selected atoms and bonds can be stored in MRV format.</li>
			<li>MRV format has been changed: there is no indentation, there are line-breaks only after the header and after the documents. The original format (including line-breaks and indentation) can be achieved using the -P option; this previous format is displayed &#8211; for easier reading &#8211; in the Edit > Source window in  Marvin graphical applications. <a href="../formats/mrv-doc.html#export.html">See export options here.</a></li>
			</ul>
	    </li>
		<li><strong>SMARTS</strong>
		<ul>
		<li>ChemAxon Extended SMARTS represents R-group (Markush) structures.</li>
		</ul>
	    </li>
		<li><strong>CML</strong>
		<ul>
		<li>CML bondStereo implementation has been completed with atom references.</li>
		<li>CML format has been changed: there is no indentation, there are line-breaks only after the molecules and after the header. The original format (including line-breaks and indentation) can be achieved using the -P option; this previous format is displayed &#8211; for easier reading &#8211; in the Edit > Source window in  Marvin graphical applications. <a href="../formats/cml-doc.html#export.html">See export options here.</a></li>
		</ul>
	    </li>
	</ul>
	</li>
	<li><strong>Calculator Plugins</strong>
		<ul>
		<li>New integrated component: NMR Predictor Beta has been implemented.</li>
		<li>"Consider tautomerization" option has been renamed to "Consider tautomerization/resonance" in logP and logD calculator plugins.</li>
		<li>"Tools" menu content has been moved to "Calculations" menu.</li>
		</ul>
	    </li>
	<li><strong>Data transfer</strong>
	<ul>
	<li>Native PDF format as the default image pasteboard format is used on MAC systems. It allows the user to copy a structure (which was originally created by either Marvin or ChemDraw) from a Microsoft Office 2011 document and to paste it to Marvin.</li>
	<li>More verbose logging from OLE .NET panel (catching managed bridge exceptions via COM) has been introduced.</li>
	</ul>
	
</ul>
<h4>Bugfixes</h4>
<ul>
	<li><strong>MarvinView</strong>
	<ul>
		<li>Sometimes molecules were lost when switching from "Spreadsheet mode" (Table>Options>Table Type) to "Molecule matrix" mode. </li>
		<li>When a zero length structure file was opened, the content of the previous file remained in the viewer until the first repainting.</li>
		<li>Scrolling up and down increased the number of molecules and changed their orders.</li>
	</ul>
    </li>
    <li><strong>Painting</strong>
	<ul>
	    <li>Until the first repaint, textbox was smaller than required, and the content was cropped to its size.</li>
		</ul>
    </li>
	<li><strong>Import/Export</strong>
	<ul>
		<li><strong>Structure to Name (s2n)</strong>
		<ul>
			<li>Incorrect name was generated for chiral allenes, for instance: (2M)-1,3-dichloropropa-1,2-diene, the correct is 1M. (The locant was previously in the middle of the cumulated double bond system, but is now at the beginning of it, as prescribed by IUPAC.)</li>
			<li>Fixed generation of names in the case where a radical marker "yl" was missing, for instance "...(4-methylbenzene)sulfonyl..." instead of the correct form "...(4-methylphenyl)sulfonyl..."</li>
			<li>Name generation was sometimes failing on fused ring systems with a spiro connections or adjacent fusion bonds. Conversion rate of fused ring systems goes up from 99.83% to 99.95% on the NCI database set.</li>
			<li>The numbering of fused ring systems was incorrect in about 1% of the cases both in <b>s2n</b> and <b>n2s</b>.</li>
		</ul> 
		<li><strong>Name to Structure (n2s)</strong>
		<ul>
			<li>Automatic name format recognition failed for names containing unicode prime characters.</li>
			<li>CAS number cache could get corrupted and created failures.</li>
		</ul>
		<li><strong>Document to Structure (d2s)</strong>
		<ul>
			<li>The total length (getEstimatedTotalCharacters) was not available to progress listeners during Document to Structure conversion of PDFs using the API.</li>
		</ul>
		</li>		
		<li><strong>SMILES</strong>
		<ul>
			<li>CXSMILES IO error due to non-escaped characters appeared.</li>
			<li>The name imported from SMILES format was wrong in rare cases, when special characters (e.g., |) were in the fields part of the extended SMILES.</li>
		</ul>
		</li>
		</ul>
		<li><strong>Calculator Plugins</strong>
			<ul>
				<li>logP and pKa calculations threw error at heavy atom count 300.</li>
				<li>Generic tautomer generation might have threw an error if the molecule contained Hydrogen isotopes.
			</ul>
	    </li>
</ul>

<h3><a NAME="v5720" class="anchor"></a>January 25th, 2012: Marvin 5.7.2</h3>
No changes.

<h3><a NAME="v5710" class="anchor"></a>December 12th, 2011: Marvin 5.7.1</h3>
<h4>Bugfixes</h4>
<ul>
	<li><strong>Molecule Painting</strong>
		<ul>
			<li>Valences higher than nine were wrongly displayed if the valence property visibility was turned off.</li>
		</ul>
	</li>
	<li><strong>Calculator Plugins</strong>
		<ul>
			<li>Some property calculations threw ArrayIndexOutOfBoundsException when the input molecule contained multiple fragments.</li>
		</ul>
	</li>
	<li><strong>Build/Installation</strong>
		<ul>
			<li>OLE defect occurred during side-by-side installations.</li>
		</ul>
	</li>	
</ul>

<h3><a NAME="v5700" class="anchor"></a>November 4th, 2011: Marvin 5.7.0</h3>
<h4>New features and improvements</h4>
<ul>
    <li><strong>MarvinSketch GUI</strong>
	<ul>
		<li>New bond type: "Hashed bond" has been introduced.</li>
		<li>"Coordinate" Bond Line Style &#8211; in the <b>Edit</b>&gt;<b>Preferences</b>&gt;<b>Bonds</b> menu &#8211; allows changing the type of coordinate bonds between the default ones (arrow for single atom and hashed for multicenter) and solid.</li>
		<li>Selected structures can be aligned or distributed relative to each other horizontally or vertically. The Align&Distribute function aligns and distribute the objects at the same time.</li>
		<li>New chain drawing tool: Curved chain tool. Instead of creating a straight chain it modifies the direction of the last bond of the chain based on the mouse movement. Curved chains or macrocycles can be created that way.  </li>
		<li>The chain drawing direction is mirrored based on the direction of the mouse movements.</li>
		<li>Selection can be moved in greater units with SHIFT + arrow keys.</li>
	</ul>
    </li>
	
	<li><strong>Import/Export</strong>
	<ul>
	<li><strong>Structure to Name</strong>
		<ul>
		<li>Traditional name for known class names (e.g., acylcholine) is generated.</li>
		</ul>
	</li>
		
	    <li><strong>Name to Structure</strong>
	<ul>
			<li>"Name to structure" converter supports  more systematic names (6% to 50% measured on various sets of names extracted from patents). For instance, names representing esterification with locants (e.g., 1,4-di-tert-butyl piperazine-1,4-dicarboxylate).</li>
			<li>Optional support for CAS numbers in Document2Structure.</li>
			<li>Faster loading of PDFs by Document to Structure (from 25% to 75% speedup in most cases). Names can be extracted from more types of PDFs.</li>
	</ul>
	</li>
		<li><strong>CDX/CDXML</strong>
		<ul>
		<li>Hashed bond type has been implemented in Marvin. "Hashed to Down Wedge" conversion has been changed to "Hashed to Hashed" conversion.</li>
		</ul>
	    </li>
	</ul>
	</li>
	
	<li><strong>Clean 3D/optimization </strong>
		<ul> <li>Verbose mode has been added to Generate3D. Use option "[verbose]" to switch on; use "molconvert -H3D" for detailed help.</li>
		</ul>
	<li><strong>Data transfer</strong>
	<ul>
	<li>Native pasteboard for Macintosh has been implemented. It solves general copy/paste issues, e.g., copy/paste between Marvin and other chemical sketcher programs (e.g., ChemDraw).</li>
	<li>Marvin supports conversion of other Vendors (currently ISIS and Symys) embedded OLE objects to Marvin OLE.</li>
	</ul>
	<li><strong>Structure Checker</strong>
	<ul>
	    <li>New checkers are available:
		<ul>
		    <li>R-atom Checker: detects R-atoms (all, disconnected, generic, linker, nested).</li>
		    <li>R-group Attachment Error Checker: detects attachment differences between R-atoms and R-group members. A new fixer corrects the attachment bond types and adds missing attachments in unambiguous cases.</li>
			<li>R-group Reference Error Checker: detects missing R-atoms, missing R-groups, self or circular references of nested R-atoms.</li>
		</ul>
		<li>Checker and fixer classes implemented by the user are loaded from given jar file.</li>
	    </li>
	</ul>
	</li>	
	<li><strong>API</strong>
	<ul>
	    <li>chemaxon.marvin.util.ColorGenerator class provides an easy way to generate the required number of different colors.
			   </li>
		<li>Several core functions are deprecated. See <a href="deprecated_functions_table.html">this table</a> for details.</li>
		<li>API support for running checkers and fixers multiple times automatically.</li>
	</ul>
	</li>
</ul>
<h4>Bugfixes</h4>
<ul>
	<li><strong>MarvinSketch</strong>
	<ul>
	<li>The Java beans and applet parameter for switching the valence property visibility OFF was missing.</li>
	</ul>
    </li>
    <li><strong>MarvinSketch GUI</strong>
	<ul>
	    <li>Scrolling with mousewheel or arrow keys enlarged Marvin Sketch canvas.</li>
		<li>Moving selection with arrow keys could not be undone.</li>
		<li>Attachment points were not highlighted by green when they were selected.</li>
		<li>Sugar templates: D-furanoses had S label on carbon 5.</li>
		<li>Double click selection did not work when it pointed to an atom.</li>
	</ul>
    </li>
	<li><strong>MarvinView</strong>
	<ul>
	<li>MViewPane did not refresh when UserSettings changed.</li>
	</ul>
    </li>
    <li><strong>MarvinView printing</strong>
	<ul>
	<li>Several MarvinView display property was missing in MolPrinter </li>
	</ul>
	   </li>
    <li><strong>Painting</strong>
	<ul>
	    <li>Error highlight was partially visible and often disappeared on mouse move.</li>
		</ul>
    </li>
	<li><strong>Molecule Painting</strong>
	<ul>
	    <li> Positive charge of hydrogen was not scaled properly in case of small magnification.</li>
		</ul>
    </li>
    <li><strong>Import/Export</strong>
		<ul>
		<li><strong>Structure to Name</strong>
		<ul>
			<li>Incorrect names where generated when the parent structure has identical substituents orientated differently, for instance: 4-formamido-1H-pyrazole-3-carboxamide</li>
		</ul> 
		</li>
		<li><strong>Inchi Key</strong>
	<ul>
	<li>Inchi slowdown with inchikey call for 10000s of structures.</li>
			</ul> 
		</li>
		<li><strong>PDF</strong>
	<ul>
	<li>OSRA did not work in Marvin unless its library was set in the PATH.</li>
			</ul> 
		</li>	
	 <li><strong>SMILES/SMARTS</strong>
		<ul>
		<li>A molfile or sketch is carrying an 'rb*' flag on any atom(s) was translated incorrectly to SMARTS ('x' atomic primitive).</li>
		<li>cxsmiles IO of Data sgroup was incorrect.</li>
		<li>mol format from smarts which contained "[C,!N]" (representing "Carbon , not Nitrogen") was converted incorrectly.</li>
		</ul>
		</li>
		</ul>
	</li>
	<li><strong>Molecule representation</strong>
	<ul><li>Connecting an additional bond to an abbreviated group freezed Marvin Sketch.</li>
	</ul>
	</li>
	<li><strong>Stereochemistry</strong>
		<ul><li>Allene parity lost by removing explicit hydrogens.</li>
		</ul>
	</li>
	<li><strong>Aromatization/dearomatization </strong>
		<ul>
			<li>Aromatization of an aromatized structure created new aromatic ring</li>
		</ul>
	</li>
	<li><strong>Elemental analysis</strong>
		<ul>
			<li>The Elemental Analyzer did not support Position-Variation bonds in formula calculation. Known issue: Dot-disconnected formula calculation is not working correctly in case of Position-Variation bonds.</li>
		</ul>
	</li>
</ul>

<h3><a NAME="v5605" class="anchor"></a>December 13th, 2011: Marvin 5.6.0.5</h3>
<p>No changes.</p>

<h3><a NAME="v5604" class="anchor"></a>October 25th, 2011: Marvin 5.6.0.4</h3>
<h4>Bugfix</h4>
<ul>
	<li><strong>Import/Export</strong>
		<ul>
			<li>Importing Molfile which starts with "BM" failed with OSRA related error message.</li>
			<li>SD files started with "P3" was recognized as PPM file instead of SDF. This bug was a result of changes in Marvin version 5.5.</li>
		</ul>
	</li>
</ul>

<h3><a NAME="V5603" class="anchor"></a>October 18th, 2011: Marvin 5.6.0.3</h3>
<p>No changes.</p>

<h3><a NAME="v5602" class="anchor"></a>October 5th, 2011: Marvin 5.6.0.2</h3>
<h4>Bugfix</h4>
<ul>
	<li><strong>Import/Export</strong>
		<ul>
			<li>Sgroups handled implicit hydrogens incorrectly depending on file formats. The bug could prevent matching in case of duplicate search.</li>
		</ul>
	</li>
</ul>
<h3><a NAME="v5601" class="anchor"></a>September 25th, 2011: Marvin 5.6.0.1</h3>
<h4>Bugfixes</h4>
<ul>
	<li><strong>Import/Export</strong>
		<ul>
			<li>Data Sgroup was converted incorrectly to cxmiles.</li>
			<li>Opening PDF files, NullPointerException occured in some cases.</li>
		</ul>
	</li>
</ul>

<h3><a NAME="v5600" class="anchor"></a>August 31st, 2011: Marvin 5.6.0</h3>
<h4>New features and improvements</h4>
<ul>
    <li><strong>MarvinSketch GUI</strong>
	<ul>
		<li>New mouse cursors have been introduced.</li>
		<li>New structure templates have been added (Alpha D sugars, Beta D sugars, Deoxynucleosides, Flavonoids, Nucleobases, Nucleosides, Protoalkaloids, True Alkaloids, Vitamins, Functional Groups).</li> 
	</ul>
    </li>
	<li><strong>MarvinView GUI</strong>
	<ul>
		<li>
		Atoms can be marked in Marvin View by hovering the cursor over it. This also works even if another atom has already been selected. 
		A property change event is sent on every atom mark (<a href="viewman.html">Documentation</a>).
		</li>
	</ul>
	</li>
   <li><strong>Applet</strong>
   <ul>
   		<li>New parameters have been introduced:
		<ul>
			<li>onError (<a href="sketchman.html#parameters.onError">Documentation</a>).</li>
			<li>alertError (<a href="sketchman.html#parameters.alertError">Documentation</a>).</li>
		</ul>
		</li>
   </ul>
   </li>
    <li><strong>Import/Export</strong>
	<ul>
	    <li><strong>Str2Name</strong>
		<ul>
		    <li>New option has been added to generate all common names for a structure, e.g.: molconvert "name:common,all" -s viagra.</li>
			<li>Less brackets are generated in names in traditional mode. For instance, instead of "{1-[(tert-butoxy)carbonyl]-1H-indol-2-yl}boronic acid" the generated traditional name is now "1-(tert-butoxycarbonyl)indol-2-ylboronic acid".</li>
		</ul>
	    </li>
		<li><strong>Name2Str</strong>
		<ul>
			<li>MolImporter can now process a PDF file as input and convert the chemical names in the text into structures. Structure images are also converted if OSRA is installed on the computer.</li>
			<li>Increased support for name to structure conversion (measured between 6% and 30% on certain datasets).</li>
		</ul>
	    </li>
	</ul>
	<li><strong>Calculator Plugins</strong>
	<ul>
	    <li>"Marvin Services" has been introduced: Web/local services client, which provides seamless integration of third-party calculations into Marvin Sketch, cxcalc, and via Chemical Terms into Instant JChem or JChem for Excel. Industrial standard solutions like SOAP with WSDL are supported, along with the more lightweight XML-RPC or JSON-RPC protocols. Java based calculations can be called without server, directly from the jar file.</li>
		<li>A new type of fragmentation has been implemented where the fragments contained in the same S-group are not separated. Fragmentation methods now have fragmentation type as parameter. The three types of fragmentation are:
	<ul>
    <li>Basic: no S-groups are considered, fragments are separated even if they are in one S-group. Broken S-groups are not added to the fragments.</li>
    <li>Keeping multicenters: multicenter S-groups are kept in one fragment, other types of S-groups that contain more than one fragment are not added to the fragments, those fragments are separated.</li>
    <li> Keeping S-groups: all S-groups are kept, fragments in the same S-group are not separated.</li>
	</ul>
Previous fragmenting methods which did not indicate the type of fragmentation are deprecated; they do the same as before (keep multicenters), but now &#8211; correctly &#8211; without generating inconsistent molecules with broken S-groups. These methods will be removed in later releases. </li>
</ul>
</li>
		   	<li><strong>Markush enumeration</strong>
	<ul>
	<li>There is a significant speedup of sequential and random Markush enumerations. (Hundreds of times speedup for average patent Markush structures. Speedup depends on the complexity of Markush structures.)</li>
	</ul>
    <li><strong>Structure Checker</strong>
	<ul>
	    <li>New checkers are available:
		<ul>
		    <li>Unbalanced Reaction Checker: reports if there is difference in the total number of atoms or atom types on the two sides of the reaction arrow.</li>
		    <li>Atom Query Property Checker: detects atoms having query properties. A new fixer can remove the identified query properties.</li>
		</ul>
		<li>New feature: "3D checker" detected issues can be fixed by "Remove Z-Coordinate Fixer". It flattens molecules by setting the z-coordinates of atoms to zero.</li>
	    <li>Structure Checker command line application has greatly improved. See the <a href="http://www.chemaxon.com/jchem/doc/user/structurecheck_cline.html"> documentation </a> for details.</li>
	    <li>Improvements in substructure checker: multifragment molecule handling, implicit hydrogen handling, improved command line access. "Transform" fixer for substructure errors: modifies substructures.</li>
	    </li>
	</ul>
	<li><strong>API</strong>
	<ul>
	    <li>New function to get the enantiomer of a molecule has been implemented: chemaxon.calculations.stereo.Stereochemistry.getEnantiomer(MoleculeGraph m).
			   </li>
	   <li>Applet API has been improved with validateMoleculeStr(String str) and validateMoleculeStr(String str, String format) methods.</li>
	</ul>
</ul>
<h4>Bugfixes</h4>
<ul>
    <li><strong>MarvinSketch GUI</strong>
	<ul>
	    <li>The double-click selection action did not work after rotating the view in 3D, and then moving/rotating the structure in 3D.</li>
	</ul>
    </li>
    <li><strong>MarvinView GUI</strong>
	<ul>
	<li>Scale factor was not updated on rescale in MarvinView.</li>
	</ul>
    </li>
	<li><strong>MarvinView printing</strong>
	<ul>
	When multiple structures were printed in PDF with molecule matrix rendering, structures with crossing bonds in the second or further cells were just partially drawn.
	</ul>
   <li><strong>Applet</strong>
	<ul>
	    <li>Marvin Applet failed to load changes in a file if the same file url was loaded before and applet started from the legacy cache.</li>
		<li>Legacy_lfecycle related initialization (parameter loading, event listening, etc.) was fixed in applets. </li>
	</ul>
    </li>
    <li><strong>Molecule Painting</strong>
	<ul>
	    <li>The shadow painting of the sprout drawing feature removed the error highlight of Structure Checker.</li>
		<li>Atom label was shifted in case of terminal double/aromatic bonds at the end of chains. Double bond was not painted in the centered position when there were no substituents on either sides.</li>
	   	</ul>
    </li>
    <li><strong>Import/Export</strong>
	<ul>
		<li>MOL and SDF files including pipe character in the first line were recognized as Chemaxon Extended SMILES.</li>
		<li>MOLFiles including a caret symbol in the Alias or Sgroup label fields were displayed incorrectly (the caret symbol was not hidden).</li>
		<li>MOL/SDF files starting with "BM" characters in the first line were recognized as BMP file.</li>
	 </ul> 
	 <li><strong>SMILES/SMARTS</strong>
		<ul>
		<li>ArrayIndexOutOfBoundsException did not work in case of SmilesImport and Chirality calculation.</li>
		</ul>
	</li>	
	<li><strong>Data transfer</strong>
	<ul>
	    <li>Copy/paste of Marvin OLE objects from MSOffice applications back to Marvin did not work. </li>
		<li>OLE image was not updated consistently after editing structure in MsOffice applications on 64 bit environment.</li>
	</ul>
    </li>
<li><strong>2D clean</strong>
		<ul>
			<li>Bad 2D cleaning in case of fused ring system.</li>
		</ul>
	</li>
	<li><strong>Aromatization/dearomatization </strong>
		<ul>
			<li>Aromata.isDaylightAromatic considered (O=[N+]1C=CC=C1) incorrectly.</li>
		</ul>
	</li>
	<li><strong>Calculator Plugin</strong>
		<ul>
			<li>logP calculation threw ArrayIndexOutOfBoundsException in some cases.</li>
			<li>There were incorrect systematic names generated in case of sulfur-based groups with non-standard valence (for instance dihydroxy-lambda6-sulfanone).</li>
			<li>FindAllRings failed with OutOfMemory error.</li>
		</ul>
	</li>
	<li><strong>Structure Checker</strong>
	<ul>
	StructureCheck command line application did not generate report field in output if checker options are set (e.g. "explicith:isotopic=true").
	</ul>
	</li>
	<li><strong>Build/Installation</strong>
	<ul>
	Marvin Beans installer could not start with 64bit Java 1.6 under OS X 10.5.
	</ul>
	</li>
	</li>
</ul>

<h3><a NAME="V5510" class="anchor"></a>July 8th, 2011: Marvin *******</h3>

<h4>New features and improvements</h4>
<ul>
	<li><strong>Import/Export</strong>
		<ul>
			<li>molconvert: The -T option works with pipes using the fields of the first molecule.</li>
			<li>Name to structure: Document to Structure also extracts smiles and InChI from the text.</li>
			<li>CML: CML files now passes the validator at http://validator.xml-cml.org.</li>
		</ul>
	</li>
</ul>
<h4>Bug fixes</h4>
<ul>
	<li><strong>MarvinSketch</strong>
		<ul>
			<li>Pressing the Shift key when the mouse cursor was on an atom threw an ArrayIndexOutOfBoundsException.</li>
		</ul>
	</li>
	<li><strong>Chemical structure painting</strong>
		<ul>
			<li>Bond coloring was wrong in 2D if the colors of the neighboring atoms were different.</li>
			<li>Quick rotation of the 3D view of 2D structures sometimes resulted in the disappearance of the structure.</li>
			<li>The Java beans and applet parameters for switching the valence property visibility OFF was missing.</li>
		</ul>
	</li>
	<li><strong>Import/Export</strong>
		<ul>
			<li>MOL/SDF: The coordinate bonds in R-group definitions were wrongly exported.</li>
			<li>Name to structure: Traditional naming export failed in applet.</li>
			<li>Name to structure: Document to Structure failed to extract names from PDF documents .</li>
		</ul>
	</li>
	<li><strong>Calculator Plugins</strong>
		<ul>
			<li>pKa prediction threw ArrayIndexOutOfBoundsException in case of oximes.</li>
			<li>Tautomerization threw ArrayIndexOutOfBoundsException in case of a few complex aromatic systems.</li>
	</ul>
	</li>
</ul>


<h3><a NAME="v5501" class="anchor"></a>May 12th, 2011: Marvin 5.5.0.1</h3>
<h4>Bugfixes</h4>
<ul>
	<li><strong>Calculator Plugins</strong>
		<ul>
			<li>Improved pKa prediction of the "OH" acid in oxim group.</li>
		</ul>
	</li>
	<li><strong>Structure Checker</strong>
		<ul>
			<li>SubstructureChecker did not work (requires JChem).</li>
		</ul>
	</li>
</ul>

<h3><a NAME="v5500" class="anchor"></a>May 3th, 2011: Marvin 5.5</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>Marvin Sketch</strong>
		<ul>
			<li>Bold Tool is intended to be used for graphical presentations of molecules. Activate the tool, click on a bond 
			and it will be changed to bold. Bold Tool is located in the Bond Menu, Tools toolbar, and bond PopUp menu by default. 
			Read more about its application in (<a href="../sketch/sketch-basic.html#boldaromatic">documentation</a>). </li>
			<li>The explicit valence setting display (v#) can be switched off (forum topics: <a 
			href="http://www.chemaxon.com/forum/viewpost19655.html&sid=34edcc0cb0d063b3c64eab04820bc928#19655">#1</a>, 
			<a href="https://www.chemaxon.com/forum/viewpost28923.html#28923">#2</a>;
			<a href="../sketch/gui/menubar.html#valence">documentation</a>).</li>
			<li>Ctrl + Mouse Wheel zooms in and out on the canvas, while the geometric center of the zoom is defined by the 
			current position of the mouse pointer.</li>
			<li>To scroll the canvas you can use the mouse wheel or the arrow keys on the keyboard.</li>
			<li>If there is a selection on the canvas, you can either move the selected object using the arrow keys or move the 
			canvas with Ctrl + Arrow Keys. 
			(See documentation: <a href="../sketch/sketch-gui.html">Marvin Sketch GUI</a>,
			<a href="../sketch/gui/shortcuts.html">shortcuts</a>,
			<a href="../sketch/gui/canvas.html">canvas</a>,
			<a href="../sketch/gui/gui-files/scroll_canvas.png">snapshoot</a>.)</li>
			<li>MarvinSketch canvas is centered to the scaffold after opening R-Group molecules.</li>
			<li>Atom List and Atom Not List can be entered by simple typing (without opening the Periodic Table).</li>
			<li>Symbols can be inserted into textboxes. An Insert Symbol dialog with recent symbol list is accessible from the 
			Textbox toolbar.</li>
			<li>Error messages, warnings, warning details, and error stack trace can be copied to the clipboard.</li>
			<li>Version and Copyright info can be found in Marvin splash screen.</li>
					</ul>
	</li>
	<li><strong>Marvin View</strong>
		<ul>
			<li><strong>Printing</strong> the molecule matrix view of a multirecord file containing data fields has been improved: 
			<ul>
				<li>Instead of cropping the content of the data fields when the cell is too narrow, they are wrapped into 
				multi-line texts automatically.</li>
				<li>A minimal size is provided for the molecule structure, so it is visible even if the data fields want to 
				occupy more space than the cell size.</li>
			</ul>
			<li>The explicit valence setting display (v#) can be switched off.</li>
			<li>Molecule matrix view is scrollable with mouse wheel.</li>
			<li>R-groups are visible in MarvinView by default.</li>
			</ul>
	</li>
	<li><strong>Marvin Space</strong>
		<ul>
			<li>Visualization of aromatic rings and triple bonds have been improved.</li>
		</ul>
	</li>
	<li><strong>Compatibility</strong>
		<ul>
			<li>Minimum <strong>Java</strong> requirement of Marvin is <strong>1.6</strong>.</li>
		</ul>
	</li>
	<li><strong>Data Transfer</strong>
		<ul>
			<li>If a selection is made on the structure in the MSketch window that has been opened from the MView pane, the 
			closing of the MSketch window (or clicking on the transfer button) transfers the selection back to the MView 
			pane.</li>
		</ul>
	</li>
	<li><strong>Chemical structure painting</strong>
		<ul>
			<li>Drawing 2D <strong>projected views</strong> of 3D aromatic rings
			(forum topics: <a href="http://www.chemaxon.com/forum/viewtopic.php?p=14630#14630">#1</a>,
			<a href="http://www.chemaxon.com/forum/viewtopic.php?t=3539&start=0">#2</a>).
				<ul>
					<li>Aromatic rings have ellipses in 3D view as well.</li>
					<li>Bold bonds and Aromatic Up bonds can be displayed.</li>
					<li>The display quality of the junctions of Bold and Up bonds with other bonds has been improved.</li>
				</ul>
			</li>
	    	<li>The electron flow arrow display has been improved: Radicals, lone pairs, and negative charges are considered now. 
			If the source is an atom, the electron flow automatically starts from the lone pair, the radical or the charge when 
			they are displayed.</li>
			<li>The automatic attachment point placement in R-group lists has been improved.</li>
		</ul>
	</li>	
	<li><strong>Electron flow arrow</strong>
	    <ul>
	    	<li>Electron flow arrows are redesigned. It is now separated to logic and display part.</li>
	    </ul>
	</li>
	<li><strong>Import/Export</strong>
		<ul>
			<li><strong>Image Export</strong>: 
				<ul>
					<li>JPG, SVG and PNG formats contain the molecule source, so when opening or pasting these images, the 
					molecule source is used (instead of trying to convert the image using OSRA).</li>
					<li>PNG image color-depth can be specified as bitrate: 1-, 2-, 4-, 8-, 24- and 32-bit PNG image can be 
					created.</li>
				</ul>
			</li>
			<li>Syn/Anti and Endo/Exo stereoisomer information is exported into ChemAxonExtended SMILES and SMARTS format.</li>
			<li>Data fields are exported into SMILES format.</li>
			<li>Improved SMART representation of generic query atoms (Q, QH, X, XH, etc.).</li>
			<li><strong>IUPAC Name</strong>
				<ul>
					<li>Major rewrite of the name to structure converter. Higher precision (around 50% 
					 improvement) and support for additional names.</li>
					<li>Import of sulfide has been improved in Name To Structure.</li>
					<li>Dictionary format improvement in IUPAC naming.</li>
					<li>Name To Structure supports Alpha and Beta sign in names.</li>
					<li>Automatic format recognition for CAS numbers. (This feature uses a Web service.)</li>
				</ul>
			</li>
			<li><strong>IUPAC InChI</strong>
				<ul>
					<li>Inchi converter now uses the IUPAC software API via the
					    jni-inchi instead of the executable. Now it can handle
					    stereochemistry information in 0D.</li>
					<li>Marvin now uses inchi 1.03.</li>
					<li>The conversion is much faster now.</li>
					<li>InChI export does not clean in 0d any more.</li>
				</ul>
			</li>
		</uL>
	</li>
	<li><strong>2D clean / aromatization</strong>
		<ul>
			<li>Keep stereo information at aromatization.</li>
			<li>Query aromatization considers single/aromatic and double/aromatic.</li>
			<li>Converting to aromatic form keeps the Bold property of the bond.</li>
		</ul>
	</li>
	<li><strong>Stereochemistry</strong>
		<ul>
			<li>Syn/Anti and Endo/Exo stereoisomers are recognized.</li>
			<li>Proper chirality detection in case of topographical differences.</li>
			<li>Define chirality for adamantane structures
			(<a href="https://www.chemaxon.com/forum/ftopic6130.html">forum topic</a>).</li>
			<li>New standardizer action: add implicit parities of bridged systems.</li>
			<li>NEMA features: atrop isomers, allenes.</li>
		</ul>
	</li>
	<li><strong>Structure Checker</strong>
		<ul>
		<li>New Checkers:</li>
		<ul>
		<li>SubstructureChecker detects user defined substructures (requires JChem);</li>
		<li>RacemateChecker detects tetrahedral stereo centers without specific stereochemistry defined.</li>
		</ul>
		<li>Improvements:</li>
		<ul><li>AromaticityErrorChecker searches for aromatic systems that can't be dearomatized or rearomatizing with the given method does not reproduce the initial molecule.</li>
		</ul>
		<li>Tooltip improvements.</li>
		<li>Checker status bar indicator can display warning and error icon as well.</li>
		<li>Rename of specific checkers is supported (affects SubstructureChecker).</li>
		</ul>
	</li>
	<li><strong>Calculator Plugins</strong>
		<ul>
			<li>Molecular volume calculator plugin has been introduced.</li>
			<li>Generic tautomer generation ignores all options except "Protect all tetrahedral stereo centers".</li>
			<li>Preserve exocyclic groups in BMF.</li>
		</ul>
	</li>
	<li><strong>Chemical Terms</strong>
		<ul>
			<li>Improving initialization speed of Chemical Terms Editor.</li>
			<li>Enhanced integration of BMF in Chemical Terms.</li>
			<li>New Chemical Terms function: markushLibrarySize(), markushLibrarySizeAsString(), markushLibraryMagnitude(), 
			 matchFirst(), booleanToNumber().</li>
		</ul>
	</li>
	<li><strong>Automapping</strong>
		<ul>
			<li>Integrate new MCES algorithm into AutoMapper.</li>
		</ul>
	</li>
	<li><strong>API</strong>
		<ul>
			<li>Implementation of MoleculeGraph.getAtomCount(int atomicNumber). It returns number of atoms in the molecule with 
			the given atomic number (<a href="https://www.chemaxon.com/forum/viewtopic.php?t=7124">forum topic</a>).</li>
		<li>Let user to insert new action into MarvinSketch popup menu.</li>
		</ul>
	</li>
</ul>

<h4>Bugfixes</h4>
<ul>
	<li><strong>Marvin Sketch</strong>
		<ul>
			<li>A bond to the second attachment point of an Sgroup could not be drawn if it was created by changing a terminal 
			atom of a previously drawn structure.</li>
			<li>The BzOM group has to be renamed to PhOMe, and its left name should be MeOPh 
			(<a href="https://www.chemaxon.com/forum/ftopic7312.html">forum topic</a>).</li>
			<li>Selecting every atom and using the "Ungroup" function from the menu caused ArrayInedOutOfBoundsException.</li>
			<li>Edit source dialog could not be opened after importing SKC.</li>
			<li>After setting of "Save/Load-default location - Startup directory" in Preferences dialog, Marvin did not navigate 
			to the Startup directory by saving or loading a molecule.</li>
			<li>The canvas was not scrollable with mouse wheel.</li>
			<li>When <em>Ala</em> was ungrouped in the <em>R1</em> definition, the attachment point (which was both an Sgroup and 
			an Rgroup attachment) disappeared.</li>
			<li>When a reagent was placed over the reaction arrow in abbreviated form, Marvin Sketch became unresponsive.</li>
			<li>When drawing electron flows to multicenter, Marvin stopped doing anything.</li>
			<li>Electron flows could cross double and triple bonds when starting from bond.</li>
			<li>The exception frame was not scrollable when the message was too long.</li>
			<li>Atoms from periodic table modified bond length when the atom in hand was attached to the end of a bond on the 
			canvas.</li>
			<li>The aromatization/dearomatization did not run during Directed Merge, therefore if the double bonds were not 
			located at the appropriate positions in case of two aromatizable rings, 5-valence carbon atoms were created.</li>
		</ul>
	</li>
	<li><strong>Marvin View</strong>
		<ul>
			<li>Scrolling and cleaning 2D in the same time caused CuncurrentModificationException.</li>
			<li>After clean 2D, Marvin View cells in molecule matrix view were not repainted automatically. You had to click on 
			each cell to refresh them.</li>
		</ul>
	</li>
	<li><strong>OLE support</strong>
		<ul>
			<li>StandAlone mode could not be set for all users.</li>
		</ul>
	</li>
	<li><strong>Molecule representation</strong>
		<ul>
			<li>QueryBond cloning did not preserve bond type.</li>
			<li>Exception related to expanding Sgroups in RgMolecules</li>
			<li>SuperatomSgroup.attachAtoms was not set automatically during SuperatomSgroup creation.</li>
		</ul>
	</li>
	<li><strong>Chemical Structure Painting</strong>
		<ul>
			<li>3D rotation of fragments started to wrong direction.</li>
			<li>Valence count displayed ugly: on the top of the atom label.</li>
			<li>The COCl group was not connected through the Carbon label when it was on the left side of a structure 
			(<a href="https://www.chemaxon.com/forum/ftopic7389.html.">forum topic</a>).</li>
		</ul>
	</li>
	<li><strong>Import/Export</strong>
		<ul>
			
			<li>Smiles did not set arrow for reaction because of it RXNMolecule threw NullPointerException.</li>
			<li>Molecule.toFormat("smarts") changed the state of Molecule: getAtomCount(), getBondCount() and the unifiedStructure 
			properties of query were null value.</li>
			<li>Certain MRV files could not be imported where <MPoint> tag included a line break.</li>
			<li>R-group queries could not be exported to SMARTS as recursive SMARTS in 5.4.x.</li>
			<li>ArrayIndexOutOfBoundsException was written on System.out by parsing SMILES.</li>
			<li><strong>IUPAC Name</strong>
				<uL>
					<li>By parsing <em>2-(o-Toluidino)ethanol</em> IUPAC name, the 'o' was ignored.</li>
					<li>IUPAC name import did not support correctly poly spiro in the simple case (numbers, not structures).</li>
					<li>Those IUPAC name could not be parsed from HTML where an <em>&amp;nbsp;</em> preceded the name.</li>
					<li>By importing certain IUPAC names, the stereo center missed on the created molecule.</li>
					<li>All IUPAC names with "hydrazon" were wrongly imported.</li>
					<li><em>dicarbonimidic diamide</em> and <em>imidodicarbonimidic diamide</em> were not supported.</li>
					<li>Generate correct name for hydrides of elements in group 1 to 12, for instance "sodium hydride".</li>
				</ul>
			</li>
			<li><strong>IUPAC InChi</strong>
				<ul>
					<li>There was a memory leak in the native call, now in the jni-inchi interface it is resolved.</li>
					<li>InChI failed to export stereochemistry on 0D molecules.</li>
				</ul>
			</li>
			<li>Certain CDX files that included graphical arrows and dashed rectangles could not import properly.</li>
		</ul>
	</li>
	<li><strong>2D cleaning / Aromatization</strong>
		<ul>
			<li>General aromatization put single-or-aromatic and double-or-aromatic bonds when it shouldn't.</li>
			<li>Molecule.partialClean(int, int[], String) removed stereo info from molecule when the input was in mrv format.</li>
			<li>At first attempt, the aromatization did not work after creating a ring with bold double bonds in it.</li>
			<li>The 2D clean of a molecule with position variation bond caused an error dialog: "Error in module Clean2D".</li>
			<li>Five membered rings were not converted to aromatic form with single_or_aromatic and double_or_aromatic bonds 
			respectively.
		</ul>
	</li>
	<li><strong>StereoChemistry</strong>
		<ul>
			<li>Superfluous wedges appeared by importing of certain SMILES.</li>
			<li>The Phosphorus in phosphines was not recognized as stereo center 
			(<a href="http://www.chemaxon.com/forum/ftopic5455.html">forum topic</a>).</li>
		</ul>
	</li>
	<li><strong>3D alignment</strong>
		<ul>
			<li>MCS based alignment bug: bad similarity score was returned for <em>CCC1CCCCC1.CCCCCCC1CCCCC1</em>.</li>
		</ul>
	</li>
	<li><strong>Calculator Plugins</strong>
		<ul>
			<li>Elemental Analyser result window did not display text fields in single fragment mode.</li>
			<li>In Naming Plugin result window, the IUPAC name disappeared after scroll down and scroll up by those compounds whose 
			name included special characters (like lambda).</li>
		</ul>
	</li>
	<li><strong>Chemical Terms</strong>
		<ul>
			<li>When a chem terms column was added in IJC that generated InChI, it occurred a lots of errors and JVM could crash 
			(<a href="http://www.chemaxon.com/forum/viewpost23832.html">forum topic</a>).</li>
		</ul>
	</li>
	<li><strong>API</strong>
		<ul>
			<li>Molecule.getBondTable() threw exception when a bond with atom index higher than atom count was get.</li>
			<li>MoleculeGraph.getFormula getMass and getExactMass were not thread-safe.</li>
			<li>When a new bond with a new end-atom was added to a SelectionMolecule, it was not cloned properly.</li>
			<li>Serialization threw an EOFException when it was used from MolImporter/MolExporter.</li>
		</ul>
	</li>
</ul>

<h3><a NAME="v5412" class="anchor"></a>April 8th, 2011: Marvin 5.4.1.2</h3>
<ul>
	<li><strong>Marvin Sketch</strong>
		<ul>
			<li>In the Save As Image dialog, there was a typo in the file type filter list.</li>
			<li>ArrayIndexOutOfBoundException in template parameter handling.</li>
		</ul>
	</li>
</ul>

<h3><a NAME="v5411" class="anchor"></a>February 16th, 2011: Marvin 5.4.1.1</h3>
<h4>Bugfix</h4>
<ul>
	<li><strong>MarvinSketch</strong>
		<ul>
			<li>Fix generic template toolbar removal ability throught <em>ttmpls</em> parameter. 
			Avoid warnings in template related code.</li>
		</ul>
	</li>
</ul>

<h3><a NAME="v5410" class="anchor"></a>February 9th, 2011: Marvin 5.4.1</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>MarvinSketch</strong>
		<ul>
			<li>Public database search in chemicalize.org.</li>
			<li>Possibility to change ligand order in MarvinSketch.</li>
		</ul>
	</li>
	<li><strong>Molecule Painting</strong>
		<ul>
			<li>Fog-effect can be controlled in various ways:
				<ul>
					<li>There is a slider on the Display tab of the Preferences dialog in the Sketch/View GUI to set the 
					strength of the fog-effect and a checkbox to switch off the automatic fog-effect.</li>
			 		<li>Applet and beans parameters are introduced: fogFactor, automaticFogEnabled. 
					(<a href="https://www.chemaxon.com/forum/viewtopic.php?p=32226#32226">forum topic</a>).</li>
				</ul>
			</li>
		</ul>
	</li>
	<li><strong>Molecule Representation</strong>
		<ul>
			<li>Attaching to a group attachment point does not automatically ungroup the Sgroup anymore.</li>
			<li>Determination of S-group content: multiple and generic S-groups have to contain the whole subgraph between 
			the bonds crossing the brackets.</li>
			<li>Update of S-group content if bracket positions change: either brackets or the structure is moved.</li>
			<li>S-group embedding and overlap are checked at molecule consistency checks.</li>
		</ul>
	</li>
	<li><strong>Import/Export</strong>
		<ul>
			<li>SMARTS import supports aliphatic carbons with double bond
			(<a href="https://www.chemaxon.com/forum/ftopic6711.html">forum topic</a>).</li>
			<li>SMILES import/export supports allenes.</li>
			<li>IUPAC name
				<ul>
					<li>Generate traditional name for fragments inside a larger compound (e.g. "dextromethorphan 
					 hydrobromide").</li>
					<li>Support for CAS number conversion.</li>
					<li>Improved OCR error correction in DocumentExtractor (missing commas, misrecognized parenthesis type, 
					extra spaces, ...).</li>
					<li>Document Extractor on PDF documents now gives the page number of hits.</li>
					<li>Improved support for common names in structure-to-name and name-to-structure. The general support for 
					name import increased by around 5% for common and 10% for systematic names. Conversion speed is increased 
					by about 20%.</li>
				</ul>
			</li>
		</ul>
	</li>
	<li><strong>Installers</strong>
		<ul>
			<li>JChem and Marvin Beans installers for Windows also extract shell script for cygwin.</li>
		</ul>
	</li>
	<li><strong>Calculator Plugins</strong>
		<ul>
			<li>Preserve exocycyclic groups in Bemis-Murcko frameworks.</li>
		</ul>
	</li>
	<li><strong>Markush Enumeration</strong>
		<ul>
			<li>Halogen can have more than one connection during enumeration.</li>
			<li>Aromatic bonds are considered during calculating the number of double bonds for homology property checking.</li>
		</ul>
	</li>
	<li><strong>API</strong>
		<ul>
			<li>Lasso selection mode can be set to be the default in MSketchPane
			(<a href="https://www.chemaxon.com/forum/ftopic7116.html">forum topic</a>).</li>
			<li>A new function, <em>isChiral</em> has been added to TopologyAnalyzer.</li>
			<li>New Chmical Terms functions: <em>isQuery()</em>, <em>fieldAsString()</em>.
		</ul>
	</li>
</ul>

<h4>Bugfixes</h4>
<ul>
	<li><strong>MarvinSketch</strong>
		<ul>
			<li>Name box was not updated after group creation because creating a group did not increase graph invariant 
			change count (<a href="https://www.chemaxon.com/forum/ftopic7029.html">forum topic</a>).</li>
			<li>MarvinSketch application did not understand "-{name}" command line parameter.</li>			
			<li>When the <em>Structure > Find structure online</em> menu was invoked under Linux, the default browser could not 
			be detected to display result (<a href="https://www.chemaxon.com/forum/ftopic7191.html">forum topic</a>).
		</ul>
	</li>
	<li><strong>Applet</strong>
		<ul>
			<li>The loading of the isotopes.data file from the java cache failed if its uncompressed version was stored in 
			the cache (i.e. if the uncompression was done automatically by the webserver, and not by Marvin). 
			<a href="https://www.chemaxon.com/forum/ftopic7036.html">Forum topic</a>, 
			<a href="../sketch/isotopelist.html">documentation</a>.</li>
		</ul>
	</li>
	<li><strong>Molecule Painting</strong>
		<ul>
			<li>Graphical objects were not displayed in the Geometry Plugin result window.</li>
			<li>On the electron-flow arrow, the blue box for highlighting was missed
			(<a href="https://www.chemaxon.com/forum/ftopic7033.html">forum topic</a>).</li>
			<li>Ligand order was not displayed on the scaffold if the definitions were deleted, even if the "Always" display
			option was set for "Ligand Orders" on the preferences dialog.</li>
		</ul>
	</li>

	<li><strong>Import/Export</strong>
		<ul>
			<li>Index of the multicenter atom became corrupt after exporting into MDL Mol format if it was connected to an 
			 R-group.</li>
			<li>Arrow stuck to rectangles was damaged during Mrv import/export
			(<a href="https://www.chemaxon.com/forum/viewtopic.php?t=7003">forum topic</a>).</li>
			<li>Import of 1-letter peptide failed at a single Histidine
			(<a href="https://www.chemaxon.com/forum/viewpost32247.html">forum topic</a>).</li>
			<li>Implicit hydrogens were missing by importing from skc.</li>
			<li>Null property value of MolAtom was not exported/imported in Mrv format.</li>
			<li>By importing certain SMARTS, wrong bond type was determined instead of <em>single or aromatic</em> bond.</li>
			<li>Certain brackets imported from Cdx files drawn in ChemDraw were displayed in the wrong direction.</li>
			<li>SMARTS export: 1-atom single bond neighbor of an aromatic ring was exported as <em>single</em> bond instead 
			of <em>single/aromatic</em>.</li>
			<li>Exporting queries with link node and R-groups in SMARTS format were incorrect.</li>
			<li>The H atom handling was ambiguous in SMARTS expressions.</li>
			<li>Mol file comment field import failed.</li>
			<li>Importing of certain SMILES was wrong
			(<a href="https://www.chemaxon.com/forum/ftopic6422.html">forum topic</a>).
		</ul>
	</li>
	<li><strong>Clean 2D</strong>
		<ul>
			<li>If the template contains a chain part which can be located in the target as a part of a ring (or ring system) we should ignore that part of the template.</li>
		</ul>
	</li>
	<li><strong>Molecule Representation</strong>
		<ul>
			<li>The <em>Fine with Hydrogenize</em> option of Clean3D did not ungroup S-groups automatically 
			(<a href="https://www.chemaxon.com/forum/ftopic6991.html">forum topic</a>).</li>
			<li>Molecule.contractSgroups() messed up the molecule coordinates
			(<a href="https://www.chemaxon.com/forum/ftopic6454.html">forum topic</a>).</li>
		</ul>
	</li>
	<li><strong>Stereochemistry</strong>
		<ul>
			<li>Explicit hydrogens were not properly taken into account in Cis/Trans stereochemistry calculation.</li>
		</ul>
	</li>
	<li><strong>Calculator Plugins</strong>
		<ul>
			<li>Tautomer generator threw ArrayIndexOutOfBoundsException in some cases.</li>
		</ul>
	</li>
	<li><strong>Markush Enumeration</strong>
		<ul>
			<li>Homology enumeration produced inconsistent molecules for some VMN input.</li>
		</ul>
	</li>
	<li><strong>Structure Checker</strong>
		<ul>
			<li>Incorrect charge count after applying CovalentCounterion fixer.</li>
			<li>The log output format of StructureChecker command line differed from output format of the wizard.</li>
			<li>ChiralFlagErrorChecker did not handle chirality correctly. Instead of calculating chirality from parity data, it used an implementation based on the isChiral method of TopologyAnalyser.</li>
		</ul>
	</li>
</ul>

<h3><a NAME="v5401" class="anchor"></a>December 17th, 2010: Marvin 5.4.0.1</h3>
<h4>Bugfixes</h4>
<ul>
	<li><strong>MarvinSketch</strong>
		<ul>
			<li>Highlighting of electron flow arrows was wrong when the base or end point was a bond
			(<a href="https://www.chemaxon.com/forum/viewpost32429.html#32429">forum topic</a>).</li>
		</ul>
	</li>
	<li><strong>Applet</strong>
		<ul>
			<li>Serialized menu configuration did not work.</li>
		</ul>
	</li>
	<li><strong>Import/Export</strong>
		<ul>
			<li><strong>MDL MOL and SDF</strong>
				<ul>
					<li>The index of the multicenter atom was incorrectly created during MDL MOLFile export in case of 
					structures where the R-group was connected to the multicenter atom (position variation bonds).</li>
				</ul>
			</li>
			<li><strong>SKC</strong>
				<ul>
					<li>Implicit hydrogens were missing from the atoms after the import or paste of structures in 
					ISIS/Symyx Sketch file format (SKC file format).</li>
				</ul>
			</li>
			<li><strong>CDX</strong>
				<ul>
					<li>CDX files including rotated graphical objects exported from old versions of ChemDraw could not 
					be imported.</li>
				</ul>
			</li>
		</ul>
	</li>
	<li><strong>Calculator Plugins</strong>
		<ul>
			<li><strong>Tautomer calculation</strong>
				<ul>
					<li>Generic tautomer was not unique enough in some cases. Due to the fix, the generic tautomer is 
					provided aromatic form.</li>
					<li>Canoncical tautomer calculation has been improved due to the fix of dearomatization.</li>
				</ul>
			</li>
		</ul>
	</li>
	<li><strong>cxcalc</strong>
		<ul>
			<li>Certain structures with explicit hydrogens connected with wedge bond gave an exception during 
			generic tautomerization.</li>
			<li>Volume calculation was missing from cxcalc.</li>
		</ul>
	</li>
	<li><strong>Structure Checker</strong>
		<ul>
			<li>Structure Checker threw NullPointerException instead of ignoring incorrect configuration file.</li>
		</ul>
	</li>
	<li><strong>API</strong>
		<ul>
			<li>chemaxon.struc.Molecule.implicitizeHydrogens(MolAtom.ALL_H) did not remove hydrogens.</li>
		</ul>
	</li>
</ul>

<h3><a NAME="v5400" class="anchor"></a>November 25th, 2010: Marvin 5.4.0</h3>
<h4>New features and improvements</h4>
<ul>
    <li><strong>MarvinSketch GUI</strong>
	<ul>
		<li>Multiple, ordered R-group attachment points in Markush structures. It is possible to define bond types on the
		ligands, and change the order of the attachment points as well.</li>
	    <li>PubChem substructure search from Marvin.</li>
	    <li>New 3D transformation options: rotate around various axes, group rotation, horizontal/vertical/group Flip,
		 horizontal/vertical/canvas/group Mirror, and Inversion
		(Documentation: <a href="../sketch/sketch-basic.html#move-rotate">Move and rotate</a>,
		 <a href="../sketch/sketch-basic.html#flip">Flip</a>, <a href="../sketch/sketch-basic.html#mirror">Mirror</a>,
		  <a href="../sketch/sketch-basic.html#inversion">Inverse</a>).</li>
	    <li>3D Plane option: upon three selected atoms it rotates the structure to have the selected atoms in the drawing plane
		(<a href="../sketch/sketch-basic.html#howto-draw">Documentation</a>).</li>
	    <li>Directed Merge option: It merges a fragment (Substituent) to another (Template or Scaffold) based on atom pairs
		 assigned by the Atom Assigner tool
		(<a href="../sketch/sketch-basic.html#merge">Documentation</a>).</li>
	    <li>New Substituent option: Opens a separate MSketch window to sketch a new fragment, and transfers it back to the main
		 window (<a href="../sketch/sketch-basic.html#newfragment">Documentation</a>).</li>
	    <li>Context based, configurable attached data editing
		 (Documentation: <a href="../sketch/sketch-chem.html#attacheddata">Attached Data</a> and
		  <a href="../sketch/sketch-attachDataConfig.html">Attach Data Config</a>).</li>
	    <li>Delete actions have been uniformized, and the Delete tool work differently on terminal atoms when holding the
		 Alt key. The default behavior of delete can be configured on the Preferences dialog
		  (<a href="http://www.chemaxon.com/forum/viewpost26224.html">forum topic</a>).</li>
		<li>Atom editing procedures have been improved, so the "feature" that automatically deleted a bond when an atom was
		 dragged over it has been removed.</li>
	</ul>
    </li>
   <li><strong>Applet</strong>
   <ul>
   		<li>New parameters have been introduced:
		<ul>
			<li>disablePluginsLookup (<a href="sketchman.html#otherparameters">Documentation</a>).</li>
			<li>defaultDatatransferPolicy (<a href="sketchman.html#otherparameters">Documentation</a>).</li>
			<li>isMyTemplatesEnabled (<a href="sketchman.html#displayparameters">Documentation</a>).</li>
		</ul>
		</li>
   </ul>
   </li>
    <li><strong>Molecule Painting</strong>
	<ul>
	    <li>Aromatic and double bond display improvements: now it follows the IUPAC recommendation in each case 
		(<a href="http://www.chemaxon.com/forum/viewpost12623.html#12623">forum topic</a>).</li>
	    <li>Ellipses are drawn in asymmetric aromatic rings in 2D.</li>
		<li>The R-group attachment point display has been changed to follow the IUPAC recommendation. The order of the
		 attachment points is visualized both at the Scaffold and the substituents.</li>
	    <li>R-logic can optionally be displayed on the sketch.</li>
		<li>The degree of rotation is displayed during rotations having specified axis.</li>
	</ul>
    </li>
    <li><strong>Molecule Representation</strong>
	<ul>
	    <li>S-groups are validated at creation based on the selected (sub)structure: only the chemically meaningful types can be
		 created. <a href="../sketch/sketch-basic.html#howto-draw.sgroups">How to draw
		  S-groups</a>. The option can be switched off on the Preferences dialog.</li>
		<li>R-group attachment point representation has been redesigned.</li>
	</ul>
    </li>
    <li><strong>Import/Export</strong>
	<ul>
	    <li><strong>IUPAC Naming</strong>
		<ul>
		    <li>Major upgrade of Name To Structure. Reduces wrong conversions by 50% to 70%, and supports about 10% more
			 names.</li>
			<li>Significant speedup (around 100%) in structure to name generation.</li>
		    <li>Local relative stereo (R* and S*) is supported in name to structure.</li>
		    <li>Pseudo-asymmetric stereo descriptors (r) and (s) are generated.</li>
		    <li>The generated names for salts have been improved.</li>
		    <li>Name generation detects new cases of very complex structures and aborts quickly instead of timing out or
			 locking. All known cases are handled well now.</li>
		</ul>
	    </li>
	    <li><strong>MRV</strong>
		<ul>
		    <li>The S-group and the R-group storage has been changed.</li>
		</ul>
	    </li>
	    <li><strong>MOL/SDF</strong>
		<ul>
		    <li>The S-group and the R-group storage has been separated: the APO tag stores only the R-group attachment points,
			 and the SAP tag is used for storing the S-group attachment points. The new storage format is not imported properly
			 by older versions of Marvin.</li>
		    <li>A new option, called "skipAtomValues" has been introduced in MolImporter and molconvert to avoid importing lines
			 containing atom values (<a href="../formats/mol-csmol-doc.html#ioptions">Documentation</a>).</li>
			 <li>SDFiles (incorrectly) having an empty line between the CTAB block and the properties block are accepted at
			  import.</li>
		</ul>
	    <li><strong>SMILES/SMARTS</strong>
		<ul>
		    <li>SMARTS representation of X
			(<a href="http://www.chemaxon.com/forum/viewtopic.php?p=19370#19370">forum topic</a>).</li>
		</ul>
	    </li> 
	    <li><strong>CDX/CDXML</strong>
		<ul>
		    <li>CDX/CDXML import has been improved: TLC plates, table objects and r-logic are imported now.</li>
		</ul>
	    </li>
	</ul>
    </li>
    <li><strong>Datatransfer</strong>
	<ul>
	    <li>Configurable clipboard handling in applets: the file formats available for copy and paste can be customized.
		<a href="./applets/clipboard_configuration.html">Documentation</a>.</li>
	    <li>Marvin OLE object: 64-bit support for windows natives (OLE, EMF generator).</li>
		<li>Traditional name has been added to the Copy as dialog formats.</li>
	</ul>
    </li>
    <li><strong>Stereochemistry</strong>
	<ul>
	    <li>Graph invariant calculation uses local parity instead of local chirality.</li>
	    <li>Improved stereochemistry (r, s, M, P descriptors).</li>
	</ul>
    </li>
    <li><strong>Aromatization</strong>
	<ul>
	    <li>New aromatization option for ambiguous 5-membered rings. Linking documents:
		<a href="../formats/basic-export-opts.html">Basic Export Options</a>,
		<a href="../sci/aromatization-doc.html">Aromaticity Detection</a>.</li>
	    <li>Query aromatization changes single bonds to single_or_aromatic, double bonds to double_or_aromatic in a ring which
		 contains _or_aromatic bond. Linking document:
		<a href="../sci/aromatization-doc.html">Aromaticity Detection</a>.</li>
	</ul>
    </li>
    <li><strong>Calculator Plugins</strong>
	<ul>
	    <li>logP and logD plugins support multiple logP training parameter files.</li>
	    <li>pKa and logD plugins support multiple pKa correction library files.</li>
	    <li>Fused output option on Markush enumerator GUI
		(<a href="https://www.chemaxon.com/forum/ftopic6308.html">forum topic</a>).</li>
	    <li>Industry standard MMFF94 forcefield options in Conformer Generator and 3D Molecular Properties.</li>
	    <li>Atrop isomers are detected during conformer generation.</li>
	    <li>Canonic tauotomer  generation was improved.</li>
	    <li>Performance and accuracy improvements of 3D molecular alignment.</li>
		<li>Structure Checker functions in Chemical Terms.
		 <a href="../chemicalterms/EvaluatorFunctions.html">Documentation</a>.</li>
		<li>Chemical Terms uses a new file format for storing predefined molecules. The new format contains also the R-group
		 representation of the molecules, which can be used for visualization.</li>
	    <li><strong>Training and Prediction</strong>
		<ul>
		    <li>A new plugin called Predictor enables to train and run the users' own molecular property prediction. 
			<a href="../calculations/predictor.html">Documentation</a>.</li>
		    <li>A command line trainer tool has been introduced, called cxtrain.</li>
		    <li>LogP and General (Atomic Contribution Based) Training and Prediction has been refactored (performance
			improvement and structural changes).</li>
		</ul>
	    </li>
	</ul>
    <li><strong>Structure Checker</strong>
	<ul>
	    <li>New checkers are available:
		<ul>
		    <li>Empty Structure Checker</li>
		    <li>Explicit Lone Pair Checker</li>
		    <li>Star Atom Checker</li>
		    <li>Metallocene Checker</li>
		    <li>Covalent Counterion Checker</li>
		    <li>OCR Error Checker</li>
		    <li>Ring Strain Error Checker</li>
		</ul>
		<a href="https://www.chemaxon.com/marvin/help/structurechecker/checker.html">Read about available checkers</a></li>
	    <li>A command line tool called structurecheck is available for checking files in batch mode.</li>
	    <li>Application based structure checker configuration support has been added to the API. It is also available in the
		 applications that are integrating Structure Checker.</li>
	    <li>Several improvements have been made in the Overlapping Atoms and the Overlapping Bonds checkers: The accuracy of the
		 overlap detection has been improved to avoid fault warnings.</li>
	    </li>
	</ul>
	<li><strong>Licenses</strong>
        <ul>
            <li>All Topology Analysis functions require a valid license.</li>
        </ul>
    </li>
    <li><strong>Installer</strong>
        <ul>
            <li>Full 64 bit support: 64-bit installers are available for all platforms.</li>
            <li>Registration of file associations can be turned-off with one checkox.</li>
        </ul>
    </li>
   </li>
</ul>   
<h4>Bugfixes</h4>
<ul>
    <li><strong>MarvinSketch GUI</strong>
	<ul>
	    <li>Delete action sometimes had undesired effect from atom context menu.</li>
	    <li>On Mac OS X, Drag and Drop of names to the canvas did not work
		 (<a href="https://www.chemaxon.com/forum/ftopic6171.html">forum topic</a>).</li>
	</ul>
    </li>
    <li><strong>Marvin Space GUI</strong>
	<ul>
	    <li>The screen flickered and an empty canvas was displayed sometimes
		 (<a href="https://www.chemaxon.com/forum/ftopic5290.html">forum topic</a>).</li>
	    <li>There was a frame closing problem when MarvinSpace was launched for MarvinSketch (forum topics:
		 <a href="https://www.chemaxon.com/forum/ftopic2924.html">1</a>,
		 <a href="https://www.chemaxon.com/forum/ftopic2905.html">2</a>).</li>
	    <li>The pull-down menus were not displayed properly (forum topics:
		 <a href="https://www.chemaxon.com/forum/ftopic5109.html">1</a>,
		 <a href="https://www.chemaxon.com/forum/ftopic4677.html">2</a>,
		 <a href="https://www.chemaxon.com/forum/ftopic4906.html">3</a>, 
		 <a href="https://www.chemaxon.com/forum/ftopic5548.html">4</a>,
		 <a href="https://www.chemaxon.com/forum/ftopic5728.html">5</a>,
		 <a href="https://www.chemaxon.com/forum/ftopic5098.html">6</a>,
		 <a href="https://www.chemaxon.com/forum/ftopic5290.html">7</a>).</li>
	</ul>
    </li>
   <li><strong>Applet</strong>
	<ul>
	    <li>On Mac OS X, in Safari, the opening of the Periodic Table (or other modal) dialog caused freezing the MarvinSketch
		 applet.</li>
	</ul>
    </li>
    <li><strong>Molecule Painting</strong>
	<ul>
	    <li>Charges disappeared on carbon atoms if carbon labels were hidden and lone pair painting 
		was turned off
		(<a href="https://www.chemaxon.com/forum/ftopic6693.html">forum topic</a>).</li>
	    <li>Certain MEflow object disappeared when display properties were changed
		 (<a href="https://www.chemaxon.com/forum/ftopic6643.html">forum topic</a>).</li>
		<li>The second data attached to the same selection using absolute placement overlapped with the first one.</li>
	</ul>
    </li>
    <li><strong>Molecule Representation</strong>
	<ul>
	    <li>RgMolecule.addRgroup(int rl, Molecule m) did not change the grinvCC, therefore the graph union was not updated and
		  had not contained the newly added Rgroup.</li>
	    <li>The bond table representation of the MoleculeGraph has been changed to avoid OutOfMemoryError.</li>
	    <li>There were a few S-groups related bugs: in creating a reaction from a molecule with S-groups, in creating S-groups
		 in reactions, and in merge/unmerge of reaction components containing S-groups.</li>
	    <li>When deleting multiple groups, n-1 occurrences of its content remained.</li>
	</ul>
    </li>
    <li><strong>Import/Export</strong>
	<ul>
	    <li>0D molfiles can not contain stereo information, therefore Marvin applications apply a 2D clean to 0D molfiles in the
		 future.</li>
	    <li><strong>SMILES/SMARTS</strong>
		<ul>
		    <li>Certain SMARTS was imported wrongly.</li>
		    <li>Explicit bond appeared where it was not needed in SMARTS.</li>
		</ul>
	    </li>
	</ul>
    </li>
    <li><strong>Datatransfer</strong>
	<ul>
	    <li>InChi with AuxInfo failed to paste in into Marvin.</li>
	</ul>
    </li>
    <li><strong>Stereochemistry</strong>
	<ul>
	    <li>If only one explicit Hydrogen is attached to one side of the double bond, then it incorrectly got E/Z stereo
		 information.</li>
	    <li>The calculation of chirality/parity for phosphanes was incorrect in some cases
		 (<a href="https://www.chemaxon.com/forum/ftopic6340.html">forum topic</a>).</li>
	    <li>Remove explicit hydrogen removing some stereochemistry
		(<a href="https://www.chemaxon.com/forum/ftopic6817.html">forum topic</a>).
	</ul>
    </li>
	<li><strong>Clean 2D</strong>
    <ul>
		<li>Cleaning of ferrocenes created some extremely long bonds.</li>
    </ul>
    <li><strong>Calculator Plugins</strong>
	<ul>
    	<li>Moieties (Repeating Units without crossing bonds) were ignored in Markush enumeration.</li>
		<li>HBDAPlugin threw ClassCastException in some cases.</li>
		<li>Some bugs were fixed in the tautomer generation (<a href="https://www.chemaxon.com/forum/ftopic6945.html">forum topic</a>).</li>
	</ul>
    </li>
</ul>

<h3><a NAME="v5308" class="anchor"></a>September 3rd, 2010: Marvin 5.3.8</h3>
<h4>Bugfixes</h4>
<ul>
    <li><strong>MarvinSketch</strong>
	<ul>
	    <li>Lone pair placement wrong in some case.</li> 
	    <li>Simple arrows could not be deleted at enpoints.</li>
	</ul>
    </li>
    <li><strong>Import/Export</strong>
	<ul>
	    <li>molconvert.bat cxsmarts:qh failed with explicit H (<a href="https://www.chemaxon.com/forum/ftopic4996.html">forum topic</a>).</li>
	    <li>Exporting of Single Electron Flow Arrow to Mrv failed.</li>
	</ul>
    </li>
</ul>

<h3><a NAME="v5307" class="anchor"></a>August 13th, 2010: Marvin 5.3.7</h3>
<h4>Bugfixes</h4>
<ul>
	<li><strong>Applet</strong>
		<ul>
			<li>Marvin applets did not start with Turkish default locale.</li>
		</ul>
	</li>
	<li><strong>Molecule representation</strong>
		<ul>
			<li>Small brackets were displayed looking like a box when reading polymer Sgroups from MOL V2000 format.</li>
		</ul>
	</li>
	<li><strong>Painting</strong>
		<ul>
			<li>When a reactant contained Sgroup, reaction arrow was not drawn.</li>
			<li>If an Sgroup was a single reactant in a reaction, it could not be ungrouped.</li>
		</ul>
	</li>
	<li><strong>Data transfer</strong>
		<ul>
			<li>Clipboard operation hanged with an error in ExecutorService if
			<em>legacy_lifecycle</em> was off and two applets were started in the same JVM
			(<a href="https://www.chemaxon.com/forum/viewpost30284.html">forum topic</a>).</li>
			<li>OLE copy fail-over mechanism did not work.</li>
		</ul>
	</li>
	<li><strong>Calculator Plugins</strong>
		<ul>
			<li>PluginFactory threw NullPointerException on applet loading.</li>
		</ul>
	</li>
</ul>

<h3><a NAME="v5306" class="anchor"></a>July 15th, 2010: Marvin 5.3.6</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>Molecule representation</strong>
		<ul>
			<li>Warning message on incorrect s-group selection in reactions.</li>
		</ul>
	</li>
	<li><strong>Calculator Plugins</strong>
		<ul>
			<li>Markush enumeration: valence filter is set off by default.</li>
		</ul>
	</li>
        <li><strong>Import/Export</strong>
            <ul>
                <li><strong>IUPAC Name</strong>
                    <ul>
                        <li>Name generation has been improved and handles more traditional names.</li>
                        <li>Shorter names are generated in more cases, for
                        instance "pentamethylhydrazinium" instead of "1,1,1,2,2-pentamethylhydrazinium".</li>
                        <li>Name generation for large structures succeeds with a systematic, Von Baeyer name instead of failing with a timeout.
                    </ul>
                </li>
            </ul>
        </li>
</ul>

<h4>Bugfixes</h4>
<ul>
	<li><strong>MarvinSketch</strong>
		<ul>
			<li>Scaffold zoom was not working.</li>
		</ul>
	</li>
	<li><strong>MarvinSpace</strong>
		<ul>
			<li>MSpace did not start on various platforms or OS versions: the installation 
			of native OpenGL libraries failed in some cases.</li>
			<li>MSpace menus were displayed behind the canvas
			(<a href="http://www.chemaxon.com/forum/ftopic4677.html">forum topic</a>).</li>

		</ul>
	</li>
	<li><strong>Editing</strong>
		<ul>
			<li>When trying to open the Format dialog from contextual menu over the
			corner point of a bracket, the Format dialog was empty.</li>
			<li>Uniformization and bug fixes in delete actions.</li>
		</ul>
	</li>
	<li><strong>Painting</strong>
		<ul>
			<li>R group sign was misplaced when inserting a graphical object into the sketch.</li>
		</ul>
	</li>
	<li><strong>Import/Export</strong>
		<ul>
			<li>MDL MOL import of incorrect bracket information caused AIOB
			(<a href="https://www.chemaxon.com/forum/viewtopic.php?p=29301#29301">forum topic</a>).</li>
			<li>InChI failed to export stereochemistry on 0D molecules.</li>
			<li>InChI export silently ignored unknown options.</li>
			<li>SRU was read as repeating group with repetition ranges.</li>
			<li>ReaxysGenerics were not imported from Extended MOLFile.</li>
                        <li>In IUPAC Name export, generation of E/Z stereo descriptors was missed in certain cases.</li>
                        </li>
		</ul>
	</li>
	<li><strong>Molecule representation</strong>
	<ul>
		<li>The implicit H was not correct after importing cxSmarts string with radical information.</li>
		<li>Changing group type to MultipleSgroup produced inconsistent molecule.</li>
		<li>Changeing a type of a non-expandable s-group to SRU didn't change bracket type from double to single.</li>
		<li>MarvinSketch had a problem with valence calculation after removing explicit hydrogens.</li>
		<li>Rubber tool was not working for attached data.</li>
		<li>On terminal groups the Repeating unit with repetition ranges group type had only a single bracket.</li>
	</ul>
	</li>
</ul>

<h3><a NAME="v5305" class="anchor"></a>June 24th, 2010: Marvin 5.3.5</h3>
<h4>Bugfixes</h4>
<ul>
	<li><strong>MarvinSketch GUI</strong>
		<ul>
			<li>Setting "Automatic Lone Pair Calculation" off does not 
			work after restarting MarvinSketch.</li>
			<li>The Save as image menu action did not update the save directory.</li>
		</ul>
	</li>
	<li><strong>Applet</strong>
		<ul>
			<li>Marvin Applets could not load-in resources from out of jars.</li>
			<li>Improve Java Plugin detecting mechanism in marvin.js to print warning
			when required Java is missing.</li>
		</ul>
	</li>
	<li><strong>Import/Export</strong>
		<ul>
		<li>Certain generics from extended molfile that had saved by 
		Marvin 5.2.x could not be loaded.</li>
		</ul>
	</li>
</ul>

<h3><a NAME="v5304" class="anchor"></a>June 15th, 2010: Marvin 5.3.4</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>IUPAC Name2Structure</strong>
		<ul>
			<li>Non standard capitalization of names in the custom dictionary are also accepted.</li>
		</ul>
	</li>
	<li><strong>Marvin OLE support</strong>
		<ul>
			<li>It is possible to change the default editing mode of Marvin objects in MS Office applications to Stand Alone mode 
			(i.e. upon double click the object is opened in a separate office document) by excecuting the corresponding 
			registry setting (.reg) files 
			(<a href="https://www.chemaxon.com/marvin/help/marvinoleserver.html#editing_mode">documentation</a>).</li>
		</ul>
	</li>
</ul>
<h4>Bugfixes</h4>
<ul>
	<li><strong>MarvinSketch</strong>
		<ul>
			<li>Loading predifined styles in format dialog threw an exception.</li>
			<li>Structure checker dialog was displayed behind the main sketcher window in OS X.</li>
		</ul>
	</li>
	<li><strong>Applet</strong>
		<ul>
			<li>There was a false request to download chemaxon/marvin/dict.class.</li>
			<li>Template files that were out of jars could not be accessed.</li>
			<li>In the student examination example, newer Java Plugins (1.6.0_19 or higher) could not load resource bundles 
			of customization.xml from outside jar.</li>
			<li>RejectedExecutionException was thrown by module loading.</li>
			<li>IUPAC name recognition system could not load functional groups dictionary in case of newer Java Plugins
			(1.6.0_19 or higher).</li>
		</ul>
	</li>
	<li><strong>Compatibility</strong>
		<ul>
			<li>In Safari, under OS X 10.6, the default value of "legacy_lifecycle" applet parameter has been changed (to false)
			to avoid hanging by concurrent applet loading and insufficent delay at the closing of applet pages.</li>
			<li>In the default configuration of Marvin applets, the "codebase_lookup" Java Plugin parameter has been set 
			to false to avoid insufficent search of certain localization files in applet codebase.</li>
		</ul>
	</li>
	<li><strong>Import/Export</strong>
		<ul>
			<li>If a MOL/SDF file contained incorrect bracket coordinates info, an exception was thrown. Now the proper 
			coordinates for the corresponding Sgroup are automatically generated in these cases
			(<a href="http://www.chemaxon.com/forum/ftopic6322.html">forum topic</a>).</li>
		</ul>
	</li>
	<li><strong>Molecule Representation</strong>
		<ul>
			<li>There was a valence check problem with NC4-radicals and C-C4radicals
			(<a href="http://www.chemaxon.com/forum/ftopic4097.html">forum topic</a>).</li>
		</ul>
	</li>
	<li><strong>Calculator plugins</strong>
		<ul>
			<li>Tautomerization of certain SMILES with explicit hydrogens failed
			(<a href="https://www.chemaxon.com/forum/viewtopic.php?p=29218#29218">forum topic</a>).</li>
		</ul>
	</li>
	<li><strong>API</strong>
		<ul>
			<li>Periodic system modality problem inside MSketchPane on a dialog or frame.</li>
			<li>The Periodic System dialog was not displayed properly in Instant JChem.</li>
			<li>Structure Checker dialog stuck under MSketchPane when it was in a modal dialog under Mac OS X.</li>
			<li><code>chemaxon.util.ImageExportUtil</code> did not calculate the size of the image properly in certain 
			cases.</li>
			<li>ChiralityFlagErrorChecker got wrong result when molecule contained a stereo with no enhanced stereo 
			property.</li>
		</ul>
	</li>
</ul>

<h3><a NAME="V5303" class="anchor"></a>May 14th, 2010: Marvin 5.3.3</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>MarvinSketch</strong>
		<ul>
			<li>A new option for direct structure search in a public database has been introduced: Find Structure in PubChem.</li>
		</ul>
	</li>
	<li><strong>Applet</strong>
		<ul>
			<li>A message dialog containing a progress bar is displayed if the loading of a module takes more than 100 milliseconds, which automatically disappears when the module is loaded.</li>
			<li>New applet parameters ahve been introduced to control pre-loading of modules:
			<a href="sketchman.html#preload">preload</a>,<a href="sketchman.html#bgloadEnabled">bgloadEnabled</a> and <a href="sketchman.html#bgloadPriority">bgloadPriority</a>.</li>
		</ul>
	</li>
	<li><strong>Molecule representation</strong>
		<ul>
			<li>A new method has been introduced that returns MolAtom: MolAtom.getLigands()
			(<a href="https://www.chemaxon.com/forum/viewpost28125.html#28125">forum topic</a>).</li>
		</ul>
	</li>
	<li><strong>Calculator Plugins</strong>
		<ul>
			<li>Tautomers: dominant tautomers are returned by default, instead of all tautomers (cxcalc, Chemical Terms, GUI).</li>
            <li>Markush enumeration: New homology example definitions are used, which are based on matching statistics from 
			a large data base.</li>
		    <li>TPSA: Sulfur and Phosphorus atoms optionally can be excluded from calculation
			(<a href="https://www.chemaxon.com/forum/viewtopic.php?p=28304#28304">forum topic</a>).</li>
		</ul>
	</li>
</ul>

<h4>Bugfixes</h4>
<ul>
	<li><strong>MarvinSketch</strong>
		<ul>
			<li> The "extraBonds" parameter value was missing for Single Bold Bond, therefore the Bold Single Type could not be removed from the Bond menu.</li>
			<li>Joining two multicenters threw an exception in some cases.</li>
		</ul>
	</li>
	<li><strong>MarvinView</strong>
		<ul>
			<li>After opening a file in MView by adding the file name as a parameter, the "Save Selection" menu did not worked in some cases.</li>
		</ul>
	</li>
	<li><strong>Applet</strong>
		<ul>
			<li>Certain resources (like template files) could not be loaded-in with Java 1.6.0_19 and above versions. Since Java blocked loading resources from untrusted content.
			(<a href="https://www.chemaxon.com/forum/ftopic6191.html">forum topic</a>).</li>
		</ul>
	</li>
	<li><strong>Painting</strong>
		<ul>
			<li>The lone-pair placement was incorrect in some cases
			(<a href="https://www.chemaxon.com/forum/viewpost27810.html#27810">forum topic</a>).</li>
		</ul>
	</li>
	<li><strong>Import/Export</strong>
		<ul>
			<li>Mrv: Reaction arrow parameters exported into Mrv files with Marvin 4.1.13 or older versions were not 
			 imported properly.</li>
			<li>Name to Structure: The stero information was lost during the conversion of Name to some other formats 
			(inchi, mol, sdf, ...).</li>
		</ul>
	</li>
	<li><strong>Molecule representation</strong>
		<ul>
			<li>Deleting atoms in reactions raised exception, did not ungroup the superatom s-group and could split content of 
			one s-group into several reaction components.</li>
			<li>During setting bond array in Sgroup constructor, an exception was thrown when calling mol.clonecopy(int[], MoleculeGraph).</li>
			<li>The attachment point information was missing when creating abbreviated group in MarvinSketch.</li>
			<li>Markush enumeration failed on repeating unit with repetition ranges
			(<a href="https://www.chemaxon.com/forum/ftopic6055.html">forum topic</a>).</li>
		</ul>
	</li>
	<li><strong>Calculator Plugins</strong>
		<ul>
			<li>StereoisomerPlugin returned wrong stereoisomer count via API (<a href="https://www.chemaxon.com/forum/ftopic6130.html">forum topic</a>).</li>
			<li>Random Markush enumeration was very slow for highly complex Markush structures.</li>
		</ul>
	</li>
	<li><strong>Chemical Terms</strong>
		<ul>
			<li><code>"evaluate --list-functions"</code> threw NullPointerException if the user's Evaluator configuration file was missing.</li>
		</ul>
	</li>
</ul>

<h3><a NAME="V5302" class="anchor"></a>April 14th, 2010: Marvin 5.3.2</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>MarvinSketch</strong>
		<ul>
			<li>New parameter "abbrevgroupsAllowed" is introduced to be able to disable abbreviated groups on key types
			(<a href="http://www.chemaxon.com/marvin/help/developer/sketchman.html#parameters.abbrevgroupsAllowed">documentation</a>,
			 <a href="https://www.chemaxon.com/marvin/examples/applets/sketch/studentexam/index.html">example</a>,
			 <a href="http://www.chemaxon.com/forum/ftopic5218.html">forum topic</a>).</li>
			<li>New parameter "toolbarFloatable" is introduced that can be used to make the toolbars immovable
			(<a href="http://www.chemaxon.com/marvin/help/developer/sketchman.html#parameters.toolbarFloatable">documentation</a>,
			 <a href="http://www.chemaxon.com/forum/ftopic5405.html">forum topic</a>).</li>
			<li>Changes in the Structure > Aromatization menu of MarvinSketch: renaming menu elements, the default aromatization method is changed to Basic.</li>
			<li>Change in the behavior of the Custom Property buttons on the Periodic System dialog window. 
			(The buttons are not toggled automatically any more. If the Value cannot be represented in the given Type, the Value text field is highlighted.)
			(<a href="http://www.chemaxon.com/marvin/help/sketch/gui/dialogs.html#periodic">Forum topic</a>.)</li>
		</ul>
	</li>
		<li><strong>Import/Export</strong>
		<ul>
			<li>ISIS (Symyx) sketch file (skc) format import and export.</li>
			<li>CDXML import.</li>
			<li>InChI is now available on windows 64 bit.</li>
		</ul>
	</li>
	<li><strong>Licenses</strong>
		<ul>
			<li>Arbitrary license file names are supported under the user's home directory 
			(<a href="http://www.chemaxon.com/marvin/help/licensedoc/install.html">documentation</a>).</li>
			<li>It is possible to define multiple license file locations using environment variables or Java system properties
			(documentation for <a href="http://www.chemaxon.com/marvin/help/licensedoc/installToDesktop.html">desktop applications</a> | 
			 <a href="http://www.chemaxon.com/marvin/help/licensedoc/installToServer.html">servers</a>).</li>
		</ul>
	</li>
	<li><strong>Calculator Plugins</strong>
		<ul>
			<li>ElementalAnalyser and ElementalAnalyserPlugin can accept molecular formula input, so mass, atom count, etc. calculations can be performed on molecular formula.</li>
			<li>New option for excluding halogens from acceptors in HBDAPlugin. Halogens are excluded from acceptors by default.</li>
			<li>Molecular volume calculation
			(<a href="https://www.chemaxon.com/forum/viewpost25792.html">forum topic</a>).</li>
			<li>Projected surface area calculation: new implementation, speedup and enhanced accuracy
			(forum topic <a href="https://www.chemaxon.com/forum/viewpost19701.html#19701">#1</a>,
			 <a href="https://www.chemaxon.com/forum/posting.php?mode=reply&t=4522">#2</a>).</li>	
			<li>The import of traditional names supports greek letters, salts, sulfonamides and bi/tri/... phenyl groups.</li>
			<li>Name import supports mixture names with proportions, for instance 'Methanol, bismuth(3+) salt (3:1)'
			(<a href="https://www.chemaxon.com/forum/ftopic5920.html">forum topic</a>).</li>
		</ul>
	</li>
	<li><strong>API</strong>
		<ul>
			<li>New MolPrinter API method to display valence errors
			<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/MolPrinter.html#isValenceErrorvisible%28%29">MolPrinter.isValenceErrorVisible()</a>.</li>
		</ul>
	</li>
</ul>
<h4>Bugfixes</h4>
<ul>
	<li><strong>MarvinSketch</strong>
		<ul>
			<li>After adding a Multicenter to a structure, the menu item did not become disabled, and a second invocation threw an AIOOB exception.</li>
			<li>Sometimes an NPE was thrown at initialization of Abbreviated groups (<a href="http://www.chemaxon.com/forum/ftopic5759.html">forum topic</a>).</li>
			<li>Switching from 3D to 2D mode using the button on the statusbar caused loosing stereo information.</li>
			<li>It was impossible to resize a textbox created by mouse-click
			(forum topic <a href="http://www.chemaxon.com/forum/viewpost26344.html#26344">#1</a>,
			 <a href="http://www.chemaxon.com/forum/ftopic5300.html">#2</a>).</li>
			<li>It was impossible to create ladder-type repeating unit with repeatition ranges in sketcher.</li>
			<li>Adding bonds in bond drawing mode sometimes damaged the molecule.</li>
			<li>It was possible to create too small textboxes by drag-drawing; a minimum size was intorduced for graphic objects to avoid that (<a href="http://www.chemaxon.com/forum/viewtopic.php?t=5799">forum topic</a>).</li>
			<li>The hydrogen number was not updated after creating position variation bond.</li>
		</ul>		
	</li>
	<li><strong>MarvinView</strong>
		<ul>
	    	<li>The MarvinView parameter "tabScale" did not work properly
			(<a href="http://www.chemaxon.com/forum/ftopic4653.html">forum topic</a>).</li>
			<li>Unnecessary hourglass cursor was displayed in MarvinView's spreadsheet view when selecting a structure.</li>
			<li>MarvinView did not display associtated file in OS X. Dragging molecule file into MarvinView did not work either
			(<a href="https://www.chemaxon.com/forum/ftopic4130.html">forum topic</a>).</li>
		</ul>
	</li>
	<li><strong>MarvinSpace</strong>
		<ul>
			<li>Transparent surface drawing artefact in MarvinSpace.</li>
		</ul>
	</li>
	<li><strong>Import/Export</strong>
		<ul>
			<li>Mol2 export did not export fragment count properly and there was a multifragment charge problem
			(<a href="http://www.chemaxon.com/forum/viewpost26448.html#26448">forum topic</a>).</li>
			<li>There were OSRA errors on some Linux and Macs machines.</li>
			<li>IJC and JCman crashed when using inchi chemical terms expressions.</li>
		</ul>
	</li>
	<li><strong>Molecule Representation</strong>
		<ul>
			<li>Bold bond type remained set when changing the bond type to single/double/triple from menu.</li>
			<li>Mrv import of two contracted embedded abbreviated S-groups was not working.</li>
			<li>There was an endless loop in MolSearch on a structure having a Multicenter S-group.</li>
			<li>The E/Z stereo information was incorrectly displayed after S-group expansion
			(<a href="https://www.chemaxon.com/forum/ftopic5577.html">forum topic</a>).</li>
			<li>Attachment points were mixed after copy and then paste or export and then import.</li>
			<li>Ungroup function of Sketcher didn't remove the empty S-groups.</li>
		</ul>
	</li>
	<li><strong>Template Handling</strong>
		<ul>
			<li>Template parameters (tmpls, ttmpls) did not work properly.</li>
			<li>Moving template sets up or down in the Template Library did not have affect on the toolbar.</li>
		</ul>
	</li>
	<li><strong>Calculator Plugins</strong>
		<ul>
		<li>Molecule with homology atoms and undefined R-atoms threw ExpansionException during markush enumeration.</li>
		<li>JavaDoc on MarkushEnumerationPlugin.setEnumerateHomology and setMolecule order dependency was not up-to-date.</li>
		<li>Markush enumeration was not working on ladder-type repeating unit with repeatition ranges structures.</li>
		<li>The MarvinView result window of the 3D Aligment calculation in MarvinSketch contained the coordinate info of the
			removed Hydrogen atoms, which sometimes resulted in not enough space for the display of the aligned structures.</li>
		</ul>
	</li>
</ul>

<h3><a NAME="V5301" class="anchor"></a>February 22th, 2010: Marvin 5.3.1</h3>
<h4>Bugfixes</h4>
<ul>
	<li><strong>MarvinSketch</strong>
	<ul>
		<li>Manual mapping did not work.</li>
		<li>The bond between the two atoms of the multicenter part of a 
		position variation bond was deleted when a new bond was added to 
		the position variation bond by click drawing</li>
	</ul>
	</li>
	<li><strong>Applet</strong>
	<ul>
		<li>Applet could not be reffered from JavaScript in Chrome.</li>
	</ul>
	</li>
	<li><strong>Calculator Plugins</strong>
	<ul>
		<li>Avoid NullPointerException in logPFragmentCreator.</li>
		<li>logP calculation in cxcalc did not produce output under Linux.</li>
		<li>pKa training threw ArrayIndexOutOfBounds exception</li>
	</ul>
	</li>
</ul>

<h3><a NAME="V53002" class="anchor"></a>January 29th, 2010: Marvin *******</h3>
<h4>Bugfixes</h4>
<ul>
    <li><strong>MarvinSpace</strong>
        <ul>
            <li>MarvinSpace could not be launched without a valid conformer license.</li>
        </ul>
    </li>
    <li><strong>Applet</strong>
	<ul>
	    <li>The unix2local method converted string by wrong way under Mac.</li>
	</ul>
    </li>
    <li><strong>API</strong>
	<ul>
	    <li>chemaxon.struc.StaticMolecule.getExplicitHcount() returned with null value instead of zero when no explicit hcount was defined for any atoms.</li>
	</ul>
    </li>
    <li><strong>Marvin OLE</strong>
        <ul>
            <li>An empty molecule was returned by the first invocation of the getMol method of Marvin OLE server after setMol.</li>
        </ul>
    </li>
    <li><strong>Documentation</strong>
	<ul>
	    <li>Complete apidoc for Structure Checker API</li>
	</ul>
    </li>
    <li><strong>Installer</strong>
        <ul>
            <li>The Marvin installer did nothing in silent mode (<em>-q</em> command line parameter) if an earlier version of Marvin was already installed previously.</li>
        </ul>
    </li>
</ul>

<h3><a NAME="V53001" class="anchor"></a>January 21th, 2010: Marvin 5.3.0.1</h3>
<h4>Bugfixes</h4>
<ul>
    <li><strong>Marvin Sketch</strong>
        <ul>
            <li>Structure Checker Progress bar remained on the screen during structure fixing.</li>
        </ul>
    </li>
    <li><strong>Applets</strong>
        <ul>
            <li>Chemical terms calculation did not work from MarvinSketch applet.</li>
        </ul>
    </li>
</ul>

<h3><a NAME="V5300" class="anchor"></a>January 15th, 2010: Marvin 5.3</h3>
<h4>New features and improvements</h4>
<ul>
<li><strong>MarvinSketch GUI</strong>
    <ul>
	<li>Structure Checker add-on, a tool for detecting molecule features or drawing errors is integrated.</li>
	<li>The Template Library is redesigned. It allows hierarchic template structure and custom locations to be set in the library. Templates are loaded dynamically (<a href="../sketch/gui/dialogs.html#template_library">documentation</a>).</li>
	<li>OLE anti-aliasing is improved: enhanced embedded object quality in MS Office documents.</li>
	<li>"Sprout" drawing: attaching templates and abbreviated groups by automatically inserting a bond. Using the SHIFT button the rings are attached as spiro, while the abbreviated groups are attached in expanded form. Rotation by 15 degrees is possible before releasing the mouse button.</li>
	<li>Bracket drawing tool: simple group creation without the need of preselection; the targeted substructure just have to rounded by the bracket tool.</li>
	<li>Bracket editing: group type can be changed in Edit > Group (no need for recreation of group).</li>
	<li>Data can be attached to brackets.</li>
	<li>Possibility to load up to 100 molecules from multirecord files to the canvas. Ranges can be defined during the opening process.</li>
	<li>Importing images by converting them to structures (using OSRA in the background).</li>
	<li>The Copy As dialog is redesigned to contain explicit formats. At the same time, the Copy tab of the Edit > Preferences dialog has been removed.</li>
	<li>Copy/Paste: single line text formats (name, inchi, ...) can be pasted even if they contain extra newlines.</li>
	<li>New bond type: bold bond.</li>
	<li>Ellipse drawing and circle drawing.</li>
	<li>Rectangle with rounded corners drawing. Square drawing.</li>
	<li>Direct structure search in public database: ChemSpider.</li>
	<li>Web links are detected in attached data, and opened in browser by Ctrl-click.</li>
	<li>The layout of the Attach Data dialog is improved, new data types are introduced and listed by default in "Name" field: [DUP], REAGENT. 
	See the <a href="http://www.chemaxon.com/marvin/help/sketch/gui/dialogs.html#attachdata">documentation</a>.</li>

	<li>Copying Marvin Objects to MS Office documents is sped up.</li>
	<li>CDX format is placed on the clipboard by default copy action and automatically recognized during paste.</li>
    </ul>
</li>
<li><strong>MarvinView GUI</strong>
    <ul>
	<li>Text search in molecule tables.</li>
	<li>Marvin View can now open name files even when not all names are supported.</li>
	<li>Save layout in MarvinView.</li>
    </ul>
</li>
<li><strong>MolConverter</strong>
    <ul>
	<li>New parameters: -U for union output (fuse) and -I <range> for processing input molecules in index range
	(<a href="http://www.chemaxon.com/forum/viewpost25001.html#25001">forum topic</a>).</li>
    </ul>
</li>
<li><strong>Marvin Space</strong>
    <ul>
	<li>3D molecular alignment is integrated into Marvin Space.</li>
	<li>Save structure in Marvin Space.</li>
    </ul>
</li>
<li><strong>Applets</strong>
    <ul>
	<li>Move Marvin applets (JMSketch, JMView) into the chemaxon.marvin.applets package.</li>
	</ul>
</li>
<li><strong>Chemical Structure Painting</strong>
    <ul>
	<li>Electron flow arrows start at closest lone pair electron.</li>
    </ul>
</li>
<li><strong>Molecule Representation</strong>
    <ul>
		<li>Annotating with URLs.</li>
		<li>New bond type: bold bond.</li>
		<li>Atoms with three similar wedge had no parity value.</li>
		<li>A bracket button for S-group bracket drawing.</li>
		<li>Valence check of alkali metals has been changed. (Alkali metals are not imported as alkali hydrides: Na is imported without implicit hydrogen atom instead of NaH.)</li>
    </ul>
</li>
<li><strong>Import/Export</strong>
    <ul>
	<li>Concurrent processing in molecule import/export.</li>
	<li>CDX Export.</li>
	<li>VMN file import.</li>
	<li>Usage of S-group bracket coordinate info (BRKXYZ tags) and style info (SBT) in MDL MOL formats.</li>
	<li>Import and paste images via OSRA.</li>
	<li><strong>SMILES/SMARTS</strong>
	    <ul>
		<li>Aliphatic explicit H is written as [H] instead of [#1A]</li>
		<li>SMILES export default behavior to check graph invariants at CIS TRANS export.</li>
	    </ul>
	</li>
	<li><strong>IUPAC Name</strong>
	    <ul>
		<li>Concise names generated for acyl-CoA (coenzyme A) derivatives.</li>
		<li>Dictionary of common and traditional names have vastly improved and augmented.</li>
		<li>Structure to name generates many more common names with the "traditional" option.</li>
	    </ul>
	</li>
    </ul>
</li>
<li><strong>Clean 2D</strong>
    <ul>
	<li>Use the template wedges in PartialClean.</li>
	<li>Clean of adamantane structures is nicer.</li>
    </ul>
</li>
<li><strong>3D Alignment</strong>
    <ul>
	<li>Alignment by pharmacophore types.</li>
	<li>Alignment by Maximum common substructure.</li>
    </ul>
</li>
<li><strong>Calculator Plugins</strong>
    <ul>
	<li>Tooltips with help messages are displayed on Calculator Plugins options panels.</li>
	<li>Some Calculator Plugin options panels have been redesigned, to fit small resolution displays (<a href="http://www.chemaxon.com/forum/ftopic5070.html">forum topic</a>).</li>
	<li>Tautomerization Plugin: ester groups are not considered as tautomerizable region by default but you can switch it on.</li>
	<li>In HBDA Plugin, sulfur atoms are not hydrogen bond acceptors by default (optional)
	(<a href="http://www.chemaxon.com/forum/viewtopic.php?p=22667#22667">forum topic</a>).</li>
    </ul>
</li>
<li><strong>Documentation</strong>
	<ul>
		<li>MarvinSketch and MarvinView documentation (JavaHelp) supports Favorites, which allows help topics to be saved and reused.</li>
	</ul>
</li>
<li><strong>Licenses</strong>
	<ul>
		<li>License files can be merged with the command line tool 
		(See <a href="http://www.chemaxon.com/marvin/help/licensedoc/install.html#merge">merging license</a>)</li>
	</ul>
</li>
<li><strong>API</strong>
	<ul>
		<li>MarvinBeans.jar has been devided into several small jars.</li>
		<li>New methods in Marvin Applets API: JMSketch.clean2D(), JMSketch.clean3D().</li>
		<li>API to add custom file formats to clipboard functionalities in Marvin.</li>
	</ul>
</li>
</ul>

<h4>Bugfixes</h4>
<ul>
<li><strong>MarvinSketch</strong>
    <ul>
		<li>Fixing compatibility problem of structure templates with a file named <i>custom.t</i>.</li>
		<li>Some of the tooltips in the Reaxys Generics dialog were mixed.</li>
		<li>Undoing the deletion of a reaction arrow was not working in case of there was nothing more on the canvas.</li>
		<li>Anti-aliasing was not working in OLE objects if atom labels were present in the structure.</li>
		<li>The multipage function buttons were missing on statusbar.</li>
		<li>NullPointerException in Marvin Sketch when opening a recent file.</li>
	<li>E/Z labels display option was not saved in MarvinSketch.</li>
    </ul>
</li>
<li><strong>Applet</strong>
    <ul>
	<li>URL of java download pages in marvin.jar have been modified (<a href="http://www.chemaxon.com/forum/viewpost25288.html#25288">forum topic</a>).</li>
	<li>Security errors in applet when it called from JavaScript
	(<a href="http://www.chemaxon.com/forum/ftopic5236.html">forum topic</a>).</li>
    </ul>
</li>
<li><strong>Import/Export</strong>
    <ul>
	<li><strong>SMILES/SMARTS</strong>
		<ul>
		<li>Clean2D not called from molconvert cxsmiles:+c</li>
		<li>SMILES export: elements in the "organic subset" is considered more seriously.</li>
		<li>SMILES export without stereo info.</li>
		</ul>
	</li>
	<li><strong>IUPAC Name</strong>
		<ul>
			<li>Add missing chiral modifiers in the name of pure hydrocarbons.</li>
		</ul>
	</li>
    </ul>
</li>
<li><strong>Clean2D</strong>
    <ul>
	<li>Stereochemistry error during 2D clean.</li>
	<li>Badly cleaned molecules in JWS example.</li>
	<li>Improvement of multicenter bonds' 2D cleaning.</li>
	<li>The Hydrogenize Chiral Center option in 2D clean changed the stereochemistry.</li>
	<li>Clean2D threw exception for graph containing only hydrogen atoms.</li>
    </ul>
</li>
<li><strong>Generate 3D</strong>
    <ul>
	<li>3D coordinate generation stereo critieria handling has been improved.</li>
	<li>Bugfix in clean 3D parallel execution.</li>
	<li>General improvement in conformer generation 
	(<a href="http://www.chemaxon.com/forum/ftopic4980.html">forum topic</a>).</li>
	<li>Forcefield (Dreiding) patch: correct geometry fo hydrazine (CNNC) like substructures.</li>
	<li>
ChemAxon force-field enhancement: planar geometry of aniline nitrogen is ensured (<a href="http://www.chemaxon.com/forum/viewpost24064.html">forum topic</a>).</li>
    </ul>
</li>
<li><strong>Molecule Representation</strong>
    <ul>
	<li>StereoChemistry: CIS TRANS interpretation fix.</li>
	<li>Add explicit H should not move the original atoms (<a href="http://www.chemaxon.com/forum/ftopic5304.html">forum topic</a>).</li>	
	<li>Chirality fix for symmetrical molecules.</li>
    </ul>
</li>
<li><strong>Installer</strong>
    <ul>
	<li>Batch files failed if its path was C:\Program Files (x86)\ChemAxon (<a href="http://www.chemaxon.com/forum/viewpost22411.html#22411">forum topic</a>).</li>
	<li>Installer did not upgrade the previously installed OLE server to newer version.</li>
    </ul>
</li>
<li><strong>API</strong>
	<ul>
		<li>MolPrinter.paint(Graphics2D,Dimension) threw NullPointerException if clip was not set for the graphics.</li>
	</ul>
</li>
</ul>

<h3><a NAME="V5205" class="anchor"></a>September 11th, 2009: Marvin 5.2.5</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>Import/Export</strong>:
		<ul>
			<li>Structures can be saved in InChiKey format from the GUI. 
			InChiKey can also be selected in the Edit > Source window.</li>
			<li>Support rel- relative stereo names in name import.</li>
			<li>Support of nucleosides and nucleotides in name import.</li>
			<li>Better support for traditional names in name import (over 15% more 
			names supported from NCI database).</li>
			<li>Better support of stereo descriptors in name import: cis-, trans- is supported.</li>
		</ul>
	</li>
    <li><strong>Documentation</strong>
        <ul>
            <li>New public example demonstrating the usage of text boxes with formatted text.</li>
        </ul>
    </li>
  <li><strong>API</strong>
	<ul>
			<li>New getter and setter methods in MolPrinter API for trancparency, 
			ballRadius, stickThickness, wireThickness, lonePairsAutoCalculated, 
			carbonVisibility, boundingRectangle.</li>
			<li>Name to structure extraction from text and html documents API has 
			been added to the name-to-structure infrastructure (see the api documentation 
			of chemaxon.naming.DocumentExtractor.</li>
			<li>Custom name conversion plugin API has been added to the name-to-structure 
			infrastructure (see the api documentation of chemaxon.naming.NameConverters). 
			This allows for instance the integration of a large in-house structure 
			dictionary (including names) stored in a local or remote database into 
			name-to-structure and document-to-structure conversions.</li>
			<li>Deprecated asynchronous molecule setting methods in MViewPane API: 
			setM(int, String), setM(int, String, String) and setM(int, File, String).</li>
	</ul>
    </li>
   <li><strong>.NET integration</strong>
       <ul>
           <li>MarvinBeans for .NET snapshot version is available
           (<a href="http://www.chemaxon.com/marvin/download-dev.html#marvindotnet">download</a>, 
		   <a href="http://www.chemaxon.com/dotNET/">documentation</a>).</li>
       </ul>
    </li>
</ul>
<h4>Bugfixes</h4>
<ul>
    <li><strong>MarvinView</strong>
        <ul>
            <li>MView table cell resizing is not working 
			(<a href="http://www.chemaxon.com/forum/viewpost24029.html">forum topic</a>).</li>
        </ul>
    </li>
    <li><strong>Import/Export</strong>
        <ul>
			<li>Aliphatic property was not added to aliphatic C with doublebond and 
			any bond during SMARTS 
			(<a href="http://www.chemaxon.com/forum/ftopic5123.html">forum topic</a>).</li>
            <li>MarvinView and MarvinSketch sometimes crashed when bad InChI was 
			imported.</li>
        </ul>        
    </li>
    <li><strong>Chemical Structure Painting</strong>
        <ul>
	        <li>The Wireframe with Knobs display option did not work.</li>
	     </ul>
    </li>
	<li><strong>Clean 2D</strong>
		<ul>
		<li>Explicit H atoms having parity were improperly added with the Clean option.</li>
		<li>2D cleaning of a bridged ring having another ring as neighbor was wrong.</li>
		</ul>
	</li>
    <li><strong>Licenses</strong>
        <ul>
            <li>Resonance plugin displayed a warning without a license (although it 
			is a free plugin).</li>
            <li>There was a minor bug in license key verification.</li>
        </ul>
    </li>
</ul>

<h3><a NAME="V5204" class="anchor"></a>August 11th, 2009: Marvin 5.2.4</h3>
<h4>New features and improvements</h4>
<ul>
   <li><strong>MarvinSketch</strong>
       <ul>
           <li>New icon is added to the "Add Attachment Point" action
           (<a href="http://www.chemaxon.com/forum/ftopic5018.html">forum topic</a>).</li>
           <li>New keyboard shortcuts: 5 - Single Up, 6 - Single Down, 7 - Wiggly 
		   bond.</li>
       </ul>
   </li>
   <li><strong>Molecule representation</strong>
       <ul>
           <li>Add function that returns improved information of parity.</li>
       </ul>
   </li>
</ul>
<h4>Bugfixes</h4>
<ul>
    <li><strong>MarvinSketch</strong>
        <ul>
            <li>Custom menus were not loaded after restarting MarvinSketch.</li>
        </ul>
    </li>
    <li><strong>MarvinView</strong>
        <ul>
            <li>Print All did nothing if multiple pages were sent to the printer 
			from MarvinView.</li>
        </ul>
    </li>
    <li><strong>Licenses</strong>
        <ul>
            <li>Fixed the displayed state of licenses to be installed. (Replaced 
			"Invalid" with "To be installed".)</li>
        </ul>
    </li>
    <li><strong>Applet</strong>
        <ul>
            <li>Complete the missing isLeopardSafari() method in marvin.js 
			(marvin/sketch/index.jsp did not load in Mac Safari)
            (<a href="http://www.chemaxon.com/forum/viewtopic.php?p=23804#23804">forum 
			topic</a>).</li>
        </ul>
    </li>
    <li><strong>Import/Export</strong>
        <ul>
            <li>Partial charges numeric format in mol2 is fixed
            (<a href="http://www.chemaxon.com/forum/viewpost21849.html#21849">forum 
			topic</a>).</li>
        </ul>        
    </li>
    <li><strong>Molecule representation</strong>
        <ul>
            <li>Nitrogen with double bond and explicit H gets stereo value
            (<a href="http://www.chemaxon.com/forum/ftopic5106.html">forum topic</a>).</li>
            <li>Bug was in setDim method in RgMolecule.</li>
        </ul>
    </li>
    <li><strong>Chemical Structure Painting</strong>
        <ul>
	        <li>Bracket overwrote lone pairs, image generation clipped off lone 
			pairs/charge symol (<a href="http://www.chemaxon.com/forum/ftopic5077.html">
			forum topic</a>).</li>
	     </ul>
    </li>
    <li><strong>Calculator Plugins</strong>
        <ul>
            <li>Canonical tautomer calculation threw ArrayIndexOutOfBoundsException 
			in some cases
            (<a href="http://www.chemaxon.com/forum/ftopic5063.html">forum topic</a>).
        </ul>
    </li>
</ul>

<h3><a NAME="V52032" class="anchor"></a>July 29th, 2009: Marvin *******</h3>
<h4>New features and improvements</h4>
<ul>
    <li><strong>API</strong>
	<ul>
	<li>New functions are added that return improved information of parity:
	<em>Parity.asymmetricAtoms(MoleculeGraph)</em>, 
	<em>Parity.chiralAtoms(MoleculeGraph)</em>.</li>
	</ul>
    </li>
</ul>

<h4>Bugfixes</h4>
<ul>
    <li><strong>MarvinSketch</strong>
	<ul>
	<li>Initialization problem in MarvinSketch if it was initialized through MarvinOLEServer.</li>
	</ul>
    </li>
    <li><strong>Calculator Plugins</strong>
	<ul>
	<li>The chiralCenterCount function in TopologyAnalyser plugin returned now zero for the number of chiral centers of 1,4-dimethylcyclohexane.</li>
	</ul>
    </li>
</ul>

<h3><a NAME="V52031" class="anchor"></a>July 15th, 2009: Marvin 5.2.3.1</h3>
<p>No changes</p>

<h3><a NAME="V5203" class="anchor"></a>July 8th, 2009: Marvin 5.2.3</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>MarvinSketch</strong>
	<ul>
		<li>A splash screen image is shown during the initialization of the MarvinSketch
		standalone version.</li>
	</ul></li>
	<li><strong>Import/Export</strong>
	<ul>
		<li>Partial charges are saved in mol2 file format based on the calculation 
		result of the charge plugin (charge license required; if it is not found then 
		the mol2 file is created without charges).
		(<a href="http://www.chemaxon.com/forum/viewpost21849.html#21849">forum topic</a>).</li>
		<li>Relative stereo descriptors (using enhanced stereo OR groups) is supported 
		in name export.</li>
                <li>Amino acid recognition in Name import has been improved: substituents are recognized, D/L configuration is supported.</li>
                <li>Importing non standard names is more flexible: more freedom at numbering, bracers and dashes.</li>
		<li>Cis/trans releated information is supported in name import.</li>
	</ul></li>
	<li><strong>Clean 2D</strong>
	<ul>
		<li>The arrangement of wedge bonds using the 'Clean wedge bonds' option has
		been improved.
		(<a href="http://www.chemaxon.com/forum/ftopic4899.html">forum topic</a>).</li>
	</ul></li>
	<li><strong>Applet</strong>
	<ul>
		<li>The loading of MSketch Applet is sped up: the total size of the files 
		downloaded during the first initialization has been reduced by 14% compared 
		to the previous version.</li>
                <li>The value of the "legacy_lifecycle" parameter is true in default in Marvin applets if you use marvin.js (see <a href="sketchman.html#parameters.legacy_lifecycle">applet parameters</a>).</li>
		<li>New methods have been introduced in marvin.js, which return with the generated 
		applet source instead of writing it into the html source: 
		<em>msketch_end_to_string()</em>, <em>mview_end_to_string()</em>
		and <em>mspace_end_to_string()</em>.</li>
                <li>A new applet parameter has been introduced ("appletid") to 
                identify applet in 
		the propertychange event notification sent to JavaScript.</li>
		<li>The marvin.js uses ID attribute instead of NAME to refer to the applet.</li>
	</ul></li>
	<li><strong>Chemical Terms</strong>
	<ul>
		<li>New Chemical Terms functions have been introduced:
		<ul>
			<li>importMol()</li>
			<li>isMarkush() 
			(<a href="http://www.chemaxon.com/forum/viewtopic.php?p=22969#22969">forum topic</a>)</li>
			<li>whereIsValenceError()</li>
			<li>hasAromatizationError()</li>
		</ul>
		</li>
	</ul></li>
	<li><strong>Calculator plugins</strong>
	<ul>
		<li>A new method has been implemented in the Structural Frameworks plugin 
		of the Geometry plugin: checkMolecule().</li>
		<li>The major tautomer generation has been sped up
		(<a href="http://www.chemaxon.com/forum/ftopic4738.html">forum topic</a>).</li>
		<li>Stereoisomer plugin can generate stereoisomers with given chirality as constraint
        (<a href="http://www.chemaxon.com/forum/ftopic3675.html">forum topic</a>).</li>
	</ul></li>
</ul>

<h4>Bugfixes</h4>
<ul>
	<li><strong>MarvinSketch</strong>
	<ul>
		<li>The 'Recent file list' contained two separate (and incorrect) items if 
		the filename contained a comma character (divided at the comma).</li>
                <li>OLE failed with some repaint events when the canvas was in the background of other java components.</li>
	</ul></li>
	<li><strong>Applet</strong>
	<ul>
		<li>The JVM version in the EMBED and the OBJECT tag in the marvin.js generated 
		code was not up-to-date. In addition to the update the NAME attribute was changed to ID 
		(<a href="http://www.chemaxon.com/forum/viewtopic.php?p=22976#22976">forum topic</a>).</li>
		<li>The "viewCarbonVisibility" applet parameter did not work
		(<a href="http://www.chemaxon.com/forum/ftopic4673.html">forum topic</a>).</li>
	</ul></li>
	<li><strong>Import/Export</strong>
	<ul>
		<li>Some R/S and E/Z stereo descriptors were missing in generated names.</li>
		<li>Failing on name generation of very complex structures was not handled 
		too gracefully.</li>
		<li>cxsmarts:h failed
		(<a href="http://www.chemaxon.com/forum/ftopic4996.html">forum topic</a>).</li>
		<li>MOLFiles with incorrect spaces at the end of the new type of Atom list
		definition lines can now be imported without error.</li>
                <li>CDX import failed at some reaction mapping.</li>
	</ul></li>
	<li><strong>Molecule representation</strong>
	<ul>
		<li>There was a NullPointerException in aromatization
		(<a href="http://www.chemaxon.com/forum/ftopic5009.html">forum topic</a>).</li>
		<li>Copy/paste and import/export of a molecule in mrv format having an S-Group with 
		only one atom did not work
		(<a href="http://www.chemaxon.com/forum/viewpost20739.html#20739">forum topic</a>).</li>
		<li>The getAromaticAndAliphaticRings method threw NullPointerException
		(<a href="http://www.chemaxon.com/forum/ftopic5009.html">forum topic</a>).</li>
	</ul></li>
	<li><strong>Calculator plugins</strong>
	<ul>
		<li>The exceptions of the Structural Frameworks plugin were sometimes improper (now it throws 
		PluginException on error).</li>
		<li>Projected surface area prediction produced inconsistent values
		(<a href="http://www.chemaxon.com/forum/viewpost17016.html#17016">forum topic</a>).</li>
	</ul></li>
	<li><strong>Template handling</strong>
	<ul>
		<li>Homology templates were missing in applets.</li>
	</ul></li>
	<li><strong>Installation</strong>
	<ul>
		<li>Marvin/JChem batch files could not handle quotes in the parameter list
		(<a href="http://www.chemaxon.com/forum/viewtopic.php?p=22695">forum topic</a>).
	</ul></li>
	<li><strong>Licenses</strong>
	<ul>
		<li>The use of the setLicense or setLicenseFile API resulted in unusable 
		client licenses unless they had the "Server Mode Allowed" field
		(<a href="http://www.chemaxon.com/forum/viewtopic.php?p=22700#22700">forum topic</a>).</li>
	</ul></li>
</ul>

<h3><a NAME="V5202" class="anchor"></a>May 27th, 2009: Marvin 5.2.2</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>MarvinSketch</strong>:
		<ul>
			<li>The <em>Clear Atom List</em> button is added to the
			<em>Periodic System</em> dialog window
			(<a href="http://www.chemaxon.com/forum/ftopic4571.htmlhttp://www.chemaxon.com/forum/ftopic4571.html">forum topic</a>).</li>
			<li>Icon for Reaxys Generics is added.</li>
		</ul>
	</li>
	<li><strong>MolConverter</strong>:
		<ul>
			<li>Option <em>-m</em> produces multiple output files from multi-molecule input
			(<a href="https://corp.chemaxon.com/flyspray/index.php?do=details&task_id=7940">forum topic</a>).
		</ul>
	</li>
	<li><strong>Applet</strong>:
		<ul>
			<li>New applet parameters for setting license files
			(forum topics: <a href="http://www.chemaxon.com/forum/ftopic3715.html">#3715</a>,
			 <a href="http://www.chemaxon.com/forum/ftopic4796.html">#4796</a>).</li>
		</ul>
	</li>
	<li><strong>Import/Export</strong>:
		<ul>
			<li>InChi 1.02 version has been integrated.</li>
			<li>InChiKey can be exported. New export option: <em>inchi:key</em></li>
		</ul>
	</li>
	<li><strong>Calculator Plugins</strong>:
		<ul>
			<li>Added average microspecies charge calculations to cxcalc
			(<em>averagemicrospeciescharge</em>) and Chemical Terms 
			(<em>averageMicrospeciesCharge()</em>)
			(<a href="http://www.chemaxon.com/forum/viewpost18838.html#18838">forum topic</a>).
		</ul>
	</li>
	<li><strong>Documentation</strong>:
		<ul>
			<li><em>chemaxon.marvin.MolPrinter</em> and 
			<em>chemaxon.formats.MolConverter</em> API are improved.</li>
		</ul>
	</li>
	<li><strong>API</strong>:
		<ul>
			<li>Named constants for applet and bean parameters are available in the API:
			<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/common/ParameterConstants.html">chemaxon.marvin.common.ParameterConstants</a>.</li>
			<li>New MolRenderer API available:
			<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/beans/MolRenderer.html">chemaxon.marvin.beans.MolRenderer</a>.</li>
			<li>Deprecated methods in <em>chemaxon.formats.MolImporter</em>: parameterless constructor, <em>setOptions(String)</em>, <em>setFileName(String)</em>.</li>
		</ul>
	</li>
</ul>
<h4>Bugfixes</h4>
<ul>
	<li><strong>MarvinSketch</strong>:
		<ul>
			<li>Undo sets viewport to lower-right corner/Undo-Redo scaling.</li>
			<li>Number of missing from menu label AND1 and AND2 in 
			<em>Atom &gt; Stereo &gt; Enhanced</em> menu.</li>
		</ul>
	</li>
	<li><strong>MarvinView</strong>:
		<ul>
			<li>Selected cell becomes unselected after changing table options.</li>
		</ul>
	</li>
	<li><strong>Import/Export</strong>:
		<ul>
			<li>Pseudoatom export bug in MDL Molfile export.</li>
			<li>Chiral center had wrong connectivity at atom.
			(<a href="http://www.chemaxon.com/forum/ftopic4795.html">forum topic</a>).</li>
			<li><em>chemaxon.util.MolHandler</em> threw ClassCastException by SMILES.</li>
			<li>Unique SMILES export bugs.</li>
			<li>ClassCastException threw by endless loop for invalid SMILES import.
			(<a href="http://www.chemaxon.com/forum/ftopic4782.html">forum topic</a>)</li>
			<li>Reaction arrow was not visible by creating JPEG reaction images.</li>
			<li>MRV import failed for polymer in abbreviated S-group.</li>
			<li>IUPAC name import bug by certain structures.</li>
		</ul>
	</li>
	<li><strong>Molecule representation</strong>:
		<ul>
			<li>The <em>chemaxon.marvin.MolPrinter.setMol</em> method ignored atom sets.</li>
		</ul>
	</li>
	<li><strong>Chemical structure and graphical object painting</strong>:
		<ul>
			<li><em>NullPointerException</em> in a <em>MolPrinter</em> example
			(<a href="http://www.chemaxon.com/forum/viewpost21913.html#21913">forum topic</a>).</li>
			<li>The <em>Get Sketch Image</em> button on Marvin Beans example 
			(<em>examples/beans/sketch-images/SketchImages</em>) generated misplaced drawing.</li>
		</ul>
	</li>
	<li><strong>Calculator Plugins</strong>:
		<ul>
			<li>3D flexible alignment throws <em>NullPointerException</em> if molecules
			are too dissimilar to align.</li>
			<li>Timelimit setting added to 3D flexible alignment API.</li>
		</ul>
	</li>
	<li><strong>Licenses</strong>:
		<ul>
			<li>Name to Structure was not running freely from Marvin.</li>
		</ul>
	</li>
</ul>

<h3><a NAME="V5201" class="anchor"></a>April 14th, 2009: Marvin 5.2.1</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>MarvinSketch</strong>
		<ul>
			<li>Copy actions run in background thread.</li>
			<li>Speed up OLE copy: MarvinOLEServer running in the background permanently.</li>
		</ul>
	</li>
	<li><strong>Applet</strong>
		<ul>
			<li>Customizable applet splash screen which is displayed while the applet is loading (<a href="../../examples/applets/sketch/splashscreen.html">live example</a>).</li>
		</ul>
	</li>
	<li><strong>Import/Export</strong>
		<ul>
			<li>Allows aromatic boron in cxsmiles.</li>
			<li>cxsmarts format stores attachment point data.</li>
			<li>Support for atom values in cxsmiles.</li>
			<li>Keep wedge arrangement in cxsmiles
			(<a href="http://www.chemaxon.com/forum/viewtopic.php?p=19796">forum topic</a>).</li>
		</ul>
	</li>
	<li><strong>Calculator Plugins</strong>
		<ul>
			<li>Structural frameworks plugin is moved to Other plugin group, 3D Alignment plugin is moved to Conformation plugin group.</li>
			<li>Added stereoDoubleBondCount calculation to TopologyAnalyserPlugin.</li>
		</ul>
	</li>
	<li><strong>Licenses</strong>
		<ul>
			<li>The Alignment plugin can be used with Conformation Plugin license.</li>
		</ul>
	</li>
	<li><strong>API</strong>
		<ul>
			<li>CGraph.cloneCopy() copies all relevant cached calculated data.</li>
		</ul>
	</li>
</ul>

<h4>Bugfixes</h4>
<ul>
	<li><strong>MarvinSketch</strong>
		<ul>
			<li>The first alteration of the structure couldn't be undone in MarvinSketch opened through MarvinView.</li>
			<li>The .H+/.H- buttons could not be used in some cases.</li>
			<li>Do not show carbon atoms when charge is on bracket.</li>
			<li>Bold font is used for superscripts in case of charge for better visibility.</li>
			<li>Missing bond problem after OLE copy: reimplementing bond-crossing drawing.</li>
			<li>Missing bond crossing visualization when crossing a bond with a bond vertically.</li>
			<li>The Insert &gt; Groups dialog contained some empty elements.</li>
		</ul>
	</li>
	<li><strong>MarvinView</strong>
		<ul>
			<li>MarvinView hid the right side of text boxes.</li>
			<li>MarvinView did not display SD file property when first entries are missing the value.</li>
			<li>MarvinView freezing when molecule subset start index set to invalid value.</li>
		</ul>
	<li><strong>MarvinSpace</strong>
		<ul>
			<li>MSpace debug printout is removed.</li>
		</ul>
	</li>
	<li><strong>Import/Export</strong>
		<ul>
			<li>Rdf conversion lost the reaction number 
			(<a href="http://www.chemaxon.com/forum/viewpost20505.html#20505">forum topic</a>).</li>
			<li>Fixed fusion bridge wrong lettering in name export
			(<a href="http://www.chemaxon.com/forum/viewpost21668.html">forum topic</a>).</li>
		</ul>
	</li>
	<li><strong>Molecule Representation</strong>
		<ul>
			<li>Ungrouping of multilevel S-groups with Molecule.DEFAULT_UNGROUP caused NPE.</li>
			<li>Empty DataSgroup-s are removed if an S-group gets empty (API, GUI).</li>
			<li>Merge Brackets popup menu item was not available in some cases.</li>
			<li>Rings were not cleaned perfectly.</li>
		</ul>
	</li>
	<li><strong>Calculator Plugins</strong>
		<ul>
			<li>MarkushEnumerationPlugin.getRandomStructure() did not return random structures, always the first enumerated was returned.</li>
		</ul>
	</li>
	<li><strong>Licenses</strong>
		<ul>
			<li>Markush license exception was incorrectly thrown by JChemSearch.</li>
		</ul>
	</li>
	<li><strong>API</strong>
		<ul>
			<li>MolImporter.getRecordCount returned 2* the expected number.</li>
			<li>First (record number) column displays the value returned by MDocSource.getDocLabel.</li>
		</ul>
	</li>
</ul>

<h3><a NAME="V5200" class="anchor"></a>March 10th, 2009: Marvin 5.2</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>Compatibility</strong>
		<ul>
			<li>Minimum <strong>Java</strong> requirement of Marvin is <strong>1.5</strong>.</li>
		</ul>
	</li>
    <li><strong>MarvinSketch</strong>
        <ul>
            <li>Truly transparent structure painting method: higher quality drawings at crossing bonds and atom labels.</li>
            <li>Vector graphics (EMF) copy place transparent picture to the clipboard.</li>
            <li>New color schema options on the Periodic System dialog.</li>
            <li>Charge can be assigned to the S-group and displayed on the molecule bracket.</li>
            <li>Drawing attachment points outside S-groups.</li>
            <li>Homology group drawing.</li>
        </ul>
    </li>
    <li><strong>MarvinView</strong>
        <ul>
		<li>Copy/Paste transfers molecule properties.</li>
		<li>"Go To" option in Table menu.</li>
		<li>New values of edited data fields and molecules are stored 
		permanently in memory even in case of large (cached) viewer tables.</li>
        </ul>
    </li>

    <li><strong>Import/Export</strong>
        <ul>
            <li>Extended support for the generation of fused names (from 75% to 80%) in IUPAC name export.</li>
            <li>CAS naming and simple fused systems supported in name import.</li>
            <li>Transparent background in image export (EMF, SVG, PNG).</li>
        </ul>
    </li>
    <li><strong>Molecule representation</strong>
        <ul>
            <li>New option for adding an explicit hydrogen atom to chiral centers having no terminal atoms when 2D cleaning is performed.</li>
        </ul>
    </li>
    <li><strong>Calculator Plugins</strong>
        <ul>
            <li>New plugin: Flexible 3D alignment of multiple molecule structures to each other.</li>
            <li>New plugin: structural framework calculation including Bemis-Murcko.</li>
            <li><strong>Markush enumeration</strong>:
                <ul>
                    <li>Enumeration of homology groups.</li>
                    <li>Enumeration of X pseudo atoms [F,Cl,Br,I].</li>
                </ul>
            </li>
            <li>pKa calculator is trainable.</li>
            <li>Total charge calculation in cxcalc ("totalcharge") (<a href="http://www.chemaxon.com/forum/ftopic3449.html">forum topic</a>).</li>
            <li>"Take major tautomeric form" option in MajorMicrospeciesPlugin.</li>
            <li>23 new ring related functions in TopologyAnalyser.</li>
            <li>Resonance Plugin was moved to "Other" plugin group, and it can be used without license key (free)
            (<a href="http://www.chemaxon.com/forum/ftopic4371.html">forum topic</a>).</li>
        </ul>
    </li>
    <li><strong>Chemical Terms</strong>
        <ul>
            <li>Flexible plugin parameter setting in Evaluator: "name1:value1 name2:value2 ...". (Example: <code>markushEnumerations('random:true id:true', '1,2', 5)</code>.)</li>
            <li>hasRadical(), radicalCount(), and hasIsotope() functions in Chemical Terms.</li>
            <li>fragments() function in Chemical Terms
            (<a href=": http://www.chemaxon.com/forum/viewpost6827.html#6827">forum topic</a>).</li>
            <li>CT Editor is available in Marvin API.</li>
        </ul>
    </li>
    <li><strong>API</strong>
        <ul>
            <li>Transfer button: optional button to send the molecule to the specified target after closing MarvinSketch window.</li>
            <li>Specify own background color separately for each cell in MarvinView via (applet) parameter.</li>
            <li>New metal types can be specified in Periodic system.</li>
            <li>Structure checker core classes and functions are implemented.</li>
			<li>MViewPane enhanced table support.</li>
			<li>PluginFactory creates new plugin and display instances 
			for each getPlugin() and getDisplay() call in case of multimol display; added MOLECULES result domain type and 
			canRepeat() in CalculatorPlugin</li>
        </ul>
    </li>
</ul>

<h4>Bugfixes</h4>
<ul>
	<li><strong>MarvinSketch</strong>
		<ul>	
			<li>Disabled tooltips in menus.</li>
                        <li>The copied and generated structure with Data S-group is much bigger than needed (<a href="http://www.chemaxon.com/forum/viewpost16590.html#16590">forum topic</a>).</li>
		</ul>
	</li>
	<li><strong>MarvinView</strong>
		<ul>
			<li>Print All displayed only the first row on each page.</li>
			<li>Multi-line SDF properties seem to be truncated in the cells until somone clicks on them (then a scrollbar appears).</li>
			<li>Page Up/Page Down not working in spreadsheet view.</li>
			<li>NullPointerException in empty mview when the "Show Fields" option was selected.</li>
		</ul>
	</li>
    <li><strong>Import/export</strong>
        <ul>
            <li>Cis Trans info defined by aromatic bonds in SMART / SMILES import.
            (<a href="http://www.chemaxon.com/forum/viewpost19346.html#19346">forum topic</a></li>
			<li>SMILES export of SuperAtom S-group in SuperAtom S-group did not work (<a href="http://www.chemaxon.com/forum/viewtopic.php?t=2899&amp;start=0">forum topic</a>).</li>
        </ul>
    </li>
    <li><strong>Molecule representation</strong>
        <ul>
            <li>Clean 2D improvement (<a href="http://www.chemaxon.com/forum/viewpost17434.html#17434">forum topic</a>).</li>			
        </ul>
    </li>
    <li><strong>Calculator Plugins</strong>
        <ul>
            <li>Double bond stereoisomers of molecules containing wiggly type double bonds are generated correctly 
            (<a href="http://www.chemaxon.com/forum/ftopic4060.html">forum topic</a>).</li>
			<li>Ungrouping grouped molecules before calculating functions in ElementalAnalyser.</li>
			<li>cxcalc in concurrent mode always used ReusableInputProducer and hence plugin objects are always re-set in the working units.</li>
        </ul>
    </li>
</ul>

<h3><a NAME="v5107" class="anchor"></a>March 26th, 2009: Marvin 5.1.7</h3>
<h4>New features and improvements</h4>
<ul>
    <li><strong>MarvinSketch</strong>
        <ul>
            <li>Customizable behavior of the "Close" option of Sketcher window.</li>
            <li>The isotope list can be modified by the user (<a href="../sketch/isotopelist.html">editing the isotope list</a>).</li>
        </ul>
    </li>
    <li><strong>Applet</strong>
        <ul>
            <li>A custom image can be used with the java splash screen displayed while the applet is loading (<a href="../../examples/applets/sketch/splashscreen.html">live example</a>).</li>
        </ul>
    </li>
</ul>
<h4>Bugfixes</h4>
<ul>
    <li><strong>Import/Export</strong>
        <ul>
            <li>The "Double Cis or Trans" bond ("Double Either" bond) was 
			improperly imported from / exported to extended MOL (V3000) format.</li>
        </ul>
    </li>
</ul>

<h3><a NAME="v5106" class="anchor"></a>March 23rd, 2009: Marvin 5.1.6</h3>
<h4>New features and improvements</h4>
<ul>
    <li><strong>MarvinSketch</strong>
        <ul>
            <li>New template group: the generic groups that are used in searches in Beilstein.</li>
        </ul>
    </li>
    <li><strong>Import/Export</strong>
        <ul>
            <li>MOL and extended MOL import and export of Belstein Generics.</li>
        </ul>
    </li>
    <li><strong>Applet</strong>
        <ul>
            <li>A java splash screen ("Applet is loading...") is displayed while the applet is loading.</li>
            <li>Load on-demand modules in background thread.</li>
            <li>Display wait icon when loading on-demand modules.</li>
        </ul>
    </li>
</ul>

<h3><a NAME="v5105" class="anchor"></a>February 14th, 2009: Marvin 5.1.5</h3>
<h4>New features and improvements</h4>
<ul>
	<li><strong>MarvinSketch</strong>
		<ul>
			<li>Explicit valence setting of atoms is available from menu
			(<a href="http://www.chemaxon.com/forum/ftopic1412.html">forum topic</a>).
		</ul>
	</li>
	<li><strong>MarvinView</strong>
		<ul>
			<li>Large file viewing improved and lots of bugs fixed with both table types.</li>
		</ul>
	</li>
    <li><strong>Applet</strong>
        <ul>
            <li>New MarvinView applet parameter: <code>cell<em>i</em>_<em>j</em></code> and the possibility to escape characters.</li>
        </ul>
    </li>
	<li><strong>Import/Export</strong>
		<ul>
			<li>Text format import from cdx.</li>
		</ul>
	</li>
	<li><strong>Installer</strong>
            <ul>
		<li>Marvin Beans installer for Linux with Java.</li>
            </ul>
	</li>
	<li><strong>API</strong>
		<ul>
			<li>Save/load MSketchPane toolbar and menu configuration : reset active configuration fix.</li>
			<li>New method in MolAtom (getLonePairCount()) that returns the same value as MoleculeGraph().getLonePairCount(int i) is implemented.
			(<a href="http://www.chemaxon.com/forum/viewtopic.php?t=4095">forum topic)</a>.</li>
		</ul>
	</li>
</ul>
<h4>Bugfixes</h4>
<ul>
	<li><strong>MarvinSketch</strong>
		<ul>
			<li>Keyboard shortcuts did not work properly
			 (<a href="http://www.chemaxon.com/forum/ftopic4496.html">forum topic</a>).</li>
		</ul>
	</li>
	<li><strong>MarvinView</strong>
		<ul>
			<li>Print All accessed only cached molecules from the table.</li>
			<li>Save As did not save all molecules.</li>
		</ul>
	</li>
    <li><strong>Applet</strong>
        <ul>
            <li>Width of HTML label components fixed in MarvinView applet.</li>
			<li>Marvin applets did not accepted inline CML/MRV files that were given in applet parameter.</li>
        </ul>
    </li>
	<li><strong>Calculator Plugins</strong>
		<ul>
			<li>Stereo isomer plugin: Enhanced stereo information was cleared before generating the stereoisomers.</li>
		</ul>
	</li>
</ul>

<h3><a NAME="v5104" class="anchor"></a>December 19th, 2008: Marvin 5.1.4</h3>
<h4>New features and improvements</h4>
<ul>
    <li><strong>MarvinSketch</strong>
        <ul>
            <li><em>Arrows</em> group is restructured in the toolbar and in the menubar (<a href="http://www.chemaxon.com/forum/viewtopic.php?p=19194">forum topic</a>).</li>
            <li>The Insert &gt; Groups menu is replaced by a more convenient dialog.</li>
        </ul>
    </li>
    <li><strong>Import/Export</strong>
        <ul>
            <li>Unique SMILES should implicitize plain Hydrogens
            (<a href=" http://www.chemaxon.com/forum/viewtopic.php?p=19569#19569">forum topic</a>).</li>
        </ul>
    </li>
    <li><strong>Calculator Plugins</strong>
        <ul>
            <li>Unique Markush code can be generated for each enumerated structure.</li>
            <li>logP and logD calculations are significantly faster.</li>
        </ul>
    </li>
    <li><strong>API</strong>
        <ul>
            <li>New methods in <em>chemaxon.beans.MViewPane</em>: <em>isViewWindowOpened(int)</em>, <em>isSketchWindowOpened(int)</em> and <em>closeSketcher(int)</em>.</li>
            <li><em>PDBReader</em> and related classes were moved from <em>chemaxon.modules</em> to <em>chemaxon.marvin.io.formats.pdb</em>.</li>
            <li><em>MacroMolecule</em> and related classes were moved from <em>chemaxon.modules</em> to <em>chemaxon.struc</em>.</li>
        </ul>
    </li>
</ul>
<h4>Bugfixes</h4>
<ul>
    <li><strong>MarvinSketch</strong>
        <ul>
            <li>The layout of the <em>Copy As...</em> dialog is polished.</li>
            <li><em>Periodic System</em> button remained pressed on the toolbar even after the dialog was closed.</li>
            <li>The Atom &gt; Stereo &gt; Enhanced, Atom &gt; Charge, Atom &gt; Map, Atom &gt; R-group menus are replaced by more convenient dialogs.
            (<a href="http://www.chemaxon.com/forum/ftopic3947.html">forum topic</a>)</li>
            <li>Ctrl+Del in MarvinSketch did not delete properties.</li>
        </ul>
    </li>
    <li><strong>Licenses</strong>
        <ul>
            <li>The command <em>license -l</em> produced bad output, not listing properly installed licenses.</li>
        </ul>
    </li>
    <li><strong>Calculator Plugins</strong>
        <ul>
            <li>When using Calculator Plugins, the result window of a previous calculation sometimes reappeared (<a href="http://www.chemaxon.com/forum/ftopic3929.html">forum topic</a>).</li>
            <li>Huckel Analysis Plugin threw exception if the input molecule contained explicit hydrogens.</li>
        </ul>
    </li>
    <li>
</ul>

<h3><a NAME="v51032" class="anchor"></a>November 13th, 2008: Marvin 5.1.3_2</h3>
<h4>Bugfixes</h4>
<ul>
    <li><strong>MarvinView</strong>
        <ul>
            <li>Thread conflict while scrolling in huge SDfiles.
            (<a href="http://www.chemaxon.com/forum/viewtopic.php?p=19259">forum topic</a>)</li>
            <li>ArithmeticException in progress monitor.</li>
        </ul>
    </li>
    <li><strong>Import/Export</strong>
        <ul>
            <li>The implicit hydrogen setting option did not work in image generation.</li>
            <li>Molfile import bug: pseudoatom names in atom block were not trimmed. (<a href="http://www.chemaxon.com/forum/ftopic4294.html">forum topic</a>)</li>
        </ul>
    </li>
</ul>

<h3><a NAME="v5103" class="anchor"></a>November 7th, 2008: Marvin 5.1.3</h3>
<h4>New Features and Improvements</h4>
<ul>
    <li><strong>MarvinSketch</strong>
        <ul>
        <li>Slight improvement in multimolecule file opening, the index of the stucture in the preview is automatically offered.</li>
        </ul>
    </li>
    <li><strong>MarvinView</strong>
        <ul>
        <li>New helper API (<a href="beans/api/chemaxon/marvin/beans/MViewParams.html">chemaxon.marvin.beans.MViewParams</a>) that 
        generates the value to the <a href="http://www.chemaxon.com/marvin/help/developer/viewman.html#advanced">molecule table parameters</a> of MarvinView.</li>
        <li>Support MDLCT format in OLE Object.</li>
        </ul>
    </li>
    <li><strong>Licenses</strong>
        <ul>
        <li>Important change in the licensing of calculator plugins: all plugins work in evaluation mode without a license.</li>
        </ul>
    </li>
    <li><strong>Import/Export</strong>
        <ul>
	    <li>Encoding detection when the default system encoding is not
		suitable for the input file. It tries to recognize
		UTF-8, UTF-16, EUC-JP and Shift_JIS. If all fails, then it also
		tries ISO-8859-1 and windows-1252.
		The guessed encoding can be printed using
		<a href="../applications/molconvert.html#query-encoding">molconvert query-encoding</a>.</li>
	    <li>Energy is stored in the second line of
		<a href="../formats/xyz-doc.html">XYZ</a> files.</li>
	    <li>MDL formats
		<ul>
		    <li>Import of SDfiles without structure fields.</li>
		    <li>Energy is imported from the header line, the "Energy"
			SDF property is removed.</li>
		    <li>Format recognition optimized: other recognizers
			are not called any more if the first 12 lines match
			at least one MDL format.</li>
		</ul>
	    </li>
	    <li>API
		<ul>
		<li><a href="beans/api/chemaxon/marvin/io/PositionedInputStream.html#skipLine()">PositionedInputStream.skipLine()</a>
		    method is created to speed up pre-reading of huge
		    multi-molecule files. It should be called in
		    MRecordReader.skipRecord() implementations instead of
		    readLine() if the line contents are unimportant.</li>
		</ul>
		</li>
	</ul>
    </li>
    <li><strong>Clean 2D</strong>
        <ul>
        <li>Clean 2D considers electron flow arrows (<a href="http://www.chemaxon.com/forum/viewpost18921.html#18921">forum topic</a>).</li> 
        </ul>
    </li>
    <li><strong>Calculator Plugins</strong>
        <ul>
            <li>Huckel orbital coefficient calculation in HuckelAnalysisPlugin (<a href="http://www.chemaxon.com/forum/ftopic3538.html">forum topic</a>)</li>
            <li>Markush enumeration ID can be generated for enumerated molecules.</li>
            <li>logP calculation methods were introduced; available methods: "VG", "KLOP", "PHYS", "user defined", "weighted".</li>
            <li>logP calculation is trainable, the "user defined" logP calculation method can be trained with experimental logP values provided by the user.</li>
            <li><em>cxcalc</em> can be used for training the plugin calculations (currently only logP is trainable).</li>
        </ul>
    </li>
</ul>
<h4>Bugfixes</h4>
<ul>
    <li><strong>MarvinSketch GUI</strong>
        <ul>
        <li>Correction in geometric transformation when placing SMARTS with the help of the Periodic System dialog window.</li>
        <li>Help menu opened very slow on Mac (<a href="http://www.chemaxon.com/forum/viewpost17520.html#17520">forum topic</a>).</li>
        <li>MarvinSketch web start application did not launch with Java 1.5.0_16.</li>
        </ul>
    </li>
    <li><strong>Import/Export</strong>
        <ul>
	<li>UTF-16 encoding is recognized and imported correctly if the file
	    starts with a byte order mark (BOM).</li>
        <li>Reaction arrow did not display (from rxn file) with exporting to image
        (<a href="http://www.chemaxon.com/forum/ftopic4102.html">forum topic</a>).</li>
	</ul>
    </li>
    <li><strong>Clean 2D</strong>
        <ul>
        <li>Minor partial clean fix in case of rings.</li>
        </ul>
    </li>
    <li><strong>Calculator Plugins</strong>
        <ul>
        <li>HuckelAnalysisPlugin: numbers in arrays returned by cxcalc are separated by semicolons (<a href="http://www.chemaxon.com/forum/ftopic3538.html">forum topic</a>).</li>
        </ul>
    </li>
</ul>

<h3><a NAME="v5102" class="anchor"></a>October 1st, 2008: Marvin 5.1.2</h3>
<h4>New Features and Improvements</h4>
<ul>
    <li><strong>MarvinSketch/View</strong>
        <ul>
            <li>New option/parameter to display the label of carbon atoms in structures.</li>
        </ul>
    </li>
    <li><strong>Moleculear representation</strong>
        <ul>
            <li>Get smallest set of smallest ring bond indexes 
            (<a href="http://www.chemaxon.com/forum/viewtopic.php?t=4123&start=0">forum topic</a>).</li>
            
        </ul>
    </li>
    <li><strong>API</strong>
        <ul>
            <li><em>chemaxon.marvin.io.MDocSource</em> and <em>chemaxon.marvin.io.MFieldAccessor</em> are added to public API.</li>
            <li>New abstract method in <em>chemaxon.marvin.io.MDocStorage.Listener</em>: <em>storageSizeFinalized(MDocStorage)</em>.</li>
        </ul>
    </li>
    <li><strong>Documentation</strong>
        <ul>
            <li><em>chemaxon.util.iterator.IteratorFactory</em> is added to apidoc and to the documentation.</li>
            <li>Improved MarvinSketch applet examples for molecular property calculations.</li>
        </ul>
    </li>
</ul>
<h4>Bugfixes</h4>
<ul>
    <li><strong>Import/Export</strong>
        <ul>
            <li>SMILES import removed parity from rigi N stereo (<a href="http://www.chemaxon.com/forum/viewtopic.php?p=18077#18077">forum topic</a>).</li>
            <li>Cxsmiles export generated unimportable output.</li>
        </ul>
    </li>
    <li><strong>Molecule Representations</strong>
        <ul>
            <li>Adding a bond to terciary N did not add + charge automatically and displayed valence error.</li>
            <li>Reaction arrow head missed after import steps in mview and molconvert.</li> 
            <li>Adding and then removing explicit hydrogens created stereo bonds.</li>
        </ul>
    </li>
    <li><strong>Clean 3D</strong>
        <ul>
            <li>Bug caused parity error for certain strained structures fixed
            (forum topics:<a  href="http://www.chemaxon.com/forum/ftopic4092.html">#4092</a>,
            <a href="http://www.chemaxon.com/forum/ftopic4089.html">#4089</a>,
            <a href="http://www.chemaxon.com/forum/ftopic3594.html">#3594</a>).
        </ul>
    </li>
    <li><strong>Calculator Plugins</strong>
        <ul>
            <li>If only scaffold coloring was set in Markush Enumeration plugin, link node bonds in scaffold were not correct.</li>
        </ul>
    </li>
</ul>

<h3><a NAME="v5101" class="anchor"></a>September 1st, 2008: Marvin 5.1.1</h3>
<h4>New Features and Improvements</h4>
<ul>
    <li><strong>MarvinSketch</strong>
    <ul>
        <li>New, explicit way of defining lone pair and radical electrons on atoms
        (<a href="http://www.chemaxon.com/marvin/examples/applets/sketch/studentexam/index.html">live example</a>).</li>
        <li>The following menus were (temporarily) removed from the Template Library: File &gt; Close, Options menu (the functions are out of order).</li>
        <li>The Structure &gt; Clean 3D &gt; Select Conformer menu option is renamed to Display Stored Conformers.</li>
    </ul></li>
    <li><strong>Applet</strong>
    <ul>
        <li>New applet parameter (<a href="sketchman.html#parameters.templateToolbarCustomizable">templateToolbarCustomizable</a>) to remove the on-the-fly customizability of the <a href="http://www.chemaxon.com/marvin/help/sketch/gui/toolbars.html#templates">Advanced Template Toolbar</a>.</li>
    </ul></li>
    <li><strong>Import/Export</strong>
    <ul>
        <li>The lone pair and radical electrons can be exported to cxsmiles format.</li>
        <li>Also recognize names that are part of the traditional dictionary
(now that name recognition should only be tried when most others have failed, it is not as problematic to load the dictionary during recognition).</li>
    </ul></li>
    <li><strong>Chemical Terms</strong>
    <ul>
        <li>Evaluator config files defined by the user are read from .chemaxon/MARVIN_MAJOR_VERSION (UNIX / Linux) or chemaxon/MARVIN_MAJOR_VERSION (Windows) directory.</li>
    </ul>
    </li>
    <li><strong>API:</strong>
    <ul>
        <li>Renderer (chemaxon.marvin.beans.MViewRenderer) and editor (chemaxon.marvin.beans.MViewEditor) components for JTables are part of the public API.</li>
    </ul>
    </li>
</ul>
<h4>Bugfixes</h4>
<ul>
    <li><strong>MarvinSketch</strong>
    <ul>
        <li>Explicit hydrogen was added automatically to newly placed atom after adding explicit hydrogen from the Structure menu.</li>
        <li>Explicit lone pairs disappeared when editing the molecule with the sprout function (mouse drag).</li>
        <li>Source window sometimes failed to open from plugin result dialog.</li>
        <li>Extra selection of the radical atom was invoked.</li>
    </ul></li>
    <li><strong>Import/Export</strong>
    <ul>
        <li>Add missing structures to nameimport dictionary, fix suffix and stereo handling on esters.</li>
        <li>Molfile recognition failed if first line includes only spaces.</li>
    </ul></li>
    <li><strong>Clean 2D</strong>
    <ul>
        <li>Some certain structures cleaned ugly
        (<a href="http://www.chemaxon.com/forum/viewpost17416.html">forum topic</a>, <a href="http://www.chemaxon.com/forum/ftopic4082.html">forum topic</a>).</li>
    </ul>
    </li>
    <li><strong>Template Handling</strong>
    <ul>
        <li>The nitro abbreviation template has been changed from neutral to the ionic form.</li>
    </ul></li>
    <li><strong>Calculator Plugins</strong>
    <ul>
        <li>Changed default SDF tag names of cxcalc calulations acceptorsitecount (ACCEPTOR_SITE_COUNT), acceptorcount (ACCEPTOR_COUNT), donorsitecount (DONOR_SITE_COUNT) and donorcount (DONOR_COUNT) 
        (<a href="http://www.chemaxon.com/forum/ftopic4018.html">forum topic</a>).</li>
        <li>Calculation result list often disappeared when the result display window was resized.</li>
        <li>Parameter files of several plugins included invalid xml syntax.</li>
    </ul></li>
    <li><strong>API</strong>
    <ul>
        <li>New methods to close streams in MolExporter: flush(), close(int).</li>
        <li>F6 and F7 did not work if MViewPane was a JTable cell.</li>
    </ul></li>
</ul>

<h3><a NAME="v5100" class="anchor"></a>August 7th, 2008: Marvin 5.1.0</h3>
<h4>New features and Improvements</h4>
<ul>
    <li><strong>MarvinSketch:</strong>
        <ul>
	        <li>Converting IUPAC Names to structure:
			    <ul>
				<li>from .name files</li>
		        <li>through the Edit &gt; Enter IUPAC Name option</li>
                <li>when pasting IUPAC names to the canvas of MarvinSketch</li>
                </ul></li>
            <li>OLE 2 support: improved support of pasting MarvinSketch objects into MS office documents, and ability to edit the inserted object inside the Office document. More memory efficient, stable on Vista. JNI-based communication, multi-document server.</li>
	    <li>Accelerated initialization of MarvinSketch and MarvinView at startup, both as standalone version, and as implemented component in other applications.</li>
            <li>Flip Group option: Flip a selected group in the molecule around the bond connecting this group to the rest of the structure.</li>
            <li>Recent file list length is configurable in the Save/Load tab of Edit > Preferences</li>
            <li>The save and load of GUI properties can be set only with one checkbox at the preferences Save/Load tab in the future.</li>
            <li>The magnification (zoom factor) of a molecule is now saved in MRV format if it is enabled on the Save/Load tab of Edit > Preferences.</li>
        </ul>           
    </li>
    <li><strong>MarvinView:</strong>
        <ul>
	        <li>(Batch) conversion of IUPAC Names to structure when opening .name files.</li>
            <li>Printing is redesigned. New options: Print to PDF and Print Preview.</li>
        </ul>
    </li>
    <li><strong>Applet:</strong>
        <ul>
            <li>New applet parameter to listen for mouse events:
            <a href="http://www.chemaxon.com/marvin/examples/applets/view/listeners.html">example</a>
            (<a href="http://www.chemaxon.com/forum/viewpost17523.html#17523">forum topic</a>).</li>
            <li>Calculator Plugins are accessible from JavaScript via Chemical Terms.</li>
        </ul></li>
    <li><strong>Import/Export:</strong>
	<ul>
        <li>IUPAC name import (converting IUPAC names to structures):
		    <ul>
		    <li>opening .name files</li>
            <li>batch conversion of names (in .name files) by molconvert</li>
		    <li>direct conversion of names to structures through the Edit > Enter IUPAC Name option</li>
		    <li>on-the-fly conversion of IUPAC Names to structure when pasting IUPAC Names to the canvas of MarvinSketch</li>
            </ul>
		<li>IUPAC name export: improved support for amino-acids and peptides.</li>
        <li>Compressed base64 encoded (cx)SMILES/(cx)SMARTS output.</li>
		<li>UTF-8 character encoding support for applet parameters
	        (<a href="http://www.chemaxon.com/forum/ftopic2738.html">forum topic</a>).</li>
        <li>Unique SMILES export has been changed because of 
            <a href="#double_bond_looses_cis_trans">the modification in aromatization</a>.</li>
	</ul>
	</li>
    <li><strong>Molecule Representation:</strong>
        <ul>
        <li>Allow N with valence 5 in aromatic rings 
        (<a href="http://www.chemaxon.com/forum/viewpost15162.html#15162">forum topic</a>).</li>
        <li>Aromatization to accept all atom types in aromatic rings.</li> 
        </ul></li>
    <li><strong>Clean 3D:</strong>
        <ul>
            <li>Major Clean 3D core modifications: the fragment-fragment fuse code is rewritten. The new version places greater
           effort to avoid situations where excessive coordinate generation time is elapsed. The performance of multiple
           conformer generation is also improved.</li>
            <li>New Dreiding force field implementation introduced.</li>
            <li>Hyperfine parameters tuned: excessive runtime when invoked with looser optimization limit fixed.</li>
        </ul></li>
    <li><strong>Calculator Plugins:</strong>
        <ul>
        <li>Tautomerization Plugin
            <ul>
            <li>Generic tautomer generation.</li>
            <li>New tautomerization options: protect double bond stereo, protect all tetrahedral stereo centers, 
                protect labeled tetrahedral stereo centers only.</li>
            </ul>
        </li>
        <li>Topology Analyser Plugin (forum topics: <a href="http://www.chemaxon.com/forum/viewtopic.php?t=3234">1</a>, 
        <a href="http://www.chemaxon.com/forum/viewtopic.php?p=9997#9997">2</a>)
            <ul>
            <li>New calculations: fragment count, ring system count, largest ring system size, smallest ring system size, 
                aromatic ring count, aliphatic ring count, size dependent ring counting functions (ring count of given size,
                aromatic ring count of given size, aliphatic ringcount of given size, ring system count of given size). 
                See also the <a href="../applications/cxcalc-calculations.html">list of cxcalc calculations</a> and the 
                <a href="../chemicalterms/EvaluatorFunctions.html">Chemical Terms Reference Tables</a>.</li>
            </ul>
        </li>
        <li>Geometry Plugin
            <ul>
            <li>Molecule projection calculations in GeometryPlugin: minimal projection area, maximal projection area, 
                minimal projection radius, maximal projection radius. See also the 
                <a href="../applications/cxcalc-calculations.html">list of cxcalc calculations</a> and the 
                <a href="../chemicalterms/EvaluatorFunctions.html">Chemical Terms Reference Tables</a>.</li>
            </ul>
        </li>
        <li>Markush Enumeration Plugin
            <ul>
            <li>Structure coloring and scaffold aligning options in Markush enumeration.</li>
            <li>Position variation bonds in Markush enumeration.</li>
            <li>New Markush enumeration options: orienting scaffold uniformly and coloring enumerated structures.</li>
            <li>Speeding up random enumeration.</li>
            </ul>
         </li>
        <li>Huckel Analysis Plugin (<a href="http://www.chemaxon.com/forum/viewtopic.php?t=3645&amp;start=0">forum topic</a>)
            <ul>
            <li>Eigenvalue and eigenvector calculations (cxcalc: "huckeleigenvalue" and "huckeleigenvector").</li>
            <li>Some cxcalc calculations have been renamed: "energy" to "localizationenergy", "pichargedensity" to "electrondensity",
                "totalchargedensity" to "chargedensity".</li>
            <li>Removed aromatic restriction from Huckel E/Nu order calculations
                (<a href="http://www.chemaxon.com/forum/ftopic3538.html">forum topic</a>).
            </ul>
       </li>
	   <li>logP plugin
            <ul>
	       <li> The calculation algorithm has been improved using approximately 13,000 experimental logP values 
		        collected from the <a href="http://www.syrres.com/esc/physprop.htm">PhysProp database</a>. Nevertheless, the original unique algorithm 
				that makes possible to accurately predict novel compounds still provides the basis of the calculation.        
           </li>
           </ul>
       </li>
        </ul>
    </li>
	<li><strong>Chemical Terms:</strong>
        <ul>
		<li>Chemical Terms is available from Marvin in command line (<a href="../chemicalterms/EvaluatorFunctions.html">Evaluator command line application</a>) 
		and API (until now it was available only in JChem).</li>
		<li>It can also be used in JavaScripts (in applets). Examples for usage:
            <ul>
                 <li><a href="../../examples/applets/sketch/chemicalterms2.html">MarvinSketch Example - Calculating molecular properties on the fly</a></li>
                 <li><a href="../../examples/applets/view/chemicalterms.html">MarvinView Example - Molecular property calculations with Chemical Terms</a></li>
         </ul></ul>
             <p>
            <ul>
                <li>New functions: abs(), fragmentCount(), ringSystemCount(), smallestRingSystemSize(), 
                largestRingSystemSize(), ringCountOfSize(), ringSystemCountOfSize(), aliphaticRingCountOfSize(), aromaticRingCountOfSize(),
                minimalProjectionArea(), maximalProjectionArea(), minimalProjectionRadius(), maximalProjectionRadius(),
                genericTautomer(), mostStableTautomer(), markushEnumerationsDisplay(), randomMarkushEnumerationsDisplay().</li>
                <li>New (optional) parameters are available for enumerations() and randomEnumerations() functions.
                <li>Functions available only in JChem: match(), matchCount(), disjointMatchCount(), dissimilarity().</li>
                <li>AND, OR, NOT boolean operators in Chemical Terms (alternatives for &amp;&amp;, ||, ! operators).</li>
                <li>Option to set output format in Evaluator command line application if result is molecule.</li>
                <li>Evaluator command line application can list available Chemical Terms functions.</li>
            </ul>
        </li>
    <li><strong>API:</strong>
        <ul>
        <li>Both aromatization methods aromatize query atoms if they have double and single bonds in the ring.</li>
        <li>Atoms belonging to the following atom groups can be recognized by the name of the group: alkali, alkaline earth, other metal, transition metal, lanthanides, actinides, metalloids, halogens.</li>
        <li><strong>Calculator Plugins:</strong>
            <ul>
            <li>New calculations in <a href="beans/api/chemaxon/calculations/TopologyAnalyser.html">TopologyAnalyser</a> and 
            <a href="beans/api/chemaxon/marvin/calculations/TopologyAnalyserPlugin.html">TopologyAnalyserPlugin</a> (see the list of calculations above).</li> 
            <li>New calculations in <a href="beans/api/chemaxon/marvin/calculations/GeometryPlugin.html">GeometryPlugin</a> 
            (see the list of calculations above).</li>            
            <li>New calculations and options in <a href="beans/api/chemaxon/marvin/calculations/TautomerizationPlugin.html">TautomerizationPlugin</a> 
            (see the list of calculations and options above).</li>
            <li>New calculations and options in <a href="beans/api/chemaxon/marvin/calculations/MarkushEnumerationPlugin.html">MarkushEnumerationPlugin</a> 
            (see the list of calculations and options above).</li>
            <li>New and renamed calculations in <a href="beans/api/chemaxon/marvin/calculations/HuckelAnalysisPlugin.html">HuckelAnalysisPlugin</a> 
            (see the list of calculations above).</li> 
            </ul></li>
	<li>Molecule file format recognizers are called in the priority order
	    defined by the <code>recogn</code> argument of the
	    <a href="beans/api/chemaxon/formats/MFileFormat.html#MFileFormat(java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, long)">MFileFormat constructor</a>.
	    Deprecated Recognizer.getPriority(), use
	    MFileFormat.<a href="beans/api/chemaxon/formats/MFileFormat.html#getPriority(java.lang.String)">getPriority(subformat)</a> instead.
	    </li>
	<li><a href="beans/api/chemaxon/util/iterator/MoleculeIterator.html">MoleculeIteratior</a>
	    interface moved to chemaxon.util.iterator.</li>
        </ul>
    </li>
    <li><strong>Documentation:</strong>
    <ul>
        <li>Updated developer's guide and examples.</li>
    </ul></li>
</ul>

<h4>Bugfixes</h4>
<ul>        
    <li><strong>MarvinSketch:</strong>
    <ul>
        <li>Atom &gt; Stereo &gt; R/S menu was missing.</li>
        <li>MarvinSketch always dropped confirmation dialog when exiting in OLE mode (<a href="http://www.chemaxon.com/forum/viewtopic.php?t=3589">forum topic</a>).</li>
        <li>In MarvinSketch, scale factor was loading in an improper time.</li>
        <li>Zooming after atom font changing shouldn't delete the set atom font.</li>
        <li>Atom font changes from Format dialog disappeared when scrolling.</li>
        <li>Automatic mapping of long chain substituents was incorrect (<a href="http://www.chemaxon.com/forum/viewpost15275.html#15275">forum topic)</a>.</li>
    </ul>
    </li>
    <li><strong>MarvinView:</strong>
    <ul>
        <li>UTF-16 character encoding was incorrect in cell headers of MarvinView.</li>
    </ul>
    </li>
    <li><strong>Applet:</strong>
    <ul>
        <li>Marvin applets did nothing by OLE copy (<a href="http://www.chemaxon.com/forum/viewtopic.php?t=3589">forum topic</a>).</li>
    </ul>
    </li>
    <li><strong>Molecule representation:</strong>
    <ul>
        <li>Wedge bonds were displayed incorrectly after adding explicit H atoms to chiral centers  (<a href="http://www.chemaxon.com/forum/viewpost14621.html">forum topic</a>).</li>
        <li>Implicitization of explicit hydrogens was sometimes incorrect.</li>
        <li>Clean2d placed wedge on symmetric atoms.</li>
        <li>Planar ligands in 3D returned 0 instead of EITHER in parity.</li>
        <li>There was a minor bug in the valence check algorithm (<a href="http://www.chemaxon.com/forum/viewpost15162.html">forum topic</a>).</li>
        <li>MRV export of the molecule in reaction editor threw NullPointerException.</li>
        <li>A valence error was given to pyridine oxide drawn with aromatic bonds
            (<a href="http://www.chemaxon.com/forum/viewpost15162.html#15162">forum topic</a>).</li>
        <li>The head of the reaction arrow disappeared after 2D or 3D clean.</li>
        <li>The reaction arrow overlapped a reactant upon hit coloring of reactions.</li>
        <li>Atom coloring didn't work after subsequent molecule loads.</li>
        <li>ArrayIndexOutOfBoundsException was thrown when drag&dropping expanded S-groups from the canvas to MyTemplates.</li>
        <li>The reaction arrow was duplicated after 2D clean.</li>
        <li>Replacing atom in S-group with Asp caused ArrayIndexOutOfBoundsException.</li>
        <li><a class="text" name="double_bond_looses_cis_trans">In SMILES format</a>, double
        bond lost cis/trans info after aromatization.</li>
        <li>M, MH, X, XH are not aromatic query atom.</li>
    </ul>
    </li>
    <li><strong>Import/export:</strong>
    <ul>
        <li>Double bond stereo information was sometimes lost during SMILES import (if a double bond was between two stereo double bonds).</li>
    </ul>
    </li>
    <li><strong>Clean 3D:</strong>
        <ul>
            <li>There were errors in gradient calculation. The fix also improves the optimization performance for some deformed initial structures.</li>
            <li>Possibly corrupted stereochemical requirements caused a bug when invoked directly from the sketcher.</li>
            <li>Using chirality when invoked from the sketcher sometimes failed. (<a href="http://www.chemaxon.com/forum/viewpost15492.html#15492 ">forum topic</a>)</li>
            <li>Hyperfine produced unpredictable results. Now MD parameters are modified (lower initial temperature) and very strict optimization limit is used.
            (<a href="http://www.chemaxon.com/forum/viewpost15492.html#15492">forum topic</a>)</li>
            <li>Energies calculated by the building process and the dreidingenergy plugin were slightly different
            (<a href="http://www.chemaxon.com/forum/viewtopic.php?p=15990#15990">forum topic</a>).</li>
            <li>Optimization limit error message fixed 
            (<a href="http://www.chemaxon.com/forum/viewtopic.php?t=3159">forum topic</a>).</li>
        </ul></li>
    <li><strong>Calculator Plugins:</strong>
    <ul>
        <li>An exception was thrown during enumeration of structures with multicenter S-group.</li>
        <li>Random Markush enumeration generated larger link node enumerates than in definition</li>
        <li>Random enumeration hasn't thrown exception in case of invalid input structures (R-group in the variable part of multiposition bond).</li>
        <li>NullPointerException was thrown in some cases of random enumeration when molecule contained possible valence error. Now a more informative RuntimeException is thrown.</li>
        <li>Random Markush enumeration has returned null for non-generic structures instead of the structure itself.</li>
        <li>DotForumla functions of Elemental Analyser threw exceptions on empty molecules.</li>
    </ul>
    </li>
</ul>

<h3><a class="anchor" name="v5007"></a>July 28th, 2008: Marvin 5.0.7</h3>
<h4>New features and Improvements</h4>
<ul>
    <li><strong>Applet:</strong>
    <ul>
        <li>Introducing new parameters 
        (<a href="sketchman.html#parameters.maxscale">maxscale</a>, 
        <a href="sketchman.html#parameters.detach">detach</a>) to control the maximum magnification of exported images and applet canvas
        (<a href="http://www.chemaxon.com/forum/ftopic3901.html">forum&nbsp;topic</a>).
    </ul></li>
    <li><strong>API:</strong>
    <ul>
        <li><strong>Calculator Plugins:</strong> New methods in pKaPlugin: getMacropKaValues(int, double[], int[]) and getMacropKaValues(int)
        (<a href="http://www.chemaxon.com/forum/ftopic3989.html">forum topic</a>).</li>
    </ul>
    </li>
</ul>
<h4>Bugfixes</h4>
<ul>
    <li><strong>Molecule representation:</strong>
    <ul>
        <li>ArrayIndexOutOfBoundsException when drag and dropping expanded S-groups from the canvas to MyTemplates.</li>
    </ul>
    </li>
</ul>

<h3><a class="anchor" name="v5006"></a>June 30th, 2008: Marvin 5.0.6</h3>
<h4>Bugfixes</h4>
<ul>
    <li><strong>MarvinSketch GUI:</strong>
    <ul>
        <li>Customization dialog was not displayed.</li>
        <li>Complete missing configuration names.</li>
    </ul></li>
    <li><strong>Licenses:</strong>
    <ul>
        <li>License management did not work via JavaScript calls (e.g. get molecule in "name" format) due to Sun's security restriction.</li>
    </ul></li>
</ul>

<h3><a class="anchor" name="v5005"></a>June 20th, 2008: Marvin 5.0.5</h3>
<h4>New features and Improvements</h4>
<ul>
    <li><strong>MarvinSketch GUI:</strong>
    <ul>
            <li>Default file location can be set in the Save/Load tab of Edit > Preferences.</li>
    </ul></li>
    <li><strong>Licenses:</strong>
    <ul>
        <li>Environment variables and Java system property is introduced for
        license install (See 
        <a href="http://www.chemaxon.com/marvin/help/licensedoc/install.html">details</a>) 
        (<a href="http://www.chemaxon.com/forum/ftopic3715.html">forum topic</a>).
    </ul></li>
</ul>
<h4>Bugfixes</h4>
<ul>
    <li><strong>MarvinSketch GUI:</strong>
    <ul>
        <li>Zooming after atom font changing deleted the settings of atom font.</li>
        <li>SMILES was transferred instead of the molecule source by Plain Text copy.</li>
        <li>Atom font changes from Format dialog disappeared by scrolling.</li>
    </ul></li>
    <li><strong>MarvinSpace:</strong>
    <ul>
        <li>MarvinSpace could not load String representation of molecules given
        by applet parameter.</li>
    </ul></li>
    <li><strong>Licenses:</strong>
    <ul>
        <li>Exception was thrown from license manager.</li>
    </ul></li>
    <li><strong>Molecule Representation:</strong>
    <ul>
        <li>Replacing atom in Sgroup with <em>Asp</em> caused 
        ArrayIndexOutOfBoundsException.</li>
        <li>Atom coloring did not work after subsequent molecule loading.</li>
    </ul></li>
    <li><strong>Calculator Plugins:</strong>
    <ul>
        <li>Exception in enumeration of structures with multicenter S-group.</li>
        <li>Tautomer generation failed if certain features were present in
        the structure (e.g. enhanced stereo).</li>
    </ul></li>
</ul>

<h3><a class="anchor" name="v5004"></a>May 24th, 2008: Marvin 5.0.4</h3>
<h4>New features and Improvements</h4>
<ul>
    <li><strong>MarvinSketch GUI:</strong>
    <ul>
        <li>In the default configuration, the <em>Explicit Hydrogens</em> 
        option has been removed from the
        <em>View/Misc</em> menu. (From now on the explicit Hydrogens are always 
		visible, but they can be added or removed using the corresponding options 
		of the <em>Structure/Add</em> and in <em>Structure/Remove</em>
        menus.)</li>
    </ul></li>
    <li><strong>API:</strong>
    <ul>
        <li>New methods to control R-groups visibility in 
            <em>chemaxon.marvin.beans.MarvinPane</em>: 
            <em>setRgroupsVisible(boolean)</em>, 
            <em>isRgroupsVisible()</em></li>
    </ul></li>
</ul>
<h4>Bugfixes</h4>
<ul>
    <li><strong>Molecule representation:</strong>
    <ul>
        <li>Ungrouping of embedded S-groups threw IndexOutOfBoundsException.</li>
    </ul></li>
    <li><strong>Import/Export:</strong>
    <ul>
        <li>There was a slowdown in MView when loading molecules during scrolling.</li>
    </ul></li>
    <li><strong>CalculatorPlugin:</strong>
    <ul>
        <li>Calculator Plugins threw exception for molecules with pseudo atoms.
        (<a href="http://www.chemaxon.com/forum/ftopic3766.html">forum topic</a>)</li>
        <li>Random Markush enumeration generated larger link node enumerates than in definition.</li>
    </ul></li>
</ul>

<h3><a class="anchor" name="v5003"></a>April 21st, 2008: Marvin 5.0.3</h3>

<h4>New features and Improvements</h4>
<ul>
    <li><strong>MarvinSketch GUI:</strong>
        <ul>
	    <li>Shift button expands/contracts all "hold-in-hand" S-groups and abbreviation groups (not just standard templates).</li>
	    <li>New icons has been added to the collection, which are not included in the toolbars by default, but can be added using the View > Customize option. These icons (category > command) are as follows: Reset View (View > Reset View), Aromatize (Structure > Aromatize), Dearomatize (Structure > Aromatize), Add explicit hydrogens (Structure > Add explicit hydrogens), Remove explicit hydrogens (Structure > Remove explicit hydrogens), Add absolute stereo (Structure > Add absolute stereo (CHIRAL)), Remove absolute stereo (Structure > Remove absolute stereo (CHIRAL))</li>
	    <li>The Erase icon has been changed (from an upside-down pen to a rubber)</li>
        </ul></li>
    </li>
    <li><strong>CalculatorPlugin:</strong>
        <ul>
	    <li>D/T symbols are used by default for Deuterium/Tritium in the following cxcalc/Chemical Terms calculations: <em>isotopeformula</em>, <em>dotdisconnectedisotopeformula</em>, <em>isotopecomposition</em>. (<a href="http://www.chemaxon.com/forum/viewtopic.php?p=15949#15949">forum topic</a>)</li>
        </ul></li>
    <li><strong>API:</strong>
        <ul>
	        <li>Added "public String getIsotopeComposition(boolean)" method to chemaxon.marvin.calulations.ElementalAnalyserPlugin; D symbol is used for Deuterium and T for Tritium in results returned by "public String getIsotopeComposition()". (<a href="http://www.chemaxon.com/forum/viewtopic.php?p=15953">forum topic</a>)</li>
            <li>Enumeration plugin is renamed to MarkushEnumeration plugin. chemaxon.marvin.calculations.EnumerationPlugin class is deprecated, use chemaxon.marvin.calculations.MarkushEnumerationPlugin instead of that.</li>
            <li>Parameters are renamed in cxcalc calculations: from <em>enumerationcount</em> to <em>markushenumerationcount</em>, <em>enumerations</em> to <em>markushenumerations</em> (old names are still available).</li>

            <li>New calculation: <em>randommarkushenumerations</em></li>
        </ul></li>        
</ul>
<h4>Bugfixes</h4>
<ul>
    <li><strong>Import/Export:</strong>
        <ul>
	    <li>R-group defintions were missing on molecule picture after image export.</li>
	    <li>Cis/trans stereo info was sometimes wrongly exported into smiles.</li>

	    <li>CML and MRV import failed when the input file did not contain any molecule</li>
	    <li>MOL/SDF export caused a null pointer exception when energy field was corrupted (NaN or Infinity)</li>
            <li>The <em>chemaxon.struc.MDocument.parseMRV(String)</em> method failed with <em>NullPointerException</em> (<a href="http://www.chemaxon.com/forum/ftopic3694.html">forum topic</a>).</li>

            <li>SmilesImport fix: If ringcorrection called set CIS|TRANS flags for bonds located in two small sssr.</li>
            <li>CxSmilesImport fix: ringcorrection (CIS flag setting for small rings) moved before setting the extra chemaxon information (in cxsmiles).</li>
        </ul></li>
    <li><strong>Clean 2D:</strong>
        <ul>
	    <li>The 2D cleaning of structures containing double bonds connected to a ring was sometimes wrong.</li>
	    <li>The arrangment of template based 2D clean was missing.</li>

	    <li>The 2D cleaning of a selected (sub)structure was sometimes inappropriate (<a href="http://www.chemaxon.com/forum/viewtopic.php?p=7191#7191">forum topic</a>).</li>
        </ul></li>
    <li><strong>Clean 3D:</strong>
        <ul>
            <li>A number of atoms disappeared from the product by 3D clean of a reaction, and the same number of explicit hydrogens appeared in the starting material.</li>
        </ul></li>

    <li><strong>MarvinSketch GUI:</strong>
        <ul>
	    <li>Zoom mode (F6) and zoom tool were unsynchronized.</li>
            <li>Drawing a bond to an R-group definition by click-drawing resulted in inconsistent molecule.</li>
            <li>Specifying outer bonds by selecting the atom before defining the Link Node did not work.</li>
            <li>Drawing link node attachment point by Rgroup definition was not correct.</li>

             <li>The location of the filechooser dialog (by <em>Open</em>, <em>Save</em> and <em>Save As</em>) was wrong in some cases.</li>
        </ul></li>

</ul>

<h3><a class="anchor" name="v5021"></a>March 20th, 2008: Marvin 5.0.2.1</h3>
<ul>
    <li>MarvinView:
        <ul>
        <li>Bugfix: Reading compressed mol files from applet parameter was wrong</li>
        </ul>
    </li>
    <li>Applet:
        <ul>
            <li>Bugfix: Error by the initalization of the recent file list (<a href="http://www.chemaxon.com/forum/ftopic3671.html">forum topic</a>) </li>
        </ul>
    </li>
</ul>
<h3><a class="anchor" name="v5002"></a>March 18th, 2008: Marvin 5.0.2</h3>
<ul>
    <li>MarvinSketch:
        <ul>
            <li>Position variation bond. It is available under the Structure > Add menu.</li>
            <li>Extended font support: Texts (atom labels and text box content) can be formatted using any font that is available in the operating system.</li>            
            <li>Global IME support in Textboxes (allows entering East Asian characters).</li>
            <li>Recent file list added to file menu.</li>
        </ul>           
    </li>
    <li>MarvinView:
    	<ul>
    		<li>Recent file list added to file menu.</li>
    		<li>"Print All" and "Save As" options are enabled in "File" menu, and they work properly</li>
    	</ul>
    </li>
    <li>MarvinSpace:
        <ul>
            <li>Displaying coordinate system (<a href="http://www.chemaxon.com/forum/viewtopic.php?p=14492#14492">forum topic</a>)</li>
            <li>Reading PDB files as simple Strings</li>
        </ul>
    </li>
    <li>Import/Export
	<ul>
	<li>Molecule file header and footer can be retreived using
	    MRecordReader.<a href="beans/api/chemaxon/marvin/io/MRecordReader.html#getHeaderAsString()">getHeaderAsString()</a> and
	    <a href="beans/api/chemaxon/marvin/io/MRecordReader.html#getFooterAsString()">getFooterAsString()</a>.<br>
	    <b>Related incompatibility:</b> the MRV file header and footer are
	    not part of the record returned by 
	    MRecord.<a href="beans/api/chemaxon/marvin/io/MRecord.html#getString()">getString()</a> any more.
	    Prepend the header and append the footer to get an importable
	    molecule.</li>
	<li>Image export option to show E/Z labels.
	    (<a href="http://www.chemaxon.com/forum/viewtopic.php?p=15596#15596">forum topic</a>)</li>
	<li>East Asian character encoding support for applet parameters
	    (<a href="http://www.chemaxon.com/forum/ftopic2738.html">forum topic</a>)</li>
        <li>The default behavior and some options of <a href="../formats/pdb-doc.html#import">PDBImport</a> have been changed. Bonds
            are recognised in hetero groups by default, bond order is assigned.
            Optional hydrogen assignment/removal and the possibility to omit
            CONECT records has been implemented. PDB import does better recognize connectivity and bond order of hetero components.</li>
	</ul>
	</li>
    <li>Bugfixes:
    <ul>
        <li>Atom > Stereo > R/S menu was missing in MarvinSketch GUI</li>
        <li>Marvin applets did nothing by OLE copy (<a href="http://www.chemaxon.com/forum/viewtopic.php?t=3589">forum topic</a>)</li>
        <li>MarvinSketch always dropped confirmation dialog when exiting in OLE mode (<a href="http://www.chemaxon.com/forum/viewtopic.php?t=3589">forum topic</a>)</li>
        <li>UTF-16 character encoding fix in cell headers of MarvinView</li>
        <li>Fix on adding explicit H atoms to chiral centers  (<a href="http://www.chemaxon.com/forum/viewpost14621.html">forum topic</a>).</li>
        <li>Implicitize hydrogens bugfix.</li>
        <li>Fixed IUPAC name export failure involving spiro compounds.</li>
        <li>Superfluous hydrogen atoms were assigned during PDB import (<a href="http://www.chemaxon.com/forum/viewpost15341.html#15341">forum topic</a>).</li>
		<li><a href="beans/api/chemaxon/marvin/modules/LonePairCounter.html#getLonePairCount()">LonePairCounter.getLonePairCount()</a> failed for structures containing
            list and not-list atoms. This also affected <a href="http://www.chemaxon.com/forum/ftopic3614.html">AutoMapper</a> </li>
        <li><a href="http://www.chemaxon.com/forum/viewpost15275.html#15275">AutoMapper bug</a> (wrong mapping of long chain substituent) has been fixed.</li>
    </ul>
    </li>
</ul>

<h3><a class="anchor" name="v5001"></a>February 18, 2008: Marvin 5.0.1</h3>
    <ul>
    <li>Import/Export:
        <ul>
        <li>Image export options are added for displaying <a href="../formats/images-doc.html#lp">lone pairs</a></li>
        <li>MRV import accepts comments in old MRV files (<a href="http://www.chemaxon.com/forum/viewpost14524.html#14524">forum topic</a>)
        <li>EMF and PDF exports uses the new version of Freehep API (2.1.1) (<a href="http://www.chemaxon.com/forum/viewpost14568.html#14568">forum topic</a>)</li>
        <li>Bugfix: Saving EMF and PDF in Marvin applets often gave emtpy or
        corrupt file (<a href="http://www.chemaxon.com/forum/ftopic2558.html">forum topic</a>)</li>
        </ul>
    </li>
    <li>MarvinSketch:
        <ul>
        <li>Toolbar drop-down list box is added</li>
        <li>Drag&Drop of templates works beside the template toolbar</li>
        <li>Bugfixes:
            <ul>
                <li>tmpls, ttmpls, xtmpls parameters did not work</li>
                <li>setEnabled(false) function did not disable toolbars</li>
                <li>Help button did not work in the desktop application (<a href="http://www.chemaxon.com/forum/ftopic3447.html">forum topic</a>)</li>
                <li>Insert &gt; IUPAC Name required license and kept complaining about the missing license</li>
                <li>Abbreviation groups assigned to My Templates worked only after restarting MarvinSketch</li>                
            </ul>
        </li>
        </ul>
    </li>
    <li>Desktop icons are optionally created under Linux by the Marvin Beans installer (<a href="http://www.chemaxon.com/forum/ftopic3408.html">forum topic</a>)</li>
    <li>Wedge arrangement around stereocenter having four ligands was improved
    (<a href="http://www.chemaxon.com/forum/viewpost14621.html">forum topic</a>)</li>
    <li>MarvinView: "Print All" and "Save As" option is temporary unavailable in
    grid view until its reimplementation</li>
    <li>MarvinSpace:
        <ul>
            <li>Animation can be stopped from menu</li>
            <li>Electrostatic potential uses the same coloring method as Charge Plugin in MarvinSketch</li>
        </ul>
    </li>
    <li>Calculator Plugins:
        <ul>
            <li>TautomeriztionPlugin: "Protect aromacity", "Protect charge" and "Exclude antiaromatic compounds" options were added</li>
            <li>EnumerationPlugin: the visualization of the result of Markush library size calculation was improved</li>
            <li>Dreiding energy can also be displayed in kJ/mol unit in GeometryPlugin and in ConformerPlugin (<a href="http://www.chemaxon.com/forum/ftopic3497.html">forum topic</a>)</li>
        </ul>
    </li>
    </ul>

<h3><a class="anchor" name="v5000"></a>January 9th, 2008: Marvin 5.0</h3>
    <ul>
    <li><b>MarvinSketch:</b>
        <ul>
        <li><a href="../sketch/gui/customization.html">Customizable GUI</a> with a
            <a href="../sketch/sketch-gui.html">brand new design</a></li>
        <li><a href="../sketch/gui/configurations.html">Configuration choices</a> (including ISIS Draw
        and ChemDraw like menu and icon arrangements)</li>
        <li>Chain drawing, displaying the last carbon number</li>
        <li>Enhanced Query, S-group and R-group drawing features</li>
        <li>When MarvinSketch canvas is empty, the scrollbars are disabled</li>
        </ul>

    </li>
    <li><b>MarvinView:</b>
        <ul>
	<li>Spreadsheet-like view for displaying fields in SD and RDfiles</li>
        <li>Bugfix in OS X: the scrolling down button was covered by the
        resize button of the window</li>
	</ul>
    </li>
    <li><b>Import/export:</b>
	<ul>
        <li>Insertable Marvin OLE component into Office documents</li>
    	<li><a href="../calculations/s2n.html">IUPAC name generator</a>,
    	with real-time name display in MarvinSketch, support for peptides and amino-acids,
    	and numerous name improvements</li>
	<li><a href="../formats/gaussian-doc.html">Gaussian Input/Output</a></li>
	<li><a HREF="../formats/cube-doc.html">Cube import</a>:
	changed default behavior - reads in volumetric data by default
	(former import option 'V'), skips it if
	<a HREF="../formats/cube-doc.html#option_M">import option 'M'</a> is specified
	<li>UTF-16 encoding support for
	    <a href="beans/api/chemaxon/formats/MolInputStream.html">MolInputStream</a>
	    </li>
	<li>API changes:
	    <ul>
	    <li>Metadata for file formats handled by Marvin is
		represented by class
		<a href="beans/api/chemaxon/formats/MFileFormat.html">MFileFormat</a>.
		File formats can be found using
		<a href="beans/api/chemaxon/formats/MFileFormatUtil.html">MFileFormatUtil</a>.<a href="beans/api/chemaxon/formats/MFileFormatUtil.html#findFormats(java.lang.String, long, long)">findFormats</a>.
		User defined formats can be registered with
		<a href="beans/api/chemaxon/formats/MFileFormatUtil.html#registerFormat(chemaxon.formats.MFileFormat)">registerFormat</a>.
		</li>
	    <li>Metadata for import or export options is represented by
		class <a href="beans/api/chemaxon/marvin/util/OptionDescriptor.html">OptionDescriptor</a>.
		Export options for a given format can be retrieved using
		<a href="beans/api/chemaxon/marvin/io/MolExportModule.html">MolExportModule</a>.<a href="beans/api/chemaxon/marvin/io/MolExportModule.html#getOptionDescriptors(java.lang.String)">getOptionDescriptors</a>.
		</li>
	    <li>Record reading.
		<a href="beans/api/chemaxon/marvin/io/MRecordReader.html">MRecordReader</a>
		implementations can be used to parse records without
		creating Molecule objects. An MRecordReader can be created
		using
		MFileFormat.<a href="beans/api/chemaxon/formats/MFileFormat.html#createRecordReader(chemaxon.formats.MolInputStream, java.lang.String)">createRecordReader</a>
		or
		MFileFormatUtil.<a href="beans/api/chemaxon/formats/MFileFormatUtil.html#createRecordReader(java.io.InputStream, java.lang.String)">createRecordReader</a>.
		</li>
            <li>Only narrow ends of wedge bonds are considered
                in 2D representation of the molecules,
                following IUPAC recommendations. To convert your
                existing 2D molecules created with the both-end interpretation to
                the new convention, use the
                <a href="http://www.chemaxon.com/jchem/doc/user/StandardizerConfiguration.html#actionstring">ConvertWedgeInterpretation</a>
                Standardizer action. </li>
	    </ul>
	    <small>Related incompatibilities:
	    <ul>
	    <li>MolImportIface and MDocumentImportIface interfaces are
		replaced by the abstract class
		<a href="beans/api/chemaxon/marvin/io/MolImportModule.html">MolImportModule</a>.
		Record skipping related methods removed
		(isSkippingSupported, skipMol and skipToNext), use
		MRecordReader.<a href="beans/api/chemaxon/marvin/io/MRecordReader.html#skipRecord()">skipRecord</a>
		or
		<a href="beans/api/chemaxon/marvin/io/MRecordImporter.html">MRecordImporter</a>.<a href="beans/api/chemaxon/marvin/io/MRecordImporter.html#skipRecord()">skipRecord</a>
		instead.
		</li>
	    <li><a href="beans/api/chemaxon/marvin/io/MolExportModule.html">MolExportModule</a>:
		the
		<a href="beans/api/chemaxon/marvin/io/MolExportModule.html#open(java.lang.String)">open</a>
		method throws
		<a href="beans/api/chemaxon/marvin/io/MolExportException.html">MolExportException</a>,
		loadMarvinModule removed and replaced by
		MFileFormatUtil.<a href="beans/api/chemaxon/formats/MFileFormatUtil.html#createExportModule(java.lang.String)">createExportModule</a>,
		getOptionSign removed.
		Both MolExportModule and MolExportException moved to
		chemaxon.marvin.io, the old versions in
		chemaxon.marvin.util are deprecated.
		</li>
	    <li>The canonical encoding name is returned by
		MFileFormatUtil.<a href="beans/api/chemaxon/formats/MFileFormatUtil.html#getEncodingFromOptions(java.lang.String)">getEncodingFromOptions</a>
		and stored by
		MolExportModule.<a href="beans/api/chemaxon/marvin/io/MolExportModule.html#setEncoding(java.lang.String, java.lang.String)">setEncoding</a>,
		MolConverter.<a href="beans/api/chemaxon/formats/MolConverter.html#createMolConverter(java.io.InputStream, java.io.OutputStream, java.lang.String[], java.io.OutputStream[], int[])">createMolConverter</a> and
		the constructors of
		<a href="beans/api/chemaxon/formats/MolImporter.html">MolImporter</a>,
		<a href="beans/api/chemaxon/formats/MolExporter.html">MolExporter</a>
		and
		<a href="beans/api/chemaxon/formats/MolConverter.html">MolConverter</a>.
		These methods and constructors may throw
		IllegalCharsetNameException or UnsupportedCharsetException.</li>
	    <li>Import/export modules (not recommended to use directly!)
		moved from chemaxon.marvin.modules to
		chemaxon.marvin.io.formats and its subpackages.</li>
	    </ul>
	    </small>
	    </li>
	</ul>
	</li>
    <li><b>Calculator Plugins:</b>
        <ul>
        <li>Multiprocessor support in <a href="../applications/calc.html">cxcalc</a></li>
        <li>Tautomerization:
            <ul>
                <li>New tautomer generation rules were implemented</li>
                <li>Estimation of the tautomer distribution as function of pH was improved</li>
            </ul>
        </li>
        <li>pKa, logP: Accuracy of  the predictions were improved.</li>
        <li>Added <a href="beans/api/chemaxon/marvin/calculations/EnumerationPlugin.html">EnumerationPlugin</a>: enumerates Markush structures</li>
	<li>ElementalAnalyserPlugin:
            <ul>
               <li>"Dot-disconnected isotope formula" calculation added</li>
               <li>"D" and "T" symbols can be used for "Deuterium" and "Tritium" in isotope formulas</li>
            </ul></li>
        <li>pKaPlugin: "Take major tautomeric form" option added</li>
        <li>logPPlugin: "Take major tautomeric form" option added</li>
        <li>logDPlugin: "Consider tautomerization" option added</li>
        <li>New surface area calculations in MSAPlugin:
        <ul>
        	<li>Water accessible surface area (ASA)</li>
        	<li>Water accessible surface area of all atoms with positive partial charge (ASA+)</li>
        	<li>Water accessible surface area of all atoms with negative partial charge (ASA-)</li>
        	<li>Water accessible surface area of all hydrophobic atoms (ASA_H)</li>
        	<li>Water accessible surface area of all polar atoms (ASA_P)</li>
        </ul></li>
        <li>GUI: "Restore defaults" button added to options panel</li>
        <li>Integration of user-developed calculations made easier </li>
	</ul>
    </li>
    <li><b>Reaction auto-mapping:</b><ul>
        <li>New mapping style was introduced, <a href="beans/api/chemaxon/marvin/modules/AutoMapper.html#CHANGING">CHANGING</a>,
            this is the default ChemAxon-style mapping from now on (see also the
            <a href="http://www.chemaxon.com/jchem/doc/user/Reactor.html">Reactor</a> documentation).</li>
        <li>Three kind of mapping strategies have been introduced:
            <a href="beans/api/chemaxon/marvin/modules/AutoMapper.html#BEST">BEST</a> which is the slowest but exhaustive search,
            <a href="beans/api/chemaxon/marvin/modules/AutoMapper.html#STANDARD">STANDARD</a>, the default, fairly reliable
            automapping strategy, and <a href="beans/api/chemaxon/marvin/modules/AutoMapper.html#FAST">FAST</a> which is a
            crude mapping strategy.</li>
        <li>Numerous bug fixes, eg. <a HREF="http://www.chemaxon.com/forum/viewpost13414.html#13414">symmetrical ring maps are shifted.</a> </li>
    </ul></li>
    <li><b>Queries, Groups and Markush structures:</b>
            <ul>
           <li><a href="../sketch/sketch-basic.html#coordination-compounds">Coordination compounds</a>: drawing, MRV, MDL Mol, Extended SMILES IO</li>
            <li><a href="../sketch/sketch-basic.html#markush-structures">Markush structures</a>: position
                variation (Markush bond) and frequency variation drawing; MRV, MDL Mol, Extended SMILES IO; Enumerating Markush structures (sequentially/randomly)</li>
            <li><a href="../sketch/sketch-basic.html#comBrackets">Components, Unordered Mixtures and Ordered Mixtures</a>: drawing, MRV, MDL Mol IO</li>
            <li><a href="../sketch/sketch-basic.html#polymers">Polymers</a>: drawing, MRV, MDL Mol IO</li>
            <li><a href="../sketch/sketch-basic.html#repeatingunits">Repeating units with repetition ranges</a>: drawing, MRV, MDL Mol IO</li>
                <li>Improved editing of hydrogenized structures</li>
                <li>Improved branching in 3D</li>
            </ul>
    </li>

    <li><b>API:</b>
        <ul>
	<li>Graphics:
	    <ul>
	    <li><a href="beans/api/chemaxon/struc/graphics/MBracket.html">MBracket</a>
		class, supports parentheses, square brackets and chevrons.
		</li>
	    <li>New
		<a href="beans/api/chemaxon/struc/graphics/MTextBox.html">MTextBox</a>
		attributes "halign" and "valign" for aligning the text, see
		<a href="beans/api/chemaxon/struc/graphics/MTextBox.html#setHorizontalAlignment(int)">setHorizontalAlignment</a>
		and
		<a href="beans/api/chemaxon/struc/graphics/MTextBox.html#setVerticalAlignment(int)">setVerticalAlignment</a>.</li>
	    <li>Incompatible change: java.awt.Graphics2D is used as the first
		argument of
		<a href="beans/api/chemaxon/struc/MObject.html">MObject</a>.<a href="beans/api/chemaxon/struc/MObject.html#paint(java.awt.Graphics2D, chemaxon.struc.CTransform3D, int, java.awt.Color, java.awt.Color, java.awt.Color)">paint()</a>
		and its implementations.</li>
	    </ul>
	</li>
	<li>GUI:
	    <ul>
	    <li>Invoking Paste action from API:
		<a href="beans/api/chemaxon/marvin/beans/MarvinPane.html#doPaste()"
		>MarvinPane.doPaste()</a></li>
	    <li>Removed MarvinPane.addSaveImageMenu and
		MViewPane.makeSaveImageMenu because the menu of multiple image
		formats is replaced by only one "Save Image(s)" menu item. It
		can be added to a custom "File" menu using
		MarvinPane.<a HREF="beans/api/chemaxon/marvin/beans/MarvinPane.html#getCommonActions()">getCommonActions</a>().<a HREF="beans/api/chemaxon/marvin/common/swing/CommonActions.html#getSaveImageAction()">getSaveImageAction</a>().<a HREF="beans/api/chemaxon/marvin/swing/MAction.html#addTo(java.awt.Container)">addTo</a>(menu).</li>
	    </ul>
        </li>
        <li>Applet Parameters:
            <ul>
            <li>New: <a href="sketchman.html#parameters.listenpropertychange">listenpropertychange</a>.</li>
            <li>Deprecated: preload, background.</li>
            </ul></li>
	<li>Incompatibilities:
	    <ul>
	    <li><a href="beans/api/chemaxon/struc/MProp.html">MProp</a>.
                <a href="beans/api/chemaxon/struc/MProp.html#convertToString(java.lang.String)">convertToString(String)</a>
		became a final method calling the abstract
		<a href="beans/api/chemaxon/struc/MProp.html#convertToString(java.lang.String, int)">convertToString(String, int)</a>
		with 0 second argument.
		</li>
	    <li><a HREF="beans/api/chemaxon/struc/CGraph.html#fuse(chemaxon.struc.CGraph)">fuse(CGraph graph)</a>
		and <a HREF="beans/api/chemaxon/struc/CGraph.html#fuse0(chemaxon.struc.CGraph)">fuse0(CGraph graph)</a>
		have been made final. Subclasses should implement
		<a HREF="beans/api/chemaxon/struc/CGraph.html#fuse(chemaxon.struc.CGraph, boolean)">
		fuse(CGraph graph, boolean check)</a>
		and <a HREF="beans/api/chemaxon/struc/CGraph.html#fuse0(chemaxon.struc.CGraph, boolean)">
		fuse0(CGraph graph, boolean check)</a> instead.
		</li>
	    </ul>
	    </li>
	</ul>
    </li>
    </ul>

<h3><a class="anchor" name="V4114"></a>December 11, 2007: Marvin 4.1.14</h3>
    <ul>
    <li>API:
        <ul>
        <li><a href="beans/api/chemaxon/struc/MDocument.html#simplifyMolecule()">MDocument.simplifyMolecule()</a></li>
        </ul>
    </li>
    <li>Fixed bugs:
        <ul>
        <li>Import/export:
            <ul>
            <li>There was a critical property based smiles export bug</li>
            <li>There was a NullPointerException in Parity (<a href="http://www.chemaxon.com/forum/viewpost13704.html#13704">forum topic</a>)</li>
            </ul>
        </li>
        <li>GUI:
            <ul>
            <li>Plugin options panel could not be closed using only the keyboard after running the calculation</li>
            <li>Edited molecule was not updated in viewer by closing the sketcher.</li>
            <li>Rotating a group in expanded form removed the group definition.</li>
            <li>Update isotope number in selection by D and T shortcuts.</li>
            <li>Type-over atom D did not work.</li>
            <li>In OS X, the scroll down button of sketcher scrollbar was covered by window resize button.
                (<a href="http://www.chemaxon.com/forum/viewpost1182.html#1182">forum topic</a>)</li>
            <li>Fix a small refreshing problem in MarvinSpace.</li>
            <li>Quick setting of component visibility on mouse drag in MarvinSpace.</li>
            </ul>
        </li>
        <li>Calculator Plugins:
            <ul>
            <li>Bugfix in the pKa plugin.</li>
            </ul>
        </li>
        <li>Misc:
            <ul>
            <li>Bugfix in the maximum common substructure search.</li>
            </ul>
        </li>
        </ul>
    </li>
    </ul>
<h3><a class="anchor" name="V4113"></a>September 18, 2007: Marvin 4.1.13</h3>
    <ul>
    <li>API:
        <ul>
        <li>Background color settings for MolPrinter: <a href="beans/api/chemaxon/marvin/MolPrinter.html#setBackgroundColor(java.awt.Color)">MolPrinter.setBackgroundColor(java.awt.Color)</a>. (<a href="http://www.chemaxon.com/forum/viewpost13056.html#13056">forum topic</a>)</li>
        </ul>
    </li>
    <li>Bugfixes:
        <ul>
        <li>Change frames to dialogs to avoid focus problem of model dialogs.
        (<a href="http://www.chemaxon.com/forum/viewpost12371.html">forum topic</a>)</li>
        <li>"Any" bond was invisible in OS X.
        (<a href="http://www.chemaxon.com/forum/viewpost334.html#334">forum topic</a>)</li>
        <li>ElementalAnalyser and ElementalAnalyserPlugin: scientifically correct rounding of molecular weight (mass)</li>
        <li>CleanUtil.arrangeComponents() bug for reactions containing Sgroups.
        (<a href="http://www.chemaxon.com/forum/viewpost10796.html#10796">forum topic</a>)</li>
        <li>Bug in abbreviated group visibility.</li>
        <li>Correcting a bug of getting the most frequent natural isotope of Hydrogen. It returned 0, though the correct result is 1.</li>
        <li>Marvin Beans batch files accept 40 parameters.
        (<a href="http://www.chemaxon.com/forum/ftopic2012.html">forum topic</a>)</li>
        <li>MarvinSpace: lone pair visibility correction.</li>
        </ul>
    </li>
    </ul>
<h3><a class="anchor" name="V4112"></a>August 17, 2007: Marvin 4.1.12</h3>
    <ul>
    <li>Improvement in IUPAC name export. Very significant performance increase for large molecules,
        fixing about half the previously existing naming failures (due to timeouts) for typical datasets.
        <a href="../calculations/s2n.html">New export option to setup naming timeout</a>.</li>
    <li>Bugfixes:
        <ul>
        <li>Redo bug fixed.</li>
        <li>Bugfix in Clean 2D</li>
        <li>Fix bug by flipping.</li>
        <li>MolAtoms were not selected when selectionChanged property change event invoked.</li>
        </ul></li>
    </ul>
<h3><a class="anchor" name="V4111"></a>July 23, 2007: Marvin 4.1.11</h3>
    <ul>
    <li>GUI:
        <ul>
        <li>Text box improvement: its highlighting color is lighter than other highlighting
    	 colors because dark colors obscured the text for short strings.</li>
        </ul>
    </li>
    <li>API:
        <ul>
        <li>Clear undo queue: <a href="beans/api/chemaxon/marvin/beans/MSketchPane.html#clearHistory()">MSketchPane.clearHistory()</a>.</li>
        <li>New property to set default save format:
        <a href="sketchman.html#parameters.defaultSaveFormat">defaultSaveFormat</a></li>
        </ul>
    </li>
    <li>Bugfixes:
    	<ul>
    	<li>IUPAC Naming: fixed timeout errors.</li>
        <li>Fixing bug "Phosphorus loses chirality after explicit-H-removal"
    	</ul></li>
    <li>Calculator plugins:
    	<ul>
    	<li>Added dominant tautomer distribution calculation to Tautomeriztion plugin
    		<ul>
    		<li>cxcalc: <a href="../applications/cxcalc-calculations.html#dominanttautomerdistribution">dominanttautomerdistribution</a> calculation</li>
    		<li>Related changes in <a href="beans/api/chemaxon/marvin/calculations/TautomerizationPlugin.html">TautomerizationPlugin API</a></li>
    		</ul>
    	</li>
    	<li>Maximum allowed lenght of the tautomerization path can be set in Tautomeriztion plugin
    	    <ul>
    	    <li>See options of the <a href="../applications/cxcalc-calculations.html#tautomers">tautomers</a> calculation in cxcalc</li>
    		<li>New method in TautomerizationPlugin API: <a href="beans/api/chemaxon/marvin/calculations/TautomerizationPlugin.html#setMaximumTautomerizationPathLength(int)">setMaximumTautomerizationPathLength(int)</a></li>
    	    </ul>
        </li>
        </ul>
    </li>
    </ul>
<h3><a class="anchor" name="V4110"></a>July 6, 2007: Marvin 4.1.10</h3>
    <ul>
    <li>Bugfix in displaying of pKa calculation results in command line (cxcalc).</li>
    </ul>
<h3><a class="anchor" name="V4109"></a>June 26, 2007: Marvin 4.1.9</h3>
    <ul>
    <li>GUI:
        <ul>
        <li>Remove small blue corners in MarvinView (except in tables).</li>
        <li>Hide dashed line in bond forming electron flow arrows.</li>
        <li>Fixing undo and copying error in reactions.</li>
        </ul>
    </li>
    <li>API:
        <ul>
        <li>New image export option to <a href="../formats/images-doc.html#anum">display atom numbers</a>.</li>
        <li>New PropertyChangeEvent to indicate when <a href="sketchman.html#selectionChanged">selection changes</a>.</li>
        <li>New method in the <em>MolPrinter</em> class to
            <a href="beans/api/chemaxon/marvin/MolPrinter.html#setDispopts(int,%20int)">set display options</a>.</li>
        <li>In the <em>chemaxon.marvin.util.MolExportModule</em> class, the <em>optionSign2</em>
    field is replaced by <a href="beans/api/chemaxon/marvin/util/MolExportModule.html#getOptionSign()">getOptionSign()</a> method.</li>
        </ul>
    </li>
    <li>IUPAC name generation improvements, in particular for ions.</li>
    <li>Bugfixes:
        <ul>
            <li>Fixing arrow initialization problem in RxnMolecule clean.</li>
            <li>In PDB export, the <em>-c</em> option did not work properly.</li>
            <li>Empty field parsing bugfix in Smiles import.</li>
        </ul>
    </li>
    </ul>
<h3><a class="anchor" name="V4108"></a>May 17, 2007: Marvin 4.1.8</h3>
    <ul>
    <li>Import/export:
        <ul>
        <li>Image export option ("noatsym") to hide atom symbols in
        3D mode.</li>
        <li>SUBSTRUCTURE section is also generated in mol2</li>
        <li>Bugfix in MRV import: absolute stereo flag could not be set.</li>
        <li>Fix superatom S-group export bug in MRV export.</li>
        </ul></li>
    <li>GUI:
        <ul>
        <li>Fix NullPointerException by displaying MViewPane with menubar.</li>
        <li>Graphical reaction arrows.</li>
	<li>Bugfix in arrow drawing.</li>
        </ul></li>
    <li>API:
        <ul>
        <a href="beans/api/chemaxon/marvin/util/MPainterUtil.html"
        >chemaxon.marvin.util.MPainterUtil</a> is deprecated, use
        <a href="beans/api/chemaxon/marvin/MolPrinter.html"
        >chemaxon.marvin.MolPrinter</a>
        </ul></li>
    <li>Bugfixes:
        <ul>
        <li>LP chirality fix</li>
        <li>Bugfix in CleanUtil</li>
        <li>Minor bugfixes in calculation plugins</li>
        </ul></li>
    </ul>
<h3><a class="anchor" name="V4107"></a>April 13, 2007: Marvin 4.1.7</h3>
    <ul>
    <li><a href="../calculations/s2n.html">IUPAC name generator</a>, for
        interactive and batch naming</li>
    <li>Bugfixes:
        <ul>
        <li>In Graph invariant.</li>
        <li>Arrow mirroring bugfixes.</li>
        <li>Infinite loop fix in Parity.</li>
        <li>Converting to "base64:gzip:mrv" did not work.</li>
        <li>Exception by missing atom list definition fixed.</li>
        <li>ArrayIndexOutOfBoundsException in RxnMolecule.removeNode()</li>
        <li>Non-transformable polyline point highlighting fix</li>
        <li>InChi import/export works also in Java Web Start</li>
        </ul>
    </li>
    </ul>
<h3><a class="anchor" name="V4106"></a>March 14, 2007: Marvin 4.1.6</h3>
    <ul>
    <li>Import/Export:
        <ul>
	<li>RDfile properties can be
	   <a href="beans/api/chemaxon/struc/MPropertyContainer.html#hierarchize()">hierarchize</a>d
	   or
	   <a href="beans/api/chemaxon/struc/MPropertyContainer.html#flatten()">flatten</a>ed.</li>
        <li>Data sgroup improvements in MRV import/export.</li>
        <li>Bugfix in MRV export.</li>
        <li>Bugfixes in MolImporter.</li>
        <li>Bugfixes in Peptide import.</li>
        <li>Bad EOL (End Of Line) characters in the output of the InChi export was fixed.</li>
        </ul>
    </li>
    <li>GUI:
        <ul>
        <li>Double-clicking at selection unselects selected atoms.</li>
        <li>Bugfix in printing: Texts components covered each others on the printed view table.</li>
        <li>Removed unnecessary confirmation dialog by closing viewer detached sketch frame.</li>
        <li>Deleting problem of multiple is solved.</li>
        <li>Corrected the wrong location of plus sign in reactions.</li>
        </ul></li>
    <li>API:
    	<ul>
    	<li><a href="beans/api/chemaxon/marvin/beans/MarvinPane.html#setEnabled(boolean)">
                MarvinPane.setEnabled(boolean)</a> is implemented.</li>
        <li><a href="beans/api/chemaxon/marvin/beans/MViewPane.html#setDraggable(boolean)">
            MViewPane.setDraggable(boolean)</a> allows or denies drag operation in cells.</li>
    	<li>New property change event in MarvinView:
            <a href="viewman.html#parameters.selectedIndex">selectedIndex</a></li>
        <li><a href="beans/api/chemaxon/struc/Molecule.html#draw(java.awt.Graphics,%20java.lang.String)">Molecule.draw</a> is deprecated, use
            <a href="beans/api/chemaxon/marvin/util/MPainterUtil.html">chemaxon.marvin.util.MPainterUtil</a> instead of that.</li>
        <li>New MarvinSketch applet parameter: <a href="sketchman.html#parameters.buttonmenubar">buttonmenubar</a></li>
        <li>Text Box improvements</li>
        </ul></li>
    <li>Bugfixes:
        <ul>
        <li>Bugfixes in Clean 2D.</li>
        <li>Bugfixes in aromatization.</li>
        <li>Fix parity error during H removal.</li>
        <li>Potential deadlocks are fixed in MarvinSketch initalization.</li>
        <li>Bugfix in Parity.</li>
        <li>Bugfixes in calculator plugins (Tautomerization,pKa,Ionizer).</li>
        </ul></li>
    </ul>
<h3><a class="anchor" name="V4105"></a>January 9, 2007: Marvin 4.1.5</h3>
    Bugfixes:
    <ul>
        <li>Import/Export:
            <ul>
            <li>EMF/PDF creating does not drop HeadlessException in headless
            mode.</li>
            <li>Fix EMF export bug (structure was invisible if canvas contained
            any text box).</li>
            <li>Problem with both super atom and data sgroups in mol import.</li>
            <li>ArrayIndexOutOfBoundsException in unique SMILES export.</li>
            </ul>
        </li>
        <li>Plugins:
            <ul>
            <li>In Tautomerization.</li>
            <li>In Charge.</li>
            <li>In pKa.</li>
            <li>In PolarGroups.</li>
            </ul>
        </li>
        <li>Parity error during H removal.</li>
        <li>Abbrev group problem with Me3SiO.</li>
    </ul>
<h3><a class="anchor" name="V4104"></a>December 7, 2006: Marvin 4.1.4</h3>
    <ul>
    <li>GUI:
        <ul>
            <li>Fix endless loop by rings join.</li>
            <li>Exception in custom amino acid loading is fixed.</li>
        </ul>
    </li>
    <li>API:
        <ul>
            <li>MarvinView drops events by opening/closing detached
            sketcher/viewer.</li>
            <li>New methods: <a href="beans/api/chemaxon/marvin/calculations/ConformerPlugin.html#getEnergy()">ConformerPlugin.getEnergy()</a>,
                <a href="beans/api/chemaxon/marvin/calculations/ConformerPlugin.html#getEnergy(int)">ConformerPlugin.getEnergy(int)</a>.</li>
        </ul>
    </li>
    <li>Plugins:
        <ul>
            <li>Bugfix in MSA calculation.</li>
            <li>Acceptor count and donor count is displayed on the plugin result window.</li>
            <li>Bugfix in Major Microspecies calculation.</li>
            <li>Minor bugfixes in pKa calculation.</li>
            <li>Improved canonical tautomer generation.</li>
        </ul>
    <li>Fixing broken peptide snake.</li>
    <li>Bugfix in peptide import/export</li>
    <li>Fixing minor bugs in clean 3D</li>
    <li>Minor bugfix in Parity</li>
    </ul>
<h3><a class="anchor" name="v4103"></a>November 17, 2006: Marvin 4.1.3</h3>
    <ul>
    <li>API:
	<ul>
	<li><a HREF="beans/api/chemaxon/struc/MoleculeGraph.html#implicitizeHydrogens(int)">implicitizeHydrogens</a>(int)
	    is final. Subclasses should implement
	    <a HREF="beans/api/chemaxon/struc/MoleculeGraph.html#implicitizeHydrogens(int, chemaxon.struc.MolAtom[])">
	    implicitizeHydrogens</a>(int, MolAtom[]) instead.
	    </li>
	</ul>
	</li>
	<li>Plugins:
	<ul>
		<li>New option added to conformer plugin to set the conformer diversity limit. API:
                    <a href="beans/api/chemaxon/marvin/calculations/ConformerPlugin.html#setDiversity(double)">setDiversity(double)</a>
	    <li>New option added to pKa plugin to handle the acid/base state depending on the submitted micro state. API:
            <a href="beans/api/chemaxon/marvin/calculations/pKaPlugin.html#setpKaPrefixType(int)">setpKaPrefix(int)</a>,
            <a href="beans/api/chemaxon/marvin/calculations/pKaPlugin.html#getpKaValues(int, int)">getpKaValues(int, int)</a>
	</ul>
        </li>
        <li>MarvinSpace
        <ul>
            <li>Available in MarvinView as an optional 3D structure viewer.</li>
            <li>Enhanced secondary structure visualization</li>
            <li>Charmm pdb file support for both structures and crystals</li>
            <li>Charmm Slab visualization</li>
        </ul>
        </li>
    </ul>
<h3><a class="anchor" name="v4102"></a>October 16, 2006: Marvin 4.1.2</h3>
    <ul>
    <li>Import/Export:
        <ul>
        <li>InChi import can load structures without AuxInfo.</li>
        <li>PDB import:
            <ul>
            <li>multi model proteins</li>
            <li>nucleic acid backbone</li>
            <li>non-standard pdb file related changes (missing mandatory
            blocks etc.)</li>
            <li>charmm pdb ATOm record support</li>
            <li>new class for Water molecules</li>
            <li>support for DOD and TIP3 water</li>
            <li>recognition of water H (even when it's denoted by O)</li>
            <li>H-O-H bonds</li>
            </ul>
        </li>
        </ul>
    <li>GUI:
        <ul>
        <li>Improved display performance for large structures.</li>
        <li>Bugfix in 3D rotation: Front atoms did not follow mouse movement.</li>
        <li>Atom/bond coloring bug for abbreviated groups is fixed.</li>
        <li>CxSmiles conversion in Edit Source window is fixed.</li>
        <li>Marvin applications uses user's home as default directory in the File Open dialog.</li>
        </ul>
    </li>
    <li>New methods in API:
    <a href="applets/api/JMSketch.html#undo()">JMSketch.undo()</a>,
    <a href="applets/api/JMSketch.html#redo()">JMSketch.redo()</a>,
    <a href="beans/api/chemaxon/marvin/beans/MSketchPane.html#undo()">MSketchPane.undo()</a> and
    <a href="beans/api/chemaxon/marvin/beans/MSketchPane.html#redo()">MSketchPane.redo()</a></li>

    <li>MarvinSpace 1.3.2:
        <ul>
        <li>Charmm grid support</li>
        <li>Displaying bonds in water molecules</li>
        <li>Secondary structure visualization of nucleic acids</li>
        </ul>
    </li>
    <li>Others:
        <ul>
        <li>New S-group types: MIX (mixtures) and COM (components).</li>
        <li>Daylight style mapping, mapping style guessing</li>
        <li>Bugfix in Parity</li>
        </ul>
    </li>
    </ul>
<h3><a class="anchor" name="v4101"></a>September 8, 2006: Marvin 4.1.1</h3>
    <ul>
        <li><a href="http://www.chemaxon.com/marvinspace/changes.html">Changes
        in MarvinSpace 1.3.1</a></li>
        <li>Bugfixes:
        <ul>
            <li>Import/export:
            <ul>
                <li>In SMARTS import</li>
                <li>In CxSmiles import/export</li>
                <li>In 3D cleaning</li>
            </ul>
            </li>
            <li>Calculator plugins:
            <ul>
                <li>In pKa calculation</li>
                <li>In logP calculation</li>
                <li>in logD calculation</li>
            </ul>
            </li>
            <li>GUI:
                <ul>
                <li>Bugfix in pseudo atom display</li>
                <li>Out of memory error by dearomatizing huge view tables</li>
                <li>Minor bugs in loading/saving journal styles settings</li>
                </ul>
            <li>Valence check bug</li>
        </ul>
        </li>
    </ul>
<h3><a class="anchor" name="v4100"></a>August 4, 2006: Marvin 4.1</h3>
    <ul>
    <li>Import/Export:
	<ul>
        <li><a href="../formats/emf-doc.html">EMF</a> export</li>
        <li>Preliminary <a href="../formats/mol2-doc.html">Mol2</a> import/export</li>
	<li><a name="v4100.image">Image export</a>: atom and bond
	    <a HREF="../formats/images-doc.html#setcolors">set colors</a>,
            <a HREF="../formats/images-doc.html#wireThickness">wire thickness</a>,
            <a HREF="../formats/images-doc.html#stickThickness">stick thickness</a>

            <a HREF="../formats/images-doc.html#ballRadius">ball radius</a>,
            <a HREF="../formats/images-doc.html#downwedge_daylight">daylight down
            wedge</a>,
            <a HREF="../formats/images-doc.html#anybond_auto">anybond style</a>
            and
            <a HREF="../formats/images-doc.html#amap">atom mapping visibility</a>
	    can be specified.</li>
	<li><a name="v4100.mprop">Molecule and document properties</a>
	    with special data types (not only strings) are exported and
	    imported in
	    <a HREF="../formats/cml-doc.html#property">MRV</a>,
	    <a HREF="../formats/mol-csmol-doc.html#mprop">SDfiles and RDfiles</a>.
	    Supported data types: boolean, int, double, int[],
	    Molecule, MDocument.</li>
	<li><a href="../applications/molconvert.html">molconvert</a>: optionally skips
	    molecule on error and continues with the next molecule (option -g)
	    (default: as before, exits on error).</li>
	</ul>
	</li>
    <li>R-groups:
	<ul>
	<li><a name="v4100.rgmax">the maximum R-group number</a>
	    is increased to 32767 (from 32),
	    the minimum is decreased to 0 (from 1). R0-R32767 are saved
	    in <a HREF="../formats/mrv-doc.html">MRV</a>
	    and <a HREF="../formats/mol-csmol-doc.html#molV3">V3 Molfiles</a>.
	    In <a HREF="../formats/mol-csmol-doc.html">V2 Molfiles</a>,
	    only R1-R999 are saved. The values of following constants are changed:
	    <a HREF="beans/api/chemaxon/struc/MolAtom.html#RGROUP_MAX">MolAtom.RGROUP_MAX</a>=32767,
	    <a HREF="beans/api/chemaxon/struc/RgMolecule.html#RG_ID_MASK">RgMolecule.RG_ID_MASK</a>,
	    <a HREF="beans/api/chemaxon/struc/RgMolecule.html#RG_ID2_OFF">RG_ID2_OFF</a>,
	    <a HREF="beans/api/chemaxon/struc/RgMolecule.html#RG_ID2_MASK">RG_ID2_MASK</a>,
	    <a HREF="beans/api/chemaxon/struc/RgMolecule.html#RG_RESTH">RG_RESTH</a>.
	    </li>
	<li><a HREF="beans/api/chemaxon/struc/RgMolecule.html#indexOf(chemaxon.struc.CNode)">RgMolecule.indexOf(CNode)</a>,
	    <a HREF="beans/api/chemaxon/struc/RgMolecule.html#indexOf(chemaxon.struc.CEdge)">indexOf(CEdge)</a>,
	    <a HREF="beans/api/chemaxon/struc/RgMolecule.html#getNode(int)">getNode(int)</a>
	    and
	    <a HREF="beans/api/chemaxon/struc/RgMolecule.html#getEdge(int)">getEdge(int)</a>
	    are applied to the graph union instead of the root structure.
	    </li>
	</ul>
	</li>
    <li>GUI:
        <ul>
	<li>Integration of MarvinSketch and MarvinSpace:</li>
	    <ul>
	    <li>3 Dimensional structure building: allows to modify ligand in the binding pocket.</li>
	    <li>Visualization of plugin results: 3D structure display, colored molecular surfaces.</li>
	    </ul>
        <li>Save options in viewer.</li>
        <li>Viewer handles huge SD files.</li>
	<li>Journal based drawing style templates</li>
	<li>Multipage molecular documents</li>
	</ul>
    </li>
    <li>Plugins:
        <ul>
	<li>Plugins are grouped</li>
	<li>Added <a HREF="beans/api/chemaxon/marvin/calculations/OrbitalElectronegativityPlugin.html">OrbitalElectronegativityPlugin</a> to Charge group</li>
	<li>Added <a HREF="beans/api/chemaxon/marvin/calculations/StereoisomerPlugin.html">StereoisomerPlugin</a> to Isomers group</li>
	<li>Added <a HREF="beans/api/chemaxon/marvin/calculations/ConformerPlugin.html">ConformerPlugin</a>
            and <a HREF="beans/api/chemaxon/marvin/calculations/MolecularDynamicsPlugin.html">MolecularDynamicsPlugin</a> to Conformation group</li>
	<li>Added <a HREF="beans/api/chemaxon/marvin/calculations/GeometryPlugin.html">GeometryPlugin</a>
            and <a HREF="beans/api/chemaxon/marvin/calculations/MSAPlugin.html">MSAPlugin</a>
            (3D molecular surface area calculation - methods: van der Waals, solvent accessible) to Geometry group</li>
	<li>Improved calculation methods in several plugins (pKa, logP, logD, etc.)</li>
	<li>Possibility to save multimolecular plugin results from GUI (e.g. conformers)</li>
	</ul>
    </li>
    <li>License handling: added graphical license handler interface
        <ul>
	<li>press the 'Manage license keys' button on the Edit / Preferences / Licenses tab,
	    in MarvinSketch or MarvinView, or else
	<li>use the <code>license</code> command line script
	</ul>
	</li>
    <li><a href="http://www.chemaxon.com/marvinspace/">MarvinSpace</a> 1.3
        <ul>
            <li>Ribbon representation of protein secondary structure</li>
            <li>Interactive editing of molecules/ligands in MarvinSketch</li>
            <li>Copy/Cut/Paste, Drag&amp;Drop functions</li>
            <li>High resolution image export in various formats (PNG, BMP, Targa, JPG), arbitrary size</li>
            <li>New coloring types: secondary structure, rainbow, b-factor (temperature factor), SETOR residue colors, color palettes</li>
            <li>Interactive change of the isovalue of Gaussian Cube surfaces</li>
            <li>Enhanced selection tools: binding pocket, residues etc.</li>
	    <li>Enhanced labels (colors, size, placement), more built in types (e.g. residue labels, secondary structure label)</li>
	    <li>Improved error handling, out of memory does not terminate</li>
            <li>Progress bar, better feedback about processes </li>
        </ul>
    </li>
    <li>API changes:
	<ul>
	<li>Added <a HREF="beans/api/chemaxon/struc/Molecule.html#cloneMoleculeWithDocument()">
	    Molecule.cloneMoleculeWithDocument()</a>: clones the molecule with its non-molecular data
	    (graphics objects: polylines, arrows; electron flows; text boxes).
	    </li>
	<li>Added <a HREF="beans/api/chemaxon/struc/MoleculeGraph.html#arrangeComponents()">
	    Molecule.arrangeComponents()</a>: avoids overlapping components (reaction components,
	    R-group definitions) by translations.
	    </li>
	<li>Added
	    <a HREF="beans/api/chemaxon/formats/MolImporter.html">MolImporter</a>.<a HREF="beans/api/chemaxon/formats/MolImporter.html#skipRecord()">skipRecord()</a>,
	    <a HREF="beans/api/chemaxon/formats/MolImporter.html#nextDoc()">nextDoc()</a>,
	    <a HREF="beans/api/chemaxon/formats/MolImporter.html#getRecordCount()">getRecordCount()</a>,
	    <a HREF="beans/api/chemaxon/formats/MolImporter.html#getRecordCountMax()">getRecordCountMax()</a>,
	    <a HREF="beans/api/chemaxon/formats/MolImporter.html#estimateNumRecords()">estimateNumRecords()</a>,
	    <a HREF="beans/api/chemaxon/formats/MolImporter.html#seekRecord(int,%20chemaxon.marvin.util.MProgressMonitor)">seekRecord(int)</a>,
	    <a HREF="beans/api/chemaxon/formats/MolImporter.html#isRewindable()">isRewindable()</a>.
	    </li>
	<li><a HREF="beans/api/chemaxon/struc/Sgroup.html#setGUIStateRecursively(boolean)">Sgroup.setGUIStateRecursively</a>
		returns true if the state was changed.
	    </li>
	<li><a HREF="beans/api/chemaxon/struc/CGraph.html">CGraph</a> became
	    abstract,
	    <a HREF="beans/api/chemaxon/struc/MoleculeGraph.html">MoleculeGraph</a>(parent)
	    and
	    <a HREF="beans/api/chemaxon/struc/Molecule.html">Molecule</a>(parent)
	    constructors removed to avoid confusion with copy constructors.
	    </li>
	<li>The implementation of seeking in MolImporter required the extension
	    of the molecule/document import interfaces:<br>
	    <a HREF="beans/api/chemaxon/marvin/io/MolImportIface.html">MolImportIface</a>.<a HREF="beans/api/chemaxon/marvin/io/MolImportIface.html#isSkippingSupported()">isSkippingSupported()</a>,
	    <a HREF="beans/api/chemaxon/marvin/io/MolImportIface.html#skipMol()">skipMol()</a>
	    and
	    <a HREF="beans/api/chemaxon/marvin/io/MDocumentImportIface.html">MDocumentImportIface</a>.getGlobalProperties() must be implemented.</li>
	<li>The implementation of the sticky midpoints of MPolylines required
	    incompatible changes in the graphics classes:<br>
	    <a HREF="beans/api/chemaxon/struc/MPoint.html">MPoint</a>.getX(),
	    getY(), getZ(), setLocation(DPoint3) and
	    setXYZ(double, double, double) are replaced by
	    <a HREF="beans/api/chemaxon/struc/MPoint.html#getLocation(chemaxon.struc.CTransform3D)">getLocation(CTransform3D)</a>
	    and
	    <a HREF="beans/api/chemaxon/struc/MPoint.html#setLocation(chemaxon.struc.DPoint3, chemaxon.struc.CTransform3D)">setLocation(DPoint3, CTransform3D)</a>,
	    <a HREF="beans/api/chemaxon/struc/MObject.html">MObject</a>.calcCenter(DPoint3),
	    transform(CTransform3D, int) and getPointRef(int) are replaced by
	    by <a HREF="beans/api/chemaxon/struc/MObject.html#calcCenter(chemaxon.struc.DPoint3, chemaxon.struc.CTransform3D)">calcCenter(DPoint3, CTransform3D)</a>,
	    <a HREF="beans/api/chemaxon/struc/MObject.html#transform(chemaxon.struc.CTransform3D, int, chemaxon.struc.CTransform3D)">transform(CTransform3D, int, CTransform3D)</a>
	    and
	    <a HREF="beans/api/chemaxon/struc/MObject.html#getPointRef(int, chemaxon.struc.CTransform3D)">getPointRef(int, CTransform3D)</a>.
	    </li>
	</ul>
	</li>
    <li>Applet/bean parameter changes:
	<ul>
	<li>Toolbar templates are not handled together with the other
	    templates appearing in their own window. Toolbar template sets
	    can be specified using the new
	    <a href="sketchman.html#parameters.ttmpls">ttmpls</a> parameter,
	    the old <a href="sketchman.html#parameters.tmpls">tmpls</a>
	    parameter is used only for other template sets.
	    </li>
	</ul>
	</li>
    <li>Deprecation:
	<ul>
	<li>bondWidth property/applet parameter is replaced by
	    <a HREF="sketchman.html#parameters.bondSpacing">bondSpacing</a></li>
	<li><a href="beans/api/chemaxon/struc/RxnMolecule.html">RxnMolecule</a>.getStructureCount,
	    getStructure, addStructure, removeStructure methods are replaced by
	    <a href="beans/api/chemaxon/struc/RxnMolecule.html#getComponentCount()">getComponentCount</a>,
	    <a href="beans/api/chemaxon/struc/RxnMolecule.html#getComponent(int, int)">getComponent</a>,
	    <a href="beans/api/chemaxon/struc/RxnMolecule.html#addComponent(chemaxon.struc.Molecule, int)">addComponent</a>
	    and
	    <a href="beans/api/chemaxon/struc/RxnMolecule.html#addComponent(int, int)">removeComponent</a>.
	    </li>
	</ul>
	</li>
        <li>AWT applets have been removed from Marvin Applets package.</li>
        <li><a href="dotnet.html">.NET support</a> in Marvin Beans.</li>
    <li>Bugfixes:
        <ul>
        <li>Molecule.calcHybridization() assigns HS_S to hydrogen atoms.</li>
        <li>Fixed potential memory leaks in Marvin applets.</li>
        </ul>
    </li>
    </ul>
<h3><a class="anchor" name="v4006"></a>May 25, 2006: Marvin 4.0.6</h3>
    <ul>
    <li>Bugfixes:
        <ul>
        <li>In aromatization.</li>
        <li>In ionizer.</li>
        <li>In Marvin Beans installer.</li>
        <li>NullPointerException at refreshing empty cells in view table</li>
        </ul>
    </li>
    </ul>
<h3><a class="anchor" name="v4005"></a>March 24, 2006: Marvin 4.0.5</h3>
    <ul>
    <li>Import/Export:
        <ul>
        <li>Minor bugfixes in SMILES and in SMARTS import.</li>
        <li>Atom value in molfile.</li>
        <li>Bugfix in PDB import.</li>
        <li>Fix RXN incompatibility in MDLCompressor and in MolImporter.</li>
        </ul></li>
    <li>GUI:
        <ul>
        <li>Speed-up slow viewer/sketcher performance on very large structures.</li>
        <li>Shortcut for clear screen.</li>
        </ul></li>
    <li>Plugins:
        <ul>
        <li>Adding the dot-disconnected molecule formula calculation to Elemental Analysis.</li>
        </ul></li>
    <li>MarvinSpace:
        <ul>
        <li>Proper handling of H and LP on hetero groups.</li>
        <li>Bond order perception.</li>
        </ul></li>
    </ul>

<h3><a class="anchor" name="v4004"></a>January 23, 2006: Marvin 4.0.4</h3>
    <ul>
    <li>Import/Export:
        <ul>
        <li>Non-ASCII characters are escaped in MRV import/export.</li>
        <li>SMILES fields can be separated with TAB separators.</li>
        <li>Slow image export bug fixed.</li>
        <li>Minor bugs fixed in CML import.</li>
        <li>Bugfixes in PDB import. Note, that PDB import is no longer
            compatible with the original implementation which is still
            applied for <code>XYZ</code> files. See <a href="../formats/pdb-doc.html#notes">
            release notes</a>.</li>
        <li>Bugfix in the SMILES import of aromatic bonds.</li>
        <li>Minor CxSmiles import/export bug.</li>
        </ul>
    </li>
    <li>API:
        <ul>
        <li>Skin parameter/menu is denied in MViewPane and in
        MSketchPane. It is available ony in Marvin applets/applications.</li>
        <li>Giving of the encoding in MolConverter was changed. Both input and output
        encoding can be specified.</li>
        </ul>
    <li>GUI Bugfixes:
        <ul>
        <li>Link node drawing problem.</li>
        <li>Copy from R-group definition did not work.</li>
        <li>Last page of the view table was not printed if there were empty
        cells.</li>
        <li>PropertyChangeEvent was not invoked when molecule was changed.</li>
        <li>Down wedge looked like Up wedge on the toolbar.</li>
        <li>Pseudo atom exception in sketcher.</li>
        <li>Missing attachment point option in popup menu.</li>
        <li>Right click raises exception for reactions with abbrev. groups.</li>
        <li>Opening molfile with embedded groups caused exception.</li>
        </ul>
    </li>
    <li>Other Bugfixes:
        <ul>
        <li>Minor bugs in Calculator Plugins.</li>
        <li>Clean 2D produced zero coordinates.</li>
        <li>Edit source bullet import bug.</li>
        <li>Potential deadlock at the closing of a MarvinSketch applet.</li>
        <li>H removal throws exception on Markush structures.</li>
        <li>Bug in Molecule.clone().</li>
        </ul>
    </li>
    </ul>
<h3><a class="anchor" name="v4003"></a>November 10, 2005: Marvin 4.0.3</h3>
    <ul>
        <li>Bugfixes:
            <ul>
                <li>Minor bugfixes in MarvinSpace</li>
                <li>In mol file export</li>
            </ul>
        </li>
    </ul>
<h3><a class="anchor" name="v4002"></a>November 4, 2005: Marvin 4.0.2</h3>
    <ul>
    <li>Import/Export:
	<ul>
	<li><a href="../formats/inchi-doc.html">IUPAC InChi</a></li>
	<li><a href="../formats/mol-csmol-doc.html">MDL Molfiles, RGfiles etc.</a>:
	    Implicit H is stored as attached data when needed.</li>
	<li>Improved <a HREF="../formats/pdb-doc.html">PDB</a> import.</li>
        <li>Hydrogenize bug fixed: tetrahedral geometry of alcohol Oxygen atom is now maintained.</li>
	<li>Volumetric data import/export for <a HREF="../formats/cube-doc.html">CUBE files</a>
            (not processed by Marvin)
	</ul>
	</li>
    <li>Implemented trivalent doublet and quartet.
	(<a HREF="beans/api/chemaxon/struc/MolAtom.html#RAD3_DOUBLET">RAD3_DOUBLET</a>,
	<a HREF="beans/api/chemaxon/struc/MolAtom.html#RAD3_DOUBLET">RAD3_QUARTET</a>)
	</li>
    <li>Applets:
	<ul>
	<li>New methods:
        MSketch.getSelectedMol,
        MSketch.getMolMass,
        MSketch.getMolExactMass,
        MSketch.getMolFormula,
        MView.getMolMass,
        MView.getMolExactMass,
        MView.getMolFormula,
        JMSketch.getMolMass,
        JMSketch.getMolExactMass,
        JMSketch.getMolFormula,
        JMView.getMolMass,
        JMView.getMolExactMass,
        JMView.getMolFormula
        </li>
        <li>New sketcher parameter:
	    <a HREF="sketchman.html#parameters.undetachByX">undetachByX</a>.
	</li>
	</ul>
	</li>
    <li><a href="http://www.chemaxon.com/marvinspace/index.html">MarvinSpace</a> beta, includes public
            <a href="beans/api/chemaxon/marvin/space">API</a>:
        <ul>
            <li>Dynamic loading of JOGL libraries.</li>
            <li>Better handling of PDB files including not standard complient variants.</li>
            <li>Save graphic scene as PNG image file.</li>
            <li>Basic Gaussian Cube file support.</li>
            <li>Improved User Interface.</li>
            <li>Bugfixes.</li>
        </ul>
    </li>
    <li>Plugin:
        <ul>
            <li>New parameter in
                <a href="beans/api/chemaxon/marvin/calculations/ResonancePlugin.html#setTakeMajorContributors(boolean)">ResonancePlugin</a>:
	        take major contributors (-m, --mcontrib).</li>
            <li>New parameter in
                <a href="beans/api/chemaxon/marvin/calculations/TautomerizationPlugin.html#setTakeDominantTautomers(boolean)">TautomerizationPlugin</a>:
	        take dominant tautomers (--d, --dominants).</li>
            <li>Bugfix in
                <a href="beans/api/chemaxon/marvin/calculations/TautomerizationPlugin.html">TautomerizationPlugin</a>.</li>
        </ul>
    </li>
    <li>Sgroup names can contain almost any ascii character.</li>
    <li>Bugfixes:
        <ul>
            <li>Hydrogenize bug fixed: tetrahedral geometry of alcohol O is now
             maintained.</li>
            <li>Minor bugfixes in aromatization, in 2D cleaning and in
            molconverter.</li>
        </ul>
    </li>
    </ul>
<h3><a class="anchor" name="v4001"></a>August 19, 2005: Marvin 4.0.1</h3>
    <ul>
    <li>Trivalent radicals are displayed and their
	<a HREF="../formats/mrv-doc.html">MRV</a> import is fixed.</li>
    <li>Bugfixes in cxsmiles, in 2D cleaning and in parity calculation.</li>
    </ul>
<h3><a class="anchor" name="v4000"></a>August 8, 2005: Marvin 4.0</h3>
    <ul>
    <li><a href="http://www.chemaxon.com/MarvinSpace/index.html">MarvinSpace 1.0-alpha</a>
        is released as part of Marvin 4.0. <br>
        MarvinSpace is a 3D molecular structure visualization tool for
        displaying macromolecules, protein-ligand complexes as well as small
        molecules.<br>
        Release notes: At present MarvinSpace is available as standalone desktop
        application and as Java Applet.
        Java Beans and the public API will be released later.
        See also <a href="http://www.chemaxon.com/MarvinSpace/index.html#issues">Known Issues</a>.</li>
    <li>Import/export
	<ul>
	<li>Link node outer bonds imported and exported in CML, MRV and MDL.
	    </li>
	<li><a HREF="../formats/mrv-doc.html">MRV</a>:
	    atom and bond set colors are stored, animation of a chemical
	    process can be stored. Parity is stored in 0D.
	    </li>
	<li><a HREF="../formats/mrv-doc.html">CML</a>:
	    MRV tags and attributes are no longer supported but they
	    can be imported. If you have a CML document that is created by an
	    older Marvin, it is recommended to save that in MRV format!
	    </li>
	<li>RDfiles with missing &quot;M  END&quot;s are also imported.</li>
	<li><a HREF="../formats/msbmp-doc.html">MS BMP</a> image format
        (only export).</li>
        <li>SDF import: reading sdf-s containing empty structures</li>
        <li>Atom types are read in case insensitive manner in mol/sdf
        import.</li>
        <li>cxsmiles import/export: Pseudo atom types</li>
        <li>SMILES import/export: Handling of radicals and unusual valences</li>
        <li>Support for localized encoding of structure files.</li>
	</ul>
    </li>
    <li>Double bond with a wiggly bond neighbour are treated as unspecified
	E/Z configuration.</li>
    <li>Improved unique smiles canonicalization</li>
    <li>Improvements in chirality calculation</li>
    <li>Clean wedges function</li>
    <li>GUI:
    <ul>
        <li>New templates in MarvinSketch: Aromatics, Bridged Polycycles,
        Bicycles, Crown Ethers, Cycloalkanes, Fullerenes</li>
        <li>My Templates are available in the MarvinSketch applet (only in
        Swing).</li>
        <li>Wrap long data fields in MarvinView</li>
        <li>Shortcut + SHIFT click: automatically ungroup abbreviated
        groups</li>
        <li>View implied lone pairs</li>
        <li>Improved rendering quality</li>
        <li>Attach data to atoms (Data S-groups)</li>
        <li>Drawing of h&lt;n&gt;, D&lt;n&gt;, s&lt;n&gt;, s*, rb&lt;n&gt;,
        rb*, u query properties</li>
    </ul>
    </li>
    <li>Plugins
	<ul>
	<li>Calculator plugin handling is completely rewritten:
            <ul>
            <li>Dynamical loading of plugins and parameter panels based on configuration by
    	    <a href="beans/api/chemaxon/marvin/plugin/PluginFactory.html">PluginFactory</a></li>
	    <li>Separated graphical plugin result display:
	    <a href="beans/api/chemaxon/marvin/plugin/CalculatorPluginDisplay.html">
	CalculatorPluginDisplay</a></li>
	    <li>Graphical parameter panels are generated from XML:
	&lt;plugin class name&gt;Parameters.xml if exists, otherwise asked from the display class:
	<a href="beans/api/chemaxon/marvin/plugin/CalculatorPluginDisplay.html#getParameterPanel()">
	CalculatorPluginDisplay.getParameterPanel()</a></li>
	    <li>Configuration files <a href="../../xjars/calc.properties">calc.properties</a>
	(<a href="../applications/calc.html">Calculator</a>) and
	<a href="../../xjars/plugins.properties">plugins.properties</a> (MarvinSketch, MarvinView)
	together with the parameter panel configuration XMLs and custom plugin JARs are placed
	in the <a href="../../xjars">xjars</a> directory under Marvin root.</li>
	    <li>CalculatorOutput used by <a href="../applications/calc.html">Calculator</a> is renamed:
	<a href="beans/api/chemaxon/marvin/plugin/CalculatorPluginOutput.html">
	CalculatorPluginOutput</a>
	(and subclasses are found automatically by the following naming convention:
	output class name = &lt;plugin class name&gt;Output)</li>
	    <li><a href="beans/api/chemaxon/marvin/plugin/CalculatorPlugin.html">
	CalculatorPlugin</a> API is slightly changed.</li>
	    <li>A <a href="../../examples/plugin-dev/index.html">custom plugin example with test application</a>
	is added.</li>
	    </ul>
        </li>
	<li>Added MRV result molecule output to <a href="../applications/calc.html">cxcalc</a>.
	</li>
	<li>Added options JPEG / PNG image save and copy &amp; paste image to chart result display
	in MarvinSketch / MarvinView.
	</li>
	<li>Changes in specific plugin calculations:
            <ul>
	    <li>Added <a href="beans/api/chemaxon/marvin/calculations/IsoelectricPointPlugin.html">
	IsoelectricPointPlugin</a>.</li>
	    <li>Added <a href="beans/api/chemaxon/marvin/calculations/TautomerizationPlugin.html">
	TautomerizationPlugin</a> and <a href="beans/api/chemaxon/marvin/calculations/ResonancePlugin.html">
	ResonancePlugin</a>.</li>
	    <li>Added microspecies distribution calculation (msdistr) to
	<a href="../applications/calc.html">cxcalc</a>.</li>
	    <li>New topological properties in
	<a href="beans/api/chemaxon/calculations/TopologyAnalyser.html">TopologyAnalyser</a> and
	<a href="beans/api/chemaxon/marvin/calculations/TopologyAnalyserPlugin.html">
	TopologyAnalyserPlugin</a>:<br>
	   <a href="beans/api/chemaxon/calculations/TopologyAnalyser.html#asymmetricAtomCount()">
	       asymmetricAtomCount()</a>,
	   <a href="beans/api/chemaxon/calculations/TopologyAnalyser.html#balabanIndex()">
	       balabanIndex()</a>,
	   <a href="beans/api/chemaxon/calculations/TopologyAnalyser.html#carboRingCount()">
	       carboRingCount()</a>,
	   <a href="beans/api/chemaxon/calculations/TopologyAnalyser.html#carboaromaticRingCount()">
	       carboaromaticRingCount()</a>,
	   <a href="beans/api/chemaxon/calculations/TopologyAnalyser.html#cyclomaticNumber()">
	       cyclomaticNumber()</a>,
	   <a href="beans/api/chemaxon/calculations/TopologyAnalyser.html#distanceCount(int, int)">
	       distanceCount(int atom, int distance)</a>,
	   <a href="beans/api/chemaxon/calculations/TopologyAnalyser.html#distanceDegree(int)">
	       distanceDegree(int atom)</a>,
	   <a href="beans/api/chemaxon/calculations/TopologyAnalyser.html#eccentricity(int)">
	       eccentricity(int atom)</a>,
	   <a href="beans/api/chemaxon/calculations/TopologyAnalyser.html#hararyIndex()">
	       hararyIndex()</a>,
	   <a href="beans/api/chemaxon/calculations/TopologyAnalyser.html#hyperWienerIndex()">
	       hyperWienerIndex()</a>,
	   <a href="beans/api/chemaxon/calculations/TopologyAnalyser.html#isAsymmetricAtom()">
	       isAsymmetricAtom()</a>,
	   <a href="beans/api/chemaxon/calculations/TopologyAnalyser.html#isConnected()">
	       isConnected()</a>,
	   <a href="beans/api/chemaxon/calculations/TopologyAnalyser.html#isConnected(int, int)">
	       isConnected(int atom1, int atom2)</a>,
	   <a href="beans/api/chemaxon/calculations/TopologyAnalyser.html#plattIndex()">
	       plattIndex()</a>,
	   <a href="beans/api/chemaxon/calculations/TopologyAnalyser.html#randicIndex()">
	       randicIndex()</a>,
	   <a href="beans/api/chemaxon/calculations/TopologyAnalyser.html#shortestPath(int, int)">
	       shortestPath(int atom1, int atom2)</a>,
	   <a href="beans/api/chemaxon/calculations/TopologyAnalyser.html#stericEffectIndex()">
	       stericEffectIndex()</a>,
	   <a href="beans/api/chemaxon/calculations/TopologyAnalyser.html#szegedIndex()">
	       szegedIndex()</a>,
	   <a href="beans/api/chemaxon/calculations/TopologyAnalyser.html#wienerIndex()">
	       wienerIndex()</a>,
	   <a href="beans/api/chemaxon/calculations/TopologyAnalyser.html#wienerPolarity()">
	       wienerPolarity()</a>.
	    </li>
	    </ul>
        </li>
	</ul>
    </li>
    <li>API
	<ul>
	<li><a href="beans/api/chemaxon/struc/PeriodicSystem.html">PeriodicSystem</a>:
	    new radius functions
	   <a href="beans/api/chemaxon/struc/PeriodicSystem.html#getAtomicRadius(int)">getAtomicRadius(int z)</a>,
	   <a href="beans/api/chemaxon/struc/PeriodicSystem.html#getCovalentRadius(int)">getCovalentRadius(int z)</a>,
	   <a href="beans/api/chemaxon/struc/PeriodicSystem.html#getVanDerWaalsRadius(int)">getVanDerWaalsRadius(int z)</a>.
	    </li>
        <li><a href="beans/api/chemaxon/marvin/beans/MSketchPane.html">MSketchPane</a>:
	    new methods
            <a href="beans/api/chemaxon/marvin/beans/MSketchPane.html#isCloseEnabled">isCloseEnabled()</a>,
            <a href="beans/api/chemaxon/marvin/beans/MSketchPane.html#setCloseEnabled(boolean)">setCloseEnabled(boolean)</a>,
            <a href="beans/api/chemaxon/marvin/beans/MSketchPane.html#getMrvWithSettings()">getMrvWithSettings()</a>,
            <a href="beans/api/chemaxon/marvin/beans/MSketchPane.html#setMrvWithSettings(java.lang.String)">setMrvWithSettings(String)</a>,
            <a HREF="beans/api/chemaxon/marvin/beans/MSketchPane.html#getImage(double)">getImage(scale)</a>,
            <a HREF="beans/api/chemaxon/marvin/beans/MSketchPane.html#getImage(java.awt.Dimension)">getImage(size)</a>,
            <a HREF="beans/api/chemaxon/marvin/beans/MSketchPane.html#getBoundRectSize()">getBoundRectSize()</a>.
	    </li>
        <li><a href="beans/api/chemaxon/struc/MoleculeGraph.html#getLocalParity(int)">MoleculeGraph.getLocalParity(int)</a> to calculate local
        parity of an atom</li>
	<li>Incompatible API changes:
	    <ul>
	    <li><a HREF="beans/api/chemaxon/marvin/beans/MarvinPane.html">MarvinPane</a>.<a HREF="beans/api/chemaxon/marvin/beans/MarvinPane.html#setAtomSymbolsVisible(boolean)">setAtomSymbolsVisible(boolean)</a>
		applies to 3D molecules only; atom symbols are always visible
		in 2D.</li>
	    <li><a HREF="beans/api/chemaxon/struc/MolAtom.html">MolAtom</a>.<a HREF="beans/api/chemaxon/struc/MolAtom.html#getAtomSymbol(int, int, int[], chemaxon.struc.CTransform3D)">getAtomSymbol</a>(opts,
		aflags, lcenter, transform) uses SYM_* constant flags in its
		first argument instead of magic numbers.</li>
	    </ul>
        </li>
        </ul>
    </li>
    </ul>
<h3><a class="anchor" name="v3509"></a>July 27, 2005: Marvin 3.5.9</h3>

    <ul>
        <li>Improved aromatization</li>
        <li>Svg export gives UTF-8 encoded output.</li>
        <li>Bugfixes:
            <ul>
            <li>In CmlImport</li>
            <li>In 3D cleaning</li>

            <li>In dearomatization</li>
            </ul>
        </li>
    </ul>

<h3><a class="anchor" name="v3508"></a>June 29, 2005: Marvin 3.5.8</h3>
    <ul>
    <li>Bugfixes:
    <ul>
        <li>In plugin calculations.</li>
        <li>In parity.</li>
        <li>In 3D cleaning.</li>
        <li>In displaying of implicit hydrogens</li>
    </ul>
    </li>
    </ul>

<h3><a class="anchor" name="v3507"></a>May 28, 2005: Marvin 3.5.7</h3>
    <ul>
    <li>Minor bugfixes in plugin calculations.</li>
    </ul>

<h3><a class="anchor" name="v3506"></a>May 17, 2005: Marvin 3.5.6</h3>
    <ul>
    <li>R/S and canonical smiles calculation speedup.</li>

    <li>3D:
    <ul>
        <li>Improved 3D cleaning efficiency.</li>
        <li>New Clean3D options. Time limit in the 3D cleaning process.</li>
    </ul></li>
    <li>Bugfixes:
        <ul>
        <li>The R/S label is not displayed on the first atom of the
        structure.</li>

        <li>Fonts are not scaled on the printed page.</li>
        <li>Bug fixes in R/S calculation.</li>
        <li>Implicit hydrogen hides upper bond.</li>
        <li>Bug fix in cxsmiles export.</li>
        </ul>
    </li>
    </ul>

<h3><a class="anchor" name="v3505"></a>March 29, 2005: Marvin 3.5.5</h3>
    <ul>
    <li>Plugins: logP calculation is supplemented with metal fragments.</li>
    <li>Bugfixes:
        <ul>
        <li>RGfile import with nonstandard header.</li>
        <li>Fixing bugs according to the user's response and improvement of pKa
        calculation quality.</li>
        </ul></li>
    </ul>

<h3><a class="anchor" name="v3504"></a>January 31, 2005: Marvin 3.5.4</h3>
    <ul>
    <li>Smarts import: atom expressions of type [C,c] are imported as [#6].</li>
    <li>Bugfix: 3D cleaning can cause Java 1.4.2_06 VM to crash.</li>
    </ul>

<h3><a class="anchor" name="v3503"></a>January 24, 2005: Marvin 3.5.3</h3>
    <ul>
    <li>Bugfix: Import of SMILES containing atoms with &quot;unspecified atomic
    number&quot; (*) is fixed.</li>
    </ul>
<h3><a class="anchor" name="v3502"></a>January 11, 2005: Marvin 3.5.2</h3>
    <ul>
    <li>Colored MarvinView labels at celular level</li>
    <li>Bugfixes:
        <ul>
            <li>Error in extended SMILES export</li>
            <li>NullPointerException at adding new structure to My Templates</li>
            <li>Error in drawing multiple groups</li>
        </ul>
    </ul>

<h3><a class="anchor" name="v3501"></a>November 29, 2004: Marvin 3.5.1</h3>
    <ul>
    <li>Extended SMILES export: <a href="../formats/cxsmiles-doc.html#option_d">d</a>
        option to write C/T info for ring double bonds.</li>
    <li>API:
        <ul>
        <li>New methods: <a href="applets/api/JMView.html#isEmpty(int)">JMView.isEmpty()</a>,
        MView.isEmpty,
        <a href="applets/api/JMSketch.html#isEmpty()">JMSketch.isEmpty()</a>,
        <a href="applets/api/JMSketch.html#getAtomCount()">JMSketch.getAtomCount()</a>,
        <a href="applets/api/JMSketch.html#isAtomSelected(int)">JMSketch.isAtomSelected</a>,
        <a href="applets/api/JMSketch.html#isEmpty()">MSketch.isEmpty()</a>,
        MSketch.getAtomCount,
        <a href="applets/api/JMSketch.html#isAtomSelected(int)">MSketch.isAtomSelected()</a>.
        </ul>
    </li>
    </ul>

<h3><a class="anchor" name="v3500"></a>November 5, 2004: Marvin 3.5</h3>
    <ul>
    <li>Plugins:
        <ul>
	<li><a href="beans/api/chemaxon/marvin/calculations/TopologyAnalyserPlugin.html">
	    TopologyAnalyserPlugin</a> is available: calculates molecule properties from topology like number of various ring types, rotatable bonds.</li>
	<li>Calculation of molecular polarizability is available in
	    <a href="beans/api/chemaxon/marvin/calculations/PolarizabilityPlugin.html">PolarizabilityPlugin</a>.</li>
	<li>Improved pKa and logP predictions.<a href="../../chemaxon/marvin/help/Validations.html#Top">Test Results</a></li>
	<li>Added calculation of aromatic system and aromatic ring total charges in
	    <a href="beans/api/chemaxon/marvin/calculations/ChargePlugin.html">ChargePlugin</a>.</li>
	</ul>
	</li>
    <li><a class="text" name="v3500io">Import/export:</a>
	<ul>
	<li>Link nodes are imported and exported in
	    <a HREF="../formats/mol-csmol-doc.html">MDL molfiles</a> and
	    <a HREF="../formats/mrv-doc.html">MRV</a>/<a HREF="../formats/cml-doc.html">CML</a>.
	    See also the <a HREF="#v3500linknodeAPI">related API changes</a>.
	    </li>
	<li>MDL Molfiles:
	    <ul>
	    <li><a HREF="../formats/mol-csmol-doc.html#ioption_Usg">Usg</a> import
		option to ungroup all S-groups.</li>
	    <li>Atom coordinates are not rescaled in 2D V2 molfiles if the
		<a HREF="../formats/mol-csmol-doc.html#ioption_b">b import</a>/<a HREF="../formats/mol-csmol-doc.html#option_b">export</a>
		option is used with 0 argument.</li>
	    <li><a HREF="molcompress-doc.html#java">MdlCompressor</a> can
		compress Rxnfiles.</li>
	    </ul>
	    </li>
	<li>SMARTS import:
	    <a HREF="../formats/smiles-doc.html#ioption_d"><b>d</b> option for Daylight-conform meaning of "H"</a>.
	    Importing ambiguous SMARTS gets a warning message when not using
	    this option.</li>
	<li><a HREF="../formats/mrv-doc.html">MRV</a> (Marvin Document) and
	    <a HREF="../formats/cml-doc.html">CML</a>:
	    <ul>
	    <li><a class="text" NAME="v3500rxnarrow"><i>reaction arrow type</i></a> is stored
		(default, resonance, retrosynthetic or equilibrium),
		see also the <a HREF="#v3500api.rxnarrow">API changes</a>.</li>
	    <li>valence is imported and exported</li>
	    </ul>
	    </li>
	<li>Image export:
	    <ul>
	    <li><a HREF="../formats/images-doc.html#chiral">Options
		for displaying chiral structures.</a></li>
	    <li>Automatic generation of atom coordinates for 0D molecules.</li>
	    </ul>
	</li>
	</ul>
	</li>
    <li>API:
	<ul>
	<li>New methods and functions:
	    <ul>
	    <li><a NAME="v3500linknodeAPI"><a href="beans/api/chemaxon/struc/MolAtom.html">MolAtom</a></a>
		methods related to link nodes:
		<a href="beans/api/chemaxon/struc/MolAtom.html#getMaxRepetitions()">getMaxRepetitions()</a>,
		<a href="beans/api/chemaxon/struc/MolAtom.html#setMaxRepetitions(int)">setMaxRepetitions(int)</a>,
		<a href="beans/api/chemaxon/struc/MolAtom.html#getMinRepetitions()">getMinRepetitions()</a>,
		<a href="beans/api/chemaxon/struc/MolAtom.html#setMinRepetitions(int)">setMinRepetitions(int)</a>,
		<a href="beans/api/chemaxon/struc/MolAtom.html#isLinkNode()">isLinkNode()</a>.
		</li>
	    <li><a NAME="v3500api.rxnarrow"><a href="beans/api/chemaxon/struc/RxnMolecule.html">RxnMolecule</a></a>
		methods for accessing reaction arrow type:
		<a href="beans/api/chemaxon/struc/RxnMolecule.html#getReactionArrowType()">getReactionArrowType()</a>,
		<a href="beans/api/chemaxon/struc/RxnMolecule.html#getReactionArrowTypeName()">getReactionArrowTypeName()</a>,
		<a href="beans/api/chemaxon/struc/RxnMolecule.html#setReactionArrowType(int)">setReactionArrowType(int)</a>,
		<a href="beans/api/chemaxon/struc/RxnMolecule.html#setReactionArrowType(java.lang.String)">setReactionArrowType(String)</a>.
		</li>
	    <li>MSketch,
		<a href="applets/api/JMSketch.html">JMSketch</a>,
		MView and
		<a href="applets/api/JMView.html">JMView</a> applet methods
		<ul>
		<li>for accessing molecule properties:
		<a href="applets/api/JMSketch.html#getMolProperty(java.lang.String)">getMolProperty(String)</a>,
		<a href="applets/api/JMSketch.html#setMolProperty(java.lang.String, java.lang.String)">setMolProperty(String, String)</a>,
		<a href="applets/api/JMSketch.html#getMolPropertyCount()">getMolPropertyCount()</a>,
		<a href="applets/api/JMSketch.html#getMolPropertyKey(int)">getMolPropertyKey(int)</a>,
		<a href="applets/api/JMView.html#getMProperty(int, java.lang.String)">getMProperty(int, String)</a>,
		<a href="applets/api/JMView.html#setMProperty(int, java.lang.String, java.lang.String)">setMProperty(int, String, String)</a>,
		<a href="applets/api/JMView.html#getMPropertyCount(int)">getMPropertyCount(int)</a>,
		<a href="applets/api/JMView.html#getMPropertyKey(int, int)">getMPropertyKey(int, int)</a>,
		</li>
		<li>for accessing extra atom label and alias strings:
		<a href="applets/api/JMSketch.html#setAtomAlias(int, java.lang.String)">setAtomAlias(int, String)</a>,
		<a href="applets/api/JMSketch.html#getAtomAlias(int)">getAtomAlias(int)</a>,
		<a href="applets/api/JMSketch.html#setAtomExtraLabel(int, java.lang.String)">setAtomExtraLabel()</a>,
		<a href="applets/api/JMSketch.html#getAtomExtraLabel(int)">getAtomExtraLabel(int)</a>,
		<a href="applets/api/JMView.html#setAtomAlias(int, int, java.lang.String)">setAtomAlias(int, int, String)</a>,
		<a href="applets/api/JMView.html#getAtomAlias(int, int)">getAtomAlias(int, int)</a>,
		<a href="applets/api/JMView.html#setAtomExtraLabel(int, int, java.lang.String)">setAtomExtraLabel(int, int, String)</a>,
		<a href="applets/api/JMView.html#getAtomExtraLabel(int, int)">getAtomExtraLabel(int, int)</a>.
		</li>
		</ul>
		</li>
	    <li>Added JavaScript functions in <code>marvin.js</code> to get JVM
		and GUI ("awt" or "swing"):
		<code>marvin_get_jvm()</code> and <code>marvin_get_gui()</code>.
		</li>
	    </ul>
	    </li>
	<li>Changes:
	    <ul>
	    <li>Valence is not a query property any more, use
		<a href="beans/api/chemaxon/struc/MolAtom.html">MolAtom</a>.<a href="beans/api/chemaxon/struc/MolAtom.html#setValenceProp(int)">setValenceProp</a>(v)
		and
		<a href="beans/api/chemaxon/struc/MolAtom.html#getValenceProp()">getValenceProp</a>()
		instead of
		<a href="beans/api/chemaxon/struc/MolAtom.html#setQProp(java.lang.String, int)">setQProp</a>("v", v)
		and
		<a href="beans/api/chemaxon/struc/MolAtom.html#getQPropAsInt(java.lang.String)">getQPropAsInt</a>("v").
		</li>
	    <li>The simpView applet parameter and MSketchPane.setSimpView(int)
		are deprecated, the simpView = 2 setting is removed. Use
		<a HREF="sketchman.html#parameters.bondDraggedAlong">bondDraggedAlong</a>
		and
		<a HREF="beans/api/chemaxon/marvin/beans/MSketchPane.html">MSketchPane</a>.<a HREF="beans/api/chemaxon/marvin/beans/MSketchPane.html#setBondDraggedAlong(boolean)">setBondDraggedAlong(boolean)</a> instead.
		</li>
	    <li>Serialization:
		<a HREF="beans/api/chemaxon/struc/DPoint3.html">DPoint3</a> and
		<a HREF="beans/api/chemaxon/struc/CTransform3D.html">CTransform3D</a>
		became Externalizable.</li>
	    </ul>
	    </li>
	</ul>
	</li>
    </ul>

<h3><a class="anchor" name="v3403"></a>August 16, 2004: Marvin 3.4.3</h3>
    <ul>
    <li>New applet parameter: <a href="viewman.html#molChanged0">molChanged0</a>
     to evaluate a JavaScript code when the molecule is loaded.</li>
    <li>Bugfixes: NullPointerException in shapely color mode,
    deadlock in MarvinSketch applet.</li>
    </ul>
<h3><a class="anchor" name="v3402"></a>August 5, 2004: Marvin 3.4.2</h3>
    <ul>
    <li>Bugfixes: memory leak and deadlock in sketcher bean.</li>
    </ul>
<h3><a class="anchor" name="v3401"></a>July 29, 2004: Marvin 3.4.1</h3>
    <ul>
    <li>Reaction <a href='beans/api/chemaxon/marvin/modules/AutoMapper.html'>AutoMapper</a> class API is available.</li>
    <li>Calculator Plugin API usage examples in class headers.</li>
    <li>Atom symbol/label font can be changed using the
	<a HREF="viewman.html#parameters.atomFont">atomFont</a>
	applet parameter.</li>
    </ul>

<h3><a class="anchor" name="v3400"></a>July 1, 2004: Marvin 3.4</h3>
    <ul>
    <li>Import/Export
	<ul>
	<li>New formats:
	    <a HREF="../formats/cxsmiles-doc.html">ChemAxon Extended SMILES
	    and SMARTS</a>,
	    <a HREF="../formats/cube-doc.html">Gaussian Cube</a>.
	    </li>
	<li>Full support of <a HREF="../formats/smiles-doc.html">SMARTS</a>.
	    Added features:
	    <ul>
	        <li>Recursive SMARTS $()</li>
	        <li>Logical expressions in atom and bond: high and low priority "and"(& and ;), "or" (,) and "not" (!).</li>
	        <li>Degree atom query property (D)</li>
	        <li>Implicit hydrogens atom query property (h)</li>
	        <li>Chiral or unspecified atoms @?, @@?</li>
	        <li>Ring bond query property (@)</li>
	        <li>Conditional directional bonds: /?,\?</li>
	    </ul></li>
	<li><a HREF="../formats/cml-doc.html">CML</a> and
	    <a HREF="../formats/mrv-doc.html">Marvin Document Format</a> fix:
	    atom aliases and query strings are imported and exported.</li>
	<li><a HREF="../formats/mol-csmol-doc.html">MDL Molfiles</a>:
	    S-group parent list (M  SPL in V2, PARENT in V3) and expansion
	    information (M  SDS EXP in V2, ESTATE in V3) are stored.</li>
	<li>Incompatible API change in the
	    <a HREF="beans/api/chemaxon/marvin/io/MolImportIface.html">MolImportIface</a>
	    interface: molecule import modules must implement the
	    <a HREF="beans/api/chemaxon/marvin/io/MolImportIface.html#createMol()">createMol()</a>
	    method.</li>
	<li><a HREF="beans/api/chemaxon/formats/MolConverter.html">MolConverter</a>
	    and
	    <a HREF="beans/api/chemaxon/formats/MolExporter.html">MolExporter</a>
	    fix: created text files are in the local text format (instead of
	    unix) if the useSysEOL boolean argument is <code>true</code>.
	    The molconvert program writes OS dependent end of lines always.</li>
	</ul>
	</li>
    <li>S-groups
	<ul>
	<li>Embedded S-group support (in MDL Molfiles and Marvin Document
	    Format).</li>
	<li>API changes in
	    <a HREF="beans/api/chemaxon/struc/Sgroup.html">Sgroup</a>,
	    <a HREF="beans/api/chemaxon/struc/sgroup/SuperatomSgroup.html">SuperatomSgroup</a>,
	    and
	    <a HREF="beans/api/chemaxon/struc/sgroup/MultipleSgroup.html">MultipleSgroup</a>:
	    the copy and the newInstance methods are replaced by copy
	    constructors and cloneSgroup.</li>
	</ul>
	</li>
    <li>Other
	<ul>
	<li>Line thickness can be set for wireframe mode using the
	    <a HREF="viewman.html#3d.wireThickness">wireThickness</a> applet
	    parameter and the
	    MarvinPane.<a HREF="beans/api/chemaxon/marvin/beans/MarvinPane.html#setWireThickness(double)">setWireThickness</a>
	    bean method.</li>
	<li>Line thickness for graphics line, polyline, rectangle and text box
	    objects can be set using
	    MPolyline.<a HREF="beans/api/chemaxon/struc/graphics/MPolyline.html#setThickness(double)">setThickness</a>.
	    </li>
	</ul>
	</li>
    </ul>

<h3><a class="anchor" name="v3303"></a>March 9, 2004: Marvin 3.3.3</h3>
    <ul>
    <li>MDL Molfile import: S-groups are contracted by default.
	Importing in expanded form; use the
	<a HREF="../formats/mol-csmol-doc.html#ioption_Xsg">Xsg</a>
	option.</li>
    <li>Abbreviated groups: Abbreviations are given a priority over the element symbols.
    New abbreviations in the default abbeviated group list.</li>
	<li>Improved p<i>K</i><sub>a</sub>, log<i>P</i>, log<i>D</i> calculation.</li>
    <li>New methods in MolPrinter:
        <a href="beans/api/chemaxon/marvin/MolPrinter.html#getChiralitySupport()">getChiralitySupport()</a>,
        <a href="beans/api/chemaxon/marvin/MolPrinter.html#getExplicitH()">getExplicitH()</a>,
        <a href="beans/api/chemaxon/marvin/MolPrinter.html#isAtomSymbolsVisible()">isAtomSymbolsVisible()</a>,
        <a href="beans/api/chemaxon/marvin/MolPrinter.html#isEzVisible()">isEzVisible()</a>,
        <a href="beans/api/chemaxon/marvin/MolPrinter.html#setAtomSymbolsVisible(boolean)">setAtomSymbolsVisible()</a>,
        <a href="beans/api/chemaxon/marvin/MolPrinter.html#setChiralitySupport(int)">setChiralitySupport()</a>,
        <a href="beans/api/chemaxon/marvin/MolPrinter.html#setExplicitH(boolean)">setExplicitH()</a>,
        <a href="beans/api/chemaxon/marvin/MolPrinter.html#setEzVisible(boolean)">setEzVisible()</a>.</li>
    <li>Marvin Applets: <a href="index.html#noliveconnect">Workaround for browsers without
        Java - JavaScript communication</a>.</li>
    <li>Mac friendly shortcuts for Cut, Copy and Paste.</li>
    <li>Bugfixes:</li>
        <ul>
        <li>In MolConverter</li>
        <li>In the opening of the edit source window.</li>
        </ul>
    </ul>

<h3><a class="anchor" name="v3302"></a>February 4, 2004: Marvin 3.3.2</h3>
    <ul>
    <li>Bugfixes: in SMILES import.</li>
    </ul>

<h3><a class="anchor" name="v3301"></a>January 29, 2004: Marvin 3.3.1</h3>
    <ul>
	    <li>Mac OS X: MarvinSketch and MarvinView applications use the
		screen menu bar.</li>
	    <li>Bugfix in the drawing of R-group attachment points.</li>
    </ul>

<h3><a class="anchor" name="v3300"></a>January 14, 2004: Marvin 3.3</h3>
    <ul>
    <li>Atom and bond sets
	<ul>
	<li>Colorable <b>bond sets</b> are introduced, see the
	    <a HREF="viewman.html#parameters.bondSet">bondSet</a> and
	    <a HREF="viewman.html#parameters.bondSetColor">bondSetColor</a>
	    applet parameters, the
	    MView.setBondSetColor()
	    applet method and the <code>MarvinPane.setBondSetColor()</code> bean
	    method.</li>
	<li>Atom and bond set coloring can be used together with other color
	    schemes. Atoms and bonds in set 0 are displayed in Monochrome,
	    CPK, Shapely or Group color scheme, but other sets are displayed
	    with atom/bond set colors if the value of the
	    <a HREF="viewman.html#parameters.setColoringEnabled">setColoringEnabled</a>
	    applet parameter is <code>true</code> (default).
	    </li>
	<li>Deprecation:
	    <ul>
	    <li>Use the
		<a HREF="viewman.html#parameters.atomSet">atomSet</a>
		applet parameter instead of set,
		<a HREF="viewman.html#parameters.atomSetColor">atomSetColor</a>
		instead of setColor and the
		MView.setAtomSetColor()
		and <code>MarvinPane.setBondSetColor()</code> methods instead of
		setSetColor.</li>
	    <li>Use
		<a HREF="viewman.html#parameters.colorScheme">colorScheme</a>="mono"
		and
		<a HREF="viewman.html#parameters.setColoringEnabled">setColoringEnabled</a>=<code>true</code>
		settings instead of the deprecated
		"atomset" color scheme.
	    </ul>
	    </li>
	</ul>
	</li>
    <li>Import/export:
	<ul>
	<li><a HREF="../formats/mrv-doc.html">Marvin Document</a> file format</li>
	<li>Multiple Sgroup support for
	    <a HREF="../formats/mol-csmol-doc.html">MDL molfiles</a></li>
	<li>New <a HREF="../formats/images-doc.html">image export</a>
	    option to switch off antialiasing: <code>noantialias</code>.
	    </li>
	<li><a HREF="../formats/cml-doc.html">CML</a> improvements:
	    support of CML2 atom/bond attributes (instead of the deprecated
	    builtins), reactions, stereochemistry, residues.
	    Because of efficiency reasons, deprecated CML-1 data types are
	    not supported any more.</li>
	<li><a HREF="../formats/base64-doc.html">Base64</a> encoding/decoding.</li>
	<li>Import options for input files are specified in braces instead of
	    parentheses (&quot;file{options}&quot;) to avoid conflicts with
	    ugly SMILES strings ending with a branch specification.
	    For backward compatibility, parentheses still work after
	    filenames (but not after SMILES strings).</li>
	</ul>
	</li>
    <li>3D:
	<ul>
	<li>Analitic gradients implemented in Dreiding forcefield for optimization and Clean 3D</li>
	</ul>
	</li>
    <li>Document editing with graphics objects in sketcher
	<ul>
	<li>The <a HREF="beans/api/chemaxon/struc/MDocument.html">MDocument</a>
	    class stores the molecule and other graphics objects like
	    polylines and rectangles. The document can be saved in
	    <a HREF="../formats/mrv-doc.html">Marvin Document</a> format.</li>
	</ul>
	</li>
	<li>Calculator plugins
    <ul>
	<li>Support electrolite concentration is taken into account in log<i>P</i> and log<i>D</i> calculations.
	</li>
	</ul>
    <ul>
	<li>Concentration of K, Na and Cl ions can be adjusted in the range of 0 - 0.25 mol/l.</li>
	</ul>
    <ul>
	<li>log<i>P</i> calculation of zwitterionic compounds also have been improved with considering distance correction
	factors.
	</li>
	</ul>
	<ul>
	<li>p<i>K</i><SUB>a</SUB> calculation have been extended to carbon-bases. Now basic p<i>K</i><SUB>a</SUB> estimation of Indole derivatives available.
	Both acidic and basic p<i>K</i><SUB>a</SUB> values are displayed.
	Microspecies distribution is shown on chart.
	</li>
	</ul>
	<ul>
	<li>Accuracy of p<i>K</i><SUB>a</SUB> calculations is improved for heterocyclic compounds.
	</li>
	<li>
	Calculation of polar surface area (PSA) based on 2D topological (TPSA) information of molecules are available.
	</li>
	</ul>

	</li>

    <li>Others:
	<ul>
	<li>Recognition of more than four hundred chemical abbreviations.</li>
	<li>Help documents can be specified by applet parameters:
	    <a HREF="sketchman.html#parameters.sketchHelp">sketchHelp</a>,
	    <a HREF="sketchman.html#parameters.sketchQuickHelp">sketchQuickHelp</a>,
	    <a HREF="viewman.html#parameters.viewHelp">viewHelp</a>,
	    <a HREF="viewman.html#parameters.viewQuickHelp">viewQuickHelp</a>.
	    </li>
	<li><a href="beans/api/chemaxon/struc/Molecule.html#draw(java.awt.Graphics,%20java.lang.String)">Molecule.draw</a>
	paints the molecule to a graphics context.</li>

	<li>Standard residue names returned by
	    <a href="beans/api/chemaxon/struc/MolAtom.html#residueSymbolOf(int)">MolAtom.residueSymbolOf(int)</a>
	    are not all upper case, only the first letter (&quot;Ala&quot;
	    instead of &quot;ALA&quot;).</li>
	<li>Marvin GUI: Set the visibility of the atom chirality (<em>R</em>/<em>S</em>) labels from
		<em>View/Misc</em> menu.
	<li>Double click in Windows operating systems opens chemical files with
		the MarvinView application.</li>
	<li>Deprecation: SketchMolecule (replaced by MDocument),
	    SuperAtom.updateSgroupCrossings() (replaced by
	    Sgroup.findCrossingBonds())</li>
	<li>Incompatible change: chemaxon.struc.sgroup.Contractable
	    interface removed,
	    <a href="beans/api/chemaxon/struc/sgroup/Expandable.html">Expandable</a>
	    changed. This change was required because MDL's incomplete
	    multiple group expansion had to be simulated for proper
	    molfile export.</li>
	</ul>
	</li>
    <li>Bugfixes: in MViewPanel.setParams, in Dreiding forcefield, PDB import,
    atom lists in V3 molfiles, resize of MViewPanel and MSketchPanel,
    R-logic, etc.</li>
    </ul>

<h3><a class="anchor" name="v3200"></a>July 28, 2003: Marvin 3.2</h3>
    <ul>
	<li>Enhanced
	<a HREF="../sci/stereo-doc.html">stereochemical representation</a>
	 support in
	<a HREF="../formats/mol-csmol-doc.html#molV3">extended molfiles</a>.
	</li>
	<li>Improved <em>Charge</em> and <em>pKa</em> calculation.</li>
	<li>Bugfixes:
		<ul>
		<li>In SMILES <em>q</em> option.</li>
		<li>In flip.</li>
		</ul></li>
	</ul>

<h3><a class="anchor" name="v3104"></a>June 30, 2003: Marvin 3.1.4</h3>
    <ul>
    <li>RDfiles containing internal and external regnos: the first molecule
	or reaction type property is supposed to be the main structure.</li>
    <li>API changes
	<ul>
	<li>Removed the broken RxnMolecule.moveReactionArrow method.</li>
	</ul>
	</li>
	<li>Calculator plugins:
		<ul>
		<li>logP: New atom types.</li>
		<li>pKa:
			<ul>
			<li>Hydrogen bound defined between aromatic carboxyl and phenolic
				hidroxyl groups in pKa plugin.</li>
			<li>Calculation of the sulphonamid acid molecules were improved.</li>
			<li>In calculation of third order amins strain is considered.</li>
			</ul>
		</ul>
    </ul>

<h3><a class="anchor" name="v3103"></a>June 2, 2003: Marvin 3.1.3</h3>
    <ul>
    <li>Molecule file formats:
	<ul>
	<li><a HREF="../formats/mol-csmol-doc.html#rxnV3">Extended reaction
	    files</a>: AGENTS are imported and exported (in a
	    non-standard way, because MDL's standard format does not support
	    them).</li>
	<li><a HREF="../formats/smiles-doc.html">SMILES</a> import:
	    error checking improved.</li>
	<li><a HREF="../formats/pdb-doc.html">PDB</a> import/export:
	    bonds of standard residues are not exported and
	    not guessed from coordinates at import. Full atom names (including
	    remoteness indicator and branch designator) are imported and
	    exported.</li>
	</ul>
	</li>
    <li>2D and 3D:
	<ul>
	<li>Background color, rendering style and navigation mode are stored
	    separately for 2D and 3D. Cleaning a molecule in 3D also switches
	    automatically to the 3D parameter set in that cell. Cleaning in 2D
	    switches back to the 2D parameters.</li>
	<li><a HREF="viewman.html#parameters.clean2dOpts">clean2dOpts</a> and
	    <a HREF="viewman.html#parameters.clean3dOpts">clean3dOpts</a>
	    parameters.</li>
	</ul>
	</li>
    </ul>

<h3><a class="anchor" name="v3102"></a>May 6, 2003: Marvin 3.1.2</h3>
    <ul>
    <li>MDL import/export improvements:
	<ul>
	<li><a HREF="../formats/mol-csmol-doc.html#rxnV3">Extended reaction file</a>
	    import/export (&quot;rxn:V3&quot;).</li>
	<li>Rgroups are supported in
	    <a HREF="../formats/mol-csmol-doc.html#molV3">extended molfile</a>
	    import/export.</li>
	<li>RDfiles: Molecule and reaction type properties ($MFMT and
	    $RFMT) are also imported and exported. Use the
	    <a HREF="beans/api/chemaxon/struc/Molecule.html#getPropertyObject(java.lang.String)">Molecule.getPropertyObject</a>
	    function to get such properties.</li>
	</ul>
	</li>
    <li>S-groups: automatic contraction after import in sketcher and viewer.
	</li>
    <li>API changes:
	<ul>
	<li><a HREF="beans/api/chemaxon/struc/MoleculeGraph.html#calcCenter()">MoleculeGraph.calcCenter</a>
	    returns the geometrical center instead of the center of mass.</li>
	</ul>
	</li>
    <li>Bugfixes: R-logic bug.</li>
    </ul>

<h3><a class="anchor" name="v3101"></a>April 30, 2003: Marvin 3.1.1</h3>
    <ul>
	<li>JRE bug workaround (inappropriate lines were drawn on the Marvin panel).</li>
	</ul>

<h3><a class="anchor" name="v3100"></a>April 29, 2003: Marvin 3.1</h3>
    <ul>
    <li>Applets
	<ul>
	<li>Java 1.0 compatibility is no longer maintained.
	    The minimum Java VM version required to run the applets is 1.1
	    <small>(Netscape 4.06, MSIE 4 or later version)</small>.
	    </li>
	<li>The Swing version of the unsigned applets are no longer
	    available. Use the signed applets instead. To unsign signed
	    jar files, see the <a href="applets/appletfaq.html#unsign_jar">FAQ</a>.</li>
	<li>The deprecated installer for Marvin Applets is no longer maintained.
	    The set of downloadable Marvin Applets packages has been reduced.
	    </li>
	</ul>
	</li>
    <li>Plugins:
	<ul>
	    <li>New plugin: Atom polarizability</li>
	    <li>Chart display in log<i>D</i> plugin</li>
	</ul></li>
    <li>Molecule import/export improvements:
	<ul>
	<li><a HREF="../formats/smiles-doc.html">SMILES:</a>
	    <ul>
	    <li><a HREF="../formats/smiles-doc.html#ioption_f">Data fields
		can be imported from multi-column files.</a></li>
	    <li>Better error checking at import: exception is thrown if a ring
		is formed from two neighboring atoms.</li>
	    </ul>
	    </li>
	<li><a NAME="v3100pdb" HREF="../formats/pdb-doc.html">PDB:</a>
	    residues are imported as Sgroups,
	    Sgroups are exported as residues.</li>
	<li><a HREF="../formats/mol-csmol-doc.html">Molfiles:</a>
	    <ul>
	    <li>Sgroups are also imported as (PDB) residues. As a consequence,
		the PDB-specific coloring styles, &quot;shapely&quot;
		and &quot;group&quot;, are also applicable to molfiles
		containing Sgroups.</li>
	    <li><a HREF="../formats/mol-csmol-doc.html#molV3">Extended (V3) molfile
		export.</a></li>
	    <li>Changes in V2 format:
		<ul>
		<li>3D molecule coordinates are stored in
		    Angstrom units. The unit can be specified as an
		    <a HREF="../formats/mol-csmol-doc.html#ioptions">import
		    option</a>.
		    </li>
		<li>Illegal atom map values (from molfiles generated by the
		    IDBS ChemXtra data cartridge) are not considered to be
		    error at import.</li>
		</ul>
		</li>
	    </ul>
	    </li>
	</ul>
	</li>
    <li>API changes
	<ul>
	<li>New RgMolecule method:
	    <a HREF="beans/api/chemaxon/struc/RgMolecule.html#addRgroupsTo(chemaxon.struc.Molecule)">RgMolecule.addRgroupsTo</a>.
	    </li>
	<li>New flag
	    <a HREF="beans/api/chemaxon/struc/RxnMolecule.html#RGROUPED">RxnMolecule.RGROUPED</a>
	    for the
	    <a HREF="beans/api/chemaxon/struc/RxnMolecule.html#getStructure(int, int)">getStructure(flags, f)</a>
	    method.</li>
	<li>New methods related to explicit Hydrogen removal:
	    MoleculeGraph.<a HREF="beans/api/chemaxon/struc/MoleculeGraph.html#calcDehydrogenizedGrinv(int[])">calcDehydrogenizedGrinv</a>,
	    <a HREF="beans/api/chemaxon/struc/MoleculeGraph.html#implicitizeHydrogens(int)">implicitizeHydrogens</a>,
	    MolAtom.<a HREF="beans/api/chemaxon/struc/MolAtom.html#isImplicitizableH(int)">isImplicitizableH</a>.
	    </li>
	<li>The protected removeNode(..., boolean changeNodes) and
	    removeEdge(..., boolean changeNodes) methods in CGraph,
	    MoleculeGraph, Molecule, etc. are replaced by
	    <a HREF="beans/api/chemaxon/struc/Molecule.html#removeNode(int, int)">removeNode(int i, int cleanupFlags)</a>,
	    <a HREF="beans/api/chemaxon/struc/Molecule.html#removeNode(chemaxon.struc.CNode, int)">removeNode(CNode node, int cleanupFlags)</a>,
	    <a HREF="beans/api/chemaxon/struc/Molecule.html#removeEdge(int, int)">removeEdge(int i, int cleanupFlags)</a>,
	    <a HREF="beans/api/chemaxon/struc/Molecule.html#removeEdge(chemaxon.struc.CEdge, int)">removeEdge(CEdge edge, int cleanupFlags)</a>.
	    </li>
	<li>Removed the malfunctioning RxnMolecule(parent) and
	    RgMolecule(parent) constructors.</li>
	</ul>
	</li>
    </ul>

<h3><a class="anchor" name="v3002"></a>March 21, 2003: Marvin 3.0.2</h3>
    <ul>
    <li><a class="text" NAME="v3002bugfixes">Bugfixes:</a>
	contracted S-groups are automatically expanded before exporting in
	molecule file formats (PDB, SMILES, XYZ, ...)
	</li>
    </ul>

<h3><a class="anchor" name="v3001"></a>March 17, 2003: Marvin 3.0.1</h3>
    <ul>
    <li><a NAME="v3001rgrxn" HREF="../formats/mol-csmol-doc.html">RGfile</a>
	import/export:
	root structure can be a reaction.<br>
	<small>Note that mixing RGfiles and Rxnfiles is not possible in
	a standard way so these files can only be imported by Marvin.</small>
	</li>
    <li><a NAME="v3001pdb" HREF="../formats/pdb-doc.html">PDB</a> import:
	HETATM records are recognized.</li>
    <li><a class="text" NAME="v3001bugfixes">Bugfixes:</a>
	Invisible atom symbols at image export in
	<a href="beans/api/chemaxon/struc/Molecule.html#toObject(java.lang.String)">
	Molecule.toObject(String)</a>, Sgroup export in MDL formats,
	Sgroup-related NullPointerException in viewer.
	</li>
    </ul>

<h3><a class="anchor" name="v3000"></a>February 10, 2003: Marvin 3.0</h3>
    <ul>
    <li>3D cleaning.</li>
    <li>New applet paremeters and bean properties
	<ul>
	<li><a href="viewman.html#parameters.detachable">detachable</a>
	    paremeter in the viewer.</li>
	<li><a href="sketchman.html#parameters.atomSymbolsVisible">
	    atomSymbolsVisible</a> parameter instead of <code>labels</code>
	    which became deprecated.</li>
	</ul>
	</li>
    <li>API improvements and simplifications:
	<ul>
	<li>chemaxon.struc:
	    <ul>
	    <li><a NAME="v3000unsync"
		   HREF="beans/api/chemaxon/struc/CGraph.html">CGraph</a> and
		<a HREF="beans/api/chemaxon/struc/Molecule.html">Molecule</a>
		are not thread safe any more but their methods became 20-30%
		faster.</li>
	    <li>Created the
		<a HREF="beans/api/chemaxon/struc/SelectionMolecule.html">SelectionMolecule</a>
		class for molecule selections, removed the CGraph.selection field.
		</li>
	    <li>Created the
		<a HREF="beans/api/chemaxon/struc/MoleculeGraph.html">MoleculeGraph</a>
		class as the direct ancestor of SelectionMolecule and Molecule.
		</li>
	    <li><a class="text" NAME="v3000rg_and_rxn">RgMolecule and RxnMolecule</a>
		changes:
		<ul>
		<li>Nodes and edges are only stored in subgraphs (root structure,
		    R-groups, product/reactant/agent structures).</li>
		<li>The graph union is returned by the
		    <a HREF="beans/api/chemaxon/struc/MoleculeGraph.html#getGraphUnion()">MoleculeGraph.getGraphUnion()</a>
		    method.
		    </li>
		<li>Methods like getNodeCount(), getEdgeCount(), getNode(i),
		    getEdge(i) call the root structure's corresponding
		    method in RgMolecule or the graph union's method in
		    RxnMolecule.</li>
		</ul>
		</li>
	    <li>CGraph.findFrags() and CGraph.findFrags(boolean) are replaced by
		CGraph.<a HREF="beans/api/chemaxon/struc/CGraph.html#findFrags(java.lang.Class)">findFrags(Class)</a>
		and
		Molecule.<a HREF="beans/api/chemaxon/struc/Molecule.html#findFrags()">findFrags()</a>
		</li>
	    <li>CGraph.cloneNodes() and cloneEdges() renamed to
		<a HREF="beans/api/chemaxon/struc/CGraph.html#getNodeVector()">getNodeVector</a>,
		<a HREF="beans/api/chemaxon/struc/CGraph.html#getEdgeVector()">getEdgeVector</a>.
		</li>
	    <li>CGraph.reallyContains(CEdge) and countRealEdges() removed.</li>
	    <li><a HREF="beans/api/chemaxon/struc/CGraph.html#getSSSR()">CGraph.getSSSR()</a>
		returns an int[][] array instead of CEdge[][]</li>
	    <li>The MolAtom.flags, MolAtom.countFlags and MolBond.flags fields
		became private.</li>
	    </ul>
	    </li>
	<li>Molecule import/export:
	    <ul>
	    <li><a HREF="beans/api/chemaxon/marvin/io/MolExportModule.html">MolExportModule</a>.<a HREF="beans/api/chemaxon/marvin/io/MolExportModule.html#convert(chemaxon.struc.Molecule)">convert(Molecule)</a>
		and
		<a HREF="beans/api/chemaxon/marvin/io/MolExportModule.html#close()">close()</a>
		throw
		<a HREF="beans/api/chemaxon/marvin/io/MolExportException.html">MolExportException</a>
		instead of IOException.</li>
	    <li><a HREF="beans/api/chemaxon/formats/MolImporter.html">MolImporter</a>.<a HREF="beans/api/chemaxon/formats/MolImporter.html#read()">read()</a>
		and <a HREF="beans/api/chemaxon/formats/MolImporter.html#importMol(byte[])">importMol(byte[])</a>
		returns RgMolecule object only if R-groups are present.</li>
	    </ul>
	    </li>
	<li>Initialization file saving:
	    <ul>
	    <li>MarvinSketch and MarvinView has a common initialization file
		in the $HOME/.chemaxon subdirectory.</li>
	    <li>MarvinPane.saveIni(), loadIni(), getIniFile() and setIniFile()
		are removed and replaced by
		<a HREF="beans/api/chemaxon/marvin/common/UserSettings.html">UserSettings</a>.<a HREF="beans/api/chemaxon/marvin/common/UserSettings.html#save(java.lang.String)">save(String)</a>,
		<a HREF="beans/api/chemaxon/marvin/common/UserSettings.html">UserSettings</a>.<a HREF="beans/api/chemaxon/marvin/common/UserSettings.html#tryToLoad()">tryToLoad()</a>,
		and
		<a HREF="beans/api/chemaxon/util/DotfileUtil.html">DotfileUtil</a>.<a HREF="beans/api/chemaxon/util/DotfileUtil.html#setDotDirName(java.lang.String)">setDotDirName(String)</a>.
		</li>
	    </ul>
	    </li>
	<li>Other bean related changes
	    <ul>
	    <li>Buttons and checkboxes in molecule <a href="viewman.html#advanced.cell">cells</a> can fire
		<em>java.awt.event.ActionEvent</em>s and
		<em>java.awt.event.ItemEvent</em>s as
		<em>javax.swing.AbstractButton</em>
		objects.
	    <li><a href="beans/api/chemaxon/marvin/beans/MViewPane.html#getCanvasComponent(int)">MViewPane.getCanvasComponent</a> is deprecated. Use
		<a href="beans/api/chemaxon/marvin/beans/MViewPane.html#getVisibleCellComponent(int)">MViewPane.getVisibleCellComponent</a>.
		</li>
	    </ul>
	    </li>
	</ul>
	</li>
    <li>Template button title is specified using the <em>abbreviation</em>
	SDF property instead of <em>title</em>.</li>
    <li>Bugfix: in Rxnfile import</li>
    </ul>

<h3><a class="anchor" name="v2105"></a>September 13, 2002: Marvin 2.10.5</h3>
    <ul>
	<li><a href="sketchman.html#parameters.atomMappingVisible">atomMappingVisible</a>
	is a new applet/bean parameter and property.</li>
	<li>Bugfixes:
	    <em>Copy</em> and <em>Copy as smiles</em> bugs under Mac.
	    Hotkey bug in swing version of Marvin applets at Java 1.4.</li>
    </ul>

<h3><a class="anchor" name="v2104"></a>August 12, 2002: Marvin 2.10.4</h3>
    <ul>
	<li><a href="sketchman.html#skin">skin</a> applet/bean parameter and new menu option (only in SWING).</li>
	<li>Improved SD file import (DTn fields).</li>
        <li>Bugfix:
	    NoSuchMethodError at drawing R-group definition in AWT mode (if Java version is earlier
		than 1.2).</li>
    </ul>

<h3><a class="anchor" name="v2103"></a>August 2, 2002: Marvin 2.10.3</h3>
    <ul>
	<li><a href="sketchman.html#parameters.grinvVisible">grinv</a> is a
	new applet/bean parameter and property.
	<li>Bugfix: in SDfile import.</li>
    </ul>

<h3><a class="anchor" name="v2102"></a>July 26, 2002: Marvin 2.10.2</h3>
    <ul>
	<li>Bugfixes:
	    <ul>
		<li>Improved printing layout of MView tables.</li>
		<li>Improvements in Copy / Paste of reactions.</li>
		<li>Fixed calculation of molecule bounds.</li>
	    </ul></li>
    </ul>

<h3><a class="anchor" name="v2101"></a>July 19, 2002: Marvin 2.10.1</h3>
    <ul>
	<li>Molweight calculation.</li>
	<li>Handling agents of reactions.</li>
	<li>Bugfix:
	    <code>MolConvert.convert()</code> cleans imported SMILES.
	    </li>
    </ul>

<h3><a class="anchor" name="v2913"></a>July 5, 2002: Marvin 2.10 (2.9.13)</h3>
    <ul>
	<li><a href="applets/signing.html">Signed</a> Marvin applets</li>
	    <ul>
		<li>Open, save, cut and paste can be used with signed
		    applets. <a href="index.html#examples">Examples</a>
		    can be launched by selecting "<em>Signed applets</em>".
		    </li>
	    </ul>
	<li>Marvin applications with
	    <a href="../../examples/webstart/example-jws-intro.html">Java Web Start</a>
	    <ul>
		<li><a href="index.html#javawebstart">Examples</a> for using
		    the MarvinSketch and the MarvinView applications via
		    Internet.
		    </li>
	    </ul>
    <li>New applet/bean parameters</li>
	<ul>
	<li>
	<a HREF="sketchman.html#parameters.valenceErrorVisible">valenceErrorVisible</a>
	applet/bean parameter and property in MarvinSketch. Its value can be also modified from
	menu: <strong>View</strong>/<strong>Misc</strong>/<strong>Valence&nbsp;Errors</strong>.</li>
	<li>
	<a HREF="sketchman.html#parameters.reactionErrorVisible">reactionErrorVisible</a>
	applet/bean parameter and propert in MarvinSketch. It can also be
	set from menu: <strong>View</strong>/<strong>Misc</strong>/<strong>Reaction&nbsp;Errors</strong>.</li>
	</ul>
    </ul>

<h3><a class="anchor" name="v2912"></a>April 23, 2002: Marvin 2.9.12</h3>
    <ul>
    <li>Import/Export
	<ul>
	<li><a HREF="../formats/cml-doc.html">CML</a> import and better export.</li>
	<li>Atom map support for <a HREF="../formats/mol-csmol-doc.html">Rxnfiles</a>
	    and reaction <a HREF="../formats/smiles-doc.html">SMILES</a>.
	    </li>
	<li>Atom label font size and double bond width can be set for image
	    export using the <a HREF="../formats/images-doc.html#atsiz">atsiz</a> and
	    <a HREF="../formats/images-doc.html#bondw">bondw</a> options.</li>
	<li>If an error occurs while reading a multi-molecule file (an SDfile
	    for example), the remaining parts of the current molecule can be
	    skipped and the file pointer can be positioned to the next one.
	    <ul>
	    <li><a HREF="beans/api/chemaxon/marvin/io/MolImportIface.html">MolImportIface</a>
		interface changed, the
		<a HREF="beans/api/chemaxon/marvin/io/MolImportIface.html#skipToNext()">skipToNext()</a>
		method introduced
		</li>
	    <li>MolImporter.skipToNext()</li>
	    </ul>
	    </li>
	</ul>
	</li>
    <li>Stereochemistry
	<ul>
	<li><a HREF="sketchman.html#parameters.chiralitySupport">chiralitySupport</a>
	    applet/bean parameter</li>
	<li><a HREF="sketchman.html#parameters.ezVisible">ezVisible</a>
	    applet/bean parameter</li>
	</ul>
	</li>
    <li>Other new applet/bean parameters and properties
	<ul>
	<li><a HREF="sketchman.html#propchanges">popupMenusEnabled</a>
	    bean property</li>
	<li><a HREF="sketchman.html#parameters.atomNumbersVisible">atomNumbersVisible</a>
	    applet/bean parameter</li>
	</ul>
	</li>
    <li>Other API changes
	<ul>
	<li>The <code>MolAtom.SELECTED</code> and <code>VALENCE_ERROR</code> constants are not public any
	    more, they are replaced by the
	    <a HREF="beans/api/chemaxon/struc/MolAtom.html#isSelected()">isSelected</a>,
	    <a HREF="beans/api/chemaxon/struc/MolAtom.html#setSelected(boolean)">setSelected</a>,
	    <a HREF="beans/api/chemaxon/struc/MolAtom.html#hasValenceError()">hasValenceError</a>
	    and
	    <a HREF="beans/api/chemaxon/struc/MolAtom.html#setValenceError(boolean)">setValenceError</a>
	    methods.
	    </li>
	<li>deprecated methods removed: <code>MolAtom.getAbbrev()</code> and <code>abbrevOf()</code>
	    replaced by <code>getSymbol()</code> and <code>symbolOf()</code></li>
	<li><code>CNode.copy(CNode)</code>, <code>MolAtom.copy(CNode)</code> does not copy the edges,
	    <code>CNode.clone()</code> became abstract, <code>MolAtom.clone()</code> does not clone the
	    edges.</li>
	</ul>
	</li>
    <li>Fixed: memory leak and printing problems with Java Plugin, atom lists
	in molfile, bugs during reaction drawing, MarvinView stdin
	reading, etc.</li>
    </ul>
<h3><a class="anchor" name="v2911"></a>February 18, 2002: Marvin 2.9.11</h3>
    <ul>
    <li>SMARTS improvements: &quot;a&quot; (aromatic) and &quot;A&quot;
	(aliphatic) atom primitives recognized even outside of brackets,
	&quot;:&quot; (aromatic bond) symbol recognized at file format
	recognition.
    <li>New <a HREF="sketchman.html#parameters.importConv">importConv</a>
	option (c) to automatically clean up the molecule.</li>
    <li>Image export improvements
	<ul>
	<li><a HREF="../formats/images-doc.html#scale">scale</a> option</li>
	<li><a HREF="beans/api/chemaxon/struc/Molecule.html#getImageSize(java.lang.String)">getImageSize</a>
	    method</li>
	</ul>
	</li>
    <li><a HREF="sketchman.html#parameters.bondSpacing">bondWidth</a> applet
	parameter and setBondWidth bean method.</li>
    <li>API changes
	<ul>
	<li>Molecule.toFormat, toBinFormat, toObject, MolExportModule.open and
	    MolExportModule.parseOption throw IllegalArgumentException instead
	    of IOException if bad format string was specified</li>
	<li>&quot;mag&quot; property renamed to &quot;scale&quot;,
	    setMag, getMag, maxMag methods became deprecated (MSketchPane,
	    MSketch, JMSketch, MolPrinter) and replaced by setScale, getScale,
	    maxScale</li>
	</ul>
	</li>
    <li>Fixed: delayed printing, image export option parsing, cml:A, etc.</li>
    </ul>
<h3><a class="anchor" name="v2910"></a>January 29, 2002: Marvin 2.9.10</h3>
    <ul>
    <li>Occassional NullPointerException in MolInputStream.canBeJTF() fixed.</li>
    </ul>
<h3>January 28, 2002: Marvin 2.9.9</h3>
    <ul>
    <li>Extra templates can be specified using the
	<a HREF="sketchman.html#parameters.xtmpls">xtmpls</a> parameter.</li>
    <li>New <a HREF="sketchman.html#parameters.importConv">importConv</a>
	option (H) to add/remove explicit Hydrogen atoms.</li>
    <li>Common aromatization and explicit Hydrogen addition/removal options for
	molecule export modules: a, +a, -a, H, +H, -H</li>
    <li>Incompatibilities:
	<ul>
	<li>In PdbExport, Hydrogens are added with the common &quot;H&quot; option
	instead of &quot;h&quot;.</li>
	<li>MolExportModule.getOptions() became protected</li>
	</ul>
	</li>
    </ul>

<h3>December 20, 2001: Marvin 2.9.8</h3>
    <ul>
    <li>Apache Batik 1.1.1 classes needed for SVG export are included with
	Marvin Beans and Applications</li>
    </ul>

<h3>December 19, 2001: Marvin 2.9.7</h3>
    <ul>
    <li>Image export:
	<ul>
	<li><a HREF="../formats/svg-doc.html">Scalable Vector Graphics</a> (svg) format.</li>
	<li><a NAME="v2907H" HREF="../formats/images-doc.html#H_off">Implicit
	    Hydrogen display options</a>
	    can be specified for the
	    <a HREF="../applications/molconvert.html">MolConverter</a> utility and the
	    <a HREF="beans/api/chemaxon/struc/Molecule.html#toObject(java.lang.String)">toObject</a>
	    and
	    <a HREF="beans/api/chemaxon/struc/Molecule.html#toBinFormat(java.lang.String)">toBinFormat</a>
	    methods.</li>
	</ul>
	</li>
    <li>MViewPane.applyRotationMatrix, applyRotationMatrixes: methods to
	apply the viewing transformation on the atomic
	coordinates appearing in the molfile</li>
    </ul>

<h3>November 26, 2001: Marvin 2.9.6</h3>
    <ul>
    <li>No autoscale is the default in the sketcher.</li>
    </ul>

<h3>November 12, 2001: Marvin 2.9.3</h3>
    <ul>
    <li>MView applet: atom sets can be selected for different coloring, using
	the <a HREF="viewman.html#parameters.atomSet">set<em>N</em>.<em>M</em></a>
	parameters.</li>
	<li>MolPanel changes:
	<ul>
		<li>autoScale property was added, it can be accessed via
		<em>setAutoScale()</em> and <em>getAutoScale()</em> methods.
		</li>
	</ul>
	<li>SketchPanel improvements:
	<ul>
		<li>
		Autoscale is avaible both in AWT and Swing versions.</li>
		<li>
		depending on the autoScale property automatic scaling
		is done when loading molecules</li>
		<li>
		The <em>select all</em> function can be triggered by Ctrl+A. </li>
		<li> Bug fixes.
	</ul>
	</li>

    </ul>

<h3>October 13, 2001: Marvin 2.9.2</h3>
    <ul>
    <li>New applet and bean methods:
	<ul>
	<li><code>getTabScale(i)</code> to query the magnification in a cell</li>
	<li><code>getBestTabScale(i)</code> to query the best magnification value for a
	    cell</li>
	<li><code>getBestTabScale()</code> to query the smallest best scale value</li>
	</ul>
	</li>
    <li>MViewPane bean only:
	<ul>
	<li><a HREF="viewman.html#parameters.autoTabScale">autoTabScale</a>
	    property enables automatic uniform scaling of cells.</li>
	</ul>
	</li>
    </ul>

<h3>September 21, 2001: Marvin 2.9.1</h3>
    <ul>
    <li><a NAME="v2901mview">MarvinView improvements</a>
	<ul>
	<li>JavaScript code can be called at image click in the applet
	    using the &quot;js:<em>javascript</em>&quot; syntax.</li>
	<li>Use <a HREF="viewman.html#parameters.tabScale">tabScale</a> to set
	    the magnification in the molecule cells (instead of automatic
	    scaling), use
	    <a HREF="viewman.html#parameters.winScale">winScale</a> to set
	    the magnification in the zoom windows (same as the old <code>mag</code>
	    parameter).</li>
	<li>Many molecules in one cell. Molecules coming from multi-molecule
	    files are merged into one molecule object containing
	    <em>atom sets</em> if MULTISET is specified as the first import
	    option in the <a HREF="viewman.html#parameters.mol">mol</a>
	    parameter. To display only the specified sets, use the
	    <a HREF="viewman.html#parameters.showSets">showSets</a> applet
	    parameter, the
	    MView.setSetVisible()
	    applet method or the MarvinPane.setInvisibleSets bean method.</li>
	<li>Atom set colors can be changed using the
	    <a HREF="viewman.html#parameters.atomSetColor">setColor</a> applet
	    parameters, the
	    MView.setSetColor()
	    applet method or the <code>MarvinPane.setSetColor()</code> bean method.
	    </li>
	<li>&quot;<a HREF="shapely-scheme.html">Shapely</a>&quot; and
	    &quot;Group&quot; color schemes (for PDB input).</li>
	</ul>
	</li>
    <li>Class library changes
	<ul>
	<li>MULTISET can also be passed to the MolImporter constructor
	    through the option string</li>
	<li>The <code>newInstance()</code> method of CGraph, Molecule, etc.
	    became public.
	    </li>
	</ul>
    <li><a NAME="v2901pdb" HREF="../formats/pdb-doc.html">PDB</a>
	<ul>
	<li>Residue symbol is imported for standard residues.</li>
	<li>Residue sequence number is imported.</li>
	<li>New export and import options.</li>
	<li>Export does not hydrogenize the molecule automatically, use the
	    <strong>h</strong> option for this.</li>
	</ul>
	</li>
    </ul>

<h3>August 20, 2001: Marvin 2.9</h3>
    <ul>
    <li><a HREF="../formats/mol-csmol-doc.html">Rxnfile, RDfile</a> and
	Reaction <a HREF="../formats/smiles-doc.html">SMILES</a> import/export</li>
    <li><code>Molecule.getComment()</code> and <code>setComment()</code> methods</li>
    <li>New MarvinSketch applet parameter and bean methods:
	<a HREF="sketchman.html#parameters.reactionSupport">reactionSupport</a>,
	<code>getReactionSupport()</code>, <code>setReactionSupport()</code>.</li>
    <li><a HREF="beans/api/chemaxon/marvin/io/MolExportModule.html">MolExportModule</a>
	API changed: the
	<a HREF="beans/api/chemaxon/marvin/io/MolExportModule.html#open(java.lang.String)">open</a>
	method has a return value</li>
    </ul>

<h3>June 25, 2001: Marvin 2.8.4</h3>
    <ul>
    <li>Default value of the &quot;format&quot; property is &quot;DEFAULT&quot;
	instead of null</li>
    </ul>

<h3>June 22, 2001: Marvin 2.8.3</h3>
    <ul>
    <li><a NAME="v2803ini">Beans:</a>
	<ul>
	<li>List of properties to save/load to/from the initialization file
	    (<code>~/.mview</code>, <code>~/.msketch</code>) can be set using the setIniProps method.</li>
	<li>Beans can be used in applets that cannot access to disk files:
	    <ul>
	    <li>Ini file is not loaded
		automatically, only when <code>sketchPane.loadIni()</code> or
		<code>viewPane.loadIni()</code> is called.</li>
	    <li>Ini file is not saved automatically, only if the
		value of the saveIniEnabled property is true.</li>
	    </ul>
	    </li>
	</ul>
	</li>
    <li>The dispopts applet parameter is deprecated, use
	<a HREF="viewman.html#parameters.implicitH">implicitH</a>,
	<a HREF="viewman.html#parameters.explicitH">explicitH</a> or
	<a HREF="viewman.html#parameters.navmode">navmode</a> instead.
	</li>
    </ul>

<h3>June 18, 2001: Marvin 2.8.1</h3>
    <ul>
    <li>New bean methods and applet parameters for customizing 3D rendering:
	<a HREF="viewman.html#3d.stickThickness">stickThickness</a>,
	<a HREF="viewman.html#3d.ballRadius">ballRadius</a>.
	</li>
    <li><a NAME="v2801image">java.awt.Image</a> objects can be created from
	molecules using
	mol.<a HREF="beans/api/chemaxon/struc/Molecule.html#toObject(java.lang.String)">toObject</a>("image");
	</li>
    <li><a HREF="../formats/formats.html#ioapi">API</a> for import module
	creation (MolImportIface).</li>
    <li>Import file format can be specified, by writing
	"filename(format:)" or "filename(format:options)" in the
	<a HREF="viewman.html#parameters.mol">mol</a> applet parameter,
	or in the command line of <a HREF="../applications/molconvert.html">molconvert</a>,
	<a HREF="../applications/msketch.html">msketch</a>
	or <a HREF="../applications/mview.html">mview</a>.
	</li>
    <li>MSketchPane: <code>setMol(File, String)</code> method added, where string can
	contain the import options, in one of the following forms:
	"format:options", "format:", "options". The old <code>setMol(File)</code> method
	removed. MViewPane: <code>setM(int, File, String)</code> added.</li>
    <li><code>MolExportModule.sbuf</code> field renamed to <code>stringBuffer</code>.</li>
    <li><code>MolAtom.getAbbrev()</code> and <code>abbrevOf</code> are deprecated, use <code>getSymbol()</code> and
	<code>symbolOf()</code> instead.</li>
    </ul>

<h3>June 12, 2001: Marvin 2.8</h3>
    <ul>
    <li>New 3D <a HREF="viewman.html#3d.rendering">rendering mode</a>: sticks.
	</li>
    <li>Display quality can be controlled with an
	<a HREF="viewman.html#parameters.dispQuality">applet parameter</a>.
	</li>
    <li>Molecule import/export:
	<ul>
	<li>New XYZ import options:
	    <a HREF="../formats/xyz-doc.html#option_f">bond length cut-off</a> (f...) and
	    <a HREF="../formats/xyz-doc.html#option_Z">maximum number of connections</a>
	    (C4,H1,Cl1)</li>
	<li><a HREF="viewman.html#parameters.importConv">Automatic
	    dearomatization</a> after molecule loading.</li>
	<li>All export modules recognize the
	    <a HREF="../formats/basic-export-opts.html">&quot;a&quot;,
	    &quot;+a&quot; (aromatize) and &quot;-a&quot; (dearomatize)</a>
	    options.
	    Example: &quot;<a HREF="../formats/smiles-doc.html">smiles</a>:a&quot;
	    </li>
	</ul>
	</li>
    <li>Class library, beans
	<ul>
	<li><code>Molecule.aromatize()</code></li>
	<li>Bugfix: <code>MViewPane.getCellCount()</code> returns the total number of cells,
	    not just the number of visible cells.
	    </li>
	<li>PropertyChangeEvents generated when the molecule is changed in the
	    <a HREF="sketchman.html#propchanges.mol">sketcher</a> or
	    <a HREF="viewman.html#propchanges.mols">viewer</a> component.</li>
	<li>Event handling fix: PropertyChangeEvent source is the
	    the bean component, not the internal SketchPanel or ViewPanel.</li>
	</ul>
	</li>
    </ul>

<h3>May 17, 2001: Marvin 2.7.11</h3>
    <ul>
    <li>New <a HREF="viewman.html#3d.rendering">rendering</a> style:
	&quot;ball &amp; stick&quot;.</li>
    <li>Class library, beans:
	<ul>
	<li>New <a HREF="../formats/images-doc.html">image export options</a>:
	    rendering style and color scheme.</li>
	<li>Image export options must be separated by commas in format
	    descriptor strings.</li>
	<li>Molecule.toBinFormat method to convert molecules into binary
	    formats such as JPEG/PNG/PPM.</li>
	<li>The type of the <code>rendering</code>, <code>colorScheme</code>,
	    <code>implicitH</code>, <code>downWedge</code>, <code>navmode</code>
	    properties in
	    <a HREF="viewman.html#propchanges">PropertyChange events</a>
	    is <code>String</code> just like the argument type of their
	    <code>set</code> methods.
	    (Previously, the two types did not match.)
	    </li>
	</ul>
	</li>
    </ul>
<h3>May 4, 2001: Marvin 2.7.9</h3>
    <ul>
    <li>Binary export formats: <a HREF="../formats/gzip-doc.html">GZIP</a>,
	<a HREF="../formats/jpeg-doc.html">JPEG</a>, <a HREF="../formats/png-doc.html">PNG</a>,
	<a HREF="../formats/ppm-doc.html">PPM</a></li>
    <li>User defined export module creation by overriding
	<code>chemaxon/marvin/util/MolExportModule</code>.</li>
    </ul>

<h3>April 1, 2001: Marvin 2.7.6</h3>
    <ul>
    <li>Text field (<code>T</code>) component for MView
	<a HREF="viewman.html#advanced">tables</a>.</li>
    <li><a HREF="viewman.html#parameters.cacheMols">cacheMols</a> applet
	parameter for caching loaded molecules.</li>
    <li><a HREF="viewman.html#parameters.loadMols">loadMols</a> applet
	parameter for preloading molecules.</li>
    <li><code>Molecule.getProperty(key)</code> is not case sensitive.</li>
    </ul>

<h3>March 27, 2001: Marvin 2.7.5:</h3> 
Optional <a HREF="viewman.html#3d.spin">spin vector</a>.

<h3>March 14, 2001: Marvin 2.7</h3>
    <ul>
    <li>File formats
	<ul>
	<li><em>New:</em> <a HREF="../formats/xyz-doc.html#import">XYZ import</a></li>
	<li><em>New:</em> <a HREF="../formats/mol-csmol-doc.html#molV3">Extended Molfile
	    (V3.0) import.</a></li>
	<li><a HREF="../formats/cml-doc.html">CML export</a> options changed,
	    cml:1 -&gt; cml, cml:0 -&gt; cml:A, cml:A writes three coordinates
	    for each atom even if the molecule is 2D
	    (so <em>Jmol</em> can read the file)
	    </li>
	</ul>
	</li>
    <li>MView applet:
	<ul>
	<li><strong>Animated</strong> XYZ files can be displayed</li>
	<li>New parameters:
	    <a HREF="viewman.html#3d.animFPS">animFPS</a>,
	    <a HREF="viewman.html#3d.animDelay">animDelay</a>,
	    <a HREF="viewman.html#3d.animSync">animSync</a>.
	    </li>
	</ul>
	</li>
    <li>MarvinSketch Applet/Bean:
	<ul>
	<li><em>New:</em> <strong>File</strong>/<strong>New</strong> menu item in the bean.
	    If your application using the bean has a
	    <strong>File</strong>/<strong>New&nbsp;Window</strong>
	    menu item, then <strong>File</strong>/<strong>New</strong> should be moved to
	    <strong>File</strong>/<strong>New</strong>/<strong>Clear&nbsp;current</strong>, and
	    <strong>File</strong>/<strong>New&nbsp;Window</strong> should be moved to
	    <strong>File</strong>/<strong>New</strong>/<strong>New&nbsp;Window</strong>. See the MarvinSketch
	    application source
	    (<code>marvinbeans/examples/sketch/chemaxon/marvin/Sketch.java</code>)
	    for details.
	    </li>
	<li>&quot;Visual fragment placement&quot; can be disabled: use the
	    simpView parameter or the
	    <strong>View</strong>/<strong>Misc</strong>/<strong>Object&nbsp;in&nbsp;Hand</strong>
	    option if you do not like to see what you are doing.
	    </li>
	<li><a HREF="#2607bondlen">Bond length related changes.</a>
	</ul>
	</li>
    <li>Class library:
	<ul>
	<li>Changes related to atomic coordinates and transformations
	    <ul>
	    <li><em>New:</em> CTransform3D class for general 3D transformations
	    (like in Java3D)</li>
	    <li>Molecule.rot(...), tra(...), magnify(...) removed, use
	    transform(CTransform3D T) instead</li>
	    <li>Atom coordinates became absolute</li>
	    <li><code>Molecule.getO()</code> and <code>setO()</code> replaced by <code>getLocation()</code> and
	    <code>setLocation()</code></li>
	    <li><code>Molecule.getCenter()</code> renamed to <code>calcCenter()</code>, and it
		calculates center of mass instead of geometrical center</li>
	    <li><em>New:</em><code>Molecule.moveTo(location)</code></li>
	    </ul>
	    </li>
	<li><code>CGraph.indexOf</code> changes
	    <ul>
	    <li>Splitted into two methods, <code>indexOf(CNode atom)</code>
		and <code>indexOf(CEdge bond)</code></li>
	    <li>Speed improvement.
		CNode and CEdge has an index field that contains the object's
		index in its parent graph.
		Calling the parent graph's indexOf method returns the value
		of this field. The index is only searched (in the old way) when
		the CGraph is not the direct parent.</li>
	    </ul>
	    </li>
	<li>Valence rules
	    <ul>
	    <li><em>New:</em> <code>MolAtom.ionChargeOf(Z)</code></li>
	    <li><code>MolAtom.negoxOf(Z, boolean)</code> removed, <code>negOxOf(Z)</code>, <code>posOxOf(Z)</code>
		created instead</li>
	    </ul>
	    </li>
	<li>SDF properties
	    <ul>
	    <li>&quot;name&quot; and &quot;informat&quot; are no longer stored
		in the properties hashtable, they became fields of Molecule
		</li>
	    <li><code>Molecule.getProperties()</code> removed</li>
	    <li><code>Molecule.clearForImport()</code> -&gt; <code>clearForImport(name)</code></li>
	    <li><em>New</em> methods in Molecule:
		getName, setName, getInputFormat, setInputFormat,
		getPropertyCount(), getPropertyKey(n), getPropertyKeys()</li>
	    </ul>
	    </li>
	<li><a HREF="#2607bondlen">Bond length related changes.</a>
	</ul>
	</li>
    <li><a NAME="2607bondlen">API simplification:</a>
	internal bond length of Marvin fixed to be MolBond.CCLENGTH
	<ul>
	<li>bondlen, rescaling applet/bean parameters, setBondlen, getBondlen,
	    setRescaling, getRescaling methods removed, use
	    <code>MSketch.getMol(&quot;<a HREF="../formats/mol-csmol-doc.html#options">mol:b=XXX</a>&quot;)</code>
	    instead of
	    &lt;param NAME=&quot;bondlen&quot; VALUE=&quot;XXX&quot;&gt;</li>
	<li>The bondlen argument of <code>Molecule.clean(dim, bondlen, opts)</code> removed,
	    use <code>clean(dim, opts)</code></li>
	<li><code>MolBond.DEFAULT_LENGTH</code> renamed to <code>MolBond.CCLENGTH</code></li>
	<li><code>MolBond.getDesiredLengthRatio()</code> removed, use the new
	    dimension dependent <code>Molecule.getDesiredLength(bond)</code> method instead
	    </li>
	<li><code>MolAtom.covalentRadiusOf(Z)</code> -&gt; <code>covalentRadiusOf(Z, bondOrder)</code></li>
	</ul>
	</li>
    </ul>

<h3>December 10, 2000: Marvin 2.6</h3>
    <ul>
    <li>The &quot;style&quot; applet parameter renamed to &quot;
	<a HREF="sketchman.html#3d.rendering">rendering</a> because of
	a conflict with a Netscape keyword. The getStyle, setStyle methods
	of the beans are also renamed to getRendering, setRendering.
	</li>
    <li>New <a HREF="sketchman.html#3d.rendering">rendering</a> style:
	&quot;Wireframe with Knobs&quot;.</li>
    <li>Atoms can be highlighted in the MView/JMView applets by specifying a
	<a HREF="viewman.html#parameters.selection">selection</a>.</li>
    <li>All query atoms and extra bond types can be enabled by omitting the
	<a HREF="sketchman.html#parameters.queryAtoms"><code>queryAtoms</code></a>,
	<a HREF="sketchman.html#parameters.atomStrings"><code>atomStrings</code></a>,
	and <a HREF="sketchman.html#parameters.extraBonds"><code>extraBonds</code></a>
	parameters.
	</li>
    <li>&quot;Cis or Unspecified&quot; and &quot;Trans or Unspecified&quot;
	bond types; &quot;ctu&quot; in
	<a HREF="sketchman.html#parameters.extraBonds"><code>extraBonds</code></a>.
	</li>
    <li>Cleaning options can be specified using the
	<a HREF="sketchman.html#parameters.cleanOpts"><code>cleanOpts</code></a>
	applet parameter.
	</li>
    <li>Optional down wedge bond orientation (MDL or Daylight) using the
	<a HREF="sketchman.html#parameters.downWedge"><code>downWedge</code></a>
	parameter.</li>
    <li>New MView/JMView applet methods:
	<ul>
	<li><code>getAtomCount(moleculeIndex)</code> to query the number of atoms
	    in a molecule cell</li>
	<li><code>isSelected(moleculeIndex, atomIndex)</code> to query whether the specified
	    atom is selected or not</li>
	<li><code>selectAtom(moleculeIndex, atomIndex, select)</code> to (un)select
	    an atom</li>
	<li><code>selectAllAtoms(moleculeIndex, select)</code> to (un)select all
	    atoms</li>
	</ul>
	</li>
    <li>Real scrolling in MView!
	<ul>
	<li>The applet is scrollable if
	    <a HREF="viewman.html#advanced.visibleRows">visibleRows</a>
	    is less than
	    <a HREF="viewman.html#advanced.rows">rows</a> or
	    <a HREF="viewman.html#advanced.visibleCols">visibleCols</a>
	    is less than
	    <a HREF="viewman.html#advanced.cols">cols</a>.
	    </li>
	<li>The names of the layout<em>i</em> and param<em>i</em>
	    <strong>parameters changed</strong> to
	    <a HREF="viewman.html#advanced.layout"><code>layout</code></a> and
	    <a HREF="viewman.html#advanced.param"><code>param</code></a>
	    for the molecule cells, <code>layoutH</code> and <code>paramH</code>
	    for the optional header.
	    </li>
	<li>The <code>scroll</code> parameter ceased to exist.</li>
	</ul>
	</li>
    <li>Class library:
	<ul>
	<li>RgMolecule for R-groups</li>
	<li>Thread safe API for molecules</li>
	<li>New methods in CGraph: <code>getCtab()</code>, <code>getBtab()</code>, <code>fragments()</code>,
	    <code>findFrag()</code>, <code>getFragIds()</code>, <code>getLock()</code>, <code>getParent()</code>, etc.</li>
	<li>New methods in CNode: <code>getLigand(i)</code>, <code>getEdge(i)</code>,
	    <code>getEdgeCount()</code>
	    </li>
	<li><code>CNode.findFrag()</code> and <code>CGraph.fuseFrag()</code> removed</li>
	<li><code>CGraph.getEdge(i)</code>, <code>CGraph.getNode(i)</code>, <code>Molecule.getAtomArray()</code>
	    created;
	    <code>CGraph.getEdges()</code>, <code>CGraph.getNodes()</code>, <code>Molecule.getAtoms()</code>,
	    <code>Molecule.getBonds()</code> removed</li>
	<li><code>Molecule.reuseAtom(Z, i)</code> and <code>Molecule.endReuse(na)</code> created,
	    <code>MolAtom.reuse(Z, mol, i)</code> removed</li>
	<li>The two-argument <code>MolAtom.setCharge</code> method removed. Instead of
	    <code>a.setCharge(charge, false)</code>, use <code>a.setCharge(charge);
	    a.setFlags(0, MolAtom.FIX_CHARGE);</code></li>
	</ul>
	</li>
    </ul>

<h3>July 31, 2000: Marvin 2.5</h3>
    <ul>
    <li><a HREF="../formats/smiles-doc.html">SMILES</a> import</li>
    <li>Class library:
	<ul>
	<li><code>CGraph.fuseFrag(CNode a)</code> has only one argument.</li>
	<li>CGraph.removeNode and removeEdge instead of remove and removeRef;
	    removeEdge leaves edge.node1 and edge.node2 unchanged
	    (they were set to <code>null</code> in removeRef).</li>
	<li><code>MolBond.setFlags(int flags, int mask)</code> method introduced.</li>
	<li>Stereochemistry: double bonds can also be UP/DOWN
	    (not just CIS/TRANS);
	    CIS and TRANS information can also be stored (not just EITHER).
	    The MolBond.getStereo and setStereo methods and the UP, DOWN,
	    EITHER constants ceased to exists, the
	    STEREO1_UP, STEREO1_DOWN,
	    STEREO1_EITHER, STEREO2_CIS, STEREO2_TRANS,
	    STEREO2_EITHER, STEREO_CARE flags introduced instead.</li>
	<li>MolBond.getTopology/setTopology ceased to exist, the
	    TOPOLOGY_RING, TOPOLOGY_CHAIN flags introduced instead.</li>
	</ul>
	</li>
    </ul>

<h3>June 21, 2000: Marvin 2.4.2</h3>
    <ul>
    <li><a NAME="v2402scroll">Scrolling</a> in the MView and JMView applets.
	See the <a HREF="viewman.html#advanced.visibleRows">visibleRows</a>
	parameter.</li>
    </ul>

<h3>May 22, 2000: Marvin 2.4</h3>
    <ul>
    <li><a NAME="v2400colors">Structures are colorful by default.</a>
	Colorization can be disabled by setting <code>colors=mono</code>,
	both in the
	<a HREF="sketchman.html#parameters.colorScheme">sketcher</a> and the
	<a HREF="viewman.html#parameters.colorScheme">viewer</a>.</li>
    <li>Spacefill rendering can be enabled instead of wireframe by using
	the <a HREF="viewman.html#3d.rendering">style</a> parameter.</li>
    <li>Atom labels can be hidden with the <code>labels</code> parameter.</li>
    <li>Up/down and cis/trans either
	<a HREF="sketchman.html#parameters.extraBonds">bonds</a></li>
    <li>Classes in <code>chemaxon/beans</code> moved to
	<code>chemaxon/marvin/beans</code>. The MarvinSketch bean is
	<code>chemaxon.marvin.beans.MSketchPane</code>, the MarvinView bean is
	<code>chemaxon.marvin.beans.MViewPane</code>.</li>
    </ul>

<h3>March 20, 2000: Marvin 2.3.1</h3>
    The basic applet tag for MSketch and MView is simplified,
    and also generalized to work with or without Sun's Java Plugin.

<h3>March 4, 2000: Marvin 2.3</h3>
    <ul>
    <li>New file format: <a HREF="../formats/pdb-doc.html">PDB</a> (for simple, non-macro
	molecules)</li>
    <li><a NAME="v2300tmpls">Changes</a> in
	<a HREF="sketchman.html#parameters.tmpls">template</a> handling
	<ul>
	<li>The four template sets that come with Marvin became default.
	    Templates only have to be specified as applet parameter if
	    you need a different template set.</li>
	<li>Templates moved to directory <code>chemaxon/marvin/templates/</code>
	    (from <code>templates/</code>).</li>
	<li>File extension changed to <code>.t</code> (from <code>.cssdf</code>).<br>
	    This change was required because of a Netscape restriction
	    related to the loading of JAR file resources.
	<li>The JAR files marvin.jar and jmarvin.jar contain
	    <code>chemaxon/marvin/templates/generic.t</code>, so the
	    <font COLOR="red">firewall</font> problem ceased for the
	    generic template set.<br>
	    If needed, the additional templates may also be put into the
	    JARs by the user with the JAR utility
	    (part of <a HREF="http://java.sun.com/jdk/">Sun's JDK</a>).
	    However, doing this increases the JAR size with a noticable
	    amount.
	</ul>
	</li>
    <li>The <code>MolImport</code> module moved into marvin.jar because of the
	firewall problem.</li>
    </ul>
<h3>January 15, 2000: Marvin 2.2</h3>
    <ol>
    <li>New
	<a HREF="sketchman.html#parameters.extraBonds">query bond types</a>:
	&quot;single or double&quot;, &quot;single or aromatic&quot;,
	&quot;double or aromatic&quot;, &quot;chain&quot; and
	&quot;ring&quot;.</li>
    <li>SMARTS export handles the heteroatom (Q in molfile) type
	intelligently.</li>
    <li>The heteroatom and the any atom types are specified in the
	<a HREF="sketchman.html#parameters.queryAtoms">queryAtoms</a> parameter.
	The <code>anyatom</code> parameter became deprecated but is
	still present for backward compatibility, with slightly changed
	behavior. Specifying <code>anyatom</code> with any value
	(empty string also) is equivalent to specifying
	&quot;<code>any,hetero</code>&quot; in <code>queryAtoms</code>.</li>
    <li>Cell selection in MarvinView can be controlled using the
	<a HREF="viewman.html#parameters.selectable">selectable</a>
	parameter.</li>
    <li>New methods in MarvinSketch:
	<code>getBondlen</code>.
	<code>getMag</code>,
	<code>getPiece</code>,
	<code>getRescaling</code>.
	New method in MarvinView:
	<code>setSelectedIndex</code>,
	The <code>setDispopts</code> method ceased to exist.
	Display options can be set using the dispopts applet parameter only.
	</li>
    <li><a NAME="v2200noclasses">In Marvin 2.2 and later, the Java classes come
	only in packaged forms:</a> marvin.jar and marvin.zip.
	It means that support is ceased for those archaic Java-compatible
	browsers that cannot handle the <code>ARCHIVE</code> option in the applet
	tag (Netscape 2 and MS Explorer 3).
	Reasons:
	<ol>
	<li>Under certain circumstances, some Netscape versions try to load the
	class files one by one instead of using <code>marvin.jar</code>. The
	simplest way to keep Netscape from doing so is to remove the class
	files.</li>
	<li>Most of our users don't understand what <code>ARCHIVE</code> is, and
	don't use it properly. The consequence is a significantly increased
	download time.<br>
	From now, a missing <code>ARCHIVE</code> option cannot be left unnoticed,
	because Marvin will simply not run. Much better than turning your
	user's hair gray.</li>
	<li>Marvin is rarely tested in the oldest (and buggiest) browsers.</li>
	</ol>
	<p><small>
	If you are absolutely sure that you want your Marvin page work with
	Netscape 2 and MS Explorer 3 too, you must unpack marvin.zip manually.
	However, we do not recommend this, because you will probably do
	something wrong. For example, once you will upgrade Marvin and
	forget to unpack the new marvin.zip. Mixing two different versions
	will result in malfunction.
	</small></p>
	</li>
    </ol>

<h3>October 4, 1999: Marvin 2.1.5:</h3> Molecules in MarvinView are only editable if the
    <a HREF="viewman.html#parameters.editable">editable applet parameter</a>
    is set.
<h3>September 14, 1999: Marvin 2.1.1</h3>
    <ul>
    <li>getCellCount()
	method in MarvinView.</li>
    <li>The <em>anchor</em> parameter in the
	<a HREF="viewman.html#advanced.layout">layout</a> also sets label
	alignment.</li>
    </ul>

<h3>September 2, 1999: Marvin 2.1</h3>
    <ol>
    <li>CML (XML) export.</li>
    <li>The
	getSelectedIndex()
	method in MarvinView.
	</li>
    <li>Default value of the <code>rescaling</code>
	parameter in MarvinSketch is changed to &quot;<code>mag</code>&quot;
	because generally this value is the most useful.</li>
    <li>The MarvinView-only package ceased to exist, the new Edit/Structure
	function made it irrelevant.</li>
    </ol>

<h3>July 20, 1999: Marvin 2.0</h3>
    <ol>
    <li><a NAME="marvin2000templates"><strong>Templates in MarvinSketch:</strong></a>
	From now on templates are stored in SDfiles (.sdf) or
	compressed SDfiles (.cssdf).
	The template file can be optionally compressed with GZIP, but the
	applet will only work in Java 1.1 compatible browsers in that case.
	The <code>tmplmol</code>, <code>tmplpar</code>, and <code>ntmpls</code> parameters
	<strong>no longer exist</strong> and are <strong>replaced by</strong> the much more easy
	to use <a HREF="sketchman.html#parameters.tmpls">tmpls</a> parameters.
	The unit rotation angle can be specified as a new Marvin-specific
	property <code>M  MRV PHI</code> in the template molecule files
	(instead of the <code>tmplpar</code> parameters).
	</li>
    <li><strong>Applet tags changed:</strong>
	Rewrite your applet tags according to the
	<em>last solution</em> in <em>MarvinSketch Example 1</em>.
	This applet tag can load the AWT and the Swing applets as well.
	<br>
	Note that the file <code>&amp;{marvinjar};</code> is not packaged now
	(its name caused too many problems), so you must rewrite your applet
	tags, otherwise the download time with MS Explorer will not be
	optimal.
	</li>
    <li><strong>New in MarvinSketch:</strong>
	The <a HREF="sketchman.html#parameters.menubar">menubar</a> parameter
	enables or disables the menu bar in the Swing sketcher.
	</li>
    <li><a NAME="marvin2000animate"><strong>New in MarvinView:</strong></a>
	Molecules start rotating in 3D automatically if you set the
	<a HREF="viewman.html#3d.animate">animate</a>
	parameter in MarvinView.
	</li>
    <li><a NAME="marvin2000border"><strong>New in MarvinView:</strong></a>
	<a HREF="viewman.html#advanced.border">Border</a> between cells in
	molecule tables.
	</li>
    <li><strong>Change in MarvinView:</strong>
	From now the colors can be specified in the same way as in
	MarvinSketch.
	Molecule background is <a HREF="viewman.html#parameters.molbg">molbg</a>,
	normal table background is
	<a HREF="viewman.html#parameters.background">bgcolor</a>.
	(Previously, bgcolor was also the molecule background.)
	</li>
    <li><a NAME="marvin2000javascripter"><strong>JavaScripter</strong></a>
	applet ceased to exist.<br>
	Its functionality is moved into a MarvinView module that is
	automatically loaded at the first <code>js:</code> action.<br>
	<blockquote>
	<small>
	In the past, JavaScripter was required because scripting applets are
	not cached by older browsers (namely Netscape 3). An applet that has
	the MAYSCRIPT attribute is loaded from the net for each page that
	contains it. In Netscape 4 and later, there is no such problem. On the
	other hand, a bug (or feature?) in Netscape 4.6 makes the use of
	JavaScripter impossible as a separate applet. That's why we merged the
	two applets now.
	</small>
	</blockquote>
	</li>
    <li><a NAME="marvin2000formats"><strong>New:</strong></a>
	Three new molecule file formats:
	<a HREF="../formats/sybyl-doc.html">&quot;sybyl&quot;</a>
	(the <em>SybylImport</em> and <em>SybylExport</em> modules),
	<a HREF="../formats/xyz-doc.html">&quot;xyz&quot;</a> (<em>XyzExport</em>),
	and <a HREF="../formats/pov-doc.html">&quot;pov&quot;</a> (<em>PovExport</em>).
	</li>
    <li><strong>New and changed:</strong>
	The &quot;<code>implicitH</code>&quot; option in the dispopts applet
	parameter instead of the <code>showH</code> parameter which ceased to exist.
	<ul>
	<li>&quot;<code>off</code>&quot; -&gt; &quot;<code>implicitH=off</code>&quot;</li>
	<li>&quot;<code>term</code>&quot; -&gt; &quot;<code>implicitH=heteroterm</code>&quot;
	    (because &quot;<code>term</code>&quot; ceased to exist)</li>
	<li>&quot;<code>hetero</code>&quot; -&gt;
	    &quot;<code>implicitH=hetero</code>&quot;</li>
	<li>&quot;<code>heteroterm</code>&quot; -&gt;
	    &quot;<code>implicitH=heteroterm</code>&quot; (default)</li>
	<li>&quot;<code>all</code>&quot; -&gt;
	    &quot;<code>implicitH=all</code>&quot;</li>
	</ul>
	New features: if you want the explicit H atoms to be invisible, use
	&quot;<code>explicitH=off</code>&quot; (default: &quot;<code>all</code>&quot;).
	<br>
	If you have 3D molecules, use &quot;dim=3&quot; for MarvinView, to
	let him know that you want 3D rotation as the default mouse drag
	action.
	</li>
    </ol>

<h3>January 4, 1999: Marvin 1.3</h3>
    <ol>
    <li><strong>New:</strong>
	The <a HREF="sketchman.html#parameters.atomStrings">atomStrings</a>
	parameter enables the usage of atom aliases and SMARTS strings in the
	sketcher.
	</li>
    <li><strong>Incompatible change:</strong>
	The <code>setAtoms</code>, <code>setNoatoms</code>,
	<code>setNobonds</code> methods, and the <code>atoms</code>,
	<code>noatoms</code>, <code>nobonds</code> 
	applet parameters ceased to exist and replaced by
	<a HREF="sketchman.html#parameters.elements"><code>elements</code></a>,
	<code>anyatom</code>,
	<a HREF="sketchman.html#parameters.queryAtoms"><code>queryAtoms</code></a>,
	and
	<a HREF="sketchman.html#parameters.extraBonds"><code>extraBonds</code></a>.
	</li>
    <li><strong>New:</strong>
	The new <a HREF="sketchman.html#parameters.detach">detach</a>
	parameter makes it possible to detach the sketcher from the web page
	immediately when the applet is loaded.
	</li>
    <li><strong>Recommended:</strong>
	In molfiles and compressed molfiles written directly in HTML, you
	should end all the molfile lines with backslash <strong>and</strong> also a real
	newline character like here:
	<blockquote>
	<pre>
...
000Wun+V70\
iuzV000W60\
iuzVGS2W60\
K72WGS2W60\
...
	</pre>
	</blockquote>
	Omitting the real newlines like in
	&quot;<code>000Wun+V70\iuzV000W60\iuzVGS2W60\K72WGS2W60\</code>&quot;,
	seemed to be safe so far, while the
	Marvin applets were tested only in Sun, Netscape, Symantec, and
	Microsoft JVM's.
	The reason for discontinuing this practice is that the
	AppletViewer in Kaffe 1.0b3
	has problems if you write more than one molfile lines in one HTML line.
	</li>
    <li><strong>Removed:</strong>
	Backward compatibility with the old style (1.0-1.1) usage of
	<code>getMol()</code> and <code>getM()</code> removed.
	It didn't work with MSIE anyway.
	</li>
    </ol>

<h3>November 29, 1998: Marvin 1.2</h3>
    <ol>
    <li><strong>Incompatible change:</strong>
	MSketch.getMol(x) and
	MView.getM(n,x).
	<p>
	In Marvin 1.1.4 and before, <code>x</code> was a boolean variable
	specifying the format of the generated molfile: MDL mol
	(<code>false</code>) or compressed mol (<code>true</code>).
	<p>
	From version 1.2, <code>x</code> is a <em>format descriptor string</em>
	to ease the introduction of new molecule file formats as Marvin evolves.
	MDL molfile format is denoted by the string
	&quot;<code>mol</code>&quot;,
	compressed mol is &quot;<code>csmol</code>&quot;,
	SMILES is &quot;<code>smiles</code>&quot;.
	</li>
    <li><strong>New:</strong>
	The <a HREF="sketchman.html#parameters.preload">preload</a>
	applet parameter.
	<p>
	&quot;Extra features&quot; are planned to be available as external
	modules in future releases of Marvin.
	External modules are downloaded only when needed.
	In Marvin 1.2.3, MDL mol, compressed mol and SMILES export are
	external modules.
	<p>
	The problem is that older browsers such as Netscape 3, cannot
	load the external modules when the module is needed by a public method
	which is called from JavaScript.
	So if you want your web page containing Marvin to work perfectly with
	earlier releases of Netscape than 4, then you should preload all
	the modules which may be needed by JavaScript calls.
	<p>
	For example, if you are using
	<code>MSketch.getMol(&quot;smiles&quot;)</code> on your page,
	then you should also have the following applet parameter:<br>
	<code>&lt;param NAME=&quot;preload&quot; VALUE=&quot;SmilesExport&quot;&gt;</code>.
	</li>
    </ol>

</body>
</html>
