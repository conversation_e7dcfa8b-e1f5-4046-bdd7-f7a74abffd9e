<html>
<head>
<meta NAME="author" CONTENT="Tamas Vertse">
<link REL ="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css" TITLE="Style">
<title>Marvin Resources</title>
</head>
<body>
<h1>Marvin Resources</h1>
<p>This document contains the full reference of the used jar files with the latest Marvin version.

<h4>Mandatory JAR files</h4>
<table CELLSPACING=0 CELLPADDING=4 BORDER=0 class="grid" width="600">
    <tr>
        <th width="50%">Jar file name</th>
        <th>Description</th>
    </tr>
    <tr>
        <td>aloe.jar</td>
        <td>Swing Extension Package</td>
    </tr>
    <tr>
        <td>forms-1.1.0.jar</td>
        <td>JGoodies Form Layout</td>
    </tr>
</table>

<h4>Mandatory JAR files of MarvinSpace</h4>

<table CELLSPACING=0 CELLPADDING=4 BORDER=0 class="grid" width="600">
    <tr>
        <th width="50%">Jar file name</th>
        <th>Description</th>
    </tr>
    <tr>
        <td>jextexp.jar</td>
        <td>DualThumbSlider component of GNF</td>
    </tr>
    <tr>
        <td>gluegen-rt.jar</td>
        <td>Used by JOGL, a Java interface to OpenGL</td>
    </tr>
    <tr>
        <td>jogl.jar</td>
        <td>Used by JOGL, a Java interface to OpenGL</td>
    </tr>
    <tr>
        <td>jogl_1.1.0-rc2/gluegen-rt-natives-*.jar</td>
        <td>Used by JOGL, a Java interface to OpenGL</td>
    </tr>
    <tr>
        <td>jogl_1.1.0-rc2/jogl-natives-*.jar</td>
        <td>Used by JOGL, a Java interface to OpenGL</td>
    </tr>
</table>

<h4>JAR files covering frequent functionalities</h4>
<table CELLSPACING=0 CELLPADDING=4 BORDER=0 class="grid" width="600">
    <tr>
        <th width="50%">Jar file name</th>
        <th>Description</th>
    </tr>
    <tr>
        <td>chart.jar</td>
        <td>Used by several Calculator Plugins</td>
    </tr>
    <tr>
        <td>jh.jar</td>
        <td>JavaHelp resource</td>
    </tr>
</table>

<h4>JAR files optionally needed for special functionalities</h4>
<table CELLSPACING=0 CELLPADDING=4 BORDER=0 class="grid" width="600">
    <tr>
        <th width="50%">Jar file name</th>
        <th>Description</th>
    </tr>
    <tr>
        <td>batik-core.jar</td>
        <td>Used for SVG Image export</td>
    </tr>
    <tr>
        <td>freehep-graphics2d-2.1.1.jar</td>
        <td>Used for EMF/PDF export</td>
    </tr>
    <tr>
        <td>freehep-graphicsio-2.1.1.jar</td>
        <td>Used for EMF/PDF export</td>
    </tr>
    <tr>
        <td>freehep-graphicsio-emf-2.1.1.jar</td>
        <td>Used for EMF export</td>
    </tr>
    <tr>
        <td>freehep-graphicsio-pdf-2.1.1.jar</td>
        <td>Used for PDF export</td>
    </tr>
    <tr>
        <td>freehep-io-2.0.2.jar</td>
        <td>Used for EMF/PDF export</td>
    </tr>
    <tr>
        <td>freehep-util-2.0.2.jar</td>
        <td>Used for EMF/PDF export</td>
    </tr>
    <tr>
        <td>inchi-native-*.jar</td>
        <td>Used for IUPAC inchi import/export</td>
    </tr>
    <tr>
        <td>jacob-1.15/jacob-1.15.jar</td>
        <td>Java-COM bridge used for various windows specific functionalities (e.g.: OLE, EMF generation)</td>
    </tr>
    <tr>
        <td>jacob-1.15/jacob-1.15-native-x86.jar</td>
        <td>32-bit native implementations for jacob</td>
    </tr>
    <tr>
        <td>jacob-1.15/jacob-1.15-native-x64.jar</td>
        <td>64-bit native implementations for jacob</td>
    </tr>
    <tr>
        <td>jnbtools.jar</td>
        <td>Required for JNI Bridge</td>
    </tr>
    <tr>
        <td>looks-2.1.4.jar</td>
        <td>Supported JGoodies Look&Feel</td>
    </tr>
    <tr>
        <td>registry/cxnregistry-x86.jar</td>
        <td>Natives for manipulating and query the windows registry thru jacob in 32 bit environments.</td>
    </tr>
    <tr>
        <td>registry/cxnregistry-x64.jar</td>
        <td>Natives for manipulating and query the windows registry thru jacob in 64 bit environments.</td>
    </tr>
</table>


</body>
</html>