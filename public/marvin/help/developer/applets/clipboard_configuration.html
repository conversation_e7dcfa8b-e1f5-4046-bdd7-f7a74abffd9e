<html>
<head>
<meta NAME="author" CONTENT="<PERSON><PERSON><PERSON>">
<link REL ="stylesheet" TYPE="text/css" HREF="../../marvinmanuals.css" TITLE="Style">
<title>Configuring copy/paste operations in applets.</title>
</head>
<body>
<h1>Configuring copy/paste operations in applets.</h1>
<p>
Using this configuration the copy/paste operations available in the applet can be controlled: the formats available for copy or paste can be manipulated, or the corresponding resources can be preloaded.<br/>
For example, the copy or paste of specific file formats can be forced to be immediately available after the start of the applet, so instead of loading the underlying architecture on demand, the required resources are loaded during the applet
initialization time.<br/>
In addition, the formats available on the clipboard after a simple copy operation can be configured, as well as the list of formats available in the Copy As dialog.
</p>
<p>
The configuration is available through a Java properties file which has to be placed to the
APPLET_CODEBASE/chemaxon/marvin/datatransfer.properties file. If this file is available in the 
applet codebase, the configuration will take effect.
</p>
<p>
This properties file contains settings for format keys. The property name format is as follows:
<code>formatkey.configuration.setting.id</code>, so for example, if somebody wants to disable the png
format on the copy as dialog, then the <code>png.use.dialog</code> key has to be set to false.
</p>
<p>
<h3>Available format keys in the property file, and the corresponding formats:</h3>
<table border="1px solid black">
<tr><th>Format key</th><th>The corresponding format</th></tr>
<tr><td>mrv</td><td>Marvin Document Format</td></tr>
<tr><td>skc</td><td>IsisDraw Sketch file format</td></tr>
<tr><td>cdx</td><td>ChemDraw Sketch file format</td></tr>
<tr><td>mol</td><td>MDL MOLfile format</td></tr>
<tr><td>rxn</td><td>MDL RXNfile format</td></tr>
<tr><td>smiles</td><td>Daylight SMILES format</td></tr>
<tr><td>smarts</td><td>Daylight SMARTS format</td></tr>
<tr><td>cxsmiles</td><td>Chemaxon extended SMILES format</td></tr>
<tr><td>cxsmarts</td><td>Chemaxon extended SMARTS format</td></tr>
<tr><td>name</td><td>IUPAC name format</td></tr>
<tr><td>trad_name</td><td>Traditional name format</td></tr>
<tr><td>inchi</td><td>IUPAC InChI format</td></tr>
<tr><td>inchikey</td><td>IUPAC InChIKey format</td></tr>
<tr><td>string</td><td>Molecule source, containing the actual file format of the molecule object</td></tr>
<tr><td>emf</td><td>Enchanced Metafile picture format (available only on windows platforms)</td></tr>
<tr><td>jpg</td><td>JPG image format</td></tr>
<tr><td>png</td><td>PNG image format</td></tr>
</table>
<h3>Available configurable properties for all formats and their meanings:</h3>
<table border="1px solid black">
<tr><th>Parameter name</th><th>Valid values</th><th>Meaning</th></tr>
<tr><td>name</td><td nowrap="true">can be any String</td><td>This will be displayed in the Copy As dialog to represent the format.</td></tr>
<tr><td>disabled</td><td>true/false</td><td>If set to false, then the format won't be available at all.</td></tr>
<tr><td>preload.copy</td><td>true/false</td><td>If set to true, then the classes needed for copying in the specified format will be preloaded at applet initialization.</td></tr>
<tr><td>preload.paste</td><td>true/false</td><td>If set to true, then the classes needed for pasting in the specified format will be preloaded at applet initialization.</td></tr>
<tr><td>preload</td><td>true/false</td><td>If set to true, then the classes needed for copying or pasting in the specified format will be preloaded at applet initialization.</td></tr>
<tr><td>use.default</td><td>true/false</td><td>If set to true, then the molecule will be placed to the clipboard by a simple copy operation in the specified format.</td></tr>
<tr><td>use.dialog</td><td>true/false</td><td>If set to false, then the specified format won't be available in the Copy As dialog.</td></tr>
</table>
By default, none of the formats are preloaded, all of them are configured as they worked before.
</p>
<p>
<h3>Other available options:</h3>
The following keys are not related to the formats, and should be used as they are:
<table border="1px solid black">
<tr><th>Property name</th><th>Valid values</th><th>Meaning</th></tr>
<tr><td>ole.enabled</td><td>true/false</td><td>If Microsoft OLE object copy should be disabled then set to false.</td></tr>
<tr><td>ole.name</td><td nowrap="true">can be any String</td><td>This will be displayed in the Copy as dialog to represent the Microsoft OLE object format.</td></tr>
<tr><td>separator</td><td>a comma separated list of format keys.</td><td>After the enumerated formats a separator will be placed in the copy as dialog.</td></tr>
</table>
</p>
<h3>The default configuration file</h3>
<code>
#define where to place separator in the copy as dialog.<br/>
#comma separated list, separators will be placed after the listed formats<br/>
separator = string<br/>
<br/>
#OLE related settings. Note: OLE is usable only on Windows platforms.<br/>
ole.name = Marvin Object (OLE)<br/>
ole.enabled = true<br/>
<br/>
#structure formats<br/>
mrv.name = Marvin Document (MRV)<br/>
mrv.disabled = false<br/>
mrv.preload.copy = false<br/>
mrv.preload.paste = false<br/>
mrv.use.default = true<br/>
mrv.use.dialog = true<br/>
<br/>
skc.name = ISIS (Symyx) file (SKC)<br/>
skc.disabled = false<br/>
skc.preload = false<br/>
skc.use.default = true<br/>
skc.use.dialog = true<br/>
<br/>
cdx.name = ChemDraw file (CDX)<br/>
cdx.disabled = false<br/>
cdx.preload = false<br/>
cdx.use.default = true<br/>
cdx.use.dialog = true<br/>
<br/>
mol.name = MDL MOLfile<br/>
mol.disabled = false<br/>
mol.preload.copy = false<br/>
mol.preload.paste = false<br/>
mol.use.default = true<br/>
mol.use.dialog = true<br/>
<br/>
rxn.name = MDL RXNfile<br/>
rxn.disabled = false<br/>
rxn.preload.copy = false<br/>
rxn.preload.paste = false<br/>
rxn.use.default = true<br/>
rxn.use.dialog = true<br/>
<br/>
smiles.name = Daylight SMILES<br/>
smiles.disabled = false<br/>
smiles.preload.copy = false<br/>
smiles.preload.paste = false<br/>
smiles.use.default = false<br/>
smiles.use.dialog = true<br/>
<br/>
smarts.name = Daylight SMARTS<br/>
smarts.disabled = false<br/>
smarts.preload.copy = false<br/>
smarts.preload.paste = false<br/>
smarts.use.default = false<br/>
smarts.use.dialog = true<br/>
<br/>
cxsmiles.name = ChemAxon SMILES (CXSMILES)<br/>
cxsmiles.disabled = false<br/>
cxsmiles.preload.copy = false<br/>
cxsmiles.preload.paste = false<br/>
cxsmiles.use.default = false<br/>
cxsmiles.use.dialog = true<br/>
<br/>
cxsmarts.name = ChemAxon SMARTS (CXSMILES)<br/>
cxsmarts.disabled = false<br/>
cxsmarts.preload.copy = false<br/>
cxsmarts.preload.paste = false<br/>
cxsmarts.use.default = false<br/>
cxsmarts.use.dialog = true<br/>
<br/>
name.name = Name<br/>
name.disabled = false<br/>
name.preload.copy = false<br/>
name.preload.paste = false<br/>
name.use.default = false<br/>
name.use.dialog = true<br/>
<br/>
inchi.name = InChI<br/>
inchi.disabled = false<br/>
inchi.preload = false<br/>
inchi.use.default = false<br/>
inchi.use.dialog = true<br/>
<br/>
inchikey.name = InChIKey<br/>
inchikey.disabled = false<br/>
inchikey.preload = false<br/>
inchikey.use.default = false<br/>
inchikey.use.dialog = true<br/>
<br/>
string.name = Molecule Source (Plain Text)<br/>
string.disabled = false<br/>
string.preload = false<br/>
string.use.default = true<br/>
string.use.dialog = true<br/>
<br/>
#image formats<br/>
#emf supported only on Windows platforms<br/>
emf.name = EMF Image<br/>
emf.disabled = false<br/>
emf.preload = false<br/>
emf.use.default = true<br/>
emf.use.dialog = true<br/>
<br/>
#default copy contains these formats because Java Image Flavor is used in default copy.<br/>
jpg.name = JPG Image<br/>
jpg.disabled = false<br/>
jpg.preload = false<br/>
jpg.use.default = false<br/>
jpg.use.dialog = true<br/>
<br/>
png.name = PNG Image<br/>
png.disabled = false<br/>
png.preload = false<br/>
png.use.default = false<br/>
png.use.dialog = true<br/>
</code>
<h3>Further format couplings</h3>
Please consider this information also while changing the configuration.
<ul>
<li>If OLE is enabled, then the OLE data will contain the mrv, the mol or rxn file, and the emf image also.</li>
<li>Default copy places image data to the clipboard, in java image format. If you want to disable this behaviour you have to set the <code>jimage.use.default</code> property to true.</li>
</ul>
</body>
</html>
