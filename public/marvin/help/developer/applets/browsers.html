<html>
<head>
<meta NAME="author" CONTENT="Tamas Vertse">
<link REL ="stylesheet" TYPE="text/css" HREF="../../marvinmanuals.css" TITLE="Style">
<title>Browser compatibility of <PERSON></title>
</head>
<body>

<h1 ALIGN=CENTER><a class="anchor" name="top">Browser compatibility of <PERSON></a></h1>
Marvin Applets are tested with several browsers on various platforms.
This document shows the recommended browsers for each operating system.
<ul>
<li><a href="#windows">Windows</a></li>
<li><a href="#linux">Linux</a></li>
<li><a href="#osx">OS X</a>
	<ul>
            <li><a href="#osx10_5">10.6 (<PERSON>)</a></li>
            <li><a href="#osx10_5">10.5 (<PERSON><PERSON>)</a></li>
	</ul>
</li>
<li><a href="#solaris">Solaris 10</a></li>
<!--<li><a href="#os9">OS 9</a></li>-->
<li><a href="#javascript">Java - JavaScript communication</a></li>    
</ul>
<center><div class="lenia">&nbsp;</div></center>

<h2><a class="anchor" name="windows">Windows</a></h2>
<blockquote>
    <table CELLSPACING=0 CELLPADDING=4 BORDER=0 class="grid" width="500">

    <tr align="center"><th width="350">&nbsp;Browser</th><th>Recommended</th></tr>
    <tr align="center">
        <td>&nbsp;Google Chrome&nbsp;</td><td><img src="../../images/yes.png"/></td>
    </tr>
    <tr align="center">
        <td>&nbsp;Firefox&nbsp;</td><td><img src="../../images/yes.png"/></td>
    </tr>
    <tr align="center">
        <td>&nbsp;Opera&nbsp;</td><td><img src="../../images/yes.png"/></td>
    </tr>
    <tr align="center"><td>&nbsp;Microsoft Internet Explorer&nbsp;</td><td><img src="../../images/yes.png"/></td></tr>
    </table>
</blockquote>

<h2><a class="anchor" name="linux">Linux</a></h2>
<blockquote>
    <table CELLSPACING=0 CELLPADDING=4 BORDER=0 class="grid" width="500">
    <tr align="center"><th width="350">&nbsp;Browser</th><th>Recommended</th></tr>
	<tr align="center">
		<td>&nbsp;Google Chrome&nbsp;</td><td><img src="../../images/yes.png"/></td>
	</tr>
    <tr align="center">
        <td>&nbsp;Firefox&nbsp;</td><td><img src="../../images/yes.png"/></td>
    </tr>
	<tr align="center">
		<td>&nbsp;Opera&nbsp;</td><td><img src="../../images/yes.png"/></td>
	</tr>
    </table>
</blockquote>

<h2><a class="anchor" name="osx">Machintosh OS X</a></h2>


<p>If your browser does not support OS X built-in Java plugin, you can use <a href="http://javaplugin.sourceforge.net/">JEP (Java Embedding Plugin)</a>.</p>

<h3><a class="anchor" name="osx10_6">10.6 (Snow Leopard)</a></h3>
<blockquote>
    <table CELLSPACING=0 CELLPADDING=4 BORDER=0 class="grid" width="500">
    <tr align="center"><th width="350">&nbsp;Browser</th><th>Recommended</th></tr>
    <tr align="center">
        <td>&nbsp;Safari&nbsp;</td>
        <td><img src="../../images/yes.png"/></td>
    </tr>
    <tr align="center">
        <td>&nbsp;Google Chrome&nbsp;</td>
        <td><img src="../../images/yes.png"/></td>
    </tr>
    <tr align="center">
        <td>&nbsp;Firefox&nbsp;</td>
        <td><img src="../../images/yes.png"/></td>
    </tr>
    </table>
</blockquote>

<h3><a class="anchor" name="osx10_5">10.5 (Leopard)</a></h3>
<blockquote>
    <table CELLSPACING=0 CELLPADDING=4 BORDER=0 class="grid" width="500">
    <tr align="center"><th width="350">&nbsp;Browser</th><th>Recommended</th></tr>
    <tr align="center">
        <td>&nbsp;Safari&nbsp;</td>
        <td><img src="../../images/yes.png"/></td>
    </tr>
    <tr align="center">
        <td>&nbsp;Firefox&nbsp;</td>
        <td><img src="../../images/yes.png"/></td>
    </tr>
    </table>
</blockquote>

<h2><a class="anchor" name="solaris">Solaris 10</a></h2>
<blockquote>
    <table CELLSPACING=0 CELLPADDING=4 BORDER=0 class="grid" width="500">
    <tr align="center"><th width="350">&nbsp;Browser</th><th>Recommended</th>
    <!--<th>&nbsp;</th>--></tr>
    <tr align="center"><td>&nbsp;Mozilla&nbsp;</td><td><img src="../../images/yes.png"/></td><!--<td>&nbsp;</td>--></tr>
    </table>
</blockquote>

<h2><a class="anchor" name="javascript">Java - JavaScript communication</a></h2>
In a few browsers, the Java - JavaScript communication is only partially supported.
<p>
<blockquote>
<table CELLSPACING=0 CELLPADDING=4 BORDER=0 id="colored-grid" width="500">
<tr ALIGN=CENTER><th>&nbsp;</th>
    <th>Calling Java<br>
	from JavaScript</th>
    <th>Calling JavaScript<br>
	from Java</th></tr>
<tr ALIGN=CENTER><td>Google Chrome</td>
    <td>yes</td>
    <td>yes (MAYSCRIPT)</td>
    </tr>
<tr ALIGN=CENTER><td>Firefox</td>
	<td>yes</td>
	<td>yes (MAYSCRIPT)</td>
	</tr>
</tr>
<tr ALIGN=CENTER><td>Opera</td>
	<td>yes</td>
	<td>yes (MAYSCRIPT)</td>
	</tr>
<tr ALIGN=CENTER><td>MS Explorer for Windows</td>
    <td>yes</td>
    <td>yes</td>
    </tr>
<tr ALIGN=CENTER><td>Safari</td>
    <td>yes</td>
    <td>yes (MAYSCRIPT)</td>
    </tr>

</table>
<p><small>1: Limitations under OS X.
<a href="#osx">(see Notes for Mac users)</a></small><br>
</blockquote>

</body>
</html>
