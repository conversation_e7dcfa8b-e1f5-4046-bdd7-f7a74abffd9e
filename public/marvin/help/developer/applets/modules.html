<html>
<head>
<meta NAME="author" CONTENT="Tamas Vertse">
<link REL="stylesheet" TYPE="text/css" HREF="../../marvinmanuals.css" TITLE="Style">
<title>Applet modules</title>
</head>
<body>
<h1 align=center>Applet modules</h1>
<table border="1">
<tr><td>reactionquerydrawing</td><td>Draw reaction query</td></tr>
<tr><td>importexport</td><td>Core classes for import / export modules</td></tr>
<tr><td>license</td><td>License manager</td></tr>
<tr><td>drawing</td><td>Drawable components</td></tr>
<tr><td>util</td><td>Frequently used utility classes</td></tr>
<tr><td>concurrent</td><td>Concurrent processing</td></tr>
<tr><td>logging</td><td>Extra classes for logging</td></tr>
<tr><td>view</td><td>MarvinView GUI core classes</td></tr>
<tr><td>sketchstart</td><td>Core classes that are called by MarvinSketch startup</td></tr>
<tr><td>lwsketchmenu</td><td>Classes to load menu in MarvinSketch from file</td></tr>
<tr><td>sketchmenu</td><td>Classes to load menu in MarvinSketch without file</td></tr>
<tr><td>sketchmenubg</td><td>Extra classes for menu in MarvinSketch</td></tr>
<tr><td>sketchbackground</td><td>Menu actions and other resources that are not required immediately by MarvinSketch startup</td></tr>
<tr><td>sketch</td><td>Core MarvinSketch classe</td></tr>
<tr><td>smarts</td><td>Smarts module</td></tr>
<tr><td>nullexport</td><td>Dummy Null export module</td></tr>
<tr><td>smilesimport</td><td>SMILES import</td></tr>
<tr><td>molimport</td><td>MDL mol format import</td></tr>
<tr><td>sybylexport</td><td>Tripos Sybyl export</td></tr>
<tr><td>sybylimport</td><td>Tripos Sybyl import</td></tr>
<tr><td>mol2export</td><td>Tripos Mol2 export</td></tr>
<tr><td>mol2import</td><td>Tripos Mol2 import</td></tr>
<tr><td>mrvexport</td><td>MRV export</td></tr>
<tr><td>cmlexport</td><td>CML export</td></tr>
<tr><td>cdximportexport</td><td>CDX import/export</td></tr>
<tr><td>gzipexport</td><td>Gzip export</td></tr>
<tr><td>gzipimport</td><td>Gzip import</td></tr>
<tr><td>base64export</td><td>Base64 export</td></tr>
<tr><td>base64import</td><td>Base64 import</td></tr>
<tr><td>xyzexport</td><td>XYZ export</td></tr>
<tr><td>inchi</td><td>IUPAC InChi import/export</td></tr>
<tr><td>hydrogenize</td><td>Hydrogenize module</td></tr>
<tr><td>pdbexport</td><td>PDB export</td></tr>
<tr><td>spacefill</td><td>Spacefill module</td></tr>
<tr><td>atomlabellers</td><td>Module for drawing atom labels</td></tr>
<tr><td>superatomsgroupcoords</td><td>Superatom Sgroup Coordinates module</td></tr>
<tr><td>threedim</td><td>3D utility functions</td></tr>
<tr><td>xyzimport</td><td>XYZ import</td></tr>
<tr><td>goutimport</td><td>Gout import</td></tr>
<tr><td>gjfexport</td><td>Gjf export</td></tr>
<tr><td>charmmimport</td><td>CharmmGrid import</td></tr>
<tr><td>pdbimport</td><td>PDB import</td></tr>
<tr><td>jpegexport</td><td>Jpeg export</td></tr>
<tr><td>msbmpexport</td><td>MS BMP image export</td></tr>
<tr><td>pngexport</td><td>PNG image export</td></tr>
<tr><td>ppmexport</td><td>PPM image export</td></tr>
<tr><td>povexport</td><td>POV image export</td></tr>
<tr><td>svgexport</td><td>SVG graphics export</td></tr>
<tr><td>vectgraphicsexport</td><td>PDF grapics export and old EMF graphics export</td></tr>
<tr><td>imageexport</td><td>common classes for various image export modules</td></tr>
<tr><td>documenthandling</td><td>Document handling</td></tr>
<tr><td>mchart</td><td>MChart module</td></tr>
<tr><td>abbrevgroups</td><td>Abbreviation groups dialog</td></tr>
<tr><td>reaxysgenerics</td><td>Generic groups dialog</td></tr>
<tr><td>abbrevgroupexport</td><td>Abbreviationgroup export</td></tr>
<tr><td>clean2d</td><td>Clean 2D</td></tr>
<tr><td>clean3d</td><td>Clean 3D</td></tr>
<tr><td>modelling</td><td>Modelling package</td></tr>
<tr><td>atommapper</td><td>Mapping atoms</td></tr>
<tr><td>singlelinerecordreader</td><td>SingleLine  record reader</td></tr>
<tr><td>nameexport</td><td>IUPAC name export</td></tr>
<tr><td>nameimport</td><td>IUPAC name import</td></tr>
<tr><td>peptidevmnimport</td><td>Peptide and VMN import</td></tr>
<tr><td>peptideexport</td><td>Peptide export</td></tr>
<tr><td>skc</td><td>Skc file format import/export</td></tr>
<tr><td>common.swing.aboutjvm</td><td>About dialog</td></tr>
<tr><td>common.swing.editmolfileframe</td><td>EditMolfile frame</td></tr>
<tr><td>common.swing.exceptionframe</td><td>Exception frame</td></tr>
<tr><td>common.swing.help</td><td>Help dialog</td></tr>
<tr><td>common.swing.loadsave</td><td>Load/save dialog</td></tr>
<tr><td>common.swing.print</td><td>Print module</td></tr>
<tr><td>common.swing.regenbonds</td><td>Regenerate bonds</td></tr>
<tr><td>common.swing.preferences</td><td>Preferences dialog</td></tr>
<tr><td>common.swing.fontediting</td><td>Font attributes panel</td></tr>
<tr><td>sketch.edit</td><td>Utility for S-groups</td></tr>
<tr><td>sketch.linesm</td><td>Polyline drawing mode</td></tr>
<tr><td>sketch.boxsm</td><td>Rectangle drawing</td></tr>
<tr><td>sketch.bracketsm</td><td>Bracket drawing</td></tr>
<tr><td>sketch.swing.periodicdialog</td><td>Periodic system dialog</td></tr>
<tr><td>sketch.swing.rlogicdialog</td><td>Rlogic dialog</td></tr>
<tr><td>sketch.swing.groupcreationdialog</td><td>Create group dialog</td></tr>
<tr><td>sketch.swing.attachdatadialog</td><td>Attach data dialog</td></tr>
<tr><td>sketch.swing.textboxeditor</td><td>Text box editor</td></tr>
<tr><td>sketch.swing.templates</td><td>Handling templates</td></tr>
<tr><td>sketch.swing.mobjectpropertiesdialog</td><td>Graphical object properties dialog</td></tr>
<tr><td>sketch.swing.guiconfigurationdialog</td><td>Menu configuration dialog</td></tr>
<tr><td>view.scripter</td><td>Helper class for handling JavaScript invokes</td></tr>
<tr><td>view.swing.viewdnd</td><td>Drag and drop in MarvinView</td></tr>
<tr><td>view.swing.imageviewframe</td><td>Image viewer frame</td></tr>
<tr><td>view.swing.mviewanimator</td><td>Molecule rotation in MarvinView</td></tr>
<tr><td>view.swing.mviewframe</td><td>Popup MarvinView frame</td></tr>
<tr><td>view.swing.mviewsketch</td><td>Popup MarvinSketch frame from MarvinView</td></tr>
<tr><td>view.swing.printview</td><td>Additional resource for print in MarvinView</td></tr>
<tr><td>view.swing.molsframe</td><td>Display multiple molecules in a separate frame</td></tr>
<tr><td>view.swing.tableview</td><td>Spreadsheet table renderer</td></tr>
<tr><td>view.swing.gridbagview</td><td>Moleculematrix table renderer</td></tr>
<tr><td>view.swing.find</td><td>Find dialog</td></tr>
<tr><td>view.swing.showconformers</td><td>Display conformers</td></tr>
<tr><td>Plugin</td><td>Core and GUI classes for calculator plugins</td></tr>
<tr><td>plugins.Alignment</td><td>Alignment plugin</td></tr>
<tr><td>plugins.CalculationsCore</td><td>Core classes needed for calculator plugins</td></tr>
<tr><td>plugins.Protonation</td><td>Protonations plugins</td></tr>
<tr><td>plugins.Partitioning</td><td>Partitioning plugins</td></tr>
<tr><td>plugins.Tautomers</td><td>Tautomer plugins</td></tr>
<tr><td>plugins.Analysers</td><td>Analyser plugins</td></tr>
<tr><td>plugins.MolecularModelling</td><td>Plugins for molecular modelling</td></tr>
<tr><td>plugins.MarkushEnumeration</td><td>Markush enumeration plugin</td></tr>
<tr><td>plugins.Other</td><td>Other plugins</td></tr>
<tr><td>checkers</td><td>Various structure checkers</td></tr>
<tr><td>propertyeditor</td><td>Classes for editing atom/bond/molecule properties</td></tr>
<tr><td>jep</td><td>jep package</td></tr>
<tr><td>icons</td><td>Icons mainly for the menus</td></tr>
</body>
</html>
