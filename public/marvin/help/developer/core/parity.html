<!DOCTYPE html><HTML>

 <head>
  <script type="text/javascript" src="../../../examples/sh_main.js"></script>
  <script type="text/javascript" src="../../../examples/sh_java.js"></script>
  <script type="text/javascript" src="../../../examples/sh_javascript.js">
    </script>
  <link REL ="stylesheet" TYPE="text/css" 
        HREF="../../../examples/sh_nedit.css" TITLE="Style">
  <META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
  <title> Parity  </title>
  <link rel=stylesheet type="text/css" href="../../marvinmanuals.css">
 </head>

<BODY onload="sh_highlightDocument();">
<h1>Parity</h1> 
<p>In case of tetrahedral parity the ligands around the stereocenter are 
numbered with 1, 2, 3, and 4 in the order of increasing atom index 
(position in the atom block). Hydrogen atom (if any) is considered to be the 
highest indexed atom (atom 4 regardless of its  position in the  atom block).  
The  center  is  viewed from a position such that the bond connecting the 
highest-numbered atom (atom 4) projects behind the plane formed by atoms 
1, 2, and 3. Viewing through the plane  (1  2  3)  towards  atom number  4,  
the three remaining atoms can be arranged in either a clockwise or 
counterclockwise direction in ascending numerical order. 
If the atoms arranged in clockwise direction then the parity value is 
ODD otherwise, if they are arranged in counterclockwise direction then 
EVEN parity value is assigned. This is the parity definition also used by MDL. 
</p>

<div style="text-align: center; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table align="center" cellspacing="0" cellpadding="0" border="0" style="border: none; border-spacing:0px;">
<tr style="text-align:left;vertical-align:top;">
<td valign="top" width="219" style="width:219px; height:88px;">
<p style="text-align: center;">
<img src="images/parity_1.gif" width="100" height="100" border="0" 
     style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="top" width="219" style="width:219px; height:88px;">
<p style="text-align: center;">
<img src="images/parity_2.gif" width="100" height="100" border="0" 
     style="margin:0 auto;margin:0px;"></p>
</td>
</tr>
<tr style="text-align:left;vertical-align:top;">
<td valign="top" width="219" style="width:219px;"><p style="text-align: center;">parity ODD</p>
</td>
<td valign="top" width="219" style="width:219px;"><p style="text-align: center;">parity EVEN</p>
</td>
</tr>
</table>
</div>

<p>An atom belongs to the tetrahedral parity class if: 
<ul>
<li>Implicit and explicit hydrogen count is less than two. </li>
<li>The number of ligands plus the implicit hydrogen count equals four. 
Exceptions: <ul>
 <li>Sulfur atom can have a lone electron pair to allow chiral sulfoxides. </li>
 <li>Nitrogen has parity if it's a bridgehead atom or is in a 3 membered ring and the if the graph invariant is different for all three ligands. </li> 
<div style="text-align: center; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table align="center" cellspacing="0" cellpadding="0" border="0" style="border: none; border-spacing:0px;">
<tr style="text-align:left;vertical-align:top;">
<td valign="top" width="219" style="width:219px; height:88px;">
<p style="text-align: center;">
<img src="images/parity_3.png" width="150" height="150" border="0" 
     style="margin:0 auto;margin:0px;"></p>
</td>
<td valign="top" width="219" style="width:219px; height:88px;">
<p style="text-align: center;">
<img src="images/Nbridge.png" width="250" height="200" border="0" 
     style="margin:0 auto;margin:0px;"></p>
</td>
</tr>
</table>
</div>
</li></ul></li>
<li>The stereocenter has only single bond connections, double bond can exist 
only to terminal oxygen atom to support chiral sulfoxides, phosphates. 
</li>
<li>Graph invariants of the ligands are different. 
Exception: ring atom have  parity even if two graph invariants of the ring 
ligands equal and an other stereocenter is located in the ring. </li> </ul>
</p>

<p>The possible values for the tetrahedral parity class:  </p>

<div style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;"><table class="table styles2" cellspacing="0" cellpadding="4" border="0" style="border: none; border-spacing:0px; border-collapse: collapse;">
<tr style="text-align:left;vertical-align:top;">
<td valign="top" width="164" bgcolor="#f3f3f3" style="width:164px; background-color:#f3f3f3; border: solid 1px #c0c0c0;"><p style="text-align: center;">0</p>
</td>
<td valign="top" width="396" bgcolor="#f3f3f3" style="width:396px; background-color:#f3f3f3; border: solid 1px #c0c0c0;"><p style="text-align: left;">For atoms which cannot have tetrahedral parity.</p>
</td>
</tr>
<tr style="text-align:left;vertical-align:top;">
<td valign="top" width="164" style="width:164px; border: solid 1px #c0c0c0;"><p style="text-align: center;">ODD</p>
</td>
<td valign="top" width="396" style="width:396px; border: solid 1px #c0c0c0;"><p style="text-align: left;">For atoms which have ODD parity.</p>
</td>
</tr>
<tr style="text-align:left;vertical-align:top;">
<td valign="top" width="164" bgcolor="#f3f3f3" style="width:164px; background-color:#f3f3f3; border: solid 1px #c0c0c0;"><p style="text-align: center;">EVEN</p>
</td>
<td valign="top" width="396" bgcolor="#f3f3f3" style="width:396px; background-color:#f3f3f3; border: solid 1px #c0c0c0;"><p style="text-align: left;">For atoms which have EVEN parity.</p>
</td>
</tr>
<tr style="text-align:left;vertical-align:top;">
<td valign="top" width="164" style="width:164px; border: solid 1px #c0c0c0;"><p style="text-align: center;">PARITY_EITHER<br>
(ODD | EVEN)</p>
</td>
<td valign="middle" width="396" style="width:396px; border: solid 1px #c0c0c0;"><p style="text-align: left;">For atoms which can have parity but it is unspecified.</p>
</td>
</tr>
</table>
</div>

<p>Note, it  is  not  possible  to  change  parity value  from 0  to  
PARITY_EITHER and  vice  versa  without changing atoms in the molecule. </p>

<p>Square-planar, trigonal-bipyramidal, and octahedral parity 
classes are not yet supported. </p>

<h2>Parity information in 0 Dimension</h2> 
<p>If the atomic coordinates are zero then the molecule's spatial dimension is 
zero. 
The parity information is stored in the atom flag of the stereocenter. </p>

<h3>Setting parity information in 0 Dimension (API)</h3> 
<p>The  parity  information  of  a  single  atom  can  be  modified  using  
the  <code>setParity(int  i,  int  p) </code> method  of 
<code>MoleculeGraph </code> class.  
For  setting parities  of all atoms  in the  molecule,  the  
<code>setParity (int[]  p)</code>  method  of <code>MoleculeGraph </code> 
class  can be  used.  
It  is  important  to  mention  that,  this method is faster than setting 
parities with <code>setParity(int i, int p) </code> one by one. <p>
<p>Code example: setting parity of one atom: </p>
<blockquote><pre class="sh_java">
<i>// setting the first atom parity to ODD</i> 
<b>boolean</b> success = molecule.<b>setParity</b>(1, StereoConstants.PARITY_ODD); 
System.out.<b>println</b>(<b>&#34;Setting parity to atom 1 was &#34;</b>+ ((!success) ? <b>&#34;not&#34;</b> : <b>&#34;&#34;</b>) + <b>&#34; successful&#34;</b>); 
</pre></blockquote>

<p>Code example: setting parity for all atoms: </p>
<blockquote><pre class="sh_java">
<b>int</b> ODD =  StereoConstants.PARITY_ODD; 
<b>int</b> EVEN =  StereoConstants.PARITY_EVEN; 

<i>// we have a molecule with 7 atoms</i> 

<b>int</b>[] parities = new <b>int</b>[]{0, ODD, 0, 0, EVEN, 0, 0}; 
<i>// setting parities</i> 
<b>boolean</b> success = molecule.<b>setParity</b>(parities); 
System.out.<b>println</b>(<b>&#34;Setting parities for the whole molecule was &#34;</b>+ 
((!success) ? <b>&#34;not&#34;</b> : <b>&#34;&#34;</b>)+ <b>&#34; successful&#34;</b>); 
</pre></blockquote>

<h3><a name="nulld"></a>
Getting parity information in 0 Dimension (API)</h3> 
<p>The parity information of a 0 dimensional molecule is stored in the 
<i>atom flags</i>. It can be retrieved with the  <code>getFlags()</code>  function  
of  the  <code>MolAtom</code>  class  or  with  the  
<code>getParity(int  i)</code>  method  of  the 
<code>MoleculeGraph</code> class. </p>

<p>Code example using getFlags method: </p>
<blockquote><pre class="sh_java">
MolAtom a = molecule.<b>getAtom</b>(1); 
<b>int</b> f = a.<b>getFlags</b>(); 
<i>// mask flags</i> 
<b>int</b> p = f &amp; StereoConstants.PARITY_MASK; 
</blockquote></pre>

<p>Code example using getParity method: </p>
<blockquote><pre class="sh_java">
<b>int</b> p = molecule.<b>getParity</b>(1); 
</blockquote></pre>

<h2>Parity information in 2 or 3 Dimensions</h2> 
<p>If the molecule is not 0 dimensional, then the parity information of the  
stereocenter  is  calculated  from the spatial arrangement of the ligands. 
In case of 3 dimensions only the atomic coordinates are used. 
In case of 2  dimensions  beside  the  atomic  coordinates  the  out  of plane  information stored  in the  bond flags – known as wedges – are used. 
</p>

<h3><a name="23d"></a>
Setting parity information in 2 Dimensions (API)</h3> 
<p>The  parity  information  in  2  dimensions  can  be  modified  by  
setting  wedge  to  the  chiral  center.  
PARITY_EITHER can be achieved for a chiral center with wiggly bond or equally,
 with removing all 
wedge from the center. 
There are two methods to define parity in 2 dimensions: 
a single atom can be modified using the <code>setParity(int i, int p)</code> 
method, while  parities  of all atoms  in the  molecule can be modified by the 
<code>setParity(int[] p) </code> method of <code> MoleculeGraph </code>class. 
</p>

<h3>Setting parity information in 3 Dimensions (API)</h3> 
<p>The only possibility to change parity information of a 3 dimensional 
molecule  is  to  change  the  atomic coordinates. 
However, it is not recommended to translate one atom as it may lead to 
unexpected bond length or collision of atoms. 
To overcome this problem one can convert the 3 dimensional molecule to 
0 dimensional, change the parity information and  finally clean the  result  to
  3  dimensions.  Please  note that all parity values should be set to 0, 
CIS or TRANS, it is not possible to set PARITY_EITHER in case of 3D molecules. 
</p>
<p>Code example:  </p>

<blockquote><pre class="sh_java">
molecule.<b>setDim</b>(0); 
molecule.<b>setParity</b>(1, StereoConstants.PARITY_EVEN); 
Cleaner.clean(molecule, 3, <b>null</b>); 
</blockquote></pre>

<h3>Getting parity information in 2 or 3 Dimensions (API)</h3> 
<p>In case of 2 dimensions the parity information is calculated from the 
coordinates and from the wedges connected  to  the  stereocenter.  
In case  of  3  dimensions  only  the  coordinates  are  used,  any  wedge 
information is neglected. 
To calculate the parity of an atom in 2 or 3 dimensions the 
<code>getParity(int i) </code> method of the <code>MoleculeGraph </code>
class can be used. 
</p>
<p>Code example using getParity method: </p>
<blockquote><pre class="sh_java" >
<b>int</b> p = molecule.<b>getParity</b>(1); 
</blockquote></pre>


<p style="text-align: left;"><span style="font-size: 10pt; font-family: 'Arial'; color: #000000;">&nbsp;</span></p
<p style="text-align: left;"><span style="font-size: 10pt; font-family: 'Arial'; color: #000000;">&nbsp;</span></p

  <p>&nbsp;</p>
  <p>&nbsp;</p>
 
 <table summary="next pages">
   <tr style="text-align:left">
     <td width="250" valign="middle"> 
       <p style="text-align: center;">
        <a href="stereo_around_double_bond.html">
          Stereoisomers around double bond</a><br>
        <i>Previous chapter</i>
       </p>   
     </td>
     <td width="250" valign="middle">
      <p style="text-align: center;">
       <a href="core_index.html"> Table of contents</a> <br>
       &nbsp;
      </p>
     </td>
     <td width="250"valign="middle" >
      <p style="text-align: center;"> 
       <a href="chirality.html"> 
           Chirality</a><br>
       <i>Next chapter</i>
      </p>
     </td>
   </tr>
 </table> 

</BODY>
</HTML>
