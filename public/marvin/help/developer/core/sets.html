<html>
<head>
 <meta NAME="description" CONTENT="Atom and bond-set handling">
 <meta NAME="author" CONTENT="<PERSON><PERSON>"> 
 <script type="text/javascript" src="../../../examples/sh_main.js"></script>
 <script type="text/javascript" src="../../../examples/sh_java.js"></script>
 <script type="text/javascript" src="../../../examples/sh_javascript.js">
   </script>
 <link REL ="stylesheet" TYPE="text/css" 
       HREF="../../../examples/sh_nedit.css" TITLE="Style">
 <link REL ="stylesheet" TYPE="text/css" 
       HREF="../../marvinmanuals.css" TITLE="Style">
 <title>Atom and bond-set handling</title>
</head>

<body onload="sh_highlightDocument();">

<h1><a class="anchor" NAME="atom-sets">Atom and bond-set handling</a></h1>

<h2>Format styles (journal styles)</h2>

You can get more advanced display format for the molecule by applying journal styles.
Format styles in Marvin include the setting of the following attributes:
<ul>
<li>type of atom font, </li>
<li>scale of atom font, </li>
<li>color of atoms and atom labels, </li>
<li>thickness of bonds, </li>
<li>color of bonds. </li>
</ul>

Maximum 64 atom sets can be defined in a molecule. Indexing of atom sets starts from zero.
The atom set having the zero index is the default atom set.
When loading a molecule all the atoms belong to the default atom set by default.
After selecting an atom set and applying a style for it, the selected atoms are
removed from the default atom set and a new set is created from the atoms with new style. 
All the atoms, whose style were not yet modified by applying a style on them, 
still belong to the default atom set. 
<p>
You can create an atom- or bond-set in Marvin to specify an atom-font/atom-color 
and bond-thickness/bond-color.
The colors, fonts, thickness values are stored in a lookup table of MDocument.
<p>
The following code example will change the format of some atoms and bonds in a 11 atom - long chain:
<code>
<pre class="sh_java">
		//import a simple chain
		Molecule mol = MolImporter.importMol("CCNCCCCCNCC");
		//create a document to register color, font, size in the lookup table
		MDocument mdoc = new MDocument(mol);
		//define two colors
		Color green = new Color(0, 255, 0);
		Color blue = new Color(0, 0, 255);
		MFont font = new MFont("Helvetica", MFont.BOLD, 20 );
</pre>	
</code>
<p>
To create a new color/font setting for a set of atom you have to register the 
color and font in the lookup table the following way:
<code>
<pre class="sh_java">
		//specify a new atom-style by setting color and font of the 1. atom set
		//set the coloring mode
		mdoc.setAtomSetColorMode(1, MDocument.SETCOLOR_SPECIFIED);
		//set the color of the 1. atom-set
		mdoc.setAtomSetRGB(1, green.getRGB());
		//set the font of the 1. atom-set
		mdoc.setAtomSetFont(1, font);
</pre>	
</code>
<p>
To create a new color/thickness setting for a set of bonds you have to register the 
color and the thickness in the lookup table the following way:
<code>
<pre class="sh_java">
		//specify a new bond-style by setting color and thickness of the 2. atom set
		//set the coloring mode
		mdoc.setBondSetColorMode(2, MDocument.SETCOLOR_SPECIFIED);
		//set the color of the 2. bond-set
		mdoc.setBondSetRGB(2, blue.getRGB());
		//set the thickness of the 2. bond-set
		mdoc.setBondSetThickness(2, 0.2);
</pre>	
</code>
<p>
You can create an atom-set in Marvin the following way:
<code>
<pre class="sh_java">
		//set the color and font of the 2. and 8. atoms in the molecule
		//by adding these atoms to the 1. atom set.
		mol.getAtom(2).setSetSeq(1);
		mol.getAtom(8).setSetSeq(1);
</pre>	
</code>
<p>
You can create a bond-set in Marvin the following way:
<code>
<pre class="sh_java">
		//set the color and thickness of 4. bond in the molecule
		//by adding this bond to the 2. atom set.
</pre>	
</code>
<p>
The result of atom and bond set coloring example is shown on the picture: 
<center>
  <img src="images/setcoloring.png" width="523" height="517"/>
</center><br>

The atom- and bond-set coloring is available using the
<code>atomSetColor</code>, <code>bondSetColor</code> applet and beans parameters.

</p>

<p>&nbsp;</p>
  <p>&nbsp;</p>
 
 <table summary="next pages">
   <tr style="text-align:left">
     <td width="250" valign="middle"> 
       <p style="text-align: center;">
        <a href="iterators.html">
          Iterator Factory</a><br>
        <i>Previous chapter</i>
       </p>   
     </td>
     <td width="250" valign="middle">
      <p style="text-align: center;">
       <a href="core_index.html"> Table of contents</a> <br>
       &nbsp;
      </p>
     </td>
     <td width="250"valign="middle" >
      <p style="text-align: center;"> 
       <a href="graphic_objects.html"> 
           Graphic Object handling</a><br>
       <i>Next chapter</i>
      </p>
     </td>
   </tr>
 </table> 


</body>
</html>
