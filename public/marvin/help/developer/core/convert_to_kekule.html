<!DOCTYPE html><HTML>
 <head>
        <META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
        <title> Converting structure from aromatic form to Kekule form  </title>
        <link rel=stylesheet type="text/css" href="../../marvinmanuals.css">
 </head>
<BODY>
<h1>Converting structure from aromatic form to Kekule form</h1> 
<p>There  are  two  functions  available  to  convert  the  molecule  from  
aromatic  form  to  Kekule  form: 
<code>dearomatize()</code>  and </code>aromatize(false)</code>  located  in 
MoleculeGraph  class.  
The  former  method  is suggested for general use as it reports false if the 
conversion was not successful. </p>
<p>Code example: </p>
<p><ul>
 <code>boolean success = molecule.<b>dearomatize</b>(); </code>
</ul></p>

<p>More information about dearomatization can be found
<a href="../../sci/aromatization-doc.html">here.
</a></p>

 <p>&nbsp;</p>
 <p>&nbsp;</p>
 
 <table summary="next pages">
   <tr style="text-align:left">
     <td width="250" valign="middle"> 
       <p style="text-align: center;">
        <a href="diff_basic_and_general.html">
          Differences between the basic and general methods</a><br>
        <i>Previous chapter</i>
       </p>   
     </td>
     <td width="250" valign="middle">
      <p style="text-align: center;">
       <a href="core_index.html"> Table of contents</a> <br>
       &nbsp;
      </p>
     </td>
     <td width="250"valign="middle" >
      <p style="text-align: center;"> 
       <a href="impl_expl_query_hydrogens.html"> 
           Implicit, Explicit and Query Hydrogens<a><br>
       <i>Next chapter</i>
      </p>
     </td>
   </tr>
 </table>

</BODY>
</HTML>
