<!DOCTYPE HTML PUBLIC >
<html>
 <HEAD>
        <META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
        <title>Molecule Graph</title>
        <link rel=stylesheet type="text/css" href="../../marvinmanuals.css">
 </HEAD>
 <body>
  <h1> Molecule Graph </h1> 
  
    <p>Graphs offer a natural way of representing chemical structures. 
       In this case the atoms are the nodes of the graph and the bonds 
       are the edges. We can then “color” the nodes with atom types and 
       “weigh” the edges with bond types. 
       This graph, which contains nodes and edges with different properties, 
       is the molecular graph.</p> 
  
   <p>Elements of the molecule graph are:</p>
   <ul>
     <li> atoms,  </li>
     <li> bonds.  </li>
   </ul>
   <p>Only one connection/bond is allowed between any two atoms and no self 
      connection is allowed. To represent this molecule graph in a computer 
      the following matrices are introduced:</p>
   <ul>
    <li><strong>Connection table</strong>, <i>ctab</i>: 
        the <i>i-</i>th row defines the indices of the atoms connected to the
        <i>i-</i>th atom.  
        <table>
          <tr style="text-align:left;vertical-align:top;">
           <td valign="top" width="302" style="width:302px;" > 
             <p style="text-align: left;">
                <img src="images/ctab.png" 
                width="223" height="158" border="0" alt="ctab"></p> </td>
           <td align="top" width="302" style="width:302px;">  
           <p style="text-align: left;">Connection table (ctab):</p>
           <p style="text-align: left; margin: 0px 0px 0px 33px;">
            <span>[2]</span></p>
           <p style="text-align: left; margin: 0px 0px 0px 33px;">
             <span>[1, 3, 4]</span></p>
           <p style="text-align: left; margin: 0px 0px 0px 33px;">
             <span>[2]</span></p>
           <p style="text-align: left; margin: 0px 0px 0px 33px;" >
             <span>[2, 5]</span></p>
           <p style="text-align: left; margin: 0px 0px 0px 33px;" >
             <span>[4]</span></p>
          </tr>
        </table>
       </li> 
    </li> 
    <li><strong>Bond table</strong>, <i>btab</i>: 
        bond indexes between atom <i>i</i> and atom <i>j</i>. 
        For small molecules it is a square matrix, for large molecules a 
        sparse matrix. It represents the following matrix:      
        <table>
          <tr style="text-align:left;vertical-align:top;">
           <td valign="top" width="302" style="width:302px;" > 
             <p style="text-align: left;">
                <img src="images/ctab.png" 
                width="223" height="158" border="0" alt="btab"></p> </td>
           <td align="top" width="302" style="width:302px;">  
           <p style="text-align: left;">Bond table (btab):</p>
           <p style="text-align: left; margin: 0px 0px 0px 33px;">
            <span>[0, 1, 0, 0, 0]</span></p>
           <p style="text-align: left; margin: 0px 0px 0px 33px;">
             <span>[1, 0, 2, 3, 0]</span></p>
           <p style="text-align: left; margin: 0px 0px 0px 33px;">
             <span>[0, 2, 0, 0, 0]</span></p>
           <p style="text-align: left; margin: 0px 0px 0px 33px;" >
             <span>[0, 3, 0, 0, 4]</span></p>
           <p style="text-align: left; margin: 0px 0px 0px 33px;" >
             <span>[0, 0, 0, 4, 0]</span></p>
          </tr>
        </table>
    </li> 

    <p> Please note that instead of getting the full bond table we are planning 
        to introduce a new method that gives back the bond index between atoms 
        <i>i</i> and <i>j</i>.  </p>   
   </ul>
   <p>Chemically relevant information in the graph:</p>
   <ul>
     <li> number of connected fragments,</li> 
     <li> rings: SSSR (Smallest Set of Smallest Rings)
   </ul>
   <p> Connections between the atoms specify the topology, but the relative 
       spatial arrangement of atoms – the configuration – also needs to be 
       defined, as molecules with same connectivity but different spatial 
       arrangement – stereoisomers 
       (see <a href="stereo_introduction.htm">stereochemistry</a> for details) 
       – should be expressed. </p>
   <p> Spatial dimension of the building atoms defines the dimension of the 
       molecule. </p>
   <ul>
     <li> 0D – all atoms are in [0, 0, 0].            </li> 
     <li> 2D – z coordinate is 0, [x, y, 0].          </li> 
     <li> 3D – all coordinates are defined [x, y, z]  </li> 
   </ul>
   <p> The dimensionality can be retrieved by the <code>getDim()</code> 
       method of <code>MoleculeGraph</code> class. It can be set by the 
       <code>setDim(int d)</code> method, but <code>setDim(int d)</code> 
       does not generate coordinates for the molecule. To generate coordinates,
       which is obviously needed (bear in mind that it can be a slow process), 
       the <code>clean(MoleculeGraph mg, int dim, java.lang.String opts)</code>
       method of <code>chemaxon.calculation.clean.Cleaner</code> 
       class can be used. 
   </p>
   <p>Once the molecule is built the atom and bond indexes are fixed, 
      they don't change with property changes (like changes in atom or 
      bond type), but if the structure is modified with removal/addition 
      of atoms or bonds, the indexes are changed. </p>
   <p>Please note, that atoms and bonds are indexed from 0, 
      but MarvinView and MarvinSketch show the indexes starting from 1.</p>

 
 
 
 <p>&nbsp;</p>
 <p>&nbsp;</p>
 <p>&nbsp;</p>
 <p>&nbsp;</p>
 
 <table>
   <tr style="text-align:left">
     <td width="250" valign="middle"> 
       <p style="text-align: center;">
        <a href="molbond.html">MolBond</a>
       </p>   
     </td>
     <td width="250" valign="middle">
      <p style="text-align: center;">
       <a href="core_index.html"> Table of contents</a> 
      </p>
     </td>
     <td width="250"valign="middle" >
      <p style="text-align: center;"> 
       <a href="rgroups.html">R-group structures</a>
      </p>
     </td>
   </tr>
   <tr style="text-align:left">
     <td width="250" valign="middle"> 
       <p style="text-align: center;"> 
        <i>Previous chapter</i> 
       </p> 
     </td>
     <td width="250" valign="middle">
         &nbsp;
     </td>
     <td width="250" valign="middle" >
      <p style="text-align: center;">
       <i>Next chapter</i>
      </p>
     </td>
   </tr>
 </table>

 </body>
</html>
