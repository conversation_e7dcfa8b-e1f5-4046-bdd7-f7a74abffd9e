<!DOCTYPE html><HTML>


<head>
 <META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
 <script type="text/javascript" src="../../../examples/sh_main.js"></script>
 <script type="text/javascript" src="../../../examples/sh_java.js"></script>
 <script type="text/javascript" src="../../../examples/sh_javascript.js">
     </script>
 <link REL ="stylesheet" TYPE="text/css" 
       HREF="../../../examples/sh_nedit.css" TITLE="Style">
 <title> Code examples  </title>
 <link rel=stylesheet type="text/css" href="../../marvinmanuals.css">
</head>


<BODY onload="sh_highlightDocument();">
<h1>Code examples</h1>

<b>Build CO molecule</b> 
<pre class="sh_java">
<i>/*</i> 
 <i>* Copyright (c) 1998-2013 ChemAxon Ltd. All Rights Reserved.</i> 
 <i>*/</i> 
<b>import</b> java.io.IOException;

<b>import</b> chemaxon.formats.MolExporter;
<b>import</b> chemaxon.struc.*; 
<i>/**</i> 
<i> * Example class for structure manipulation. Creates CO</i> 
<i> *</i> 
<i> * </i><b>@author</b><i> Andras Volford, Miklos Vargyas</i> 
<i> * </i> 
<i> */</i> 
<b>public class</b> BuildMoleculeCO { 
    <b>public static</b> <b>void</b> <b>main</b>(String[] args) <b>throws</b> IOException{ <br>
        <i>// create an empty Molecule</i> 
        Molecule m = <b>new</b> <b>Molecule</b>(); 

        <i>// create the Carbon atom</i> 
        MolAtom a1 = <b>new</b> <b>MolAtom</b>(6); 
        <i>// and add it to the molecule </i> 
        m.<b>add</b>(a1); 

        <i>// create the Oxygen atom</i> 
        MolAtom a2 = <b>new</b> <b>MolAtom</b>(8); 
        <i>// and add it to the molecule</i> 
        m.<b>add</b>(a2);     

        System.out.<b>println</b>(MolExporter.exportToFormat(m,<b>&#34;smiles&#34;</b>)); 
        <i>// this prints C.O as no bond has been defined yet </i> 

        <i>// create a bond between atoms, bond order </i> 
        MolBond b = <b>new</b> <b>MolBond</b>(a1, a2, 2);         
        m.<b>add</b>(b);     <br>     
        System.out.<b>println</b>(MolExporter.exportToFormat(m,<b>&#34;smiles&#34;</b>)); 
        <i>// this prints C=O </i> 
    } 
} 
</pre>

<b>Build water molecule</b> 
<pre class="sh_java">

package chemaxon.examples.strucrep

import chemaxon.struc.*;

/**
 * Example class for structure manipulation. Creates water.
 *
 * <AUTHOR> Volford
 * 
 */
public class BuildMoleculeWater {

    public static void main(String[] args) {

        // create an empty Molecule
        Molecule m = new Molecule();

        // create the Carbon atom
        MolAtom a1 = new MolAtom(8);
        // and add it to the molecule 
        m.add(a1);

        // create the Hydrogen atom
        MolAtom a2 = new MolAtom(1);
        // and add it to the molecule
        m.add(a2);

        // create the Hydrogen atom
        MolAtom a3 = new MolAtom(1);
        // and add it to the molecule
        m.add(a3);
        
        System.out.println(m.toFormat("smiles"));
        // this prints [H+].[H+].O as no bond has been defined yet 

        // create a bond between atoms, bond order 
        MolBond b1 = new MolBond(a1, a2, 1);
        m.add(b1);
        MolBond b2 = new MolBond(a1, a3, 1);
        m.add(b2);
        
        System.out.println(m.toFormat("smiles"));
        // this prints water 
    }
}
</pre>

<b>BuildMoleculeEthylene.java</b> 
<pre class="sh_java">
<i>/*</i> 
<i> * Copyright (c) 1998-2013 ChemAxon Ltd. All Rights Reserved.</i> 
<i> */</i>
<b>import</b> java.io.IOException;

<b>import</b> chemaxon.formats.MolExporter;
<b>import</b> chemaxon.calculations.clean.Cleaner;
<b>import</b> chemaxon.struc.*; 
<i>/**</i> 
<i> * Example class for structure manipulation. Creates ethylene C/C=C=/C</i> 
<i> * atom by atom.</i> 
<i> *</i> 
<i> * </i><b>@author</b><i> Andras Volford, Miklos Vargyas </i> 
<i> */</i> 
<b>public class</b> BuildMoleculeEthylene { 
    <b>public static</b> <b>void</b> <b>main</b>(String[] args) <b>throws</b> IOException{ 
        <i>// create an empty Molecule</i> 
        Molecule m = <b>new</b> <b>Molecule</b>(); 

        <i>// create a Carbon atom</i> 
        MolAtom a1 = <b>new</b> <b>MolAtom</b>(6); 
        <i>// and add it to the molecule </i> 
        m.<b>add</b>(a1); 

        <i>// create another Carbon atom</i> 
        MolAtom a2 = <b>new</b> <b>MolAtom</b>(6); 
        <i>// and add it to the molecule</i> 
        m.<b>add</b>(a2);     <br>     
        <i>// create a bond between atoms, bond order </i> 
        MolBond b = <b>new</b> <b>MolBond</b>(a1, a2, 2);         
        m.<b>add</b>(b);   
        
        System.out.<b>println</b>(MolExporter.exportToFormat(m,<b>&#34;smiles&#34;</b>)); 
        <i>// this prints C=C</i> 

         
        <i>// add ligands</i> 
        MolAtom l1 = <b>new</b> <b>MolAtom</b>(6);         
        MolAtom l2 = <b>new</b> <b>MolAtom</b>(6);         
        m.<b>add</b>(l1);         
        m.<b>add</b>(l2);         
        m.<b>add</b>(<b>new</b> <b>MolBond</b>(a1, l1));         
        m.<b>add</b>(<b>new</b> <b>MolBond</b>(a2, l2));      

        System.out.<b>println</b>(MolExporter.exportToFormat(m,<b>&#34;smiles&#34;</b>));
        System.out.<b>println</b>(MolExporter.exportToFormat(m,<b>&#34;mol&#34;</b>));          
        <i>// generate 2D coordinates</i> 
        Cleaner.clean(m, 2, <b>null</b>);        
        System.out.<b>println</b>(MolExporter.exportToFormat(m, <b>&#34;mol&#34;</b>));          

        <i>// CIS/TRANS information is not defined, the bond is wiggly</i> 
        b = m.<b>getBond</b>(0); 
        System.out.<b>println</b>(<b>&#34;cis=&#34;</b> + MolBond.CIS );
        System.out.<b>println</b>(<b>&#34;trans=&#34;</b> + MolBond.TRANS );
        System.out.<b>println</b>(<b>&#34;stereo flag before setting: &#34;</b> + (b.<b>getFlags</b>()  &amp; MolBond.STEREO_MASK)); 
        
        <i>// set bond to CIS</i> 
        b.<b>setFlags</b>(MolBond.CIS, MolBond.STEREO_MASK);         
        System.out.<b>println</b>(<b>&#34;stereo flag after setting: &#34;</b> + (b.<b>getFlags</b>()  &amp; MolBond.STEREO_MASK)); 
        System.out.<b>println</b>(MolExporter.exportToFormat(m, <b>&#34;mol&#34;</b>));   
           
        <i>// render again in 2D</i> 
        System.out.<b>println</b>(<b>&#34;Cleaned again&#34;</b>);         
        Cleaner.clean(m, 2, <b>null</b>);        
        System.out.<b>println</b>(<b>&#34;stereo flag: &#34;</b> + (b.<b>getFlags</b>() &amp; MolBond.STEREO_MASK)); 
        System.out.<b>println</b>(MolExporter.exportToFormat(m, <b>&#34;mol&#34;</b>)); 

        m.<b>setDim</b>(0);         
        b.<b>setFlags</b>(MolBond.CIS, MolBond.STEREO_MASK);         
        System.out.<b>println</b>(<b>&#34;stereo flag after setting: &#34;</b> + (b.<b>getFlags</b>() &amp; MolBond.STEREO_MASK)); 
        System.out.<b>println</b>(MolExporter.exportToFormat(m, <b>&#34;mol&#34;</b>)); 

        <i>// render again in 2D</i> 
        System.out.<b>println</b>(<b>&#34;Cleaned the 3rd time after setdim(0)&#34;</b>);         
        Cleaner.clean(m, 2, <b>null</b>);        
        System.out.<b>println</b>(<b>&#34;stereo flag: &#34;</b> + (b.<b>getFlags</b>() &amp; MolBond.STEREO_MASK)); 
        System.out.<b>println</b>(MolExporter.exportToFormat(m, <b>&#34;mol&#34;</b>));     
    } 
} 
</pre>

<b>BuildMoleculeEthyleneStereo.java</b> 
<pre class="sh_java">
<i>/*</i> 
<i> * Copyright (c) 1998-2013 ChemAxon Ltd. All Rights Reserved.</i> 
<i> */</i> 
<b>import</b> java.io.IOException;

<b>import</b> chemaxon.formats.MolExporter;
<b>import</b> chemaxon.calculations.clean.Cleaner;
<b>import</b> chemaxon.struc.*; 
<i>/**</i> 
<i> * Example class for structure manipulation. Creates ethylene C/C=C=/C</i> 
<i> * atom by atom.</i> 
<i> *</i> 
<i> * </i><b>@author</b><i> Andras Volford, Miklos Vargyas </i> 
<i> */</i> 
<b>public class</b> BuildMoleculeEthyleneStereo { 

    <b>public static</b> <b>void</b> <b>main</b>(String[] args) <b>throws</b> IOException { 
        <i>// create an empty Molecule</i> 
        Molecule m = <b>new</b> <b>Molecule</b>(); 

        <i>// create a Carbon atom</i> 
        MolAtom a1 = <b>new</b> <b>MolAtom</b>(6); 
        <i>// and add it to the molecule </i> 
        m.<b>add</b>(a1); 

        <i>// create anoter Carbon atom</i> 
        MolAtom a2 = <b>new</b> <b>MolAtom</b>(6); 
        <i>// and add it to the molecule</i> 
        m.<b>add</b>(a2);     
     
        <i>// create a bond between atoms, bond order </i> 
        MolBond b = <b>new</b> <b>MolBond</b>(a1, a2, 2);         
        m.<b>add</b>(b);          
        
        System.out.<b>println</b>(MolExporter.exportToFormat(m, <b>&#34;smiles&#34;</b>)); 
        <i>// this prints C=C</i> 
         
        <i>// add ligands</i> 
        MolAtom l1 = <b>new</b> <b>MolAtom</b>(6);         
        MolAtom l2 = <b>new</b> <b>MolAtom</b>(6);         
        m.<b>add</b>(l1);         
        m.<b>add</b>(l2);         
        m.<b>add</b>(<b>new</b> <b>MolBond</b>(a1, l1));         
        m.<b>add</b>(<b>new</b> <b>MolBond</b>(a2, l2)); 
     
        System.out.<b>println</b>(MolExporter.exportToFormat(m, <b>&#34;smiles&#34;</b>));
        System.out.<b>println</b>(MolExporter.exportToFormat(m, <b>&#34;mol&#34;</b>));          
        <i>// generate 2D coordinates</i> 
        Cleaner.clean(m, 2, <b>null</b>);       
        System.out.<b>println</b>(m.<b>toFormat</b>(MolExporter.exportToFormat(m, <b>&#34;mol&#34;</b>)); 
    } 
} 
</pre>

<b>MoleculeAtoms.java</b> 
<pre class="sh_java">

/* 
 * Copyright (c) 1998-2013 ChemAxon Ltd. All Rights Reserved. 
 */ 
<b>import</b> java.io.IOException; 
<b>import</b> chemaxon.formats.MolFormatException; 
<b>import</b> chemaxon.formats.MolImporter; 
<b>import</b> chemaxon.struc.Molecule; 
<b>import</b> chemaxon.struc.MolAtom; 
<b>import</b> chemaxon.struc.MolBond; 
<i>/**</i> 
<i> * Example class to demonstrate how to access atoms and bonds </i> 
<i> * of the molecule. </i> 
<i> *</i> 
<i> * </i><b>@author</b><i> Andras Volford, Miklos Vargyas</i> 
<i> * </i> 
<i> */</i>  
<b>public class</b> MoleculeAtoms { 
    <b>public static</b> <b>void</b> <b>main</b>(String[] args) { 
        String filename = args[0]; 
         
        <b>try</b> { 
            <i>// create a molecule importer for the given file</i> 
            MolImporter mi = <b>new</b> <b>MolImporter</b>(filename);          

            <i>// read the first molecule from the file</i> 
            Molecule m = mi.<b>read</b>();              

            <b>while</b> (m != null) {                 
                <b>printAtoms</b>(m);                 
                <b>printBonds</b>(m);                  
                
                <i>// read the next molecule from the input file</i> 
                m = mi.<b>read</b>();             
               } 
               mi.<b>close</b>();         
         } 
        <b>catch</b> (MolFormatException e) {             
           System.err.<b>println</b>(<b>&#34;Molecule format not recognised.&#34;</b>);         
         } 
        <b>catch</b> (IOException e) {             
            System.err.<b>println</b>(<b>&#34;I/O error:&#34;</b> + e);
         } 
    } 
         
    <b>private static</b> <b>void</b> <b>printAtoms</b>( Molecule m ) {     
           m.<b>calcHybridization</b>();
           System.out.<b>println</b>(<b>&#34;Atoms in the molecule</b>\n<b>atomic number</b>\t<b>charge</b>\t<b>hybridisation&#34;</b>);
           <b>for</b> (<b>int</b> i = 0; i &lt; m.<b>getAtomCount</b>(); i++) { 
              MolAtom a = m.<b>getAtom</b>(i);             
              System.out.<b>println</b>( i + <b>&#34;th atom: &#34;</b> + a.<b>getAtno</b>() + <b>&#34;</b>\t\t<b>&#34;</b>  
                                  + a.<b>getCharge</b>() + <b>&#34;</b>\t<b>&#34;</b> 
                                  + a.<b>getHybridizationState</b>()); 
        } 
    } 
     
     
    <b>private static</b> <b>void</b> <b>printBonds</b>( Molecule m ) { 
        System.out.<b>println</b>(<b>&#34;Bonds in the molecule</b>\n<b>bond order</b>\t<b>coodinate&#34;</b>); 
        <b>for</b> (<b>int</b> i = 0; i &lt; m.<b>getBondCount</b>(); i++) {  
          MolBond b = m.<b>getBond</b>(i);             
          System.out.<b>println</b>( b.<b>getType</b>() + <b>&#34;</b>\t\t<b>&#34;</b> + b.<b>isCoordinate</b>() + <b>&#34; &#34; </b>
                + m.<b>indexOf</b>( b.<b>getAtom1</b>()) + <b>&#34;-&#34;</b> + m.<b>indexOf</b>( b.<b>getAtom2</b>())); 
        }       
    } 
     
} 

</pre>

<b>MoleculeIterators.java</b> 
<pre class="sh_java">
<i>/* 
 * Copyright (c) 1998-2013 ChemAxon Ltd. All Rights Reserved. 
 */ </i>
<b>import</b> java.io.IOException; 
<b>import</b> chemaxon.formats.MolFormatException; 
<b>import</b> chemaxon.formats.MolImporter; 
<b>import</b> chemaxon.struc.Molecule; 
<b>import</b> chemaxon.struc.MolAtom; 
<b>import</b> chemaxon.struc.MolBond; 
<b>import</b> chemaxon.util.iterator.IteratorFactory; 
<b>import</b> chemaxon.util.iterator.IteratorFactory.AtomIterator; 
<b>import</b> chemaxon.util.iterator.IteratorFactory.BondIterator; 
<i>/**</i> 
<i> * Example class to demonstrate how to access atoms and bonds </i> 
<i> * of the molecule using Iterators.</i> 
<i> *</i> 
<i> * </i><b>@author</b><i> Andras Volford, Miklos Vargyas</i> 
<i> *</i> 
<i> */</i>  
<b>public class</b> MoleculeIterators { 

    <b>public static</b> <b>void</b> <b>main</b>(String[] args) { 

        String filename = args[0]; 
         
        <b>try</b> { 
            <i>// create a molecule importer for the given file</i> 
            MolImporter mi = <b>new</b> <b>MolImporter</b>(filename);          
         
            <i>// read the first molecule from the file</i> 
            Molecule m = mi.<b>read</b>();              
            <b>while</b> (m != null) {
                 IteratorFactory itFac = <b>new</b> <b>IteratorFactory</b>(m,
                        IteratorFactory.INCLUDE_CHEMICAL_ATOMS_ONLY, 
                        IteratorFactory.REPLACE_COORDINATE_BONDS ); 
                <b>printAtoms</b>(itFac,m);
                <b>printBonds</b>(itFac,m);                  

                <i>// read the next molecule from the input file</i> 
                m = mi.<b>read</b>();
             } 
            mi.<b>close</b>(); 
        } 
        <b>catch</b> (MolFormatException e) {
             System.err.<b>println</b>(<b>&#34;Molecule format not recognised.&#34;</b>);
         } 
        <b>catch</b> (IOException e) { 
            System.err.<b>println</b>(<b>&#34;I/O error:&#34;</b> + e);
         } 
    } 
         
    <b>private static</b> <b>void</b> <b>printAtoms</b>( IteratorFactory itFac, Molecule m ) {
         AtomIterator ai = itFac.<b>createAtomIterator</b>();
          
         System.out.<b>println</b>(<b>&#34;Atoms in the molecule</b>\n<b>atomic number</b>\t<b>charge&#34;</b>);
         <b>while</b> (ai.<b>hasNext</b>()) {
             MolAtom a = ai.<b>next</b>();
             System.out.<b>println</b>( a.<b>getAtno</b>() + <b>&#34;</b>\t\t<b>&#34;</b> + a.<b>getCharge</b>() );
         } 
    } 
         
    <b>private static</b> <b>void</b> <b>printBonds</b>( IteratorFactory itFac, Molecule m ) {
         BondIterator bi = itFac.<b>createBondIterator</b>();
 
         System.out.<b>println</b>(<b>&#34;Bonds in the molecule</b>\n<b>bond order</b>\t<b>coodinate&#34;</b>);
 
        <b>while</b> (bi.<b>hasNext</b>()) {
             MolBond b = bi.<b>next</b>();
             System.out.<b>println</b>( b.<b>getType</b>() + <b>&#34;</b>\t\t<b>&#34;</b> + b.<b>isCoordinate</b>()
                      + <b>&#34; &#34;</b> + m.<b>indexOf</b>(b.<b>getAtom1</b>()) + <b>&#34;-&#34;</b>  
                      + m.<b>indexOf</b>(b.<b>getAtom2</b>())); 
        }       
    } 
     
} 

</pre>

<b>ReadMoleculeFile.java</b> 
<pre class="sh_java">
<i>/*</i> 
<i> * Copyright (c) 1998-2013 ChemAxon Ltd. All Rights Reserved.</i> 
<i> */</i> 
<b>import</b> java.io.IOException; 
<b>import</b> chemaxon.formats.MolFormatException; 
<b>import</b> chemaxon.formats.MolImporter; 
<b>import</b> chemaxon.struc.Molecule; 
<i>/**</i> 
<i> * Example class for molecule import.</i> 
<i> *</i> 
<i> * </i><b>@author</b><i> Andras Volford, Miklos Vargyas</i> 
<i> * </i> 
<i> */</i> 
<b>public class</b> ReadMoleculeFile {
 
    <b>public static</b> <b>void</b> <b>main</b>(String[] args) {      

        String filename = args[0]; 
         
        <b>try</b> { 
            <i>// create a molecule importer for the given file</i> 
            MolImporter mi = <b>new</b> <b>MolImporter</b>(filename);          

            <i>// read the first molecule from the file</i> 
            Molecule m = mi.<b>read</b>();              

            <b>while</b> (m != null) {                  
                <b>printProperties</b>( m );                 
                <i>// read the next molecule from the input file</i> 
                m = mi.<b>read</b>();
             } 
            mi.<b>close</b>();
         } 
        <b>catch</b> (MolFormatException e) {
             System.err.<b>println</b>(<b>&#34;Molecule format not recognised.&#34;</b>);
         } 
        <b>catch</b> (IOException e) {
             System.err.<b>println</b>(<b>&#34;I/O error:&#34;</b> + e);
         } 
    } 
     
    <b>private static</b> <b>void</b> <b>printProperties</b>( Molecule m ) {
         System.out.<b>println</b>( <b>&#34;</b>\n<b>Molecule &#34;</b> + m.<b>getName</b>() );
         <b>int</b> nProps = m.<b>getPropertyCount</b>();
         <b>for</b> ( <b>int</b> i = 0; i &lt; nProps; i++ ) {
             String propKey = m.<b>getPropertyKey</b>( i );
             String propValue = m.<b>getProperty</b>( propKey );
             System.out.<b>println</b>( propKey + <b>&#34; = &#34;</b> + propValue );
         } 
    } 
} 
</pre>

<b>ReadMoleculeString.java</b> 
<pre  class="sh_java">
<i>/*</i> 
<i> * Copyright (c) 1998-2013 ChemAxon Ltd. All Rights Reserved.</i> 
<i> */</i> 

<b>import</b> chemaxon.formats.MolImporter; 
<b>import</b> chemaxon.formats.MolFormatException; 
<b>import</b> chemaxon.struc.Molecule; 

<i>/**</i> 
<i> * Simple example of building a molecule from a SMILES string.</i> 
<i> *</i> 
<i> * </i><b>@author</b><i> Andras Volford, Miklos Vargyas</i> 
<i> * </i> 
<i> */</i> 

<b>public class</b> ReadMoleculeString { 
    <b>public static</b> <b>void</b> <b>main</b>(String[] args) {
      
        <b>try</b> {
             String smiles = <b>&#34;CC&gt;&gt;CC&#34;</b>;
 
             Molecule m = MolImporter.<b>importMol</b>(smiles);
 
             System.out.<b>println</b>(m.<b>getAtomCount</b>());
             System.out.<b>println</b>(m.<b>toFormat</b>(<b>&#34;mol&#34;</b>));
          
        }  
        <b>catch</b> (MolFormatException e) {
             System.err.<b>println</b>(<b>&#34;Format not recognised.&#34;</b>);
         } 
    } 
} 
</pre>


<b>CisTransExample.java</b> 
<p><pre class="sh_java">
<i>/*</i> 
 <i>* Copyright (c) 1998-2013 ChemAxon Ltd. All Rights Reserved.</i> 
 <i>*/</i>
 
<b>import</b> java.io.IOException; 
<b>import</b> chemaxon.struc.Molecule; 
<b>import</b> chemaxon.struc.MolBond; 
<b>import</b> chemaxon.struc.CNode; 
<b>import</b> chemaxon.struc.StereoConstants; 
<b>import</b> chemaxon.formats.MolImporter; 

<i>/**</i> 
 <i>* Example to get double bond stereo information of double bonds.</i> 
 <i>* </i><i>Usage:</i> 
 <i>* </i><i>java CisTransExample filename</i> 
 <i>*</i> 
 <i>* </i><b>@version</b> <i>5.1 04/24/2008</i> 
 <i>* </i><b>@since</b> <i>Marvin 5.1</i> 
 <i>* </i><b>@author</b> <i>Andras Volford</i> 
 <i>*/</i> 

<b>public</b> <b>class</b> CisTransExample { 
     <i>/**</i> 
      <i>* Main method.</i> 
      <i>* </i><b>@param</b> <i>args   command line arguments (filename)</i> 
      <i>*/</i> 

    <b>public</b> <b>static</b> <b>void</b> main(String[] args) <b>throws</b> IOException {
         <b>if</b>(args.length &lt; 1) {
             System.err.println(<b>&#34;Usage: java CisTransTest filename&#34;</b>);
             System.exit(0); 
        } 

        <i>// create importer for the file argument</i> 
        String s = (String)args[0]; 
        MolImporter molimp = <b>new</b> MolImporter(s); 

        <i>// store the imported molecules in m</i> 
        Molecule m = <b>new</b> Molecule(); 

        <i>// counter for molecules</i> 
        <b>int</b> n = 0; 

        <b>while</b>(molimp.read(m)){  <i>// read molecules from the file</i> 
            ++n;                <i>// increment counter</i> 
            System.err.println(<b>&#34;mol &#34;</b>+n);
 
            <i>// calculate double bond stereo for every double bond</i> 
            <i>// which have at least one ligand on each node of the double bond</i> 
            <b>for</b> (<b>int</b> i=0; i&lt;m.getBondCount(); i++){
                 MolBond b = m.getBond(i); 
                <b>if</b> (b.getType() == 2){
 
                    <i>// get the default frame</i> 
                    CNode c1 = b.getCTAtom1(); 
                    CNode c2 = b.getNode1(); 
                    CNode c3 = b.getNode2(); 
                    CNode c4 = b.getCTAtom4(); 

                    <b>if</b> (c1 != <b>null</b> &amp;&amp; c4 != <b>null</b>){
 
                        <i>// cis/trans stereo for the default frame</i> 
                        <b>int</b> ct = m.getStereo2(b, c1, c4, <b>true</b>);
                          
                        System.out.println(m.indexOf(c1)+<b>&#34;-&#34;</b>+
                        m.indexOf(c2)+<b>&#34;=&#34;</b>+m.indexOf(c3)+<b>&#34;-&#34;</b>+
                        m.indexOf(c4)+<b>&#34;  &#34;</b>+ 
                            ((ct == MolBond.CIS) ? <b>&#34;CIS&#34;</b> :
                             (ct == MolBond.TRANS) ? <b>&#34;TRANS&#34;</b> :
                             (ct == (MolBond.TRANS|MolBond.CIS)) ?  
                                   <b>&#34;CIS|TRANS&#34;</b> : (<b>&#34;&#34;</b>+ct) )+
                             (((ct &amp; StereoConstants.CTUNSPEC) != 0) ?  
                                   <b>&#34;CTUNSPEC&#34;</b> : <b>&#34; &#34;</b>) ); 

                        <i>// E/Z stereo</i> 
                        ct = m.getStereo2(b); 
                        System.out.println(<b>&#34;E/Z &#34;</b>+
                             m.indexOf(c2)+<b>&#34;=&#34;</b>+m.indexOf(c3)+<b>&#34;   &#34;</b>+
                             ((ct == MolBond.CIS) ? <b>&#34;Z&#34;</b> :
                              (ct == MolBond.TRANS) ? <b>&#34;E&#34;</b> : <b>&#34;&#34;</b>+ct )+
                             (((ct &amp; StereoConstants.CTUNSPEC) != 0) ?    
                                   <b>&#34;CTUNSPEC&#34;</b> : <b>&#34;&#34;</b>) ); 
                    } 
                } 
            } 
        } 
    } 
}

</pre>

 
<b>ParityExample.java</b> 
<p><pre class="sh_java">
<i>/*</i> 
 <i>* Copyright (c) 1998-2013 ChemAxon Ltd. All Rights Reserved.</i> 
 <i>*/</i> 

<b>import</b> java.io.IOException; 

<b>import</b> chemaxon.struc.Molecule; 
<b>import</b> chemaxon.formats.MolImporter; 
<b>import</b> chemaxon.struc.StereoConstants; 

<b>public</b> <b>class</b> ParityExample { 

     <i>/**</i> 
      <i>* Example to get the parity and chirality of the atoms.</i> 
      <i>* </i><b>@param</b> <i>args   command line arguments</i> 
      <i>* </i><b>@throws</b> <i>java.io.IOException </i> 
      <i>* </i> 
      <i>* </i><b>@version</b> <i>5.1 04/24/2008</i> 
      <i>* </i><b>@since</b> <i>Marvin 5.1</i> 
      <i>* </i><b>@author</b> <i>Andras Volford</i> 
      <i>*/</i> 

    <b>public</b> <b>static</b> <b>void</b> main(String[] args) <b>throws</b> IOException {
         <b>if</b> (args.length &lt; 1) {
             System.err.println(<b>&#34;Usage: java ParityExample filename&#34;</b>);
             System.exit(0); 
        } 

        <i>// create importer for the file argument</i> 
        String s = (String) args[0]; 
        MolImporter molimp = <b>new</b> MolImporter(s); 

        <i>// store the imported molecules in m</i> 
        Molecule m = <b>new</b> Molecule(); 

        <i>// counter for molecules</i> 
        <b>int</b> n = 0; 

        <b>while</b> (molimp.read(m)) {  <i>// read molecules from the file</i> 
            ++n;                  <i>// increment counter</i> 
            System.out.println(<b>&#34;mol &#34;</b> + n); 

            <i>// print parity information followed by the chirality</i> 
            <b>for</b> (<b>int</b> i = 0; i &lt; m.getAtomCount(); i++) {
                 <b>int</b> c = m.getChirality(i);
                 System.out.println( 
                    i + <b>&#34; Parity &#34;</b> + m.getParity(i) +
                     <b>&#34; Chirality &#34;</b> +
                     ((c == StereoConstants.CHIRALITY_R) ? <b>&#34;R&#34;</b> :
                          (c == StereoConstants.CHIRALITY_S) ? <b>&#34;S&#34;</b> :
                              (<b>&#34;&#34;</b> + c)) + <b>&#34; &#34;</b> + c);
             } 
        } 
    } 
} 
</pre>


<b>ExplicitHToChiralCenter.java</b> 
<p><pre class="sh_java">
<i>/*</i> 
 <i>* Copyright (c) 1998-2013 ChemAxon Ltd. All Rights Reserved.</i> 
 <i>*/</i> 

<b>import</b> java.io.IOException; 

<b>import</b> chemaxon.struc.Molecule; 
<b>import</b> chemaxon.struc.MolAtom; 
<b>import</b> chemaxon.formats.MolImporter; 
<b>import</b> chemaxon.struc.StereoConstants; 

<b>public</b> <b>class</b> ExplicitHToChiralCenter { 
    <i>/**</i> 
     <i>* Example to add Explicit H to Chiral centers only.</i> 
     <i>* </i><b>@param</b> <i>args   command line arguments</i> 
     <i>* </i><b>@throws</b> <i>java.io.IOException </i> 
     <i>* </i> 
     <i>* </i><b>@version</b> <i>5.1 04/24/2008</i> 
     <i>* </i><b>@since</b> <i>Marvin 5.1</i> 
     <i>* </i><b>@author</b> <i>Andras Volford</i> 
     <i>*/</i> 

    <i>// ODD and EVEN parity values</i> 
    <b>static</b> <b>int</b> ODD = StereoConstants.PARITY_ODD;     
    <b>static</b> <b>int</b> EVEN = StereoConstants.PARITY_EVEN; 
    
    <b>public</b> <b>static</b> <b>void</b> main(String[] args) <b>throws</b> IOException { 
        <b>if</b> (args.length &lt; 1) {
             System.err.println(<b>&#34;Usage: java ExplicitHToChiralCenter</b> 
               <b>filename&#34;</b>); 
            System.exit(0); 
        } 

        <i>// create importer for the file argument</i> 
        String s = (String) args[0]; 
        MolImporter molimp = <b>new</b> MolImporter(s); 

        <i>// store the imported molecules in m</i> 
        Molecule m = <b>new</b> Molecule(); 

        <b>while</b> (molimp.read(m)) {  <i>// read molecules from the file </i>
            <b>int</b> ac = m.getAtomCount(); 
 
           <i>// Atoms with odd or even parity</i> 
            MolAtom[] t = <b>new</b> MolAtom[ac];
             <b>int</b> n = 0;
             <b>for</b> (<b>int</b> i = 0; i &lt; ac; i++){
                 <b>int</b> p = m.getParity(i);
                 <b>boolean</b> add = p == ODD || p == EVEN; 

                <i>// if the atom has ODD or EVEN parity</i> 
                <b>if</b> (add) {
                     t[n++] = m.getAtom(i); 
                } 
            } 

            <i>// reduce atom array</i> 
            MolAtom[] a = <b>new</b> MolAtom[n];
            System.arraycopy(t, 0, a, 0, n); 
            
            <i>// add explicit H</i> 
            m.addExplicitHydrogens(0, a); 
             
            <b>if</b> (m.getDim() != 2)
                 m.clean(2, <b>null</b>);              

            <i>// write the result</i> 
            System.out.print(m.toFormat(<b>&#34;sdf&#34;</b>));
          } 
    } 
} 
</pre>

<a id="rgroup"/></a><b> Build R-group</b> 
<p>
<pre class="sh_java">
/*
 *  Copyright (c) 1998-2013 ChemAxon Ltd. All Rights Reserved.
 *  This software is the confidential and proprietary information of
 *  ChemAxon. You shall not disclose such Confidential Information
 *  and shall use it only in accordance with the terms of the agreements
 *  you entered into with ChemAxon.
 *  
 */
package chemaxon.examples.strucrep;

import java.io.IOException;

import chemaxon.calculations.clean.Cleaner;
import chemaxon.formats.MolExporter;
import chemaxon.formats.MolImporter;
import chemaxon.struc.MolAtom;
import chemaxon.struc.MolBond;
import chemaxon.struc.Molecule;
import chemaxon.struc.RgMolecule;

/**
 * Example class. Creates a basic RgMolecule.
 * 
 * <AUTHOR> Kendi
 * 
 */
public class BuildRgMolecule {

    public static void main(String[] args) throws IOException {

	// Create the root of the RgMolecule
	Molecule root = MolImporter.importMol("C1CCCCC1");

	// Create Rgroups
	MolAtom r1 = new MolAtom(MolAtom.RGROUP);
	r1.setRgroup(1);
	root.add(r1);
	root.add(new MolBond(r1, root.getAtom(0)));

	MolAtom r2 = new MolAtom(MolAtom.RGROUP);
	r2.setRgroup(2);
	root.add(r2);
	root.add(new MolBond(r2, root.getAtom(5)));

	// Create the RgMolecule
	RgMolecule rgMol = new RgMolecule();
	rgMol.setRoot(root);

	// Add Rgroup definitions
	Molecule rg = MolImporter.importMol("O");
	rg.getAtom(0).addRgroupAttachmentPoint(1, 1);
	rgMol.addRgroup(1, rg);

	rg = MolImporter.importMol("N");
	rg.getAtom(0).addRgroupAttachmentPoint(1, 2);
	rgMol.addRgroup(1, rg);

	rg = MolImporter.importMol("CC");
	rg.getAtom(0).addRgroupAttachmentPoint(1, 1);
	rgMol.addRgroup(2, rg);

	Cleaner.clean(rgMol, 2, null);

	System.out.println(MolExporter.exportToFormat(rgMol, "mrv:P"));
    }

}
</pre>


<a id="rxnmolecule"/></a><b> Build a Reaction</b> 
<pre class="sh_java">
/*
 *  Copyright (c) 1998-2013 ChemAxon Ltd. All Rights Reserved.
 *  This software is the confidential and proprietary information of
 *  ChemAxon. You shall not disclose such Confidential Information
 *  and shall use it only in accordance with the terms of the agreements
 *  you entered into with ChemAxon.
 *  
 */
package chemaxon.examples.strucrep;

import java.io.IOException;

import chemaxon.calculations.clean.Cleaner;
import chemaxon.formats.MolExporter;
import chemaxon.formats.MolImporter;
import chemaxon.struc.Molecule;
import chemaxon.struc.RxnMolecule;

/**
 * Example class. Creates a basic RxnMolecule.
 * 
 * <AUTHOR> Kendi
 * 
 */
public class BuildRxnMolecule {

    public static void main(String[] args) throws IOException {

	// Create an empty reaction
	RxnMolecule mol = new RxnMolecule();

	// Create the components
	Molecule reactant1 = MolImporter.importMol("CC(=C)C");
	Molecule reactant2 = MolImporter.importMol("Cl");
	Molecule agent = MolImporter.importMol("CCOCC");
	Molecule product = MolImporter.importMol("C(Cl)(C)(C)C");

	// Add the components
	mol.addComponent(reactant1, RxnMolecule.REACTANTS);
	mol.addComponent(reactant2, RxnMolecule.REACTANTS);
	mol.addComponent(agent, RxnMolecule.AGENTS);
	mol.addComponent(product, RxnMolecule.PRODUCTS);

	// Calculate coordinates.
	Cleaner.clean(mol, 2, null);

	// Change the reaction arrow type.
	mol.setReactionArrowType(RxnMolecule.EQUILIBRIUM);

	System.out.println(MolExporter.exportToFormat(mol, "mrv:P"));
    }
}
</pre>

<p> <strong> Reaction of aromatic nitration </strong>  
<pre class="sh_java">
  
package chemaxon.examples.strucrep

import chemaxon.struc.*;
import chemaxon.formats.MolImporter;
import chemaxon.formats.MolFormatException;

/**
 * Example class for structure manipulation. 
 * Creates a simple reaction.
 *
 * <AUTHOR> Volford
 * 
 */
public class AromaticNitration {

    public static void main(String[] args) {

        // create an empty Molecule
        RxnMolecule m = new RxnMolecule();

        try{
            Molecule reactant = MolImporter.importMol("c1ccccc1");
            Molecule agent = MolImporter.importMol("N(O)(=O)=O.S(O)(O)(=O)=O");
            Molecule product = MolImporter.importMol("c1ccccc1N(=O)=O");

            m.addComponent(reactant, RxnMolecule.REACTANTS);
            m.addComponent(agent, RxnMolecule.AGENTS);
            m.addComponent(product, RxnMolecule.PRODUCTS);
            m.addComponent(MolImporter.importMol("O"), RxnMolecule.PRODUCTS);
            System.out.println(m.toFormat("mrv"));

        } catch (MolFormatException e) {
            System.err.println("Format not recognised.");
        }
    }
}

</pre>

<p> <strong> Aromatization </strong>  
<pre class="sh_java">
/*
 *  Copyright (c) 1998-2013 ChemAxon Ltd. All Rights Reserved.
 *  This software is the confidential and proprietary information of
 *  ChemAxon. You shall not disclose such Confidential Information
 *  and shall use it only in accordance with the terms of the agreements
 *  you entered into with ChemAxon.
 *  
 */
package chemaxon.examples.strucrep;

import chemaxon.formats.MolFormatException;
import chemaxon.formats.MolImporter;
import chemaxon.struc.MolBond;
import chemaxon.struc.Molecule;
import chemaxon.struc.MoleculeGraph;

/**
 * Example class for aromatization.
 * 
 * <AUTHOR> Kendi
 * 
 */
public class AromatizationExample {

    public static void main(String[] args) throws MolFormatException {

	// Import a molecule from smiles
	Molecule mol = MolImporter.importMol("O=C1NC=CC=C1");

	// Call basic aromatization method
	mol.aromatize(MoleculeGraph.AROM_BASIC);
	System.out.println("Aromatic: " + isAromatic(mol));

	// Call general aromatization method
	mol.aromatize(MoleculeGraph.AROM_GENERAL);
	System.out.println("Aromatic: " + isAromatic(mol));
    }

    /**
     * Check if the given molecule is aromatic or not.
     * 
     * @param m
     * @return true if the molecule is aromatic, false otherwise
     */
    public static boolean isAromatic(Molecule m) {
	boolean aromatic = false;
	for (MolBond b : m.getBondArray()) {
	    if (b.getType() == MolBond.AROMATIC) {
		aromatic = true;
		break;
	    }
	}
	return aromatic;
    }
}
</pre>

<p> <strong> Periodic System </strong>  
<pre class="sh_java">
 
 /*
 *  Copyright (c) 1998-2013 ChemAxon Ltd. All Rights Reserved.
 *  This software is the confidential and proprietary information of
 *  ChemAxon. You shall not disclose such Confidential Information
 *  and shall use it only in accordance with the terms of the agreements
 *  you entered into with ChemAxon.
 *  
 */
package chemaxon.examples.strucrep;

import chemaxon.struc.PeriodicSystem;
import static chemaxon.struc.PeriodicSystem.*;

/**
 * Example methods of the PeriodicSystem class.
 * 
 * <AUTHOR> Kendi
 * 
 */
public class PeriodicSystemExample {

    public static void main(String[] args) {

	System.out.println("Atomic number of C: "
		+ PeriodicSystem.findAtomicNumber("C"));

	System.out.println("Mass of C: " + PeriodicSystem.getMass(C));

	System.out.println("Column of C: " + PeriodicSystem.getColumn(C));

	System.out.println("Number of C isotopes: "
		+ PeriodicSystem.getIsotopeCount(C));
    }
}
</pre>

<p><a id="sgroup">
<strong> Build molecules with Superatom S-group and Repeating unit S-group  
</strong> </a>

<pre class="sh_java">
/*
 *  Copyright (c) 1998-2013 ChemAxon Ltd. All Rights Reserved.
 *  This software is the confidential and proprietary information of
 *  ChemAxon. You shall not disclose such Confidential Information
 *  and shall use it only in accordance with the terms of the agreements
 *  you entered into with ChemAxon.
 *  
 */
package chemaxon.examples.strucrep;

import java.io.IOException;

import chemaxon.calculations.clean.Cleaner;
import chemaxon.formats.MolExporter;
import chemaxon.formats.MolImporter;
import chemaxon.marvin.util.CleanUtil;
import chemaxon.struc.Molecule;
import chemaxon.struc.Sgroup;
import chemaxon.struc.graphics.MBracket;
import chemaxon.struc.sgroup.RepeatingUnitSgroup;
import chemaxon.struc.sgroup.SuperatomSgroup;

/**
 * Example class for creating sgroups.
 * 
 * <AUTHOR> Kendi
 * 
 */
public class BuildMoleculeWithSgroupExample {

    public static void main(String[] args) throws IOException {

	// Import the molecule
	Molecule mol = MolImporter.importMol("C1=CC=CC=C1C(C)CC");
	Cleaner.clean(mol, 2, null);

	// Create Superatom S-group.
	SuperatomSgroup superSg = new SuperatomSgroup(mol);

	for (int i = 0; i &lt; 6; i++) {
	    mol.setSgroupParent(mol.getAtom(i), superSg, true);
	}
	superSg.setSubscript("Ph");
	
	//Add attachment point. The attach atom is the fifth of the molecule.
	//The crossing bond is also fifth one of the molecule. 
	superSg.addAttachmentPoint(mol.getAtom(5), 1);
	superSg.addCrossingBond(mol.getAtom(5), mol.getBond(5));
	
	//Create Repeating unit S-group
	RepeatingUnitSgroup repeatingSg = new RepeatingUnitSgroup(mol, "ht",
		Sgroup.ST_SRU);
	
	// Set the RepeatingUnitSgroup atoms.
	for (int i = 0; i &lt; 9; i++) {
	    if (mol.getAtom(i).getBondCount() &gt;  1) {
		mol.setSgroupParent(mol.getAtom(i), repeatingSg, true);
	    }
	}
	repeatingSg.addStarAtoms();
	
	// Set the parent-child relation.
	repeatingSg.addChildSgroup(superSg);

	
	CleanUtil.generateBracketCoords(repeatingSg, MBracket.T_SQUARE);

	System.out.println(MolExporter.exportToFormat(mol, "mrv:P"));
    }

}
</pre>


<p>&nbsp;</p>
  <p>&nbsp;</p>
 
 <table summary="next pages">
   <tr style="text-align:left">
     <td width="250" valign="middle"> 
       <p style="text-align: center;">
        <a href="graphic_objects.html">
          Graphic Object handling</a><br>
        <i>Previous chapter</i>
       </p>   
     </td>
     <td width="250" valign="middle">
      <p style="text-align: center;">
       <a href="core_index.html"> Table of contents</a> <br>
       &nbsp;
      </p>
     </td>
     <td width="250"valign="middle" >
      <p style="text-align: center;"> 
       &nbsp; <br>
       &nbsp;
      </p>
     </td>
   </tr>
 </table> 


</BODY>
</HTML>
