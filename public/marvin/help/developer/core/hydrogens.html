<html>
<head>
<meta NAME="description" CONTENT="Implicit/ Explicit Hydrogens">
<meta NAME="author" CONTENT="Andras Volford">
<link REL ="stylesheet" TYPE="text/css" HREF="../../marvinmanuals.css" TITLE="Style">
<title>Implicit/ Explicit Hydrogens</title>
</head>
<body>

<h1><a class="anchor" NAME="hydrogens">Implicit / Explicit / Query Hydrogens</a></h1>
<p>
The Hydrogen atoms are included in the molecule implicitly, explicitly or both. 
The implicit hydrogen count is calculated from the valence of the atom. 
It equals the valence of the atom minus the valence calculated from the bond 
connections. It is accurate if and only if there is no further modification 
on the structure since the last valence check.

<p>
The following example demonstrates how to get the implicit / explicit hydrogen 
count of the atoms:

<pre>
<code>
import chemaxon.calculations.Hydrogenize;
//import a simple chain
Molecule mol = MolImporter.importMol("CCNCCCCCNCC");

// check valences 
mol.valenceCheck();

for (int i = 0; i &lt; mol.getAtomCount(); i++){
    MolAtom atom = mol.getAtom(i);
    int implicitH = atom.getImplicitHcount();
    int explicitH = atom.getExplicitHcount();
    System.out.println(i+"th atom has "+implicitH+" implicit and "
        +explicitH+" explicit Hydrogens.");
}
</code>
</pre>

<p>
The implicit hydrogens can be converted to explicit ones and vice versa.


<h2>Converting implicit Hydrogens to explicit </h2>
<p>
The following methods are available in chemaxon.calculations.Hydrogenize class to convert 
implicit hydrogens to explicit ones:
<ul>
<li> Hydrogenize.addHAtoms(MoleculeGraph molecule, MolAtom[] atoms, int f)</li> 
<li> Hydrogenize.addHAtoms(MoleculeGraph molecule)</li>
</ul>

<p>
In the first method the coordinate refinement to avoid atom collisions 
can be skipped using the OMIT_POSTCLEAN option.<br>

<p>
You can convert implicit Hydrogens to explicit ones without additional cleaning:
<pre>
<code>
//import a simple chain
Molecule mol = MolImporter.importMol("methylhexene.mol");
Hydrogenize.addHatoms(mol, null, MoleculeGraph.OMIT_POSTCLEAN);
</code>
</pre>

<p>
<table border="0" cellspacing="0" cellpadding="5" class="grid">
 <tr>
  <td align=center><img src="images/image071.png" width="191" height="108"></td>
  <td align=center><img src="images/image072.png" width="196" height="121"></td>
  <td align=center><img src="images/image073.png" width="214" height="126"></td> </tr>
 <tr>
        <td align=center>original methylhexene molecule</td>
        <td align=center>with OMIT_POSTCLEAN option</td>
        <td align=center>without OMIT_POSTCLEAN option</td>
 </tr>
</table>
<p>


<h2>Converting explicit Hydrogens to implicit </h2>
<p>
The following methods are available in chemaxon.calculations.Hydrogenize class to convert 
explicit hydrogens to implicit ones:
<ul>
<li> Hydrogenize.removeHAtoms(MoleculeGraph molecule, MolAtom[] atoms, int f, boolean add)</li>
<li> Hydrogenize.removeHAtoms(MoleculeGraph molecule, MolAtom[] atoms, int f)</li>
<li> Hydrogenize.removeHAtoms(MoleculeGraph molecule, int f)</li>
<li> Hydrogenize.removeHAtoms(MoleculeGraph molecule)</li>
</ul>

<p>
In the first three methods it is possible to define which type of Hydrogen atoms 
should be implicitized:
<ul>
<li>ALL_H - all Hydrogen atoms</li>
<li>LONELY_H - Hydrogen atom without connection</li>
<li>ISOTOPE_H - isotope Hydrogen atom</li>
<li>CHARGED_H - charged Hydrogen atom</li>
<li>RADICAL_H - Hydrogen radical</li>
<li>MAPPED_H - Hydrogen with atom map</li>
<li>WEDGED_H - Hydrogen with wedged bond</li>
<li>HCONNECTED_H - Hydrogen connected to Hydrogen atom</li>
<li>CTSPECIFIC_H - Hydrogen with CIS/TRANS information</li>
<li>POLYMERENDGROUP_H - Hydrogen connected to Sgroup (not Data or Superatom)</li>
<li>SGROUPEND_H - Hydrogen connected to a SuperatomSgroup</li>
<li>VALENCEERROR_H - Hydrogen connected to an atom which has valence error</li>
<li>SGROUP_H - Hydrogen which is the only atom in an Sgroup</li>
<li>BRIDGEHEAD_H - Hydrogen connected to a bridgehead atom</li>
</ul>

<p>
The following example shows how to convert non-charged explicit hydrogens 
to implicit ones:

<pre>
<code>
Hydrogenize.removeHAtoms(molecule, MoleculeGraph.ALL_H &amp; ~MoleculeGraph.CHARGED_H);
</code>
</pre>

<h2>Query hydrogens</h2>

<p>
For query molecules it is possible to define the number of Hydrogen atoms 
needed to have in the target structure. Either define it explicitly by 
adding the Hydrogen atoms to the specified atoms or implicitly by setting 
implicit hydrogen count. If the implicit hydrogen count is specified 
then it is stored in the atom and not calculated from the valence any more.<br>

<p>
The example below shows how to define the query implicit hydrogen count:
<pre>
<code>
atom.setImplicitHcount(2);
</code>
</pre>

</body>
</html>
