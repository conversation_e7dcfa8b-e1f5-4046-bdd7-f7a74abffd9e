<!DOCTYPE html><HTML>
 <head>
   <META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
   <script type="text/javascript" src="../../../examples/sh_main.js"></script>
   <script type="text/javascript" src="../../../examples/sh_java.js"></script>
   <script type="text/javascript" src="../../../examples/sh_javascript.js">
      </script>
   <link REL ="stylesheet" TYPE="text/css" 
         HREF="../../../examples/sh_nedit.css" TITLE="Style">
   <title> Converting explicit Hydrogens to implicit  </title>
   <link rel=stylesheet type="text/css" href="../../marvinmanuals.css">
 </head>

<BODY onload="sh_highlightDocument();"  >
<h1>Converting explicit Hydrogens to implicit</h1> 
<p>The following methods are available in 
<code> chemaxon.calculations.Hydrogenize </code> class to convert 
explicit hydrogens to implicit ones: </p> 
<ul>
<li><code> 
 Hydrogenize.convertExplicitHToImplicit(MoleculeGraph molecule, MolAtom[] atoms, int f, boolean add)  </code> </li>
<li><code> 
    Hydrogenize.convertExplicitHToImplicit(MoleculeGraph molecule, MolAtom[] atoms, int f)
    </code> </li>
<li><code> Hydrogenize.convertExplicitHToImplicit(MoleculeGraph molecule, int f)  
    </code> </li>
<li><code>
   Hydrogenize.convertExplicitHToImplicit(MoleculeGraph molecule)</code></li>
</ul>

<p>In the first three methods it is possible to define which type of Hydrogen 
atoms should be implicitized.
 By default, <code> convertExplicitHToImplicit() </code> removes those Hydrogens that 
removal does not cause 
information loss. That means for example that leaving group hydrogens are never implicitized. 
This behavior can be extended with the following flags.
 </p>
<ul>
 <li><code>LONELY_H</code>  – include Hydrogen atom without connection as well;
        </li>
 <li><code>ISOTOPE_H</code>  – include isotope Hydrogen atom as well; 
        </li>
 <li><code>CHARGED_H</code>  – include charged Hydrogen atom as well; 
        </li>
 <li><code>RADICAL_H</code>  – include Hydrogen radical as well; 
        </li>
 <li><code>MAPPED_H</code>  – include Hydrogen with atom map as well; 
        </li>
 <li><code>WEDGED_H</code>  – include Hydrogen with wedged bond as well; 
        </li>
 <li><code>HCONNECTED_H </code> – include Hydrogen connected to Hydrogen atom 
as well; </li>
 <li><code>CTSPECIFIC_H </code> – include Hydrogen atom(s) connecting alone 
to one side of a double bond with specified CIS or TRANS stereo information.
as well; </li>
 <li><code>POLYMERENDGROUP_H</code> – include Hydrogen atom(s) which have a 
neighbor that is in an Sgroup (not DataSgroup or SuperatomSgroup) as well; 
        </li>
 <li><code>SGROUPEND_H</code> – include Hydrogen atom(s) which have a neighbor
    that is in SuperatomSgroup as well; 
        </li>
 <li><code>VALENCEERROR_H</code> - include Hydrogen atoms that connects to an 
atom that has valence error as well; 
        </li>
 <li><code>SGROUP_H</code> - include Hydrogen atom that is the only member of an S-group;
 <li><code>BRIDGEHEAD_H</code> - include Hydrogen connected to a bridgehead atom
 <li><code>ALL_H</code>  – all Hydrogen atoms. </li>
 </ul>

<h4>Examples</h4>
<ol>
 <li> Convert non-charged explicit hydrogens
to implicit ones: 

 <blockquote>
    <pre class="sh_java">
Hydrogenize.convertExplicitHToImplicit(molecule, MolAtom.ALL_H &amp; ~MolAtom.CHARGED_H);
    </pre>
 </blockquote>
 </li>
 
 <li>
  Remove explicit Hydrogens that connect to atoms having specified 
  CIS or TRANS stereo information.
  <blockquote>
    <pre class="sh_java">
Hydrogenize.convertExplicitHToImplicit(molecule, MolAtom.CTSPECIFIC_H);
    </pre>
    <table>
      <tr>
        <td>
           <img src="images/CTSpec.png">
        </td>
        <td>
           <img src="images/CTSpecwoH.png">
        </td>
      </tr>
    </table>
 </blockquote> 
 </li>

 <li>
  Remove explicit hydrogenes that have neighbor in SRU S-group:
  <blockquote>
    <pre class="sh_java">
Hydrogenize.convertExplicitHToImplicit(molecule, MolAtom.POLYMERENDGROUP_H);
    </pre>
    <table>
      <tr>
        <td>
           <img src="images/NewSRU.png">
        </td>
        <td>
           <img src="images/NewSRUwoH.png">
        </td>
      </tr>
    </table>
    All explicit hydrogens are converted to implicit hydrogens.
 </blockquote> 
 </li>
   
 <li>
  Remove those explicit hydrogenes that have 
  neighbor in Superatom S-group but 
  the explicit hydrogen itself is not in that S-group:
  <blockquote>
    <pre class="sh_java">
Hydrogenize.convertExplicitHToImplicit(molecule, MolAtom.SGROUPEND_H);
    </pre> 
    <table>
      <tr>
        <td>
           <img src="images/SuperatomSGWithExpHConn_g.png">
        </td>
        <td>
           <img src="images/SuperatomSGWithoutExpHConn_g.png">
        </td>
      </tr>
    </table>
    After the removal of the explicit hydrogen, an S-group attachment point is
    taken to the S-group atom. All of the explicit hydrogens
    in the S-group will be replaced by implicit ones as well. 
  </blockquote> 
 </li>

</ol>



 <p>&nbsp;</p>
 <p>&nbsp;</p>
 
 <table summary="next pages">
   <tr style="text-align:left">
     <td width="250" valign="middle"> 
       <p style="text-align: center;">
        <a href="convert_implicit_to_explicit.html">
          Converting implicit Hydrogens to explicit</a><br>
        <i>Previous chapter</i>
       </p>   
     </td>
     <td width="250" valign="middle">
      <p style="text-align: center;">
       <a href="core_index.html"> Table of contents</a> <br>
       &nbsp;
      </p>
     </td>
     <td width="250"valign="middle" >
      <p style="text-align: center;"> 
       <a href="query_hydrogens.html"> 
           Query Hydrogens<a><br>
       <i>Next chapter</i>
      </p>
     </td>
   </tr>
 </table>



</BODY>
</HTML>
