<html>
<head>
<meta NAME="description" CONTENT="Graphic object handling">
<meta NAME="author" CONTENT="Erika Biro">
<link REL ="stylesheet" TYPE="text/css" HREF="../../marvinmanuals.css" TITLE="Style">
<title>Graphic object handling</title>
</head>
<body>

<h1><a class="anchor" NAME="graphic-objects">Graphic Object handling</a></h1>

Graphic objects are represented by the following classes in Marvin:
<ul>
<li>MNameTexBox: contains the generated IUPAC name, only one can be used at a time. </li>
<li>MRArrow: used in reaction structures.</li>
<li>MTextBox: represents simple text-box.</li>
<li>MPolyline: represents simple lines and polylines, single and two headed graphic arrows.</li>
<li>MRectangle: graphic rectangle object.</li>
<li>MBracket: bracket object (with four different types)</li>
</ul>

We differentiate two kinds of graphic objects: simple graphic object and graphic object connected
to chemical structures like reaction arrow in chemical reaction, or brackets in groups. We store the
simple graphic objects in an MDocument object and we store connected object in the related 
structure. 
After creating the structure we should add the object to its container class. 
In case of simple
graphic object, the container is always the MDocument object, 
and the graphic object should be added by method 
<code>MDocument.addObject(MObject o)</code>. 
In case of graphic object connected to chemical structures, 
the object is always stored at the related chemical object: 
in case of reaction arrow (represented by MRArrow) 
the container class is the reaction (represented by the RxnMolecule).
However, all graphic object are available for processing with MDocument methods: 
<ul>
<li><code>MDocument.getConnectedObjectCount() </code>and</li>
<li><code>MDocument.getConnectedObject(int i)</code></li>
</ul>
Simple graphic objects:
<ul>
<li>MNameTexBox</li>
<li>MTextBox</li>
<li>MPolyline</li>
<li>MRectangle</li>
<li>MBracket</li>
</ul>

Graphic objects connected to chemical structure:
<ul>
<li>MRArrow (used in reaction molecules)</li>
</ul>

<p>&nbsp;</p>
  <p>&nbsp;</p>
 
 <table summary="next pages">
   <tr style="text-align:left">
     <td width="250" valign="middle"> 
       <p style="text-align: center;">
        <a href="sets.html">
          Atom and bond-set handling</a><br>
        <i>Previous chapter</i>
       </p>   
     </td>
     <td width="250" valign="middle">
      <p style="text-align: center;">
       <a href="core_index.html"> Table of contents</a> <br>
       &nbsp;
      </p>
     </td>
     <td width="250"valign="middle" >
      <p style="text-align: center;"> 
       <a href="appendix.html"> 
           Code examples</a><br>
       <i>Next chapter</i>
      </p>
     </td>
   </tr>
 </table> 


</body>
</html>