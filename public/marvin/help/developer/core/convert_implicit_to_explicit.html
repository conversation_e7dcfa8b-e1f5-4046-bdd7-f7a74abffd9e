<!DOCTYPE html><HTML>
 <head>
  <meta NAME="author" CONTENT="<PERSON><PERSON>">
  <script type="text/javascript" src="../../../examples/sh_main.js"></script>
  <script type="text/javascript" src="../../../examples/sh_java.js"></script>
  <script type="text/javascript" src="../../../examples/sh_javascript.js">
    </script>
  <link REL ="stylesheet" TYPE="text/css" 
        HREF="../../../examples/sh_nedit.css" TITLE="Style">
        <META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
        <title> Converting implicit Hydrogens to explicit  </title>
        <link rel=stylesheet type="text/css" href="../../marvinmanuals.css">
 </head>

<BODY onload="sh_highlightDocument();">
<h1>Converting implicit Hydrogens to explicit</h1> 
<p>The following methods are available in  
<code> chemaxon.calculations.Hydrogenize </code>  
class to convert implicit hydrogen atoms  to explicit ones: </p>
<ul>
<li>
 <code> Hydrogenize.convertImplicitHToExplicit(MoleculeGraph molecule, MolAtom[] atoms, int f)
  </code>   
 </li> 
<li><code> Hydrogenize.convertImplicitHToExplicit(MoleculeGraph molecule)   </code></li>
</ul>

<p>
In the first method the coordinate refinement to avoid atom collisions
can be skipped using the
<code>OMIT_POSTCLEAN</code> option. </p>

<p>
You can convert implicit Hydrogens to explicit ones without additional 
cleaning:
<blockquote>
<pre class="sh_java">
//import a simple chain
Molecule mol = MolImporter.importMol("methylhexene.mol");
Hydrogenize.convertImplicitHToExplicit(mol, null, MoleculeGraph.OMIT_POSTCLEAN);
</pre>
</blockquote>
</p>

<p>
<table border="0" cellspacing="0" cellpadding="5" class="grid" >
 <tr>
  <td align=center>
    <img src="images/image071.png" width="191" height="108">
  </td>
  <td align=center>
    <img src="images/image072.png" width="196" height="121">
  </td>
  <td align=center>
   <img src="images/image073.png" width="214" height="126">
  </td> 
 </tr>
 <tr>
        <td align=center>original methylhexene molecule</td>
        <td align=center>with OMIT_POSTCLEAN option</td>
        <td align=center>without OMIT_POSTCLEAN option</td>
 </tr>
</table>
</p>

<p>&nbsp;</p>
 <p>&nbsp;</p>
 
 <table summary="next pages">
   <tr style="text-align:left">
     <td width="250" valign="middle"> 
       <p style="text-align: center;">
        <a href="impl_expl_query_hydrogens.html">
          Implicit, Explicit and Query Hydrogens</a><br>
        <i>Previous chapter</i>
       </p>   
     </td>
     <td width="250" valign="middle">
      <p style="text-align: center;">
       <a href="core_index.html"> Table of contents</a> <br>
       &nbsp;
      </p>
     </td>
     <td width="250"valign="middle" >
      <p style="text-align: center;"> 
       <a href="convert_explicit_to_implicit.html"> 
           Converting explicit Hydrogens to implicit<a><br>
       <i>Next chapter</i>
      </p>
     </td>
   </tr>
 </table>

</BODY>
</HTML>
