<!DOCTYPE html><HTML>
 <head>
        <META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
        <title>Examples for the Molecule representation</title>
        <link rel=stylesheet type="text/css" href="../../marvinmanuals.css">
 </head>

<BODY>
 <h1> Examples for the Molecule representation </h1>
  <p>In this part several chemical structures are used  to  illustrate  the  
  internal  representation  of  molecule graphs.  
  The diagrams below are not complete yet demonstrate the most relevant data 
  items and their internal relationships. 
  Examples range from the simplest molecule to complex reaction schemas. </p>

 <h2>Simple molecular structure</h2> 
<p> As a first example take the water molecule. <br> </p>
<p style="text-align: center; margin: 7px 0px 7px 0px;">
<img src="images/simpl_mol_struc_h2o.png" width="158" height="77" border="0" 
     alt="str_rep01" style="margin:0 auto;margin:0px;"> </p>  
<p>Labels next to the atom symbols are the internal atom indexes assigned 
by the system 
(note, that actual values of the indexes are the displayed values minus 1, 
as representation starts from 0). <br>
 <p style="text-align: center; margin: 0px 0px 0px 38px;">
 <img src="images/simpl_mol_struc_diagr.png" width="514" height="375" 
      border="0" alt="Image3" style="margin:0 auto;margin:0px;"></p>
<p>In this diagram each box represents an object of the class named in the 
header row.  Names and values in the bottom part of the box are data fields 
(attributes, class members) and actual values that represent the molecule drawn 
(water, in this example).  Embedded boxes represent data objects that  
constitute the outer object: water consists of two bonds 
(MolBond objects) and three atoms (MolAtom objects). 
Lines connecting the boxes represent associations between the objects: 
each pair of atoms is linked by a bond. </p>

<p> In the next examples the notation of the Molecule object is used, 
but only from a “bird’s eye view”, that is, without considering its internal
 finer scale structure, which, however, is always present. </p> 

<h2>Molecules with S-groups</h2> 
<p>S-groups are structural entities within a molecule. S-group provides a 
   base implementation to represent abbreviations,  multiple  groups,  
   polymers,  attached  data,  coordinated  structures,  position  variations, 
   generic groups, ordered and unordered mixtures of components. 
   Abbreviated and multiple groups are often drawn in an abbreviated or 
   condensed form in the structural formula. </p>

<p>In  the  example  below  the  monomer  unit  of  polystyrene  is  used  
   to  demonstrate  the  internal representation of S-groups in ChemAxon core 
   classes. </p>   
<p style="text-align: center;">
  <img src="images/mol_with_sgroup_1.png" width="187" height="237" border="0" 
       style="margin:0 auto;margin:0px;"></p>

<p>This monomer consists of two structural parts that can be denoted by an 
S-group: the phenol ring and the hydrocarbon chain. </p>
<p style="text-align: center; margin: 0px 0px 0px 47px;">
  <img src="images/mol_with_sgroup_2.png" width="525" height="325" border="0"
       style="margin:0 auto;margin:0px;"></p>

<p>The structure contained in the S-group is represented by a Molecule object 
just as any other structure, 
since the S-group can simply be regarded as a particular structural entity 
within the molecule.  
Thus the internal representation of the Molecule is extended by two 
S-group objects that depict the type of the S-group  and  its  abbreviation  
(symbolic  name)  if  one  exists.    
Each  S-group  object  refers  to  its constituent atoms. </p>

<h2>Molecules with R-groups</h2> 
<p>An R-group is a collection of possible substituent fragments that can be 
part of a molecule at a specific location. </p>
<p style="text-align: center;">
 <img src="images/mol_with_rgroup_1.png" border="0" 
      style="margin:0 auto;margin:0px;"></p> 
<p>The complexity of such chemical structure representation cannot be captured  
by one  single  Molecule object. </p>
<p style="text-align: center; margin: 0px 0px 0px 47px;">
   <img src="images/mol_with_rgroup_2.png" width="516" height="334" border="0" 
        style="margin:0 auto;margin:0px;"></p>

<p>Instead,  an  RgMolecule  object  is  introduced  
(which,  however,  inherits  Molecule)  and  this  object embeds all parts of 
the structure: the core (or  scaffold)  structure,  referred  to  as  the  
“root” molecule, and all elements of each R-group as Molecule objects. 

<h2>Reaction schemas</h2> 
<p>A reaction is  a  collection  of  reagents,  products  and  agents.  
It  is  represented  by  the  RxnMolecule object.  The  reagent,  
product  and  agent  elements  are  Molecule  objects  embedded  into  the 
RxnMolecule object. The type of an element is defined by its  relative  
position to  the  reaction arrow. 
The present representation is capable to express single step reactions only.  
<p style="text-align: center;">
 <img src="images/reaction_schemas1.png" width="518" height="179" border="0" >
</p>
<p style="text-align: center;">
 <img src="images/reaction_schemas2.png" width="531" height="253" border="0" 
      style="margin:0 auto;margin:0px;"></p>

<h2>Reaction schemas with R-groups</h2> 
Embedding of R-groups into a reaction is possible by setting the reaction as 
the root of an RgMolecule. 
<p style="text-align: center;">
  <img src="images/reaction_schemas_with_rgroup.png" width="621" height="149" 
       vspace="1" hspace="1" border="0" alt=""></p>



 <p>&nbsp;</p>
 <p>&nbsp;</p>
 <p>&nbsp;</p>
 <p>&nbsp;</p>
 
 <table summary="next pages">
   <tr style="text-align:left">
     <td width="250" valign="middle"> 
       <p style="text-align: center;">
        <a href="reactions.html">Representation of reactions</a><br>
        <i>Previous chapter</i>
       </p>   
     </td>
     <td width="250" valign="middle">
      <p style="text-align: center;">
       <a href="core_index.html"> Table of contents</a> <br>
       &nbsp;
      </p>
     </td>
     <td width="250"valign="middle" >
      <p style="text-align: center;"> 
       <a href="aromaticity.html"> Aromaticity</a><br>
       <i>Next chapter</i>
      </p>
     </td>
   </tr>
 </table>


</BODY>
</HTML>
