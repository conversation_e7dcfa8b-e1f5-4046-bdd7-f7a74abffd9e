<html>
<head>
<meta NAME="description" CONTENT="MarvinSpace Developer's Guide">
<meta NAME="author" CONTENT="Judit Papp">
<link REL ="stylesheet" TYPE="text/css" HREF="../../marvinmanuals.css" TITLE="Style">
<title>MarvinSpace Developer's Guide</title>
</head>
<body>
<h1>MarvinSpace Developer's Guide</h1>

<h2>SurfaceColoring</h2>
<h3>Code examples of typical usages</h3>
<ol>
<li><b>Built-in CPK</b> coloring<br>
    <code>
    SurfaceColoring sc = new SurfaceColoring(molecules, surface, precision);<br>
    sc.doColoring(SurfaceColoring.BUILT_IN_CPK_MAPPING);
    </code>
</li>
<li>Built-in residue coloring<br>
    <code>
    SurfaceColoring sc = new SurfaceColoring(molecules, surface, precision);<br>
    sc.doColoring(SurfaceColoring.BUILT_IN_RESIDUE_MAPPING);
    </code>
</li>
<li>Built-in chain coloring<br>
    <code>
    SurfaceColoring sc = new SurfaceColoring(molecules, surface, precision);<br>
    sc.doColoring(SurfaceColoring.BUILT_IN_CHAIN_MAPPING);
    </code>
</li>
<li>Coloring by <b>parametrized built-in electrostaic potential</b> property and built-in colors<br>
    <code>
    SurfaceColoring sc = new SurfaceColoring( molecules, surface, precision );<br>
    sc.setEnrtyRadiusMode( SurfaceColoring.RADIUS_VDW_EXTENDED );<br>
    surfaceColoring.setAtomRadiusExtension( 1 );<br>
    sc.setPropertyValueDecreasement( SurfaceColoring.DECREASE_RECIPROCAL_SQUARE );<br>
    sc.setReciprocalConstant( 8.854 );<br>
    sc.setWeightMode( SurfaceColoring.SUM_OF_VALUES );<br>
    sc.setBuiltInColorMapperMethod(SurfaceColoring.COLOR_MAPPER_RED_AND_BLUE);<br>
    sc.setPropertyMethod( MoleculeIterators.AtomPropertyInterface.class.getMethod(
                "getPartialAtomCharge", new Class[] {int.class} ) );<br>
    sc.doColoring();
    </code>
</li>
<li>Coloring by custom atom property (of built-in MoleculeIterators.AtomPropertyInterface class)
    and built-in rainbow colors<br>
   <code>
       SurfaceColoring sc = new SurfaceColoring(molecules, surface, precision);<br>
       sc.setWeightMode( SurfaceColoring.DISTANCE_WEIGHTED );<br>
       sc.setPropertyMethod( MoleculeIterators.AtomPropertyInterface.class.getMethod(
                   "getType", new Class[] {int.class} ) );<br>
       sc.setBuiltInColorMapperMethod( SurfaceColoring.COLOR_MAPPER_RAINBOW );<br>
       sc.doColoring();
   </code>
</li>
<li>Coloring by custom atom property and <b>parametrized colors</b><br>
   <code>
       SurfaceColoring sc = new SurfaceColoring(molecules, surface, precision);<br>
       sc.setWeightMode( SurfaceColoring.DISTANCE_WEIGHTED );<br>
       sc.setPropertyMethod( MoleculeIterators.AtomPropertyInterface.class.getMethod(
                   "getType", new Class[] {int.class} ) );<br>
       sc.setHomogeneousPaletteColors( new byte[][] {Colors.lightgray, Colors.yellow} );<br>
       sc.doColoring();
   </code>
</li>
<li>Coloring by custom property and <b>custom colors</b><br>
   <code>
       chemaxon.marvin.space.util.Palette palette =
               new chemaxon.marvin.space.util.Palette(0, 16);<br>
       palette.clear();<br>
       palette.putColor( Colors.lightgray );<br>
       palette.putColor( Colors.yellow );<br>
       SurfaceColoring sc = new SurfaceColoring(molecules, surface, precision);<br>
       sc.setWeightMode( SurfaceColoring.DISTANCE_WEIGHTED );<br>
       sc.setPropertyMethod( MoleculeIterators.AtomPropertyInterface.class.getMethod(
                   "getType", new Class[] {int.class} ) );<br>
       sc.setPropertyColorMapperMethod(
               chemaxon.marvin.space.util.Palette.class.getMethod("getByteColor", new Class[] {double.class}) );<br>
       sc.setPropertyColorObject(palette);<br>
       sc.doColoring();
   </code>
</li>
<li>Coloring by <b>custom values of a custom object</b> and built-in coloring:<br>
    <code>
        SurfaceColoring sc = new SurfaceColoring( molecules, surface, precision );<br>
        sc.setPropertyObject( myGrid );<br>
        sc.setPropertyMethod( MyGrid.class.getMethod( "getLinearWeightedValue", new Class[] {float.class, float.class, float.class} ) );<br>
        sc.setBuiltInColorMapperMethod( SurfaceColoring.COLOR_MAPPER_RAINBOW );<br>
        sc.doColoring();
    </code>
</li>

</ol>

<hr>
<p>
<center>
<font size="-2" face="helvetica">

Copyright &copy; 2004-2013
<a href="http://www.chemaxon.com">ChemAxon Ltd.</a>
&nbsp;&nbsp;&nbsp;All rights reserved.
</font>
</center>
</body>
</html>
