<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta NAME="description" CONTENT="deprecated methods in Marvin">
<meta NAME="keywords" CONTENT="deprecated, removed, methods">
<meta NAME="author" CONTENT="<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>">
<link REL="stylesheet" TYPE="text/css" HREF="../../marvinmanuals.css" TITLE="Style">
<title>Deprecated methods in Marvin</title>
</head>
<body>
<h1>Deprecated and removed methods</h1>
<h2>Deprecated methods</h2>
<h3>
<strong><code>chemaxon.struc</code></strong>
</h3>
<table border="0" cellspacing="0" cellpadding="5" class="grid">
<tr style="text-align:left">
<td align="center"><strong>Name of deprecated class, field or method</strong></td><td align="center"><strong>Deprecation version</strong></td><td align="center"><strong>Recommended class, field or method</strong></td>
</tr>
<tr>
<td><code>MDocument.exportToBinFormat(String fmt)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/formats/MolExporter.html#exportToBinFormat(chemaxon.struc.MDocument, java.lang.String)" target="_blank">chemaxon.formats.MolExporter.exportToBinFormat(MDocument, String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MDocument.exportToFormat(String fmt)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/formats/MolExporter.html#exportToFormat(chemaxon.struc.MDocument, java.lang.String)" target="_blank">chemaxon.formats.MolExporter.exportToFormat(MDocument, String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MDocument.exportToObject(String fmt)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/formats/MolExporter.html#exportToObject(chemaxon.struc.MDocument, java.lang.String)" target="_blank">chemaxon.formats.MolExporter.exportToObject(MDocument, String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MDocument.parseMRV(String sval)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/formats/MolImporter.html#parseMRV(java.lang.String)" target="_blank">chemaxon.formats.MolImporter.parseMRV(String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MolAtom.CTSPECIFIC_H</code></td><td align="center">Marvin 5.11.</td><td>Graph invariants are checked by default
when calculating CIS/TRANS stereo, so this option is out of
use.</td>
</tr>
<tr>
<td><code>MolAtom.getAtomicNumber(String element)</code></td><td align="center">Marvin 5.9</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/PeriodicSystem.html#findAtomicNumber(java.lang.String)" target="_blank">chemaxon.struc.PeriodicSystem.findAtomicNumber(String)</a>
<br>The recommended method does not work for symbol 
not representing a chemical element (e.g. R-atom, any atom),
as this method should not have worked on them either.</td>
</tr>
<tr>
<td><code>MolAtom.getAttach()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/sgroup/SuperatomSgroup.html#getAttachmentPointOrders(chemaxon.struc.MolAtom)" target="_blank">chemaxon.struc.sgroup.SuperatomSgroup.getAttachmentPointOrders(MolAtom)</a>
<br>If the atom takes place in a
Superatom S-group then it's attachment point orders can be
reached through the Superatom S-group.</td>
</tr>
<tr>
<td><code>MolAtom.getQuerystr()</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MolAtom.html#getQueryString()" target="_blank">chemaxon.struc.MolAtom.getQueryString()</a>
<br>
</td>
</tr>
<tr>
<td><code>MolAtom.hasSMARTSProps()</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/io/formats/smiles/SmartsAtomQuerifier.html#hasSMARTSProps(chemaxon.struc.MolAtom)" target="_blank">chemaxon.marvin.io.formats.smiles.SmartsAtomQuerifier.hasSMARTSProps(MolAtom)</a>
<br>
</td>
</tr>
<tr>
<td><code>MolAtom.hasSMARTSPropsExcluding(String exclude)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/io/formats/smiles/SmartsAtomQuerifier.html#hasSMARTSPropsExcluding(chemaxon.struc.MolAtom, java.lang.String)" target="_blank">chemaxon.marvin.io.formats.smiles.SmartsAtomQuerifier.hasSMARTSPropsExcluding(MolAtom, String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MolAtom.numOf(String e)</code></td><td align="center">Marvin 5.9</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/PeriodicSystem.html#findAtomicNumber(java.lang.String)" target="_blank">chemaxon.struc.PeriodicSystem.findAtomicNumber(String)</a>
<br>The recommended method does not work for symbol that 
does not represent a chemical element (e.g. R-atom, any atom),
as this method should not have worked on them either.</td>
</tr>
<tr>
<td><code>MolAtom.setAttach(int a)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/sgroup/SuperatomSgroup.html#addAttachmentPoint(chemaxon.struc.MolAtom)" target="_blank">chemaxon.struc.sgroup.SuperatomSgroup.addAttachmentPoint(MolAtom)</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/sgroup/SuperatomSgroup.html#addAttachmentPoint(chemaxon.struc.MolAtom, int)" target="_blank">chemaxon.struc.sgroup.SuperatomSgroup.addAttachmentPoint(MolAtom, int)</a>
<br>
</td>
</tr>
<tr>
<td><code>MolAtom.setAttach(int newOrder, Sgroup sg)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/sgroup/SuperatomSgroup.html#addAttachmentPoint(chemaxon.struc.MolAtom)" target="_blank">chemaxon.struc.sgroup.SuperatomSgroup.addAttachmentPoint(MolAtom)</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/sgroup/SuperatomSgroup.html#addAttachmentPoint(chemaxon.struc.MolAtom, int)" target="_blank">chemaxon.struc.sgroup.SuperatomSgroup.addAttachmentPoint(MolAtom, int)</a>
<br>
</td>
</tr>
<tr>
<td><code>MolAtom.setQuerystr(String s, int options)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/io/formats/smiles/SmartsAtomQuerifier.html#setQuerystr(chemaxon.struc.MolAtom, java.lang.String, int)" target="_blank">chemaxon.marvin.io.formats.smiles.SmartsAtomQuerifier.setQuerystr(MolAtom, String, int)</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MolAtom.html#setQueryString(java.lang.String)" target="_blank">chemaxon.struc.MolAtom.setQueryString(String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MolAtom.setQuerystr(String s)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/io/formats/smiles/SmartsAtomQuerifier.html#setQuerystr(chemaxon.struc.MolAtom, java.lang.String)" target="_blank">chemaxon.marvin.io.formats.smiles.SmartsAtomQuerifier.setQuerystr(MolAtom, String)</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MolAtom.html#setQueryString(java.lang.String)" target="_blank">chemaxon.struc.MolAtom.setQueryString(String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MolAtom.setSMARTS(String s)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/io/formats/smiles/SmartsAtomQuerifier.html#setSMARTS(chemaxon.struc.MolAtom, java.lang.String)" target="_blank">chemaxon.marvin.io.formats.smiles.SmartsAtomQuerifier.setSMARTS(MolAtom, String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MolBond.getDesiredLength()</code></td><td align="center">Marvin 5.11</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MolBond.html#desiredLength(int, int, int, int)" target="_blank">chemaxon.struc.MolBond.desiredLength(int, int, int, int)</a>
<br>
</td>
</tr>
<tr>
<td><code>MolBond.getDesiredLength(boolean shortenHbonds)</code></td><td align="center">Marvin 5.11</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MolBond.html#desiredLength(int, int, int, int)" target="_blank">chemaxon.struc.MolBond.desiredLength(int, int, int, int)</a>
<br>
</td>
</tr>
<tr>
<td><code>Molecule.checkConsistency()</code></td><td align="center">Marvin 5.7</td><td>Not intended for public use, it was intended only for internal debugging.</td>
</tr>
<tr>
<td><code>Molecule.exportToBinFormat(String fmt)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/formats/MolExporter.html#exportToBinFormat(chemaxon.struc.Molecule, java.lang.String)" target="_blank">chemaxon.formats.MolExporter.exportToBinFormat(Molecule, String)</a>
<br>
</td>
</tr>
<tr>
<td><code>Molecule.exportToFormat(String fmt)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/formats/MolExporter.html#exportToFormat(chemaxon.struc.Molecule, java.lang.String)" target="_blank">chemaxon.formats.MolExporter.exportToFormat(Molecule, String)</a>
<br>
</td>
</tr>
<tr>
<td><code>Molecule.exportToObject(String fmt)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/formats/MolExporter.html#exportToObject(chemaxon.struc.Molecule, java.lang.String)" target="_blank">chemaxon.formats.MolExporter.exportToObject(Molecule, String)</a>
<br>
</td>
</tr>
<tr>
<td><code>Molecule.getProperty(String key)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/io/MPropHandler.html#convertToString(chemaxon.struc.MProp, java.lang.String)" target="_blank">chemaxon.marvin.io.MPropHandler.convertToString(MProp, String)</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>String property = MPropHandler.convertToString(mol.properties(), key);</pre>
</td>
</tr>
<tr>
<td><code>Molecule.getPropertyKeys()</code></td><td align="center">Marvin 4.1</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/Molecule.html#properties()" target="_blank">chemaxon.struc.Molecule.properties()</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MPropertyContainer.html#getKeys()" target="_blank">chemaxon.struc.MPropertyContainer.getKeys()</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/Molecule.html#properties()" target="_blank">chemaxon.struc.Molecule.properties()</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MPropertyContainer.html#getKeyEnumeration()" target="_blank">chemaxon.struc.MPropertyContainer.getKeyEnumeration()</a>
<br>One-to-one replacement is method getKeyEnumeration() but
method getKeys() is simpler to use.</td>
</tr>
<tr>
<td><code>Molecule.implicitizeHydrogens0(int f, MolAtom[] atoms, boolean check)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/calculations/hydrogenize/Hydrogenize.html#convertExplicitHToImplicit(chemaxon.struc.MoleculeGraph, chemaxon.struc.MolAtom[], int, boolean)" target="_blank">chemaxon.calculations.hydrogenize.Hydrogenize.convertExplicitHToImplicit(MoleculeGraph, MolAtom[], int, boolean)</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>Hydrogenize.convertExplicitHToImplicit(mol, atoms, f, check);</pre>
</td>
</tr>
<tr>
<td><code>Molecule.sortSgroupXBonds()</code></td><td align="center">Marvin 5.12</td><td>Crossing bonds of a SuperatomSgroup do not need to be sorted 
any more. It is not advised to sort them anyway because then the indexes of 
bonds change in the parent molecule.</td>
</tr>
<tr>
<td><code>Molecule.toBinFormat(String fmt)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/formats/MolExporter.html#exportToBinFormat(chemaxon.struc.Molecule, java.lang.String)" target="_blank">chemaxon.formats.MolExporter.exportToBinFormat(Molecule, String)</a>
<br>
</td>
</tr>
<tr>
<td><code>Molecule.toFormat(String fmt)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/formats/MolExporter.html#exportToFormat(chemaxon.struc.Molecule, java.lang.String)" target="_blank">chemaxon.formats.MolExporter.exportToFormat(Molecule, String)</a>
<br>
  To get exact replacement use:<br>
  <pre>
  try {
     String out = MolExporter.exportToFormat(molecule, fmt);
  } catch (IOException e){
  }
  </pre>
</td>
</tr>
<tr>
<td><code>Molecule.toObject(String fmt)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/formats/MolExporter.html#exportToObject(chemaxon.struc.Molecule, java.lang.String)" target="_blank">chemaxon.formats.MolExporter.exportToObject(Molecule, String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.CACHE_REMOVE_SSSRMODULE</code></td><td align="center">Marvin 5.12</td><td>SSSR module is not cached any more.</td>
</tr>
<tr>
<td><code>MoleculeGraph.VALENCE_CHECK_AMBIGUOUS</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/core/calculations/valencecheck/ValenceCheckOptions.html" target="_blank">chemaxon.core.calculations.valencecheck.ValenceCheckOptions</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.addExplicitHydrogens(int f, MolAtom[] atoms)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/calculations/hydrogenize/Hydrogenize.html#convertImplicitHToExplicit(chemaxon.struc.MoleculeGraph, chemaxon.struc.MolAtom[], int)" target="_blank">chemaxon.calculations.hydrogenize.Hydrogenize.convertImplicitHToExplicit(MoleculeGraph, MolAtom[], int)</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>Hydrogenize.convertImplicitHToExplicit(molecule, atoms, f);</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.addExplicitHydrogens(int f)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/calculations/hydrogenize/Hydrogenize.html#convertImplicitHToExplicit(chemaxon.struc.MoleculeGraph, chemaxon.struc.MolAtom[], int)" target="_blank">chemaxon.calculations.hydrogenize.Hydrogenize.convertImplicitHToExplicit(MoleculeGraph, MolAtom[], int)</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>Hydrogenize.convertImplicitHToExplicit(molecule, null, f);</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.addExplicitLonePairs()</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/calculations/hydrogenize/Hydrogenize.html#convertImplicitLonePairsToExplicit(chemaxon.struc.MoleculeGraph, chemaxon.struc.MolAtom[])" target="_blank">chemaxon.calculations.hydrogenize.Hydrogenize.convertImplicitLonePairsToExplicit(MoleculeGraph, MolAtom[])</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>Hydrogenize.convertImplicitLonePairsToExplicit(molecule, null);</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.arrangeComponents()</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/util/CleanUtil.html#arrangeComponents(chemaxon.struc.MoleculeGraph, boolean, boolean)" target="_blank">chemaxon.marvin.util.CleanUtil.arrangeComponents(MoleculeGraph, boolean, boolean)</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>CleanUtil.arrangeComponents(molecule, true, true);</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.checkConsistency()</code></td><td align="center">Marvin 5.7</td><td>Not intended for public use, it was intended only for internal debugging.</td>
</tr>
<tr>
<td><code>MoleculeGraph.clean(int dim, String opts, MProgressMonitor pmon)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/calculations/clean/Cleaner.html#clean(chemaxon.struc.MoleculeGraph, int, java.lang.String, MProgressMonitor)" target="_blank">chemaxon.calculations.clean.Cleaner.clean(MoleculeGraph, int, String, MProgressMonitor)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.clean(int dim, String opts)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/calculations/clean/Cleaner.html#clean(chemaxon.struc.MoleculeGraph, int, java.lang.String)" target="_blank">chemaxon.calculations.clean.Cleaner.clean(MoleculeGraph, int, String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.findComponentIds()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#getFragIds(int)" target="_blank">chemaxon.struc.MoleculeGraph.getFragIds(int)</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#FRAG_BASIC" target="_blank">chemaxon.struc.MoleculeGraph.FRAG_BASIC</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>mol.getFragIds(MoleculeGraph.FRAG_BASIC);</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.findFrag(int i, MoleculeGraph frag)</code></td><td align="center">Marvin 5.6</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#findFrag(int, int, chemaxon.struc.MoleculeGraph)" target="_blank">chemaxon.struc.MoleculeGraph.findFrag(int, int, MoleculeGraph)</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#FRAG_KEEPING_MULTICENTERS" target="_blank">chemaxon.struc.MoleculeGraph.FRAG_KEEPING_MULTICENTERS</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>mol.findFrag(i, MoleculeGraph.FRAG_KEEPING_MULTICENTERS, frag);</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.findFrags(Class&lt;C&gt; cl)</code></td><td align="center">Marvin 5.6</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#findFrags(java.lang.Class, int)" target="_blank">chemaxon.struc.MoleculeGraph.findFrags(Class, int)</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#FRAG_KEEPING_MULTICENTERS" target="_blank">chemaxon.struc.MoleculeGraph.FRAG_KEEPING_MULTICENTERS</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>mol.findFrags(cl, MoleculeGraph.FRAG_KEEPING_MULTICENTERS);</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.getBtab()</code></td><td align="center">Marvin 5.4</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#getBondTable()" target="_blank">chemaxon.struc.MoleculeGraph.getBondTable()</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/core/util/BondTable.html#getMatrixArray()" target="_blank">chemaxon.core.util.BondTable.getMatrixArray()</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>molecule.getBondTable().getMatrixArray();</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.getDesiredLength(int atno1, int atno2, int type)</code></td><td align="center">Marvin 5.11</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MolBond.html#desiredLength(int, int, int, int)" target="_blank">chemaxon.struc.MolBond.desiredLength(int, int, int, int)</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#getDim()" target="_blank">chemaxon.struc.MoleculeGraph.getDim()</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>MolBond.desiredLength(atno1, atno2, type, getDim());</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.getDesiredLength(MolBond b)</code></td><td align="center">Marvin 5.11</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MolBond.html#desiredLength(int, int, int, int)" target="_blank">chemaxon.struc.MolBond.desiredLength(int, int, int, int)</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MolBond.html#getType()" target="_blank">chemaxon.struc.MolBond.getType()</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#getDim()" target="_blank">chemaxon.struc.MoleculeGraph.getDim()</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>MolBond.desiredLength(atno1, atno2, b.getType, getDim());</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.getFragCount()</code></td><td align="center">Marvin 5.6</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#getFragCount(int)" target="_blank">chemaxon.struc.MoleculeGraph.getFragCount(int)</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#FRAG_KEEPING_MULTICENTERS" target="_blank">chemaxon.struc.MoleculeGraph.FRAG_KEEPING_MULTICENTERS</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>mol.getFragCount(MoleculeGraph.FRAG_KEEPING_MULTICENTERS);</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.getGrinv(int[] gi, boolean uniqueFlag)</code></td><td align="center">Marvin 4.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#getGrinv(int[], int)" target="_blank">chemaxon.struc.MoleculeGraph.getGrinv(int[], int)</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>getGrinv(gi, uniqueFlag ? GRINV_OLDSTEREO : 0);</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.getPossibleAttachmentPoints(MolAtom molAtom)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/sgroup/SuperatomSgroup.html#getAttachmentPointOrders(chemaxon.struc.MolAtom)" target="_blank">chemaxon.struc.sgroup.SuperatomSgroup.getAttachmentPointOrders(MolAtom)</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MolAtom.html#getAttachParentSgroup()" target="_blank">chemaxon.struc.MolAtom.getAttachParentSgroup()</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>SuperatomSgroup group = (SuperatomSgroup)molAtom.getAttachParentSgroup());
List&amp;ltInteger&amp;gt orders = group.getAttachmentPointOrders(molAtom);</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.getValenceCheckState()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#getValenceCheckOptions()" target="_blank">chemaxon.struc.MoleculeGraph.getValenceCheckOptions()</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#isValenceCheckEnabled()" target="_blank">chemaxon.struc.MoleculeGraph.isValenceCheckEnabled()</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>// Same as getValenceCheckState() == ValenceCheckState.OFF:
isValenceCheckEnabled() == true;
// Same as getValenceCheckState() == ValenceCheckState.AMBIGUOUS_AROMATIC_ATOMS_IGNORED: 
getValenceCheckOptions.isLocalAromatic() == true; 
// Same as getValenceCheckState() == ValenceCheckState.FULL:
getValenceCheckOptions.isLocalAromatic() == false;</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.hydrogenize(boolean add)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/calculations/hydrogenize/Hydrogenize.html#convertImplicitHToExplicit(chemaxon.struc.MoleculeGraph)" target="_blank">chemaxon.calculations.hydrogenize.Hydrogenize.convertImplicitHToExplicit(MoleculeGraph)</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/calculations/hydrogenize/Hydrogenize.html#convertExplicitHToImplicit(chemaxon.struc.MoleculeGraph)" target="_blank">chemaxon.calculations.hydrogenize.Hydrogenize.convertExplicitHToImplicit(MoleculeGraph)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.implicitizeHydrogens(int f, MolAtom[] atoms, boolean check)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/calculations/hydrogenize/Hydrogenize.html#convertExplicitHToImplicit(chemaxon.struc.MoleculeGraph, chemaxon.struc.MolAtom[], int, boolean)" target="_blank">chemaxon.calculations.hydrogenize.Hydrogenize.convertExplicitHToImplicit(MoleculeGraph, MolAtom[], int, boolean)</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>Hydrogenize.convertExplicitHToImplicit(mol, atoms, f, check);</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.implicitizeHydrogens(int f, MolAtom[] atoms)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/calculations/hydrogenize/Hydrogenize.html#convertExplicitHToImplicit(chemaxon.struc.MoleculeGraph, chemaxon.struc.MolAtom[], int)" target="_blank">chemaxon.calculations.hydrogenize.Hydrogenize.convertExplicitHToImplicit(MoleculeGraph, MolAtom[], int)</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>Hydrogenize.convertExplicitHToImplicit(mol, atoms, f);</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.implicitizeHydrogens(int f)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/calculations/hydrogenize/Hydrogenize.html#convertExplicitHToImplicit(chemaxon.struc.MoleculeGraph, int)" target="_blank">chemaxon.calculations.hydrogenize.Hydrogenize.convertExplicitHToImplicit(MoleculeGraph, int)</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>Hydrogenize.convertExplicitHToImplicit(molecule, f);</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.implicitizeHydrogens0(int f, MolAtom[] atoms, boolean check)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/calculations/hydrogenize/Hydrogenize.html#convertExplicitHToImplicit(chemaxon.struc.MoleculeGraph, chemaxon.struc.MolAtom[], int, boolean)" target="_blank">chemaxon.calculations.hydrogenize.Hydrogenize.convertExplicitHToImplicit(MoleculeGraph, MolAtom[], int, boolean)</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>Hydrogenize.convertExplicitHToImplicit(mol, atoms, f, check);</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.partialClean(int dim, int[] fixed, String opts)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/calculations/clean/Cleaner.html#partialClean(chemaxon.struc.MoleculeGraph, int, int[], java.lang.String)" target="_blank">chemaxon.calculations.clean.Cleaner.partialClean(MoleculeGraph, int, int[], String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.partialClean(Molecule[] template, String opts)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/calculations/clean/Cleaner.html#partialClean(chemaxon.struc.chemaxon.struc.Molecule, chemaxon.struc.chemaxon.struc.Molecule[], java.lang.String)" target="_blank">chemaxon.calculations.clean.Cleaner.partialClean(Molecule, Molecule[], String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.partialClean(MoleculeGraph template, int[] map, String opts)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/calculations/clean/Cleaner.html#partialClean(chemaxon.struc.chemaxon.struc.MoleculeGraph, chemaxon.struc.chemaxon.struc.MoleculeGraph, int[], java.lang.String)" target="_blank">chemaxon.calculations.clean.Cleaner.partialClean(MoleculeGraph, MoleculeGraph, int[], String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.removeExplicitLonePairs()</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/calculations/hydrogenize/Hydrogenize.html#convertExplicitLonePairsToImplicit(chemaxon.struc.MoleculeGraph)" target="_blank">chemaxon.calculations.hydrogenize.Hydrogenize.convertExplicitLonePairsToImplicit(MoleculeGraph)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.setValenceCheckState(ValenceCheckState state)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#setValenceCheckOptions(chemaxon.core.calculations.valencecheck.ValenceCheckOptions)" target="_blank">chemaxon.struc.MoleculeGraph.setValenceCheckOptions(ValenceCheckOptions)</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#setValenceCheckEnabled(boolean)" target="_blank">chemaxon.struc.MoleculeGraph.setValenceCheckEnabled(boolean)</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>// Same as setValenceCheckState(ValenceCheckState.OFF):
setValenceCheckEnabled(false);
// Same as setValenceCheckState(ValenceCheckState.AMBIGUOUS_AROMATIC_ATOMS_IGNORED): 
setValenceCheckOptions(ValenceCheckOptions.DEFAULT); 
// Same as setValenceCheckState(ValenceCheckState.FULL):
setValenceCheckOptions(new ValenceCheckOptions(false, true));</pre>
</td>
</tr>
<tr>
<td><code>MProp.convertToString(String fmt, int flags)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/io/MPropHandler.html#convertToString(chemaxon.struc.MProp, java.lang.String)" target="_blank">chemaxon.marvin.io.MPropHandler.convertToString(MProp, String)</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MProp.html#CONV_SELFREF" target="_blank">chemaxon.struc.MProp.CONV_SELFREF</a>
<br>
</td>
</tr>
<tr>
<td><code>MProp.convertToString(String fmt)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/io/MPropHandler.html#convertToString(chemaxon.struc.MProp, java.lang.String)" target="_blank">chemaxon.marvin.io.MPropHandler.convertToString(MProp, String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MPropertyContainer.getString(String key)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/io/MPropHandler.html#convertToString(chemaxon.struc.MPropertyContainer, java.lang.String)" target="_blank">chemaxon.marvin.io.MPropHandler.convertToString(MPropertyContainer, String)</a>
<br>
</td>
</tr>
<tr>
<td><code>PeriodicSystem.getAtomicNumber(String symbol)</code></td><td align="center">Marvin 5.9</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/PeriodicSystem.html#findAtomicNumber(java.lang.String)" target="_blank">chemaxon.struc.PeriodicSystem.findAtomicNumber(String)</a>
<br>
</td>
</tr>
<tr>
<td><code>RgMolecule.checkConsistency()</code></td><td align="center">Marvin 5.7</td><td>Not intended for public use, it was intended only for internal debugging.</td>
</tr>
<tr>
<td><code>Sgroup.Sgroup(Molecule parent, int t, int xstate)</code></td><td align="center">Marvin 5.12</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/SgroupFactory.html#createSgroup(chemaxon.struc.Molecule, chemaxon.struc.SgroupType)" target="_blank">chemaxon.struc.SgroupFactory.createSgroup(Molecule, SgroupType)</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/Sgroup.html#setXState(int)" target="_blank">chemaxon.struc.Sgroup.setXState(int)</a>
<br>
</td>
</tr>
<tr>
<td><code>Sgroup.Sgroup(Molecule parent, int t)</code></td><td align="center">Marvin 5.12</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/SgroupFactory.html#createSgroup(chemaxon.struc.Molecule, chemaxon.struc.SgroupType)" target="_blank">chemaxon.struc.SgroupFactory.createSgroup(Molecule, SgroupType)</a>
<br>
</td>
</tr>
</table>
<h3>
<strong><code>chemaxon.struc.graphics</code></strong>
</h3>
<table border="0" cellspacing="0" cellpadding="5" class="grid">
<tr style="text-align:left">
<td align="center"><strong>Name of deprecated class, field or method</strong></td><td align="center"><strong>Deprecation version</strong></td><td align="center"><strong>Recommended class, field or method</strong></td>
</tr>
<tr>
<td><code>MEFlow.getValidSourceAtomPairsForMolecule(@SuppressWarnings("unused") Molecule m)</code></td><td align="center">Marvin 5.12.</td>
<td>No replacement</td>
</tr>
</table>
<h3>
<strong><code>chemaxon.struc.prop</code></strong>
</h3>
<table border="0" cellspacing="0" cellpadding="5" class="grid">
<tr style="text-align:left">
<td align="center"><strong>Name of deprecated class, field or method</strong></td><td align="center"><strong>Deprecation version</strong></td><td align="center"><strong>Recommended class, field or method</strong></td>
</tr>
<tr>
<td><code>MBooleanProp.convertToString(String fmt, int flags)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/io/MPropHandler.html#convertToString(chemaxon.struc.MProp, java.lang.String)" target="_blank">chemaxon.marvin.io.MPropHandler.convertToString(MProp, String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MDoubleArrayProp.convertToString(String fmt, int flags)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/io/MPropHandler.html#convertToString(chemaxon.struc.MProp, java.lang.String)" target="_blank">chemaxon.marvin.io.MPropHandler.convertToString(MProp, String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MDoubleProp.convertToString(String fmt, int flags)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/io/MPropHandler.html#convertToString(chemaxon.struc.MProp, java.lang.String)" target="_blank">chemaxon.marvin.io.MPropHandler.convertToString(MProp, String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MHashProp.convertToString(String fmt, int flags)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/io/MPropHandler.html#convertToString(chemaxon.struc.MProp, java.lang.String)" target="_blank">chemaxon.marvin.io.MPropHandler.convertToString(MProp, String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MHCoords3DProp.convertToString(String fmt, int flags)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/io/MPropHandler.html#convertToString(chemaxon.struc.MProp, java.lang.String)" target="_blank">chemaxon.marvin.io.MPropHandler.convertToString(MProp, String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MIntegerArrayProp.convertToString(String fmt, int flags)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/io/MPropHandler.html#convertToString(chemaxon.struc.MProp, java.lang.String)" target="_blank">chemaxon.marvin.io.MPropHandler.convertToString(MProp, String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MIntegerProp.convertToString(String fmt, int flags)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/io/MPropHandler.html#convertToString(chemaxon.struc.MProp, java.lang.String)" target="_blank">chemaxon.marvin.io.MPropHandler.convertToString(MProp, String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MListProp.convertToString(String fmt, int flags)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/io/MPropHandler.html#convertToString(chemaxon.struc.MProp, java.lang.String)" target="_blank">chemaxon.marvin.io.MPropHandler.convertToString(MProp, String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MMoleculeProp.convertToString(String fmt, int flags)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/io/MPropHandler.html#convertToString(chemaxon.struc.MProp, java.lang.String)" target="_blank">chemaxon.marvin.io.MPropHandler.convertToString(MProp, String)</a>
<br>Instead 
CONV_SELFREF
 flag use fmt + ":-selfrefprops" string in fmt.</td>
</tr>
<tr>
<td><code>MObjectProp.convertToString(String fmt, int flags)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/io/MPropHandler.html#convertToString(chemaxon.struc.MProp, java.lang.String)" target="_blank">chemaxon.marvin.io.MPropHandler.convertToString(MProp, String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MStringProp.convertToString(String fmt, int flags)</code></td><td align="center">Marvin 5.7</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/io/MPropHandler.html#convertToString(chemaxon.struc.MProp, java.lang.String)" target="_blank">chemaxon.marvin.io.MPropHandler.convertToString(MProp, String)</a>
<br>
</td>
</tr>
</table>
<h3>
<strong><code>chemaxon.struc.sgroup</code></strong>
</h3>
<table border="0" cellspacing="0" cellpadding="5" class="grid">
<tr style="text-align:left">
<td align="center"><strong>Name of deprecated class, field or method</strong></td><td align="center"><strong>Deprecation version</strong></td><td align="center"><strong>Recommended class, field or method</strong></td>
</tr>
<tr>
<td><code>SuperatomSgroup.getAttachAtoms()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/sgroup/SuperatomSgroup.html#getBoundAttachAtoms()" target="_blank">chemaxon.struc.sgroup.SuperatomSgroup.getBoundAttachAtoms()</a>
<br>
</td>
</tr>
<tr>
<td><code>SuperatomSgroup.getFreeLegalAttachAtoms()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/sgroup/SuperatomSgroup.html#getFreeAttachAtoms()" target="_blank">chemaxon.struc.sgroup.SuperatomSgroup.getFreeAttachAtoms()</a>
<br>
</td>
</tr>
<tr>
<td><code>SuperatomSgroup.getLegalAttachAtoms()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/sgroup/SuperatomSgroup.html#getAllAttachAtoms()" target="_blank">chemaxon.struc.sgroup.SuperatomSgroup.getAllAttachAtoms()</a>
<br>
</td>
</tr>
<tr>
<td><code>SuperatomSgroup.isFreeLegalAttachAtom(MolAtom a)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/sgroup/SuperatomSgroup.html#isFreeAttachAtom(chemaxon.struc.MolAtom)" target="_blank">chemaxon.struc.sgroup.SuperatomSgroup.isFreeAttachAtom(MolAtom)</a>
<br>
</td>
</tr>
<tr>
<td><code>SuperatomSgroup.isLegalAttachment(MolAtom atom)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/sgroup/SuperatomSgroup.html#isAttachmentAtom(chemaxon.struc.MolAtom)" target="_blank">chemaxon.struc.sgroup.SuperatomSgroup.isAttachmentAtom(MolAtom)</a>
<br>
</td>
</tr>
<tr>
<td><code>SuperatomSgroup.sortXBonds()</code></td><td align="center">Marvin 5.12</td><td>Crossing bonds of a SuperatomSgroup do not need to be sorted 
any more. It is not advised to sort them anyway because then the indexes of 
bonds change in the parent molecule.</td>
</tr>
</table>
<h2>Removed methods</h2>
<h3>
<strong><code>chemaxon.struc</code></strong>
</h3>
<table border="0" cellspacing="0" cellpadding="5" class="grid">
<tr style="text-align:left">
<td align="center"><strong>Name of deprecated class, field or method</strong></td><td align="center"><strong>Removal version</strong></td><td align="center"><strong>Recommended class, field or method</strong></td>
</tr>
<tr style="text-align:left">
<td><code>CEdge</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MolBond.html" target="_blank">chemaxon.struc.MolBond</a>
<br>
</td>
</tr>
<tr style="text-align:left">
<td><code>CGraph</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html" target="_blank">chemaxon.struc.MoleculeGraph</a>
<br>
</td>
</tr>
<tr style="text-align:left">
<td><code>CNode</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MolAtom.html" target="_blank">chemaxon.struc.MolAtom</a>
<br>
</td>
</tr>
<tr>
<td><code>MDocument.setGUIProperyContainer(MPropertyContainer gpc)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MDocument.html#setGUIPropertyContainer(chemaxon.struc.MPropertyContainer)" target="_blank">chemaxon.struc.MDocument.setGUIPropertyContainer(MPropertyContainer)</a>
<br>
</td>
</tr>
<tr>
<td><code>MolAtom.getEdge(int i)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MolAtom.html#getBond(int)" target="_blank">chemaxon.struc.MolAtom.getBond(int)</a>
<br>
</td>
</tr>
<tr>
<td><code>MolAtom.getEdgeCount()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MolAtom.html#getBondCount()" target="_blank">chemaxon.struc.MolAtom.getBondCount()</a>
<br>
</td>
</tr>
<tr>
<td><code>MolAtom.getEdgeTo(MolAtom other)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MolAtom.html#getBondTo(chemaxon.struc.MolAtom)" target="_blank">chemaxon.struc.MolAtom.getBondTo(MolAtom)</a>
<br>
</td>
</tr>
<tr>
<td><code>MolAtom.haveSimilarEdges(MolAtom a)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MolAtom.html#haveSimilarBonds(chemaxon.struc.MolAtom)" target="_blank">chemaxon.struc.MolAtom.haveSimilarBonds(MolAtom)</a>
<br>
</td>
</tr>
<tr>
<td><code>MolAtom.isArrowEnd()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MPoint.html" target="_blank">chemaxon.struc.MPoint</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/graphics/MRArrow.html" target="_blank">chemaxon.struc.graphics.MRArrow</a>
<br>MolAtom objects cannot represent end-points of reaction 
arrows any more.
Class chemaxon.struc.graphics.MRArrow is used to represent 
reaction arrows and class MPoint represents the end-points 
of a reaction arrow.</td>
</tr>
<tr>
<td><code>MolAtom.isNobleGas()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/PeriodicSystem.html#isNobleGas(int)" target="_blank">chemaxon.struc.PeriodicSystem.isNobleGas(int)</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MolAtom.html#getAtno()" target="_blank">chemaxon.struc.MolAtom.getAtno()</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>PeriodicSystem.isNobleGas(atom.getAtno());</pre>
</td>
</tr>
<tr>
<td><code>MolAtom.setMassnoIfKnown(String sym)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MolAtom.html#setForSpecIsotopeSymbol(java.lang.String)" target="_blank">chemaxon.struc.MolAtom.setForSpecIsotopeSymbol(String)</a>
<br>
</td>
</tr>
<tr>
<td><code>MolAtom.valenceCheck(int opts)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MolAtom.html#valenceCheck()" target="_blank">chemaxon.struc.MolAtom.valenceCheck()</a>
<br>There are no options for valence check.</td>
</tr>
<tr>
<td><code>MolBond.ARROW</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/graphics/MRArrow.html" target="_blank">chemaxon.struc.graphics.MRArrow</a>
<br>Class MRArrow is used to represent reaction arrows.</td>
</tr>
<tr>
<td><code>MolBond.cloneEdge(MolAtom a1, MolAtom a2)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MolBond.html#cloneBond(chemaxon.struc.chemaxon.struc.MolAtom, chemaxon.struc.chemaxon.struc.MolAtom)" target="_blank">chemaxon.struc.MolBond.cloneBond(MolAtom, MolAtom)</a>
<br>
</td>
</tr>
<tr>
<td><code>MolBond.getNode1()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MolBond.html#getAtom1()" target="_blank">chemaxon.struc.MolBond.getAtom1()</a>
<br>
</td>
</tr>
<tr>
<td><code>MolBond.getNode2()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MolBond.html#getAtom2()" target="_blank">chemaxon.struc.MolBond.getAtom2()</a>
<br>
</td>
</tr>
<tr>
<td><code>MolBond.getOtherNode(MolAtom node)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MolBond.html#getOtherAtom(chemaxon.struc.MolAtom)" target="_blank">chemaxon.struc.MolBond.getOtherAtom(MolAtom)</a>
<br>
</td>
</tr>
<tr>
<td><code>MolBond.isArrow()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/graphics/MRArrow.html" target="_blank">chemaxon.struc.graphics.MRArrow</a>
<br>MolBond objects cannot represent reaction arrows any more.
Class MRArrow is used to represent reaction arrows.</td>
</tr>
<tr>
<td><code>MolBond.isCoordinative()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MolBond.html#isCoordinate()" target="_blank">chemaxon.struc.MolBond.isCoordinate()</a>
<br>
</td>
</tr>
<tr>
<td><code>Molecule.mergeFrags()</code></td><td align="center">Marvin 6.0</td><td>It was used by an internal method, not intended for public usage.</td>
</tr>
<tr>
<td><code>MoleculeGraph.AROM_CHEMAXON</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#AROM_BASIC" target="_blank">chemaxon.struc.MoleculeGraph.AROM_BASIC</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.AROM_DAYLIGHT</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#AROM_GENERAL" target="_blank">chemaxon.struc.MoleculeGraph.AROM_GENERAL</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.findFragById(int fragId, MoleculeGraph frag)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#findFragById(int, int, chemaxon.struc.MoleculeGraph)" target="_blank">chemaxon.struc.MoleculeGraph.findFragById(int, int, MoleculeGraph)</a>
<br>To keep the same functionality, use value 
FRAG_KEEPING_MULTICENTERS
for parameter fragmentationType.<p>
<i>Usage:</i>
</p>
<pre>mol.findFragById(fragId, MoleculeGraph.FRAG_KEEPING_MULTICENTERS, frag);</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.getAromrings()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#getAromaticAndAliphaticRings(int, boolean, boolean, int, int)" target="_blank">chemaxon.struc.MoleculeGraph.getAromaticAndAliphaticRings(int, boolean, boolean, int, int)</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>getAromaticAndAliphaticRings(AROM_BASIC, true, false, 18, 1000)[0];</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.getAromrings(int size)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#getAromaticAndAliphaticRings(int, boolean, boolean, int, int)" target="_blank">chemaxon.struc.MoleculeGraph.getAromaticAndAliphaticRings(int, boolean, boolean, int, int)</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>getAromaticAndAliphaticRings(AROM_BASIC, true, false, size, 1000)[0];</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.getEdge(int i)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#getBond(int)" target="_blank">chemaxon.struc.MoleculeGraph.getBond(int)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.getEdgeArray()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#getBondArray()" target="_blank">chemaxon.struc.MoleculeGraph.getBondArray()</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.getEdgeCount()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#getBondCount()" target="_blank">chemaxon.struc.MoleculeGraph.getBondCount()</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.getEdgeVector()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#getBondArray()" target="_blank">chemaxon.struc.MoleculeGraph.getBondArray()</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>Vector&lt;MolBond&gt; v = new Vector&lt;MolBond&gt;(getBondCount());
v.addAll(Arrays.asList(getBondArray())).</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.getFragIds()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#getFragIds(int)" target="_blank">chemaxon.struc.MoleculeGraph.getFragIds(int)</a>
<br>To keep the same functionality, use value 
FRAG_KEEPING_MULTICENTERS
for parameter fragmentationType.<p>
<i>Usage:</i>
</p>
<pre>mol.getFragIds(MoleculeGraph.FRAG_KEEPING_MULTICENTERS);</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.getNode(int i)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#getAtom(int)" target="_blank">chemaxon.struc.MoleculeGraph.getAtom(int)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.getNodeCount()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#getAtomCount()" target="_blank">chemaxon.struc.MoleculeGraph.getAtomCount()</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.getNodeVector()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#getAtomArray()" target="_blank">chemaxon.struc.MoleculeGraph.getAtomArray()</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>Vector&lt;MolAtom&gt; v = new Vector&lt;MolAtom&gt;(getAtomCount());
v.addAll(Arrays.asList(getAtomArray()));</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.getNonAromrings()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#getAromaticAndAliphaticRings(int, boolean, boolean, int, int)" target="_blank">chemaxon.struc.MoleculeGraph.getAromaticAndAliphaticRings(int, boolean, boolean, int, int)</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>getAromaticAndAliphaticRings(AROM_BASIC, true, false, 18, 1000)[1];</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.getNonAromrings(int size)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#getAromaticAndAliphaticRings(int, boolean, boolean, int, int)" target="_blank">chemaxon.struc.MoleculeGraph.getAromaticAndAliphaticRings(int, boolean, boolean, int, int)</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>getAromaticAndAliphaticRings(AROM_BASIC, true, false, size, 1000)[1];</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.insertEdge(int i, MolBond edge)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#insertBond(int, chemaxon.struc.MolBond)" target="_blank">chemaxon.struc.MoleculeGraph.insertBond(int, MolBond)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.insertEdgeInOrder(MolBond e, MolBond[] order)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#insertBondInOrder(chemaxon.struc.chemaxon.struc.MolBond, chemaxon.struc.chemaxon.struc.MolBond[])" target="_blank">chemaxon.struc.MoleculeGraph.insertBondInOrder(MolBond, MolBond[])</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.insertNode(int i, MolAtom node)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#insertAtom(int, chemaxon.struc.MolAtom)" target="_blank">chemaxon.struc.MoleculeGraph.insertAtom(int, MolAtom)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.isValenceCheckEnabled()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#getValenceCheckState()" target="_blank">chemaxon.struc.MoleculeGraph.getValenceCheckState()</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.mergeFrags()</code></td><td align="center">Marvin 6.0</td><td>It was an empty method in this class which is not 
required any more, it's usage can simply be deleted.</td>
</tr>
<tr>
<td><code>MoleculeGraph.mergeFrags(int id1, int id2)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#mergeFrags(int, int, int)" target="_blank">chemaxon.struc.MoleculeGraph.mergeFrags(int, int, int)</a>
<br>To keep the same functionality, use value 
FRAG_KEEPING_MULTICENTERS
for parameter fragmentationType.<p>
<i>Usage:</i>
</p>
<pre>mol.mergeFrags(id1, id2, MoleculeGraph.FRAG_KEEPING_MULTICENTERS);</pre>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.mergeNodes(MolAtom that, MolAtom a)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#mergeAtoms(chemaxon.struc.chemaxon.struc.MolAtom, chemaxon.struc.chemaxon.struc.MolAtom)" target="_blank">chemaxon.struc.MoleculeGraph.mergeAtoms(MolAtom, MolAtom)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.regenEdges()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#regenBonds()" target="_blank">chemaxon.struc.MoleculeGraph.regenBonds()</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.removeAllEdges()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#removeAllBonds()" target="_blank">chemaxon.struc.MoleculeGraph.removeAllBonds()</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.removeEdge(int i)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#removeBond(int)" target="_blank">chemaxon.struc.MoleculeGraph.removeBond(int)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.removeEdge(MolBond edge)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#removeBond(chemaxon.struc.MolBond)" target="_blank">chemaxon.struc.MoleculeGraph.removeBond(MolBond)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.removeNode(int i, int cleanupFlags)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#removeAtom(int, int)" target="_blank">chemaxon.struc.MoleculeGraph.removeAtom(int, int)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.removeNode(int i)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#removeAtom(int)" target="_blank">chemaxon.struc.MoleculeGraph.removeAtom(int)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.removeNode(MolAtom node, int cleanupFlags)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#removeAtom(chemaxon.struc.MolAtom, int)" target="_blank">chemaxon.struc.MoleculeGraph.removeAtom(MolAtom, int)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.removeNode(MolAtom node)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#removeAtom(chemaxon.struc.MolAtom)" target="_blank">chemaxon.struc.MoleculeGraph.removeAtom(MolAtom)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.replaceEdge(MolBond olde, MolBond newe)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#replaceBond(chemaxon.struc.chemaxon.struc.MolBond, chemaxon.struc.chemaxon.struc.MolBond)" target="_blank">chemaxon.struc.MoleculeGraph.replaceBond(MolBond, MolBond)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.setEdge(int i, MolBond edge)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#setBond(int, chemaxon.struc.MolBond)" target="_blank">chemaxon.struc.MoleculeGraph.setBond(int, MolBond)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.setNode(int i, MolAtom node)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#setAtom(int, chemaxon.struc.MolAtom)" target="_blank">chemaxon.struc.MoleculeGraph.setAtom(int, MolAtom)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.setSetSeqs(int id)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#setAtomSetSeq(int)" target="_blank">chemaxon.struc.MoleculeGraph.setAtomSetSeq(int)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.setValenceCheckEnabled(boolean b)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#setValenceCheckState(ValenceCheckState)" target="_blank">chemaxon.struc.MoleculeGraph.setValenceCheckState(ValenceCheckState)</a>
<br>
</td>
</tr>
<tr>
<td><code>MoleculeGraph.sortEdgesAccordingTo(MolBond[] order)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#sortBondsAccordingTo(chemaxon.struc.MolBond[])" target="_blank">chemaxon.struc.MoleculeGraph.sortBondsAccordingTo(MolBond[])</a>
<br>
</td>
</tr>
<tr>
<td><code>RgMolecule.createMol(String fmt)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/io/MolImportModule.html#createMol()" target="_blank">chemaxon.marvin.io.MolImportModule.createMol()</a>
<br>Replacement method works for all file formats.</td>
</tr>
<tr>
<td><code>RgMolecule.getBtab()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/RgMolecule.html#getBondTable()" target="_blank">chemaxon.struc.RgMolecule.getBondTable()</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/core/util/BondTable.html#getMatrixArray()" target="_blank">chemaxon.core.util.BondTable.getMatrixArray()</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>molecule.getBondTable().getMatrixArray();</pre>
</td>
</tr>
<tr>
<td><code>RgMolecule.setAbsStereo(boolean c, int i, int j)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/RgMolecule.html#getRgroupMember(int, int)" target="_blank">chemaxon.struc.RgMolecule.getRgroupMember(int, int)</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/MoleculeGraph.html#setAbsStereo(boolean)" target="_blank">chemaxon.struc.MoleculeGraph.setAbsStereo(boolean)</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>rgMolecule.getRgroupMember(i, j).setAbsStereo(c);</pre>
</td>
</tr>
<tr>
<td><code>RxnMolecule.addStructure(Molecule m, int type, boolean beNew)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/RxnMolecule.html#addComponent(chemaxon.struc.Molecule, int, boolean)" target="_blank">chemaxon.struc.RxnMolecule.addComponent(Molecule, int, boolean)</a>
<br>
</td>
</tr>
<tr>
<td><code>RxnMolecule.addStructure(Molecule m, int type)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/RxnMolecule.html#addComponent(chemaxon.struc.Molecule, int)" target="_blank">chemaxon.struc.RxnMolecule.addComponent(Molecule, int)</a>
<br>
</td>
</tr>
<tr>
<td><code>RxnMolecule.getBtab()</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/RxnMolecule.html#getBondTable()" target="_blank">chemaxon.struc.RxnMolecule.getBondTable()</a>
<br>
<a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/core/util/BondTable.html#getMatrixArray()" target="_blank">chemaxon.core.util.BondTable.getMatrixArray()</a>
<br>
<p>
<i>Usage:</i>
</p>
<pre>molecule.getBondTable().getMatrixArray();</pre>
</td>
</tr>
<tr>
<td><code>RxnMolecule.getStructure(int flags, int i)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/RxnMolecule.html#getComponent(int, int)" target="_blank">chemaxon.struc.RxnMolecule.getComponent(int, int)</a>
<br>
</td>
</tr>
<tr>
<td><code>RxnMolecule.getStructureCount(int type)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/RxnMolecule.html#getComponentCount(int)" target="_blank">chemaxon.struc.RxnMolecule.getComponentCount(int)</a>
<br>
</td>
</tr>
<tr>
<td><code>RxnMolecule.removeStructure(int flags, int i)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/struc/RxnMolecule.html#removeComponent(int, int)" target="_blank">chemaxon.struc.RxnMolecule.removeComponent(int, int)</a>
<br>
</td>
</tr>
</table>
<h3>
<strong><code>chemaxon.struc.prop</code></strong>
</h3>
<table border="0" cellspacing="0" cellpadding="5" class="grid">
<tr style="text-align:left">
<td align="center"><strong>Name of deprecated class, field or method</strong></td><td align="center"><strong>Removal version</strong></td><td align="center"><strong>Recommended class, field or method</strong></td>
</tr>
<tr>
<td><code>MMoleculeProp.MMoleculeProp(String sval)</code></td><td align="center">Marvin 6.0</td><td><a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/io/MPropHandler.html#createMMoleculeProp(java.lang.String)" target="_blank">chemaxon.marvin.io.MPropHandler.createMMoleculeProp(String)</a>
<br>
</td>
</tr>
</table>
</body>
</html>
