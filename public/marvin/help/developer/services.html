<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en">
	
	<head>
		<meta http-equiv="content-type" content="text/html;charset=UTF-8" />
		<meta name="description" content="Developer's Guide of Marvin Services" />
		<meta name="keywords" content="marvin,service,soap,wsdl,webservice,json-rpc,xml-rpc,localservice" />
		<meta name="author" content="<PERSON><PERSON><PERSON>, <PERSON>" />
		<link rel="stylesheet" type="text/css" href="../marvinmanuals.css" />
		<title>Marvin Services</title>
	</head>
	
	<body>
		<h1><PERSON> Services</h1>
		<h2>Contents</h2>
		<ul>
			<li><a href="#intro">Introduction</a></li>
			<li><a href="#manage">Manage services</a></li>
			<li>
				<a href="#implement">Service implementations</a>
				<ul>
					<li><a href="#local">Local Service</a></li>
					<li><a href="#soap">WSDL/SOAP</a></li>
					<li><a href="#xmlrpc">XML-RPC</a></li>
					<li><a href="#jsonrpc">JSON-RPC</a></li>
					<li><a href="#http">HTTP</a></li>
				</ul>
			</li>
			<li><a href="#config">Configuration</a></li>
			<li><a href="#calling">Calling services</a></li>
			<li><a href="#result">Viewing the results</a></li>
		</ul>
	
		<h2><a class="anchor" name="intro">Introduction</a></h2>
		<p>Marvin Services provides you seamless integration of third-party calculations into MarvinSketch,
		cxcalc, and via Chemical Terms into Instant JChem or JChem for Excel. Industrial standard
		solutions like SOAP with WSDL are supported along with the more lightweight XML-RPC or JSON-RPC
		protocols. Java based calculations can be called without server, directly from the jar file.</p>
		
		<p>Easy to use graphical interface for convenient usage in MarvinSketch, a single configuration
		file for easy maintenance, and automatic integration to other products mentioned above.</p> 
		
		<h2><a class="anchor" name="manage">Manage Marvin services</a></h2>
		<p>Configure services in MarvinSketch preferences with provided graphical user interface.
		The preferences tab offers all available service implementation with editor support.
		For custom service implementations or alternative editors, user-defined mapping can be used
		by placing a file named <code>servicedescriptoreditormapping.xml</code> to the user's ChemAxon
		directory - usually located in user home as .chemaxon or ChemAxon, depending on operating
		system. The file should be valid to the following XSD schema:
		</p>
		<pre style='background:#fbfbfb;'>
	&lt;?xml version="1.0" encoding="utf-8"?&gt;
	&lt;xs:schema elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema"&gt;
	&lt;xs:attribute name="class" type="xs:string" /&gt;
	&lt;xs:attribute name="editor" type="xs:string" /&gt;
	&lt;xs:complexType name="descriptorType"&gt;
	  &lt;xs:attribute ref="class" /&gt;
	  &lt;xs:attribute ref="editor" /&gt;
	&lt;/xs:complexType&gt;
	&lt;xs:element name="DescriptorEditorMapping"&gt;
	  &lt;xs:complexType&gt;
	    &lt;xs:sequence&gt;
	      &lt;xs:element name="Descriptor" type="descriptorType" minOccurs="0" maxOccurs="unbounded" /&gt;
	    &lt;/xs:sequence&gt;
	  &lt;/xs:complexType&gt;
	&lt;/xs:element&gt;
	&lt;/xs:schema&gt;
		</pre>
				
		<p>The <code>code</code> attribute is the class name of the <code>ServiceDescriptor</code> implementation,
		and the <code>editor</code> attribute is the class name of the <code>ServiceDescriptorEditor</code>
		implementation.</p>
		
		<p>Services saved by MarvinSketch are available by their names in cxcalc and as Chemical Terms function.
		Arguments also can be referred by their names set at the editor. Please note that the built-in editor has
		type restrictions that can be avoided by direct access of the Marvin Services API. The most important
		types such as <code>MDocument</code>, <code>Molecule</code>, <code>String</code>, <code>Integer</code>,
		<code>Long</code>, <code>Double</code>, <code>Float</code> and <code>Boolean</code> are available.</p>

		<h2><a class="anchor" name="implement">Service implementations</a></h2>
		<p>Although the most common services are already implemented, Marvin Services provides an extensible API 
		to implement custom services. Please note that some implementations has type restrictions handled by the
		built-in editor. The supported service protocols listed below:
		</p>

		<h4><a class="anchor" name="local">Local Service</a></h4>
		<p>Local service is for accessing third party java functions. It does not require server or network
		connection. Java Archive (JAR) file acts as "server", any public class with default constructor acts
		as "service", and all the public method can be called. Please mind that the build-in editor may hide
		methods that requires unsupported argument type. Direct access of the Marvin Services API does not
		have this restriction, any type can be used. </p>

		<p>Local Service is the most easy way to embed third-party calculation to MarvinSketch application,
		cxcalc or Chemical Terms, however java coding is required to assemble the jar files. Also note that
		these services can not be accessed in non-java environment such as Marvin .NET or JChem for Excel.</p>
		
		<p>Keep in mind that classes used via service call should be stateless, as each service call will
		create a new instance of the class by the default constructor before calling the function.</p>

		<p>Local Service makes good use of the <code>Alias</code> and <code>Description</code> annotations. Any methods annotated can
		provide default names and description for services and arguments. Also, these aliases are available from cxcalc
		as well - so a default service and argument name can be guaranteed by the class author.	</p>
		
		<h5>Calling Local Service from API</h5>
		<p>The following code snippet calls the <code>Integer countAtoms(Molecule)</code> function of
		<code>example.services.SampleService</code> class located in <a href="localserviceexample.jar">localserviceexample.jar</a>.
		</p>
		<pre style='color:#000000;background:#fbfbfb;'>
	<span style='color:#3f7f59; '>// input molecule</span>
	Molecule input = MolImporter.importMol(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>c1ccncc1</span><span style='color:#2a00ff; '>"</span>);

	<span style='color:#3f7f59; '>// initialize descriptor</span>
	LocalServiceDescriptor descriptor = <span style='color:#7f0055; font-weight:bold; '>new</span> LocalServiceDescriptor();
	descriptor.setURL(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>/path/to/localserviceexample.jar</span><span style='color:#2a00ff; '>"</span>);
	descriptor.setClassName(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>example.services.SampleService</span><span style='color:#2a00ff; '>"</span>);
	descriptor.setMethodName(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>countAtoms</span><span style='color:#2a00ff; '>"</span>);
	descriptor.addArgument(ServiceArgument.createArgument(<span style='color:#7f0055; font-weight:bold; '>new</span> Molecule()));

	<span style='color:#3f7f59; '>// asynchronous call</span>
	descriptor.getServiceHandler().callService(descriptor, <span style='color:#7f0055; font-weight:bold; '>new</span> AsyncCallback&lt;Integer&gt;() {

	    <span style='color:#808080; '>@Override</span>
	    <span style='color:#7f0055; font-weight:bold; '>public</span> <span style='color:#7f0055; font-weight:bold; '>void</span> onSuccess(Integer result) {
	        System.out.println(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>Asynchronous call returned </span><span style='color:#2a00ff; '>"</span> + result);
	    }

	    <span style='color:#808080; '>@Override</span>
	    <span style='color:#7f0055; font-weight:bold; '>public</span> <span style='color:#7f0055; font-weight:bold; '>void</span> onFailure(ServiceException caught) {
	        System.err.println(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>Asynchronous call failed.</span><span style='color:#2a00ff; '>"</span>);
	    }
	}, input);

	<span style='color:#3f7f59; '>// synchronized call</span>
	Object result = null;
	<span style='color:#7f0055; font-weight:bold; '>try</span> {
  	    result = descriptor.getServiceHandler().callService(descriptor, input);
	} <span style='color:#7f0055; font-weight:bold; '>catch</span> (ServiceException e) {
	    System.err.println(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>Service call failed.</span><span style='color:#2a00ff; '>"</span>);
	}
	System.out.println(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>Synchronized call returned </span><span style='color:#2a00ff; '>"</span> + result);
		</pre>

		<h5>Use annotations to define default names and description</h5>
		<p>Local Service can look up default service and argument names, as well as description information from annotations. These values used in MarvinSketch
		when adding the Local Service to the services list by automatically completing the form. The values can be edited manually, but the defaults are always
		available from Chemical Terms or cxcalc - as well as the optionally overwritten ones. You can find a sample class can be used as a Local Service below.
		To download the sample service jar file with source, click <a href="localserviceexample.jar">here</a>.</p>
		
		<pre style='color:#000000;background:#fbfbfb;'>
<span style='color:#3f7f59; '>/*</span>
<span style='color:#3f7f59; '>&#xa0;* Copyright (c) 1998-2013 ChemAxon Ltd. All Rights Reserved.</span>
<span style='color:#3f7f59; '>&#xa0;*</span>
<span style='color:#3f7f59; '>&#xa0;* This software is the confidential and proprietary information of</span>
<span style='color:#3f7f59; '>&#xa0;* ChemAxon. You shall not disclose such Confidential Information</span>
<span style='color:#3f7f59; '>&#xa0;* and shall use it only in accordance with the terms of the agreements</span>
<span style='color:#3f7f59; '>&#xa0;* you entered into with ChemAxon.</span>
<span style='color:#3f7f59; '>&#xa0;*</span>
<span style='color:#3f7f59; '>&#xa0;*/</span>
package example.services;

<span style='color:#7f0055; font-weight:bold; '>import</span> chemaxon.formats.MolFormatException;
<span style='color:#7f0055; font-weight:bold; '>import</span> chemaxon.formats.MolImporter;
<span style='color:#7f0055; font-weight:bold; '>import</span> chemaxon.marvin.services.localservice.Alias;
<span style='color:#7f0055; font-weight:bold; '>import</span> chemaxon.marvin.services.localservice.Description;
<span style='color:#7f0055; font-weight:bold; '>import</span> chemaxon.struc.Molecule;

<span style='color:#3f5fbf; '>/**</span>
<span style='color:#3f5fbf; '>&#xa0;* This is a sample class to demonstrate how to write</span>
<span style='color:#3f5fbf; '>&#xa0;* classes for Marvin Services Local Service implementation.</span>
<span style='color:#3f5fbf; '>&#xa0;* </span><span style='color:#7f9fbf; font-weight:bold; '>@author</span><span style='color:#3f5fbf; '> Istvan Rabel</span>
<span style='color:#3f5fbf; '>&#xa0;*/</span>
<span style='color:#7f0055; font-weight:bold; '>public</span> <span style='color:#7f0055; font-weight:bold; '>class</span> SampleService {

    <span style='color:#3f5fbf; '>/**</span>
<span style='color:#3f5fbf; '>&#xa0;&#xa0;&#xa0;&#xa0;&#xa0;* Returns the number of atoms in the specified molecule</span>
<span style='color:#3f5fbf; '>&#xa0;&#xa0;&#xa0;&#xa0;&#xa0;* </span><span style='color:#7f9fbf; font-weight:bold; '>@param</span><span style='color:#3f5fbf; '> molecule the molecule being checked</span>
<span style='color:#3f5fbf; '>&#xa0;&#xa0;&#xa0;&#xa0;&#xa0;* </span><span style='color:#7f9fbf; font-weight:bold; '>@return</span><span style='color:#3f5fbf; '> the number of atoms in the molecule</span>
<span style='color:#3f5fbf; '>&#xa0;&#xa0;&#xa0;&#xa0;&#xa0;*/</span>
    <span style='color:#3f7f59; '>/* </span>
<span style='color:#3f7f59; '>&#xa0;&#xa0;&#xa0;&#xa0;&#xa0;* (non-javadoc)</span>
<span style='color:#3f7f59; '>&#xa0;&#xa0;&#xa0;&#xa0;&#xa0;* This method can be called as a LocalService from</span>
<span style='color:#3f7f59; '>&#xa0;&#xa0;&#xa0;&#xa0;&#xa0;* Marvin Sketch, cxcalc and Chemical Terms.</span>
<span style='color:#3f7f59; '>&#xa0;&#xa0;&#xa0;&#xa0;&#xa0;* Annotations are used to provide default names</span>
<span style='color:#3f7f59; '>&#xa0;&#xa0;&#xa0;&#xa0;&#xa0;* for Service and arguments, as well as a description.</span>
<span style='color:#3f7f59; '>&#xa0;&#xa0;&#xa0;&#xa0;&#xa0;*/</span>
    <span style='color:#808080; '>@Alias</span>(name=<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>AtomCount</span><span style='color:#2a00ff; '>"</span>, params={<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>Structure</span><span style='color:#2a00ff; '>"</span>})
    <span style='color:#808080; '>@Description</span>(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>Returns the number of atoms in the structure</span><span style='color:#2a00ff; '>"</span>)
    <span style='color:#7f0055; font-weight:bold; '>public</span> Integer countAtoms(Molecule molecule) {
        <span style='color:#7f0055; font-weight:bold; '>return</span> molecule.getAtomCount();
    }
    
    <span style='color:#3f5fbf; '>/**</span>
<span style='color:#3f5fbf; '>&#xa0;&#xa0;&#xa0;&#xa0;&#xa0;* Returns a formatted (HTML) message with the number of</span>
<span style='color:#3f5fbf; '>&#xa0;&#xa0;&#xa0;&#xa0;&#xa0;* atoms in the molecule imported from argument.</span>
<span style='color:#3f5fbf; '>&#xa0;&#xa0;&#xa0;&#xa0;&#xa0;* </span><span style='color:#7f9fbf; font-weight:bold; '>@param</span><span style='color:#3f5fbf; '> moleculeString a string representation of a molecule</span>
<span style='color:#3f5fbf; '>&#xa0;&#xa0;&#xa0;&#xa0;&#xa0;* </span><span style='color:#7f9fbf; font-weight:bold; '>@return</span><span style='color:#3f5fbf; '> a formatted (HTML) message with the number of atoms</span>
<span style='color:#3f5fbf; '>&#xa0;&#xa0;&#xa0;&#xa0;&#xa0;*/</span>
    <span style='color:#3f7f59; '>/* </span>
<span style='color:#3f7f59; '>&#xa0;&#xa0;&#xa0;&#xa0;&#xa0;* (non-javadoc)</span>
<span style='color:#3f7f59; '>&#xa0;&#xa0;&#xa0;&#xa0;&#xa0;* This method can be called as a LocalService from</span>
<span style='color:#3f7f59; '>&#xa0;&#xa0;&#xa0;&#xa0;&#xa0;* Marvin Sketch, cxcalc and Chemical Terms.</span>
<span style='color:#3f7f59; '>&#xa0;&#xa0;&#xa0;&#xa0;&#xa0;* Annotations are used to provide default names</span>
<span style='color:#3f7f59; '>&#xa0;&#xa0;&#xa0;&#xa0;&#xa0;* for Service and arguments, as well as a description.</span>
<span style='color:#3f7f59; '>&#xa0;&#xa0;&#xa0;&#xa0;&#xa0;*/</span>
    <span style='color:#808080; '>@Alias</span>(name=<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>AtomCountText</span><span style='color:#2a00ff; '>"</span>, params={<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>Molecule</span><span style='color:#2a00ff; '>"</span>})
    <span style='color:#808080; '>@Description</span>(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>Returns a formatted text message containing the number of atoms in the structure.</span><span style='color:#2a00ff; '>"</span>)
    <span style='color:#7f0055; font-weight:bold; '>public</span> <span style='color:#7f0055; font-weight:bold; '>String</span> countAtomsHTML(<span style='color:#7f0055; font-weight:bold; '>String</span> moleculeString) {
        
        <span style='color:#3f7f59; '>// import the molecule</span>
        Molecule molecule = null;
        <span style='color:#7f0055; font-weight:bold; '>try</span> {
            molecule = MolImporter.importMol(moleculeString);
        } <span style='color:#7f0055; font-weight:bold; '>catch</span> (MolFormatException e) {
            <span style='color:#3f7f59; '>// invalid molecule string</span>
            molecule = <span style='color:#7f0055; font-weight:bold; '>new</span> Molecule();
        }
    
        <span style='color:#3f7f59; '>// get the atom count</span>
        <span style='color:#7f0055; font-weight:bold; '>int</span> value = countAtoms(molecule);
    
        <span style='color:#3f7f59; '>// build and return the result string</span>
        StringBuilder builder = <span style='color:#7f0055; font-weight:bold; '>new</span> StringBuilder(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>&lt;html&gt;&lt;body&gt;</span><span style='color:#2a00ff; '>"</span>);
        <span style='color:#7f0055; font-weight:bold; '>if</span>(value &gt; 1) {
            builder.append(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>The structure has &lt;font color='blue'&gt;&lt;b&gt;</span><span style='color:#2a00ff; '>"</span>);
            builder.append(value);
            builder.append(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>&lt;/b&gt;&lt;/font&gt; atoms.</span><span style='color:#2a00ff; '>"</span>);
        } <span style='color:#7f0055; font-weight:bold; '>else</span> {
            builder.append(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>The structure has &lt;font color='red'&gt;&lt;i&gt;</span><span style='color:#2a00ff; '>"</span>
                + (value == 0 ? <span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>no atoms</span><span style='color:#2a00ff; '>"</span> : <span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>only one atom</span><span style='color:#2a00ff; '>"</span>)
                + <span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>&lt;/i&gt;&lt;/font&gt;.</span><span style='color:#2a00ff; '>"</span>);
        }
        builder.append(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>&lt;/body&gt;&lt;/html&gt;</span><span style='color:#2a00ff; '>"</span>);
        <span style='color:#7f0055; font-weight:bold; '>return</span> builder.toString();
    }
    
}
		</pre>

	
		<h4><a class="anchor" name="soap">WSDL/SOAP</a></h4>
		<p>SOAP Services defined by WSDL can be accessed via Marvin Services. The user interface supports automatic discovery of
		various options from local file system, or any available URL.</p>
		<h5>Supported Types and their mapping</h5>
		<table style="border: solid 2px #006579; padding: 2px">
			<tr><td>SOAP</td><td>Java</td></tr>
			<tr><td><code>xs:string</code></td><td><code>java.lang.String</code></td></tr>
			<tr><td><code>xs:int</code></td><td><code>java.lang.Integer</code></td></tr>
			<tr><td><code>xs:double</code></td><td><code>java.lang.Double</code></td></tr>
			<tr><td><code>xs:float</code></td><td><code>java.lang.Float*</code></td></tr>
			<tr><td><code>xs:boolean</code></td><td><code>java.lang.Boolean</code></td></tr>
			<tr><td><code>xs:anytype</code></td><td><code>java.lang.Object</code></td></tr>
		</table>
		<p><i>* Please note that WSDL Float support is not complete.</i></p>


		<h4><a class="anchor" name="xmlrpc">XML-RPC</a></h4>
		<p>XML-RPC Service uses the standard XML-RPC protocol to access remote calculations. The XML for
		the request is automatically generated from the provided attributes and arguments, and the response
		is parsed to unwrap the result of service call.</p>
		
		<h5>Calling XML-RPC Service from API</h5>
		<pre style='color:#000000;background:#fbfbfb;'>
    XMLRPCServiceDescriptor descriptor = new XMLRPCServiceDescriptor();
    descriptor.setURL(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>http://sample.server.net/xmlrpc</span><span style='color:#2a00ff; '>"</span>);
    descriptor.setMethodName(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>SampleService.countAtoms</span><span style='color:#2a00ff; '>"</span>);
    
    descriptor.addArgument(ServiceArgument.createArgument(<span style='color:#2a00ff; '>""</span>));
    descriptor.addArgument(ServiceArgument.createArgument(<span style='color:#2a00ff; '>""</span>));

    Object result = null;
    try {
        result = descriptor.getServiceHandler().callService(descriptor, <span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>C1CCNCCC1</span><span style='color:#2a00ff; '>"</span>, <span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>C</span><span style='color:#2a00ff; '>"</span>);
    } catch (ServiceException e) {
        System.err.println(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>Service call failed.</span><span style='color:#2a00ff; '>"</span>);
    }
    System.out.println(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>Synchronized call returned: </span><span style='color:#2a00ff; '>"</span> + <span style='color:#7f0055; font-weight:bold; '>String</span>.valueOf(result));

    
    descriptor.getServiceHandler().callService(descriptor, new AsyncCallback&lt;Integer&gt;() {

        <span style='color:#808080; '>@Override</span>
        public <span style='color:#7f0055; font-weight:bold; '>void</span> onSuccess(Integer result) {
            System.out.println(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>Asynchronous call returned: </span><span style='color:#2a00ff; '>"</span> + result);
        }

        <span style='color:#808080; '>@Override</span>
        public <span style='color:#7f0055; font-weight:bold; '>void</span> onFailure(ServiceException caught) {
            System.err.println(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>Asynchronous call failed.</span><span style='color:#2a00ff; '>"</span>);
        }
        
    }, <span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>C1CCNCCC1</span><span style='color:#2a00ff; '>"</span>, <span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>C</span><span style='color:#2a00ff; '>"</span>);
		</pre>
		
		
		<h4><a class="anchor" name="jsonrpc">JSON-RPC</a></h4>
		<p>Marvin supports protocol versions 1.0 and 1.1. JSON Schema Service Descriptor can also be used
		for runtime parameter discovery. The response MUST be a textual representation of any finite combinations
		of <code>java.lang.Boolean</code>, <code>java.lang.Number</code>, <code>java.lang.String</code>, <code>java.lang.Object[]</code>,
		<code>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</code>, and <code>null</code></p>

		<h5>Calling JSON-RPC Service from API</h5>
		<pre style='color:#000000;background:#fbfbfb;'>
    JsonServiceDescriptor descriptor = new JsonServiceDescriptor();
    descriptor.setURL(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>http://api.geonames.org/</span><span style='color:#2a00ff; '>"</span>);
    descriptor.setMethodName(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>citiesJSON</span><span style='color:#2a00ff; '>"</span>);
    
    descriptor.addArgument(ServiceArgument.createArgument(<span style='color:#2a00ff; '>"north"</span>, new Double(0)));
    descriptor.addArgument(ServiceArgument.createArgument(<span style='color:#2a00ff; '>"south"</span>, new Double(0)));
    descriptor.addArgument(ServiceArgument.createArgument(<span style='color:#2a00ff; '>"east"</span>, new Double(0)));
    descriptor.addArgument(ServiceArgument.createArgument(<span style='color:#2a00ff; '>"west"</span>, new Double(0)));
    descriptor.addArgument(ServiceArgument.createArgument(<span style='color:#2a00ff; '>"language"</span>, <span style='color:#2a00ff; '>""</span>));
    descriptor.addArgument(ServiceArgument.createArgument(<span style='color:#2a00ff; '>"username"</span>, <span style='color:#2a00ff; '>""</span>));

    Object result = null;
    try {
        result = descriptor.getServiceHandler().callService(descriptor, 44.1, -9.9, 22.4, 55.2, <span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>en</span><span style='color:#2a00ff; '>"</span>, <span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>demo</span><span style='color:#2a00ff; '>"</span>);
    } catch (ServiceException e) {
        System.err.println(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>Service call failed.</span><span style='color:#2a00ff; '>"</span>);
    }
    StringBuilder builder = new StringBuilder();
    Object[] array = (Object[]) ((Map&lt;<span style='color:#7f0055; font-weight:bold; '>String</span>, Object&gt;)result).get(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>geonames</span><span style='color:#2a00ff; '>"</span>);
    <span style='color:#7f0055; font-weight:bold; '>for</span>(Object obj : array) {
        builder.append(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>Synchronized call returned: </span><span style='color:#2a00ff; '>"</span> + ((Map&lt;<span style='color:#7f0055; font-weight:bold; '>String</span>, Object&gt;)obj).get(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>name</span><span style='color:#2a00ff; '>"</span>) + <span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>\n</span><span style='color:#2a00ff; '>"</span>);
    }
    System.out.println(builder.toString());

    descriptor.getServiceHandler().callService(descriptor, new AsyncCallback&lt;Map&lt;<span style='color:#7f0055; font-weight:bold; '>String</span>, Object&gt;&gt;() {

        <span style='color:#808080; '>@Override</span>
        public <span style='color:#7f0055; font-weight:bold; '>void</span> onSuccess(Map&lt;<span style='color:#7f0055; font-weight:bold; '>String</span>, Object&gt; result) {
            StringBuilder builder = new StringBuilder();
            Object[] array = (Object[]) result.get(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>geonames</span><span style='color:#2a00ff; '>"</span>);
            <span style='color:#7f0055; font-weight:bold; '>for</span>(Object obj : array) {
                builder.append(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>Asynchronous call returned: </span><span style='color:#2a00ff; '>"</span> + ((Map&lt;<span style='color:#7f0055; font-weight:bold; '>String</span>, Object&gt;)obj).get(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>name</span><span style='color:#2a00ff; '>"</span>) + <span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>\n</span><span style='color:#2a00ff; '>"</span>);
            }
            System.out.println(builder.toString());
        }

        <span style='color:#808080; '>@Override</span>
        public <span style='color:#7f0055; font-weight:bold; '>void</span> onFailure(ServiceException caught) {
            System.err.println(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>Asynchronous call failed.</span><span style='color:#2a00ff; '>"</span>);
        }
        
    }, 44.1, -9.9, 22.4, 55.2, <span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>en</span><span style='color:#2a00ff; '>"</span>, <span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>demo</span><span style='color:#2a00ff; '>"</span>);
		</pre>


		<h4><a class="anchor" name="http">HTTP</a></h4>
		<p>HTTP Service is the most lightweight and unrestricted remote service: it supports <code>POST</code>
		and <code>GET</code> requests to a predefined URL. Result is retrieved as is.</p>
		<h5>Accessing chemicalize.org via HTTP Service from API</h5>
	<pre style='color:#000000;background:#fbfbfb;'>
    HTTPServiceDescriptor descriptor = new HTTPServiceDescriptor();
    descriptor.setURL(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>http://chemicalize.org/tomcat-files/datapage.jsp</span><span style='color:#2a00ff; '>"</span>);
    
    descriptor.addArgument(ServiceArgument.createArgument(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>mol</span><span style='color:#2a00ff; '>"</span>, <span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>"</span>));
    descriptor.addArgument(ServiceArgument.createArgument(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>special</span><span style='color:#2a00ff; '>"</span>, <span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>"</span>));
    descriptor.addArgument(ServiceArgument.createArgument(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>pka_width</span><span style='color:#2a00ff; '>"</span>, 0));

    Object result = null;
    try {
        result = descriptor.getServiceHandler().callService(descriptor, <span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>c1ccnccc1</span><span style='color:#2a00ff; '>"</span>, <span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>pka</span><span style='color:#2a00ff; '>"</span>, 300);
    } catch (ServiceException e) {
       System.err.println(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>Service call failed.</span><span style='color:#2a00ff; '>"</span>);
    }
    System.out.println(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>Synchronized call returned: </span><span style='color:#2a00ff; '>"</span> + <span style='color:#7f0055; font-weight:bold; '>String</span>.valueOf(result));

    
    descriptor.getServiceHandler().callService(descriptor, new AsyncCallback&lt;<span style='color:#7f0055; font-weight:bold; '>String</span>&gt;() {

        <span style='color:#808080; '>@Override</span>
        public <span style='color:#7f0055; font-weight:bold; '>void</span> onSuccess(<span style='color:#7f0055; font-weight:bold; '>String</span> result) {
            System.out.println(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>Aynchronous call returned: </span><span style='color:#2a00ff; '>"</span> + result);
        }

        <span style='color:#808080; '>@Override</span>
        public <span style='color:#7f0055; font-weight:bold; '>void</span> onFailure(ServiceException caught) {
            System.err.println(<span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>Asynchronous call failed.</span><span style='color:#2a00ff; '>"</span>);
        }
        
    }, <span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>c1ccnccc1</span><span style='color:#2a00ff; '>"</span>, <span style='color:#2a00ff; '>"</span><span style='color:#2a00ff; '>pka</span><span style='color:#2a00ff; '>"</span>, 300);
		</pre>
		
		<h2><a class="anchor" name="config">Configuration</a></h2>
		<p>The location of service configuration file is provided by the <code>servicesConfigURL</code>
		User Setting, and defaults to <code>servicesconfig.xml</code> in the users's ChemAxon folder - located
		in user home as .chemaxon or ChemAxon, depending on operating system. The file should be valid for
		the following XSD schema:
		</p>
				<pre style='background:#fbfbfb;'>
	&lt;?xml version="1.0" encoding="utf-8"?&gt;
	&lt;xs:schema elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema"&gt;

	&lt;!--  definition of attributes --&gt;
	&lt;xs:attribute name="alias"   type="xs:string" /&gt;
	&lt;xs:attribute name="name"   type="xs:string" /&gt;
	&lt;xs:attribute name="class"  type="xs:string" /&gt;
	&lt;xs:attribute name="url"    type="xs:string" /&gt;
	&lt;xs:attribute name="method" type="xs:string" /&gt;
	&lt;xs:attribute name="descriptor" type="xs:string" default="chemaxon.marvin.services.descriptors.LocalServiceDescriptor"/&gt;
	&lt;xs:attribute name="value" type="xs:string" /&gt;
	&lt;xs:attribute name="expression" type="xs:string" /&gt;

	&lt;!-- definition of complex types --&gt;
	&lt;xs:complexType name="evaluationType"&gt;
	  &lt;xs:simpleContent&gt;
	    &lt;xs:extension base="xs:string"&gt;
	      &lt;xs:attribute ref="class" use="optional" default="chemaxon.marvin.services.ChemicalTermsArgument" /&gt;
	      &lt;xs:attribute ref="expression" use="optional" /&gt;
	    &lt;/xs:extension&gt;
	  &lt;/xs:simpleContent&gt;
	&lt;/xs:complexType&gt;

	&lt;xs:complexType name="argumentType"&gt;
	  &lt;xs:all&gt;
	    &lt;xs:element name="Evaluate" type="evaluationType" minOccurs="0" maxOccurs="1" /&gt;
	  &lt;/xs:all&gt;
	  &lt;xs:attribute name="class" use="optional" default="java.lang.String"&gt;
	    &lt;xs:simpleType&gt;
	      &lt;xs:restriction base="xs:string"&gt;
	        &lt;xs:enumeration value="java.lang.Integer" /&gt;
	        &lt;xs:enumeration value="java.lang.Float" /&gt;
	        &lt;xs:enumeration value="java.lang.Double" /&gt;
	        &lt;xs:enumeration value="java.lang.String" /&gt;
	        &lt;xs:enumeration value="java.lang.Long" /&gt;
	        &lt;xs:enumeration value="java.lang.Boolean" /&gt;
	        &lt;xs:enumeration value="chemaxon.struc.Molecule" /&gt;
	        &lt;xs:enumeration value="chemaxon.struc.MDocument" /&gt;
	      &lt;/xs:restriction&gt;
	    &lt;/xs:simpleType&gt;
	  &lt;/xs:attribute&gt;
	  &lt;xs:attribute ref="value" use="optional" /&gt;
	  &lt;xs:attribute ref="name" use="optional" default="Unnamed" /&gt;
	  &lt;xs:attribute ref="alias" use="optional" /&gt;	
	&lt;/xs:complexType&gt;

	&lt;xs:complexType name="serviceType"&gt;
	  &lt;xs:all&gt;
	    &lt;xs:element name="Arguments" minOccurs="0"&gt;
	      &lt;xs:complexType&gt;
	        &lt;xs:sequence&gt;
	          &lt;xs:element name="Argument" type="argumentType" maxOccurs="unbounded" /&gt;
	        &lt;/xs:sequence&gt;
	      &lt;/xs:complexType&gt;
	    &lt;/xs:element&gt;
	  &lt;/xs:all&gt;
	  &lt;xs:attribute ref="name" use="required" /&gt;
	  &lt;xs:attribute ref="url" use="required" /&gt;
	  &lt;xs:attribute ref="method" use="required" /&gt;
	  &lt;xs:attribute ref="class" use="optional" /&gt;
	  &lt;xs:attribute ref="descriptor" use="optional" /&gt;
	  &lt;xs:attribute ref="alias" use="optional" /&gt;
	  &lt;xs:anyAttribute processContents="skip" /&gt;
	&lt;/xs:complexType&gt;

	&lt;xs:element name="Services"&gt;
	  &lt;xs:complexType&gt;
	    &lt;xs:sequence&gt;
	      &lt;xs:element name="Service" type="serviceType" minOccurs="0" maxOccurs="unbounded" /&gt;
	    &lt;/xs:sequence&gt;
	  &lt;/xs:complexType&gt;
	&lt;/xs:element&gt;

	&lt;/xs:schema&gt;
				</pre>
		
		<h2><a class="anchor" name="calling">Calling services</a></h2>
		<p>Configured services are accessible via MarvinSketch Tools/Services menu. A disabled entry
		indicates that the protocols for the service are not available - due to missing libraries.
		Calling a service from MarvinSketch will pop-up a dialog with the input data, the attributes
		and the results. Outside MarvinSketch such as cxcalc or Chemical Terms, the services are
		available by their names or aliases.</p>
		
		<h2><a class="anchor" name="result">Viewing the results</a></h2>
		<p>In MarvinSketch the result is recognized automatically: if a Molecule or MDocument is
		returned, it is rendered. All other typed are displayed in a HTML view. Please note that
		returning partial HTML documents can provide nice looking results - with images as well.</p>
		
		<!--
		<p>
			<a href="http://validator.w3.org/check?uri=referer">
				<img src="http://www.w3.org/Icons/valid-xhtml10" alt="Valid XHTML 1.0 Strict" height="31" width="88" />
			</a>
		</p>
		//-->
		
	</body>
</html>