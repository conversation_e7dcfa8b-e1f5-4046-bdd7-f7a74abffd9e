<html>
<head>
<meta NAME="author" CONTENT="Peter <PERSON>">
<link REL ="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css" TITLE="Style">
<title>Marvin modules</title>
</head>

<body>

<h1>Marvin modules</h1>

<p>A modules is a set of class files that is packed in a separate jar (not in
the core <code>jmarvin.jar</code>).
Marvin modules are downloaded at runtime, only as needed.</p>

<table CELLSPACING=0 CELLPADDING=4 BORDER=0 id="colored-grid">
<tr ALIGN="LEFT" VALIGN="TOP"><th>Module name</th><th>Jar file</th>
    <th>Description</th></tr>
<tr><td>AbbrevGroupExport</td><td>sjars/abbrevgroupexport.jar</td><td>Abbreviated group export.</td></tr>
<tr><td>AtomIndexLabeller</td><td>sjars/atomlabellers.jar</td><td>Atom index, displayed as a label.</td></tr>
<tr><td>Base64Export</td><td>sjars/base64export.jar</td><td>Base64 export.</td></tr>
<tr><td>Base64Import</td><td>sjars/base64import.jar</td><td>Base64 import.</td></tr>
<tr><td>Clean2D</td><td>sjars/clean2d.jar</td><td>2D cleaning.</td></tr>
<tr><td>Clean3D</td><td>sjars/clean3d.jar</td><td>3D cleaning.</td></tr>
<tr><td>CmlExport</td><td>sjars/cmlexport.jar</td><td>CML export</td></tr>
<tr><td>CmlImport</td><td>sjars/cmlimport.jar</td><td>CML import</td></tr>
<tr><td>CxsmilesImport</td><td>sjars/smilesimport.jar</td><td>ChemAxon Extended SMILES/SMARTS import.</td></tr>
<tr><td>Dearomata</td><td>sjars/dearomata.jar</td><td>Dearomatization.</td></tr>
<tr><td>DocumentSettings</td><td>sjars/documenthandling.jar</td><td>Document settings.</td></tr>
<tr><td>GzipExport</td><td>sjars/gzipexport.jar</td><td>GZIP export.</td></tr>
<tr><td>GzipImport</td><td>sjars/gzipimport.jar</td><td>GZIP import.</td></tr>
<tr><td>Hydrogenize</td><td>sjars/hydrogenize.jar</td><td>Creates explicit H atoms from implicit ones.</td></tr>
<tr><td>ImageExport</td><td>sjars/imageexport.jar</td><td>Off screen image creation support.</td></tr>
<tr><td>InchiExport</td><td>sjars/inchiexport.jar</td><td>IUPAC InChi export.</td></tr>
<tr><td>InchiImport</td><td>sjars/inchiimport.jar</td><td>IUPAC InChi import.</td></tr>
<tr><td>Jpeg Import/Export</td><td>sjars/jpegio.jar</td><td>JPEG image export.</td></tr>
<tr><td>LicenseManager</td><td>sjars/Plugin.jar</td><td>License manager.</td></tr>
<tr><td>MChart</td><td>sjars/mchart.jar</td><td>Chart.</td></tr>
<tr><td>Mol2Export</td><td>sjars/mol2export.jar</td><td>Tripos Mol2 export.</td></tr>
<tr><td>Mol2Import</td><td>sjars/mol2import.jar</td><td>Tripos Mol2 import.</td></tr>
<tr><td>MolExport</td><td>sjars/molexport.jar</td><td>MDL molfile and compressed molfile export.</td></tr>
<tr><td>MolImport</td><td>sjars/molimport.jar</td><td>MDL molfile and compressed molfile import.</td></tr>
<tr><td>MPolylineModule</td><td>sjars/mpolylinemodule.jar</td><td>Polyline drawing.</td></tr>
<tr><td>MrvExport</td><td>sjars/mrvexport.jar</td><td>MRV export.</td></tr>
<tr><td>MrvImport</td><td>sjars/mrvimport.jar</td><td>MRV import.</td></tr>
<tr><td>MsbmpExport</td><td>sjars/msbmpexport.jar</td><td>MS BMP export.</td></tr>
<tr><td>MTextBoxModule</td><td>sjars/mtextboxmodule.jar</td><td>Textbox drawing.</td></tr>
<tr><td>NullExport</td><td>sjars/nullexport.jar</td><td>Export module with null output.</td></tr>
<tr><td>Parity</td><td>sjars/parity.jar</td><td>Parity.</td></tr>
<tr><td>PdbExport</td><td>sjars/pdbexport.jar</td><td>PDB export.</td></tr>
<tr><td>PdbImport</td><td>sjars/pdbimport.jar</td><td>PDB import.</td></tr>
<tr><td>PeptideExport</td><td>sjars/peptideexport.jar</td><td>Peptide export.</td></tr>
<tr><td>PeptideImport</td><td>sjars/peptideimport.jar</td><td>Peptide import.</td></tr>
<tr><td>Png Import/Export</td><td>sjars/pngio.jar</td><td>PNG export.</td></tr>
<tr><td>PovExport</td><td>sjars/povexport.jar</td><td>POV export.</td></tr>
<tr><td>PpmExport</td><td>sjars/ppmexport.jar</td><td>PPM export.</td></tr>
<tr><td>SmartsAtomQuerifier</td><td>sjars/smarts.jar</td><td>Partial interpretation of smarts atoms.</td></tr>
<tr><td>Spacefill</td><td>sjars/spacefill.jar</td><td>Spacefill.</td></tr>
<tr><td>SuperatomSgroupCoords</td><td>sjars/superatomsgroupcoords.jar</td><td>Recalculates atom coordinates for residue contraction or expansion.</td></tr>
<tr><td>Svg Import/Export</td><td>sjars/svgio.jar</td><td>SVG export</td></tr>
<tr><td>SybylExport</td><td>sjars/sybylexport.jar</td><td>Sybyl Export</td></tr>
<tr><td>SybylImport</td><td>sjars/sybylimport.jar</td><td>Sybyl Import</td></tr>
<tr><td>Threedim</td><td>sjars/threedim.jar</td><td>Threedim.</td></tr>
<tr><td>VectGraphicsExport</td><td>sjars/vectgraphicsexport.jar</td><td>EMF and PDF export.</td></tr>
<tr><td>XyzExport</td><td>sjars/xyzexport.jar</td><td>XYZ export.</td></tr>
<tr><td>XyzImport</td><td>sjars/xyzimport.jar</td><td>XYZ import.</td></tr>
<tr><td>common.swing.AboutJVM</td><td>sjars/common/swing/aboutjvm.jar</td><td>About JVM dialog.</td></tr>
<tr><td>common.swing.EditMolfileFrame</td><td>sjars/common/swing/editmolfileframe.jar</td><td>Molecule source window.</td></tr>
<tr><td>common.swing.ExceptionFrame</td><td>sjars/common/swing/exceptionframe.jar</td><td>Exception window.</td></tr>
<tr><td>common.swing.FontAttrPanel</td><td>sjars/common/swing/fontediting.jar</td><td>Format dialog.</td></tr>
<tr><td>common.swing.Help</td><td>sjars/common/swing/help.jar</td><td>Help window.</td></tr>
<tr><td>common.swing.LicenseHandlerDialog</td><td>sjars/licensehandlerdialog.jar</td><td>License hander dialog.</td></tr>
<tr><td>common.swing.LoadSave</td><td>sjars/common/swing/loadsave.jar</td><td>Load/Save dialog.</td></tr>
<tr><td>common.swing.modules.RegenBonds</td><td>sjars/common/swing/regenbonds.jar</td><td>Dialog box with bond generation options. Selecting Edit/Bonds/Regenerate in the menu brings up this dialog.</td></tr>
<tr><td>common.swing.MolSelectionSmi</td><td>sjars/common/swing/molselectionsmi.jar</td><td>Copy as SMILES.</td></tr>
<tr><td>common.swing.MolSelection</td><td>sjars/common/swing/molselection.jar</td><td>Copy/Paste functionality.</td></tr>
<tr><td>common.swing.MultiMoleculeSave</td><td>sjars/common/swing/multimoleculesave.jar</td><td>Save more molecules.</td></tr>
<tr><td>common.swing.Preferences</td><td>sjars/common/swing/preferences.jar</td><td>Preferences dialog.</td></tr>
<tr><td>common.swing.Print</td><td>sjars/common/swing/print.jar</td><td>Print functionality.</td></tr>
<tr><td>sketch.AtomActions</td><td>sjars/sketch/edit.jar</td><td>Clean ligands.</td></tr>
<tr><td>sketch.AtomMapper</td><td>sjars/atommapper.jar</td><td>Abbreviated group export.</td></tr>
<tr><td>sketch.LineSM</td><td>sjars/sketch/linesm.jar</td><td>Line drawing mode.</td></tr>
<tr><td>sketch.TextBoxSM</td><td>sjars/sketch/boxsm.jar</td><td>Textbox creating mode.</td></tr>
<tr><td>sketch.swing.AttachDataDialog</td><td>sjars/sketch/swing/attachdatadialog.jar</td><td>Attach data dialog box.</td></tr>
<tr><td>sketch.swing.GroupCreationDialog</td><td>sjars/sketch/swing/groupcreationdialog.jar</td><td>Create group dialog.</td></tr>
<tr><td>sketch.swing.MObjectPropertiesDialog</td><td>sjars/sketch/swing/mobjectpropertiesdialog.jar</td><td>Properties dialog of graphical objects.</td></tr>
<tr><td>sketch.swing.MTextBoxEditor</td><td>sjars/sketch/swing/textboxeditor.jar</td><td>Textbox editor.</td></tr>
<tr><td>sketch.swing.PeriodicFrame</td><td>sjars/sketch/swing/periodicframe.jar</td><td>Periodic system window.</td></tr>
<tr><td>sketch.swing.RlogicDialog</td><td>sjars/sketch/swing/rlogicdialog.jar</td><td>Rlogic dialog.</td></tr>
<tr><td>sketch.swing.SketchDnD</td><td>sjars/sketch/swing/sketchdnd.jar</td><td>Drag and Drop functionality in sketch.</td></tr>
<tr><td>sketch.swing.TemplateButtonDnD</td><td>sjars/sketch/swing/templatebuttondnd.jar</td><td>Drag and Drop for the molecule buttons.</td></tr>
<tr><td>sketch.swing.TemplateFrame.java</td><td>sjars/sketch/swing/templateframe.jar</td><td>Template window.</td></tr>
<tr><td>sketch.swing.TemplatePropertiesDialog</td><td>sjars/sketch/swing/templatepropertiesdialog.jar</td><td>Properties dialog for templates</td></tr>
<tr><td>view.NetscapeJavaScripter</td><td>sjars/view/scripter.jar</td><td>To call JavaScript from Java.</td></tr>
<tr><td>view.swing.GridBagView</td><td>sjars/view/swing/gridbagview.jar</td><td>GridBagLayout-like viewer table handler.</td></tr>
<tr><td>view.swing.ImageViewFrame</td><td>sjars/view/swing/imageviewframe.jar</td><td>Image viewer frame.</td></tr>
<tr><td>view.swing.LoadSave</td><td>sjars/view/swing/loadsave.jar</td><td>Load/Save dialog for viewer.</td></tr>
<tr><td>view.swing.MolsFrame</td><td>sjars/view/swing/molsframe.jar</td><td>Display multiple molecules.</td></tr>
<tr><td>view.swing.MViewAnimator</td><td>sjars/view/swing/mviewanimator.jar</td><td>Molecule rotation in the viewer.</td></tr>
<tr><td>view.swing.MViewFrame</td><td>sjars/view/swing/mviewframe.jar</td><td>Molecule viewer frame.</td></tr>
<tr><td>view.swing.MViewSketch</td><td>sjars/view/swing/mviewsketch.jar</td><td>MarvinView/MarvinSketch bridge.</td></tr>
<tr><td>view.swing.PrintView</td><td>sjars/view/swing/printview.jar</td><td>Print all molecules of a MarvinView.</td></tr>
<tr><td>view.swing.TableView</td><td>sjars/view/swing/tableview.jar</td><td>JTable based viewer handle.</td></tr>
<tr><td>view.swing.ViewDnD</td><td>sjars/view/swing/viewdnd.jar</td><td>Drag and Drop for the viewer.</td></tr>
</table>

<center><div class="lenia">&nbsp;</div></center>

<p ALIGN=CENTER><small>Copyright &copy; 1998-2013
<a HREF="http://www.chemaxon.com" TARGET="_top">ChemAxon Ltd.</a>
&nbsp;&nbsp;&nbsp;All rights reserved.</small></p>
</body>
</html>
