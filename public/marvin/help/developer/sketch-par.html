<html>
<head>
<meta NAME="description" CONTENT="MarvinSketch: parameters and events">
<meta NAME="author" CONTENT="Peter Csizmadia">
<link REL ="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css" TITLE="Style">
<title>MarvinSketch: parameters and events</title>
</head>
<body>

<h1>MarvinSketch: parameters and events</h1>
<h3 align=center>Version @MARVINVERSION@</h3>


<blockquote>
<table BORDER=0 cellpadding="4" cellspacing="0" id="colored-grid">
<tr align="CENTER"><th>Parameter</th><th>Meaning</th><th>Default</th></tr>
<tr valign="TOP"><td><code>autoscale</code></td>
    <td><a NAME="parameters.autoscale"></a>How to display a loaded molecule:
	<ul>
	<li><code>&quot;true&quot;</code> - fit to the applet's window</li>
	<li><code>&quot;false&quot;</code> - use default scaling</li>
	</ul>
	</td>
    <td align="CENTER"><code>false</code></td></tr>
<tr valign="TOP"><td><code>viewonly</code></td>
    <td><a NAME="parameters.viewonly"></a>Visualization mode:
	hide editing buttons;
	<code>&quot;true&quot;</code> or <code>&quot;false&quot;</code>.
	</td>
    <td align="CENTER"><code>false</code></td></tr>
<tr valign="TOP"><td><code><strike>verticalbar</strike></code></td>
    <td><a NAME="parameters.verticalbar"></a>Deprecated.
	</td>
    <td align="CENTER"><code>&nbsp;</code></td></tr>
<tr valign="TOP"><td><code>statusBar</code></td>
    <td><a NAME="parameters.statusBar"></a>Enable the status bar.</td>
    <td align="CENTER"><code>false</code></td></tr>
<tr valign="TOP"><td><code>dispQuality</code></td>
    <td><a NAME="parameters.dispQuality"></a>Display quality.
	<ul>
	<li><code>0</code> - low quality, faster rendering</li>
	<li><code>1</code> - high quality (antialiasing), slower rendering</li>
	</ul>
	</td>
    <td align="CENTER"><code>1</code></td></tr>
<tr valign="TOP"><td><code>implicitH</code></td>
    <td><a NAME="parameters.implicitH"></a>How to display H labels.
	<ul>
	<li><code>off</code></li>
	<li><code>hetero</code> - on heteroatoms</li>
	<li><code>heteroterm</code> - on hetero or terminal atoms</li>
	<li><code>all</code> - all atoms</li>
	</ul>
	</td>
    <td align="CENTER"><code>heteroterm</code></td></tr>
<tr valign="TOP"><td><code>explicitH</code></td>
    <td><a NAME="parameters.explicitH"></a>Show explicit hydrogens.
	</td>
    <td align="CENTER"><code>true</code></td></tr>
<tr valign="TOP"><td><code>chiralitySupport</code></td>
    <td><a NAME="parameters.chiralitySupport"></a>When to show atom chirality
	(R/S).
	<ul>
	<li><code>off</code> - never</li>
	<li><code>selected</code> - if chiral flag is set for the molecule
	or the atom's enhanced stereo type is absolute
	</li>
	<li><code>all</code> - always</li>
	</ul>
	</td>
    <td align="CENTER"><code>off</code></td></tr>
<tr valign="TOP"><td><code>ezVisible</code></td>
    <td><a NAME="parameters.ezVisible"></a>Show (<code>true</code>)
	or hide (<code>false</code>) E/Z labels.
	</td>
    <td align="CENTER"><code>false</code></td></tr>
<tr valign="TOP"><td><code><strike>background</strike></code></td>
    <td><a NAME="parameters.background"></a>Deprecated.</td>
    <td>&nbsp;</td></tr>
<tr valign="TOP"><td><code>molbg</code></td>
    <td><a NAME="parameters.molbg"></a>Molecule background color in hexa.
    Sets the background color of the molecule canvas. See also:
    <a href="#parameters.background">background</a></td>
    <td>&nbsp;</td></tr>
<tr valign="TOP"><td><code>colorScheme</code></td>
    <td><a NAME="parameters.colorScheme"></a>Color scheme.
	<ul>
	<li><code>mono</code> - monochrome</li>
	<li><code>cpk</code> - Corey-Pauling-Kultun</li>
	<li><code>shapely</code> -
	    <a HREF="shapely-scheme.html">shapely</a> (residue types)</li>
	<li><code>group</code> - residue sequence numbers</li>
	</ul>
	</td>
    <td align="CENTER"><code>cpk</code></td></tr>
<!-- <tr valign="TOP"><td><code>labels</code></td>
    <td><a NAME="parameters.labels"></a>Atom labels
	<ul>
	<li><code>true</code> - display</li>
	<li><code>false</code> - do not display</li>
	</ul>
	</td>
    <td align="CENTER"><code>true</code></td></tr> -->
<tr valign="TOP"><td><code>rendering</code></td>
    <td><a NAME="3d.rendering"></a>Rendering style.
	<ul>
	<li><code>wireframe</code> - wireframe</li>
	<li><code>wireknobs</code> - wireframe with knobs</li>
	<li><code>sticks</code> - 3D sticks</li>
	<li><code>ballstick</code> - ball &amp; stick</li>
	<li><code>spacefill</code> - balls</li>
	</ul>
	</td>
    <td align="CENTER"><code>wireframe</code></td></tr>
<tr valign="TOP"><td><code>wireThickness</code></td>
    <td><a NAME="3d.wireThickness"></a>Line thickness for
	&quot;wireframe&quot; and &quot;wireknobs&quot;
	<a HREF="#3d.rendering">rendering modes</a>,
	in Angstroms.</td>
    <td align="CENTER">0.064</td></tr>
<tr valign="TOP"><td><code>stickThickness</code></td>
    <td><a NAME="3d.stickThickness"></a>3D stick diameter for
	&quot;sticks&quot; and &quot;ballstick&quot;
	<a HREF="#3d.rendering">rendering modes</a>,
	in Angstroms.</td>
    <td align="CENTER">0.1</td></tr>
<tr valign="TOP"><td><code>ballRadius</code></td>
    <td><a NAME="3d.ballRadius"></a>Ball radius for
	&quot;ballstick&quot; <a HREF="#3d.rendering">rendering mode</a>,
	in units of covalent radius.</td>
    <td align="CENTER">0.5</td></tr>
<tr valign="TOP"><td><code>scale</code></td>
    <td><a NAME="parameters.scale"></a>Magnification.
	A 1.54 &Aring; long C-C bond is magnified to <em>scale</em> pixels.</td>
    <td align="CENTER">26.4</td></tr>
<tr valign="TOP"><td><code>atomFont</code></td>
    <td><a NAME="parameters.atomFont"></a>Atom symbol/label font:
	<code>Serif</code>, <code>SansSerif</code> or <code>Monospaced</code>
	</td>
    <td align="CENTER"><code>SansSerif</code></td></tr>
<tr valign="TOP"><td><code>atomsize</code></td>
    <td><a NAME="parameters.atomsize"></a>Atom symbol font size
	in C-C bond length units:<br>
	<center>
	<em>atomsize</em>*1.54 &Aring; = <em>atomsize</em>*<em>scale</em> points
	</center>
	</td>
    <td align="CENTER">0.4</td></tr>
<tr valign="TOP"><td><code>bondSpacing</code></td>
    <td><a NAME="parameters.bondSpacing"></a>Double bond spacing
	in C-C bond length units:<br>
	<center>
	<em>spacing</em>*1.54 &Aring; = <em>spacing</em>*<em>scale</em> pixels
	</center>
	</td>
    <td align="CENTER">0.18</td></tr>
<tr valign="TOP"><td><code>stickdst</code></td>
    <td>Stick distance of atoms in C-C bond length units.</td>
    <td align="CENTER">0.3</td></tr>
<tr valign="TOP"><td><code>mergedst</code></td>
    <td>Merge distance of atoms in C-C bond length units.</td>
    <td align="CENTER">0.1</td></tr>
<tr valign="TOP"><td><code>downWedge</code></td>
    <td><a NAME="parameters.downWedge"></a>Wedge bond display convention.
	Down wedge points downward in MDL's convention (<code>mdl</code>),
	upward (at the chiral center) in Daylight's (<code>daylight</code>).
	</td>
    <td align="CENTER"><code>mdl</code></td></tr>
<tr valign="TOP"><td><code>importConv</code></td>
    <td><a NAME="parameters.importConv"></a>Conversion(s) after molecule
	loading. Currently the following options are implemented:
	<table CELLSPACING=0 CELLPADDING=3 border="0" id="no-grid">
	<tr><td NOWRAP>&quot;a&quot; or &quot;+a&quot;&nbsp;&nbsp;&nbsp;&nbsp;</td>
	    <td><a href="../sci/aromatization-doc.html#daylight_arom">General</a> aromatization </td></tr>
    	<tr><td NOWRAP>&quot;a_bas&quot; or &quot;+a_bas&quot;&nbsp;&nbsp;&nbsp;&nbsp;</td>
	    <td><a href="../sci/aromatization-doc.html#basic">Basic</a> aromatization </td></tr>
	<tr><td NOWRAP>&quot;-a&quot;</td>
	    <td>dearomatization</td></tr>
	<tr valign="TOP"><td NOWRAP>&quot;H&quot; or &quot;+H&quot;&nbsp;&nbsp;&nbsp;&nbsp;</td>
	    <td>add explicit H atoms</td></tr>
	<tr valign="TOP"><td NOWRAP>&quot;-H&quot;</td>
	    <td>remove explicit H atoms</td></tr>
	 <tr valign="TOP"><td NOWRAP>&quot;c&quot;</td>
	    <td>automatic cleaning</td></tr>
	</table></td>
    <td align="CENTER">&nbsp;</td></tr>
<tr valign="TOP"><td><code>cleanOpts</code></td>
    <td><a NAME="parameters.cleanOpts"></a>Options for 2D or 3D cleaning.<br>
        <code>cleanOpts</code> accepts the same parameter values as
        <a href="#parameters.clean2dOpts">clean2dOpts</a> or
        <a href="#parameters.clean3dOpts">clean3dOpts</a>
	depending on the cleaning dimension
        (<a HREF="#parameters.cleanDim">cleanDim</a>).
        </td>
    <td align="CENTER">&nbsp;</td></tr>
<tr valign="TOP"><td><code>clean2dOpts</code></td>
    <td><a NAME="parameters.clean2dOpts"></a>Options for 2D cleaning (0D-&gt;2D)
	See <a href="../sci/cleanoptions.html#base.2d">base 2D cleaning
        options</a>
	</td>
    <td align="CENTER">&nbsp;</td></tr>
<tr valign="TOP"><td><code>clean3dOpts</code></td>
    <td><a NAME="parameters.clean3dOpts"></a>Options for 3D cleaning (0D-&gt;3D)
	See <a href="../sci/cleanoptions.html#base.3d">base 3D cleaning
        options</a>
	</td>
    <td align="CENTER">&nbsp;</td></tr>
<tr valign="TOP"><td><code>cleanDim</code></td>
    <td><a NAME="parameters.cleanDim"></a>Number of space dimensions for cleaning.
	<ul>
	<li><code>2</code> - two-dimensional cleaning</li>
	<li><code>3</code> - three-dimensional cleaning</li>
	</ul>
	See also: <a href="#parameters.cleanOpts">cleanOpts</a>,
		  <a href="#parameters.importConv">importConv</a>.</td>
    <td align="CENTER"><code>2</code></td></tr>
<tr valign="TOP"><td><code>showSets</code></td>
    <td><a NAME="parameters.showSets"></a>Show the specified atom sets only.
    Comma separated list of set sequence numbers (0, ..., 63).</td>
    <td align="CENTER">&nbsp;</td></tr>
<tr valign="TOP"><td><code>setColoringEnabled</code></td>
    <td><a NAME="parameters.setColoringEnabled"></a>Atom/bond set coloring.</td>
    <td align="CENTER"><code>true</code></td></tr>
<tr valign="TOP"><td><code>atomSetColor0<br>atomSetColor1<br>...<br>atomSetColor63</code></td>
    <td><a NAME="parameters.atomSetColor"></a>Atom set color in hexa.</td>
    <td align="CENTER">&nbsp;</td></tr>
<tr valign="TOP"><td><code>bondSetColor1<br>...<br>bondSetColor63</code></td>
    <td><a NAME="parameters.bondSetColor"></a>Bond set color in hexa.</td>
    <td align="CENTER">&nbsp;</td></tr>
<tr valign="TOP"><td><code>undo</code></td>
    <td>Maximum number of undo operations.</td>
    <td align="CENTER">50</td></tr>
<tr valign="TOP"><td><code>moreEnabled</code></td>
    <td><a NAME="parameters.moreEnabled"></a>Enable/disable the <strong>More</strong>
	button.</td>
    <td align="CENTER"><code>true</code></td></tr>
<tr valign="TOP"><td><code>elements</code></td>
    <td><a NAME="parameters.elements"></a>Comma-separated list of allowed
	elements (<code>H</code>, <code>He</code>, <code>Li</code>, ...).
	All elements in an atomic number interval can be
	specified with the short <code>X-Y</code> notation.<br>
	Example: <code>&quot;B-F,P-Cl&quot;</code>.
	Here <code>B-F</code> is equivalent to <code>B,C,N,O,F</code>.<br>
	</td>
    <td align="CENTER">H-Ha</td></tr>
<tr valign="TOP"><td><code>queryAtoms</code></td>
    <td><a NAME="parameters.queryAtoms"></a>Comma-separated list of query atoms
	and properties.
	<ul>
	<li><code>any</code> - any atom
	    (&quot;A&quot; in molfile, &quot;*&quot; in SMARTS),</li>
	<li><code>arom</code> - aliphatic and aromatic, (&quot;a&quot; and &quot;A&quot; in SMARTS),</li>
	<li><code>conn</code> - total number of connections, (&quot;X&quot; in SMARTS),</li>
	<li><code>H</code> - total number of hydrogens, (&quot;H&quot; in SMARTS),</li>
	<li><code>hetero</code> - heteroatom
	    (&quot;Q&quot; in molfile, &quot;[!C!H]&quot; in SMARTS),</li>
	<li><code>list</code> - atom list,</li>
	<li><code>notlist</code> - atom 'NOT' list,</li>
	<li><code>Rgroup</code> - Rgroups,</li>
	<li><code>rings</code> - &quot;R&quot; in SMARTS,</li>
	<li><code>srs</code> - smallest ring size, &quot;r&quot; in SMARTS,</li>
	<li><code>val</code> - valence, &quot;v&quot; in SMARTS,</li>
	</ul>
	Example: <code>&quot;list,notlist&quot;</code>.<br>
	See also: <a HREF="queryprops.html">Query properties in file formats.</a>
	</td>
    <td align="CENTER">&nbsp;</td></tr>
<tr valign="TOP"><td><code>atomStrings</code></td>
    <td><a NAME="parameters.atomStrings"></a>Comma-separated list of string type
	atom properties.
	<ul>
	<li><code>alias</code> - atom aliases</li>
	<li><code>pseudo</code> - pseudoatoms</li>
	<li><code>smarts</code> - SMARTS query atom string</li>
	</ul>
	Example: <code>&quot;smarts&quot;</code>.
	</td>
    <td align="CENTER">&nbsp;</td></tr>
<tr valign="TOP"><td><code>extraBonds</code></td>
    <td><a NAME="parameters.extraBonds"></a>Comma-separated list of extra bond
	types.
	<ul>
	<li><code>arom</code> - Aromatic bond,</li>
	<li><code>any</code> - Any bond,</li>
	<li><code>1or2</code> - &quot;Single or Double&quot; query bond,</li>
	<li><code>aromany</code> - &quot;Single or Aromatic&quot; and
		&quot;Double or Aromatic&quot; query bond types,</li>
	<li><code>topology</code> - &quot;Chain&quot; and &quot;Ring&quot; bonds
	    (in molfiles),</li>
	<li><code>wedge</code> - Up and Down stereo wedge bonds,</li>
	<li><code>either</code> - &quot;Up or Down&quot; and
	    &quot;Cis or Trans&quot;,</li>
	<li><code>ctu</code> - &quot;Cis or Unspecified&quot; and
	    &quot;Trans or Unspecified&quot;.</li>
	</ul>
	Example: <code>&quot;arom,wedge&quot;</code>.<br>
	See also: <a HREF="queryprops.html">Query properties in file formats.</a>
	</td>
    <td align="CENTER">&nbsp;</td></tr>
<tr valign="TOP"><td><code>reactionSupport</code></td>
    <td><a NAME="parameters.reactionSupport"></a>Enables/disables reaction arrow drawing.
	</td>
    <td align="CENTER"><code>true</code></td></tr>
<tr valign="TOP"><td><code>tmpls0</code><br><code>tmpls1</code><br><code>tmpls2</code><br>...
	</td>
    <td><a NAME="parameters.tmpls"></a>The format of this parameter is
	:<i>name</i>:<i>file</i>[:-], where
	<i>name</i> is the template set name, <i>file</i> is the
	sdf or cssdf file containing the template structures and the option
	<i>-</i> defines a separator line over the template name in the menu.
	<p>
	The file can be optionally compressed with GZIP (Java &gt;= 1.1).
	A template molecule is automatically converted to 2D if the
	<i>mol2dcmd</i> field contains one of the following values:
	<ul>
	<li><code>z=0</code> - for trivial conversion,</li>
	<li><code>clean</code> - for sophisticated 2D cleaning.</li>
	</ul>
	For automatic 3D conversion, the value of the <i>mol3dcmd</i> field
	must be
	<ul>
	<li><code>clean</code> - for sophisticated 3D cleaning.</li>
	</ul>
	Template buttons may have a title if the <i>abbreviation</i> fields
	are specified. A button is rotatable if the <i>rotation.unit</i> field
	is specified (the unit rotation angle in degrees).
	For template tables, <i>endrow</i>=<code>E</code> can be specified for
	molecules in the last column. If it is not set for any molecule, then
	the best number of columns and rows are calculated automatically.
	<p>
	</td>
    <td align="CENTER">
	<a HREF="templates.html#default"><em>click here</em></a>
	</td></tr>
<tr valign="TOP"><td><code>ttmpls0</code><br><code>ttmpls1</code><br><code>ttmpls2</code><br>...
	</td>
    <td><a NAME="parameters.ttmpls"></a>Templates for the toolbar.
	Same as <a HREF="#parameters.tmpls">tmpls</a> but these template
	tables must contain only one row to fit in the toolbar.</td>
    <td align="CENTER">
	<a HREF="templates.html#default"><em>click here</em></a>
	</td></tr>
<tr valign="TOP"><td><code>xtmpls</code></td>
    <td><a NAME="parameters.xtmpls"></a>File containing extra templates.
	Extra templates are left to the normal templates
	(see <a HREF="#parameters.tmpls">tmpls</a>)
	in the template panel.
	</td>
    <td align="LEFT">&nbsp;</td>
    </tr>
<tr valign="TOP"><td><code>isMyTemplatesEnabled</code></td>
    <td><a NAME="parameters.isMyTemplatesEnabled"></a>To avoid
    the lookup for marvin.mytemplates file and disable the usability of the
    My templates on template toolbar set this property to false.
	</td>
    <td align="center"><code>true</code></td>
    </tr>
<tr valign="TOP"><td><code>abbrevgroups</code></td>
    <td><a NAME="parameters.abbrevgroups"></a>File containing
	the abbreviated groups.</td>
    <td align="LEFT"><code>chemaxon/marvin<br>
			 /templates<br>
			 /default.abbrevgroup</code></td>
    </tr>
<tr valign="TOP"><td><code>molFormat</code></td>
    <td>Default file format:
	<code>mol</code>, <code>csmol</code>,
	<code>smiles</code>, <code>cxsmiles</code>,
	<code>cml</code>, <code>pdb</code>, <code>pov</code>, <code>sybyl</code>,
	or <code>xyz</code>.</td>
    <td align="CENTER"><code>mol</code></td></tr>
<tr valign="TOP"><td><code>bondDraggedAlong</code></td>
    <td><a NAME="parameters.bondDraggedAlong"></a>Bond in hand is visible
	(<code>true</code>) or not (<code>false</code>).
	This option can also be set from the
	<strong>Edit</strong>/<strong>Preferences</strong>/<strong>Sketching</strong> menu.
	</td>
    <td align="CENTER"><code>true</code></td></tr>
<tr valign="TOP"><td><code>debug</code></td>
    <td>Debug mode. Possible values: 0, 1, 2.</td>
    <td align="CENTER">0</td></tr>
<tr valign="TOP"><td><code>bondLengthVisible</code></td>
    <td><a NAME="parameters.bondLengthVisible"></a>Shows bond length labels
	<ul>
	<li><code>true</code> - display</li>
	<li><code>false</code> - do not display</li>
	</ul>
	</td>
    <td align="CENTER"><code>false</code></td></tr>
<tr valign="TOP"><td><code>atomSymbolsVisible</code></td>
    <td><a NAME="parameters.atomSymbolsVisible"></a>Shows atom symbols
	<ul>
	<li><code>true</code> - display</li>
	<li><code>false</code> - do not display</li>
	</ul>
	</td>
    <td align="CENTER"><code>true</code></td></tr>
<tr valign="TOP"><td><code>atomNumbersVisible</code></td>
    <td><a NAME="parameters.atomNumbersVisible"></a>Show atom numbers
	<ul>
	<li><code>true</code> - display</li>
	<li><code>false</code> - do not display</li>
	</ul>
	</td>
    <td align="CENTER"><code>false</code></td></tr>
<tr valign="TOP"><td><code>atomMappingVisible</code></td>
    <td><a NAME="parameters.atomMappingVisible"></a>Show atom mapping
	<ul>
	<li><code>true</code> - display</li>
	<li><code>false</code> - do not display</li>
	</ul>
	</td>
    <td align="CENTER"><code>true</code></td></tr>
<tr valign="TOP"><td><code>atomPropertiesVisible</code></td>
    <td><a NAME="parameters.atomPropertiesVisible"></a>Show atom properties
	<ul>
	<li><code>true</code> - display</li>
	<li><code>false</code> - do not display</li>
	</ul>
	</td>
    <td align="CENTER"><code>true</code></td></tr>
<tr valign="TOP"><td><code>lonePairsVisible</code></td>
    <td><a NAME="parameters.lonePairsVisible"></a>Show lone pairs
	<ul>
	<li><code>true</code> - display</li>
	<li><code>false</code> - do not display</li>
	</ul>
	</td>
    <td align="CENTER"><code>false</code></td></tr>
<tr valign="TOP"><td><code>valenceErrorVisible</code></td>
    <td><a NAME="parameters.valenceErrorVisible"></a>Highlight (by underlining) the labels of
    those atoms with valence errors.
    This option can also be set from the
    <strong>Edit</strong>/<strong>Preferences</strong>/<strong>Sketching</strong>
    menu.
	<ul>
	<li><code>true</code> - display errors</li>
	<li><code>false</code> - do not display errors</li>
	</ul>
	</td>
    <td align="CENTER"><code>true</code></td></tr>
<tr valign="TOP"><td><code>reactionErrorVisible</code></td>
    <td><a NAME="parameters.reactionErrorVisible"></a>Highlight invalid reactions with a red rectangle around the reaction arrow.
    You can also change its value from
    <strong>Edit</strong>/<strong>Preferences</strong>/<strong>Sketching</strong>
    menu.
	<ul>
	<li><code>true</code> - display rectangle</li>
	<li><code>false</code> - do not display rectangle</li>
	</ul>
	</td>
    <td align="CENTER"><code>true</code></td></tr>
<tr valign="TOP"><td><code>grinvVisible</code></td>
    <td><a NAME="parameters.grinvVisible"></a>Set visibility of graph invariants.
    	<ul>
	<li><code>true</code> - show graph invariants</li>
	<li><code>false</code> - hide them</li>
	</ul>
	</td>
    <td align="CENTER"><code>false</code></td></tr>
<tr valign="TOP"><td><code><a NAME="sketchHelp"></a>sketchHelp</code></td>
    <td><a NAME="parameters.sketchHelp"></a>Sketcher help contents.</td>
    <td align="LEFT"><code>chemaxon/marvin/help<br>
		/sketch-index.html</code></td></tr>
<tr valign="TOP"><td><code><a NAME="sketchHelp"></a>sketchQuickHelp</code></td>
    <td><a NAME="parameters.sketchQuickHelp"></a>Sketcher quick help.</td>
    <td align="LEFT"><code>chemaxon/marvin/help<br>
		/sketch.html</code></td></tr>
<tr valign="TOP"><td><code><a NAME="viewHelp"></a>viewHelp</code></td>
    <td><a NAME="parameters.viewHelp"></a>Viewer help contents.</td>
    <td align="LEFT"><code>chemaxon/marvin/help<br>
		/view-index.html</code></td></tr>
<tr valign="TOP"><td><code><a NAME="viewHelp"></a>viewQuickHelp</code></td>
    <td><a NAME="parameters.viewQuickHelp"></a>Viewer quick help.</td>
    <td align="LEFT"><code>chemaxon/marvin/help<br>
		/view.html</code></td></tr>
<tr valign="TOP"><td><code><a NAME="parameters.addRemoveHatomsEnabled"
    ></a>addRemoveHatomsEnabled</code></td>
    <td><b>Add/Remove -&gt; Explicit Hydrogens</b> is enabled or disabled
    in the <b>Edit</b> menu. It will be disabled if this parameter is
    false.</td>
    <td align="CENTER">true</td></tr>
<tr valign="TOP"><td><code><a NAME="parameters.2dviewerEnabled"
    ></a>2dviewerEnabled</code></td>
    <td>2D viewer is allowed or not.
    <ul>
    <li><code>true</code> - enabled</li>
    <li><code>false</code> - disabled</li>
    </ul></td>
    <td align="CENTER">true</td></tr>
<tr valign="TOP"><td><code><a NAME="parameters.3dviewerEnabled"
    ></a>3dviewerEnabled</code></td>
    <td>3D viewer is allowed or not.
    <ul>
    <li><code>true</code> - enabled</li>
    <li><code>false</code> - disabled</li>
    </ul>
    </td>
    <td align="CENTER">true</td></tr>
<tr valign="TOP"><td><code><a NAME="parameters.copyOpts"
    ></a>copyOpts</code></td>
    <td>Output formats for the copy command.
    To specify more formats, enumerate them in a comma separated list.
    <ul>
    <li><code>text</code> - Copy As Text</li>
    <li><code>bitmap</code> - Copy As Bitmap Image</li>
    <li><code>emf</code> - Copy As Vector Graphical Image (EMF)</li>
    </ul>
    </td>
    <td align="CENTER">platform dependent</td></tr>
<tr VALIGN=TOP><td><code><a NAME="parameters.defaultSaveFormat"
    ></a>defaultSaveFormat</code></td>
    <td>Determines the default chemical file format in the Save As dialog.
    </td>
    <td ALIGN=CENTER>mrv</td></tr>
<tr valign="TOP"><td><code><a NAME="parameters.sketchAnyBond"
    ></a>sketchAnyBond</code></td>
    <td>How to draw any bond in sketcher.
    <ul>
    <li><code>auto</code> - dashed line in most cases, solid line only when
    all bonds are generated from atom coordinates (e.g. XYZ and PDB files).</li>
    <li><code>dashed</code> - draw dashed line</li>
    <li><code>solid</code> - draw solid line</li>
    </ul>
    </td>
    <td align="CENTER">auto</td></tr>
<tr valign="TOP"><td><code><a NAME="parameters.viewAnyBond"
    ></a>viewAnyBond</code></td>
    <td>How to draw any bond in viewer.
    <ul>
    <li><code>auto</code> - dashed line in most cases, solid line only when
    all bonds are generated from atom coordinates (e.g. XYZ and PDB files).</li>
    <li><code>dashed</code> - draw dashed line</li>
    <li><code>solid</code> - draw solid line</li>
    </ul>
    </td>
    <td align="CENTER">auto</td></tr>
<tr valign="TOP"><td><code><a NAME="parameters.rgroupsVisible"
    ></a>rgroupsVisible</code></td>
    <td>Show (true) or hide (false) R-group definitions.
    </td>
    <td align="CENTER">true</td></tr>
<tr valign="TOP"><td><code><a NAME="parameters.terminalBondDeletionStyle"
    ></a>terminalBondDeletionStyle</code></td>
    <td>If set to "withAtom", the default operation of the eraser tool when clicking on a terminal bond
    is to delete the bond, and the terminal atom. With the ALT modifier key it is possible to
    delete only the bond.
    The default behaviour and the behaviour with the ALT key can be switched if this is being
    set to "withoutAtom".
    There is also a possibility to set this on the Preferences dialog.
    </td>
    <td align="CENTER">withAtom</td></tr>
<tr valign="TOP"><td><code><a NAME="parameters.disablePluginsLookup"
    ></a>disablePluginsLookup</code></td>
    <td>Set this to true if you want to disable the lookup for plugins.properties.
    To load plugins.properties form a location to enable the Tools menu, define the
    location of the file with toolfiles parameter.
    </td>
    <td align="CENTER"><code>false</code></td></tr>
<tr valign="TOP"><td><code><a NAME="parameters.defaultDatatransferPolicy"
    ></a>defaultDatatransferPolicy</code></td>
    <td>Set this to true if you want to disable the search of datatransfer.properties
    in the codebase, and use the default configuration (mostly suitable for every use case).
    </td>
    <td align="CENTER"><code>false</code></td></tr>
</table>
</blockquote>


</body>
</html>
