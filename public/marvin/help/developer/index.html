<html>
<head>
<meta NAME="author" CONTENT="Tamas Vertse">
<link REL ="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css" TITLE="Style">
<title><PERSON>'s Guide</title>
</head>
<body>

<h1 align=center><PERSON>'s Guide</h1>
<h3 align=center>Version @MARVINVERSION@</h3>

<table CELLSPACING=0 CELLPADDING=5 BORDER=0 id="colored" align="center">
<tr><td VALIGN="TOP">
<table CELLSPACING=0 CELLPADDING=5 BORDER=0>
<tr><td VALIGN="TOP">
<h3>General documentation</h3>
<ul>
    <li><a href="../index.html">User's Guide</a></li>
    <li><a HREF="../applications/install.html">Installation &amp; System Requirements</a></li>
    <li><a HREF="../applications/libraries.html">Marvin Resources</a></li>
    <li><a HREF="../licensedoc/index.html">License Management</a></li>
    <li><a HREF="core/core_index.html">Structure representation</a></li>
    <li><a href="io/index.html">Input and output system</a></li>
    <li><a HREF="../formats/formats.html">File formats in Marvin</a></li>
	<li><a HREF="naming.html">Naming</a></li>
	<li><a HREF="d2s.html">Document to Structure</a></li>
    <li><a HREF="checker.html">Structure Checker development</a></li>
    <li><a HREF="../applications/docextractor.html">Document extractor guide</a></li>
    <li><a HREF="deprecatedObjects.html">Deprecated and removed methods</a></li>
</ul>

<h3>Parameters and Events</h3>
<ul>
    <li><a HREF="sketchman.html">MarvinSketch Parameters and Events</a>
    <li><a HREF="viewman.html">MarvinView Parameters and Events</a></li>
    <li><a HREF="space-par.html">MarvinSpace Parameters and Events</a>
</ul>

<h3>Frequently Asked Questions</h3>
<ul>
    <li><a HREF="../faq/bugs.html">Crashes, freezes and other bugs FAQ</a></li>
    <li><a href="../faq/trouble.html">MarvinSpace FAQ</a></li>
</ul>


</td></tr></table>
</td>
<td width=10>&nbsp;</td>
<td VALIGN="TOP">
<table CELLSPACING=0 CELLPADDING=5 BORDER=0>
<tr><td VALIGN=TOP>
<h3>Marvin Applets</h3>
<ul>
    <li><a HREF="applets/browsers.html">Browser compatibility</a></li>
    <li><a HREF="applets/api/index.html" target="blank">Marvin Applets API documentation</a></li>
    <li><a HREF="../../examples/applets/examples-marvinsketch-applet.html"
        >MarvinSketch Applet Examples</a></li>
    <li><a HREF="../../examples/applets/examples-marvinview-applet.html"
        >MarvinView Applet Examples</a></li>
    <li><a HREF="../../examples/applets/example-mspace-index.html"
        >MarvinSpace Applet Examples</a></li>
    <li><a HREF="applets/appletfaq.html">Marvin Applets FAQ</a>
</ul>
<!--BEANSONLY_BEGIN-->
<h3>Marvin Beans for Java</h3>
<ul>
    <li><a HREF="beans/api/index.html" target="blank">Marvin Beans API documentation</a></li>
    <li><a HREF="../../examples/beans/index.html">Marvin Beans Examples</a>
    <li><a HREF="spaceman.html">MarvinSpace Examples</a>
    <li><a HREF="beans/beanfaq.html">Marvin Beans FAQ</a></li>
</ul>

<h3>Marvin Beans for .NET</h3>
<ul>
    <li><a href="http://www.chemaxon.com/dotNET/index.html">Developer's Guide</a></li>
</ul>

<h3>Java Web Start</h3>
<ul>
    <li><a HREF="../../examples/webstart/index.html">Introduction</a>
    <li><a HREF="../../examples/webstart/example-jws-sketch.html">MarvinSketch Examples</a></li>
    <li><a HREF="../../examples/webstart/example-jws-view.html">MarvinView Examples</a></li>
    <li><a HREF="../../../marvinspace/html/webstart.html">MarvinSpace Examples</a>
</ul>
<!--BEANSONLY_END-->
</td></tr></table>
</td></tr></table>


<p ALIGN=CENTER><small>Copyright &copy; 1998-2013
<a HREF="http://www.chemaxon.com" TARGET="_top">ChemAxon Ltd.</a>
&nbsp;&nbsp;&nbsp;All rights reserved.</small></p>

</body>
</html>
