<HTML>
<HEAD>
<LINK REL ="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css" 
TITLE="Style">
<TITLE>Document to Structure Developer's Guide</TITLE>
</HEAD>
<BODY>
<center>
<h1>Document to Structure Developer's Guide</h1>
<h3>Version @MARVINVERSION@</h3>
</center>

<h2>Contents</h2>
<ul>
<li><a href="#intro">Introduction</a>
<li><a href="#api">Basic API usage</a>
<li><a href="#conf">Configuring behavior</a>
<li><a href="#progress">Monitoring progress</a>
<li><a href="#cli">Command line usage</a>
</ul>

<center><div class="lenia">&nbsp;</div></center>

<h2><a name="intro"></a>Introduction</h2>

<p>
The Document to Structure product finds chemical structures in documents.
Chemical names in the text of document, structures embedded in Office documents,
or image drawings of structure are all support (see the <a href="../d2s/d2s.html">user documentation</a>
for more details).
The structures can then be exported to any supported molecule format, or manipulated in memory.
</p>

<center><div class="lenia">&nbsp;</div></center>

<h2><a name="api"></a>Basic API usage</h2>
Document to Structure plugs into the generic IO API of ChemAxon.
This means that documents can be used exactly as other molecular formats (sdf, ...)
as a source for importing structures.
<p>
 Example usage:
<pre>
// We have a document to process
File document = new File("document.pdf");

MolImporter importer = new MolImporter(document, "d2s");

// Iterate through the hits
for (Molecule m : importer) {
  String smiles = m.toFormat("smiles");
  String name = m.getName();
  String sourceText = m.getProperty(DocumentToStructure.SOURCE_TEXT);
  //...
}
</pre>
<p>
The exact same code can be used to import an XML file, a Microsoft Office document, ...
The format is detected automatically.
<p>
The <a href="beans/api/chemaxon/naming/DocumentToStructure.html">list of all available properties</a> can be found in the API.
Which property is available depends on the format. For instance, in text formats like
xml, html and txt, the number of characters since the beginning of the file is
available as DocumentToStructure.CHARACTER, while this has no value in a binary format.
<p>
Note that SOURCE_TEXT contains the name as it appears in the source document. A cleaned version 
(of possible OCR errors, typos, ...) can be retrieved with <code>m.getName()</code>.

<h2><a name="conf"></a>Processing text directly</h2>
When the text to convert is given as a String object, the MolImporter object can be constructed with:
<pre>
String text = ...;
MolImporter importer = DocumentToStructure.process(text);
</pre>

<h2><a name="conf"></a>Configuring behavior</h2>

Document to Structure accepts options to configure how it behaves.
All <a href="../formats/name-doc.html#import">name to structure options</a> can be used with
document to structure as well, to configure which name conversions are attempted.
For instance, by default elements and ions are not converted when using d2s, as
they may occur often in documents and are not always useful. However their conversion
can be enabled, using:
<pre>
MolImporter importer = new MolImporter(document, "d2s:+elements,+ions");
</pre>
Document to Structure has specific options as well:
<ul>
<li><strong>cas</strong>: enable the conversion of CAS numbers (uses a webservice, off by default).
<li><strong>smiles</strong>: enable the conversion of SMILES strings (on by default)
<li><strong>inchi</strong>: enable the conversion of InChI strings (on by default)
<li><strong>OSRA</strong>: enable the conversion of structure drawings by the OSRA external tool (on by default if OSRA is installed)
<li><strong>filterOSR</strong>: enable the filtering of OSR structures for incomplete recognition (on by default)
<li><strong>text</strong>: enable the conversion of all text based formats: name, smiles, InChI, CAS (on by default)
<li><strong>acronyms</strong>: enable the conversion acronyms, such as ATP for Adenosine TriPhosphate (off by default)
<li><strong>OLE</strong>: enable the conversion of structures embedded in office documents (on by default)
<li><strong>startPage=N</strong>: start processing document at page N (can be combined with endPage to process a range of pages)
<li><strong>endPage=N</strong>: stop processing document at page N
<li><strong>insideTag=&lt;tag&gt;</strong>: for markup formats, enable the conversion only inside the given tag
(typically <strong>insideTag=body</strong> for HTML). Off by default.
<li><strong>contextRadius=N</strong>: maximum number of characters of context to include, on each side of the hit (default = 40).
<li><strong>contextIndex</strong>: whether to include the index of the hit in the context. Off by default.
</ul>
Each option can be precedeed by a minus sign <strong>-</strong> (for instance <strong>-smiles</strong>) to disable it.
Both forms <strong>smiles</strong> and <strong>+smiles</strong> are accepted to enable an option.

<h2><a name="progress"></a>Monitoring progress</h2>
For estimating the progress of converting a document, you can use the standard method
MolImporter.estimateNumRecords().

<h2><a name="cli"></a>Command line usage</h2>
Document to Structure can be used as any other import file format. For instance,
command line usage can be achieved by using MolConverter on a format supported by Document to Structure:
<pre>
molconvert sdf document.doc -o structures.sdf
</pre>

<center><div class="lenia">&nbsp;</div></center>
<h2>See also</h2>

<ul>
<li>Detailed <a href="../../examples/d2s/index.html">code examples</a> using Document to Structure
in real-world situations.
<li><a href="beans/api/chemaxon/formats/MolImporter.html">API documentation of the MolImporter interface</a>
<li><a href="beans/api/chemaxon/naming/DocumentToStructure.html">API documentation of the DocumentToStructure constants for properties, and helper methods</a>
<li><a href="../d2s/d2s.html">User documentation for d2s</a></li>
<li>An <a href="document-extractor.html">earlier version of the API</a> of the Document to Structure.
</ul>
</p>

<center><div class="lenia">&nbsp;</div></center>

<center>
<font size="-2" face="helvetica">
Copyright &copy; 1999-2013 
<a href="http://www.chemaxon.com">ChemAxon Ltd.</a>
&nbsp;&nbsp;&nbsp;All rights reserved.
</font>
</center>

</BODY>
</HTML>
