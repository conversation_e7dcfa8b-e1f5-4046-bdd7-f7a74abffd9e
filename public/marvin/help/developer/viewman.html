<html>
<head>
<meta NAME="description" CONTENT="MarvinView: parameters and events">
<meta NAME="author" CONTENT="Peter Csizmadia">
<link REL ="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css" TITLE="Style">
<title>MarvinView: parameters and events</title>
</head>
<body>

<h1 align=center>MarvinView: parameters and events</h1>
<h3 align=center>Version @MARVINVERSION@</h3>

<h2>Contents</h2>

<ol>
<li><a class="text" HREF="#parameters">Parameters</a>
    <ol>
    <li><a HREF="#appletandbeans">Applet and JavaBeans parameters</a>
	<ol>
	<li><a HREF="#displayparameters">Display parameters</a></li>
	<li><a HREF="#structuredisplayparameters">Structure display parameters</a></li>
	<li><a HREF="#structureparameters">Structure parameters</a></li>
	<li><a HREF="#3d">3D and animation</a></li>
        <li><a HREF="#advanced">Molecule tables</a>
            <ol>
            <li><a HREF="#advanced.layout">The <code>layout</code> parameter</a></li>
            <li><a HREF="#advanced.param">The <code>param</code> parameter</a></li>
            <li><a HREF="#advanced.cell">The <code>cell<em>i</em></code> and
		<code>cell<em>i</em>_<em>j</em></code> parameters</a></li>
            </ol>
            </li>
        <li><a HREF="#otherparameters">Other parameters</a></li>
	</ol></li>
<!--APPLETONLY_BEGIN-->
    <li><a HREF="#appletonly">Applet only parameters</a></li>
 <!--APPLETONLY_END-->
    </ol>
    </li>
<!--BEANSONLY_BEGIN-->
<li><a HREF="#propchanges">Events fired by the JavaBean</a></li>
<!--BEANSONLY_END-->
<!--APPLETONLY_BEGIN--><li><a HREF="#troubleshooting">Troubleshooting -
    MView and JMView tables</a></li><!--APPLETONLY_END-->
</ol>

<center><div class="lenia">&nbsp;</div></center>

<h2><a class="anchor" NAME="parameters">1 &nbsp; Parameters</a></h2>
<h3><a class="anchor" NAME="appletandbeans">1.1 &nbsp; Applet and JavaBeans parameters</a></h3>

Parameters of the <em>MView</em> and <em>JMView</em> applets can be set using
the <code>&lt;param&gt;</code> HTML tag.
<!--BEANSONLY_BEGIN-->
Parameters of the <em>MViewPane</em>
JavaBean can be set using the
<a HREF="beans/api/chemaxon/marvin/beans/MarvinPane.html#setParams(java.lang.String)"><code>setParams</code></a> function.<br>
For parameter constants please visit the
<a HREF="beans/api/chemaxon/marvin/common/ParameterConstants.html"><code>ParameterConstants</code></a> and
<a HREF="beans/api/chemaxon/marvin/view/ViewParameterConstants.html#setParams(java.lang.String)"><code>ViewParameterConstants</code></a> API.

<!--BEANSONLY_END-->
<p>

<h4><a class="anchor" NAME="displayparameters">1.1.1 &nbsp; Display parameters</a></h4>
<blockquote>
<table cellpadding="4" cellspacing="0" border="0" class="colored-grid" width="600">
<tr ALIGN=CENTER><th width="20%">Parameter</th><th>Meaning</th><th width="10%">Default</th></tr>
<tr VALIGN=TOP><td><code><a NAME="parameters.addRemoveHatomsEnabled" class="text"
    >addRemoveHatomsEnabled</a></code></td>
    <td><b>Add/Remove -&gt; Explicit Hydrogens</b> is enabled or disabled
    in the <b>Edit</b> menu. It will be disabled if this parameter is
    false.</td>
    <td ALIGN=CENTER><code>true</code></td></tr>
<tr VALIGN=TOP><td><code><a NAME="parameters.autoTabScale" class="text">autoTabScale</a></code></td>
    <td>Automatically change scale when
	component size changed. Only applicable to the MViewPane bean.</td>
    <td ALIGN=CENTER><code>false</code></td></tr>
<tr VALIGN=TOP><td><code><a NAME="parameters.background" class="text">background</a></code></td>
    <td>Background color in hexa.
    Sets the background color of the components (like menubar, toolbar,
    labels and buttons),
    excluding the molecule canvas. See also: <a href="#parameters.molbg">molbg</a></td>
    <td ALIGN=CENTER>&nbsp;</td></tr>
<tr valign="TOP"><td><a class="text" name="parameters.confirmTransfer"><code>confirmTransfer</code></a></td>
    <td>Displays a confirmation dialog window upon closing the structure editor window in case the structure has been altered.</td>
    <td align="center"><code>false</code></td>
</tr>
<tr VALIGN=TOP><td><code><a NAME="parameters.detachable" class="text">detachable</a></code></td>
    <td>Enable or disable popping up detached viewer window. If this
    parameter is true, the selected molecule can be displayed in separate window
    by selecting "Window" menu item in the popup menu or by doubleclicking
    on the canvas.
    Viewer is not detachable if detachable parameter is false.
     (See also <a href="#parameters.editable">editable</a>.)
	</td>
    <td ALIGN=CENTER><code>true</code></td></tr>
<tr VALIGN=TOP><td><code><a NAME="parameters.dispQuality" class="text">dispQuality</a></code></td>
    <td>Display quality.
	<ul>
	<li><code>0</code> - low quality, faster rendering</li>
	<li><code>1</code> - high quality (antialiasing), slower rendering</li>
	</ul>
	</td>
    <td ALIGN=CENTER><code>1</code></td></tr>
<tr VALIGN=TOP><td><code><a NAME="parameters.molbg" class="text">molbg</a></code></td>
    <td>Molecule background color in hexa.
    To set different colors for some specified cells, use a comma separated
    list of "<em>i</em><code>:#</code>rrggbb" settings, where <em>i</em> is
    the cell index. For example, to set the default color to white and the
    color of cell 2 to red, use "<code>#ffffff,2:#ff0000</code>".
    See also:
    <a href="#parameters.background">background</a></td>
    <td ALIGN=CENTER>&nbsp;</td></tr>

<!-- <tr VALIGN=TOP><td><code>labels</code></td>
    <td><a NAME="parameters.labels"></a>Atom labels
	<ul>
	<li><code>true</code> - display</li>
	<li><code>false</code> - do not display</li>
	</ul>
	</td>
    <td ALIGN=CENTER><code>true</code></td></tr> -->
<tr VALIGN=TOP><td><code><a NAME="parameters.selectedIndex" class="text"
    >selectedIndex</a></code></td>
    <td>Select the specified cell.</td>
    <td ALIGN=CENTER>&nbsp;</td></tr>
<tr VALIGN=TOP><td><code><a name="parameters.tabScale" class="text">tabScale</a></code></td>
    <td>Magnification in the molecule table.
	Each cell has the specified magnification if positive.
	The scaling is automatic if negative (-1).</td>
    <td ALIGN=CENTER><code>-1</code></td></tr>
<tr valign="TOP"><td><a class="text" name="parameters.transferButtonIcon"><code>transferButtonIcon</code></a></td>
    <td>Sets the location of a custom icon for the Transfer action.</td>
    <td>&nbsp</td>
</tr>
<tr valign="TOP"><td><a class="text" name="parameters.transferButtonText"><code>transferButtonText</code></a></td>
    <td>Sets custom text for the Transfer action.</td>
    <td>&nbsp</td>
</tr>
<tr valign="TOP"><td><a class="text" name="parameters.transferButtonVisible"><code>transferButtonVisible</code></a></td>
    <td>Makes the Transfer button visible on the General Toolbar.</td>
    <td align="center"><code>true</code></td>
</tr>
<tr VALIGN=TOP><td><code><a name="parameters.winScale" class="text">winScale</a></code></td>
    <td>Magnification in the zoom window.
	A 1.54 &Aring; long C-C bond is magnified to <em>winScale</em> pixels.
	</td>
    <td ALIGN=CENTER><code>26.4</code></td></tr>
</table>
</blockquote>

<p><!--APPLETONLY_BEGIN-->
Simple viewer applet example:
<blockquote><pre>
&lt;applet code=&quot;MView&quot; width=100 height=100&gt;
&lt;param name=&quot;molbg&quot; value=&quot;#a0ffa0&quot;&gt;
&lt;param name=&quot;escapeChar&quot; value=&quot;\&quot;&gt;
&lt;param name=&quot;mol&quot; value=&quot;Pyrrole\n
...
M  END\n&quot;&gt;&lt;/applet&gt;
</pre></blockquote>

<h4><a class="anchor" NAME="structuredisplayparameters">1.1.2 &nbsp; Structure display parameters</a></h4>
<blockquote>
<table cellpadding="4" cellspacing="0" border="0" class="colored-grid" width="600">
<tr ALIGN=CENTER><th width="20%">Parameter</th><th>Meaning</th><th width="10%">Default</th></tr>
<tr VALIGN=TOP><td><code><a name="parameters.atomFont" class="text">atomFont</a></code></td>
    <td>Atom symbol/label font:
	<code>Serif</code>, <code>SansSerif</code> or <code>Monospaced</code>
	</td>
    <td ALIGN=CENTER><code>SansSerif</code></td></tr>
<tr VALIGN=TOP><td><code><a name="parameters.atomMappingVisible" class="text">atomMappingVisible</a></code></td>
    <td>Show (true) or hide (false) atom mapping</td>
    <td ALIGN=CENTER><code>true</code></td></tr>
<tr valign="TOP"><td><a class="text" name="parameters.atomPropertiesVisible"><code>atomPropertiesVisible</code></a></td>
    <td>Show (true) or hide (false) atom properties</td>
    <td align="CENTER"><code>true</code></td></tr>
<tr VALIGN=TOP><td><code><a name="parameters.atomNumbersVisible" class="text">atomNumbersVisible</a></code></td>
    <td>Show (true) or hide (false) atom numbers</td>
    <td ALIGN=CENTER><code>false</code></td></tr>
<tr VALIGN="TOP"><td><a class="text" name="parameters.valencePropertyVisible"><code>valencePropertyVisible</code></a></td>
    <td>Show (true) or hide (false) valence properties
	</td>
    <td ALIGN=CENTER><code>true</code></td></tr>
<tr VALIGN=TOP><td><code><a name="parameters.atomsize" class="text">atomsize</a></code></td>
    <td>Atom symbol font size
	in C-C bond length units:<br>
	<center>
	<em>atomsize</em>*1.54 &Aring; = <em>atomsize</em>*<em>scale</em> points,
	</center>
	where <em>scale</em> is the current magnification.
	</td>
    <td ALIGN=CENTER><code>0.4</code></td></tr>
<tr VALIGN=TOP><td><code><a name="parameters.atomSymbolsVisible" class="text">atomSymbolsVisible</a></code></td>
    <td>Show (true) or hide (false) atom symbols in 3D view</td>
    <td ALIGN=CENTER><code>true</code></td></tr>
<tr VALIGN=TOP><td><code><a name="parameters.bondLengthVisible" class="text">bondLengthVisible</a></code></td>
    <td>Show (true) or hide (false) bond length labels</td>
    <td ALIGN=CENTER><code>false</code></td></tr>
<tr VALIGN=TOP><td><code><a name="parameters.bondSpacing" class="text">bondSpacing</a></code></td>
    <td>Double bond spacing
	in C-C bond length units:<br>
	<center>
	<em>spacing</em>*1.54 &Aring; = <em>spacing</em>*<em>scale</em> pixels,
	</center>
	where <em>scale</em> is the current magnification.
	</td>
    <td ALIGN=CENTER><code>0.18</code></td></tr>
<tr valign="TOP"><td><a class="text" name="parameters.chargeWithCircle"><code>chargeWithCircle</code></a></td>
    <td>Charge label (plus or minus sign) is displayed circled(true) or normal(false)
	</td>
    <td align="CENTER"><code>false</code></td></tr>
<tr VALIGN=TOP><td><code><a name="parameters.chiralitySupport" class="text">chiralitySupport</a></code></td>
    <td>When to show atom chirality
	(R/S).
	<ul>
	<li><code>off</code> - never</li>
	<li><code>selected</code> - if the chiral flag is set for the molecule
	or the atom's enhanced stereo type is absolute</li>
	<li><code>all</code> - always</li>
	</ul>
	</td>
    <td ALIGN=CENTER><code>off</code></td></tr>
<tr valign="TOP"><td><a class="text" name="parameters.circledChargeFont"><code>circledChargeFont</code></a></td>
    <td>Circled charge labels font: any of the fonts installed on the machine
	</td>
    <td align="CENTER"><code>SansSerif</code></td></tr>
<tr valign="TOP"><td><a class="text" name="parameters.circledChargeSize"><code>circledChargeSize</code></a></td>
    <td>Circled Charge labels font size in C-C bond length units:<br>
	<center>
	<em>circledChargeSize</em>*1.54 &Aring; = <em>circledChargeSize</em>*<em>scale</em> points
	</center>
	</td>
    <td align="CENTER"><code>0.4</code></td></tr>
<tr VALIGN=TOP><td><code><a name="parameters.colorScheme" class="text">colorScheme</a></code></td>
    <td>Color scheme.
	<ul>
	<li><code>mono</code> - monochrome</li>
	<li><code>cpk</code> - Corey-Pauling-Kultun</li>
	<li><code>shapely</code> -
	    <a HREF="shapely-scheme.html">shapely</a> (residue types)</li>
	<li><code>group</code> - residue sequence numbers</li>
	</ul>
	</td>
    <td ALIGN=CENTER><code>cpk</code></td></tr>
<tr VALIGN=TOP><td><code><a name="parameters.downWedge" class="text">downWedge</a></code></td>
    <td>Wedge bond display convention.
	Down wedge points downward in MDL's convention (<code>mdl</code>),
	upward (at the chiral center) in Daylight's (<code>daylight</code>).
	</td>
    <td ALIGN=CENTER><code>mdl</code></td></tr>
<tr VALIGN=TOP><td><code><a name="parameters.explicitH" class="text">explicitH</a></code></td>
    <td>Show (true) or hide (false) explicit hydrogens.</td>
    <td ALIGN=CENTER><code>true</code></td></tr>
<tr VALIGN=TOP><td><code><a name="parameters.ezVisible" class="text">ezVisible</a></code></td>
    <td>Show (true) or hide (false) E/Z labels.</td>
    <td ALIGN=CENTER><code>false</code></td></tr>
<tr VALIGN=TOP><td><code><a name="parameters.grinv" class="text">grinv</a></code></td>
    <td>Show (true) or hide (false) graph invariants</td>
    <td ALIGN=CENTER><code>false</code></td></tr>
<tr VALIGN=TOP><td><code><a name="parameters.implicitH" class="text">implicitH</a></code></td>
    <td>How to display H labels.
	<ul>
	<li><code>off</code></li>
	<li><code>hetero</code> - on heteroatoms</li>
	<li><code>heteroterm</code> - on hetero or terminal atoms</li>
	<li><code>all</code> - all atoms</li>
	</ul>
	</td>
    <td ALIGN=CENTER><code>false</code></td></tr>
<tr VALIGN="TOP"><td><a class="text" name="parameters.ligandErrorVisible"><code>ligandErrorVisible</code></a></td>
    <td>Show (true) or not (false) ligand error by coloring to red.
	</td>
    <td align="CENTER"><code>false</code></td></tr>    
<tr VALIGN=TOP><td><code><a name="parameters.lonePairsVisible" class="text">lonePairsVisible</a></code></td>
    <td>Show (true) or hide (false) lone pairs</td>
    <td ALIGN=CENTER><code>false</code></td></tr>
<tr VALIGN=TOP><td><code><a name="parameters.peptideDisplayType" class="text">peptideDisplayType</a></code></td>
    <td>Show peptide sequences with one letter or three letter abbreviations.<br/>
    Valid values are "1-letter" and "3-letter"</td>
    <td ALIGN=CENTER><code>3-letter</code></td></tr>
<tr VALIGN=TOP><td><code><a name="parameters.rgroupsVisible" class="text"
    >rgroupsVisible</a></code></td>
    <td>Show (true) or hide (false) R-group definitions</td>
    <td ALIGN=CENTER><code>true</code></td></tr>
<tr VALIGN=TOP><td><code><a name="parameters.selection" class="text">selection0<br>selection1<br>selection2<br>...</a></code></td>
    <td VALIGN=TOP>Comma-separated list of
	atom numbers (0, ..., n-1).<br>
	Selected atoms are highlighted.<br>
	In a simple one-molecule viewer, <code>selection0</code> must be used.
	In a molecule table, <code>selection</code><em>n</em> corresponds to
	molecule cell <em>n</em>.</td>
    <td ALIGN=CENTER>&nbsp;</td></tr>
<tr VALIGN=TOP><td><code><a name="parameters.showSets" class="text">showSets</a></code></td>
    <td>Show the specified atom sets only.
	Comma separated list of set sequence numbers (0, ..., 63).</td>
    <td ALIGN=CENTER><code>heteroterm</code></td></tr>
<tr VALIGN=TOP><td><code><a name="parameters.sketchAnyBond" class="text">sketchAnyBond</a></code></td>
    <td>Display type of the Any bond in the sketcher:
    <ul>
        <li><code>auto</code> - displayed as dashed line in most cases, solid line only when
    all bonds are generated from atom coordinates (e.g. XYZ and PDB files).</li>
        <li><code>dashed</code> - displayed as dashed line</li>
        <li><code>solid</code> - displayed as solid line</li>
    </ul>
    </td>
    <td ALIGN=CENTER><code>auto</code></td></tr>
<tr VALIGN=TOP><td><code><a name="parameters.valenceErrorVisibleInView" class="text">valenceErrorVisibleInView</a></code></td>
    <td>Show (true) or hide (false) valence errors</td>
    <td ALIGN=CENTER><code>true</code></td></tr>
<tr VALIGN=TOP><td><code><a name="parameters.viewAnyBond" class="text">viewAnyBond</a></code></td>
    <td>Display type of the Any bond in the viewer:
    <ul>
    <li><code>auto</code> - displayed as dashed line in most cases, solid line only when
    all bonds are generated from atom coordinates (e.g. XYZ and PDB files).</li>
    <li><code>dashed</code> - displayed as dashed line</li>
    <li><code>solid</code> - displayed as solid line</li>
    </ul>
    </td>
    <td align="CENTER"><code>auto</code></td></tr>
<tr VALIGN=TOP><td><code><a NAME="parameters.viewAtomMarkEnabled" class="text"
    >viewAtomMarkEnabled</a></code></td>
    <td>Enables (true) or disables (false) atom highlight and atom mark in View.</td>
    <td ALIGN=CENTER><code>true</code></td></tr>
<tr valign="TOP"><td><a class="text" name="parameters.showViewCarbon"><code>viewCarbonVisibility</code></a></td>
    <td>Display the label of carbon atoms in structures.
    <ul>
	<li><code>on</code> - Always show the atom labels of carbon atoms.</li>
	<li><code>off</code> - Never show the atom labels of carbon atoms.</li>
	<li><code>inChain</code> - Show the atom labels of carbon atoms at
	 straight angles and at implicit Hydrogens.</li>
	</ul>
    <td align="CENTER"><code>inChain</code></td></tr>
    <tr valign="TOP"><td><a class="text" name="parameters.showViewLigandOrder"><code>viewLigandOrderVisibility</code></a></td>
    <td>Display the ligand order of R-group atoms in structures.
    <ul>
	<li><code>on</code> - Always show the ligand order of R-group atoms.</li>
	<li><code>off</code> - Never show the ligand order of R-group atoms.</li>
	<li><code>showOnlyWithDefinition</code> - Show the ligand order of R-group atoms only on R-groups with definition.</li>
	</ul>
    <td align="CENTER"><code>off</code></td></tr>
</table>
</blockquote>

<h4><a class="anchor" NAME="structureparameters">1.1.3 &nbsp; Structure parameters</a></h4>
<blockquote>
<table cellpadding="4" cellspacing="0" border="0" class="colored-grid" width="600">
<tr ALIGN=CENTER><th width="20%">Parameter</th><th>Meaning</th><th width="10%">Default</th></tr>
<tr VALIGN=TOP><td><code><a NAME="parameters.atomSet" class="text">
    atomSet0.1 ... atomSet0.63<br>atomSet1.1 ... atomSet1.63<br>...</a></code></td>
    <td>Atom sets. Comma separated list of
	atom indices (0, ..., <i>n</i>-1). An atom in molecule cell number
	<i>N</i> will be in set <i>M</i> if its number appears in the
	atomSet<i>N</i>.<i>M</i> list. By default, all atoms are in set 0.</td>
    <td ALIGN=CENTER>&nbsp;</td></tr>
<tr VALIGN=TOP><td><code><a NAME="parameters.atomSetColor" class="text">
    atomSetColor0<br>...<br>atomSetColor63</a></code></td>
    <td>Atom set color in hexa.</td>
    <td ALIGN=CENTER>&nbsp;</td></tr>
<tr VALIGN=TOP><td><code>bondSet0.1 ... bondSet0.63<br>bondSet1.1 ... bondSet1.63<br>...</code></td>
    <td><a NAME="parameters.bondSet"></a>Bond sets. Comma separated list of
	atom index pairs in <i>i</i>-<i>j</i> format,
	where <i>i</i>, <i>j</i> = 0, ..., <i>n</i>-1.
	A bond in molecule cell number <i>N</i>
	will be in set <i>M</i> if the corresponding atom pair appears in the
	bondSet<i>N</i>.<i>M</i> list.
	By default, all bonds are in set 0.</td>
    <td ALIGN=CENTER>&nbsp;</td></tr>
<tr VALIGN=TOP><td><code>bondSetColor1<br>...<br>bondSetColor63</code></td>
    <td><a NAME="parameters.bondSetColor"></a>Bond set color in hexa.</td>
    <td ALIGN=CENTER>&nbsp;</td></tr>
<tr VALIGN=TOP><td><code>clean2dOpts</code></td>
    <td><a NAME="parameters.clean2dOpts"></a>Options for 2D cleaning (0D-&gt;2D)
        See <a href="../sci/cleanoptions.html#base.2d">base 2D cleaning
        options</a>
	</td>
    <td ALIGN=CENTER><code>&nbsp;</code></td>
</tr>
<tr VALIGN=TOP><td><code>clean3dOpts</code></td>
    <td><a NAME="parameters.clean3dOpts"></a>Options for 3D cleaning (0D-&gt;3D)
        See <a href="../sci/cleanoptions.html#base.3d">base 3D cleaning
        options</a>
	</td>
    <td ALIGN=CENTER><code>&nbsp;</code></td></tr>
<tr VALIGN=TOP><td><code>cleanDim</code></td>
    <td><a NAME="parameters.cleanDim"></a>Number of space dimensions for cleaning.
	<ul>
	<li><code>2</code> - two-dimensional cleaning</li>
	<li><code>3</code> - three-dimensional cleaning</li>
	</ul>
	See also: <a href="#parameters.cleanOpts">cleanOpts</a>,
		  <a href="#parameters.importConv">importConv</a>.</td>
    <td ALIGN=CENTER><code>2</code></td></tr>

<tr VALIGN=TOP><td><code>cleanOpts</code></td>
    <td><a NAME="parameters.cleanOpts"></a>Options for 2D or 3D cleaning.<br>
        <code>cleanOpts</code> accepts the same parameter values as
        <a href="#parameters.clean2dOpts">clean2dOpts</a> or
        <a href="#parameters.clean3dOpts">clean3dOpts</a>
        depending on the cleaning dimension
        (<a HREF="#parameters.cleanDim">cleanDim</a>).
        </td>
    <td ALIGN=CENTER><code>&nbsp;</code></td>
    </tr>

<tr VALIGN=TOP><td><code>importConv</code></td>
    <td><a NAME="parameters.importConv"></a>Conversion(s) after molecule
	loading. Currently the following options are implemented:
	<table cellpadding="3" cellspacing="0" border="0" id="no-grid">
	<tr><td NOWRAP>&quot;a&quot; or &quot;+a&quot;&nbsp;&nbsp;&nbsp;&nbsp;</td>
	    <td><a href="../sci/aromatization-doc.html#daylight_arom">General</a> aromatization</td></tr>
    	<tr><td NOWRAP>&quot;a_bas&quot; or &quot;+a_bas&quot;&nbsp;&nbsp;&nbsp;&nbsp;</td>
	    <td><a href="../sci/aromatization-doc.html#basic">Basic</a> aromatization</td></tr>
	<tr><td NOWRAP>&quot;-a&quot;</td>
	    <td>dearomatization</td></tr>
	<tr VALIGN=TOP><td NOWRAP>&quot;H&quot; or &quot;+H&quot;&nbsp;&nbsp;&nbsp;&nbsp;</td>
	    <td>add explicit H atoms</td></tr>
	<tr VALIGN=TOP><td NOWRAP>&quot;-H&quot;</td>
	    <td>remove explicit H atoms</td></tr>
	<tr VALIGN=TOP><td NOWRAP>&quot;c&quot;</td>
	    <td>automatic cleaning</td></tr>
	</table></td>
    <td ALIGN=CENTER>&nbsp;</td></tr>
<tr VALIGN=TOP><td><code>setColoringEnabled</code></td>
    <td><a NAME="parameters.setColoringEnabled"></a>Atom/bond set coloring.</td>
    <td ALIGN=CENTER><code>true</code></td></tr>
</table>
</blockquote>

<h4><a class="anchor" NAME="3d">1.1.4 &nbsp; 3D and animation</a></h4>
<blockquote>
<table cellpadding="4" cellspacing="0" border="0" class="colored-grid" width="600">
<tr ALIGN=CENTER><th width="20%">Parameter</th><th>Meaning</th><th width="10%">Default</th></tr>

<tr VALIGN=TOP><td><code>animate</code></td>
    <td><a NAME="3d.animate"></a>Start an XYZ animation sequence, or starts
	rotating the specified 3D molecule(s).<br>
	Possible values: <code>off</code>, <code>all</code>, or a
	comma-separated list of molecule indices (0...n-1).<br>
	<strong>Note:</strong> This parameter does not apply to beans, only to applet.
	</td>
    <td ALIGN=CENTER><code>off</code></td></tr>
<tr VALIGN=TOP><td><code>animDelay</code></td>
    <td><a NAME="3d.animDelay"></a>Delay (seconds) before repeating
	the animation.</td>
    <td ALIGN=CENTER><code>0</code></td></tr>
    <tr VALIGN=TOP><td><code>animFPS</code></td>
        <td><a NAME="3d.animFPS"></a>Number of frames per second
            in 3D animation.</td>
        <td ALIGN=CENTER><code>10</code></td></tr>
<tr VALIGN=TOP><td><code>animSync</code></td>
    <td><a NAME="3d.animSync"></a>Whether the animations in
	different cells should be synchronized.</td>
    <td ALIGN=CENTER><code>false</code></td></tr>

    <tr VALIGN=TOP><td><code>ballRadius</code></td>
        <td><a NAME="3d.ballRadius"></a>Ball radius for
            &quot;ballstick&quot; <a HREF="#3d.rendering">rendering mode</a>,
            in units of covalent radius.</td>
        <td ALIGN=CENTER><code>0.5</code></td></tr>

    <tr VALIGN=TOP><td><code>rendering</code></td>
        <td><a NAME="3d.rendering"></a>Rendering style.
            <ul>
            <li><code>wireframe</code> - wireframe</li>
            <li><code>wireknobs</code> - wireframe with knobs</li>
            <li><code>sticks</code> - 3D sticks</li>
            <li><code>ballstick</code> - ball &amp; stick</li>
            <li><code>spacefill</code> - balls</li>
            </ul>
            </td>
        <td ALIGN=CENTER><code>wireframe</code></td></tr>

<tr VALIGN=TOP><td><code>script</code><br>
    <code>script0</code><br><code>script1</code><br><code>script2</code><br><code>...</code>
    </td>
    <td VALIGN=TOP><a NAME="3d.script"></a>Script containing
	a subset of <a HREF="rasmol-doc.html">RasMol commands</a> delimited by
	semicolons.
	If this applet parameter is specified, then the
	<a HREF="#parameters.mol">mol</a> parameter is neglected.
	The molecule(s) must be loaded using the
	<a HREF="rasmol-doc.html#load">load</a> command in the script.<br>
	<strong>Note:</strong> To load the script from a file, use the
	<a HREF="rasmol-doc.html#script">script</a> command:<br>
	<code>&lt;param name=&quot;script&quot;
	    value=&quot;script myscript.spt&quot&gt;</code>
	</td>
    <td ALIGN=CENTER>&nbsp;</td></tr>

    <tr VALIGN=TOP><td><code>spin</code><br>
        <code>spin0</code><br>
        <code>spin1</code><br>
        <code>spin2</code><br>
        ...</td>
        <td><a NAME="3d.spin"></a>Spinning rate and axis for
            <a HREF="#3d.animate">animated</a> molecules. Spinning rate is
            specified in degrees per second, the axis is specified with a 3D
            vector. Numbers are separated by commas.<br>
            <strong>Note:</strong> XYZ animations do not spin by default, only if this
            parameter is set.</td>
        <td ALIGN=CENTER><code>36,0,1,0</code></td></tr>

    <tr VALIGN=TOP><td><code>stickThickness</code></td>
        <td><a NAME="3d.stickThickness"></a>3D stick diameter for
            &quot;sticks&quot; and &quot;ballstick&quot;
            <a HREF="#3d.rendering">rendering modes</a>,
            in Angstroms.</td>
        <td ALIGN=CENTER><code>0.1</code></td></tr>

    <tr VALIGN=TOP><td><code>wireThickness</code></td>
    <td><a NAME="3d.wireThickness"></a>line thickness for
        &quot;wireframe&quot; and &quot;wireknobs&quot;
        <a HREF="#3d.rendering">rendering modes</a>,
        in Angstroms.</td>
    <td ALIGN=CENTER><code>0.064</code></td></tr>
</table>
</blockquote>

<p>

<h4><a class="anchor" NAME="advanced">1.1.5 &nbsp; Molecule tables</a></h4>

<p>The cells in the multi-cell molecule table mode of MarvinView can be divided further
and they can have they own layout, which however is uniform for all cells in the table.
The parameters described here are used to define the structure of such a cell.<br>
Example of using molecule tables: <a href="../../examples/beans/view-table/index.html">MarvinView Table View Example</a><br>
A utility class is also available to generate parameter values: <a href="beans/api/chemaxon/marvin/beans/MViewParams.html">MViewParams</a>
</p>

<blockquote>
<table cellpadding="4" cellspacing="0" border="0" class="colored-grid" width="600">
<tr VALIGN=TOP><td><code>rows</code></td>
    <td><a NAME="advanced.rows"></a>Number of &quot;molecule rows&quot;,
	including the optional header row.
	</td></tr>
<tr VALIGN=TOP><td><code>cols</code></td>
    <td><a NAME="advanced.cols"></a>Number of &quot;molecule columns&quot;.
	</td></tr>
<tr VALIGN=TOP><td><code>visibleRows</code></td>
    <td><a NAME="advanced.visibleRows"></a>Number of visible rows.
	The table is scrollable if less than <a HREF="#advanced.rows">rows</a>.
	Default value: visibleRows = rows.
	</td></tr>
<tr VALIGN=TOP><td><code>visibleCols</code></td>
    <td><a NAME="advanced.visibleCols"></a>Number of visible columns.
	The table is scrollable if less than <a HREF="#advanced.cols">cols</a>.
	Default value: visibleCols = cols.
	</td></tr>
<tr VALIGN=TOP><td><code>layout</code></td>
    <td><a HREF="#advanced.layout">Layout</a> of
	molecule, label, button and checkbox components in a cell.</td></tr>
<tr VALIGN=TOP><td><code>layoutH</code></td>
    <td>Layout for the header row.</td></tr>
<tr VALIGN=TOP><td><code>param</code></td>
    <td><a HREF="#advanced.param">Parameters</a> of the components.
	</td></tr>
<tr VALIGN=TOP><td><code>paramH</code></td>
    <td>Parameters for the header row.</td></tr>
<tr VALIGN=TOP><td><code>cell0<br>cell1<br>cell2<br>...</code></td>
    <td>Contents of the <a HREF="#advanced.cell">cells</a>.<br>
	Use these instead of the <code>mol</code> parameter.</td></tr>
<tr VALIGN=TOP><td><code>border</code></td>
    <td><a NAME="advanced.border"></a>Border width in pixels.
	Default: 0 (no border between cells).<br>
	Increases the total applet <code>WIDTH</code> by <em>border*(cols-1)</em>,
	and the <code>HEIGHT</code> by <em>border*(rows-1)</em>.</td></tr>
</table>
</blockquote>

<h5><a NAME="advanced.layout" class="anchor">1.1.5.1 &nbsp; The <code>layout</code> parameter</a></h5>
The <code>layout</code> and <code>layoutH</code> parameters are in the
following format:
&quot;<em>:rows:cols:type:k:l:h:w:anchor:fill:[weighty:[weightx:]]type:k:l:...</em>&quot;,
where <em>:</em> is the field separator,
<em>rows</em> is the number of rows in the cell,
<em>cols</em> is the number of columns, <em>type</em> is the type of the
component in row <em>k</em>, column <em>l</em> of the current cell,
<em>h</em> is the height of the component (rows),
<em>w</em> is the width (columns).<br>
The <em>anchor</em> parameter determines where to place the component if its
size is smaller than its display area.
It the component size is larger than the size of its text label,
then anchor also sets the label alignment.
Possible values:
<code>c</code> (center), <code>n</code> (north), <code>s</code> (south),
<code>e</code> (east), <code>w</code> (west), <code>ne</code> (northeast), etc.
<br>
The <em>fill</em> parameter determines how to resize the component if its
size is smaller then its display area.
Possible values: <code>n</code> (none), <code>h</code> (horizontal),
<code>v</code> (vertical) or <code>b</code> (both).
<br>
The <em>weights</em> are nonnegative integers, zero by default.
They determine how to distribute space.
If all the weights are zero, all the components clump together in the center of
the applet.
<p>
<small>
If you are familiar with Java, you might have noticed that <em>anchor</em>,
<em>fill</em>, <em>weightx</em> and <em>weighty</em> have the same names as the
fields of the
<a HREF="http://java.sun.com/j2se/1.5.0/docs/api/java/awt/GridBagConstraints.html">GridBagConstraints</a>
class. The reason is that MarvinView uses the
<a HREF="http://java.sun.com/j2se/1.5.0/docs/api/java/awt/GridBagLayout.html">GridBagLayout</a>
 class to determine the places and sizes of the components.
</small>
<p>
Component types: <code>M</code> (molecule), <code>L</code> (label),
<code>B</code> (button), <code>C</code> (checkbox), <code>T</code> (text field) and
<em>I</em> (image).

<p>
Example:
<blockquote><pre>
&lt;param name=&quot;layout&quot; value=&quot;:4:3:
M:0:0:4:1:c:n:
L:0:2:1:1:c:n:0:10:
L:1:2:1:1:c:n:0:10:
C:0:1:1:1:c:n:
C:1:1:1:1:c:n:
C:2:1:1:1:c:n:
B:3:1:1:1:c:n:10&quot;&gt;
</pre></blockquote>

<p>
<h5><a NAME="advanced.param" class="anchor">1.1.5.2 &nbsp; The <code>param</code> parameter</a></h5>
In a <code>param</code> or <code>paramH</code> parameter, the first character is
the field separator (`:' here).
The first field is the type of the first component
(<code>M</code>, <code>L</code>, <code>B</code>, <code>C</code> or <code>T</code>),
followed by its <em>n</em> parameters in the forthcoming <em>n</em> fields.
Then the same for the second component, etc.
Component parameters:
<blockquote>
<table cellpadding="4" cellspacing="0" border="0" class="colored-grid" width="600">
<tr VALIGN=TOP>
    <td><code>:M:</code><em>width</em><code>:</code><em>height</em></td>
    <td><em>width</em> and <em>height</em> are in pixel units.</td></tr>
<tr VALIGN=TOP>
    <td><code>:L:</code><em>fontprops</em></td>
    <td><em>fontprops:</em> font properties.</td></tr>
<tr VALIGN=TOP>
    <td><code>:B:</code><em>fontprops</em><code>::</code><br>
	<code>:B:</code><em>fontprops</em><code>:</code><em>description</em><code>:</code><br>
	<code>:B:</code><em>fontprops</em><code>:</code><em>description</em><code>:</code><em>frame</em></td>
    <td><em>fontprops:</em> font properties,<br>
	<em>description:</em> description string,<br>
	<em>target:</em> target frame for the URL
	    ("<code>_self</code>" = show in the window and frame that
		contain the applet,
	    "<code>_parent</code>" = show in the applet's parent frame,
	    "<code>_top</code>" = show in the top-level frame of the
		applet's window,
	    "<code>_blank</code>" = show in new window).
	</td></tr>
<tr VALIGN=TOP>
    <td><code>:C:</code><em>fontprops</em><code>:</code><br>
	<code>:C:</code><em>fontprops</em><code>:</code><em>description</em></td>
    <td><em>fontprops:</em> font properties,<br>
	<em>description:</em> description string.<br>
	You may specify the checkbox group number in the type field, by writing
	<code>g</code><em>n</em> next to the type-specifying character
	<code>C</code>, where <em>n</em> is the group number.
	No more than one button in a group can be pressed at the same time.
	</td></tr>
<tr VALIGN=TOP>
    <td><code>:T:</code><em>fontprops</em><br>
	<code>:T:</code><em>fontprops</em><code>:</code><em>columns</em><br>
	<code>:T:</code><em>fontprops</em><code>:</code><em>columns</em><code>:</code><em>editable</em></td>
    <td><em>fontprops:</em> font properties,<br>
	<em>columns:</em> number of columns,<br>
	<em>editable:</em> editable (rw) or read only (r).</td></tr>
</table>
</blockquote>
The <em>fontprops</em> field usually contains only one number, the
font size. Additional properties are &quot;b&quot; (bold font) and
&quot;i&quot; (italic). A 12pt bold italic font is specified as
&quot;12bi&quot;.
<p>
Example:
<blockquote><pre>
&lt;param name=&quot;param&quot; value=&quot;:
M:100:100:
L:10b:
C:10:select this molecule:
Cg0:10:include this structure:
B:10:search for more molecules like this:_self:
T:10:
T:10:15:
T:10:15:rw&quot;&gt;
</pre></blockquote>

<p>
<h5><a NAME="advanced.cell" class="anchor">1.1.5.3 &nbsp; The
<code>cell<em>i</em></code> and <code>cell<em>i</em>_<em>j</em></code>
parameters</a></h5>

The parameter values of components in cell <em>i</em> can be specified either
by listing them in one single list, using the <code>cell</code><em>i</em>
parameter, or by assignments for each component separately, using
<code>cell</code><em>i</em>_<em>j</em>, where <em>i</em> = 0, 1, ..., <em><a
href="#advanced.rows">rows</a></em>*<em><a
href="#advanced.cols">cols</a></em>-1 is the cell index and <em>j</em> = 0, 1,
2, ... is the component index inside the cell.
<p>
The contents of <code>cell</code><em>i</em>_<em>j</em> parameters are listed
in the following table for the different component types:
<blockquote>
<table cellpadding="4" cellspacing="0" border="0" class="colored-grid" width="600">
<tr VALIGN=TOP><th>Component</th><th>Parameters</th><th>Description</th></tr>
<tr VALIGN=TOP>
    <td ALIGN=CENTER><code>M</code></td>
    <td><code></code><em>molfile</em></td>
    <td><em>molfile:</em> the molecule URL or the molecule itself
	(inline) in a molecule file format (MDL mol, compressed mol, MRV
	etc.)</td></tr>
<tr VALIGN=TOP>
    <td ALIGN=CENTER><code>L</code></td>
    <td><code></code><em>label</em></td>
    <td><em>label:</em> the label string.</td></tr>
<tr VALIGN=TOP>
    <td ALIGN=CENTER><code>B</code></td>
    <td><code>|</code><em>label</em><code>|</code><em>spec</em><code>|</code><em>action</em></td>
    <td><em>label:</em> the button label,<br>
	<em>spec:</em> the special character (<code>%</code> from now),<br>
	<em>action:</em> the action string.</td></tr>
<tr VALIGN=TOP>
    <td ALIGN=CENTER><code>C</code></td>
    <td><code>|||||</code><br>
	<code>|||</code><em>state</em><code>||</code><br>
	<code>|||</code><em>state</em><code>|-|-</code><br>
	...<br>
	<code>|</code><em>label0</em><code>|</code><em>label1</em><code>|</code><em>state</em><code>|</code><em>action0</em><code>|</code><em>action1</em></td>
    <td><em>label0:</em> label for the unchecked state,<br>
      <em>label1:</em> label for the checked state,<br>
      <em>state:</em> default state (0, 1 or nothing),<br>
      <em>action0:</em> action string for unchecking,<br>
      <em>action1:</em> action string for checking.<br>
      For a &quot;real&quot; checkbox, both labels have to be empty string,
      otherwise the &quot;checkbox&quot; will be buttonlike.<br>
      If both <em>action</em> strings are empty, then the checkbox is disabled.
      To make it enabled without specifying JavaScript actions, the
      <em>action</em> values must be &quot;<code>-</code>&quot;. Example:
      &quot;<code>|||1|-|-</code>&quot;</td></tr>
<tr VALIGN=TOP>
    <td ALIGN=CENTER><code>T</code></td>
    <td><code></code><em>string</em></td>
    <td><em>string:</em> initial value.</td></tr>
<tr VALIGN=TOP>
    <td ALIGN=CENTER><code>I</code></td>
    <td><code>|</code><em>iconfile</em><code>||</code><br>
	<code>|</code><em>iconfile</em><code>|</code><em>action</em><code>|</code><em>title</em></td>
    <td><em>iconfile:</em> GIF or JPEG file containing the icon,<br>
	<em>action:</em> image file or <code>js:</code>JavaScript code<br>
	<em>title:</em> window title, only used if action is an image file.</td></tr>
</table>
</blockquote>
Since newlines are usually neglected in applet parameters by the browsers,
each newline should be replaced or preceded by <code>\n</code>, where
<code>\</code> is an escape character defined using the <a
href="#parameters.escapeChar">escapeChar</a> parameter. If it is defined,
then occurrences of this character must also be escaped as "<code>\\</code>".
The parameter lists of the <code>B</code>, <code>C</code> and
<code>I</code> components should start with "<code>E\|</code>" or
"<code>|</code>", where <code>\</code> is an escape character which overrides
the <a href="#parameters.escapeChar">escapeChar</a> setting and <code>|</code>
is the separator character (can also be colon, comma or anything else).  If
the separator occurs inside the parameter string of a component, then it must
be escaped: "<code>|</code>" should be replaced by "<code>\|</code>".<br>

<p>Note that specifying the applet parameters in JavaScript (instead of
directly inside an HTML applet tag) requires backslash to be escaped once
more, except in case of the newline (\n) which does not need without extra
escaping.

<p>The action string for the <code>B</code>, <code>C</code> and <code>I</code>
components can be an URL for buttons, a JPEG, PNG or GIF file for images, or
<code>js:</code><em>javascript expression</em> for buttons, checkboxes and
images.  The button action string may contain <code>%C</code> (where
<code>%</code> is the special character), which is substituted with the
checkbox states represented as a hexadecimal number.  The n<sup>th</sup> bit
in this number is the state of the n<sup>th</sup> checkbox.

<!--APPLETONLY_BEGIN-->
<p>A javascript expression can be used <em>only</em> if you specify the
<code>MAYSCRIPT</code> attribute in the applet tag.
<!--APPLETONLY_END-->

<p>Example:
<blockquote><pre>
&lt;param name=&quot;escapeChar&quot; value=&quot;\&quot;&gt;
&lt;param name=&quot;cell2_0&quot; value=&quot;\n
  MSketch 04169816382D\n
\n
 66 72  0  0  0  0  0  0  0  0999 V2000\n
mcIWFRwV60\n
...
M  END\n&quot;&gt;
&lt;param name=&quot;cell2_1&quot; value=&quot;1002&quot;&gt;
&lt;param name=&quot;cell2_2&quot; value=&quot;not bad&quot;&gt;
&lt;param name=&quot;cell2_3&quot; value=&quot;|||1|js:cbx(1002,'not selected')|js:cbx(1002,'selected')&quot;&gt;
&lt;param name=&quot;cell2_4&quot; value=&quot;| Include | Include ||js:cbx(1002,'no include')|js:cbx(1002,'include')&quot;&gt;
&lt;param name=&quot;cell2_5&quot; value=&quot;| Exclude | Exclude ||js:cbx(1002,'no exclude')|js:cbx(1002,'exclude')&quot;&gt;
&lt;param name=&quot;cell2_6&quot; value=&quot;| Search |%|js:searchmore(1002)&quot;&gt;
</pre></blockquote>

<p>
A <code>cell</code><em>i</em> parameter contains the parameter values for
all components in a single list. The list starts either with
"<code>E\|</code>" or "<code>|</code>", where <code>\</code> and <code>|</code>
are the escape and separator characters. (Not necessarily backslash and
vertical line.)
<p>
Example:
<blockquote><pre>
&lt;param name=&quot;cell2&quot; value=&quot;E\|\n
  MSketch 04169816382D\n
\n
 66 72  0  0  0  0  0  0  0  0999 V2000\n
mcIWFRwV60\n
...
M  END\n
|1002|not bad|||1|js:cbx(1002,'not selected')|js:cbx(1002,'selected')
| Include | Include ||js:cbx(1002,'no include')|js:cbx(1002,'include')
| Exclude | Exclude ||js:cbx(1002,'no exclude')|js:cbx(1002,'exclude')
| Search |%|searchmore.cgi?mol=1002&amp;n=5&amp;cbx=%C&quot;&gt;
</pre></blockquote>

<!--BEANSONLY_BEGIN-->
<p><small>It is suggested to use celli parameter in MViewPane JavaBean to create
an advanced layout (containing more visual elements than one molecule with a label, such as, checkboxes, buttons, images, etc.).
Since Javascript is not supported in MarvinBeans: use
<em>java.awt.event.ActionListener</em>s and
<em>java.awt.event.ItemListener</em>s
in your code instead of action strings.
See marvinbeans/examples/view-checkbox/MViewExampleWithCheckbox.java.
</small>
</p><!--BEANSONLY_END-->

<h4><a NAME="otherparameters" class="anchor">1.1.3 &nbsp; Other parameters</a></h4>
<blockquote>
<table cellpadding="4" cellspacing="0" border="0" class="colored-grid" width="600">
<tr ALIGN=CENTER><th>Parameter</th><th>Meaning</th><th>Default</th></tr>
<tr valign="TOP"><td><a class="text" name="parameters.copyAsFormat"><code>copyAsFormat</code></a></td>
    <td>Default clipboard format by Copy As action.</td>
    <td align="center">platform dependent</td></tr>
<tr VALIGN=TOP><td><code><a NAME="parameters.copyOpts"
    ></a>copyOpts</code></td>
    <td>Enabled output formats at copy command
    To specify more formats, enumerate them in a comma separated list.
    <ul>
    <li><code>text</code> - Copy As Text</li>
    <li><code>bitmap</code> - Copy As Bitmap Image</li>
    <li><code>emf</code> - Copy As Vector Graphical Image (EMF)</li>
    </ul>
    </td>
    <td ALIGN=CENTER>platform dependent</td></tr>
<tr VALIGN=TOP><td><code>debug</code></td>
    <td>Debug mode. Possible values: 0, 1, 2.</td>
    <td ALIGN=CENTER><code>0</code></td></tr>
<tr VALIGN=TOP><td><code><a NAME="parameters.defaultSaveFormat"
    ></a>defaultSaveFormat</code></td>
    <td>Determines the default chemical file format in the Save As dialog.
    </td>
    <td ALIGN=CENTER><code>mrv</code></td></tr>
<tr VALIGN=TOP><td><code><a NAME="parameters.draggable" class="text"
    >draggable</a></code></td>
    <td>Allows (true) or denies (false) mouse dragged events in cells.</td>
    <td ALIGN=CENTER><code>true</code></td></tr>
<tr VALIGN=TOP><td><code>editable</code></td>
    <td><a NAME="parameters.editable"></a>Modes: display only (0) or
	editable (1, 2).
	If <code>editable=2</code>, then doubleclick launches
	MarvinSketch (instead of a viewer window).</td>
    <td ALIGN=CENTER><code>0</code></td></tr>
<tr valign="TOP"><td><a class="text" name="parameters.escapeChar"><code>escapeChar</code></a></td>
    <td>Escape character to use for parsing the values of
        <a href="#parameters.mol">mol</a> and
        <a href="#advanced.cell">cell</a> parameters.</td>
    <td align="center">&nbsp;</td></tr>
<tr VALIGN=TOP><td><code>importEnabled</code></td>
    <td><a NAME="parameters.importEnabled"></a>Allow or forbid molecule import.
    If this parameter is false, paste function is disabled in MarvinView, also
    <code>Import</code> option is disabled in the Molecule Source window.
    (Use <a href="#parameters.editable">editable</a> parameter to forbid the
    editing of the molecules.)
	</td>
    <td ALIGN=CENTER><code>true</code></td></tr>
<tr VALIGN=TOP><td><code>molFormat</code></td>
    <td>Default file format:
	<code>mol</code>, <code>csmol</code>,
	<code>smiles</code>, <code>cxsmiles</code>,
	<code>cml</code>, <code>pdb</code>, <code>pov</code>, <code>sybyl</code>,
	or <code>xyz</code>.</td>
    <td ALIGN=CENTER><code>mol</code></td></tr>
<tr VALIGN=TOP><td><code>navmode</code></td>
    <td><a NAME="parameters.navmode"></a>Mouse drag action.
	<ul>
	<li><code>translate</code> - move the structure</li>
	<li><code>zoom</code> - change magnification</li>
	<li><code>rotZ</code> - rotate in the screen's plane</li>
	<li><code>rot3d</code> - 3D rotation</li>
	</ul>
	</td>
    <td ALIGN=CENTER><code>rotZ</code></td></tr>
<tr VALIGN=TOP><td><code>selectable</code></td>
    <td><a NAME="parameters.selectable"></a>Use <code>selectable=false</code> to forbid
	molecule selection with mouse click.</td>
    <td ALIGN=CENTER><code>true</code></td></tr>
 <tr VALIGN=TOP><td><code><a NAME="sketchHelp"></a>sketchHelp</code></td>
    <td><a NAME="parameters.sketchHelp"></a>Sketcher help contents.
	</td>
    <td ALIGN=LEFT><code>chemaxon/marvin/help<br>
		/sketch-index.html</code></td></tr>
<tr VALIGN=TOP><td><code><a NAME="sketchQuickHelp"></a>sketchQuickHelp</code></td>
    <td><a NAME="parameters.sketchQuickHelp"></a>Sketcher quick help.
	</td>
    <td ALIGN=LEFT><code>chemaxon/marvin/help<br>
		/sketch.html</code></td></tr>
    <tr valign="TOP"><td><code><a NAME="parameters.namingWebServiceURL"
    ></a>namingWebServiceURL</code></td>
    <td>Specifies the URL of a name recognition and import service for the name import to use.
    </td>
    <td align="CENTER"><code>empty</code></td></tr>
</table>
</blockquote>

<p>

<h3><a NAME="appletonly" class="anchor">1.2 &nbsp; Applet only parameters</a></h3>

<blockquote>
<table cellpadding="4" cellspacing="0" border="0" class="colored-grid" width="600">
<tr ALIGN=CENTER><th width="20%">Parameter</th><th>Meaning</th><th width="10%">Default</th></tr>
<tr VALIGN=TOP><td><code>mol</code></td>
    <td><a NAME="parameters.mol"></a>The URL of the molecule file
	or the file itself (inline) in any supported structure format such as MDL mol, compressed mol, SMILES,
	SMARTS, etc format.<br>
	For multiline values of this parameter, it is recommended to specify
	<a href="#parameters.escapeChar">escapeChar</a>="\" and
	replace or precede each newline occurring in the string with "\n".
	The escape character should also be escaped as "\\".<br>
	The file format and/or import options can be specified if one
	of the following forms is used:<br>
	&quot;file{options}&quot;,<br>
	&quot;file{MULTISET,options}&quot;,<br>
	&quot;file{format:}&quot;,<br>
	&quot;file{format:options}&quot;, or<br>
	&quot;file{format:MULTISET,options}&quot;.<br>
	If the MULTISET option is specified, then a multi-molecule file is
	read into one molecule object containing multiple atom sets.<br>
	Examples: "foo.xyz{f1.4C4}",
	"foo.xyz.gz{gzip:xyz:MULTISET,f1.4C4}"</td>
        <td>&nbsp;</td>
    </tr>
<tr VALIGN=TOP><td><code><strike>preload</strike></code></td>
    <td>Deprecated
	</td><td>&nbsp;</td>
    </tr>
<tr VALIGN=TOP><td><code>cacheMols</code></td>
    <td><a NAME="parameters.cacheMols"></a>Store loaded molecules in an internal
	cache (<code>true</code> or <code>false</code>).
	</td><td>&nbsp;</td>
    </tr>
<tr VALIGN=TOP><td><code>loadMols</code></td>
    <td><a NAME="parameters.loadMols"></a>Comma-separated list of molfiles to
	preload.
	Useful for caching molecules in JavaScript &quot;slideshows&quot;.
	</td><td>&nbsp;</td>
    </tr>
<tr VALIGN=TOP><td><code>molChanged0<br>molChanged1<br>molChanged2<br>...</code>
    </td><td><a name="molChanged0"></a>Evaluates a JavaScript code when the molecule
     is loaded or changed in the specified cell. The number in the
     parameter name determines the cell index. If no molecule table, use
     <i>molChanged0</i> to refer to the molecule. The parameter value should
     be a JavaScript code. E.g.:
    <pre>
    &lt;param name="molChanged0" value="alert('First molecule is modified')"&gt;
    </pre>
</td><td>&nbsp;</td></tr>
<tr valign="TOP"><td><code>listenpropertychange</code></td>
    <td><a NAME="parameters.listenpropertychange"></a>Enable/disable
        property change event listener of the applet.
    If the listener is enabled the applet will
    call the <strong>propertyChange(prop)</strong> JavaScript method.
    The code snippet below demonstrates the handling of property change events in JavaScript.
    Note: If the MAYSCRIPT option is not specified, JavaScript can not be evaluated from the applet.
     <pre>
     function propertyChange(prop) {
        if(document.eventform != null) {
            var s = document.eventform.txt.value;
            document.eventform.txt.value = s + "\n" + prop;
        }
     }

     mview_mayscript=true;
     mview_begin("marvin",400,300);
     mview_param("listenpropertychange","true");
     mview_end();
     </pre>
    Live applet example: <a href="../../examples/applets/view/listeners.html">MarvinView Example - Listening for events in applets</a><br>
    If you also specify the "appletid" parameter, its value will appearin the
    name of the property as prefix (e.g.: "firstapplet.mol").
    </td>
    <td>false</td></tr>
<tr valign="TOP"><td><code><a NAME="parameters.appletid" class="text">appletid</a></code></td>
    <td>Identify applet in property change event notifications to JavaScript.
    Use this parameter with <a href="#parameters.listenpropertychange">listenpropertychange</a>
    when more than one applet are located on the same web page. It helps
    to identify which applet sent the notification.</td><td></td></tr>
<tr valign="TOP"><td><code><a NAME="parameters.listenmouseevent" class="text">listenmouseevent</a></code></td>
    <td>Enable/disable mouse event listener of the applet.
    If the listener is enabled the applet will
    call the <strong>mouseEvent(evt)</strong> JavaScript method.<br>
    The code snippet below demonstrates the handling of mouse events in JavaScript.
    Note: If the MAYSCRIPT option is not specified, JavaScript can not be evaluated from the applet.
    <pre>
    function mouseEvent(evt) {
        var s = document.mouseform.txt.value;
        s = s + evt + "\n";
        document.mouseform.txt.value = s;
    }

     mview_mayscript=true;
     mview_begin("marvin",400,300);
     mview_param("listenmouseevent","true");
     mview_end();
     </pre>
    Live applet example: <a href="../../examples/applets/view/listeners.html">MarvinView Example - Listening for events in applets</a>
    </td>
    <td>false</td></tr>

<tr VALIGN=TOP><td><code><a name="skin"></a>skin</code></td>
    <td><a NAME="parameters.skin"></a>Change the component's Look & Feel (only in SWING applet).
    	If you do not specify this parameter,
	Marvin will use the default LookAndFeel style.
	<BR>This parameter value is the name of the new LookAndFeel class.
	</td>
    <td ALIGN=LEFT><code>javax.swing.plaf<br>
		    .metal<br>
		    .MetalLookAndFeel</code></td></tr>
<tr VALIGN=TOP><td><code><a name="splashScreenImage"></a>splashScreenImage</code></td>
    <td><a NAME="parameters.SplashScreenImage"></a>Change the component's Splash Screen displayed at startup.
    	If you do not specify this parameter, Marvin will use its default splash screen.
	<BR>This parameter value is the relative path of the image, specify relatively to the CODEBASE<br/>
	<font style="font-size: 10px;">Note: Animated gif files can be displayed badly(flashing, or too fast animation)</font>
	</td>
    <td ALIGN=LEFT><code>null</code></td></tr>


<tr VALIGN=TOP><td><a NAME="parameters.license" class="text"><a class="text" name="license"><code>license</code></a></a></td>
<td>Sets the contents of a pre-read license file to the license manager to read and validate licenses from.
</td>
<td>&nbsp;</td></tr>
<tr VALIGN=TOP><td><a NAME="parameters.licenseFile" class="text"><a class="text" name="licenseFile"><code>licenseFile</code></a></a></td>
<td>Sets the license file location to the license manager to read and validate licenses from.
</td>
<td>&nbsp;</td></tr>
    <tr VALIGN=TOP><td><a NAME="parameters.onError" class="text"><a class="text" name="onError"><code>onError</code></a></a></td>
    <td>A string containing a javascript code, that has to be run by the applet, if it has a failure at initialization.<br/>
    The %s wildcard can be used to include the error message given by the applet inside the javascript code.<br/>
    If used with <code>alertError</code> also set to true, the alert window will be shown first, and after the user has 
    pressed the OK button on the alert will the given code run.
    </td>
    <td>&nbsp;</td></tr>
    <tr VALIGN=TOP><td><a NAME="parameters.alertError" class="text"><a class="text" name="alertError"><code>alertError</code></a></a></td>
    <td>If this parameter is set to true, the applet will show a javascript alert window with an error message, if
    there was a problem in the initialization process of the applet.
    </td>
    <td>&nbsp;</td></tr>
    <tr VALIGN=TOP><td><a NAME="parameters.legacy_lifecycle" class="text"><a class="text" name="legacy_lifecycle"><code>legacy_lifecycle</code></a></a></td>
    <td>The "legacy_lifecycle" is a general Java applet parameter. If its value
    is true, it
    helps avoid destroying the applet when you leaves the applet page.
    If you use "marvin.js", it sets its value to true automatically unless you specify its value explicitly. By the way, the defalut value of this parameter in Java
    is false.
    See the discussion about it on the 
    <a href="http://www.chemaxon.com/forum/ftopic4945.html">forum</a>.</td>
    <td>&nbsp;</td></tr>
<tr VALIGN=TOP><td><code><a NAME="viewHelp"></a>viewHelp</code></td>
    <td><a NAME="parameters.viewHelp"></a>Viewer help contents. Accepts relative URL to the applet codebase or any absolute URL.</td>
    <td ALIGN=LEFT><code>chemaxon/marvin/help<br>
		/view-index.html</code></td></tr>
<tr VALIGN=TOP><td><code><a NAME="viewQuickHelp"></a>viewQuickHelp</code></td>
    <td><a NAME="parameters.viewQuickHelp"></a>Viewer quick help. Accepts relative URL to the applet codebase or any absolute URL.</td>
    <td ALIGN=LEFT><code>chemaxon/marvin/help<br>
		/view.html</code></td></tr>
</table>
</blockquote>
<!--APPLETONLY_END-->
<p>


<!--BEANSONLY_BEGIN-->

<h2><a NAME="propchanges" class="anchor">2 &nbsp; Events fired by the JavaBean</a></h2>

To handle the property change events of <code>MViewPane</code>,
the <code>java.beans.PropertyChangeListener</code> interface must be implemented.
<p>

<blockquote>
<table cellpadding="4" cellspacing="0" border="0" class="colored-grid" width="600">
<tr ALIGN=CENTER><th>Property name</th><th>Type</th><th>Meaning</th></tr>
<tr VALIGN=TOP><td><a NAME="propchanges.mols"></a><code>&quot;mols#&quot;</code></td>
    <td ALIGN=CENTER><code>Molecule[]</code></td>
    <td>Molecule(s) changed in the specified cell (# = 0, ..., n-1).
	</td></tr>
<tr VALIGN=TOP><td><code>&quot;implicitH&quot;</code></td>
    <td ALIGN=CENTER><code>String</code></td>
    <td><a HREF="#parameters.implicitH">Implicit Hydrogen display
	style</a> changed.</td></tr>
<tr VALIGN=TOP><td><code>&quot;explicitH&quot;</code></td>
    <td ALIGN=CENTER><code>Boolean</code></td>
    <td>Explicit Hydrogen display style changed.<br>
	<code>true</code>: show explicit H atoms<br>
	<code>false</code>: hide explicit H atoms<br>
	</td></tr>
<tr VALIGN=TOP><td><code>&quot;navmode&quot;</code></td>
    <td ALIGN=CENTER><code>String</code></td>
    <td>Drag action changed.<br>
	<code>translate</code>: Translate, <code>zoom</code>: Zoom,
	<code>rotZ</code>: Rotate, <code>rot3d</code>: Rotate in 3D</td></tr>
<tr VALIGN=TOP><td><code>&quot;tabScale&quot;</code></td>
    <td ALIGN=CENTER><code>Double</code></td>
    <td><a HREF="#parameters.tabScale">Magnification for molecule cells</a>
	changed.</td></tr>
<tr VALIGN=TOP><td><code>&quot;winScale&quot;</code></td>
    <td ALIGN=CENTER><code>Double</code></td>
    <td><a HREF="#parameters.winScale">Zoom window magnification</a>
	changed.</td></tr>


<!-- <tr VALIGN=TOP><td><code>&quot;labels&quot;</code></td>
    <td ALIGN=CENTER><code>Boolean</code></td>
    <td><a HREF="#parameters.labels">Atom labels</a> displayed/not displayed.
	</td></tr> -->
<tr VALIGN=TOP><td><code>&quot;colorScheme&quot;</code></td>
    <td ALIGN=CENTER><code>String</code></td>
    <td><a HREF="#parameters.colorScheme">Color scheme</a> changed.<br>
	</td></tr>
<tr VALIGN=TOP><td><code>&quot;rendering&quot;</code></td>
    <td ALIGN=CENTER><code>String</code></td>
    <td><a HREF="#3d.rendering">Rendering style</a> changed.<br>
	</td></tr>
<tr VALIGN=TOP><td><code>&quot;wireThickness&quot;</code></td>
    <td ALIGN=CENTER><code>Double</code></td>
    <td><a HREF="#3d.wireThickness">Wire thickness</a> changed.<br>
	</td></tr>
<tr VALIGN=TOP><td><code>&quot;stickThickness&quot;</code></td>
    <td ALIGN=CENTER><code>Double</code></td>
    <td><a HREF="#3d.stickThickness">Sticks diameter</a> changed.<br>
	</td></tr>
<tr VALIGN=TOP><td><code>&quot;ballRadius&quot;</code></td>
    <td ALIGN=CENTER><code>Double</code></td>
    <td><a HREF="#3d.ballRadius">Ball radius</a> changed.<br>
	</td></tr>
<tr VALIGN=TOP><td><code>&quot;grinv&quot;</code></td>
    <td ALIGN=CENTER><code>Boolean</code></td>
    <td><a href="#parameters.grinv">Graph invariants</a> displayed/not displayed.
	</td></tr>
<tr VALIGN=TOP><td><code>&quot;downWedge&quot;</code></td>
    <td ALIGN=CENTER><code>String</code></td>
    <td><a HREF="#parameters.downWedge">Down wedge bond orientation</a>
	changed.
	</td></tr>
<tr VALIGN=TOP><td><code>&quot;animFPS&quot;</code></td>
    <td ALIGN=CENTER><code>Double</code></td>
    <td><a HREF="#3d.animFPS">Animation frame rate</a>
	changed.</td></tr>
<tr VALIGN=TOP><td><code>&quot;animDelay&quot;</code></td>
    <td ALIGN=CENTER><code>Double</code></td>
    <td><a HREF="#3d.animDelay">Animation repeat delay</a>
	changed.</td></tr>
<tr VALIGN=TOP><td><code>&quot;animSync&quot;</code></td>
    <td ALIGN=CENTER><code>Double</code></td>
    <td><a HREF="#3d.animSync">Animation synchronization</a>
	changed.</td></tr>
<tr VALIGN=TOP><td><code>&quot;invisibleSets&quot;</code></td>
    <td ALIGN=CENTER><code>Long</code></td>
    <td><a HREF="#parameters.showSets">Visibility of atom sets</a>
	changed.</td></tr>

<tr VALIGN=TOP><td><code>&quot;atomNumbersVisible&quot;</code></td>
    <td ALIGN=CENTER><code>Boolean</code></td>
    <td><a HREF="#parameters.atomNumbersVisible">Visibility of atom numbers</a>
	changed.</td></tr>
<tr VALIGN="TOP"><td><code>&quot;valencePropertyVisible&quot;</code></td>
    <td align="CENTER"><code>Boolean</code></td>
    <td><a HREF="#parameters.valencePropertyVisible">Visibility of valence properties</a>
	changed.</td></tr>
<tr VALIGN="TOP"><td><code>&quot;ligandErrorVisible&quot;</code></td>
    <td align="CENTER"><code>Boolean</code></td>
    <td><a HREF="#parameters.ligandErrorVisible">Visibility of ligand error</a>
	changed.</td></tr>
<tr VALIGN=TOP><td><code>&quot;atomMappingVisible&quot;</code></td>
    <td ALIGN=CENTER><code>Boolean</code></td>
    <td><a HREF="#parameters.atomMappingVisible">Visibility of atom mapping</a>
	changed.</td></tr>
<tr VALIGN=TOP><td><code>&quot;popupMenusEnabled&quot;</code></td>
    <td ALIGN=CENTER><code>Boolean</code></td>
    <td>Popup menus are enabled/disabled.
	</td></tr>
<tr VALIGN=TOP><td><code>&quot;detachable&quot;</code></td>
    <td ALIGN=CENTER><code>Boolean</code></td>
    <td><a href="#parameters.detachable">Detachability of viewer</a> is changed.
	</td></tr>
	
<tr VALIGN=TOP><td><code>&quot;selectedIndex&quot;</code></td>
    <td ALIGN=CENTER><code>Integer</code></td>
    <td><a href="#parameters.selectedIndex">The selected cell</a>is changed.
	</td></tr>
<tr VALIGN=TOP><td><code>&quot;atomMarked&quot;</code></td>
    <td ALIGN=CENTER><code>String</code></td>
    <td>An atom is selected on one of the canvases. The value contains the cell 
    index and the atom index separated with a comma.
	</td></tr>
<tr VALIGN=TOP><td><code>&quot;draggable&quot;</code></td>
    <td ALIGN=CENTER><code>Boolean</code></td>
    <td>The <a href="#parameters.draggable">draggable</a> property is changed.
	</td></tr>
<tr VALIGN=TOP><td><code>&quot;windowOpened&quot;</code></td>
    <td ALIGN=CENTER><code>String</code></td>
    <td>A new MarvinView or MarvinSketch window is opened from the viewer. 
    If a sketcher is opened the value contains sketchInView#cellindex#, when
    a viewer is opened, the value contains viewframe#cellindex, where #cellindex 
    is the index of the opened cell in the viewer firing the event.
	</td></tr>
<tr VALIGN=TOP><td><code>&quot;windowClosed&quot;</code></td>
    <td ALIGN=CENTER><code>Boolean</code></td>
    <td>An MarvinView or MarvinSketch window that was opened from the viewer is being closed. 
    If a sketcher was opened the value contains sketchInView#cellindex#, when
    a viewer wass opened, the value contains viewframe#cellindex, where #cellindex 
    is the index of the opened cell in the viewer firing the event.
	</td></tr>
<tr VALIGN=TOP><td><code>&quot;sketchInView&quot;</code></td>
    <td ALIGN=CENTER><code>MSketchPane</code></td>
    <td>This event is being fired when a sketcher gets opened from the viewer,
    or such a sketcher is closing. At open the newValue contains a reference for the
    opening sketcher, when closing, the newValue is null, and the oldValue contains
    the reference for the sketcher.
	</td></tr>
</table>
</blockquote>
<p>
<p>Since the behaviour of buttons and checkboxes in molecule <a href="#advanced.cell">cells</a> are similar as
<em>javax.swing.AbstractButton</em>
objects, they can fire
<em>java.awt.event.ActionEvent</em>s and
<em>java.awt.event.ItemEvent</em>s.</p>
<!--BEANSONLY_END-->
<p>
<!--APPLETONLY_BEGIN-->
<hr WIDTH="50%">

<h2><a NAME="troubleshooting" class="anchor">3 &nbsp; Troubleshooting - MView and JMView tables</a></h2>
<ul>
<li>JavaScript Error: uncaught Java exception
    java/lang/ArrayIndexOutOfBoundsException<br>
    <strong>Cause:</strong> <code>getC</code>, <code>setC</code>,
    <code>setActionC</code>, <code>setActionB</code> or <code>setM</code>
    called with too large argument.
<li>Molecule cells hang out of the applet, some inner rows intertwine.<br>
    <strong>Possible causes:</strong>
    <ul>
    <li>Applet size too small.<br>
    <li>There are some rows of labels, buttons or checkboxes in a cell between
	the molecule, and their total height is bigger than the molecule height
	you specified.<br>
	<strong>Solution</strong>: increase molecule height, decrease
	font size or reduce the number of rows per cell.</li>
    </ul>
</ul><!--APPLETONLY_END-->

</body>
</html>
