<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
<head>
    <link rel="stylesheet" href="../marvinmanuals.css" type="text/css">
<title>About ChemAxon Products</title>
</head>
<body>
<h2>About ChemAxon Products</h2>

<h4><PERSON> and <PERSON></h4>
Marvin is a collection of Java tools containing MarvinSketch, MarvinView
and MarvinSpace for drawing and visualising chemical structures, queries and reactions.

<h4>Calculator Plugins</h4>
Calculator Plugins are a range of structure based prediction tools.<br>
Single or small numbers of structures can be processed from the Tools menu
of MarvinSketch and MarvinView. For batch processing they can be called from
command line (via cxcalc), ChemAxon's Chemical Terms language and from the API.

<h4>JChem Base</h4>
JChem Base is a tool for the development of applications that allow for the
search of mixed structural and non-structural data.<br>
JChem Base will integrate with a variety of database systems
(Oracle, MS SQL Server, DB2, Access, etc) with web interfaces
and offers fast substructure, similarity, exact and superstructure
search engine using 2D hashed fingerprints.

<h4>JChem Cartridge</h4>

JChem Cartridge adds chemical knowledge to the Oracle platform.<br>
Data can be searched by structure, substructure and similarity
criteria through extending Oracle's native SQL language.
Chemical data can be easily inserted and modified using SQL.

<h4>Instant JChem</h4>

Instant JChem is a desktop application for scientists to manage chemical and non
chemical information on local and remote databases.<br>

Based on ChemAxon's JChem and Marvin, Instant JChem lets users easily create
chemical databases, import and export structure files and view, search, sort,
analyse and edit contents. 

<h4>Standardizer</h4>

Standardizer is a structure canonicalization tool in JChem for converting
molecules from different sources into standard representational forms.
Standardizer can automate the identification of mesomers and tautomers and
can be used for counter-ion removal.

<h4>Reactor</h4>

Reactor is the virtual reaction engine of Chemaxon's JChem technology.
It supports "smart" reactions (generic reaction equations combined with reaction rules)
generating chemically feasible products even in batch mode.
The professional version of the tool includes support for multi-step virtual
synthesis and filtering of chemically feasible molecules which are not of interest.

<h4>JKlustor</h4>

JKlustor is a tool of JChem for clustering, diversity calculations,
and library comparisons based on molecular fingerprints and other descriptors.
JKlustor is useful in combinatorial chemistry, drug design, or other areas where
a large number of compounds need to be analyzed.

<h4>Screen</h4>

Screen is a comprehensive HTS suite in JChem, which works with files and structure
databases and features various models for the similarity analysis of molecules
and pharmacophore hypotheses.

<h4>Fragmenter</h4>

Fragmenter uses RECAP and other methods to create building blocks by fragmenting
larger molecules. Fragmenter is particularly relevant for generating analogues
of biologically active compounds for lead discovery and for decomposing compounds
to identify central structures.

</body>
</html>