
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<link rel="stylesheet" type="text/css" href="../marvinmanuals.css">
<title>Homology groups and Markush structures</title>
</head>

<body>
<h1>Homology groups and Markush structures</h1>

<p>Currently JChem enables the searching of Markush structures containing 
Homology groups only with specific molecule queries (with no query 
features). Homology groups are supported on the target and on the query side 
- the latter only for non-Markush targets. Properties can also be specified for the groups.  
<p><a href="../sketch/sketch-basic.html#query.groups">Read the user's guide about Homology groups and editing their properties in MarvinSketch.</a>

<h2>Contents</h2>

  <ul>
                    <li><a href="#definition">Definition of Homology groups</a></li>
                    <li><a href="#searchoptions">Search options</a></li>
                    <li><a href="#enumeration">Markush Enumeration</a></li>
                    <li><a href="#homology_properties">Properties of Homology groups</a></li>
                    
  </ul>
<h2><a class="anchor" name="definition">Definition of Homology groups</a></h2>

<p>Homology groups are represented by <a class="anchor" name="Pseudo">Pseudo</a> atoms, labeled with common chemical annotations of these groups. Most groups have Alias names that allow shorter names. The names are case insensitive, spaces might be inserted. <br>
Pseudo atoms can be easily drawn in Marvin Sketch using the Homology groups <a href="../sketch/sketch-basic.html#templates">template group</a>.</p>

There are two major types of Homology groups regarding the way of their definition:
<ol>
        <li><b><a class="anchor" name="builtin">Built-in Homology groups</a></b> are defined by specific structural properties of the group. These groups are not enumerated during the search, but the query structure 
		is recognized when fulfills the requirements for such a structure. The possible 
		number of covered structures is usually infinite, unless the number of atoms is 
		limited. Examples of built-in groups are alkyl, aryl, heterocycle, etc.</li>

        <li><b>User-defined Homology groups</b> are explicitly defined and only the listed
        structures can match these homology groups. The definition is given in the 
		form of an R-group definition and any of the generic features (discussed in the 
		<a href="#enumeration">Markush	chapter</a>) can be used in the definition. There are some  <a href="#predefined">Predefined</a> and new 'User-defined' groups can be added, too. These 'User-defined' definitions can be customized by the user, and they can be context-specific. (E.g. Protecting group definition will depend on the functional group, which is protected.)</li>
</ol>

<h3>1. Built-in Homology groups</a></h3>

Table 1. shows the properties of the Built-in Homology groups. The groups are recognized having specific features. These features are shown in the table
as "compulsory" parts. Optional parts are structures that are not necessarily part of the structure that matches on the Homology group. <br>
'Incomplete case' means structures that are substructures of structures that would represent a homology group.  
It is required that even such incomplete structures should be extended to a complete homology
group. <a href="#searchoptions">Search options</a> regulate if incomplete structures can match on a homology group or complete structures are required.      
<p> The "Example" column shows complete structures representing the homology groups. 

<p>
<b>Table 1.</b> Built-in Homology groups
</p>
<table border="0" cellspacing="0" cellpadding="5" class="grid" summary="" rules="all">
        <thead>
        <tr>
            <th>Group name (Alias names)</th>
            <th width="200">Compulsory</th>
            <th width="200">Optional</th>
            <th width="200">Incomplete case</th>
            <th>Example</th>
         </tr>
    </thead>
    <tbody>
        <tr>    
            <td>Alkyl <br>(CHK)</td>
            <td>- minimum of one carbon atom<br>
                - only carbon and hydrogen atoms<br>
                - single bonds<br>
                - no ring bonds</td>
            <td>connection point at arbitrary position(s)</td>
            <td>same requirements</td>
        <td align="center"><img src="Homology_files/alkyl.png" alt="" width="234" height="116"></td>
        </tr>
        <tr>
            <td>Alkenyl <br>(CHE)</td>
            <td>- at least one double bond<br>
                - minimum of 2 carbon atoms<br>
                - otherwise same as for Alkyl</td>
            <td>same as above</td>
            <td>same as at compulsory, but the matching structure does not need to have any double bond</td>
	         <td align="center"><img src="Homology_files/alkenyl.png" alt="" width="72" height="70"></td>
        </tr>
        <tr>
            <td>Alkynyl <br>(CHY)</td>
            <td>- at least one triple bond<br>
                - minimum of 2 carbon atoms<br>
                - otherwise same as for Alkyl</td>
            <td>same as above, double bond</td>
            <td>same as at compulsory, but the matching structure does not need to have any triple bond</td>
	         <td align="center"><img src="Homology_files/alkynyl.png" alt="" width="175" height="108"></td>
        </tr>
        <tr>
            <td>CarbonTree<br>(acyclicCarbon)</td>
            <td>Any connected acyclic carbon structure.</td>
            <td align="center">-</td>
            <td align="center">-</td>
            <td align="center"><img src="Homology_files/carbontree.png" width="152" height="85"></td>
        </tr>
        <tr>
            <td>Carboalicyclyl <br>(CYC, cycloalkyl)</td>
            <td>- monocyclic or fused aliphatic rings<br>
                - only carbon and hydrogen atoms<br>
		- no substitution by (saturated) alkyl chains</td>
            <td>- double or triple bonds in the ring but not aromatic<br>
                - several connection points on the rings
            </td>
            <td>- any carbon structure without aromatic bonds<br>
                - the substituting alkyl chain can be unsaturated</td>
            <td align="center"><img src="Homology_files/carboalicyclyl.png" alt="" width="136" height="132"></td>
        </tr>
        <tr>
            <td>Carboaryl <br>(ARY, aryl)</td>
            <td>- monocyclic or fused rings<br>
                - among these rings at least one should be aromatic<br>
                - only carbon and hydrogen atoms</td>
            <td>- double bonds/triple bonds in the aliphatic rings<br>
                - several connection points but all must be on an
    aromatic ring (can't have external connection on an
    aliphatic ring)</td>
            <td>- similar to carboalicyclyl but the atoms can have aromatic bonds:<br>
                - any carbon structure where the external connection is on an 
                atom that has aromatic bond or has only one bond.<br>
                - the matching structure doesn't need to have a ring.</td>
            <td align="center"><img src="Homology_files/aryl.png" alt="" width="160" height="145"></td>
        </tr>
        <tr>
            <td> Heteromonoalicyclyl <br>(HET, heterocycle, heterocyclyl,<br> AliphaticHeterocyclyl)</td>
            <td>- monocyclic, aliphatic ring with at least one hetero atom, carbon atom is also required<br>
                <br>
		</td>
            <td>same as carboalicyclyl</td>
            <td>similar to carboalicyclyl but here hetero atoms are accepted as well, which means any structure without aromatic bonds</td>
            <td align="center"><img src="Homology_files/heterocycle.png" alt="" width="100" height="97"></td>

        </tr>
        <tr>
            <td>Heteromonoaryl <br>(HEA, Heteroaryl)</td>
            <td>- similar to aryl but the monocyclic aromatic ring should contain at least one
            hetero atom, carbon atom is also required<br>
		- no fused rings</td>
            <td>same as aryl</td>
            <td>Similar to aryl but here hetero atoms are accepted as well. Condition for the
externally connecting atom holds as in case of aryl.</td>
            <td align="center"><img src="Homology_files/heteroaryl.png" alt="" width="88" height="90"></td>
        </tr>
        <tr>
            <td>FusedHeterocyclyl <br>(HEF, fusedHetero)</td>
            <td>- Fused ring system having at least one hetero atom, carbon atom is also required</td>
            <td>same as aryl, but the connection point can be on an aliphatic ring as well</td>
            <td>Any structure having hetero, carbon and hydrogen atoms, with any bonds.</td>
            <td align="center"><img src="Homology_files/fusedhetero.png" width="188" height="103"></td>
        </tr>
        <tr>
            <td>Cyclyl<br>(anycyclyl, anyring)</td>
            <td>Any kind of ring regardless fuseness, aromaticity and hetero-carbo nature.</td>
            <td align="center">-</td>
            <td align="center">-</td>
            <td align="center"><img src="Homology_files/cyclyl.png" width="132" height="86"></td>
        </tr>
        <tr>
            <td>RingSegment<br>-</td>
            <td>A part of a ring where every atom has only 2 ring connections. 
            Non ring connections are allowed. The group does not represent a whole ring.</td>
            <td align="center">-</td>
            <td align="center">-</td>
            <td align="center"><img src="Homology_files/ringsegment.png" width="81" height="82"></td>
        </tr>
        <tr>
            <td>Unknown group <br>(UNK)</td>
            <td align="center">-</td>
            <td>Any structure. Unknown structures are enumerated as the union of all other homology groups.</td>
            <td align="center">-</td>
            <td align="center">-</td>
        </tr>
	<tr>
            <td>AnyAtom<br>-</td>
            <td>Any atom except hydrogen</td>
            <td>-</td>
            <td align="center">-</td>
            <td align="center">C, N, O, P, S, ...</td>
        </tr>
        <tr>
            <td>Metal <br>(MX)</td>
            <td>Any metal</td>
            <td align="center">-</td>
            <td align="center">-</td>
            <td align="center">U, K, Fe, Na, Ni, Al, ...</td>
        </tr>
        <tr>
            <td>AlkaliMetal <br>(AMX)</td>
            <td>Alkali and alkaline earth metals</td>
            <td align="center">-</td>
            <td align="center">-</td>
            <td align="center">Na, K, Ca, Mg, ...</td>
        </tr>
        <tr>
            <td>OtherMetal <br>(A35)</td>
            <td>Group IIIa-Va metals</td>
            <td align="center">-</td>
            <td align="center">-</td>
            <td align="center">Al, Ga, ...</td>
        </tr>
        <tr>
            <td>TransitionMetal <br>(TRM)</td>
            <td>Transition metals excluding lanthanum</td>
            <td align="center">-</td>
            <td align="center">-</td>
            <td align="center">Fe, Ni, Zn, Co, Hg, W, ...</td>
        </tr>
        <tr>
            <td>Lanthanide <br>(LAN)</td>
            <td>Lanthanides (including lanthanum)</td>
            <td align="center">-</td>
            <td align="center">-</td>
            <td align="center">Nd, Ce, Pr, ...</td>
        </tr>
        <tr>
            <td>Actinide<br>(ACT)</td>
            <td>Actinides (including actinium)</td>
            <td align="center">-</td>
            <td align="center">-</td>
            <td align="center">U, Th, Pa, ...</td>
        </tr>
    </tbody>
</table>

<h4><a class="anchor" name="subset">Subset rules between homology groups</a></h4>

	<ul>
		<li>alkyl, alkenyl and alkynyl are subsets of carbontree</li>
		<li>carboalicyclyl, carboaryl, heteromonoaryl, heteromonoalicyclyl and fused hetero cyclyl are subsets of cyclyl</li>
		<li>alkalimetal, transitionmetal, othermetal, lanthanides and actinides are subsets of metal.</li>
		<li>all of the above groups are subsets of the "any" homology group</li>		
	</ul>  	 

<h3><a name="userdefined" class="anchor">2. User-defined <a name="predefined" class="anchor"></a>Homology groups</a></h3>

<p>The homology groups are defined by the user, but there are some <a href="#predefined">Predefined</a> groups, too. User-defined Homology groups are represented by R-group definitions and during search these Pseudo atoms are translated to the corresponding R-group definitions.</p>

<!--<p>Location of the definitions: in chemaxon/enumeration/homology/user_def_groups in the jar file.
The name implies that these groups are represented by these definitions during
search and enumeration as well. For the enumeration of built-in structures see section enumeration.

<p>The user can modify these definitions, just has to copy the definition from the
jar file to the user chemaxon directory into the directory: homology/user_def_groups.
You can modify these files which will affect searching groups.
-->

<p>These group definitions are <a href="#customizedef">customizable</a>, the user can modify them or can make new definitions as well. Group names are treated as case insensitive, but in case sensitive file systems the definition files should be lowercase.

<!-- <p>Technically, the group definitions are handled in a conversion step before the 
search, in which the homology group is replaced by the R-atom. This atom receives
an alias string, which shows the name of the converted homology group, and the
R-group index. Internally this alias helps to distinguish between originally existing
R-groups and R-groups resulting from homology conversion.
 -->
<p>The following <a name="predefined" class="anchor"></a> <a href="#predefined">Predefined </a>(User-defined) Homology groups are readily available in the system:

<h4><a name="halogen" class="anchor">Halogen</a></h4>

Halogen elements: F, Cl, I and Br.
<br>
JChem's group name: halogen
<br>
alias name: HAL

<h4><a name="protecting" class="anchor">Protecting</a></h4>

<p>Protecting groups' definition file contains several definitions, 
each for protecting different functional groups. The protected functional
group is defined by the neighborhood of the R-atom. When the R-atom has the same
neighborhood as the "protecting" pseudo atom, then the group is replaced 
by the R-atom.</p>
<p>
The conversion processes the group definitions in their order in the file. 
This means that more specific environments should be placed earlier.
For example, a carboxyl protecting group definition should precede an 
alcohol definition, otherwise the alcohol definitions will be applied 
instead.
Currently they are located in the following order:
<ol>
	<li>amino</li>
	<li>carboxyl</li>
	<li>alcohol</li>
</ol>

<!--See example definition. (rename it to mrv.)-->

<p>Currently the system can't handle protecting groups having more than one
attachment point, or groups where the heavy atoms of the functional group 
should be changed by the substitution.
The readily available definitions contain amine, carboxyl and hydroxyl 
protecting groups.</p>

<p>JChem's group name: protecting
<br>
alias name: PRT</p>

<p>Some examples with different functional groups protected can be found on Table 2.</p>

<p>
<b>Table 2.</b> Protecting group examples
</p>
<p>
<table border="0" cellspacing="0" cellpadding="5" class="grid" summary="" rules="all">
        <tr>
            <td align="center"><b>Protecting group</b></td>
            <td align="center" colspan="3"><b>Represented examples</b></th>
         </tr>
        <tr>
            <td align="center"><img src="Homology_files/protectingN.png" alt="" width="114" height="74"></td>
            <td align="center"><img src="Homology_files/protectingN1.png" alt="" width="75" height="82"></td>
            <td align="center"><img src="Homology_files/protectingN2.png" alt="" width="100" height="104"></td>
            <td align="center"><img src="Homology_files/protectingN3.png" alt="" width="129" height="123"></td>
        </tr>
        <tr>
            <td align="center"><img src="Homology_files/protectingO.png" alt="" width="120" height="57"></td>
            <td align="center"><img src="Homology_files/protectingO1.png" alt="" width="124" height="96"></td>
            <td align="center"><img src="Homology_files/protectingO2.png" alt="" width="106" height="86"></td>
            <td align="center"><img src="Homology_files/protectingO3.png" alt="" width="101" height="70"></td>
        </tr>
        <tr>
            <td align="center"><img src="Homology_files/protectingCOO.png" alt="" width="136" height="68"></td>
            <td align="center"><img src="Homology_files/protectingCOO1.png" alt="" width="105" height="72"></td>
            <td align="center"><img src="Homology_files/protectingCOO2.png" alt="" width="135" height="77"></td>
            <td align="center"><img src="Homology_files/protectingCOO3.png" alt="" width="117" height="69"></td>
        </tr>
</table>


<h4><a name="acyl" class="anchor">Acyl</a></h4>

<p>Residue left after removal of one or more OH groups from an acid. Currently it behaves 
as simple pseudo atoms: can only be matched by itself and is not enumerated. This behavior
 complies with the Thomson-Reuters/Questel acyl group handling.</p>

<p>JChem's group name: acyl
<br>
Alias name: acy</p>

<h4><a name="any" class="anchor">Any group</a></h4>

<p>The union of all other homology groups except acyl, unknown and protecting. This union is represented 
by cyclyl, carbonTree, metal and halogen groups. If the group occurs
in a ring then represents a ringSegment homology group.
</p>
<p>JChem's group name: any
<br>
alias names: XX, anygroup</p>

<h2><a class="anchor" name="searchoptions">Search Options</a></h2>

Search options regulating the search behavior are also available:

<p>Currently there is one regulating option: 'completeHG', which specifies if the
part of the query side structure matching on the given group should represent an
entire homology group or if substructures are also accepted. Of course in the incomplete
case an entire structure can also match on the given homology group.
<br>For example, if completeHG is set to true (default) an alkyl chain can't match on a cycloalkyl
group, only a ring (system). The detailed behavior is found at the definition of the groups.
And example is shown on Table 3.</p>
<p><b>Table 3.</b> Complete and incomplete structures of Homology groups</p>
<p><table border="0" cellspacing="0" cellpadding="5" class="grid" summary="">
    <tr>
        <td rowspan="2" align="center">
            <b>target</b>
        </td>
        <td rowspan="2" align="center">
            <b>query</b>
        </td>
        <td colspan="2" align="center">
            <b>hit</b>
        </td>
    </tr>

    <tr>

        <td align="center">
            <em>completeHG:y</em>
        </td>
        <td align="center">
            <em>completeHG:n</em>
        </td>
    </tr>
    <tr>

    <tr>
        <td align="center" rowspan="2">
            <img src="Homology_files/cycloalkylt.png" alt="" width="90" height="57">
        </td>
        <td align="center"><img src="Homology_files/cycloalkylq1.png" alt="" width="157" height="90"></td>
        <td align="center"><img src="Homology_files/yes.png" width="24" height="24" alt=""></td>
        <td align="center"><img src="Homology_files/yes.png" width="24" height="24" alt=""></td>
    </tr>
    <tr>
        <td align="center"><img src="Homology_files/cycloalkylq2.png" alt="" width="231" height="111"></td>
        <td align="center"><img src="Homology_files/no.png" width="24" height="24" alt=""></td>
        <td align="center"><img src="Homology_files/yes.png" width="24" height="24" alt=""></td>
    </tr>
</table></p>

<h2><a class="anchor" name="enumeration">Markush Enumeration</a></h2>

<p>To enable the enumeration of homology groups, the 'Homology Enumeration' option of 
Markush enumeration has to be switched on. Otherwise the 'Homology groups' are kept 
as 'Pseudo atoms'. This latter option might be useful for showing that these 
structures can't be fully enumerated.</p>

<h3><a class="anchor" name="predefined.enum">
Predefined</a> Homology groups</h3>

<p>For the Predefined groups, the R-group definitions specify the enumerable library as User-defined groups. I.e. these groups definitions can be <a href="#customizedef">customized</a>. These structures are
characteristic to the Homology group and encompass simple and large structures as well. </p>
<p>We have to emphasize, that these definitions are  used only for enumeration and do not affect searching. As noted earlier, arbitrary structures fulfilling the requirements for the Homology group will match such a target.</p>
<p>Enumeration definitions contain two attachment points as default. After enumeration these are the atoms which connect to the first two neighbors
of the group. If the enumerated Homology group's Pseudo atom has more than two connections, then further attachment points are added. These are put on atoms that have free valence and comply the requirements for externally connecting atoms of the given group. E.g. for 'aryl' only aromatic ring atoms can be the connection points. 
The atoms of the definition are investigated in the order of the Atom Numbers.  
If a definition does not have the sufficient number of such atoms, then it is rejected. When every definition of the homology group is rejected, an exception is thrown showing that the given homology group does not have any valid enumeration definition. </p>  

<!--<p>The definition files are located in the following directory:
chemaxon/enumeration/homology/enumeration_only in the jar file.

<p>These files can be copied to the users "chemaxon" (or ".chemaxon") directory as
written in case of user-defined groups. The name of the definition's directory
in the user's directory should be "homology/enumeration_only".
-->

<h3><a class="anchor" name="userdefenum">
Customizing the (User-defined) Homology groups</a></h3>

<p>Enumeration of User-defined Homology groups use the same (<a href="#customizedef">customizable</a>) 
R-group definitions as searching.
User-defined Homology groups should have the same number of connections as in the definitions.</p>
Customization modes:  
<ol>
<li>To define NEW User-defined homology (or Protecting) groups under different file names from the Predefined files in the <i>MarvinBeans-enumeration.jar.</i></li>
<li>To MODIFY the Predefined User-defined homology (or protecting) groups being in the <i>MarvinBeans-enumeration.jar.</i></li>
</ol>
Enumeration-only type User-defined groups can be defined by the same way. 

<h4><a class="anchor" name="customizedef">Modifying the Predefined Homology group definitions</a></h4>

The <i>lib</i> directories of the installed MarvinBeans and JChem contain MarvinBeans-enumeration.jar file. The default homology groups are defined there as user-defined groups and as enumeration-only groups. 
<dl>
	<dt>Modifying the Predefined 'Enumeration-only' groups</dt>
	<dd>Directory <i>chemaxon/enumeration/homology/enumeration_only</i> in the <i>MarvinBeans-enumeration.jar</i> file.
These groups are represented by these definitions during enumeration only.</dd>
	<dt>Modifying the new User-defined groups</dt>
	<dd>Directory <i>chemaxon/enumeration/homology/user_def_groups</i> in the MarvinBeans-enumeration.jar file.
These groups are represented by these definitions during search and enumeration as well.</dd>
</dl> 

<h4><a class="anchor" name="chemaxondirectory">Location of user-defined homology group definition files</h4>
<p>The default location of <a class="anchor" name="chemaxonhome"><b>chemaxon_home</b> directory of the user</a> on different platforms: 
<ul>
	<li>Windows: %USERPROFILE%\chemaxon\ (in other words ..\Users\&lt;USERNAME&gt;\chemaxon\)</li>
	<li>Unix/Linux: ~/.chemaxon/</li>
</ul>
</p>

<p>Location of "User-defined" (for search and enumeration) user-defined homology group definition files:
<a href="#chemaxonhome">chemaxon_home</a>/homology/user_def_groups/
</p>
<p>Location of "Enumeration-only" user-defined homology group definition files:
<a href="#chemaxonhome">chemaxon_home</a>/homology/enumeration_only/
</p>
<p><b>Note:</b> Create the above two directories if they do not exist.</p>


<h4>1. Defining NEW User-defined Homology (or Protecting) groups</h4>

<ol>
<li>Draw the desired group definition in MarvinSketch and save as mrv; the name of the new group should be specified by 
the name of the file; the name of the file must be in lower case; <br>See example <i>nucleobase.mrv</i> below:<br>
<img src="Homology_files/nucleobase.png" width="476" height="363" border="0" alt=""></li>
<li>copy the mrv file into <i><a href="#chemaxonhome">chemaxon_home</a>/homology/user_def_groups/</i>.</li>
</ol>

The files of enumeration-only type User-defined groups should be placed into the directory <i><a href="#chemaxonhome">chemaxon_home</a>/homology/enumeration_only/</i>.  

<h4>2. Modifying the Predefined (User-defined) Homology (or Protecting) groups</h4>
Modifying these files will affect searching/enumeration in case of 
User-defined groups and the enumeration in case of the Predefined groups.
<BR> The modified definition or the newly added group can also be dependent on the neighborhood 
(context-sensitive) as in the case of Protecting groups.
<BR>

The modification of these definitions can be executed:
<ul>
<li>the same way as described above for the creation of the NEW User-defined Homology (or Protecting) groups, but the name of the mrv file must be the same 
as the built-in file name within MarvinBeans-enumeration.jar; copy the mrv file into <i><a href="#chemaxonhome">chemaxon_home</a>/homology/user_def_groups/</i> </li> 
<li>or by modifying the existing default file from MarvinBeans-enumeration.jar</li>
<ol>
	<li>Copy protecting group definition to the user's chemaxon library: e.g. from 
	<i>.../MarvinBeans-enumeration.jar/chemaxon/enumeration/homology/user_def_groups/protecting.mrv</i> to 
	<i><a href="#chemaxonhome">chemaxon_home</a>/homology/user_def_groups/ </i></li>
	<li>Open the newly copied file in the user's directory with MarvinSketch.</li>
	<li>A dialog appears asking the index of molecule to open. Enter 1 because 
	this contains the amino protecting group definition. If the proper molecule number is
	not known, all the definitions can be displayed using MarvinView.</li>
	<li>Overwrite the structures, e.g. delete the FMOC group, see Table 4. 
	The new definition will be used in searching and enumeration, see Table 4.</li>
</ol>
</ul>
<p>
The files of enumeration-only type user-defined groups must be placed into the directory <i><a href="#chemaxonhome">chemaxon_home</a>/homology/enumeration_only/</i>.  
</p>
<p>
If you would like to have different definitions for searching and enumeration of a
 user-defined group, then a separate file should be specified under the same file name in the "<i>enumeration_only</i>" dictionary as well.
 In this case the content of the "<i>user_def_groups</i>" will be used during searching and 
 the content of the "<i>enumeration_only</i>" for enumeration. 
</p>

<p>If a definition is modified it comes into effect immediately, however the addition of a new group 
requires a restart of the Java Virtual Machine. </p>

<p>
<b>Table 4.</b> Modifying amino protecting group definitions.
<p>

<table border="0" cellspacing="0" cellpadding="5" class="grid" summary="">
	<tr>
		<td align="center"><b>overwriting the definition</b></td>	
		<td align="center"><b>sample markush file</b></td>	
		<td align="center"><b>enumerations</b></td>	
	</tr>
	<tr>
		<td align="center"><img src="Homology_files/protectingOverr.png" alt="" width="382" height="266"></td>	
		<td align="center"><img src="Homology_files/protectingSample.png" alt="" width="86" height="70"></td>	
		<td align="center"><img src="Homology_files/protectingEnum.jpg" alt="" width="356" height="526"></td>	
	</tr>
</table>
<p>
</p>

<h2><a type="anchor" name="homology_properties"></a>Properties of Homology groups</h2>

<p>Some Homology groups have important properties. You might want to specify if the alkyl chain is branched, or
 any deuterium atoms are present. The Homology groups have a special property editing dialog where you can set 
the different properties. They include the followings (with the group to which it may be applied):
<ol>
    <li><b>Deuterium and tritium count:</b> for all Homology groups. The value should be given as e.g. D1-4T3, meaning the group 
contains up to 4 deuterium atoms and 3 tritium atoms.</li>
    <li><b>Text notes:</b> for all Homology groups.
	<br>Text format: Letters denoting different parameters followed by number ranges. <br>
	These entries are separated by commas (,). Specification of attachment atom type is also possible.<br>
	Possible parameters: 
	<dl>
	<dt>E</dt><dd>Number of double bonds</dd>
	<dt>Y</dt><dd>Number of triple bonds</dd>
	<dt>C</dt><dd>Number of carbon atoms</dd>
	<dt>Hetero atom symbol</dt><dd>Number of occurrences of a particular heteroatom</dd>
	<dt>X</dt><dd>Number of occurrences of heteroatoms not defined otherwise</dd>
	<dt>NR</dt><dd>Number of rings in a ring system</dd>
	<dt>RA</dt><dd>Number of atoms in a ring system</dd>
	<dt>&gt;atomic symbol</dt><dd>Presence of one attachment to the specified atom</dd>
	<dt>&gt&gt;atomic symbol</dt><dd>Presence of more than one attachment to the specified atom</dd>
	</dl>
	Example: N1-3,NR4,E1-2,&gt&gt;C    
    </li>
    <li><b>Branching:</b> for chain Homology groups (BRA for branched, STR for straight chain).</li>
    <li><b>Size:</b> for chains. Chains are marked as low (C1-6. LO), mid (C7-10, MID) or high (C11-, HI) according to the length of the chain.</li>
    <li><b>Saturation:</b> for ring groups. They can be marked as saturated or unsaturated.</li>
    <li><b>Ring type:</b> for ring groups. They are marked as monocyclic (MON) or multicyclic (FU), or can be marked as 'not specified'.</li>
</ol>
Not specifying a property means that there is no restriction on that property.
<p><a href="../sketch/sketch-basic.html#markush-structures">Read the user's guide about Homology groups and property editing in MarvinSketch.</a>

</body>
</html>
