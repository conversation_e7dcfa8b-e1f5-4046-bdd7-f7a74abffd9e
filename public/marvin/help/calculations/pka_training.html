<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
<head>
<link rel="stylesheet" type="text/css" href="../marvinmanuals.css">
<title>Step-by-step instructions for pKa plugin training</title>
<meta name="description" content="Training of the correction library for pKa calculations. Usage via Chemaxon products such as cxcalc, Chemical Terms (Instant JChem), and MarvinSketch"/>
</head>

<body>
    <h1><a name="pkatraining" type="anchor"></a>Training of the correction library for p<i>K</i><sub>a</sub> calculations </h1>
<p>
    If you feel your experimental data could improve the performance of the default p<i>K</i><sub>a</sub> calculator,
    you can take advantage of the supervised p<i>K</i><sub>a</sub> learning method that is built
    into the p<i>K</i><sub>a</sub> calculator. Special structural parts may have an effect on the p<i>K</i><sub>a</sub> 
    values calculated by the built-in method, so your correction library based on experimental data of your compound 
    family helps the p<i>K</i><sub>a</sub> calculator to increase the prediction accuracy. 

    <p>
	<h2>How to improve the accuracy of the p<i>K</i><sub>a</sub> calculation?</h2>
    First, you need to see clearly which ionization center(s) was predicted
    inaccurately by the p<i>K</i><sub>a</sub> calculator.
    You have to collect experimental data for that ionization center(s). The learning
    algorithm is based on linear regression analysis, therefore you need to collect a
    certain amount of experimental p<i>K</i><sub>a</sub> data otherwise the regression analysis will fail.
    There is no strict rule how large pool of data is required to perform a
    reliable p<i>K</i><sub>a</sub> training. If your purpose is to create a local model 
    only for a certain type of chemical environment of the ionization
    center, then it may be enough to collect a few representative structures. A more
    robust model, however, requires as many  diverse structures and
    p<i>K</i><sub>a</sub> values of the ionization center in question as possible.
    <p>
     The first step of the training process is the input of the collected data into
    an sdf file. After that, you have to run the training algorithm which
    creates a correction library from your data. <a href="../calculations/training_libr_place.html">This will be stored on your computer</a><!-- home directory under chemaxon\calculations\training -->.
	You can use this correction library via MarvinSketch, cxcalc, Chemical Terms. 
<p>
	<h2>How to create a training set and generate a correction library</h2>
       
<!--     <p>This guide is a step-by-step manual for creating and using a
        training set that can be applied for the trainable calculations of Calculator
        Plugins (p<i>K</i><sub>a</sub>, log<i>P</i>).
    </p>

    <p>The training of the plugin starts with a file where you collect your
    experimental data (step 1, see below). From this, you generate a file which serves as knowledge
    base for the plugin (step 2), that you have to copy to a directory where the plugin
    picks it for usage (step 3). The plugin calculation will consider this knowledge base
    file if you turn on this option when running the calculation (step 4).

    <p>Please note that logP knowledge base needs a large set of data -
even your molecules are members of a small compound family. If you train your
plugin for benzene derivatives, you might find that values for cyclohexane
derivatives will be miscalculated.-->
 
    <ol>
        <li> <b> Create a training set in sdf file (.sdf) format. </b> 
		<br> This can be easily done by using the graphical user interface of Instant JChem. Your sdf file must
            contain the following fields:
            <ul>
                <li>structure of the molecule</li>
                <li>p<i>K</i><sub>a</sub> value 1 (field name: pKa1)</li>
            <li>ID of the atom which has the pKa1 value (field name: ID1). It can be viewed by checking the Atom number option in MarvinView (menu: <B class="buttonName">View &gt; Misc)</B>.</li></ul>
            Additional fields of p<i>K</i><sub>a</sub> values are optional (recommended for handling multiprotic compunds). For example p<i>K</i><sub>a</sub> value 2 (pKa2),
                ID2, etc.
            </ul>
            Definition of only one p<i>K</i><sub>a</sub> value is enough to apply the
            training data, but more values in case of multiprotic compounds will enhance the
            reliability of the p<i>K</i><sub>a</sub> training.
            <p>
			<b>Example</b>
			
            <br>The picture below shows the details of the training set (<a href="training_files/pKa_trainingset.sdf">pKa_trainingset.sdf</a>). ID1 is the index
            of the atom with the experimental p<i>K</i><sub>a</sub>1 value (ID2 would
            be the index of the second measured p<i>K</i><sub>a</sub> value /p<i>K</i><sub>a</sub>2/, etc.).<br>
            
            <br><img src="training_files/mydata_zoomedmol.PNG" width="665" height="465" alt="mypkadata"/>
      
		</li>
		
        <li><b>Generate the correction library</b>
		   <br>Execute the following command <b>from command line</b>:
		    <pre>cxtrain pka -i [library name] [training file] </pre> 
			<b>Example</b>
            <pre>cxtrain pka -i mypka mydata.sdf</pre> 
		</li>	
  
</ol>
<h2>Usage of the p<i>K</i><sub>a</sub> plugin  with correction library</h2>
<ol><h3>MarvinSketch</h3>
    <li>Select MarvinSketch menu:<B class="buttonName">Tools &gt; Protonation &gt; pKa</B>.</li>
	<li>Set the 'Use correction library' box to activate the training option (see figure below).</li>
	<li>If you have created multiple training sets, choose the most accurate one from the dropdown list below the checkbox.</li><br>
            <td width="200"><img src="training_files/pKa_options_panel.png" width="351" height="381" alt="pKa options panel in Marvin"/></td>
<br> <br>The next figure shows the results with (I) and without (II) applying the correction llbrary.
<p>  <table border="0">
                <tr><td>
			<img src="training_files/trained.png" width="372" height="153" alt="MarvinSketch trained pKa calculation"/>
            </td>
            <td><img src="training_files/no_training.png" width="372" height="151" alt="MarvinSketch not trained pKa calculation"/>
            </td>
        </tr>
		<tr><th>I. p<i>K</i><sub>a</sub> calculation with training data</th>
            <th>II. p<i>K</i><sub>a</sub> calculation without training data</th>
            
        </tr>
    </table>
    <p>      
<h3><code>cxcalc</code></h3>
To apply your corrections for the p<i>K</i><sub>a</sub> calculation use the parameter <code>--correctionlibrary</code> or its short form: <code>-L</code>).
		<pre>cxcalc pKa  <code>--correctionlibrary</code>  [library name] [input file/string]</pre>
		<b>Example</b> 
                      <pre>$ cxcalc pKa <code>--correctionlibrary</code> mypka "CSC1=NC2=C(N1)C=NC(O)=N2"</pre>
         <I>Result</I>
				<PRE style="margin-top: 0px; margin-bottom: 0px;"> id      apKa1   apKa2   bpKa1   bpKa2   atoms</PRE>
				<PRE style="margin-top: 0px; margin-bottom: 0px;"> 1       11.19   16.01   2.34    -2.59   7,11,9,4</PRE><br>
				If you use cxcalc pKa calculation without the correction library, the results will be calculated with the built-in dataset.<br>
	   <b>Example</b> <br>
                      <pre>$ cxcalc pKa "CSC1=NC2=C(N1)C=NC(O)=N2"</pre>
         <I>Result</I>
				<PRE style="margin-top: 0px; margin-bottom: 0px;"> id      apKa1   apKa2   bpKa1   bpKa2   atoms</PRE>
				<PRE style="margin-top: 0px; margin-bottom: 0px;"> 1       8.34   16.01   2.34    -2.59   7,11,9,4</PRE>
        <p>For more options see <a href="../applications/cxcalc-calculations.html#pka">this page</a>.

    
    <h3>Chemical Terms</h3>
  p<i>K</i><sub>a</sub> calculation applying correction library can be performed via Chemical Terms from Evaluator command line or from Instant JChem.
 <h4>Chemical Terms Evaluator</h4>
 The Chemical Terms Evaluator is designed to evaluate mathematical expressions on molecules. To use your correction library, the following expession has to be typed into the command line.<br>
 <pre><code>evaluate -e "pKa('correctionlibrary:[library name]')" "[input file/string]"</code></pre>
 <b>Example</b>
 <pre><code>evaluate -e "pKa('correctionlibrary:mypka')" "CSC1=NC2=C(N1)C=NC(O)=N2"</code></pre>
 <I>Result</I><br>
 <pre><code>;;;-2,59;;;11,19;;2,34;;16,01;</code></pre>
 <p>For more details see <a href="../chemicalterms/EvaluatorFunctions.html#notes">this page</a>.
 </ol>
 <ol>
 <h4>Chemical Terms in Instant JChem</h4>
 <a href="https://www.chemaxon.com/instantjchem/ijc_latest/docs/user/help/htmlfiles/ijcTOC.html">Instant JChem</a> is an out-of-the-box tool that allows scientists to create, manage and analyze chemical structures and their data. You can also apply your p<i>K</i><sub>a</sub> correction library via Chemical Terms in it.   
 <li> Choose the 'New Chemical Terms Field icon' on the panel on the right side.</li>
 <li> Type the chemical term into the window, use the correctionlibrary:[library name] parameter. Do not forget to adjust the Name, the Type and the DB Column Name.</li>
	<br> 
	<b>Example</b> <br>
	The following figure presents the usage of p<i>K</i><sub>a</sub> training in the 'New Chemical terms' window. 
	The expression <code>pKa ('correctionlibrary:mypKa type:acidic','1')</code> defines that the plugin use the correction library named mypKa, and it will calculate the strongest acidic p<i>K</i><sub>a</sub> of the molecule(s).
	<h2></h2><br>
<img src="training_files/instantJChem_ChemicalTerms.png" width="608" height="549" alt="New Chemical Terms window in Instant JChem"/>
<h2></h2>
<br>The part of the results of this calculation is presented on the next figure. You can see the difference between the untrained(column 5., Strongest acidic pKa) and trained (column 6., Trained strongest acidic pKa) p<i>K</i><sub>a</sub> values. 
  	<h2></h2>
<img src="training_files/InstantJchem_results.png" width="516" height="576" alt="New Chemical Terms window in Instant JChem"/>
</ol>

</body>
</html>
