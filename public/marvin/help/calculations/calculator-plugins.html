<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2//EN">
<html>
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8">
<title>Calculator Plugins</title>
<link rel=stylesheet type="text/css" href="../marvinmanuals.css">
</head>
<body>
<h1>Calculator Plugins</h1>

<h2>Introduction</h2>
<p>Calculator Plugins are modules of ChemAxon's Marvin and JChem
cheminformatics platforms which calculate physico-chemical properties from chemical structures. Calculator Plugins currently cover a range of
life science related properties and the development of new plugins is ongoing. 
Available directly from Marvin, Instant JChem, and Reactor applications, command line, API, or via ChemAxon's Chemical Terms language. The calculations can be performed in single or batch mode.</p>
<ul><li><p>The available calculator plugins are located in the <b class="buttonName">Calculations</b>
  menu in the graphical user interface of <b>MarvinSketch</b>, and in the <b class="buttonName">Tools</b> menu in <b><PERSON>View</b>.</p></li>
  <li>
    <p><a href="../applications/calc.html"><code><b>cxcalc</b></code></a> is the command line tool of the Calculator Plugind. Batch processing is available using <code>cxcalc</code> 
      (see the <a href="../applications/cxcalc-calculations.html">list of calculations</a>
      accessible from <code>cxcalc</code>).
  </p></li>
  <li><p>The calculations are used in the <b>Chemical Terms</b> language with which you are
    able to calculate combinations of properties (like Lipinski's rule of 5) in
  an easy way. Learn more about it in the <a href="../chemicalterms/ChemicalTerms.html">Chemical Terms</a> section.</li>
    <li>
    <p>Plugin calculations can be used for filtering results of database searches 
      in <b>JChem Base</b>, <b>Instant JChem</b> and <b>JChem Cartridge</b>.
  </p></li>
  <li>
  <p>Define smart reaction rules using plugin calculations in <b>Reactor</b> (ChemAxon's virtual reaction processing tool).</p></li>
  <li>
    <p>Plugin calculations can be integrated easily into any <b>Java application.</b>  
      For more information on using calculator plugin Java API please see our
      <a href="http://www.chemaxon.com/marvin/help/developer/beans/api/index.html">
      chemaxon.marvin.calculation package</a>.
  </p></li>
  <li>
    <p>Some of the calculators (such as <a href="../calculations/logp_training.html">log<i>P</i></a>, <a href="../calculations/pka_training.html">p<i>K</i><sub>a</sub></a> and <a href="../calculations/predictor_training.html">Predictor </a>) can be trained with the user's data <a href="../calculations/cxtrain.html"><b>via cxtrain</b></a>.</p></li>  
  <li>
    <p><b>Third-party calculations</b> can be integrated easily into <b>MarvinSketch</b> via the <a href="services_menu.html">Services</a> modul of the Grafical User Interface.</b>  
      For more information on integrating third-party calculations see our <a href="../sketch/gui/setting_services.html">Setting Services</a> page.
  </p></li>
  
</ul>

<h3>List of Calculator Plugins</h3>
<ul>
	<li><a href="elemanal.html">Elemental Analysis Plugin</a>
	<li><a href="s2n.html">Naming Plugin</a>
	<li><a href="protonation.html">Protonation</a>
	<ul>
		<li><a href="protonation.html#pka">p<i>K</i><sub>a</sub> Plugin</a>  <a href="../calculations/pka_training.html">[training]</a>
		<li><a href="protonation.html#ms">Major Microspecies Plugin</a>
		<li><a href="protonation.html#isopoint">Isoelectric Point Plugin</a>
	</ul>
    <li><a href="partitioning.html">Partitioning</a> 
        
	<ul>
		<li><a href="partitioning.html#logp">log<i>P</i> Plugin</a>  <a href="../calculations/logp_training.html">[training]</a>
		<li><a href="partitioning.html#logd">log<i>D</i> Plugin</a>
	</ul>
       	<li><a href="chargegroup.html">Charge</a>
	<ul>
		<li><a href="chargegroup.html#charge">Charge Plugin</a>
		<li><a href="chargegroup.html#polarizability">Polarizability Plugin</a>
		<li><a href="chargegroup.html#oen">Orbital Electronegativity Plugin</a>
		<li><a href="chargegroup.html#dipole">Dipole Moment Calculation Plugin</a>
	</ul>
	 <li><a href="nmr.html">NMR</a>
	<ul>
		<li><a href="nmrpredict.html">CNMR Prediction</a>
		<li><a href="nmrpredict.html">HNMR Prediction</a>
		<li><a href="nmrview.html">NMR Spectrum Viewer</a>
	</ul>
	<li><a href="isomers.html">Isomers</a>
	<ul>
		<li><a href="isomers.html#tautomer">Tautomers Plugin</a>
		<li><a href="isomers.html#stereoisomer">Stereoisomers Plugin</a>
	</ul>
	<li><a href="conformation.html">Conformation</a>
	<ul>
		<li><a href="conformation.html#conformer">Conformers Plugin</a>
		<li><a href="conformation.html#moldyn">Molecular Dynamics Plugin</a>
        <li><a href="conformation.html#align">3D Alignment Plugin</a>
	</ul>
	<li><a href="geometrygroup.html">Geometry</a>
	<ul>
		<li><a href="geometrygroup.html#topolanal">Topology Analysis Plugin</a>
		<li><a href="geometrygroup.html#geometry">Geometry Plugin</a>
		<li><a href="geometrygroup.html#TPSA">Polar Surface Area Plugin (2D)</a>
		<li><a href="geometrygroup.html#MSA">Molecular Surface Area Plugin (3D)</a>
		
	</ul>
        <li><a href="markush.html">Markush Enumeration Plugin</a></li>
	<li><a href="predictor.html">Predictor Plugin</a>
	<li><a href="other.html">Other</a>
	<ul>
		<li><a href="other.html#HBDA">Hydrogen Bond Donor-Acceptor Plugin</a>
		<li><a href="other.html#huckel">Huckel Analysis Plugin</a>
		<li><a href="other.html#refractivity">Refractivity Plugin</a>
		<li><a href="other.html#resonance">Resonance Plugin</a>
		<li><a href="other.html#framework">Structural Frameworks Plugin</a>
	</ul>
        <li><a href="Validations.html">Test Results</a></li>
	<li><a href="references.html">References</a></li>
</ul>

<p align="center"><a href="../index.html">Back to Marvin User's Guide</a></p>
<p align="center">Copyright &copy; 1998-2013 <a
	href="http://www.chemaxon.com">ChemAxon Ltd.</a></p>
<p align="center"><a HREF="http://www.chemaxon.com/marvin">http://www.chemaxon.com/marvin</a>
</p>

</body>
</html>
