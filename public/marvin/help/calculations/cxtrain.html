<!DOCTYPE HTML>
<html>
<head>
	<link rel=stylesheet type="text/css" href="../marvinmanuals.css">
	<meta name="description" content="cxtrain a commandline tool of Chemaxon for pKa, logP and general atomic properties training.">
</head>

<body>
<h1> Training of Calculator Plugins via <code>cxtrain</code></h1>
<h3 align="center">Version @MARVINVERSION@</h3>

<h2>Contents</h2>
<ul>
<li><a href="#intro">Introduction</a></li>
<li><a href="#inst">Installation</a></li>
<li><a href="#usage">Usage</a><br>
Options
<ul>
<li><a href="#general">General</a></li>
<li><a href="#pKa">p<i>K</i><sub>a</sub></a></li>
<li><a href="#logP">log<i>P</i></a> </li>
<li><a href="#custom">Custom prediction</a></li>
</ul></li>
<li><a href="#input">Input</a></li>
<li><a href="#output">The place of the training library</a></li>
<li><a href="#examples">Examples</a></li>
<li><a href="../licensedoc/index.html">License Management</a></li>
</ul>

<center><div class="lenia">&nbsp;</div></center>

<h2><a class="anchor" name="intro">Introduction</a></h2>

<p>Calculation of property predictions (such as <a href="../calculations/logPlogD.html">log<i>P</i></a> and <a href="../calculations/pKa.html">p<i>K</i><sub>a</sub></a> ) can be enhanced when experimental data are available for molecules that are similar to the target. 
 Such user-specific information can be incorporated into so-called training libraries, which can be generated via the ChemAxon's commandline tool <code>cxtrain</code>.
It is a part of JChem and Marvin Beans pogram packages. The generated training library, stored on the user's own computer, is later used by the calculator plugins for improving the prediction of properties.
</p>

<center><div class="lenia">&nbsp;</div></center>

<h2><a class="anchor" name="inst">Installation</a></h2>
<p>
<a href="../../download-user.html">Download</a> and launch platform specific 
installer by following the <a href="../applications/install.html">installation instructions</a>.
</p>

<h2><a class="anchor" name="usage">Usage</a></h2>
<p>
<pre>cxtrain &#60;prediction&#62; [options] [input file (training set)] </pre>
<pre>Prediction:
pka                                   train pKa prediction
logp                                  train logP prediction
prediction                            train custom prediction
General options:
cxtrain -h, --help                    this help message
 -i, --training-id&#60;training>          sets the training ID
 -l, --list                           list available training ID's
 -g, --ignore-error                   continue with next molecule on error
pKa options:
 -V, --validation &lt;filepath>          validation results file path
logP options:
 -t, --tag &lt;tag name>                 name of the SDFile tag that stores the experimental logP values
 -a, --add-built-in-training-set      add built-in logP training set
Custom prediction options:
 -t, --tag &lt;tag name>                 name of the SDFile tag that stores the experimental property values </pre>
</p> 
<p>The training is run by calling cxtrain as follows:

<pre>cxtrain &#60;prediction&#62; [options] [input file (training set)] </pre>
where 'prediction' must be chosen from among "pka", "logP" or "prediction" (used for a custom property). There are general options that can be used with each training type and property-specific options as well.</p>

<h5><a class="anchor" name="general">General options</a></h5>
<ul>
<li>Applying the option <code>--training-id&nbsp;(-i)</code>, you can set the ID of your training. Afterwards, this ID will refer the given training during the calculation.
<li>The available training ID's can be listed using option <code>--list&nbsp;(-l)</code>.
<li><code>--ignore-error&nbsp;(-g)</code> skips the molecule on error and continues with the next correct one.
</ul>

<h5><a class="anchor" name="pKa">p<i>K</i><sub>a</sub> specific option</a></h5>
<ul>
<li>--validation &lt;filepath&gt (-V) creates validation data; the file path of the p<i>K</i><sub>a</sub> training validation chart can be defined optionally.</li>
</ul>
<h5><a class="anchor" name="logP">log<i>P</i> specific options</a></h5>
<ul>
<li><code>--add-built-in-training-set (-a)</code> merges your data with the data from built-in logP training set.</br>
<li>Option <code>--tag (-t) </code> defines the name of the SDFile tag that stores the experimental logP values.
</ul>
<h5><a class="anchor" name="custom">Custom prediction option</a></h5>
<ul>
<li>Option <code>--tag (-t) </code> defines the name of the SDFile tag that stores the experimental custom defined values.</li>
</ul>

<h2><a class="anchor" name="input">Input</a></h2>

<p>The input of the software is a file which supports molecular properties (such as 
<a href="../formats/mol-csmol-doc.html" target="blank">SDfile, MDL molfile, Compressed molfile, Compressed SDfile</a>).</p>

<h2><a class="anchor" name="output">The place of the training library</a></h2>
<p>
The generated training library <a href="../calculations/training_libr_place.html"> will be stored on your computer</a> , and it can be used via Marvin, Chemical Terms, Instant JChem or <code>cxcalc</code>.</p>

<center><div class="lenia">&nbsp;</div></center>

<h2><a class="anchor" name="examples">Usage examples</a></h2>

<ol>
<li>
<ul>
<li><b>Training</b><br>
This command trains p<i>K</i><sub>a</sub> calculation, using the datafile pKa_trainingset.sdf and setting training ID to "mypka":
<pre>cxtrain pka -i mypka pKa_trainingset.sdf</pre>
</li>
<li><b>Calculation</b><br>
The following example presents, how this generated training set can be utilized in pKa calcutlations via <code>cxcalc</code> :
<pre>cxcalc pKa <code>--correctionlibrary</code> mypka "CSC1=NC2=C(N1)C=NC(O)=N2"</pre>
</li>
<li><b>Result</b>
				<pre style="margin-top: 0px; margin-bottom: 0px;"> id      apKa1   apKa2   bpKa1   bpKa2   atoms</pre>
				<pre style="margin-top: 0px; margin-bottom: 0px;"> 1       11.19   16.01   2.34    -2.59   7,11,9,4</pre></li>
</ul>
</li>
<li>
<ul>
<li><b>Training</b><br>
Same for log<i>P</i> calculation, using the datafile logP_trainingset.sdf, with the experimental logP values stored in the SDF tag named "LOGP", setting training ID to "mylogp" and including data from the built-in training set:
  <pre>cxtrain logp -t LOGP -i mylogp -a logP_trainingset.sdf</pre>
</li>
<li><b>Calculation</b><br>
To apply your generated Log<i>P</i> training library in calculations; use the parameter <code>--trainingid</code> combine with the parameter <code>--method</code> via <code>cxcalc</code>.
<pre><code>cxcalc logp --method user --trainingid mylogp "CC(C)CCO"</code></pre>
</li>
<li><b>Result</b>
<pre>id      logP
1       1,13</pre>
</li>
</ul>
</li>
<li>
<ul>
<li><b>Training</b><br>
The following command lists available training ID's for logP calculation:
  <pre>cxtrain logp --list</pre>
</li>
</ul>
</li>
<li>
<ul>
<li><b>Training</b><br>
This command trains a custom property calculation, using the datafile pampa_trainingset.sdf, with the experimental values stored in the SDF tag named "PAMPA", setting training ID to "mypampa":
  <pre>cxtrain prediction -t PAMPA -i mypampa pampa_trainingset.sdf</pre>
</li>
</ul>
</li>
</ol>
<p>
See also <a href="../calculations/logp_training.html">log<i>P</i></a>, <a href="../calculations/pKa_training.html">p<i>K</i><sub>a</sub></a> and <a href="../calculations/predictor_training.html">Predictor training pages.
</p>

</body>
</html>