<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2//EN">
<html>
<head>
<meta HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
<title>Conformation</title>
<link rel=stylesheet type="text/css" href="../marvinmanuals.css">
</head>
<body>
<h1>Conformation</h1>


<h2><a class="anchor" NAME="conformer">Conformer Plugin</a></h2>
<p>Conformational isomerism is a form of isomerism that describes the phenomenon of molecules with the same structural formula having different shapes due to rotations about one or more bonds. Different conformations might have different energies, can usually interconvert, and are very rarely isolatable. <br>Conformer plugin generates selected number of conformers or
the lowest energy conformer of a molecule. For conformer calculation
Dreiding force field is used. </p>
<p>Different calculation parameters can be set in the <b CLASS="buttonName">Conformers Options</b> panel:</p>
<p>
<table>
	<tr>
		<td><img src="images/conform_panel.png" width="339" height="452"></td>
	</tr>
</table>
</p>
<ul>
<li><b>Display options</b>
	<ul>
		<li><b>Display conformers:</b> conformers are displayed in a MarivnView window.</li> 
		<li><b>Store conformer information in property field:</b> the conformer data are calculated and stored with the structures. This option provides the calculations needed to select a specific conformer when using 3D cleaning (menu item Structure > Clean 3D > Display Stored Conformers). The conformers will only be stored if you select one result and click on "Select".</li>
	</ul> 
</li>
<li>
	<b>Force field:</b> force field used for calculation.</li>
<li>
	<b>Energy unit:</b> giving results in kcal/mol or kJ/mol.</li>
<li>
	<b>Optimization limit:</b> set the optimization to loose, normal, strict very strict (in this order increasing calculation times and precisity).</li>
<li>
	<b>Calculate lowest energy conformer:</b> calculates and displays only the lowest energy conformer structure. When checking this option, max. number if conformers and diversity limit are disabled.</li>
<li>
	<b>Maximum numbers of conformers:</b> limiting the number of calculated structures. </li>
<li>
	<b>Diversity limit:</b> conformers within diversity limit will be considered the same and doubles removed.</li>
<li>
	<b>Timelimit (s):</b> no conformers will be displayed if the calculation is stopped at the time limit set (e.g. there are too many conformers to calculate, the operation is cancelled after the given time had elapsed).</li>

<li>
	<b>Prehydrogenize:</b> if checked, converts all implicit hydrogens to explicit hydrogens without removing them after the calculation. If unchecked, no explicit hydrogens will be added.</li>
<li>
	<b>Hyperfine:</b> inserts more itineration steps in the calculations, gives more precision in results but the needed time becomes longer.</li>
<li>
	<b>Multi-fragment optimization:</b> multi-fragment optimization with MMFF94.</li>
<li>
	<b>Visualize H bonds</b>: marks intramolecular hydrogen bonds in the conformer where it is likely to occur.</li>
</ul>
<p>The results appear in a new window, containing all calculated conformers with their energy indicated:
</p>
<p>
<table cellpadding="4">
	<tr>
		<td><img src="images/conformers.png" width="800" height="433"></td>
	</tr>
</table>
</p>

<h2><a class="anchor" NAME="moldyn">Molecular Dynamics Plugin</a></h2>

<p>The molecular dynamics plugin calculates the configurations of
the system by integrating Newton's laws of motion.</p>

<p>The calculation and the display options can be set in the <b>Molecular Dynamics 
Options</b> panel:</p>

<p>
<table>
	<tr>
		<td><img src="images/mddialog.png" width="247" height="313"></td>
		<td></td>
	</tr>
</table>
</p>


<ul>
	<li><b>Display</b>: display mode
	<ul>		
		<li><b>Animation</b>: trajectory is displayed as an <a
			href="images/mdanim.gif">animation</a>.</li>
		<li><b>Frames</b>: trajectory frames are displayed individually (see above).</li>
	</ul>
	</li>
	<li><b>Force field:</b> force field used for calculation.</li>
	<li><b>Integrator:</b> integrator type used for solving Newton's
	laws of motion.</li>
	<li><b>Simulation steps:</b> number of simulation steps.</li>
	<li><b>Step time (fs):</b> time between simulation steps in femtoseconds.</li>
	<li><b>Initial temperature (K):</b> initial temperature of the system
	in kelvin.</li>
	<li><b>Start time of display (fs):</b> the time of the first simulation
	frame to be displayed in femtoseconds.</li>
	<li><b>Frame interval (fs):</b> time between displayed simulation
	frames in femtoseconds.</li>
</ul>

<p>The result is shown in a new window:</p>
<p>
<table>
	<tr>
		<td><img src="images/md.png" width="800" height="434"></td>
		<td></td>
	</tr>
</table>
</p>
<p>The window is a MarvinView window, with all its funcionalities to reach.</p>

<h2><a class="anchor" name="align">3D Alignment Plugin</a></h2>
<p>3D Alignment overlays drug sized molecules onto each other in the 3D space.

<p>
<p><b>Input</b> can be two or more molecules in 2D or in 3D. If 2D molecules are used their 3D structure is automatically generated by generate3D.
<p>The conformation of the molecules can be treated flexible or the input conformation can be preserved. To preserve the input conformation simply select the molecule.
<p><b>Usage:</b> Molecules to align shoud be placed into the same MarvinSketch canvas by reading multiple molecules from a file. Alternatively, copy &amp; paste or drag molecules from another sketch window.
<p><b>Output</b> is the aligned molecules in 3D. To save the aligned orientation use the popup menu: Click on the molecules with the second mouse button.

<p>Following options can be set in the <b>3D Alignment
Options</b> panel:</p>
<p>
<table>
	<tr>
		<td><img src="images/alignment_panel.png" width="458" height="418"></td>
	</tr>
</table>
</p>

<p><b>Alignment options</b>
</p>
<p>
<ul>
<li><b>Align by extended atom types:</b> Extended atom types are assigned to each atom to enable chemically relevant atomic overlay. During the alignment process the overlap of the atoms of the same type is maximized. Types differentiate atomic number, hybridization state and aromaticity, e.g. aromatic nitrogen atom is not matched against a tertiary amine. These extended atom types correspond to the ones used in Dreiding force field. </li>
<li><b>Align by MCS:</b> The atom-atom pairing is obtained from the 2D maximum common substructure of the molecules. Alignment by extended atom types is applied on the non MCS atoms.</li>
</ul>


<p><b>Detailed options</b>
<ul>
	<li><b>Initial conformation size:</b>  Number of diverse conformations to generate as an input for the alignment. </li>
	<li><b>Accuracy</b>: low, normal, high, very high: If lower selected the calcualtion is faster. The default is normal.</li>	
</ul>
<b>Display in MarvinSpace:</b> the result window is a MarvinSpace 3D viewer. Molecules are visualized in different colors for better distinction of structures.

<p>
<table>
	<tr>
		<td><img src="images/alignment.png" width="722" height="424"></td>
		<td></td>
	</tr>
</table>

<p>The aligned molecules are shown in a MarvinSpace window. Click and drag to rotate.

<h4>An example of usage</h4>

<p>
Suppose you have an SDfile contaning some molecules (called <i>wish.sdf</i>) that you wish to align. This must be converted for the alignment to a single molecule multi-fragment file where each fragment is a molecule from <i>wish.sdf</i>:
<ul>
<li>Create an empty file in MarvinSketch called <i>empty.mol</i>
<li>Type at command prompt: <code>molconvert mol empty.mol -R wish.sdf -o wish_fused.mol</code>
<li>Open wish_fused.mol in MarvinSketch
</ul>
<p>If you know which atoms to overlap use the Reaction arrow tool to connect them. This can improve the alignment. If you have only 3D molecules as input and you select one of them, its original conformation will be preserved during the alignment, while others remain flexible.
<ul><li>Select Tools > Conformation > 3D Alignment
<li>Untick the Display in MarvinSpace option
<li>Alignment process can take a while, around a minute for 4 drugsize molecules
<li>Click on the result window with the right mouse button and select "Save As" from the pop menu.

</body>
</html>
