<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8">
<title>Markush Enumerator Plugin</title>
<link rel="stylesheet" type="text/css" href="../marvinmanuals.css">
</head>

<body>
<h1>Markush Enumerator Plugin</h1>

A Markush structure is a description of a compound class by generic notations,
primarily used in patent claims and the description of combinatorial libraries.
The library of a Markush structure is the total set of specific molecules that
are described by the Markush structure. 
<p>
The Markush enumeration plugin can be used to generate a whole or a subset of
the library of a generic Markush structure. It is also capable of calculating
the total number of specific structures present in a Markush library. The plugin
is accessible from the followings:
<ul>
	<li>Marvin GUI (Structure > Markush Enumeration)</li>
	<li><a href="http://www.chemaxon.com/instantjchem/ijc_latest/docs/user/help/htmlfiles/chemistry_functions/markush_enumeration.html">Instant JChem </a></li>
	<li><a href="http://www.chemaxon.com/jchem/doc/user/markush_viewer.html#nesting">Markush viewer</a></li>
	<li><a href="../applications/calc.html">cxcalc
    command-line program</a> (see <a href="../applications/cxcalc-calculations.html#Enumerations">this
    link</a> for the detailed usage of the plugin in command line)</li>
	<li>via <a href="http://www.chemaxon.com/marvin/help/developer/beans/api/chemaxon/marvin/calculations/MarkushEnumerationPlugin.html">API</a></li>
	<li>Markush search example <a href="http://www.chemaxon.com/jchem/examples/db_search/index.jsp">JSP web application</a></li>
	<li><a href="../chemicalterms/EvaluatorFunctions.html">Chemical Terms functions in JChem</a></li>

    <p>
<table border="0" width="600" cellpadding="1">
        <th>
           <tr> <td><b>Markush features</b></td>
            <td><b>Functionality of the plugin</b></td>
            </tr>  </th>
        <tbody>
            <tr><td valign="top">
                <ul>
                    <li><a href="#feat_rgroup">R-groups</a></li>
                    <li><a href="#feat_atomlist">Atom lists</a></li>
                    <li><a href="#feat_bondlist">Bond lists</a></li>
                    <li><a href="#feat_linknode">Link nodes</a></li>
                    <li><a href="#repeatingunits">Repeating units</a></li>
                    <li><a href="#feat_posvarbonds">Position variation bond</a></li>
                    <li><a href="#feat_homology">Homology groups</a></li>
                    
                </ul>
            </td>
        <td>
                <ul>
                    <li><a href="#function_sequential">Sequential enumeration</a></li>
                    <li><a href="#function_random">Random enumeration</a></li>
                    <li><a href="#function_libsize">Calculate library size</a></li>
                    <li><a href="#function_selected">Selected part enumeration</a></li>
                    <li><a href="#function_valence">Valence filter</a></li>
                    <li><a href="#function_homology">Homology group enumeration</a></li>
                    <li><a href="#function_aligncolor">Scaffold alignment and coloring</a></li>
                    <li><a href="#function_markushcode">Markush code generation</a></li>
                </ul>
            </td>
        </tr>
    </tbody>
</table>



<h3>Markush features</h3>
Currently, the Markush enumeration plugin supports the following features that describe
Markush structures in combinatorial libraries:

<p>
      <ol>
          <li><a name="feat_rgroup" type="anchor"></a><b>R-groups</b>
          <p> R-groups (also referred to as "substituent variation")
          are the most widely known Markush generic features. The variable
          part of the structure is denoted by an R-atom (eg. R1), and the
          definitions are given separately. In each definition the connection
          points must be defined to show where the bonds of the R-atom are
          linked. R-atoms can appear in both rings and chains, and can have one or more than one
          attachments point. The same R-atom can appear multiple
          times, and the different occurrences are handled as different cases.
          (So they can be substituted with different definitions.) R-group
          nesting in R-group definitions is allowed to any depth, but
          without recursion. (An R-group definition cannot use the
          R-atom it is defining, not even through the use of other
          embedding R-atom(s).)
          R-groups up to number R32767 can be used.
          <p> <table cellspacing="0" cellpadding="5" id="grid">
            <tr><th align="center">Example</th>
            <th align="center">Example Markush library member</th></tr>
            <tr><td align="center"><img src="images/markush01.png" width="298" height="144"></td>
              <td align="center"><img src="images/markush05.png" width="107" height="151"></td>
          </tr></table>
		<p> R-group drawing in Marvin Sketch is described in the Marvin Sketch 
		<a href="../sketch/sketch-basic.html#howto-draw.rgroups">User's Guide</a>.
        </li>
        
        <li><a name="feat_atomlist" type="anchor"></a><b>Atom lists</b>
          <p>  Atom lists are another example of substituent variation.
          They define lists of atom types at a given position. There is no
          restriction for the length of the list and for bond count of atom
          lists. Atom list drawing in Marvin Sketch is described 
				<a href="../sketch/sketch-chem.html#atomlist">here</a>.
          <p><table cellspacing="0" cellpadding="5" id="grid">
            <tr><th align="center">Example</th>
            <th align="center">Example Markush library member</th></tr><tr>
              <td align="center"><img src="images/markush02.png" width="139" height="94"></td>
              <td align="center"><img src="images/markush06.png" width="99" height="83"></td>
          </tr></table>
        </li>
        
        <li><a name="feat_bondlist" type="anchor"></a><b>Bond lists</b>
          <p>The following bond lists (generic bond types) are
          supported by the plugin: single or double, any(single, double or
          triple), single or aromatic, double or aromatic.  In Marvin Sketch, bond lists 
				are accessible amongst query bond types in the 
				<a href="../sketch/gui/popups.html#bond.popup">bonds pop-up menu</a>.
          <p>  <table cellspacing="0" cellpadding="5" id="grid">
            <tr><th align="center">Example</th>
            <th align="center">Example Markush library member</th></tr><tr>
              <td align="center"><img src="images/markush03.png" width="118" height="51"></td>
              <td align="center"><img src="images/markush07.png" width="126" height="48"></td>
          </tr></table>
        </li>
        
        <li><a name="feat_linknode" type="anchor"></a><b>Link nodes</b>
          <p>Link nodes are atoms that may repeat between two of
          their designated bonds (called outer bonds, denoted by brackets).
          All other substituents (if exist) repeat together with the atom.
          In the results, the new bonds between the repeating atoms will have
          the bond type of the lower order outer bond.  Link nodes can be drawn in Marvin Sketch using the
				<a href="../sketch/sketch-basic.html#howto.draw.linkAtom">popup menu</a>.
          <p>  <table cellspacing="0" cellpadding="5" id="grid">
            <tr><th align="center">Example</th>
            <th align="center">Example Markush library member</th></tr><tr>
              <td align="center"><img src="images/markush04.png" width="230" height="105"></td>
              <td align="center"><img src="images/markush08.png" width="234" height="101"></td>
          </tr></table>
        </li>

        <li><a class="anchor" name="repeatingunits"></a><b>Repeating units</b>
            <p>Repeating units represent structural parts that can be repeated several times. 
			The repeating unit is enclosed in brackets with one or two head and the same number of tail 
			crossing bonds. (Head crossing bonds go through the left bracket.) Two bond pairs represent 
			ladder type repeating units. The repetition range is a comma-separated list of possible 
			repetitions or repetition intervals, e.g. "1,3,5-9". The repetition pattern specifies the 
			way how the subsequent repeated units are linked together: it can be head-to-head(hh), 
			head-to-tail(ht) or either/unknown(eu) (the either/unknown case is not handled by the 
			search software). In case of ladder type polymers there is also a flip(f) option that 
			defines that the top and bottom crossing bonds are flipped during each connection.
			repeating groups with specified repetition ranges. 
			</p>

	<p> Repeating unit drawing is described in the Marvin Sketch Help 
		<a href="../sketch/sketch-basic.html#repeatingunits">here</a>, 
		and ladder-type bracket drawing is described at the 
		<a href="../sketch/sketch-basic.html#polymers">polymer drawing section</a>.
           
            <p>  
	    <table cellspacing="0" cellpadding="5" class="grid" summary="">
              <tr><th align="center">Example</th>
              <th align="center">Example Markush library member</th></tr>
              <tr>
                <td align="center"><img src="images/markush18c.png" width="156" height="81" alt="">
                </td>
                <td align="center"><img src="images/markush19c.png" width="234" height="103" alt="">
                </td>
              </tr>
            </table>
        </li>
        
        <li><a name="feat_posvarbonds" type="anchor"></a><b>Position variation bonds</b>
          <p>Position variation bonds are bonds attached to variable atoms at one or both end positions.
          The set of variable atoms is drawn as a multicenter group. A position variation bond connects
          one atom from one end position to one atom from the other end position. If the end position is a single atom
          then the bond is attached to this atom, if the end position is a multicenter group then the bond is attached 
          to an arbitrary member of the group.  Position variation drawing in Marvin Sketch is described in 
		<a href="../sketch/sketch-basic.html#markush-structures">Help</a>.

        <!--
<p>If the connectivity of a substituent is not defined exactly, you can use the position variation bond to mark those atoms that
may be attached to that substituent. The set of these variable atoms is drawn as a multicenter group, and their background
color changes to gray (including the bonds between them if the atoms are not disjunct).
A bond of any type starting from the center of this group is connecting the substituent with the multicenter group.
The substituent itself may be an atom, an R-group, a multicenter group but there are
certain limitations.
<p>Position variation drawing in Marvin Sketch is described in
		<a href="http://www.chemaxon.com/marvin/help/sketch/sketch-basic.html#markush-structures">Help</a>.

-->

          <p><b>Limitations:</b> 
          <p>
          <ul>
            <li>Substructure search is not yet prepared to handle the case when
            both end positions are multicenter groups.</li>
            <li>A multicenter end position is not allowed to contain R-atoms.</li>
            <li>A multicenter end position is not allowed to contain another position variation bond
            (ie, position variation bonds cannot be nested).</li>
          </ul>
          <p>
          If a link node is a member of a multicenter group then the group will 
          include the repeated atoms as well in case when the original multicenter group contains no more atoms from the
          link fragment, otherwise the position variation bond is part of the link fragment and repeated together with the
          link node. Although an R-atom is not allowed to take part in position variation, 
          it can be the single-atom end position of a position variation bond, in which case its attachment point 
          is connected to the bond. 
          <p> <table cellspacing="0" cellpadding="5" id="grid">
              <tr><th align="center">Example</th>
              <th align="center">Example Markush library members</th></tr>  <!--<tr>
                <td align="center"><img src="images/markush14.png" width="362" height="416">
                  </td>
                  <td align="center"><img src="images/markush15.png" width="360" height="417">
                  </td>
              </tr>-->
              <tr><td align="center"><img src="images/markush14_1c.png" width="85" height="105">
                  </td>
                  <td align="center"><img src="images/markush14_1b.png" width="111" height="114">
                  </td>
              </tr>
              <tr><td align="center"><img src="images/markush14_2c.png" width="85" height="139">
                  </td>
                  <td align="center"><img src="images/markush14_2d.png" width="82" height="139">
                  </td>
              </tr>
          </table>
        </li>

<li><a name="feat_homology" type="anchor"></a><b>Homology Groups</b>
    <p>Homology groups stand for sets of homologous molecular parts (e.g. functional groups).
    These are represented by pseudo atoms labelled with the common
    chemical annotation of the groups (alkyl, aryl, heterocycle etc.). See the detailed
    definition of these groups <a href="homologygroups.html#definition">in a separate 
	document</a>. The pseudo atoms can be most easily drawn in Marvin Sketch using the 
	Homology Groups 
	<a href="../sketch/sketch-basic.html#templates">template group</a>.</p>

	    <p>  <table cellspacing="0" cellpadding="5" class="grid" summary="">
          <tr><th align="center">Example</th>
          <th align="center">Example Markush library member</th></tr>
          <tr>
            <td align="center"><img src="images/homgr1.png" alt="" width="299" height="92">
            </td>
            <td align="center"><img src="images/homgr2.png" alt="" width="360" height="152">
            </td>
          </tr>
        </table>
    
    <p>There are two major types of homology groups regarding their way of definition:
    <ol>
        <li><b>Built-in groups</b> are defined by specific structural properties of the 
		group. These groups are not enumerated during searching, but the query structure 
		is recognized as fulfilling the requirements for such a structure. The possible 
		number of covered structures is usually infinite, unless the number of atoms is 
		limited. Examples of built-in groups are alkyl, aryl, heterocycle, etc.</li>
        <li><b>User-defined groups</b> are explicitly defined and only the listed
        structures can match on these homology groups. The definition is given in the 
		form of an R-group definition, and any of the generic features discussed in this
		chapter can be used in the definition. These definitions can be customized by 
		the user, and may be context-specific. (E.g. protecting group definition 
		depends on which functional group it is protecting.)</li>
    </ol></p>
    <p><a href="homologygroups.html">Read more</a> about homology groups.</p>
    </li>
      </ol>


<h3>Functionality of the plugin</h3>
The plugin allows the following functionality. Examples are given using Marvin
GUI.
<dl>
<dt><a name="function_sequential" type="anchor"></a><b>Sequential enumeration</b></dt> <dd>Enumerates members of the Markush
    library in a sequential manner (by substituting the first definition of
    the first variable, etc). The results are specific structures. The plugin
    user interface allows the enumeration of all library members, or a specified
    number.
    <p>
    <img src="images/markush09.png" align="middle" width="663" height="853">
</dd>
<dt><a name="function_random" type="anchor"></a><b>Random enumeration</b></dt> <dd>This mode generates a random subset of the
    Markush library to give a quick sampling. It is especially helpful for huge
    libraries, where full enumeration is impossible. In random mode variable
    parts are chosen randomly, and the substitution probability of each
    definition is proportionate with the fragment library size that the given
    definition generates. This ensures the generation of uniform distribution of representatives
    over the Markush library space.
    <p>
    <img src="images/markush10.png" align="middle" width="668" height="844">
</dd>
<dt><a name="function_libsize" type="anchor"></a><b>Calculate library size</b></dt> <dd>The size of the Markush
    library can be calculated by arbitrary precision. On the user interface,
    the exact value is displayed until 20 digits, above that only the magnitude
    is shown (for example, 10^28). The calculated number is the size of the
    whole library, and does not consider the valence check filter. (See below.)
    <p>If the 'Enumerate homology groups' option is enabled, the number of enumerated
        molecules increases accordingly, multiplied by the number of built-in species.
    <p>
    <img src="images/markush11.png" align="middle" width="630 height="800">
</dd>
<dt><a name="function_selected" type="anchor"></a><b>Selected part enumeration</b></dt> <dd>If part of the Markush structure is
    selected, only the generic features in the selected part are considered for
    enumeration/calculation. This allows focusing on a particular area of the
    Markush structure. Enumeration of selected parts only may result in
    generating (more specific) Markush structures.
    <p>
    <img src="images/markush12.png" align="middle" width="763" height="844">
</dd>
<dt><a name="function_valence" type="anchor"></a><b>Valence filter</b></dt> <dd>If the Markush structure is not properly
    (or too generally) formulated, it is possible that it describes structures
    with valence errors. In this case, the valence filter setting is useful to
    filter out the offending result structures. The default value is off (no filtering).
    <p>
    <img src="images/markush13.png" align="middle" width="860" height="720">
</dd>

<dt><a name="function_homology" type="anchor"></a><b>Homology group enumeration</b></dt> <dd>Version 5.2 introduced the
enumeration of homology groups. Homology groups are R-groups, represented as pseudo
atoms - with the names covering a set of R-groups either built-in or user-defined.
For detailed information on homology groups
<a href="homologygroups.html">click here</a>.
<p>
<img src="images/markush20.png" align="middle" width="533" height="680"></dd>

<dt><a name="function_aligncolor" type="anchor"></a><b>Scaffold alignment and coloring</b></dt> <dd>Coloring the scaffold
(part of the structure containg no Markush features) and/or the R-groups in
enumerated structures can help visual recognition of parts of the molecules. 
Differentiation of the structures is aided by alignment of all structures to 
the original scaffold. These options are available in sequential and random enumeration.
    <p>
    <img src="images/markush16.png" align="middle" width="534" height="801">
</dd>

<dt><a name="function_markushcode" type="anchor"></a><b>Markush code generation</b></dt> <dd>A special ID number can be generated
for the library members: every structure gets its own unique tag (molecule property), which can be saved in the
structure file (in .mrv and .sdf formats) named as 'Markush Code'. This ID is visible in
the plugin result window as well. It gives the following information:
<ol>
    <li><b>Ri(n):x</b>  R-group number <i>i</i> (at atom nr. <i>n</i>) is the ligand containing the
        atom numbered <i>x</i> (which is the smallest number in that fragment but not
        neccessarily the attachment point)
        <br>
        <b>Custom reagent codes:</b> instead of atom index numbers, custom
        reagent codes (e.g. company identifiers) can also be used. Add attached
        data to R-group members with name 'reagent'.
        These reagent codes will appear in the enumerated structures both
        in the Markush code and in the generated molecule structures. (See example below)
    </li>
    <li><b>ID tag name</b> the name you specified in the options panel (in this example Test1).
        If a tag with this name is attached to the Markush molecule, its value will be used.</li>
    <li><b>Ln:x</b>  link node on atom nr. <i>n</i> in the variation nr. <i>x</i> (in this
        example 1 or two methylene groups are inserted).</li>
    <li><b>Bn-m:x</b> bond between atoms <i>n</i> and <i>m</i> is nr. <i>x</i> in the bond list (referring to the bond type)</li>
    <li><b>PVn-m:x-y</b>  position variation bond between <i>n</i> and <i>m</i> (multicenter numbered)
        occured between atoms <i>x</i> and <i>y</i></li>

    <li><b>An:x</b>  atom nr. <i>n</i> is nr. <i>x</i> from the atom list</li>
</ol>
    <p>
    <img src="images/markush17.png" align="middle" width="534" height="800"/>

</dd>
</dl>
</body>
</html>
