<!DOCTYPE HTML>
<html lang="en">
<head>
	<meta http-equiv="content-type" content="text/html; charset=utf-8">
	<meta name="description" content="<PERSON><PERSON><PERSON><PERSON>'s apparatus to predict nuclear magnetic resonance (NMR) spectra"/>
	<meta name="keywords" content="NMR">
	<meta name="author" content="<PERSON><PERSON><PERSON>, Viktor<PERSON>">
	<link rel="stylesheet"  type="text/css" href="../marvinmanuals.css" title="NMR">
	<title>Prediction of nuclear magnetic resonance (NMR) spectra</title>
</head>
<body >

<h1>Prediction of nuclear magnetic resonance (NMR) spectra</h1>
<h3 align="center">Version @MARVINVERSION@</h3>
<p>
Fast and accurate prediction of <sup>13</sup>C and <sup>1</sup>H NMR spectra from the molecular structure plays an important
role in structure validation and elucidation of molecules. The NMR predictor application is able to predict NMR spectra for
standard organic molecules containing the most frequent atoms (H, C, N, O, F, Cl, Br, I, P, S, Si, Se, B, Sn, Ge, Te, As).<br>
Chemical shifts are estimated by a mixed HOSE and linear model based on a topological description scheme and are in relation to the chemical shift of tetramethylsilane (<i>&delta;</i>(TMS)=0 ppm).
<sup>13</sup>C and <sup>1</sup>H chemical shift training data were retrieved from the <a href="http://www.nmrshiftdb.org">NMRShift Database</a>. 
Read more about <a href="nmr_model.html">NMR chemical shift model description</a>.
</p>

<p>
<h2>Basic features</h2>
<ul>
	<li>Prediction of <b><sup>13</sup>C</b> and <b><sup>1</sup>H <a href="nmrpredict.html">NMR</a></b> chemical shifts;</li>
	<li><b>Spin-spin couplings</b> are taken into account according to the first order approximation;</li>
	<li><b>H-H, H-F and C-F couplings</b> are considered during NMR spectrum calculation;</li>
	<li><b>Diastereotopic protons</b> are differentiated;</li>
	<li>NMR Spectrum Viewer is able to display NMR spectra in JCAMP-DX format.</li>
</ul>
<h3>The NMR Predictor graphical user interface incorporates the following features:</h3>
<ul>
	<li><b>Export</b> predicted spectrum to <b>molfile</b>;</li>
	<li><b>Export</b> predicted spectrum to JCAMP-DX file and/or <b>import JCAMP-DX</b> (*.jdx) reference spectrum;</li>
	<li>Create <b>PDF file as report</b> of your prediction, containing molecule structure, predicted spectrum, and related tables;</li>
	<li>Detached <b>Copy to clipboard</b> action for all predictor panels and tables is available;</li>
	<li><b>Update</b> molecule from MarvinSketch;</li>
	<li>Toggle between decoupled and <b>coupled NMR spectrum</b>;</li>
	<li><img src="nmr_files/hnmr_icon.png"> Toggle between explicit and <b>implicit hydrogen display</b>;</li>
	<li>Select NMR <b>prediction frequency</b> from a predetermined list;</li>	
	<li><b>Add</b> common organic <b>solvent peaks</b> to predicted spectrum;</li>
	<li><b>Add tautomer peaks</b> to predicted spectrum;</li>
	<li><b>Restore</b> default NMR predictor <b>settings</b>, e.g., prediction frequency, display, and view options;</li>  
	<li>Display <b>realistic</b> or <b>line</b> NMR spectra;</li>
	<li><b>Add</b> atom indices or chemical shift values to signals as <b>spectrum labels</b>;</li>
	<li>Display spectrum scale in <b>ppm</b> or <b>Hz</b> units;</li>
	<li>Show <b>integral curve</b> to assign value to NMR spectrum signals;</li>
	<li>Display <b>legend</b> on spectrum display panel.</li>
	<li>Show local maximum values of reference spectrum;</li>
	<li>Personalize the <b>color management</b> of NMR Predictor;</li> 
	<li><b>Set</b> chart <b>color</b> uniquely;</li>
	<li>When you click on a peak on spectrum display panel or on an atom on molecule preview panel, selection will move to and zoom in on the selected signal;</li>
	<li>Choose multiplet selection mode: <b>individual selection</b> in case of overlapping multiplets is available;</li>
	<li>Use <b>various modes of zoom in</b> on spectrum.</li>
	<li>Find spectrum and molecule structure related information in <b>Atom</b>, <b>Multiplet</b>, and <b>Coupling</b> tables.</li>
	<li>Show atom indices on molecule structure corresponding to the different multiplets;</li>
</ul>
<p>
Atoms of the input molecule and multiplets of the NMR spectrum are linked together: upon selection of an atom the corresponding multiplet is highlighted and vice versa.<br>
A single NMR prediction is allowed to contain more molecules.</p>
<p>
NMR predictor is integrated into <b>MarvinSketch</b>'s <b>Calculations</b> menu, and contains the following three components to discover NMR spectra of molecules:
</p>
<p>
<img src="nmr_files/nmr_00.png"/>
</p>
<p>
<ol>
<li><a href="nmrpredict.html"><b>CNMR Prediction</b></a> and</li> 
<li><a href="nmrpredict.html"><b>HNMR Prediction</b></a>;</li>
<li><a href="nmrview.html"><b>NMR Spectrum Viewer</b></a>.</li>
</ol>
</p>
<p>NMR Prediction is accessible via <b>cxcalc</b> as well (<code>cxcalc nmr -h</code>).</p>
<p>To improve our product, please send <NAME_EMAIL>.
</p>
<p><a href="#top">Back to top</a></p>
<center><div class="lenia">&nbsp;</div></center>
<center><font face="helvetica"> Copyright&copy; 2011-2012 <a href="http://www.chemaxon.com">ChemAxon Ltd.</a>
&nbsp;&nbsp;&nbsp;All rights reserved. </font></center>
</body>
</html>
