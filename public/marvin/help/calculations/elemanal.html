<!DOCTYPE HTML>
<html lang="en">
<head>
	<meta http-equiv="content-type" content="text/html; charset=utf-8">
	<title>Elemental Analysis</title>
	<link rel="stylesheet" type="text/css" href="../marvinmanuals.css">
</head>
<body>
<h1>Elemental Analysis Plugin</h1>
<p>Basic molecular values related to the elemental composition of the
molecule are calculated by the Elemental Analysis plugin. </p>
<p>In the <b>Elemental Analysis Options</b> panel you can check different
properties:</P>
<table>
	<tr>
		<td>
			<p><img src="images/elemanal_panel.png" width="290" height="346" alt="elemanalysis panel"/></p>
		</td>
	</tr>
</table>
<ul>
	<li><b>Type</b> 
	<ul>
		<li><b>Mass:</b> average molecular mass calculated from the
		standard atomic weights <a href="#ref1"><sup>1</sup></a>.</li>
		<li><b>Exact mass:</B> monoisotopic mass calculated from the
		weights <a href="#ref2"><sup>2</sup></a> of the most abundant
		natural isotopes of the elements.</LI>
		<li><b>Formula:</b> chemical formula of the molecule according
		to the Hill system <a href="#ref3"><sup>3</sup></a>: the number of
		carbon atoms is indicated first, the number of hydrogen atoms
		next,	and then the number of all other chemical elements
		subsequently, in alphabetical order. Isotopes (like Deuterium and
		Tritium) are not listed separately but counted together (e.g.,
		deuterium and tritium atoms are counted as hydrogens). When the
		formula contains no carbon, all the elements, including hydrogen,
		are listed	alphabetically. If the molecule contains an SRU or 
		Repeating Unit S-group, it will be taken into account and Polymer Formula 
		will be generated. <br>
		<b>Note</b>: For polymer structures, mass, composition, and atom count calculations are 
		not available and will return NaN, N/A, and -1, respectively.
		</li>
		<li><b>Isotope formula:</b> chemical formula of the molecule
		listing isotopes separately according to the Hill system.</li>
		<li><b>Dot-disconnected formula:</b> chemical formula of the
		molecule(s) separating fragment formulas by dots (e.g. salts,
		counterions, solvent molecules etc. are present).</li>
		<li><b>Dot-disconnected isotope formula:</b> chemical formula of
		the molecule separating fragment formulas by dots and listing
		isotopes separately.</li>
		<li><b>Composition:</b> elemental composition given in weight
		percentage (w/w %) calculated from the atomic masses.</li>
		<li><b>Isotope composition:</b> elemental composition listing
		isotopes separately (w/w %).</li>
		<li><b>Atom count:</b> number of all atoms in the molecule.</li>
	</ul>
	</li>
<p>The examples shown below illustrating the difference
between formula types:
<table>
	<tr>
		<td><b>Multifragment Molecule with isotopes</b></td>
		<td><b>SRU Polymer S-group</b></td>
		<td><b>Polymer defined as Repeating units S-group</b></td>
	</tr>
	<tr>
		<td>
			<p><img src="images/formulas.png" alt="graphics3" width="318" height="480"/></p>
		</td>
		<td>
			<p><img src="images/formulas_sru.png" width="309" height="485" alt="Formula of SRU"/></p>
		</td>
		<td>
			<p><img src="images/formulas_repunit.png" width="370" height="485" alt="Formula of Repeating units"/></p>
		</td>
	</tr>
</table>
</p>
</ul>
<p>
<ul>
	<li><b>Use D/T symbols for deuterium/Tritium:</b> if unchecked
	(default), isotopes of hydrogen are displayed in formulas as 2H and
	3H, if checked, D and T symbols are used.</li>
	<li><b>Single fragment mode:</b> if unchecked (default), the
	calculation handles unlinked molecules together (e.g. salt
	molecules), summing up the masses of each component, if checked, the
	results are displayed in a scroll window.</li>
</ul>
</p>
<p>The results are shown in a new window:</p>
<table cellpadding="2" cellspacing="2">
	<tr>
		<td style="border: none; padding: 0cm">
			<p><img src="images/elemanal.png" width="388" height="565" /></p>
		</td>
	</tr>
</table>
<p>The contents of the text field can be copied to the clipboard by
Ctrl+C, the structure field offers a context menu from MarvinView. 
</p>

<h2>References</h2>
<ul>
	<li><a name="ref1"></a>Atom weights:
	M. E. Wieser, &quot;Atomic weights of the elements 2005 (IUPAC
	Technical Report)&quot; Pure Appl. Chem., Vol. 78, No. 11, pp.
	2051-2066, 2006; <a href="http://dx.doi.org/10.1351/pac200678112051">doi</a>
	<li><a name="ref2"></a>Isotope
	weights: G.Audi and A.H.Wapstra, &quot;The 1995 update to the atomic
	mass evaluation&quot; Nuclear Physics A595 vol. 4, pp. 409-480,
	1995; <a href="http://dx.doi.org/10.1016/0375-9474(95)00445-9">doi</a>
	<li><a name="ref3"></a>The Hill system: E. A. Hill, &quot;On A
	System Of Indexing Chemical Literature; Adopted By The
	Classification Division Of The U. S. Patent Office&quot;. J. Am.
	Chem. Soc., 22(8), pp. 478-494, 1900; <a href="http://dx.doi.org/10.1021/ja02046a005">doi</a>
</ul>
</body>
</html>