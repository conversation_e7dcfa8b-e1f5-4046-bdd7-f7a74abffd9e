<!DOCTYPE HTML>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8">
<title>Charge</title>
<link rel="stylesheet" type="text/css" href="../marvinmanuals.css">
</head>

<body>
<h1>Charge</h1>

<h2><a class="anchor" id="charge"></a>Charge Plugin</h2>
<p>The partial charge distribution determines many physico-chemical
properties of a molecule, such as ionization constants, reactivity and
pharmacophore pattern. Use Charge plugin to compute the partial charge
value of each atom. Total charge is calculated from sigma and pi charge
components, and any of these three charge values can be displayed. 
<a href="charge.html">Learn more</a> about how the plugin calculates the
partial charge.</p>
<p>In the <b CLASS="buttonName">Charge Options</b> panel you can set the following:
</p>
<p>
<table>
	<tr>
		<td><img src="images/charge_panel2.png" width="199" height="236"/></td>
	</tr>
</table>
</p>
<ul>
	<li><b>Decimal places:</b> setting the number of decimal places with which the result value is given.</li>
	<li><b>Type:</b> setting type of the calculus: total charge, sigma charge or pi charge components. </li>
	<li><b>Charges of implicit hydrogens:</b> gives you in detail the increments of the charge by the implicit hydrogens. </li>
	<li><b>Take resonant structures:</b> the average of the charge of the resonant srtuctures will be calculated.</li>
	<li><b>Take major microspecies/ at pH:</b> the charge of the major microspecies present at the given pH.</li>
	<li><b>Display in MarvinSpace</b>: the result window opens as 3D MarvinSpace viewer. If unchecked, the results will be shown on a 2D picture. </li>
</ul>
<p>The results are shown in a new window, if more molecules present on the sketching canvas (in MarvinSketch) then all molecules appear in one single field in 2D:</p>
<p>
<table>
	<tr>
		<td><img src="images/charge.png" width="563" height="370"/></td>
	</tr>
</table>
</p>
<p>Charge is expressed in atomic unit [e].The numbers in brackets refer to the charge sums of implicit hydrogen
atoms, and displayed only if the "Increment of Hs" option is switched on 
in the <b>Charge Options</b> panel.</p>
<p>If the Display in MarvinSpace checkbox was checked, the results appear in seperate fields, but operations (zooming, rotating etc.) are linked:
</p>
<p>
<table>
	<tr>
		<td><img src="images/charge_mspace.png" width="733" height="602"/></td>
	</tr>
</table>
</p>
<h2><a class="anchor" id="polarizability"></a>Polarization Plugin</h2>
<p>The electric field generated by partial charges of a molecule
spread through intermolecular cavities and the solvent. The induced partial charge (induced dipole) has a tendency to diminish the external electric field. This phenomenon is called polarizability. The more stable the ionized site is the more
its vicinity is polarizable. This is why atomic polarizability is an
important factor in the determination of p<i>K</i><sub>a</sub> and why
it is considered in our p<i>K</i><sub>a</sub> calculation plugin. Atomic
polarizability is altered by partial charges of atoms. We use two methods to calculate polarizability: one of the calculations
is based on <a href="#ref3">Miller's and Savchik's</a> atomic parameters, while the other method is based on <a href="#ref4">Thole's</a> parameters. </p>
<p>In the <b>Polarizability Options</b> panel you can set the following:
</p>
<p>
<table>
	<tr>
		<td><img src="images/polar_panel.png" width="199" height="240"/></td>
	</tr>
</table>
</p>
<ul>
	<li><b>Decimal places:</b> setting the number of decimal places with which the result value is given.</li>
	<li><b>Type:</b> setting type of the calculus: molecular or atomic polarizability components. </li>
	<li><b>Take 3D geometry (Thole):</b> calculates the polarization tensor values.</li>
	<li><b>Take major microspecies:</b> the polarizability of major microspecies at the given pH is calculated. </li>
</ul>
<p>The result appears in a new window, displaying on each atom its polarizability value (dimension: &#197;<sup>3</sup>) (2D view and 3D view):</p>
<p>
<table cellpadding="4">
	<tr>
		<td><img src="images/polarizability.png" width="259" height="286"/></td>
		<td><img src="images/polarizability_mspace.png" width="537" height="286"/></td>
	</tr>
</table>
</p>
<p>The contents of the text field can be copied to the clipboard by Ctrl+C, the structure field offers a context menu from MarvinView.
</p>

<h2><a class="anchor" id="oen"></a>Orbital Electronegativity Plugin</h2>
<p>Partial charge distribution of the molecule is governed by the
orbital electronegativity of the atoms contained in the molecule.<BR> 
<a href="charge.html#Concept of Orbital Electronegativity">Learn more</a> about how the plugin calculates orbital electronegativity.</p>
<p>In the <b>Orbital Electronegativity Options</b> panel you can set the following:
</p>
<p>
<table>
	<tr>
		<td><img src="images/oen_panel.png" width="199" height="190"/></td>
	</tr>
</table>
</p>
<ul>
	<li><b>Decimal places:</b> setting the number of decimal places with which the result value is given.</li>
	<li><b>Type:</b> setting type of the calculus: sigma charge or pi electronegativity components. </li>
	<li><b>Take resonant structures:</b> the average of the charge of the resonant srtuctures will be calculated.</li>
	<li><b>Take major microspecies:</b> the electronegativity of major microspecies at the given pH is calculated. </li>
</ul>
<p>The result appears in a new window, displaying on each atom (except of hydrogens) its EN value:</p>
<p>
<table>
	<tr>
		<td><img src="images/oen.png" width="388" height="407"/></td>
	</tr>
</table>
The structure field offers a context menu from MarvinView.
</p>
<h2><a class="anchor" id="dipole"></a>Dipole Moment Calculation Plugin</h2>
<p>Dipole moment (<i>&mu;</i>) is the measure of net molecular polarity, and describes 
the charge separation in a molecule, where electron density is shared unequally between atoms. 	
<br>Dipole Moment Calculation presents the overall dipole moment of a molecule as a vector 
expressed in the principal axis frame. 
The dipole moment information is deduced about the molecular geometry and partial charges. 
The unit of the dipole moment is Debye (D). 
</p>
<p>
<img src="images/dipole1.png" width="273" height="368" alt="dipole moment"/>
</p>
<h2>References</h2>
<ul>
	<li><a class="text" name="ref3">Miller, K. J.; Savchik, J. A., <i>J.
	Am. Chem. Soc.</i>, <b>1979</b>, <i>101</i>, 7206-7213; <a href="http://dx.doi.org/10.1021/ja00518a014">doi</a> </a></li>
	<li><a class="text" name="ref4">Jensen, L.; Åstrand, P.-O.; Osted, A.; Kongsted, J.; Mikkelsen, K.V. <i>J.
	Chem. Phys.</i>, <b>2002</b>, <i>116</i>, 4001-4010; <a href="http://dx.doi.org/10.1063/1.1433747">doi</a> </a></li>
</ul>

</body>
</html>