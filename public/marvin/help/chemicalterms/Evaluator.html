<!DOCTYPE HTML>
<HTML>
<HEAD>
<LINK REL ="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css">
<TITLE>Chemical Terms Evaluator</TITLE>
</HEAD>
<BODY>
<center>
<h1>Chemical Terms Evaluator</h1>
<h3>Version @MARVINVERSION@</h3>
</center>

<p><h2>Contents</h2>
<ul>
<li><a href="#intro">Introduction</a>
<li><a href="#inst">Installation</a>
<li><a href="#usage">Usage</a>
<li><a href="#input">Input</a>
<li><a href="#output">Output</a>
<li><a href="#config">Configuration</a>
<li><a href="#examples">Usage Examples</a>
<li><a href="https://www.chemaxon.com/marvin/examples/evaluator/index.html#basic_examples">Working examples</a>
<li><a href="EvaluatorFunctions.html">Chemical Terms Reference Tables</a>
<li><a href="ChemicalTerms.html">Chemical Terms Language Reference</a>
</ul>

<center><div class="lenia">&nbsp;</div></center>

<P><h2><a name="intro"></a>Introduction</h2>

<p>The <i>Chemical Terms Evaluator</i> is designed to evaluate mathematical expressions
on molecules. These expressions usually have a chemical meaning formulated in
ChemAxon's <a href="ChemicalTerms.html"><i>Chemical Terms Language</i></a> using 
<a href="EvaluatorFunctions.html">built-in chemical and general purpose functions</a>.
It is also possible to extend this built-in set of calculations by a
<a href="#config">user-defined configuration</a>.</p>

<p>Apart from evaluating Chemical Terms by the <code>evaluate</code> chemaxon command
line tool, this evaluation mechanism is used for chemical calculations in chemaxon products
where computational and/or search conditions come into the picture, such as
<a href="http://www.chemaxon.com/jchem/doc/user/PMapper.html">pharmacophore feature identification (PMapper)</a>
(note, that pmapper feature definitions use a 
<a href="http://www.chemaxon.com/jchem/doc/user/PMapper.html#pharmacophoredefs">specific syntax</a>),
<a href="http://www.chemaxon.com/jchem/doc/user/reactor.html">reaction definitions (Reactor)</a>,
<a href="http://www.chemaxon.com/jchem/doc/guide/cartridge/index.html">database filters and chemical calculations (JChem Cartridge)</a>.</p>

<p>The heart of the evaluator mechanism is the 
<a href="http://jep.sourceforge.net/" target="_blank">JEP Java Expression Parser</a>.</p>

<p>You may want to look at the <a href="ChemicalTerms.html">complete language reference</a> 
including a description of the <a href="ChemicalTerms.html#syntax">expression syntax</a> and
<a href="ChemicalTerms.html#simpleexamples">some simple examples</a> 
showing how some well-known chemical rules can be formulated in this language.</p>

<p>Evaluator uses <a href="ChemicalTerms.html#molcontext">molecule context</a> to set  
the input molecule, therefore calculations refer to the input molecule by default.
The <a href="ChemicalTerms.html">language reference</a> also includes a set of
<a href="ChemicalTerms.html#molcontextexamples">Evaluator examples</a>.
A set of <a href="https://www.chemaxon.com/marvin/examples/evaluator/index.html#basic_examples">working examples</a>
is available.</p>

<center><div class="lenia">&nbsp;</div></center>

<h2><a class="anchor" name="inst">Installation</a></h2>
<blockquote>
<ul>
<li><a href="../../download-user.html">Download</a> and launch platform specific 
installer by following the <a href="../applications/install.html">installation instructions</a>.
</ul>
<p>
</blockquote>

<h2><a name="usage"></a>Usage</h2>

The command line tool <code>evaluate</code> evaluates a single expression and prints the result
in human readable text format or else outputs the input molecule with the result set as a 
specified SDF tag.

<blockquote>
<font COLOR="#333399"><strong>evaluate</strong></font>
[options]
[input files/strings]
</blockquote>

<h3><a name="options"></a>Options</h3>

<pre>
Options:
  -h, --help                          this help message
  -l, --list-functions                list Chemical Terms functions

Input Options:
  -c, --config &lt;filepath&gt;             configuration XML file
                                      (if omitted then default
                                      configuration is applied)
  -n, --no-input-mol                  expression should be evaluated
                                      without input molecule
  -e, --expr-string &lt;str|filepath&gt;    expression string or file

Output Options:
  -o, --output &lt;filepath&gt;             output file path (default: stdout)
  -g, --ignore-error                  continue with next molecule on error
  -v, --verbose                       verbose output
  -C, --clean &lt;dim[:opts]&gt;            clean output molecules (dim: 2 or 3)
                                      with options
                                      (default: t2000 - time limit: 2 sec)
           (see http://www.chemaxon.com/marvin/help/sci/cleanoptions.html)
  -f, --format &lt;format&gt;               output format if result is molecule
                                      (default: smiles or smarts)
                                      (ignores the output options below)
  -x, --extract &lt;format&gt;              extract mode: write exactly those
                                      molecules in the specified format that
                                      satisfy the input boolean expression
                                      (excludes other output options)
  -p, --precision &lt;precision&gt;         max. number of fractional digits
                                      in the output (default: 2)
  -S, --sdf-output                    SDF output (otherwise text output)
  -t, --tag                           name of the SDFile tag to store the
                                      evaluation result (default: CALC)
  -i, --include-expr                  output expression string
</pre>

<p>The input molecule file can contain more than one molecule, 
in this case the expression evaluation is performed for all input molecules one-by-one.</p>
<p>The command line parameter <code>--config</code> specifies the filename of the
<a href="#config">configuration file</a>. If this parameter is not specified, then the 
<a href="#builtin">default configuration</a> is used.</p>
<p>If the command line parameter <code>--no-input-mol</code> is specified then the expression is evaluated
without input molecule.</p>
<p>The command line parameter <code>--expr-string</code> specifies the expression string if it is
given on the command line or the file path containing the expression string.</p>
<p>The command line parameter <code>--format</code> specifies the output molecule format in case
when the output is a molecule or a molecule array. The default format is SMILES / SMARTS.
If this option is used then all other output options except for <code>--output</code>, 
<code>--ignore-error</code> and <code>--verbose</code> are ignored.</p>
<p>If the command line parameter <code>--clean</code> is specified then result molecules as well as
SDF output is cleaned in the given dimension.</p>
<p>If the command line parameter <code>--extract</code> is specified then the input expression is
used as a molecule filter: for each input molecule it is evaluated as a boolean condition and the 
program filters the molecules that satisfy this condition, that is, for which the expression 
evaluation result is <code>true</code>. These molecules are written as output in the specified format.
If this option is used then all other output options except for <code>--output</code>, 
<code>--ignore-error</code> and <code>--verbose</code> are ignored.</p>
<p>The command line parameter <code>--precision</code> specifies the maximum number of fractional
digits to be displayed in the output.</p>
<p>If the command line parameter <code>--sdf-output</code> is specified then input molecules are
written to the output in SDF format with evaluation result set as an SDF tag. 
The command line parameter <code>--tag</code> specifies this SDF tag.</p>
<p>If the command line parameter <code>--include-expr</code> is specified then the evaluation result
is preceeded by the expression string itself in the output.</p>
<p>If the command line parameter <code>--ignore-error</code> is specified, then import/export errors 
will not stop the processing but the error is written to the console and the molecule is skipped.
By default, the program exits in case of molecule import/export errors.</p> 

<h2><a name="input"></a>Input</h2>

<p>The software may take molecules from a text file. 
Most molecular file formats are accepted 
(<a href="../formats/mol-csmol-doc.html"target="blank">MDL molfile,
Compressed molfile, SDfile, Compressed SDfile</a>,
<a href="http://www.daylight.com/dayhtml/smiles/" target="blank">SMILES</a>,
<a href="../formats/formats.html" target="blank">etc.</a>).


<p>If no input file name is given in the command line the standard input is read.</p>

<h2><a name="output"></a>Output</h2>

<p>If no output file name is given, results are written to the standard output.</p>

<p>If the <code>--sdf-output</code> command line parameter is specified, 
the output format is SDF and the evaluation result is written to an SDF tag 
(default tag: CALC). Otherwise only the evaluation result is written to the
output in simple text format.</p>

<center><div class="lenia">&nbsp;</div></center>

<P><h2><a name="config"></a>Configuration</h2>

<p>The configuration file is an XML file containing some/all of the following
optional subsections:</p>
<ol>
<li><a href="#paramsdef">Evaluator parameters</a>: this section specifies
general evaluator parameters, currently <a href="#cached">cache-mode</a> can be set here
<li><a href="#plugindef">Plugin definitions</a>: this section describes the plugins 
and their parameters that can be referenced from the expression
(these override the <a href="#builtin">default plugin definitions</a>)
<li><a href="#functiondef">Function definitions</a>: this section describes the predefined
and user-defined functions that can be referenced from the expression
(these override the <a href="#builtin">default function definitions</a>)
<li><a href="#matchdef">Matching conditions</a>: this section specifies the reference ID 
of the substructure matching function and its search options in case when they are different 
from the default substructure search settings
(these override the <a href="#builtin">default matching condition</a> )
</ol>

<h3><a name="paramsdef"></a>Evaluator Parameters</h3>

<p>The evaluator parameter section currently sets the <a name="cached"></a><i>cache-mode</i> attribute:
if set to "true" then <a href="#matchdef">matching condition</a> and 
<a href="#plugindef">plugin calculation</a> results are cached in the molecule object and
reused instead of performing the same structure search or chemical calculation repeatedly.
The default is "false", since typically a Chemical Terms evaluation does not contain multiple
references to the same matching condition or calculation and the caching procedure by itself also
has some overhead.</p>

<P><b>Example:</b>

<pre>
&lt;Params <a name="cache"></a>Cached="true"/&gt;
</pre>

<h3><a name="plugindef"></a>Plugin Definitions</h3>

<p>The plugin declarations enables different structure based chemical calculations (e.g. p<i>K</i><sub>a</sub>,
log<i>P</i>, log<i>D</i>) to be referenced in the expression strings.</p>

<P><b>Declaration</b>

<p>The plugin definition section contains the following data for each plugin reference 
that is to be used in the expressions:
<ol>
<li><a href="#pluginname">the plugin name</a> which the plugin is referenced by in the expression;
<li><a href="#pluginjar">the plugin JAR</a> relative to the <code>marvin/plugins</code> directory 
(<code>marvin</code> refers to Marvin istallation directory), 
where the plugin class should be loaded from (optional, loaded from the usual CLASSPATH if omitted);
<li><a href="#pluginclass">the plugin java class</a> which wraps the plugin calculation into a 
prescribed frame;
<li><a href="#pluginparams">the plugin parameters</a> as parameter name-value pairs - this section
is optional: if omitted, the default plugin parameters are used.
</ol>

<p>The set of possible plugin parameters and a short description for each plugin can be seen 
with the help of the <a href="../applications/calc.html">cxcalc</a> program:</p>
<pre>
cxcalc &lt;plugin&gt; -h
</pre>
<p>where <i>plugin</i> is the plugin ID in the 
<a href="../applications/calc.html#config">cxcalc configuration file</a>.
The parameter names used by the Evaluator are the long command line parameter names, without the 
starting '--' double dashes. For example, take p<i>K</i><sub>a</sub>, type:</p>
<pre>
cxcalc pka -h
</pre>
<p>which prints out the following help text:</p>
<pre>
Calculator plugin: pka.
pKa calculation.
 
Usage:
  cxcalc [general options] [input files] pka
[pka options] [input files]
 
pka options:
  -h, --help       this help message
  -p, --precision  &lt;floating point precision as number of
                   fractional digits: 0-8 or inf&gt; default: 2
  -t, --type       [pKa|acidic|basic] (default: pKa)
  -m, --mode       [macro|micro] (default: macro)
  -n, --ions       max number of ionizable atoms to be considered (default: 8)
  -i, --min        min basic pKa (default: -10)
  -x, --max        max acidic pKa (default: 20)
  -a, --na         number of acidic pKa values displayed (default: 2)
  -b, --nb         number of basic pKa values displayed (default: 2)
</pre>
<p>The <code>help</code>, <code>precision</code>, <code>na</code> and <code>nb</code>
parameters refer to display options, therefore these are not used by the Evaluator.
Thus the parameter set for the p<i>K</i><sub>a</sub> calculation in our case is: 
<pre>
type, mode, ions, min, max.
</pre>


<p>The same plugin can be used with different parameter settings if the XML configuration has more than one
<a href="#plugintag"><code>&lt;Plugin&gt;</code></a> section with the same 
<a href="#pluginclass">java class</a> but different <a href="#pluginname">plugin names</a> 
used to reference the plugins with each of the different 
<a href="#pluginparams">parameter sections</a>. In the following example the 
<a href="#pka1"><code>pKa1</code></a> name references p<i>K</i><sub>a</sub> calculation with minimal 
basic p<i>K</i><sub>a</sub> value
<code>-3</code> and maximal acidic p<i>K</i><sub>a</sub> value <code>10</code> while the 
<a href="#pka2"><code>pKa2</code></a> name references p<i>K</i><sub>a</sub> calculation with 
minimal basic p<i>K</i><sub>a</sub> value
<code>-20</code> and maximal acidic p<i>K</i><sub>a</sub> value <code>30</code>. 
Different functions of a calculator plugin can be referenced by different
IDs. In the example below, the "mass" result type of the <code>ElemetalAnalyser</code>
plugin is referenced by the <a href="#mass">mass</a> name, while the "exactmass" result type of
the same plugin is referred by the <a href="#exactmass">exactmass</a> name.</p>

<P><a name="pluginconfig"></a><b>Example:</b>

<pre>
<a name="pluginstag"></a>&lt;Plugins&gt;
    <a name="plugintag"></a>&lt;Plugin <a name="pluginname"></a>ID="charge" 
        <a name="pluginclass"></a>Class="chemaxon.marvin.calculations.ChargePlugin"
        <a name="pluginjar"></a>JAR="ChargePlugin.jar"/&gt;
    <a name="ioncharge"></a>&lt;Plugin ID="ioncharge" Class="chemaxon.marvin.calculations.IonChargePlugin"&gt;
	<a name="pluginparams"></a>&lt;Param Name="pH" Value="3.6"/&gt;
        &lt;Param Name="max-ions" Value="6"/&gt;
	<a name="minpercent"></a>&lt;Param Name="min-percent" Value="5"/&gt;
	&lt;Param Name="charge-type" Value="accumulated"/&gt;
    &lt;/Plugin&gt;
    &lt;Plugin ID="microspecies" Class="chemaxon.marvin.calculations.MajorMicrospeciesPlugin"/&gt;
    &lt;Plugin ID="pka" Class="chemaxon.marvin.calculations.pKaPlugin"/&gt;
    &lt;Plugin <a name="pka1"></a>ID="pKa1" 
        Class="chemaxon.marvin.calculations.pKaPlugin"&gt;
	&lt;Param Name="min" Value="-3"/&gt;
	&lt;Param Name="max" Value="10"/&gt;
    &lt;/Plugin&gt;
    &lt;Plugin <a name="pka2"></a>ID="pKa2" 
        Class="chemaxon.marvin.calculations.pKaPlugin"&gt;
	&lt;Param Name="min" Value="-20"/&gt;
	&lt;Param Name="max" Value="30"/&gt;
    &lt;/Plugin&gt;
    &lt;Plugin ID="logp" 
        Class="chemaxon.marvin.calculations.logPPlugin"&gt;
        <a name="plugintype"></a>&lt;Param Name="type" Value="logPMicro"/&gt;
    &lt;/Plugin&gt;
    &lt;Plugin <a name="mass"></a>ID="mass" 
        Class="chemaxon.marvin.calculations.ElementalAnalyserPlugin"&gt;
	&lt;Param Name="type" Value="mass"/&gt;
    &lt;/Plugin&gt;
    &lt;Plugin <a name="exactmass"></a>ID="exactmass" 
        Class="chemaxon.marvin.calculations.ElementalAnalyserPlugin"&gt;
	&lt;Param Name="type" Value="exactmass"/&gt;
    &lt;/Plugin&gt;
    &lt;Plugin ID="logp" Class="chemaxon.marvin.calculations.logPPlugin"/&gt;
    &lt;Plugin ID="logd" Class="chemaxon.marvin.calculations.logDPlugin"/&gt;
    &lt;Plugin ID="acc" Class="chemaxon.marvin.calculations.HBDAPlugin"&gt;
	&lt;Param Name="type" Value="acc"/&gt;
    &lt;/Plugin&gt;
    &lt;Plugin ID="don" Class="chemaxon.marvin.calculations.HBDAPlugin"&gt;
	&lt;Param Name="type" Value="don"/&gt;
    &lt;/Plugin&gt;
    &lt;Plugin ID="acceptorcount" Class="chemaxon.marvin.calculations.HBDAPlugin"&gt;
	&lt;Param Name="type" Value="acceptorcount"/&gt;
    &lt;/Plugin&gt;
    &lt;Plugin ID="donorcount" Class="chemaxon.marvin.calculations.HBDAPlugin"&gt;
	&lt;Param Name="type" Value="donorcount"/&gt;
    &lt;/Plugin&gt;
&lt;/Plugins&gt;
</pre>


<h3><a name="functiondef"></a>Function Definitions</h3>

<p>The expression strings can also include references to predefined functions. 
These functions are implemented by java classes that have to implement the 
<code>org.nfunk.jep.function.PostfixMathCommandI</code> interface. See the 
<a href="http://jep.sourceforge.net/doc/javadoc/index.html" target="_blank">JEP API Documentation</a> for details.</p>

<P><b>Declaration</b>

<p>The function definition section contains the user-defined function implementation java classes
accessible from the expressions. Each class is given an <a href="#functionid"><code>ID</code></a>: 
this is the name that the function is referenced by from the expression. The 
<a href="#functionclass"><code>Class</code></a> attribute specifies the java class that implements
the function. A predefined function may have preset parameters in a similar fashion as in the 
<a href="#pluginstag">Plugin declaration section</a>. Currently only the 
<a href="#fatomprop">atomic property query</a> function applies this for presetting the name of the
atomic property to be queried.</p>

<P><b>Example:</b>

<pre>
    <a name="functions"></a>&lt;Functions&gt;
        &lt;Function <a name="arrayid"></a>ID="array" Class="chemaxon.jep.function.IntArray"/&gt;
        &lt;Function <a name="functionid"></a>ID="min" <a name="functionclass"></a>Class="chemaxon.jep.function.Min"/&gt;
	&lt;Function ID="max" Class="chemaxon.jep.function.Max"/&gt;
	&lt;Function ID="count" Class="chemaxon.jep.function.Count"/&gt;
	&lt;Function ID="sum" Class="chemaxon.jep.function.Sum"/&gt;
	&lt;Function ID="sortasc" Class="chemaxon.jep.function.SortAsc"/&gt;
	&lt;Function ID="sortdesc" Class="chemaxon.jep.function.SortDesc"/&gt;
	<a name="fin"></a>&lt;Function ID="in" Class="chemaxon.jep.function.In"/&gt;
	&lt;Function ID="eval" Class="chemaxon.jep.function.AtomEvaluatorFunction"/&gt;
	&lt;Function ID="filter" Class="chemaxon.jep.function.Filter"/&gt;
	&lt;Function ID="minatom" Class="chemaxon.jep.function.MinAtom"/&gt;
	&lt;Function ID="maxatom" Class="chemaxon.jep.function.MaxAtom"/&gt;
	&lt;Function ID="minvalue" Class="chemaxon.jep.function.MinValue"/&gt;
	&lt;Function ID="maxvalue" Class="chemaxon.jep.function.MaxValue"/&gt;
	<a name="fatomprop"></a>&lt;Function ID="atomprop" Class="chemaxon.jep.function.AtomProperties"/&gt;
	<a name="fhcount"></a>&lt;Function ID="hcount" Class="chemaxon.jep.function.AtomProperties"&gt;
	    <a name="fparam"></a>&lt;Param Name="property" Value="hcount"/&gt;
	&lt;/Function&gt;
	&lt;Function ID="connections" Class="chemaxon.jep.function.AtomProperties"&gt;
	    &lt;Param Name="property" Value="connections"/&gt;
	&lt;/Function&gt;
	<a name="fvalence"></a>&lt;Function ID="valence" Class="chemaxon.jep.function.AtomProperties"&gt;
	    &lt;Param Name="property" Value="valence"/&gt;
	&lt;/Function&gt;
	&lt;Function ID="atno" Class="chemaxon.jep.function.AtomProperties"&gt;
	    &lt;Param Name="property" Value="atno"/&gt;
	&lt;/Function&gt;
	&lt;Function ID="map" Class="chemaxon.jep.function.AtomProperties"&gt;
	    &lt;Param Name="property" Value="map"/&gt;
	&lt;/Function&gt;
	&lt;Function ID="arom" Class="chemaxon.jep.function.AtomProperties"&gt;
	    &lt;Param Name="property" Value="arom"/&gt;
	&lt;/Function&gt;
    &lt;/Functions&gt;
</pre>

<h3><a name="matchdef"></a>Matching Conditions</h3>

<p>The matching condition declaration enables the <code>Match</code> function to be used
in expression strings. This function performs substructure search and optionally checks for 
atom matching.</p>

<P><b>Declaration</b>

<p>The declaration gives a reference ID to the function, should contain a <a href="#matchclass"><code>Class</code></a> 
attribute which specifies the java class that implements the function, and can specify the 
<a href="#searchattr">search attributes</a> in case when they differ from the default settings. 
Specifying search attributes is optional, if omitted then the default values are used. For a detailed description of 
the search options see the <a href="http://www.chemaxon.com/jchem/doc/user/queryindex.html">JChem Query Guide</a>.</p> 

<p align="center">
<a name="searchattributes"></a><table border="0" cellspacing="0" cellpadding="5" id="grid" align="center">
<caption><small><b>Search attributes that can be set in the 
<a href="#searchattr"><code>Search</code></a> section.
</b></small></caption>
<tr><th>Attribute</th><th>Range</th><th>Default Value</th></tr>
<tr><td>StereoSearch</td><td><code>true/false</code></td><td><code>true</code></td></tr>
<tr><td>DoubleBondStereoMatchingMode</td><td><code>none/marked/all</code></td><td><code>marked</code></td></tr>
<tr><td>SubgraphSearch</td><td><code>true/false</code></td><td><code>true</code></td></tr>
<tr><td>ExactAtomMatching</td><td><code>true/false</code></td><td><code>false</code></td></tr>
<tr><td>ExactStereoMatching</td><td><code>true/false</code></td><td><code>false</code></td></tr>
<tr><td>OrderSensitiveSearch</td><td><code>true/false</code></td><td><code>false</code></td></tr>
</table>

<P><b>Example:</b>

<pre>
&lt;Matching <a name="matchid"></a>ID="match" <a name="matchclass"></a>Class="chemaxon.jep.function.Match"&gt;
    <a name="searchattr"></a>&lt;Search DoubleBondStereoMatchingMode="all" OrderSensitiveSearch="true"/&gt;
&lt;/Matching&gt;
</pre>

<p>A detailed description of the usage of the match function in expression strings is given below.
A table of <a href="EvaluatorFunctions.html#matchdesc">match function descriptions</a> with
<a href="EvaluatorFunctions.html#matchex">examples</a> is also available as a short reference.</p>


<h3><a name="builtin"></a>Default function definitions, plugin definitions and matching conditions</h3>

<p>Default plugin and function definitions as well as the default matching condition are read from the 
<a href="Evaluator_files/evaluator.xml.txt">built-in <code>evaluator.xml</code> file</a> located under the 
<code>chemaxon/jep</code> directory in <code>marvinbeans.jar / jchem.jar</code> provided by ChemAxon.
<a href="#plugindef">Plugins</a>, <a href="#functiondef">functions</a> and <a href="#matchdef">matching conditions</a> 
defined by the user are read from <code>marvin/config/evaluator.xml</code> file (where <code>marvin</code> is the Marvin 
istallation directory; in case of JChem it is the JChem installation directory) and from <code>MARVIN_MAJOR_VERSION/evaluator.xml</code> 
file (where <code>MARVIN_MAJOR_VERSION</code> is the major version of Marvin/JChem, e.g. "5.1") located under the 
<code>.chemaxon</code> (UNIX / Linux) or <code>chemaxon</code> (Windows) subdirectory in the user's home directory. 
The user defined XML configuration elements are added to default configuration, if both exist then user defined configuration 
override the built-in settings.</p>

<center><div class="lenia">&nbsp;</div></center>

<h2><a name="examples"></a>Usage Examples</h2>

<ol>
<li>Calculates the molecule mass for the molecules in the <code>target.sdf</code> file
where the mass calculator plugin is defined in the <code>config.xml</code> configuration file:
<pre>
evaluate -c config.xml -e "mass()" target.sdf
</pre>
</li>
<li>Filters molecules with molecule mass at least <code>200</code>, molecule mass is 
computed according to the <a href="#builtin">default configuration</a>:
<pre>
evaluate -e "mass() >= 200" -x sdf -o heavy.sdf target.sdf
</pre>
</li>
<li>Evaluates the expression in file calc.txt for molecules in target.sdf,
uses the <a href="#builtin">default configuration</a>:
<pre>
evaluate -e calc.txt target.sdf
</pre>
</li>
<li>The same with SDF output into file with results written to the SDF tag RESULT,
preceded by the expression string:
<pre>
evaluate -e calc.txt -S -i -t RESULT -o result.sdf target.sdf
</pre>
</li>
<li>The same but the expression string is given in the <code>expr.txt</code> file:
<pre>
evaluate -e expr.txt -m query.sdf target.sdf
</pre>
</li>
<li>Calculates partial charges for each atom with precision of <code>3</code> fractional digits,
uses the charge calculation defined in <code>config.xml</code>:
<pre>
evaluate -c config.xml -e "charge()" -p 3 target.sdf
</pre>
</li>

<li>The same with SDF file output with charge values written to the CHARGES SDF tag:
<pre>
evaluate -c config.xml -e "charge()" -p 3 -S -t CHARGE -o result.sdf target.sdf
</pre>
</li>

<li>Enumerates atoms <code>1</code> and <code>2</code> of the Markush structure m.mrv,
writes the resulting structures in MRV format:
<pre>
evaluate -e "markushEnumerations('1,2')" m.mrv -f mrv
</pre> 
</li>

<li>Returns <code>3</code> random enumerations of the Markush structure m.mrv,
writes the resulting structures in MRV format, aligns scaffold and stores scaffold/R-group coloring data:
<pre>
evaluate -e "randomMarkushEnumerationsDisplay(3)" m.mrv -f mrv
</pre> 
<p>Note, that the display options (coordinates and attached coloring data) cannot be stored in the default SMILES output format, therefore it is necessary to specify the MRV output format in this case. 
</li>

<li>Searches for structural issues, given as <a href="../structurechecker/">action strings</a>; evaluator returns found explicit hydrogens or SMARTS defined substructures:
<pre>
evaluate -e "check('explicith..substructure:name=nitro check:reactionSmarts=[O:2]=[N:1]=O')" "[H]C1=CC(C)=CC(=C1)N(=O)=O" "[H]C1=CC(C)=CC=C1" "CC1=CC(=CC=C1)N(=O)=O" "CC1=CC=CC=C1"
</pre> 
</li>
</ol>


<center><div class="lenia">&nbsp;</div></center>
<center>
<font size="-2" face="helvetica">
Copyright &copy; 1999-2013 
<a href="http://www.chemaxon.com">ChemAxon Ltd.</a>
&nbsp;&nbsp;&nbsp;All rights reserved.
</font>
</center>

</BODY>
</HTML>
