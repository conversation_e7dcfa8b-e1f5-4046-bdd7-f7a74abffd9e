<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
	"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
	<head>
		<meta http-equiv="content-type" content="text/html;charset=utf-8"/>
		<meta http-equiv="Content-Style-Type" content="text/css" />
		<link rel="stylesheet" type="text/css" href="../marvinmanuals.css"/>
		<title>Chemical Terms Reference Tables</title>
		<style type="text/css">
			a.fakelink {
				color: #6b6f40;
				text-decoration: underline;
				padding-bottom: 1px;
				cursor: pointer;
			}
		</style>
	</head>
	<body>
		<script type="text/javascript">
		<!--
			function toggle(id){
				var e = document.getElementById(id);
				if(e.style.display != 'none'){
					e.style.display = 'none';
				}else{
					e.style.display = '';
				}
			}
			function toggleAll(id, type){
				var divs, i, visible;
				divs = document.getElementsByTagName('div');
				toggle(id);
				visible = document.getElementById(id).style.display;
				for(i in divs){
					if(divs[i].id.substring(divs[i].id.length-type.length) == type){
						document.getElementById(divs[i].id).style.display = visible;
					}
				}
			}
		//-->
		</script>
		<h1 style="text-align: center;">Chemical Terms Reference Tables</h1>
		<h3 style="text-align: center;">Version @MARVINVERSION@</h3>
		<h2><a name="contents">Contents</a></h2>
		<ul>
			<li><a href="#functions">Chemical Terms functions and examples</a>
				<ul>
					<li><a href="#charge_functions">Charge Functions</a></li>
					<li><a href="#conformation_functions">Conformation Functions</a></li>
					<li><a href="#dissimilarity_functions">Dissimilarity Functions</a></li>
					<li><a href="#elemental_analysis_functions">Elemental Analysis Functions</a></li>
					<li><a href="#general_functions">General Functions</a></li>
					<li><a href="#geometry_functions">Geometry Functions</a></li>
					<li><a href="#hbda_functions">HBDA Functions</a></li>
					<li><a href="#huckel_functions">Huckel Functions</a></li>
					<li><a href="#isomers_functions">Isomers Functions</a></li>
					<li><a href="#markush_functions">Markush Functions</a></li>
					<li><a href="#match_functions">Match Functions</a></li>
					<li><a href="#name_functions">Name Functions</a></li>
					<li><a href="#partitioning_functions">Partitioning Functions</a></li>
					<li><a href="#prediction_functions">Prediction Functions</a></li>
					<li><a href="#protonation_functions">Protonation Functions</a></li>
					<li><a href="#refractivity_functions">Refractivity Functions</a></li>
					<li><a href="#structuralframeworks_functions">StructuralFrameworks Functions</a></li>
					<li><a href="#structure_checker_functions">Structure Checker Functions</a></li>
				</ul>
			</li>
			<li><a href="#notes">Notes</a></li>
			<li><a href="EvaluatorAllFunctions.html">All functions in alphabetic order</a></li>
			<li><a href="http://www.chemaxon.com/marvin/examples/evaluator/index.html">Working examples</a></li>
		</ul>
		<p>&nbsp;</p>
		<div style="margin-left: auto; margin-right: auto; " class="lenia">&nbsp;</div>
		<div><a name="functions"></a></div>
		<h3><a name="charge_functions"></a>Charge Functions</h3>
		<table style="margin-left: auto; margin-right: auto; text-align: left;" border="0" cellspacing="0" cellpadding="5" class="grid">
			<tr>
				<th style="width:5%;">Name</th>
				<th style="width:5%;">License</th>
				<th style="width:25%;">Description</th>
				<th style="width:15%;">Return value</th>
				<th style="width:25%;">Parameters</th>
				<th style="width:25%;">Examples</th>
			</tr>
			<tr>
				<td><a name="atomicpolarizability"/><a name="atomicpolarizabilitydesc"/><a name="atomicpolarizabilityex"/>atomicPolarizability<br/>atomPol<br/>pol<br/>polarizability</td>
				<td>Charge Plugin Group</td>
				<td>calculates atomic polarizability</td>
				<td>the polarizability values</td>
				<td><ul>
<li>the atom index / MolAtom object,
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('atomicpolarizabilitymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('atomicpolarizabilitymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="atomicpolarizabilitymoleculecontextexample">
						<code>atomicPolarizability(0)</code> returns the polarizability of atom <code>0</code> of the input molecule <code>atomicPolarizability(2, "7.4")</code>
		returns the polarizability of atom <code>2</code> of the major microspecies taken at pH <code>7.4</code>
					</div>

					<a class="fakelink" onclick="toggle('atomicpolarizabilityreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('atomicpolarizabilityreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="atomicpolarizabilityreactioncontextexample" style="display:none;">
						<code>atomicPolarizability(ratom(1))</code> returns the polarizability of reactant atom matching map <code>1</code> in the
		reaction equation <code>atomicPolarizability(patom(2), "7.4")</code> returns the polarizability of the product atom matching map <code>2</code>
		of the major microspecies taken at pH <code>7.4</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="averagepolarizability"/><a name="averagepolarizabilitydesc"/><a name="averagepolarizabilityex"/>averagePolarizability<br/>averagePol<br/>avgPol</td>
				<td>Charge Plugin Group</td>
				<td>calculates average molecular polarizability component considering 3D geometry</td>
				<td>the polarizability value</td>
				<td><ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('averagepolarizabilitymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('averagepolarizabilitymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="averagepolarizabilitymoleculecontextexample">
						<code>averagePolarizability()</code> returns the average polarizability component of the input molecule <code>averagePolarizability("7.4")</code>
		returns the average polarizability component of the major microspecies taken at pH <code>7.4</code>
					</div>

					<a class="fakelink" onclick="toggle('averagepolarizabilityreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('averagepolarizabilityreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="averagepolarizabilityreactioncontextexample" style="display:none;">
						<code>averagePolarizability(reactant(1))</code> returns the average polarizability component of the second reactant in the
		reaction equation <code>averagePolarizability(product(0), "7.4")</code> returns the average polarizability component of the first
		product major microspecies taken at pH <code>7.4</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="axxpol"/><a name="axxpoldesc"/><a name="axxpolex"/>axxPol</td>
				<td>Charge Plugin Group</td>
				<td>calculates principal component of polarizability tensor (a(xx), a(yy), a(zz))</td>
				<td>the principal component of polarizability tensor</td>
				<td><ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('axxpolmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('axxpolmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="axxpolmoleculecontextexample">
						<code>axxPol()</code> returns the principal component <code>a(xx)</code> of polarizability tensor of the input molecule
		<code>axxPol("7.4")</code> returns the principal component <code>a(xx)</code> of polarizability tensor of the major microspecies taken
		at pH <code>7.4</code>
					</div>

					<a class="fakelink" onclick="toggle('axxpolreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('axxpolreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="axxpolreactioncontextexample" style="display:none;">
						<code>axxPol(reactant(1))</code> returns the principal component <code>a(xx)</code> of polarizability tensor of the second
		reactant in the reaction equation <code>axxPol(product(0), "7.4")</code> returns the principal component <code>a(xx)</code> of polarizability
		tensor of the first product major microspecies taken at pH <code>7.4</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="ayypol"/><a name="ayypoldesc"/><a name="ayypolex"/>ayyPol</td>
				<td>Charge Plugin Group</td>
				<td>calculates principal component of polarizability tensor (a(xx), a(yy), a(zz))</td>
				<td>the principal component of polarizability tensor</td>
				<td><ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('ayypolmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('ayypolmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="ayypolmoleculecontextexample">
						<code>ayyPol()</code> returns the principal component <code>a(yy)</code> of polarizability tensor of the input molecule
		<code>ayyPol("7.4")</code> returns the principal component <code>a(yy)</code> of polarizability tensor of the major microspecies taken
		at pH <code>7.4</code>
					</div>

					<a class="fakelink" onclick="toggle('ayypolreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('ayypolreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="ayypolreactioncontextexample" style="display:none;">
						<code>ayyPol(reactant(1))</code> returns the principal component <code>a(yy)</code> of polarizability tensor of the second
		reactant in the reaction equation <code>ayyPol(product(0), "7.4")</code> returns the principal component <code>a(yy)</code> of polarizability
		tensor of the first product major microspecies taken at pH <code>7.4</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="azzpol"/><a name="azzpoldesc"/><a name="azzpolex"/>azzPol</td>
				<td>Charge Plugin Group</td>
				<td>calculates principal component of polarizability tensor (a(xx), a(yy), a(zz))</td>
				<td>the principal component of polarizability tensor</td>
				<td><ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('azzpolmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('azzpolmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="azzpolmoleculecontextexample">
						<code>azzPol()</code> returns the principal component <code>a(zz)</code> of polarizability tensor of the input molecule
		<code>azzPol("7.4")</code> returns the principal component <code>a(zz)</code> of polarizability tensor of the major microspecies taken
		at pH <code>7.4</code>
					</div>

					<a class="fakelink" onclick="toggle('azzpolreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('azzpolreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="azzpolreactioncontextexample" style="display:none;">
						<code>azzPol(reactant(1))</code> returns the principal component <code>a(zz)</code> of polarizability tensor of the second
		reactant in the reaction equation <code>azzPol(product(0), "7.4")</code> returns the principal component <code>a(zz)</code> of polarizability
		tensor of the first product major microspecies taken at pH <code>7.4</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="charge"/><a name="chargedesc"/><a name="chargeex"/>charge</td>
				<td>Charge Plugin Group</td>
				<td>calculates partial charges on atoms<br />
		for result types "aromaticsystem" / "aromaticring", calculates the sum of partial charges of the atoms in the aromatic
		system / smallest aromatic ring containing the atom</td>
				<td>the charge values</td>
				<td><ul>
<li>the atom index / MolAtom object,
			</li><li>the result type ("total" (default), "pi", "sigma", "aromaticsystem", "aromaticsystemsigma",
			"aromaticsystempi", "aromaticring", "aromaticringsigma", "aromaticringpi"),
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('chargemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('chargemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="chargemoleculecontextexample">
						<code>charge(0)</code> returns the partial charge on atom <code>0</code> of the input molecule <code>charge(2, "pi", "7.4")</code>
		returns the partial "pi" charge on atom <code>2</code> of the major microspecies taken at pH <code>7.4</code>
					</div>

					<a class="fakelink" onclick="toggle('chargereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('chargereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="chargereactioncontextexample" style="display:none;">
						<code>charge(ratom(1), "aromaticsystem")</code> returns the sum of partial pi charges in the aromatic system
		containing the reactant atom matching map <code>1</code> in the reaction equation <code>charge(patom(2), "aromaticring", "7.4")</code>
		returns the sum of partial charges in the smallest aromatic ring containing the product atom matching map <code>2</code>
		in the major microspecies taken at pH <code>7.4</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="molecularpolarizability"/><a name="molecularpolarizabilitydesc"/><a name="molecularpolarizabilityex"/>molecularPolarizability<br/>molPol</td>
				<td>Charge Plugin Group</td>
				<td>calculates molecular polarizability</td>
				<td>the polarizability value</td>
				<td><ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('molecularpolarizabilitymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('molecularpolarizabilitymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="molecularpolarizabilitymoleculecontextexample">
						<code>molecularPolarizability()</code> returns the molecular polarizability of the input molecule <code>molecularPolarizability("7.4")</code>
		returns the molecular polarizability of the major microspecies taken at pH <code>7.4</code>
					</div>

					<a class="fakelink" onclick="toggle('molecularpolarizabilityreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('molecularpolarizabilityreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="molecularpolarizabilityreactioncontextexample" style="display:none;">
						<code>molecularPolarizability(reactant(1))</code> returns the molecular polarizability of the second reactant in the reaction
		equation <code>molecularPolarizability(product(0), "7.4")</code> returns the molecular polarizability of the first product major
		microspecies taken at pH <code>7.4</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="piorbitalelectronegativity"/><a name="piorbitalelectronegativitydesc"/><a name="piorbitalelectronegativityex"/>piOrbitalElectronegativity<br/>pOEN</td>
				<td>Charge Plugin Group</td>
				<td>calculates atomic pi orbital electronegativity</td>
				<td>the pi orbital electronegativity values</td>
				<td><ul>
<li>the atom index / MolAtom object,
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('piorbitalelectronegativitymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('piorbitalelectronegativitymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="piorbitalelectronegativitymoleculecontextexample">
						<code>piOrbitalElectronegativity(0)</code> returns the pi orbital electronegativity of atom <code>0</code> of
		the input molecule <code>piOrbitalElectronegativity(2, "7.4")</code> returns the pi orbital electronegativity of atom <code>2</code>
		of the major microspecies taken at pH <code>7.4</code>
					</div>

					<a class="fakelink" onclick="toggle('piorbitalelectronegativityreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('piorbitalelectronegativityreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="piorbitalelectronegativityreactioncontextexample" style="display:none;">
						<code>pOEN(ratom(1))</code> returns the pi orbital electronegativity of reactant atom matching map <code>1</code>
		in the reaction equation <code>pOEN(patom(2), "7.4")</code> returns the pi orbital electronegativity of the product atom
		matching map <code>2</code> of the major microspecies taken at pH <code>7.4</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="resonantcharge"/><a name="resonantchargedesc"/><a name="resonantchargeex"/>resonantCharge</td>
				<td>Charge Plugin Group</td>
				<td>calculates partial charges on atoms considering resonance effect<br />
		for result types "aromaticsystem" / "aromaticring", calculates the sum of partial charges of the atoms in the aromatic
		system / smallest aromatic ring containing the atom</td>
				<td>the charge values</td>
				<td><ul>
<li>the atom index / MolAtom object,
			</li><li>the result type ("total" (default), "pi", "sigma", "aromaticsystem", "aromaticsystemsigma",
			"aromaticsystempi", "aromaticring", "aromaticringsigma", "aromaticringpi"),
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('resonantchargemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('resonantchargemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="resonantchargemoleculecontextexample">
						<code>resonantCharge(0)</code> returns the partial charge on atom <code>0</code> of the input molecule considering resonance effect<code>resonantCharge(2, "pi", "7.4")</code>
		returns the partial "pi" charge on atom <code>2</code> of the major microspecies taken at pH <code>7.4</code> considering resonance effect
					</div>

					<a class="fakelink" onclick="toggle('resonantchargereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('resonantchargereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="resonantchargereactioncontextexample" style="display:none;">
						<code>resonantCharge(ratom(1), "aromaticsystem")</code> returns the sum of partial pi charges in the aromatic system
		containing the reactant atom matching map <code>1</code> in the reaction equation considering resonance effect<code>resonantCharge(patom(2), "aromaticring", "7.4")</code>
		returns the sum of partial charges in the smallest aromatic ring containing the product atom matching map <code>2</code>
		in the major microspecies taken at pH <code>7.4</code> considering resonance effect
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="sigmaorbitalelectronegativity"/><a name="sigmaorbitalelectronegativitydesc"/><a name="sigmaorbitalelectronegativityex"/>sigmaOrbitalElectronegativity<br/>sOEN</td>
				<td>Charge Plugin Group</td>
				<td>calculates atomic sigma orbital electronegativity</td>
				<td>the sigma orbital electronegativity values</td>
				<td><ul>
<li>the atom index / MolAtom object,
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('sigmaorbitalelectronegativitymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('sigmaorbitalelectronegativitymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="sigmaorbitalelectronegativitymoleculecontextexample">
						<code>sigmaOrbitalElectronegativity(0)</code> returns the sigma orbital electronegativity of atom <code>0</code>
		of the input molecule <code>sigmaOrbitalElectronegativity(2, "7.4")</code> returns the sigma orbital electronegativity
		of atom <code>2</code> of the major microspecies taken at pH <code>7.4</code>
					</div>

					<a class="fakelink" onclick="toggle('sigmaorbitalelectronegativityreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('sigmaorbitalelectronegativityreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="sigmaorbitalelectronegativityreactioncontextexample" style="display:none;">
						<code>sOEN(ratom(1))</code> returns the sigma orbital electronegativity of reactant atom matching map <code>1</code>
		in the reaction equation <code>sOEN(patom(2), "7.4")</code> returns the sigma orbital electronegativity of the product
		atom matching map <code>2</code> of the major microspecies taken at pH <code>7.4</code>
					</div>
				</td>
			</tr>
		</table>
		<p><a href="#contents">Back to Contents</a></p>
		<p>&nbsp;</p>
		<div style="margin-left: auto; margin-right: auto; " class="lenia">&nbsp;</div>
		<h3><a name="conformation_functions"></a>Conformation Functions</h3>
		<table style="margin-left: auto; margin-right: auto; text-align: left;" border="0" cellspacing="0" cellpadding="5" class="grid">
			<tr>
				<th style="width:5%;">Name</th>
				<th style="width:5%;">License</th>
				<th style="width:25%;">Description</th>
				<th style="width:15%;">Return value</th>
				<th style="width:25%;">Parameters</th>
				<th style="width:25%;">Examples</th>
			</tr>
			<tr>
				<td><a name="conformer"/><a name="conformerdesc"/><a name="conformerex"/>conformer</td>
				<td>Conformation Plugin Group</td>
				<td>calculates a conformer of the molecule</td>
				<td>the conformer</td>
				<td><ul>
<li>the conformer index (0-based)
			</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('conformermoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('conformermoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="conformermoleculecontextexample">
						<code>conformer(0)</code> returns the first conformer of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('conformerreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('conformerreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="conformerreactioncontextexample" style="display:none;">
						<code>conformer(reactant(0), 1)</code> returns the second conformer of the first reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="conformercount"/><a name="conformercountdesc"/><a name="conformercountex"/>conformerCount</td>
				<td>Conformation Plugin Group</td>
				<td>returns the number of calculated conformers</td>
				<td>the number of calculated conformers</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('conformercountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('conformercountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="conformercountmoleculecontextexample">
						<code>conformerCount()</code> returns the number of calculated conformers of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('conformercountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('conformercountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="conformercountreactioncontextexample" style="display:none;">
						<code>conformerCount(reactant(0))</code> returns the number of calculated conformers of the first reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="conformers"/><a name="conformersdesc"/><a name="conformersex"/>conformers</td>
				<td>Conformation Plugin Group</td>
				<td>calculates conformers of the molecule (maximum number of conformers to be calculated can be set, default:
		100)</td>
				<td>the conformer array</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('conformersmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('conformersmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="conformersmoleculecontextexample">
						<code>conformers()</code> returns conformers of the input molecule in an array
					</div>

					<a class="fakelink" onclick="toggle('conformersreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('conformersreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="conformersreactioncontextexample" style="display:none;">
						<code>conformers(reactant(0))</code> returns conformers of the first reactant in an array
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="hasvalidconformer"/><a name="hasvalidconformerdesc"/><a name="hasvalidconformerex"/>hasValidConformer</td>
				<td>Conformation Plugin Group</td>
				<td>returns true if the input molecule exist in 3D space (has a valid conformer)</td>
				<td>true if the input molecule exist in 3D space</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('hasvalidconformermoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('hasvalidconformermoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="hasvalidconformermoleculecontextexample">
						<code>hasValidConformer()</code> returns true if the input molecule exists in 3D space (has a valid conformer)
					</div>

					<a class="fakelink" onclick="toggle('hasvalidconformerreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('hasvalidconformerreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="hasvalidconformerreactioncontextexample" style="display:none;">
						<code>hasValidConformer(reactant(0))</code> returns true the if the first reactant exists in 3D space <code>hasValidConformer(product(1))</code>
		returns true the if the second product exist in 3D space
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="lowestenergyconformer"/><a name="lowestenergyconformerdesc"/><a name="lowestenergyconformerex"/>lowestEnergyConformer<br/>leconformer</td>
				<td>Conformation Plugin Group</td>
				<td>calculates the lowest energy conformer of the molecule</td>
				<td>the lowest energy conformer</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('lowestenergyconformermoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('lowestenergyconformermoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="lowestenergyconformermoleculecontextexample">
						<code>lowestEnergyConformer()</code> returns the lowest energy conformer of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('lowestenergyconformerreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('lowestenergyconformerreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="lowestenergyconformerreactioncontextexample" style="display:none;">
						<code>lowestEnergyConformer(reactant(0))</code> returns the lowest energy conformer of the first reactant
		<code>lowestEnergyConformer(product(1))</code> returns the lowest energy conformer of the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="mmff94optimizedstructure"/><a name="mmff94optimizedstructuredesc"/><a name="mmff94optimizedstructureex"/>mmff94OptimizedStructure</td>
				<td>Conformation Plugin Group</td>
				<td>calculates the MMFF94 optimized lowest energy conformer</td>
				<td>the MMFF94 optimized lowest energy conformer</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('mmff94optimizedstructuremoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('mmff94optimizedstructuremoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="mmff94optimizedstructuremoleculecontextexample">
						<code>mmff94OptimizedStructure()</code> returns the MMFF94 optimized lowest energy conformer of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('mmff94optimizedstructurereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('mmff94optimizedstructurereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="mmff94optimizedstructurereactioncontextexample" style="display:none;">
						<code>mmff94OptimizedStructure(reactant(0))</code> returns the MMFF94 optimized lowest energy conformer of the first reactant
		<code>mmff94OptimizedStructure(product(1))</code> returns the MMFF94 optimized lowest energy conformer of the second product
					</div>
				</td>
			</tr>
		</table>
		<p><a href="#contents">Back to Contents</a></p>
		<p>&nbsp;</p>
		<div style="margin-left: auto; margin-right: auto; " class="lenia">&nbsp;</div>
		<h3><a name="dissimilarity_functions"></a>Dissimilarity Functions</h3>
		<table style="margin-left: auto; margin-right: auto; text-align: left;" border="0" cellspacing="0" cellpadding="5" class="grid">
			<tr>
				<th style="width:5%;">Name</th>
				<th style="width:5%;">License</th>
				<th style="width:25%;">Description</th>
				<th style="width:15%;">Return value</th>
				<th style="width:25%;">Parameters</th>
				<th style="width:25%;">Examples</th>
			</tr>
			<tr>
				<td><a name="dissimilarity"/><a name="dissimilaritydesc"/><a name="dissimilarityex"/>dissimilarity</td>
				<td>-</td>
				<td>computes the dissimilarity value between two molecules
	    <br/><b>Note 1:</b> Dissimilarity function requires JChem.
	    <br/><b>Note 2:</b> The dissimilarity values returned by dissimilarity function and similarity search in JChem database may differ because of the applied fingerprints. Dissimilarity function uses the default options of the chemical 
		fingerprints (length=1024, bonds=7, bits=2) optimized for similarity search. Database 
		similarity search uses fingerprints specified during the generation of the given table, optimized for substructure (and related) search types.</td>
				<td>the dissimilarity value</td>
				<td><a href="#descriptors">descriptor:metric</a> or descriptor (with default metric) (optional, chemical
		fingerprint with Tanimoto metric is taken by default), one or two molecules (if only one is specified then the other
		one is taken from the context)</td>
				<td>
					<a class="fakelink" onclick="toggle('dissimilaritymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('dissimilaritymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="dissimilaritymoleculecontextexample">
						<code>dissimilarity("PF", "c1ccccc1", "C1CCCCC1")</code> returns the dissimilarity value between the benzene
		ring and cyclohexane, computed with pharmacophore fingerprint and its default metric (Tanimoto) <code>dissimilarity("c1ccccc1", "C1CCCCC1")</code>
		returns the dissimilarity value between the benzene ring and cyclohexane, computed with default fingerprint and its
		default metric (chemical fingerprint with Tanimoto)
		<code>dissimilarity("PF:Euclidean", "c1ccccc1")</code> returns the dissimilarity value between the benzene ring
		and the input molecule, computed with pharmacophore fingerprint and Euclidean metric <code>dissimilarity("LogD", "c1ccccc1")</code>
		returns the dissimilarity value between the benzene ring and the input molecule, computed with the LogD descriptor and
		its default <code>AbsDiff</code> metric
					</div>

					<a class="fakelink" onclick="toggle('dissimilarityreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('dissimilarityreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="dissimilarityreactioncontextexample" style="display:none;">
						<code>dissimilarity("CF:Euclidean", "c1ccccc1", reactant(0))</code> returns the dissimilarity value between the
		benzene ring and the first reactant, computed with chemical fingerprint and Euclidean metric <code>dissimilarity(reactant(0), product(0))</code>
		returns the dissimilarity value between the first reactant and the first product, computed with default fingerprint
		and its default metric (chemical fingerprint with Tanimoto)
					</div>
				</td>
			</tr>
		</table>
		<h4><a name="descriptors"></a>Dissimilarity descriptors and metrics</h4>
		<table style="width: 50%; text-align: left;" border="0" cellspacing="0" cellpadding="5" class="grid">
			<tr>
				<th style="width: 80%;">Descriptor</th>
				<th>Metric</th>
			</tr>
			<tr>
				<td>ChemicalFingerprint (or CF)</td>
				<td>Tanimoto (default)<br/>Euclidean</td>
			</tr>
			<tr>
				<td>PharmacophoreFingerprint (or PF)</td>
				<td>Tanimoto (default)<br/>Euclidean</td>
			</tr>
			<tr>
				<td>ECFP</td>
				<td>Tanimoto (default)<br/>Euclidean</td>
			</tr>
			<tr>
				<td>Burden eigenvalue descriptor (or BCUT) (BCUT is a trademark of Tripos, Inc., used with permission)</td>
				<td>Euclidean</td>
			</tr>
			<tr>
				<td>HDon</td>
				<td>Euclidean</td>
			</tr>
			<tr>
				<td>HAcc</td>
				<td>AbsDiff</td>
			</tr>
			<tr>
				<td>Heavy</td>
				<td>AbsDiff</td>
			</tr>
			<tr>
				<td>LogD</td>
				<td>AbsDiff</td>
			</tr>
			<tr>
				<td>LogP</td>
				<td>AbsDiff</td>
			</tr>
			<tr>
				<td>Mass</td>
				<td>AbsDiff</td>
			</tr>
			<tr>
				<td>TPSA</td>
				<td>AbsDiff</td>
			</tr>
		</table>
		<p><a href="#contents">Back to Contents</a></p>
		<p>&nbsp;</p>
		<div style="margin-left: auto; margin-right: auto; " class="lenia">&nbsp;</div>
		<h3><a name="elemental_analysis_functions"></a>Elemental Analysis Functions</h3>
		<table style="margin-left: auto; margin-right: auto; text-align: left;" border="0" cellspacing="0" cellpadding="5" class="grid">
			<tr>
				<th style="width:5%;">Name</th>
				<th style="width:5%;">License</th>
				<th style="width:25%;">Description</th>
				<th style="width:15%;">Return value</th>
				<th style="width:25%;">Parameters</th>
				<th style="width:25%;">Examples</th>
			</tr>
			<tr>
				<td><a name="atomcount"/><a name="atomcountdesc"/><a name="atomcountex"/>atomCount</td>
				<td>-</td>
				<td>calculates the number of atoms (all atoms or specific atoms)</td>
				<td>the atom count</td>
				<td><ul>
<li>atomic number (optional) and mass number (optional) as a single string, separated by "." (e.g. "6" for the
			number of carbon atoms, "6.12" for the number of carbon atoms with mass number <code>12</code>) - if omitted, all
			atoms are counted, if the mass number is set to <code>0</code> then all non-isotope atoms are counted
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('atomcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('atomcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="atomcountmoleculecontextexample">
						<code>atomCount()</code> returns the number of atoms in the input molecule <code>atomCount("6")</code> returns
		the number of carbon atoms in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('atomcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('atomcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="atomcountreactioncontextexample" style="display:none;">
						<code>atomCount(reactant(0), "7")</code> returns the number of nitrogen atoms in the first reactant <code>atomCount(product(1), "7.14")</code>
		returns the number of nitrogen atoms with mass number <code>14</code> in the second product <code>atomCount(product(1), "7.0")</code>
		returns the number of non-isotope nitrogen atoms in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="composition"/><a name="compositiondesc"/><a name="compositionex"/>composition</td>
				<td>-</td>
				<td>returns the composition</td>
				<td>the composition</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('compositionmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('compositionmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="compositionmoleculecontextexample">
						<code>composition()</code> returns the composition of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('compositionreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('compositionreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="compositionreactioncontextexample" style="display:none;">
						N / A (reaction rules are numerical)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="dotdisconnectedformula"/><a name="dotdisconnectedformuladesc"/><a name="dotdisconnectedformulaex"/>dotDisconnectedFormula</td>
				<td>-</td>
				<td>returns the dot-disconnected formula</td>
				<td>the dot-disconnected formula</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('dotdisconnectedformulamoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('dotdisconnectedformulamoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="dotdisconnectedformulamoleculecontextexample">
						<code>dotDisconnectedFormula()</code> returns the dot-disconnected formula of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('dotdisconnectedformulareactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('dotdisconnectedformulareactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="dotdisconnectedformulareactioncontextexample" style="display:none;">
						N / A (reaction rules are numerical)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="dotdisconnectedisotopeformula"/><a name="dotdisconnectedisotopeformuladesc"/><a name="dotdisconnectedisotopeformulaex"/>dotDisconnectedIsotopeFormula</td>
				<td>-</td>
				<td>returns the dot-disconnected isotope formula</td>
				<td>the dot-disconnected isotope formula</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('dotdisconnectedisotopeformulamoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('dotdisconnectedisotopeformulamoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="dotdisconnectedisotopeformulamoleculecontextexample">
						<code>dotDisconnectedIsotopeFormula()</code> returns the dot-disconnected isotope formula of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('dotdisconnectedisotopeformulareactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('dotdisconnectedisotopeformulareactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="dotdisconnectedisotopeformulareactioncontextexample" style="display:none;">
						N / A (reaction rules are numerical)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="elementalanalysis"/><a name="elementalanalysisdesc"/><a name="elementalanalysisex"/>elementalAnalysis</td>
				<td>-</td>
				<td>performs elemental analysis on molecule represented by formula</td>
				<td>the value calculated by the invoked function (e.g., mass)</td>
				<td><ul>
				  <li>molecular formula</li>
				  <li>available functions:
				    <ul>
				      <li>atomCount</li>
				      <li>mass</li>
				      <li>massPrecision</li>
				      <li>exactMass</li>
				      <li>exactMassPrecision</li>
				      <li>formula</li>
				      <li>isotopeFormula</li>
				      <li>dotDisconnectedFormula</li>
				      <li>dotDisconnectedIsotopeFormula</li>
				    </ul>
				  </li>
				</ul></td>
				<td>
					<a class="fakelink" onclick="toggle('elementalanalysismoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('elementalanalysismoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="elementalanalysismoleculecontextexample">
						<code>elementalAnalysis('C12H25O', 'mass')</code> returns the mass of molecule represented by formula
					</div>

					<a class="fakelink" onclick="toggle('elementalanalysisreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('elementalanalysisreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="elementalanalysisreactioncontextexample" style="display:none;">
						N / A
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="exactmass"/><a name="exactmassdesc"/><a name="exactmassex"/>exactMass</td>
				<td>-</td>
				<td>calculates the exact mass of the molecule</td>
				<td>the exact mass</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('exactmassmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('exactmassmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="exactmassmoleculecontextexample">
						<code>exactMass()</code> returns the exact mass of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('exactmassreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('exactmassreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="exactmassreactioncontextexample" style="display:none;">
						<code>exactMass(product(1))</code> returns the exact mass of the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="formula"/><a name="formuladesc"/><a name="formulaex"/>formula</td>
				<td>-</td>
				<td>returns the formula</td>
				<td>the formula</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('formulamoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('formulamoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="formulamoleculecontextexample">
						<code>formula()</code> returns the formula of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('formulareactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('formulareactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="formulareactioncontextexample" style="display:none;">
						N / A (reaction rules are numerical)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="isotopecomposition"/><a name="isotopecompositiondesc"/><a name="isotopecompositionex"/>isotopeComposition</td>
				<td>-</td>
				<td>returns the isotope composition</td>
				<td>the isotope composition</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('isotopecompositionmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('isotopecompositionmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="isotopecompositionmoleculecontextexample">
						<code>isotopeComposition()</code> returns the isotope composition of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('isotopecompositionreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('isotopecompositionreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="isotopecompositionreactioncontextexample" style="display:none;">
						N / A (reaction rules are numerical)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="isotopeformula"/><a name="isotopeformuladesc"/><a name="isotopeformulaex"/>isotopeFormula</td>
				<td>-</td>
				<td>returns the isotope formula</td>
				<td>the isotope formula</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('isotopeformulamoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('isotopeformulamoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="isotopeformulamoleculecontextexample">
						<code>isotopeFormula()</code> returns the isotope formula of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('isotopeformulareactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('isotopeformulareactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="isotopeformulareactioncontextexample" style="display:none;">
						N / A (reaction rules are numerical)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="mass"/><a name="massdesc"/><a name="massex"/>mass</td>
				<td>-</td>
				<td>calculates the molecule mass</td>
				<td>the mass</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('massmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('massmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="massmoleculecontextexample">
						<code>mass()</code> returns the mass of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('massreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('massreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="massreactioncontextexample" style="display:none;">
						<code>mass(reactant(0))</code> returns the mass of the first reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="sortableformula"/><a name="sortableformuladesc"/><a name="sortableformulaex"/>sortableFormula</td>
				<td>-</td>
				<td>returns the fixed digit sortable formula</td>
				<td>fixed digit sortable molecular formula</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('sortableformulamoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('sortableformulamoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="sortableformulamoleculecontextexample">
						<code>sortableFormula()</code> returns the sortable formula of the input molecule; sortableFormula('digits:4')
				returns the 4 digit sortable formula of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('sortableformulareactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('sortableformulareactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="sortableformulareactioncontextexample" style="display:none;">
						N / A (reaction rules are numerical)
					</div>
				</td>
			</tr>
		</table>
		<p><a href="#contents">Back to Contents</a></p>
		<p>&nbsp;</p>
		<div style="margin-left: auto; margin-right: auto; " class="lenia">&nbsp;</div>
		<h3><a name="general_functions"></a>General Functions</h3>
		<table style="margin-left: auto; margin-right: auto; text-align: left;" border="0" cellspacing="0" cellpadding="5" class="grid">
			<tr>
				<th style="width:5%;">Name</th>
				<th style="width:5%;">License</th>
				<th style="width:25%;">Description</th>
				<th style="width:15%;">Return value</th>
				<th style="width:25%;">Parameters</th>
				<th style="width:25%;">Examples</th>
			</tr>
			<tr>
				<td><a name="abs"/><a name="absdesc"/><a name="absex"/>abs</td>
				<td>-</td>
				<td>returns the absolute value of a number</td>
				<td>the absolute value</td>
				<td>integer or real number</td>
				<td>
					<a class="fakelink" onclick="toggle('absgeneralexample')">General</a>
<a class="fakelink" onclick="toggleAll('absgeneralexample','generalexample')">(All)</a><br/>
					<div id="absgeneralexample">
						<code>abs(7)</code>returns <code>7</code><br/><code>abs(-4.9)</code>returns <code>4.9</code>
					</div>

					<a class="fakelink" onclick="toggle('absmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('absmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="absmoleculecontextexample" style="display:none;">
						<code>abs(7)</code>returns <code>7</code><br/><code>abs(-4.9)</code>returns <code>4.9</code>
					</div>

					<a class="fakelink" onclick="toggle('absreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('absreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="absreactioncontextexample" style="display:none;">
						<code>abs(7)</code>returns <code>7</code><br/><code>abs(-4.9)</code>returns <code>4.9</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="arom"/><a name="aromdesc"/><a name="aromex"/>arom</td>
				<td>-</td>
				<td>returns if the atom has an aromatic bond</td>
				<td><code>true</code> if the atom has an aromatic bond, <code>false</code> otherwise</td>
				<td>atom index or MolAtom object</td>
				<td>
					<a class="fakelink" onclick="toggle('arommoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('arommoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="arommoleculecontextexample">
						<code>arom(0)</code> returns if the atom <code>0</code> has an aromatic bond
					</div>

					<a class="fakelink" onclick="toggle('aromreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('aromreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="aromreactioncontextexample" style="display:none;">
						<code>arom(patom(2))</code> returns <code>true</code> if the product atom matching map <code>2</code>
		in the reaction equation has an aromatic bond
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="array"/><a name="arraydesc"/><a name="arrayex"/>array</td>
				<td>-</td>
				<td>constructs an integer array from its arguments</td>
				<td>the integer array</td>
				<td>integers or MolAtom objects</td>
				<td>
					<a class="fakelink" onclick="toggle('arraygeneralexample')">General</a>
<a class="fakelink" onclick="toggleAll('arraygeneralexample','generalexample')">(All)</a><br/>
					<div id="arraygeneralexample">
						<code>array(2, 5, 6, 8)</code>
					</div>

					<a class="fakelink" onclick="toggle('arraymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('arraymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="arraymoleculecontextexample" style="display:none;">
						<code>array(2, 5, 6, 8)</code>
					</div>

					<a class="fakelink" onclick="toggle('arrayreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('arrayreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="arrayreactioncontextexample" style="display:none;">
						<code>array(ratom(2), ratom(5), ratom(6), ratom(8))</code> <code>array(patom(2), patom(5), patom(6), patom(8))</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="atno"/><a name="atnodesc"/><a name="atnoex"/>atno<br/>atomicNumber</td>
				<td>-</td>
				<td>returns the atomic number</td>
				<td>the atomic number</td>
				<td>atom index or MolAtom object</td>
				<td>
					<a class="fakelink" onclick="toggle('atnomoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('atnomoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="atnomoleculecontextexample">
						<code>atno(0)</code> returns the atomic number of atom <code>0</code>
					</div>

					<a class="fakelink" onclick="toggle('atnoreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('atnoreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="atnoreactioncontextexample" style="display:none;">
						<code>atno(ratom(1))</code> returns the atomic number of the reactant atom matching map <code>1</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="atomprop"/><a name="atompropdesc"/><a name="atompropex"/>atomProp</td>
				<td>-</td>
				<td></td>
				<td></td>
				<td></td>
				<td>
				</td>
			</tr>
			<tr>
				<td><a name="atoms"/><a name="atomsdesc"/><a name="atomsex"/>atoms</td>
				<td>-</td>
				<td></td>
				<td></td>
				<td></td>
				<td>
				</td>
			</tr>
			<tr>
				<td><a name="booleantonumber"/><a name="booleantonumberdesc"/><a name="booleantonumberex"/>booleanToNumber</td>
				<td>-</td>
				<td>returns the number representation of a boolean value (<code>true</code> = <code>1</code>, <code>false</code> = <code>0</code>),
				or the number itself, if the input is a number</td>
				<td>the number representation of a boolean value</td>
				<td>boolean or number</td>
				<td>
					<a class="fakelink" onclick="toggle('booleantonumbergeneralexample')">General</a>
<a class="fakelink" onclick="toggleAll('booleantonumbergeneralexample','generalexample')">(All)</a><br/>
					<div id="booleantonumbergeneralexample">
						<code>booleanToNumber(1&lt;2)</code>returns <code>1</code><br/><code>booleanToNumber(2+2==5)</code>returns <code>0</code><br/>
				<code>booleanToNumber(5*5)</code>returns <code>25</code>
					</div>

					<a class="fakelink" onclick="toggle('booleantonumbermoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('booleantonumbermoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="booleantonumbermoleculecontextexample" style="display:none;">
						<code>booleanToNumber(hasValenceError())</code>returns <code>0</code> for molecules having no valence error
					</div>

					<a class="fakelink" onclick="toggle('booleantonumberreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('booleantonumberreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="booleantonumberreactioncontextexample" style="display:none;">
						<code>booleanToNumber(ringBond(reactant(0), bond(ratom(1), ratom(2))))</code>returns <code>1</code> if reactant atoms
				matching maps 1 and 2 in the reaction equation are connected by a ring bond in the corresponding reactant, <code>0</code> otherwise
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="connections"/><a name="connectionsdesc"/><a name="connectionsex"/>connections</td>
				<td>-</td>
				<td>returns the bond plus implicit H count of an atom</td>
				<td>the bond plus implicit H count</td>
				<td>atom index or MolAtom object</td>
				<td>
					<a class="fakelink" onclick="toggle('connectionsmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('connectionsmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="connectionsmoleculecontextexample">
						<code>connections(2)</code> returns the number of connections of atom <code>2</code>
					</div>

					<a class="fakelink" onclick="toggle('connectionsreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('connectionsreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="connectionsreactioncontextexample" style="display:none;">
						<code>connections(ratom(1))</code> returns the number of connections of the reactant atom matching map <code>1</code> in the
		reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="count"/><a name="countdesc"/><a name="countex"/>count</td>
				<td>-</td>
				<td>determines the number of elements in an array</td>
				<td>the number of array elements</td>
				<td>integer array or real number array</td>
				<td>
					<a class="fakelink" onclick="toggle('countgeneralexample')">General</a>
<a class="fakelink" onclick="toggleAll('countgeneralexample','generalexample')">(All)</a><br/>
					<div id="countgeneralexample">
						<code>count(array(3.4, 5.6, 1.2))</code> returns <code>3</code>
					</div>

					<a class="fakelink" onclick="toggle('countmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('countmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="countmoleculecontextexample" style="display:none;">
						<code>count(filter("charge() > 0"))</code> returns the number of atoms with positive charge <code>count(filter("match('[#8][C:1]=O', 1"))</code>
		returns the number of carboxylic carbons
					</div>

					<a class="fakelink" onclick="toggle('countreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('countreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="countreactioncontextexample" style="display:none;">
						<code>count(filter(reactant(1), "charge() > 0"))</code> returns the number of atoms with positive charge in the
		second reactant <code>count(filter(product(0), "match('[#8][C:1]=O', 1"))</code> returns the number of carboxylic
		carbons in the first product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="elemanal"/><a name="elemanaldesc"/><a name="elemanalex"/>elemanal</td>
				<td>-</td>
				<td></td>
				<td></td>
				<td></td>
				<td>
				</td>
			</tr>
			<tr>
				<td><a name="eval"/><a name="evaldesc"/><a name="evalex"/>eval</td>
				<td>-</td>
				<td></td>
				<td></td>
				<td></td>
				<td>
				</td>
			</tr>
			<tr>
				<td><a name="field"/><a name="fielddesc"/><a name="fieldex"/>field<br/>property</td>
				<td>-</td>
				<td>returns a molecule property (SDF field value)</td>
				<td>the molecule property</td>
				<td>the property key (SDF field name)</td>
				<td>
					<a class="fakelink" onclick="toggle('fieldmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('fieldmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="fieldmoleculecontextexample">
						<code>field('ACTIVITY')</code> returns the value of the ACTIVITY property (SDF field) <code>field('ACTIVITY') > 2</code>
		returns <code>1</code> if the ACTIVITY value is bigger than <code>2</code>, returns <code>0</code> otherwise
					</div>

					<a class="fakelink" onclick="toggle('fieldreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('fieldreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="fieldreactioncontextexample" style="display:none;">
						<code>field(reactant(1), 'ACTIVITY')</code> returns the ACTIVITY property value of the second reactant <code>field(product(0), 'ACTIVITY') > field(reactant(0), 'ACTIVITY')</code>
		returns <code>1</code> if the ACTIVITY value of the first product is bigger than that of the first reactant, returns <code>0</code>
		otherwise
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="fieldasstring"/><a name="fieldasstringdesc"/><a name="fieldasstringex"/>fieldAsString<br/>propertyAsString</td>
				<td>-</td>
				<td>returns a molecule property (SDF field value) as string</td>
				<td>the molecule property as string</td>
				<td>the property key (SDF field name)</td>
				<td>
					<a class="fakelink" onclick="toggle('fieldasstringmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('fieldasstringmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="fieldasstringmoleculecontextexample">
						<code>fieldAsString('ID')</code> returns the value of the ID property (SDF field)
					</div>

					<a class="fakelink" onclick="toggle('fieldasstringreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('fieldasstringreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="fieldasstringreactioncontextexample" style="display:none;">
						<code>field(reactant(0), 'ID')</code> returns the value of the ID property field in first reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="filter"/><a name="filterdesc"/><a name="filterex"/>filter</td>
				<td>-</td>
				<td>filters target atoms by filtering condition</td>
				<td>target atom indices satisfying the filtering condition</td>
				<td>target atom indices / objects or index / atom object array (optional, all atoms taken if omitted), filtering
		condition (boolean expression)</td>
				<td>
					<a class="fakelink" onclick="toggle('filtermoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('filtermoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="filtermoleculecontextexample">
						<code>filter("charge() > 0")</code> returns the indices of atoms with positive partial charge in the input
		molecule <code>filter(6, 7, 8, 9, "match('[#8][C:1]=O', 1)")</code> returns the carboxylic carbons out of atoms <code>6,
		7, 8, 9</code> in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('filterreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('filterreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="filterreactioncontextexample" style="display:none;">
						<code>filter(reactant(0), "charge() > 0")</code> returns the indices of atoms with positive partial charge in
		the first reactant <code>filter(patom(1), patom(2), "match('[#8][C:1]=O', 1)")</code> returns the carboxylic carbons out
		of product atoms matching map <code>1</code> or <code>2</code> in the reaction equation (note, that these atoms are
		supposed to be in the same product molecule)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="formalcharge"/><a name="formalchargedesc"/><a name="formalchargeex"/>formalCharge<br/>totalCharge</td>
				<td>-</td>
				<td>calculates formal charge of molecule</td>
				<td>the formal charge value</td>
				<td>atom index or MolAtom object (optional)</td>
				<td>
					<a class="fakelink" onclick="toggle('formalchargemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('formalchargemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="formalchargemoleculecontextexample">
						<code>formalCharge()</code> returns the formal charge of the input molecule
				<code>formalCharge(0)</code> returns the formal charge of atom <code>0</code>
					</div>

					<a class="fakelink" onclick="toggle('formalchargereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('formalchargereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="formalchargereactioncontextexample" style="display:none;">
						formalCharge(reactant(0)) returns the formal charge of the first reactant
				<code>formalCharge(ratom(1))</code> returns the formal charge of the reactant atom matching map <code>1</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="fragments"/><a name="fragmentsdesc"/><a name="fragmentsex"/>fragments</td>
				<td>-</td>
				<td>converts the molecule to its disconnected fragments</td>
				<td>the disconnected fragments of the molecule</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('fragmentsmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('fragmentsmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="fragmentsmoleculecontextexample">
						<code>fragments()</code> returns the disconnected fragments of the molecule
					</div>

					<a class="fakelink" onclick="toggle('fragmentsreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('fragmentsreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="fragmentsreactioncontextexample" style="display:none;">
						<code>fragments(reactant(1))</code> returns the disconnected fragments of the second reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="hasaromatizationerror"/><a name="hasaromatizationerrordesc"/><a name="hasaromatizationerrorex"/>hasAromatizationError</td>
				<td>-</td>
				<td>determines if there is error in the aromatization of the molecule</td>
				<td><code>true</code> if there is an error in the aromatization of the molecule, <code>false</code> otherwise</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('hasaromatizationerrormoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('hasaromatizationerrormoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="hasaromatizationerrormoleculecontextexample">
						<code>hasAromatizationError()</code> returns <code>true</code> if there is an error in the aromatization of the molecule,
				<code>false</code> otherwise
					</div>

					<a class="fakelink" onclick="toggle('hasaromatizationerrorreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('hasaromatizationerrorreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="hasaromatizationerrorreactioncontextexample" style="display:none;">
						<code>hasAromatizationError(reactant(0))</code> returns <code>true</code> if there is an error in the aromatization of the first reactant,
				<code>false</code> otherwise
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="hasisotope"/><a name="hasisotopedesc"/><a name="hasisotopeex"/>hasIsotope</td>
				<td>-</td>
				<td>determines if any atom in the molecule is a specific isotope of the element</td>
				<td><code>true</code> if any atom in the molecule is a specific isotope of the element, <code>false</code> otherwise</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('hasisotopemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('hasisotopemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="hasisotopemoleculecontextexample">
						<code>hasIsotope()</code> returns <code>true</code> if any atom in the molecule is a specific isotope of the element, <code>false</code> otherwise
					</div>

					<a class="fakelink" onclick="toggle('hasisotopereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('hasisotopereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="hasisotopereactioncontextexample" style="display:none;">
						<code>hasIsotope(reactant(1))</code> returns <code>true</code> if any atom in the second reactant is a specific isotope of the element,
				<code>false</code> otherwise
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="hasradical"/><a name="hasradicaldesc"/><a name="hasradicalex"/>hasRadical</td>
				<td>-</td>
				<td>determines if any atom in the molecule has radical</td>
				<td><code>true</code> if any atom in the molecule has radical, <code>false</code> otherwise</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('hasradicalmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('hasradicalmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="hasradicalmoleculecontextexample">
						<code>hasRadical()</code> returns <code>true</code> if any atom in the molecule has radical, <code>false</code> otherwise
					</div>

					<a class="fakelink" onclick="toggle('hasradicalreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('hasradicalreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="hasradicalreactioncontextexample" style="display:none;">
						<code>hasRadical(reactant(1))</code> returns <code>true</code> if any atom in the second reactant has radical,
				<code>false</code> otherwise
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="hasvalenceerror"/><a name="hasvalenceerrordesc"/><a name="hasvalenceerrorex"/>hasValenceError</td>
				<td>-</td>
				<td>determines if any atom in the molecule has valence error</td>
				<td><code>true</code> in case of valence error, <code>false</code> otherwise</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('hasvalenceerrormoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('hasvalenceerrormoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="hasvalenceerrormoleculecontextexample">
						<code>hasValenceError()</code> returns <code>true</code> if any atom in the molecule has valence error, <code>false</code> otherwise
					</div>

					<a class="fakelink" onclick="toggle('hasvalenceerrorreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('hasvalenceerrorreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="hasvalenceerrorreactioncontextexample" style="display:none;">
						<code>hasValenceError(reactant(1))</code> returns <code>true</code> if any atom in the second reactant has valence error,
				<code>false</code> otherwise
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="hbda"/><a name="hbdadesc"/><a name="hbdaex"/>HBDA</td>
				<td>HBDA Plugin</td>
				<td></td>
				<td></td>
				<td></td>
				<td>
				</td>
			</tr>
			<tr>
				<td><a name="hcount"/><a name="hcountdesc"/><a name="hcountex"/>hCount</td>
				<td>-</td>
				<td>returns the hydrogen count of an atom</td>
				<td>the hydrogen count</td>
				<td>atom index or MolAtom object</td>
				<td>
					<a class="fakelink" onclick="toggle('hcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('hcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="hcountmoleculecontextexample">
						<code>hCount(0)</code> returns the hydrogen count of atom <code>0</code>
					</div>

					<a class="fakelink" onclick="toggle('hcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('hcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="hcountreactioncontextexample" style="display:none;">
						<code>hCount(patom(3))</code> returns the hydrogen count on the product atom matching map <code>3</code>
		in the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="importmol"/><a name="importmoldesc"/><a name="importmolex"/>importMol</td>
				<td>-</td>
				<td>imports and returns the molecule from its string representation</td>
				<td>the string representation of the molecule</td>
				<td>the molecule in string representation (e.g. "c1ccccc1")</td>
				<td>
					<a class="fakelink" onclick="toggle('importmolmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('importmolmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="importmolmoleculecontextexample">
						<code>importMol("c1ccccc1")</code> returns benzene molecule
					</div>

					<a class="fakelink" onclick="toggle('importmolreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('importmolreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="importmolreactioncontextexample" style="display:none;">
						N / A (reaction rules are numerical)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="in"/><a name="indesc"/><a name="inex"/>in</td>
				<td>-</td>
				<td>determines whether an integer / atom index is contained in a given integer array</td>
				<td><code>true</code> if the array contains the specified integer, <code>false</code> otherwise</td>
				<td>an integer or MolAtom object and an integer array</td>
				<td>
					<a class="fakelink" onclick="toggle('ingeneralexample')">General</a>
<a class="fakelink" onclick="toggleAll('ingeneralexample','generalexample')">(All)</a><br/>
					<div id="ingeneralexample">
						<code>in(5, array(3, 5, 1))</code> returns <code>true</code> <code>in(2, array(3, 5, 1))</code> returns <code>false</code>
					</div>

					<a class="fakelink" onclick="toggle('inmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('inmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="inmoleculecontextexample" style="display:none;">
						<code>in(3, maxatom("charge()", 2))</code> returns <code>true</code> if the partial charge on atom <code>3</code>
		is within the first <code>2</code> largest partial charges in the input molecule <code>in(3, minatom("pol()", 4))</code>
		returns <code>true</code> if the polarizability on atom <code>3</code> is within the first <code>4</code> smallest
		polarizability values in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('inreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('inreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="inreactioncontextexample" style="display:none;">
						<code>in(ratom(3), maxatom(reactant(0), "charge()", 2))</code> returns <code>true</code> if the partial charge
		on reactant atom matching map <code>3</code> in the reaction equation is within the first <code>2</code> largest
		partial charges in the first reactant <code>in(patom(1), minatom(product(1), "pol()", 4))</code> returns <code>true</code>
		if the polarizability on product atom matching map <code>1</code> in the reaction equation is within the first <code>4</code>
		smallest polarizability values in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="ioncharge"/><a name="ionchargedesc"/><a name="ionchargeex"/>ionCharge</td>
				<td>Charge Plugin Group</td>
				<td></td>
				<td></td>
				<td></td>
				<td>
				</td>
			</tr>
			<tr>
				<td><a name="isempty"/><a name="isemptydesc"/><a name="isemptyex"/>isEmpty</td>
				<td>-</td>
				<td>decides whether the given molecule is empty (does not contain any atoms, bonds, or non-empty S-groups)</td>
				<td><code>true</code> if the molecule does not contain any atoms, bonds, or non-empty S-groups, <code>false</code> otherwise</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('isemptymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('isemptymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="isemptymoleculecontextexample">
						<code>isQuery()</code> returns <code>true</code> if the given molecule does not contain any atoms, bonds, or non-empty S-groups, <code>false</code> otherwise
					</div>

					<a class="fakelink" onclick="toggle('isemptyreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('isemptyreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="isemptyreactioncontextexample" style="display:none;">
						<code>isQuery(reactant(1))</code> returns <code>true</code> if the second reactant contains any atoms, bonds, or non-empty S-groups,
				<code>false</code> otherwise
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="isquery"/><a name="isquerydesc"/><a name="isqueryex"/>isQuery</td>
				<td>-</td>
				<td>decides whether the given molecule contains any query features</td>
				<td><code>true</code> if the molecule contains any query features, <code>false</code> otherwise</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('isquerymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('isquerymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="isquerymoleculecontextexample">
						<code>isQuery()</code> returns <code>true</code> if the given molecule contains any query features, <code>false</code> otherwise
					</div>

					<a class="fakelink" onclick="toggle('isqueryreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('isqueryreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="isqueryreactioncontextexample" style="display:none;">
						<code>isQuery(reactant(1))</code> returns <code>true</code> if the second reactant contains any query features,
				<code>false</code> otherwise
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="map"/><a name="mapdesc"/><a name="mapex"/>map</td>
				<td>-</td>
				<td>returns the atom map number</td>
				<td>the atom map number</td>
				<td>atom index or MolAtom object</td>
				<td>
					<a class="fakelink" onclick="toggle('mapmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('mapmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="mapmoleculecontextexample">
						<code>map(3)</code> returns the atom map number of atom <code>3</code>
					</div>

					<a class="fakelink" onclick="toggle('mapreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('mapreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="mapreactioncontextexample" style="display:none;">
						<code>map(patom(3))</code> returns the atom map number on the product atom matching map <code>3</code>
		in the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="max"/><a name="maxdesc"/><a name="maxex"/>max</td>
				<td>-</td>
				<td>takes maximum of its array and/or numerical parameters</td>
				<td>the maximum value</td>
				<td>integers, real numbers, integer arrays, real number arrays</td>
				<td>
					<a class="fakelink" onclick="toggle('maxgeneralexample')">General</a>
<a class="fakelink" onclick="toggleAll('maxgeneralexample','generalexample')">(All)</a><br/>
					<div id="maxgeneralexample">
						<code>min(2, 8, 6)</code> returns <code>2</code> <code>max(3.4, 5.6, 1.2)</code> returns <code>5.6</code>
					</div>

					<a class="fakelink" onclick="toggle('maxmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('maxmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="maxmoleculecontextexample" style="display:none;">
						<code>min(charge(0), charge(2))</code> returns the least of the partial charge values on atoms <code>0</code>
		and <code>2</code> <code>max(charge())</code> returns the maximal partial charge value on the input molecule
					</div>

					<a class="fakelink" onclick="toggle('maxreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('maxreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="maxreactioncontextexample" style="display:none;">
						<code>min(charge(ratom(2)), charge(ratom(3)))</code> returns the least of the partial charge values on reactant
		atoms matching maps <code>2</code> and <code>3</code> in the reaction equation <code>max(charge(product(0)))</code>
		returns the maximal partial charge value on the first product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="maxatom"/><a name="maxatomdesc"/><a name="maxatomex"/>maxAtom</td>
				<td>-</td>
				<td>evaluates objective function for each atom, finds largest value(s)</td>
				<td>the atom index / indices corresponding to the largest evaluation result(s)</td>
				<td>target atom indices / objects or index / atom object array (optional, all atoms taken if omitted), the
		objective function (as inner expression string), the number of largest values to be taken (optional, takes
		only one if omitted)</td>
				<td>
					<a class="fakelink" onclick="toggle('maxatommoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('maxatommoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="maxatommoleculecontextexample">
						<code>maxAtom(6, 7, 8, 9, "charge('7.4')", 2)</code>
		selects the two largest partial charges on atoms <code>6, 7, 8, 9</code> in the major microspecies at pH <code>7.4</code>
		of the input molecule and returns the corresponding indices
					</div>

					<a class="fakelink" onclick="toggle('maxatomreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('maxatomreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="maxatomreactioncontextexample" style="display:none;">
						<code>maxAtom(patom(1), patom(2), patom(3), "charge('7.4')", 2)</code>
		selects the two largest partial charges on product atoms matching maps <code>1, 2, 3</code> in the major microspecies
		at pH <code>7.4</code> of the product molecule of these atoms and returns the corresponding indices (note, that these
		atoms are supposed to be in the same product molecule)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="maxvalue"/><a name="maxvaluedesc"/><a name="maxvalueex"/>maxValue</td>
				<td>-</td>
				<td>evaluates objective function for each atom, finds largest value(s)</td>
				<td>the largest evaluation result(s)</td>
				<td>target atom indices / objects or index / atom object array (optional, all atoms taken if omitted), the
		objective function (as inner expression string), the number of largest values to be taken (optional, takes
		only one if omitted)</td>
				<td>
					<a class="fakelink" onclick="toggle('maxvaluemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('maxvaluemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="maxvaluemoleculecontextexample">
						<code>maxValue(6, 7, 8, 9, "charge('7.4')", 2)</code> returns the two largest partial charges on
		atoms <code>6, 7, 8, 9</code> in the major microspecies at pH <code>7.4</code> of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('maxvaluereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('maxvaluereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="maxvaluereactioncontextexample" style="display:none;">
						<code>maxValue(patom(1), patom(2), patom(3), "charge('7.4')", 2)</code>
		returns the two largest partial charges on product atoms matching maps <code>1, 2, 3</code> in the major microspecies
		at pH <code>7.4</code> of the product molecule of these atoms (note, that these atoms are supposed to be in the same
		product molecule)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="min"/><a name="mindesc"/><a name="minex"/>min</td>
				<td>-</td>
				<td>takes minimum of its array and/or numerical parameters</td>
				<td>the minimum value</td>
				<td>integers, real numbers, integer arrays, real number arrays</td>
				<td>
					<a class="fakelink" onclick="toggle('mingeneralexample')">General</a>
<a class="fakelink" onclick="toggleAll('mingeneralexample','generalexample')">(All)</a><br/>
					<div id="mingeneralexample">
						<code>min(2, 8, 6)</code> returns <code>2</code> <code>max(3.4, 5.6, 1.2)</code> returns <code>5.6</code>
					</div>

					<a class="fakelink" onclick="toggle('minmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('minmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="minmoleculecontextexample" style="display:none;">
						<code>min(charge(0), charge(2))</code> returns the least of the partial charge values on atoms <code>0</code>
		and <code>2</code> <code>max(charge())</code> returns the maximal partial charge value on the input molecule
					</div>

					<a class="fakelink" onclick="toggle('minreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('minreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="minreactioncontextexample" style="display:none;">
						<code>min(charge(ratom(2)), charge(ratom(3)))</code> returns the least of the partial charge values on reactant
		atoms matching maps <code>2</code> and <code>3</code> in the reaction equation <code>max(charge(product(0)))</code>
		returns the maximal partial charge value on the first product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="minatom"/><a name="minatomdesc"/><a name="minatomex"/>minAtom</td>
				<td>-</td>
				<td>evaluates objective function for each atom, finds smallest value(s)</td>
				<td>the atom index / indices corresponding to the smallest evaluation result(s)</td>
				<td>target atom indices / objects or index / atom object array (optional, all atoms taken if omitted), the
		objective function (as inner expression string), the number of smallest values to be taken (optional, takes
		only one if omitted)</td>
				<td>
					<a class="fakelink" onclick="toggle('minatommoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('minatommoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="minatommoleculecontextexample">
						<code>minAtom("charge('7.4')")</code> returns the atom index corresponding to minimum partial charge in the
		major microspecies at pH <code>7.4</code> of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('minatomreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('minatomreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="minatomreactioncontextexample" style="display:none;">
						<code>minAtom(reactant(0), "charge('7.4')")</code> returns the atom index corresponding to minimum partial
		charge in the major microspecies at pH <code>7.4</code> of the first reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="minvalue"/><a name="minvaluedesc"/><a name="minvalueex"/>minValue</td>
				<td>-</td>
				<td>evaluates objective function for each atom, finds smallest value(s)</td>
				<td>the smallest evaluation result(s)</td>
				<td>target atom indices / objects or index / atom object array (optional, all atoms taken if omitted), the
		objective function (as inner expression string), the number of smallest values to be taken (optional, takes
		only one if omitted)</td>
				<td>
					<a class="fakelink" onclick="toggle('minvaluemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('minvaluemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="minvaluemoleculecontextexample">
						<code>minValue("charge('7.4')")</code> returns the minimum partial charge in the major microspecies at pH <code>7.4</code>
		of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('minvaluereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('minvaluereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="minvaluereactioncontextexample" style="display:none;">
						<code>minValue(reactant(0), "charge('7.4')")</code> returns the minimum partial charge in the major
		microspecies at pH <code>7.4</code> of the first reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="molatom"/><a name="molatomdesc"/><a name="molatomex"/>molAtom</td>
				<td>-</td>
				<td>creates a MolAtom</td>
				<td>the MolAtom object</td>
				<td>atomic number</td>
				<td>
					<a class="fakelink" onclick="toggle('molatommoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('molatommoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="molatommoleculecontextexample">
						<code>molAtom(6)</code> returns a carbon atom
					</div>

					<a class="fakelink" onclick="toggle('molatomreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('molatomreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="molatomreactioncontextexample" style="display:none;">
						<code>molAtom(6)</code> returns a carbon atom
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="molbinformat"/><a name="molbinformatdesc"/><a name="molbinformatex"/>molBinFormat<br/>molImage</td>
				<td>-</td>
				<td>returns the binary representation (image, pdf, GZIP compressed molecule file) of the molecule in specified format</td>
				<td>the binary representation (image, pdf, GZIP compressed molecule file) of the molecule</td>
				<td>the binary format with options (e.g. "jpeg", "png:w150,h150", "pdf", "gzip:sdf")</td>
				<td>
					<a class="fakelink" onclick="toggle('molbinformatmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('molbinformatmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="molbinformatmoleculecontextexample">
						<code>molImage("jpeg:w100,Q95,#ffff00")</code> returns the 100x100 JPEG image of the input molecule with yellow background, 95% quality
					</div>

					<a class="fakelink" onclick="toggle('molbinformatreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('molbinformatreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="molbinformatreactioncontextexample" style="display:none;">
						N / A (reaction rules are numerical)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="molstring"/><a name="molstringdesc"/><a name="molstringex"/>molString<br/>molConvert<br/>molFormat</td>
				<td>-</td>
				<td>returns the string representation of a molecule, or an array of molecules, in specified molecule format</td>
				<td>the string representation of the molecule(s)</td>
				<td><ul>
					<li>the <a href="../formats/formats.html">molecule format</a> (e.g. "mol", "sdf", "mrv", "smiles")</li>
					<li>the clean dimension</li>
				</ul></td>
				<td>
					<a class="fakelink" onclick="toggle('molstringmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('molstringmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="molstringmoleculecontextexample">
						<code>molFormat("mrv")</code> returns the ChemAxon Marvin Document format representation of the input molecule
				<code>molFormat(tautomers(), "sdf", 2)</code> returns the SDF format representation of the tautomers of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('molstringreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('molstringreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="molstringreactioncontextexample" style="display:none;">
						N / A (reaction rules are numerical)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="pair"/><a name="pairdesc"/><a name="pairex"/>pair<br/>bond</td>
				<td>-</td>
				<td>converts two atoms or 0-based atom indexes into an "index1-index2" 1-based atom index setter string<br />
		(used for pairing atoms in <a href="#shortestpathdesc">shortestPath</a>)</td>
				<td>the generated string</td>
				<td>two atom indexes or two MolAtom objects</td>
				<td>
					<a class="fakelink" onclick="toggle('pairgeneralexample')">General</a>
<a class="fakelink" onclick="toggleAll('pairgeneralexample','generalexample')">(All)</a><br/>
					<div id="pairgeneralexample">
						<code>pair(2, 5)</code> returns "3-6"
					</div>

					<a class="fakelink" onclick="toggle('pairreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('pairreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="pairreactioncontextexample" style="display:none;">
						<code>pair(ratom(1), ratom(2))</code> returns "index1-index2" where "index1" and "index2" are the 1-based atom
		indexes of the reactant atoms matching map <code>1</code> and <code>2</code> in the reaction equation <code>bond(patom(2), patom(5)</code>
		returns "index1-index2" where "index1" and "index2" are the 1-based atom indexes of the product atoms matching map <code>2</code>
		and <code>5</code> in the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="radicalcount"/><a name="radicalcountdesc"/><a name="radicalcountex"/>radicalCount</td>
				<td>-</td>
				<td>returns the radical count of an atom</td>
				<td>the radical count</td>
				<td>atom index or MolAtom object</td>
				<td>
					<a class="fakelink" onclick="toggle('radicalcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('radicalcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="radicalcountmoleculecontextexample">
						<code>radicalCount(0)</code> returns the radical count of atom <code>0</code>
					</div>

					<a class="fakelink" onclick="toggle('radicalcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('radicalcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="radicalcountreactioncontextexample" style="display:none;">
						<code>radicalCount(patom(3))</code> returns the radical count on the product atom matching map <code>3</code>
		in the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="sortasc"/><a name="sortascdesc"/><a name="sortascex"/>sortAsc</td>
				<td>-</td>
				<td>sorts an array in ascending order</td>
				<td>the sorted array</td>
				<td>integer array or real number array</td>
				<td>
					<a class="fakelink" onclick="toggle('sortascgeneralexample')">General</a>
<a class="fakelink" onclick="toggleAll('sortascgeneralexample','generalexample')">(All)</a><br/>
					<div id="sortascgeneralexample">
						<code>sortAsc(array(3.4, 5.6, 1.2))</code> returns <code>array(1.2, 3.4, 5.6)</code>
					</div>

					<a class="fakelink" onclick="toggle('sortascmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('sortascmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="sortascmoleculecontextexample" style="display:none;">
						<code>sortAsc(charge())</code> returns the partial charge values in ascending order
					</div>

					<a class="fakelink" onclick="toggle('sortascreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('sortascreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="sortascreactioncontextexample" style="display:none;">
						<code>sortAsc(charge(reactant(1)))</code> returns the partial charge values of the second reactant in ascending
		order
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="sortdesc"/><a name="sortdescdesc"/><a name="sortdescex"/>sortDesc</td>
				<td>-</td>
				<td>sorts an array in descending order</td>
				<td>the sorted array</td>
				<td>integer array or real number array</td>
				<td>
					<a class="fakelink" onclick="toggle('sortdescgeneralexample')">General</a>
<a class="fakelink" onclick="toggleAll('sortdescgeneralexample','generalexample')">(All)</a><br/>
					<div id="sortdescgeneralexample">
						<code>sortDesc(array(3.4, 5.6, 1.2))</code> returns <code>array(5.6, 3.4, 1.2)</code>
					</div>

					<a class="fakelink" onclick="toggle('sortdescmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('sortdescmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="sortdescmoleculecontextexample" style="display:none;">
						<code>sortDesc(pka("basic"))</code> returns the basic p<i>K</i><sub>a</sub> values in descending order
					</div>

					<a class="fakelink" onclick="toggle('sortdescreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('sortdescreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="sortdescreactioncontextexample" style="display:none;">
						<code>sortDesc(pka(product(0), "basic"))</code> returns the basic p<i>K</i><sub>a</sub> values of the first
		product in descending order
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="sum"/><a name="sumdesc"/><a name="sumex"/>sum</td>
				<td>-</td>
				<td>computes the sum of array elements</td>
				<td>the sum</td>
				<td>integer array or real number array</td>
				<td>
					<a class="fakelink" onclick="toggle('sumgeneralexample')">General</a>
<a class="fakelink" onclick="toggleAll('sumgeneralexample','generalexample')">(All)</a><br/>
					<div id="sumgeneralexample">
						<code>sum(array(3.4, 5.6, 1.2))</code> returns <code>10.2</code>
					</div>

					<a class="fakelink" onclick="toggle('summoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('summoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="summoleculecontextexample" style="display:none;">
						<code>sum(charge())</code> returns the sum of charge values <code>sum(pol())</code> returns the sum of atom
		polarizability values
					</div>

					<a class="fakelink" onclick="toggle('sumreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('sumreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="sumreactioncontextexample" style="display:none;">
						<code>sum(charge(reactant(0)))</code> returns the sum of charge values in the first reactant <code>sum(pol(product(0)))</code>
		returns the sum of atom polarizability values in the first product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="topanal"/><a name="topanaldesc"/><a name="topanalex"/>topanal</td>
				<td>Geometry Plugin Group</td>
				<td></td>
				<td></td>
				<td></td>
				<td>
				</td>
			</tr>
			<tr>
				<td><a name="valence"/><a name="valencedesc"/><a name="valenceex"/>valence</td>
				<td>-</td>
				<td>returns the sum of bond orders and query H atoms of an atom</td>
				<td>the sum of bond orders and query H atoms</td>
				<td>atom index or MolAtom object</td>
				<td>
					<a class="fakelink" onclick="toggle('valencemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('valencemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="valencemoleculecontextexample">
						<code>valence(0)</code> returns the valence of atom <code>0</code>
					</div>

					<a class="fakelink" onclick="toggle('valencereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('valencereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="valencereactioncontextexample" style="display:none;">
						<code>valence(ratom(1))</code> returns the valence of the reactant atom matching map <code>1</code> in the
		reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="whereisvalenceerror"/><a name="whereisvalenceerrordesc"/><a name="whereisvalenceerrorex"/>whereIsValenceError</td>
				<td>-</td>
				<td>returns the index of the first atom with valence error, or <code>-1</code> if there is no valence error</td>
				<td>the index of the first atom with valence error, or <code>-1</code> if there is no valence error</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('whereisvalenceerrormoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('whereisvalenceerrormoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="whereisvalenceerrormoleculecontextexample">
						<code>whereIsValenceError()</code> returns the index of the first atom with valence error, or <code>-1</code>
				if there is no valence error in the molecule
					</div>

					<a class="fakelink" onclick="toggle('whereisvalenceerrorreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('whereisvalenceerrorreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="whereisvalenceerrorreactioncontextexample" style="display:none;">
						<code>whereIsValenceError(reactant(1))</code> returns the index of the first atom with valence error in the second reactant,
				or <code>-1</code> f there is no valence error in the second reactant
					</div>
				</td>
			</tr>
		</table>
		<p><a href="#contents">Back to Contents</a></p>
		<p>&nbsp;</p>
		<div style="margin-left: auto; margin-right: auto; " class="lenia">&nbsp;</div>
		<h3><a name="geometry_functions"></a>Geometry Functions</h3>
		<table style="margin-left: auto; margin-right: auto; text-align: left;" border="0" cellspacing="0" cellpadding="5" class="grid">
			<tr>
				<th style="width:5%;">Name</th>
				<th style="width:5%;">License</th>
				<th style="width:25%;">Description</th>
				<th style="width:15%;">Return value</th>
				<th style="width:25%;">Parameters</th>
				<th style="width:25%;">Examples</th>
			</tr>
			<tr>
				<td><a name="aliphaticatom"/><a name="aliphaticatomdesc"/><a name="aliphaticatomex"/>aliphaticAtom</td>
				<td>Geometry Plugin Group</td>
				<td>checks if the specified atom is aliphatic</td>
				<td><code>true</code> for aliphatic atoms,<br />
<code>false</code> for non-aliphatic atoms</td>
				<td><ul>
<li>the atom index / MolAtom object
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('aliphaticatommoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('aliphaticatommoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="aliphaticatommoleculecontextexample">
						<code>aliphaticAtom(2)</code> returns <code>true</code> if atom <code>2</code> of the input molecule is
		aliphatic, <code>false</code> otherwise
					</div>

					<a class="fakelink" onclick="toggle('aliphaticatomreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('aliphaticatomreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="aliphaticatomreactioncontextexample" style="display:none;">
						<code>aliphaticAtom(ratom(2))</code> returns <code>true</code> if the reactant atom matching map <code>2</code>
		in the reaction equation is aliphatic, <code>false</code> otherwise <code>aliphaticAtom(patom(1))</code> returns <code>true</code>
		if the product atom matching map <code>1</code> in the reaction equation is aliphatic, <code>false</code> otherwise
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="aliphaticatomcount"/><a name="aliphaticatomcountdesc"/><a name="aliphaticatomcountex"/>aliphaticAtomCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the aliphatic atom count</td>
				<td>the aliphatic atom count</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('aliphaticatomcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('aliphaticatomcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="aliphaticatomcountmoleculecontextexample">
						<code>aliphaticAtomCount()</code> returns the number of aliphatic atoms in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('aliphaticatomcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('aliphaticatomcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="aliphaticatomcountreactioncontextexample" style="display:none;">
						<code>aliphaticAtomCount(reactant(0))</code> returns the number of aliphatic atoms in the first reactant <code>aliphaticAtomCount(product(1))</code>
		returns the number of aliphatic atoms in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="aliphaticbondcount"/><a name="aliphaticbondcountdesc"/><a name="aliphaticbondcountex"/>aliphaticBondCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the aliphatic bond count</td>
				<td>the aliphatic bond count</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('aliphaticbondcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('aliphaticbondcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="aliphaticbondcountmoleculecontextexample">
						<code>aliphaticBondCount()</code> returns the number of bonds in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('aliphaticbondcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('aliphaticbondcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="aliphaticbondcountreactioncontextexample" style="display:none;">
						<code>aliphaticBondCount(reactant(0))</code> returns the number of aliphatic bonds in the first reactant <code>aliphaticBondCount(product(1))</code>
		returns the number of aliphatic bonds in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="aliphaticringcount"/><a name="aliphaticringcountdesc"/><a name="aliphaticringcountex"/>aliphaticRingCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the aliphatic ring count</td>
				<td>the aliphatic ring count</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('aliphaticringcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('aliphaticringcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="aliphaticringcountmoleculecontextexample">
						<code>aliphaticRingCount()</code> returns the number of aliphatic rings in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('aliphaticringcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('aliphaticringcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="aliphaticringcountreactioncontextexample" style="display:none;">
						<code>aliphaticRingCount(reactant(0))</code> returns the number of aliphatic rings in the first reactant <code>aliphaticRingCount(product(1))</code>
		returns the number of aliphatic rings in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="aliphaticringcountofsize"/><a name="aliphaticringcountofsizedesc"/><a name="aliphaticringcountofsizeex"/>aliphaticRingCountOfSize</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of aliphatic rings of given size</td>
				<td>the number of aliphatic rings of given size</td>
				<td>the ring size</td>
				<td>
					<a class="fakelink" onclick="toggle('aliphaticringcountofsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('aliphaticringcountofsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="aliphaticringcountofsizemoleculecontextexample">
						<code>aliphaticRingCountOfSize(6)</code> returns the number of aliphatic rings of size 6 in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('aliphaticringcountofsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('aliphaticringcountofsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="aliphaticringcountofsizereactioncontextexample" style="display:none;">
						<code>aliphaticRingCountOfSize(reactant(0), 5)</code> returns the number of aliphatic rings of size 5 in the first reactant
		<code>aliphaticRingCountOfSize(product(1), 5)</code> returns the number of aliphatic rings of size 5 in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="aliphaticrings"/><a name="aliphaticringsdesc"/><a name="aliphaticringsex"/>aliphaticRings</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the aliphatic rings in the molecule</td>
				<td>atom indexes of the aliphatic rings in the molecule (null if the molecule does not contain aliphatic rings)</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('aliphaticringsmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('aliphaticringsmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="aliphaticringsmoleculecontextexample">
						<code>aliphaticRings()</code> returns the atom indexes of the aliphatic rings in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('aliphaticringsreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('aliphaticringsreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="aliphaticringsreactioncontextexample" style="display:none;">
						<code>aliphaticRings(reactant(0))</code> returns the atom indexes of the aliphatic rings in the first reactant <code>aliphaticRings(product(1))</code>
		returns the atom indexes of the aliphatic rings in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="aliphaticringsofsize"/><a name="aliphaticringsofsizedesc"/><a name="aliphaticringsofsizeex"/>aliphaticRingsOfSize</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the aliphatic rings in the molecule having a given size (number of atoms)</td>
				<td>atom indexes of the aliphatic rings in the molecule having the given size (null if the molecule does not contain aliphatic rings)</td>
				<td>the ring size</td>
				<td>
					<a class="fakelink" onclick="toggle('aliphaticringsofsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('aliphaticringsofsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="aliphaticringsofsizemoleculecontextexample">
						<code>aliphaticRings()</code> returns the atom indexes of the aliphatic rings in the input molecule having the given size
					</div>

					<a class="fakelink" onclick="toggle('aliphaticringsofsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('aliphaticringsofsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="aliphaticringsofsizereactioncontextexample" style="display:none;">
						<code>aliphaticRings(reactant(0))</code> returns the atom indexes of the aliphatic rings in the first reactant having the given size <code>aliphaticRings(product(1))</code>
		returns the atom indexes of the aliphatic rings in the second product having the given size
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="angle"/><a name="angledesc"/><a name="angleex"/>angle</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the angle between three atoms</td>
				<td>the angle between three atoms</td>
				<td><ul>
<li>the (1-based) atom indexes of the three atoms in a string: "index1-index2-index3" (e.g. '2-3-5')
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('anglemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('anglemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="anglemoleculecontextexample">
						<code>angle('1-2-3')</code> and <code>angle(atoms(0, 1, 2))</code> both return the angle between atoms <code>1</code>,
		<code>2</code> and <code>3</code> in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('anglereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('anglereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="anglereactioncontextexample" style="display:none;">
						<code>angle(reactant(0), atoms(ratom(1), ratom(2), ratom(3)))</code> returns the angle between reactant atoms matching maps
		<code>1</code>, <code>2</code> and <code>3</code> in the reaction equation (<a href="#note1">see note 1</a>)
		<code>angle(product(1), atoms(patom(2), patom(3), patom(4)))</code> returns the angle between product atoms matching maps <code>2</code>,
		<code>3</code> and <code>4</code> in the reaction equation (<a href="#note1">see note 1</a>)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="aromaticatom"/><a name="aromaticatomdesc"/><a name="aromaticatomex"/>aromaticAtom</td>
				<td>Geometry Plugin Group</td>
				<td>checks if the specified atom is aromatic</td>
				<td><code>true</code> for aromatic atoms,<br />
<code>false</code> for non-aromatic atoms</td>
				<td><ul>
<li>the atom index / MolAtom object
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('aromaticatommoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('aromaticatommoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="aromaticatommoleculecontextexample">
						<code>aromaticAtom(2)</code> returns <code>true</code> if atom <code>2</code> of the input molecule is
		aromatic, <code>false</code> otherwise
					</div>

					<a class="fakelink" onclick="toggle('aromaticatomreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('aromaticatomreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="aromaticatomreactioncontextexample" style="display:none;">
						<code>aromaticAtom(ratom(2))</code> returns <code>true</code> if the reactant atom matching map <code>2</code>
		in the reaction equation is aromatic, <code>false</code> otherwise <code>aromaticAtom(patom(1))</code> returns <code>true</code>
		if the product atom matching map <code>1</code> in the reaction equation is aromatic, <code>false</code> otherwise
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="aromaticatomcount"/><a name="aromaticatomcountdesc"/><a name="aromaticatomcountex"/>aromaticAtomCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the aromatic atom count</td>
				<td>the aromatic atom count</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('aromaticatomcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('aromaticatomcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="aromaticatomcountmoleculecontextexample">
						<code>aromaticAtomCount()</code> returns the number of aromatic atoms in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('aromaticatomcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('aromaticatomcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="aromaticatomcountreactioncontextexample" style="display:none;">
						<code>aromaticAtomCount(reactant(0))</code> returns the number of aromatic atoms in the first reactant <code>aromaticAtomCount(product(1))</code>
		returns the number of aromatic atoms in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="aromaticbondcount"/><a name="aromaticbondcountdesc"/><a name="aromaticbondcountex"/>aromaticBondCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the aromatic bond count</td>
				<td>the aromatic bond count</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('aromaticbondcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('aromaticbondcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="aromaticbondcountmoleculecontextexample">
						<code>aromaticBondCount()</code> returns the number of aromatic bonds in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('aromaticbondcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('aromaticbondcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="aromaticbondcountreactioncontextexample" style="display:none;">
						<code>aromaticBondCount(reactant(0))</code> returns the number of aromatic bonds in the first reactant <code>aromaticBondCount(product(1))</code>
		returns the number of aromatic bonds in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="aromaticringcount"/><a name="aromaticringcountdesc"/><a name="aromaticringcountex"/>aromaticRingCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the aromatic ring count</td>
				<td>the aromatic ring count</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('aromaticringcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('aromaticringcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="aromaticringcountmoleculecontextexample">
						<code>aromaticRingCount()</code> returns the number of aromatic rings in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('aromaticringcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('aromaticringcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="aromaticringcountreactioncontextexample" style="display:none;">
						<code>aromaticRingCount(reactant(0))</code> returns the number of aromatic rings in the first reactant <code>aromaticRingCount(product(1))</code>
		returns the number of aromatic rings in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="aromaticringcountofsize"/><a name="aromaticringcountofsizedesc"/><a name="aromaticringcountofsizeex"/>aromaticRingCountOfSize</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of aromatic rings of given size</td>
				<td>the number of aromatic rings of given size</td>
				<td>the ring size</td>
				<td>
					<a class="fakelink" onclick="toggle('aromaticringcountofsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('aromaticringcountofsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="aromaticringcountofsizemoleculecontextexample">
						<code>aromaticRingCountOfSize(6)</code> returns the number of aromatic rings of size 6 in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('aromaticringcountofsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('aromaticringcountofsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="aromaticringcountofsizereactioncontextexample" style="display:none;">
						<code>aromaticRingCountOfSize(reactant(0), 5)</code> returns the number of aromatic rings of size 5 in the first reactant
		<code>aromaticRingCountOfSize(product(1), 5)</code> returns the number of aromatic rings of size 5 in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="aromaticrings"/><a name="aromaticringsdesc"/><a name="aromaticringsex"/>aromaticRings</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the aromatic rings in the molecule</td>
				<td>atom indexes of the aromatic rings in the molecule (null if the molecule does not contain aromatic rings)</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('aromaticringsmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('aromaticringsmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="aromaticringsmoleculecontextexample">
						<code>aromaticRings()</code> returns the atom indexes of the aromatic rings in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('aromaticringsreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('aromaticringsreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="aromaticringsreactioncontextexample" style="display:none;">
						<code>aromaticRings(reactant(0))</code> returns the atom indexes of the aromatic rings in the first reactant <code>aromaticRings(product(1))</code>
		returns the atom indexes of the aromatic rings in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="aromaticringsofsize"/><a name="aromaticringsofsizedesc"/><a name="aromaticringsofsizeex"/>aromaticRingsOfSize</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the aromatic rings in the molecule having a given size (number of atoms)</td>
				<td>atom indexes of the aromatic rings in the molecule having the given size (null if the molecule does not contain aromatic rings)</td>
				<td>the ring size</td>
				<td>
					<a class="fakelink" onclick="toggle('aromaticringsofsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('aromaticringsofsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="aromaticringsofsizemoleculecontextexample">
						<code>aromaticRings()</code> returns the atom indexes of the aromatic rings in the input molecule having the given size
					</div>

					<a class="fakelink" onclick="toggle('aromaticringsofsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('aromaticringsofsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="aromaticringsofsizereactioncontextexample" style="display:none;">
						<code>aromaticRings(reactant(0))</code> returns the atom indexes of the aromatic rings in the first reactant having the given size <code>aromaticRings(product(1))</code>
		returns the atom indexes of the aromatic rings in the second product having the given size
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="asahydrophobic"/><a name="asahydrophobicdesc"/><a name="asahydrophobicex"/>ASAHydrophobic</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the water accessible molecular surface area of all hydrophobic atoms</td>
				<td>the molecular surface area</td>
				<td><ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('asahydrophobicmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('asahydrophobicmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="asahydrophobicmoleculecontextexample">
						<code>ASAHydrophobic()</code> returns the water accessible surface area of all atoms of the input molecule with hydrophobic partial charge
		<code>ASAHydrophobic('7.4')</code>returns the water accessible surface area of all atoms with hydrophobic partial charge of the major microspecies taken at
		pH <code>7.4</code>
					</div>

					<a class="fakelink" onclick="toggle('asahydrophobicreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('asahydrophobicreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="asahydrophobicreactioncontextexample" style="display:none;">
						<code>ASAHydrophobic(reactant(0))</code> returns the water accessible surface area of all atoms of the first reactant with hydrophobic partial charge
		<code>ASAHydrophobic(product(0), '7.4')</code> returns the water accessible surface area of all atoms with hydrophobic partial charge  of the major
		microspecies of the first product taken at pH <code>7.4</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="asanegative"/><a name="asanegativedesc"/><a name="asanegativeex"/>ASANegative</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the water accessible molecular surface area of all atoms with negative partial charge</td>
				<td>the molecular surface area</td>
				<td><ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('asanegativemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('asanegativemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="asanegativemoleculecontextexample">
						<code>ASANegative()</code> returns the water accessible surface area of all atoms of the input molecule with negative partial charge
		<code>ASANegative('7.4')</code>returns the water accessible surface area of all atoms with negative partial charge of the major microspecies taken at
		pH <code>7.4</code>
					</div>

					<a class="fakelink" onclick="toggle('asanegativereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('asanegativereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="asanegativereactioncontextexample" style="display:none;">
						<code>ASANegative(reactant(0))</code> returns the water accessible surface area of all atoms of the first reactant with negative partial charge
		<code>ASANegative(product(0), '7.4')</code> returns the water accessible surface area of all atoms with negative partial charge  of the major
		microspecies of the first product taken at pH <code>7.4</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="asaplus"/><a name="asaplusdesc"/><a name="asaplusex"/>ASAPlus</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the water accessible molecular surface area of all atoms with positive partial charge</td>
				<td>the molecular surface area</td>
				<td><ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('asaplusmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('asaplusmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="asaplusmoleculecontextexample">
						<code>ASAPlus()</code> returns the water accessible surface area of all atoms of the input molecule with positive partial charge
		<code>ASAPlus('7.4')</code>returns the water accessible surface area of all atoms with positive partial charge of the major microspecies taken at
		pH <code>7.4</code>
					</div>

					<a class="fakelink" onclick="toggle('asaplusreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('asaplusreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="asaplusreactioncontextexample" style="display:none;">
						<code>ASAPlus(reactant(0))</code> returns the water accessible surface area of all atoms of the first reactant with positive partial charge
		<code>ASAPlus(product(0), '7.4')</code> returns the water accessible surface area of all atoms with positive partial charge  of the major
		microspecies of the first product taken at pH <code>7.4</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="asapolar"/><a name="asapolardesc"/><a name="asapolarex"/>ASAPolar</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the water accessible molecular surface area of all polar atoms</td>
				<td>the molecular surface area</td>
				<td><ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('asapolarmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('asapolarmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="asapolarmoleculecontextexample">
						<code>ASAPolar()</code> returns the water accessible surface area of all atoms of the input molecule with polar partial charge
		<code>ASAPolar('7.4')</code>returns the water accessible surface area of all atoms with polar partial charge of the major microspecies taken at
		pH <code>7.4</code>
					</div>

					<a class="fakelink" onclick="toggle('asapolarreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('asapolarreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="asapolarreactioncontextexample" style="display:none;">
						<code>ASAPolar(reactant(0))</code> returns the water accessible surface area of all atoms of the first reactant with polar partial charge
		<code>ASAPolar(product(0), '7.4')</code> returns the water accessible surface area of all atoms with polar partial charge  of the major
		microspecies of the first product taken at pH <code>7.4</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="asymmetricatom"/><a name="asymmetricatomdesc"/><a name="asymmetricatomex"/>asymmetricAtom</td>
				<td>Geometry Plugin Group</td>
				<td>checks if the specified atom is an asymmetric atom</td>
				<td><code>true</code> for asymmetric atoms,<br />
<code>false</code> for symmetric atoms</td>
				<td><ul>
<li>the atom index / MolAtom object
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('asymmetricatommoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('asymmetricatommoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="asymmetricatommoleculecontextexample">
						<code>asymmetricAtom(2)</code> returns <code>true</code> if atom <code>2</code> of the input molecule is an
		asymmetric atom, <code>false</code> otherwise
					</div>

					<a class="fakelink" onclick="toggle('asymmetricatomreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('asymmetricatomreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="asymmetricatomreactioncontextexample" style="display:none;">
						<code>asymmetricAtom(ratom(2))</code> returns <code>true</code> if the reactant atom matching map <code>2</code>
		in the reaction equation is an asymmetric atom, <code>false</code> otherwise <code>asymmetricAtom(patom(1))</code>
		returns <code>true</code> if the product atom matching map <code>1</code> in the reaction equation is an asymmetric
		atom, <code>false</code> otherwise
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="asymmetricatomcount"/><a name="asymmetricatomcountdesc"/><a name="asymmetricatomcountex"/>asymmetricAtomCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of asymmetric atoms</td>
				<td>the asymmetric atom count</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('asymmetricatomcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('asymmetricatomcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="asymmetricatomcountmoleculecontextexample">
						<code>asymmetricAtomCount()</code> returns the number of asymmetric atoms in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('asymmetricatomcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('asymmetricatomcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="asymmetricatomcountreactioncontextexample" style="display:none;">
						<code>asymmetricAtomCount(reactant(0))</code> returns the number of asymmetric atoms in the first reactant <code>asymmetricAtomCount(product(1))</code>
		returns the number of asymmetric atoms in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="asymmetricatoms"/><a name="asymmetricatomsdesc"/><a name="asymmetricatomsex"/>asymmetricAtoms</td>
				<td>Geometry Plugin Group</td>
				<td>determines the asymmetric atoms</td>
				<td>indexes of asymmetric atoms</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('asymmetricatomsmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('asymmetricatomsmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="asymmetricatomsmoleculecontextexample">
						<code>asymmetricAtoms()</code> returns the indexes of asymmetric atoms in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('asymmetricatomsreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('asymmetricatomsreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="asymmetricatomsreactioncontextexample" style="display:none;">
						<code>asymmetricAtoms(reactant(0))</code> returns the indexes of asymmetric atoms in the first reactant <code>asymmetricAtoms(product(1))</code>
		returns the indexes of asymmetric atoms in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="balabanindex"/><a name="balabanindexdesc"/><a name="balabanindexex"/>balabanIndex</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the Balaban index</td>
				<td>the Balaban index</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('balabanindexmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('balabanindexmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="balabanindexmoleculecontextexample">
						calculates the Balaban index
					</div>

					<a class="fakelink" onclick="toggle('balabanindexreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('balabanindexreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="balabanindexreactioncontextexample" style="display:none;">
						the Balaban index
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="bondcount"/><a name="bondcountdesc"/><a name="bondcountex"/>bondCount</td>
				<td>-</td>
				<td>calculates the bond count</td>
				<td>the bond count</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('bondcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('bondcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="bondcountmoleculecontextexample">
						<code>bondCount()</code> returns the number of bonds in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('bondcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('bondcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="bondcountreactioncontextexample" style="display:none;">
						<code>bondCount(reactant(0))</code> returns the number of bonds in the first reactant <code>bondCount(product(1))</code>
		returns the number of bonds in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="bondtype"/><a name="bondtypedesc"/><a name="bondtypeex"/>bondType</td>
				<td>Geometry Plugin Group</td>
				<td>returns the bond type between two atoms</td>
				<td>the bond type between two atoms, <code>-1</code> if there is no bond between the two atoms</td>
				<td><ul>
<li>the (1-based) atom indexes of the two atoms in a string: "index1-index2" (e.g. '2-3')
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('bondtypemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('bondtypemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="bondtypemoleculecontextexample">
						<code>bondType('2-3')</code> and <code>bondType(bond(1, 2))</code> both return the bond type between atoms <code>1</code>
		and <code>2</code> in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('bondtypereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('bondtypereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="bondtypereactioncontextexample" style="display:none;">
						<code>bondType(reactant(0), bond(ratom(1), ratom(2)))</code> returns the bond type between reactant atoms matching maps <code>1</code>
		and <code>2</code> in the reaction equation (<a href="#note1">see note 1</a>)<code>bondType(product(1), bond(patom(2), patom(3)))</code>
		returns the bond type between product atoms matching maps <code>2</code> and <code>3</code> in the reaction equation
		(<a href="#note1">see note 1</a>)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="carboaliphaticringcount"/><a name="carboaliphaticringcountdesc"/><a name="carboaliphaticringcountex"/>carboaliphaticRingCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of carboaliphatic rings in the molecule (aliphatic rings containing carbon atoms only)</td>
				<td>number of carboaliphatic rings</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('carboaliphaticringcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('carboaliphaticringcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="carboaliphaticringcountmoleculecontextexample">
						<code>carboaliphaticRingCount()</code> returns the number of carboaliphatic rings in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('carboaliphaticringcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('carboaliphaticringcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="carboaliphaticringcountreactioncontextexample" style="display:none;">
						<code>carboaliphaticRingCount(reactant(0))</code> returns the number of carboaliphatic rings in the first
		reactant <code>carboaliphaticRingCount(product(1))</code> returns the number of carboaliphatic rings in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="carboaromaticringcount"/><a name="carboaromaticringcountdesc"/><a name="carboaromaticringcountex"/>carboaromaticRingCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of carboaromatic rings in the molecule (aromatic rings containing carbon atoms only)</td>
				<td>number of carboaromatic rings</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('carboaromaticringcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('carboaromaticringcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="carboaromaticringcountmoleculecontextexample">
						<code>carboaromaticRingCount()</code> returns the number of carboaromatic rings in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('carboaromaticringcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('carboaromaticringcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="carboaromaticringcountreactioncontextexample" style="display:none;">
						<code>carboaromaticRingCount(reactant(0))</code> returns the number of carboaromatic rings in the first
		reactant <code>carboaromaticRingCount(product(1))</code> returns the number of carboaromatic rings in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="carboringcount"/><a name="carboringcountdesc"/><a name="carboringcountex"/>carboRingCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of carbocyclic rings in the molecule (rings containing carbon atoms only)</td>
				<td>number of carbocyclic rings</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('carboringcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('carboringcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="carboringcountmoleculecontextexample">
						<code>carboRingCount()</code> returns the number of carbocyclic rings in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('carboringcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('carboringcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="carboringcountreactioncontextexample" style="display:none;">
						<code>carboRingCount(reactant(0))</code> returns the number of carbocyclic rings in the first reactant <code>carboRingCount(product(1))</code>
		returns the number of carbocyclic rings in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="carboringcountofsize"/><a name="carboringcountofsizedesc"/><a name="carboringcountofsizeex"/>carboRingCountOfSize</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of carbocyclic rings of given size (rings containing carbon atoms only)</td>
				<td>the number of carbocyclic rings of given size</td>
				<td>the ring size</td>
				<td>
					<a class="fakelink" onclick="toggle('carboringcountofsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('carboringcountofsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="carboringcountofsizemoleculecontextexample">
						<code>carboRingCountOfSize(6)</code> returns the number of carbocyclic rings of size 6 in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('carboringcountofsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('carboringcountofsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="carboringcountofsizereactioncontextexample" style="display:none;">
						<code>carboRingCountOfSize(reactant(0), 5)</code> returns the number of carbocyclic rings of size 5 in the first reactant
		<code>carboRingCountOfSize(product(1), 5)</code> returns the number of carbocyclic rings of size 5 in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="carborings"/><a name="carboringsdesc"/><a name="carboringsex"/>carboRings</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the carbocyclic rings in the molecule (rings containing carbon atoms only)</td>
				<td>atom indexes of the carbocyclic rings in the molecule (null if the molecule does not contain carbocyclic rings)</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('carboringsmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('carboringsmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="carboringsmoleculecontextexample">
						<code>carboRings()</code> returns the atom indexes of the carbocyclic rings in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('carboringsreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('carboringsreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="carboringsreactioncontextexample" style="display:none;">
						<code>carboRings(reactant(0))</code> returns the atom indexes of the carbocyclic rings in the first reactant <code>carboRings(product(1))</code>
		returns the atom indexes of the carbocyclic rings in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="carboringsofsize"/><a name="carboringsofsizedesc"/><a name="carboringsofsizeex"/>carboRingsOfSize</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the carbocyclic rings in the molecule having a given size (number of atoms)</td>
				<td>atom indexes of the carbocyclic rings in the molecule having the given size (null if the molecule does not carbocyclic carbo rings)</td>
				<td>the ring size</td>
				<td>
					<a class="fakelink" onclick="toggle('carboringsofsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('carboringsofsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="carboringsofsizemoleculecontextexample">
						<code>carboRings()</code> returns the atom indexes of the carbocyclic rings in the input molecule having the given size
					</div>

					<a class="fakelink" onclick="toggle('carboringsofsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('carboringsofsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="carboringsofsizereactioncontextexample" style="display:none;">
						<code>carboRings(reactant(0))</code> returns the atom indexes of the carbocyclic rings in the first reactant having the given size <code>carboRings(product(1))</code>
		returns the atom indexes of the carbocyclic rings in the second product having the given size
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="chainatom"/><a name="chainatomdesc"/><a name="chainatomex"/>chainAtom</td>
				<td>Geometry Plugin Group</td>
				<td>checks if the specified atom is a chain atom</td>
				<td><code>true</code> for chain atoms,<br />
<code>false</code> for non-chain atoms</td>
				<td><ul>
<li>the atom index / MolAtom object
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('chainatommoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('chainatommoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="chainatommoleculecontextexample">
						<code>chainAtom(2)</code> returns <code>true</code> if atom <code>2</code> of the input molecule is a chain
		atom, <code>false</code> otherwise
					</div>

					<a class="fakelink" onclick="toggle('chainatomreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('chainatomreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="chainatomreactioncontextexample" style="display:none;">
						<code>chainAtom(ratom(2))</code> returns <code>true</code> if the reactant atom matching map <code>2</code> in
		the reaction equation is a chain atom, <code>false</code> otherwise <code>chainAtom(patom(1))</code> returns <code>true</code>
		if the product atom matching map <code>1</code> in the reaction equation is a chain atom, <code>false</code> otherwise
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="chainatomcount"/><a name="chainatomcountdesc"/><a name="chainatomcountex"/>chainAtomCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the chain atom count</td>
				<td>the chain atom count</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('chainatomcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('chainatomcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="chainatomcountmoleculecontextexample">
						<code>chainAtomCount()</code> returns the number of chain atoms in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('chainatomcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('chainatomcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="chainatomcountreactioncontextexample" style="display:none;">
						<code>chainAtomCount(reactant(0))</code> returns the number of chain atoms in the first reactant <code>chainAtomCount(product(1))</code>
		returns the number of chain atoms in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="chainbond"/><a name="chainbonddesc"/><a name="chainbondex"/>chainBond</td>
				<td>Geometry Plugin Group</td>
				<td>checks if two atoms are connected by a chain bond</td>
				<td><code>true</code> if the two atoms are connected by a chain bond, <code>false</code> otherwise</td>
				<td><ul>
<li>the (1-based) atom indexes of the two atoms in a string: "index1-index2" (e.g. '2-3')
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('chainbondmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('chainbondmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="chainbondmoleculecontextexample">
						<code>chainBond('2-3')</code> and <code>chainBond(bond(1, 2))</code> both return <code>true</code> if atoms <code>1</code>
		and <code>2</code> are connected by a chain bond the input molecule
					</div>

					<a class="fakelink" onclick="toggle('chainbondreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('chainbondreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="chainbondreactioncontextexample" style="display:none;">
						<code>chainBond(reactant(0), bond(ratom(1), ratom(2)))</code> returns <code>true</code> if reactant atoms matching maps <code>1</code>
		and <code>2</code> in the reaction equation are connected by a chain bond in the corresponding reactant molecule (<a href="#note1">see note 1</a>)
		<code>chainBond(product(1), bond(patom(2), patom(3)))</code> returns <code>true</code> if product atoms matching maps <code>2</code> and <code>3</code> in the
		reaction equation are connected by a chain bond in the corresponding product molecule (<a href="#note1">see note 1</a>)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="chainbondcount"/><a name="chainbondcountdesc"/><a name="chainbondcountex"/>chainBondCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the chain bond count</td>
				<td>the chain bond count</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('chainbondcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('chainbondcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="chainbondcountmoleculecontextexample">
						<code>chainBondCount()</code> returns the number of chain bonds in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('chainbondcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('chainbondcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="chainbondcountreactioncontextexample" style="display:none;">
						<code>chainBondCount(reactant(0))</code> returns the number of chain bonds in the first reactant <code>chainBondCount(product(1))</code>
		returns the number of chain bonds in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="chiralcenter"/><a name="chiralcenterdesc"/><a name="chiralcenterex"/>chiralCenter</td>
				<td>Geometry Plugin Group</td>
				<td>checks if the specified atom is a tetrahedral stereogenic center</td>
				<td><code>true</code> for tetrahedral stereogenic center atoms</td>
				<td><ul>
<li>the atom index / MolAtom object
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('chiralcentermoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('chiralcentermoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="chiralcentermoleculecontextexample">
						<code>chiralCenter(2)</code> returns <code>true</code> if atom <code>2</code> of the input molecule is a
		tetrahedral stereogenic center, <code>false</code> otherwise
					</div>

					<a class="fakelink" onclick="toggle('chiralcenterreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('chiralcenterreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="chiralcenterreactioncontextexample" style="display:none;">
						<code>chiralCenter(ratom(2))</code> returns <code>true</code> if the reactant atom matching map <code>2</code>
		in the reaction equation is a tetrahedral stereogenic center, <code>false</code> otherwise <code>chiralCenter(patom(1))</code>
		returns <code>true</code> if the product atom matching map <code>1</code> in the reaction equation is a tetrahedral
		stereogenic center, <code>false</code> otherwise
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="chiralcentercount"/><a name="chiralcentercountdesc"/><a name="chiralcentercountex"/>chiralCenterCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of tetrahedral stereogenic center atoms</td>
				<td>the tetrahedral stereogenic center count</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('chiralcentercountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('chiralcentercountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="chiralcentercountmoleculecontextexample">
						<code>chiralCenterCount()</code> returns the number of tetrahedral stereogenic centers in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('chiralcentercountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('chiralcentercountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="chiralcentercountreactioncontextexample" style="display:none;">
						<code>chiralCenterCount(reactant(0))</code> returns the number of tetrahedral stereogenic centers in the first
		reactant <code>chiralCenterCount(product(1))</code> returns the number of tetrahedral stereogenic centers in the second
		product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="chiralcenters"/><a name="chiralcentersdesc"/><a name="chiralcentersex"/>chiralCenters</td>
				<td>Geometry Plugin Group</td>
				<td>determines the chiral center atoms</td>
				<td>indexes of chiral center atoms</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('chiralcentersmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('chiralcentersmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="chiralcentersmoleculecontextexample">
						<code>chiralCenters()</code> returns the indexes of chiral center atoms in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('chiralcentersreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('chiralcentersreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="chiralcentersreactioncontextexample" style="display:none;">
						<code>chiralCenters(reactant(0))</code> returns the indexes of chiral center atoms in the first
		reactant <code>chiralCenters(product(1))</code> returns the indexes of chiral center atoms in the second
		product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="connected"/><a name="connecteddesc"/><a name="connectedex"/>connected</td>
				<td>Geometry Plugin Group</td>
				<td>checks if two atoms are connected</td>
				<td><code>true</code> if the two atoms belong to the same connected component, <code>false</code> otherwise</td>
				<td><ul>
<li>the (1-based) atom indexes of the two atoms in a string: "index1-index2" (e.g. '2-3')
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('connectedmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('connectedmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="connectedmoleculecontextexample">
						<code>connected('2-3')</code> and <code>connected(pair(1, 2))</code> both return <code>true</code> if atoms <code>1</code>
		and <code>2</code> are in the same connected component of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('connectedreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('connectedreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="connectedreactioncontextexample" style="display:none;">
						<code>connected(reactant(0), pair(ratom(1), ratom(2)))</code> returns <code>true</code> if reactant atoms matching maps <code>1</code>
		and <code>2</code> in the reaction equation are connected in the corresponding reactant molecule (<a href="#note1">see note 1</a>)
		<code>connected(product(1), pair(patom(2), patom(3)))</code> returns <code>true</code> if product atoms matching maps <code>2</code>
		and <code>3</code> in the reaction equation are connected in the corresponding product molecule (<a href="#note1">see note 1</a>)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="connectedgraph"/><a name="connectedgraphdesc"/><a name="connectedgraphex"/>connectedGraph</td>
				<td>Geometry Plugin Group</td>
				<td>checks whether the molecule graph is connected</td>
				<td><code>true</code> if the molecule graph is connected, <code>false</code> otherwise</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('connectedgraphmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('connectedgraphmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="connectedgraphmoleculecontextexample">
						<code>connectedGraph()</code> returns <code>true</code> if the input molecule graph is connected
					</div>

					<a class="fakelink" onclick="toggle('connectedgraphreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('connectedgraphreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="connectedgraphreactioncontextexample" style="display:none;">
						<code>connectedGraph(reactant(0))</code> returns <code>true</code> if the first reactant is connected <code>connectedGraph(product(1))</code>
		returns <code>true</code> if the second product is connected
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="cyclomaticnumber"/><a name="cyclomaticnumberdesc"/><a name="cyclomaticnumberex"/>cyclomaticNumber</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the cyclomatic number</td>
				<td>the cyclomatic number</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('cyclomaticnumbermoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('cyclomaticnumbermoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="cyclomaticnumbermoleculecontextexample">
						<code>cyclomaticNumber()</code> returns the cyclomatic number of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('cyclomaticnumberreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('cyclomaticnumberreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="cyclomaticnumberreactioncontextexample" style="display:none;">
						<code>cyclomaticNumber(reactant(0))</code> returns the cyclomatic number of the first reactant <code>cyclomaticNumber(product(1))</code>
		returns the cyclomatic number of the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="dihedral"/><a name="dihedraldesc"/><a name="dihedralex"/>dihedral</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the dihedral of four atoms</td>
				<td>the dihedral of four atoms</td>
				<td><ul>
<li>the (1-based) atom indexes of the four atoms in a string: "index1-index2-index3-index4" (e.g. '2-3-7-4')
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('dihedralmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('dihedralmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="dihedralmoleculecontextexample">
						<code>dihedral('1-2-3-4')</code> and <code>dihedral(atoms(0, 1, 2, 3))</code> both return the dihedral of atoms <code>1</code>,
		<code>2</code>, <code>3</code> and <code>4</code> in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('dihedralreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('dihedralreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="dihedralreactioncontextexample" style="display:none;">
						<code>dihedral(reactant(0), atoms(ratom(1), ratom(2), ratom(3), ratom(4)))</code> returns the dihedral of reactant atoms
		matching maps <code>1</code>, <code>2</code>, <code>3</code> and <code>4</code> in the reaction equation (<a href="#note1">see note 1</a>)
		<code>dihedral(product(1), atoms(patom(2), patom(3), patom(5), patom(6)))</code> returns the dihedral of product atoms matching maps <code>2</code>,
		<code>3</code>, <code>5</code> and <code>6</code> in the reaction equation (<a href="#note1">see note 1</a>)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="distance"/><a name="distancedesc"/><a name="distanceex"/>distance</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the distance between two atoms</td>
				<td>the distance between two atoms</td>
				<td><ul>
<li>the (1-based) atom indexes of the two atoms in a string: "index1-index2" (e.g. '2-3')
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('distancemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('distancemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="distancemoleculecontextexample">
						<code>distance('1-2')</code> and <code>distance(pair(0, 1))</code> both return the distance between atoms <code>1</code>
		and <code>2</code> in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('distancereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('distancereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="distancereactioncontextexample" style="display:none;">
						<code>distance(reactant(0), pair(ratom(1), ratom(2)))</code> returns the distance between reactant atoms matching maps <code>1</code>
		and <code>2</code> in the reaction equation (<a href="#note1">see note 1</a>)
		<code>distance(product(1), pair(patom(2), patom(3)))</code> returns the distance between product atoms matching maps <code>2</code> and <code>3</code>
		in the reaction equation (<a href="#note1">see note 1</a>)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="distancedegree"/><a name="distancedegreedesc"/><a name="distancedegreeex"/>distanceDegree</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the distance degree of an atom</td>
				<td>the distance degree</td>
				<td><ul>
<li>the atom index / MolAtom object
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('distancedegreemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('distancedegreemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="distancedegreemoleculecontextexample">
						<code>distanceDegree(2)</code> returns the distance degree of atom <code>2</code> of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('distancedegreereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('distancedegreereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="distancedegreereactioncontextexample" style="display:none;">
						<code>distanceDegree(ratom(2))</code> returns the distance degree of the reactant atom matching map <code>2</code>
		in the reaction equation <code>distanceDegree(patom(1))</code> returns the distance degree of the product atom matching
		map <code>1</code> in the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="dreidingenergy"/><a name="dreidingenergydesc"/><a name="dreidingenergyex"/>dreidingEnergy</td>
				<td>Geometry Plugin Group</td>
				<td>returns the Dreiding energy of the input molecule (conformer)</td>
				<td>the dreiding energy</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('dreidingenergymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('dreidingenergymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="dreidingenergymoleculecontextexample">
						<code>dreidingEnergy()</code> returns the dreiding energy of the input molecule (conformer)
					</div>

					<a class="fakelink" onclick="toggle('dreidingenergyreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('dreidingenergyreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="dreidingenergyreactioncontextexample" style="display:none;">
						<code>dreidingEnergy(reactant(0))</code> returns the dreiding energy of the first reactant <code>dreidingEnergy(product(1))</code>
		returns the dreiding energy of the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="eccentricity"/><a name="eccentricitydesc"/><a name="eccentricityex"/>eccentricity</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the eccentricity of an atom</td>
				<td>the eccentricity of an atom</td>
				<td><ul>
<li>the atom index / MolAtom object
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('eccentricitymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('eccentricitymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="eccentricitymoleculecontextexample">
						<code>eccentricity(2)</code> returns the eccentricity of atom <code>2</code> of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('eccentricityreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('eccentricityreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="eccentricityreactioncontextexample" style="display:none;">
						<code>eccentricity(ratom(2))</code> returns the eccentricity of the reactant atom matching map <code>2</code>
		in the reaction equation <code>eccentricity(patom(1))</code> returns the distance degree of the product atom matching
		map <code>1</code> in the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="fragmentcount"/><a name="fragmentcountdesc"/><a name="fragmentcountex"/>fragmentCount</td>
				<td>Geometry Plugin Group</td>
				<td>returns the number of fragments (disconnected parts)</td>
				<td>the fragment count</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('fragmentcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('fragmentcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="fragmentcountmoleculecontextexample">
						<code>fragmentCount()</code> returns the number of fragments in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('fragmentcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('fragmentcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="fragmentcountreactioncontextexample" style="display:none;">
						<code>fragmentCount(reactant(0))</code> returns the number of fragments in the first reactant <code>fragmentCount(product(1))</code>
		returns the number of fragments in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="fusedaliphaticringcount"/><a name="fusedaliphaticringcountdesc"/><a name="fusedaliphaticringcountex"/>fusedAliphaticRingCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of fused aliphatic rings<br />
		(SSSR smallest set of smallest aliphatic rings)</td>
				<td>the fused aliphatic ring count</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('fusedaliphaticringcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('fusedaliphaticringcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="fusedaliphaticringcountmoleculecontextexample">
						<code>fusedAliphaticRingCount()</code> returns the number of fused aliphatic rings in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('fusedaliphaticringcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('fusedaliphaticringcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="fusedaliphaticringcountreactioncontextexample" style="display:none;">
						<code>fusedAliphaticRingCount(reactant(0))</code> returns the number of fused aliphatic rings in the first
		reactant <code>fusedAliphaticRingCount(product(1))</code> returns the number of fused aliphatic rings in the second
		product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="fusedaliphaticringcountofsize"/><a name="fusedaliphaticringcountofsizedesc"/><a name="fusedaliphaticringcountofsizeex"/>fusedAliphaticRingCountOfSize</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of fused aliphatic rings of given size</td>
				<td>the number of fused aliphatic rings of given size</td>
				<td>the ring size</td>
				<td>
					<a class="fakelink" onclick="toggle('fusedaliphaticringcountofsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('fusedaliphaticringcountofsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="fusedaliphaticringcountofsizemoleculecontextexample">
						<code>fusedAliphaticRingCountOfSize(6)</code> returns the number of fused aliphatic rings of size 6 in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('fusedaliphaticringcountofsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('fusedaliphaticringcountofsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="fusedaliphaticringcountofsizereactioncontextexample" style="display:none;">
						<code>fusedAliphaticRingCountOfSize(reactant(0), 5)</code> returns the number of fused aliphatic rings of size 5 in the first reactant
		<code>fusedAliphaticRingCountOfSize(product(1), 5)</code> returns the number of fused aliphatic rings of size 5 in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="fusedaliphaticrings"/><a name="fusedaliphaticringsdesc"/><a name="fusedaliphaticringsex"/>fusedAliphaticRings</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the fused aliphatic rings in the molecule</td>
				<td>atom indexes of the fused aliphatic rings in the molecule (null if the molecule does not contain fused aliphatic rings)</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('fusedaliphaticringsmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('fusedaliphaticringsmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="fusedaliphaticringsmoleculecontextexample">
						<code>fusedAliphaticRings()</code> returns the atom indexes of the fused aliphatic rings in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('fusedaliphaticringsreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('fusedaliphaticringsreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="fusedaliphaticringsreactioncontextexample" style="display:none;">
						<code>fusedAliphaticRings(reactant(0))</code> returns the atom indexes of the fused aliphatic rings in the first reactant <code>fusedAliphaticRings(product(1))</code>
		returns the atom indexes of the fused aliphatic rings in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="fusedaliphaticringsofsize"/><a name="fusedaliphaticringsofsizedesc"/><a name="fusedaliphaticringsofsizeex"/>fusedAliphaticRingsOfSize</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the fused aliphatic rings in the molecule having a given size (number of atoms)</td>
				<td>atom indexes of the fused aliphatic rings in the molecule having the given size (null if the molecule does not contain fused aliphatic rings)</td>
				<td>the ring size</td>
				<td>
					<a class="fakelink" onclick="toggle('fusedaliphaticringsofsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('fusedaliphaticringsofsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="fusedaliphaticringsofsizemoleculecontextexample">
						<code>fusedAliphaticRings()</code> returns the atom indexes of the fused aliphatic rings in the input molecule having the given size
					</div>

					<a class="fakelink" onclick="toggle('fusedaliphaticringsofsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('fusedaliphaticringsofsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="fusedaliphaticringsofsizereactioncontextexample" style="display:none;">
						<code>fusedAliphaticRings(reactant(0))</code> returns the atom indexes of the fused aliphatic rings in the first reactant having the given size <code>fusedAliphaticRings(product(1))</code>
		returns the atom indexes of the fused aliphatic rings in the second product having the given size
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="fusedaromaticringcount"/><a name="fusedaromaticringcountdesc"/><a name="fusedaromaticringcountex"/>fusedAromaticRingCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of fused aromatic rings<br />
		(SSSR smallest set of smallest aromatic rings)</td>
				<td>the fused aromatic ring count</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('fusedaromaticringcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('fusedaromaticringcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="fusedaromaticringcountmoleculecontextexample">
						<code>fusedAromaticRingCount()</code> returns the number of fused aromatic rings in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('fusedaromaticringcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('fusedaromaticringcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="fusedaromaticringcountreactioncontextexample" style="display:none;">
						<code>fusedAromaticRingCount(reactant(0))</code> returns the number of fused aromatic rings in the first
		reactant <code>fusedAromaticRingCount(product(1))</code> returns the number of fused aromatic rings in the second
		product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="fusedaromaticringcountofsize"/><a name="fusedaromaticringcountofsizedesc"/><a name="fusedaromaticringcountofsizeex"/>fusedAromaticRingCountOfSize</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of fused aromatic rings of given size</td>
				<td>the number of fused aromatic rings of given size</td>
				<td>the ring size</td>
				<td>
					<a class="fakelink" onclick="toggle('fusedaromaticringcountofsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('fusedaromaticringcountofsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="fusedaromaticringcountofsizemoleculecontextexample">
						<code>fusedAromaticRingCountOfSize(6)</code> returns the number of fused aromatic rings of size 6 in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('fusedaromaticringcountofsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('fusedaromaticringcountofsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="fusedaromaticringcountofsizereactioncontextexample" style="display:none;">
						<code>fusedAromaticRingCountOfSize(reactant(0), 5)</code> returns the number of fused aromatic rings of size 5 in the first reactant
		<code>fusedAromaticRingCountOfSize(product(1), 5)</code> returns the number of fused aromatic rings of size 5 in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="fusedaromaticrings"/><a name="fusedaromaticringsdesc"/><a name="fusedaromaticringsex"/>fusedAromaticRings</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the fused aromatic rings in the molecule</td>
				<td>atom indexes of the fused aromatic rings in the molecule (null if the molecule does not contain fused aromatic rings)</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('fusedaromaticringsmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('fusedaromaticringsmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="fusedaromaticringsmoleculecontextexample">
						<code>fusedAromaticRings()</code> returns the atom indexes of the fused aromatic rings in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('fusedaromaticringsreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('fusedaromaticringsreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="fusedaromaticringsreactioncontextexample" style="display:none;">
						<code>fusedAromaticRings(reactant(0))</code> returns the atom indexes of the fused aromatic rings in the first reactant <code>fusedAromaticRings(product(1))</code>
		returns the atom indexes of the fused aromatic rings in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="fusedaromaticringsofsize"/><a name="fusedaromaticringsofsizedesc"/><a name="fusedaromaticringsofsizeex"/>fusedAromaticRingsOfSize</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the fused aromatic rings in the molecule having a given size (number of atoms)</td>
				<td>atom indexes of the fused aromatic rings in the molecule having the given size (null if the molecule does not contain fused aromatic rings)</td>
				<td>the ring size</td>
				<td>
					<a class="fakelink" onclick="toggle('fusedaromaticringsofsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('fusedaromaticringsofsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="fusedaromaticringsofsizemoleculecontextexample">
						<code>fusedAromaticRings()</code> returns the atom indexes of the fused aromatic rings in the input molecule having the given size
					</div>

					<a class="fakelink" onclick="toggle('fusedaromaticringsofsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('fusedaromaticringsofsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="fusedaromaticringsofsizereactioncontextexample" style="display:none;">
						<code>fusedAromaticRings(reactant(0))</code> returns the atom indexes of the fused aromatic rings in the first reactant having the given size <code>fusedAromaticRings(product(1))</code>
		returns the atom indexes of the fused aromatic rings in the second product having the given size
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="fusedringcount"/><a name="fusedringcountdesc"/><a name="fusedringcountex"/>fusedRingCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of fused rings<br />
		(SSSR smallest set of smallest rings)</td>
				<td>the fused ring count</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('fusedringcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('fusedringcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="fusedringcountmoleculecontextexample">
						<code>fusedRingCount()</code> returns the number of fused rings in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('fusedringcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('fusedringcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="fusedringcountreactioncontextexample" style="display:none;">
						<code>fusedRingCount(reactant(0))</code> returns the number of fused rings in the first reactant <code>fusedRingCount(product(1))</code>
		returns the number of fused rings in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="hararyindex"/><a name="hararyindexdesc"/><a name="hararyindexex"/>hararyIndex</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the Harary index</td>
				<td>the Harary index</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('hararyindexmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('hararyindexmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="hararyindexmoleculecontextexample">
						calculates the Harary index
					</div>

					<a class="fakelink" onclick="toggle('hararyindexreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('hararyindexreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="hararyindexreactioncontextexample" style="display:none;">
						the Harary index
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="heteroaliphaticringcount"/><a name="heteroaliphaticringcountdesc"/><a name="heteroaliphaticringcountex"/>heteroaliphaticRingCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of aliphatic heterocyclic rings<br />
		(SSSR smallest set of smallest aliphatic rings)</td>
				<td>the aliphatic heterocyclic ring count</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('heteroaliphaticringcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('heteroaliphaticringcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="heteroaliphaticringcountmoleculecontextexample">
						<code>heteroaliphaticRingCount()</code> returns the number of aliphatic heterocyclic rings in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('heteroaliphaticringcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('heteroaliphaticringcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="heteroaliphaticringcountreactioncontextexample" style="display:none;">
						<code>heteroaliphaticRingCount(reactant(0))</code> returns the number of aliphatic heterocyclic rings in the first
		reactant <code>heteroaliphaticRingCount(product(1))</code> returns the number of aliphatic heterocyclic rings in the second
		product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="heteroaliphaticringcountofsize"/><a name="heteroaliphaticringcountofsizedesc"/><a name="heteroaliphaticringcountofsizeex"/>heteroaliphaticRingCountOfSize</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of aliphatic heterocyclic rings of given size</td>
				<td>the number of aliphatic heterocyclic rings of given size</td>
				<td>the ring size</td>
				<td>
					<a class="fakelink" onclick="toggle('heteroaliphaticringcountofsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('heteroaliphaticringcountofsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="heteroaliphaticringcountofsizemoleculecontextexample">
						<code>heteroaliphaticRingCountOfSize(6)</code> returns the number of aliphatic heterocyclic rings of size 6 in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('heteroaliphaticringcountofsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('heteroaliphaticringcountofsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="heteroaliphaticringcountofsizereactioncontextexample" style="display:none;">
						<code>heteroaliphaticRingCountOfSize(reactant(0), 5)</code> returns the number of aliphatic heterocyclic rings of size 5 in the first reactant
		<code>heteroaliphaticRingCountOfSize(product(1), 5)</code> returns the number of aliphatic heterocyclic rings of size 5 in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="heteroaliphaticrings"/><a name="heteroaliphaticringsdesc"/><a name="heteroaliphaticringsex"/>heteroaliphaticRings</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the aliphatic heterocyclic rings in the molecule</td>
				<td>atom indexes of the aliphatic heterocyclic rings in the molecule (null if the molecule does not contain aliphatic heterocyclic rings)</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('heteroaliphaticringsmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('heteroaliphaticringsmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="heteroaliphaticringsmoleculecontextexample">
						<code>heteroaliphaticRings()</code> returns the atom indexes of the aliphatic heterocyclic rings in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('heteroaliphaticringsreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('heteroaliphaticringsreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="heteroaliphaticringsreactioncontextexample" style="display:none;">
						<code>heteroaliphaticRings(reactant(0))</code> returns the atom indexes of the aliphatic heterocyclic rings in the first reactant <code>heteroaliphaticRings(product(1))</code>
		returns the atom indexes of the aliphatic heterocyclic rings in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="heteroaliphaticringsofsize"/><a name="heteroaliphaticringsofsizedesc"/><a name="heteroaliphaticringsofsizeex"/>heteroaliphaticRingsOfSize</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the aliphatic heterocyclic rings in the molecule having a given size (number of atoms)</td>
				<td>atom indexes of the aliphatic heterocyclic rings in the molecule having the given size (null if the molecule does not contain aliphatic heterocyclic rings)</td>
				<td>the ring size</td>
				<td>
					<a class="fakelink" onclick="toggle('heteroaliphaticringsofsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('heteroaliphaticringsofsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="heteroaliphaticringsofsizemoleculecontextexample">
						<code>heteroaliphaticRings()</code> returns the atom indexes of the aliphatic heterocyclic rings in the input molecule having the given size
					</div>

					<a class="fakelink" onclick="toggle('heteroaliphaticringsofsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('heteroaliphaticringsofsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="heteroaliphaticringsofsizereactioncontextexample" style="display:none;">
						<code>heteroaliphaticRings(reactant(0))</code> returns the atom indexes of the aliphatic heterocyclic rings in the first reactant having the given size <code>heteroaliphaticRings(product(1))</code>
		returns the atom indexes of the aliphatic heterocyclic rings in the second product having the given size
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="heteroaromaticringcount"/><a name="heteroaromaticringcountdesc"/><a name="heteroaromaticringcountex"/>heteroaromaticRingCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of aromatic heterocyclic rings<br />
		(SSSR smallest set of smallest aromatic rings)</td>
				<td>the aromatic heterocyclic ring count</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('heteroaromaticringcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('heteroaromaticringcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="heteroaromaticringcountmoleculecontextexample">
						<code>heteroaromaticRingCount()</code> returns the number of aromatic heterocyclic rings in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('heteroaromaticringcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('heteroaromaticringcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="heteroaromaticringcountreactioncontextexample" style="display:none;">
						<code>heteroaromaticRingCount(reactant(0))</code> returns the number of aromatic heterocyclic rings in the first
		reactant <code>heteroaromaticRingCount(product(1))</code> returns the number of aromatic heterocyclic rings in the second
		product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="heteroaromaticringcountofsize"/><a name="heteroaromaticringcountofsizedesc"/><a name="heteroaromaticringcountofsizeex"/>heteroaromaticRingCountOfSize</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of aromatic heterocyclic rings of given size</td>
				<td>the number of aromatic heterocyclic rings of given size</td>
				<td>the ring size</td>
				<td>
					<a class="fakelink" onclick="toggle('heteroaromaticringcountofsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('heteroaromaticringcountofsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="heteroaromaticringcountofsizemoleculecontextexample">
						<code>heteroaromaticRingCountOfSize(6)</code> returns the number of aromatic heterocyclic rings of size 6 in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('heteroaromaticringcountofsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('heteroaromaticringcountofsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="heteroaromaticringcountofsizereactioncontextexample" style="display:none;">
						<code>heteroaromaticRingCountOfSize(reactant(0), 5)</code> returns the number of aromatic heterocyclic rings of size 5 in the first reactant
		<code>heteroaromaticRingCountOfSize(product(1), 5)</code> returns the number of aromatic heterocyclic rings of size 5 in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="heteroaromaticrings"/><a name="heteroaromaticringsdesc"/><a name="heteroaromaticringsex"/>heteroaromaticRings</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the aromatic heterocyclic rings in the molecule</td>
				<td>atom indexes of the aromatic heterocyclic rings in the molecule (null if the molecule does not contain aromatic heterocyclic rings)</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('heteroaromaticringsmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('heteroaromaticringsmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="heteroaromaticringsmoleculecontextexample">
						<code>heteroaromaticRings()</code> returns the atom indexes of the aromatic heterocyclic rings in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('heteroaromaticringsreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('heteroaromaticringsreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="heteroaromaticringsreactioncontextexample" style="display:none;">
						<code>heteroaromaticRings(reactant(0))</code> returns the atom indexes of the aromatic heterocyclic rings in the first reactant <code>heteroaromaticRings(product(1))</code>
		returns the atom indexes of the aromatic heterocyclic rings in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="heteroaromaticringsofsize"/><a name="heteroaromaticringsofsizedesc"/><a name="heteroaromaticringsofsizeex"/>heteroaromaticRingsOfSize</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the aromatic heterocyclic rings in the molecule having a given size (number of atoms)</td>
				<td>atom indexes of the aromatic heterocyclic rings in the molecule having the given size (null if the molecule does not contain aromatic heterocyclic rings)</td>
				<td>the ring size</td>
				<td>
					<a class="fakelink" onclick="toggle('heteroaromaticringsofsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('heteroaromaticringsofsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="heteroaromaticringsofsizemoleculecontextexample">
						<code>heteroaromaticRings()</code> returns the atom indexes of the aromatic heterocyclic rings in the input molecule having the given size
					</div>

					<a class="fakelink" onclick="toggle('heteroaromaticringsofsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('heteroaromaticringsofsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="heteroaromaticringsofsizereactioncontextexample" style="display:none;">
						<code>heteroaromaticRings(reactant(0))</code> returns the atom indexes of the aromatic heterocyclic rings in the first reactant having the given size <code>heteroaromaticRings(product(1))</code>
		returns the atom indexes of the aromatic heterocyclic rings in the second product having the given size
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="heteroringcount"/><a name="heteroringcountdesc"/><a name="heteroringcountex"/>heteroRingCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of heterocyclic rings<br />
		(SSSR smallest set of smallest rings)</td>
				<td>the heterocyclic ring count</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('heteroringcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('heteroringcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="heteroringcountmoleculecontextexample">
						<code>heteroRingCount()</code> returns the number of heterocyclic rings in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('heteroringcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('heteroringcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="heteroringcountreactioncontextexample" style="display:none;">
						<code>heteroRingCount(reactant(0))</code> returns the number of heterocyclic rings in the first
		reactant <code>heteroRingCount(product(1))</code> returns the number of heterocyclic rings in the second
		product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="heteroringcountofsize"/><a name="heteroringcountofsizedesc"/><a name="heteroringcountofsizeex"/>heteroRingCountOfSize</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of heterocyclic rings of given size</td>
				<td>the number of heterocyclic rings of given size</td>
				<td>the ring size</td>
				<td>
					<a class="fakelink" onclick="toggle('heteroringcountofsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('heteroringcountofsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="heteroringcountofsizemoleculecontextexample">
						<code>heteroRingCountOfSize(6)</code> returns the number of heterocyclic rings of size 6 in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('heteroringcountofsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('heteroringcountofsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="heteroringcountofsizereactioncontextexample" style="display:none;">
						<code>heteroRingCountOfSize(reactant(0), 5)</code> returns the number of heterocyclic rings of size 5 in the first reactant
		<code>heteroRingCountOfSize(product(1), 5)</code> returns the number of heterocyclic rings of size 5 in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="heterorings"/><a name="heteroringsdesc"/><a name="heteroringsex"/>heteroRings</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the heterocyclic rings in the molecule</td>
				<td>atom indexes of the heterocyclic rings in the molecule (null if the molecule does not contain heterocyclic rings)</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('heteroringsmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('heteroringsmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="heteroringsmoleculecontextexample">
						<code>heteroRings()</code> returns the atom indexes of the heterocyclic rings in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('heteroringsreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('heteroringsreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="heteroringsreactioncontextexample" style="display:none;">
						<code>heteroRings(reactant(0))</code> returns the atom indexes of the heterocyclic rings in the first reactant <code>heteroRings(product(1))</code>
		returns the atom indexes of the heterocyclic rings in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="heteroringsofsize"/><a name="heteroringsofsizedesc"/><a name="heteroringsofsizeex"/>heteroRingsOfSize</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the heterocyclic rings in the molecule having a given size (number of atoms)</td>
				<td>atom indexes of the heterocyclic rings in the molecule having the given size (null if the molecule does not contain heterocyclic rings)</td>
				<td>the ring size</td>
				<td>
					<a class="fakelink" onclick="toggle('heteroringsofsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('heteroringsofsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="heteroringsofsizemoleculecontextexample">
						<code>heteroRings()</code> returns the atom indexes of the heterocyclic rings in the input molecule having the given size
					</div>

					<a class="fakelink" onclick="toggle('heteroringsofsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('heteroringsofsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="heteroringsofsizereactioncontextexample" style="display:none;">
						<code>heteroRings(reactant(0))</code> returns the atom indexes of the heterocyclic rings in the first reactant having the given size <code>heteroRings(product(1))</code>
		returns the atom indexes of the heterocyclic rings in the second product having the given size
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="hyperwienerindex"/><a name="hyperwienerindexdesc"/><a name="hyperwienerindexex"/>hyperWienerIndex</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the Hyper Wiener index</td>
				<td>the Hyper Wiener index</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('hyperwienerindexmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('hyperwienerindexmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="hyperwienerindexmoleculecontextexample">
						calculates the Hyper Wiener index
					</div>

					<a class="fakelink" onclick="toggle('hyperwienerindexreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('hyperwienerindexreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="hyperwienerindexreactioncontextexample" style="display:none;">
						the Hyper Wiener index
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="largestatomringsize"/><a name="largestatomringsizedesc"/><a name="largestatomringsizeex"/>largestAtomRingSize</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the size of the largest ring containing the specified atom</td>
				<td>the size of the largest ring containing the specified atom</td>
				<td><ul>
<li>the atom index / MolAtom object
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('largestatomringsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('largestatomringsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="largestatomringsizemoleculecontextexample">
						<code>largestAtomRingSize(1)</code> returns the size of the largest ring containing atom <code>1</code>
		in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('largestatomringsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('largestatomringsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="largestatomringsizereactioncontextexample" style="display:none;">
						<code>largestAtomRingSize(patom(2))</code> returns the size of the largest ring containing product atom
		matching map <code>2</code> in the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="largestring"/><a name="largestringdesc"/><a name="largestringex"/>largestRing</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the atoms of the largest ring (number of atoms) in the molecule.</td>
				<td>atom indexes of the largest ring in the molecule (null when acyclic)</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('largestringmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('largestringmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="largestringmoleculecontextexample">
						<code>largestRingSize()</code> returns the atom indexes of the largest ring in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('largestringreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('largestringreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="largestringreactioncontextexample" style="display:none;">
						<code>largestRingSize(product(1))</code> returns the atom indexes of the largest ring in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="largestringsize"/><a name="largestringsizedesc"/><a name="largestringsizeex"/>largestRingSize</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the largest ring size</td>
				<td>the largest ring size</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('largestringsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('largestringsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="largestringsizemoleculecontextexample">
						<code>largestRingSize()</code> returns the size of the largest ring in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('largestringsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('largestringsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="largestringsizereactioncontextexample" style="display:none;">
						<code>largestRingSize(product(1))</code> returns the size of the largest ring in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="largestringsystem"/><a name="largestringsystemdesc"/><a name="largestringsystemex"/>largestRingSystem</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the atoms of the largest ring system (number of rings) in the molecule.</td>
				<td>atom indexes of the largest ring system in the molecule (null when acyclic)</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('largestringsystemmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('largestringsystemmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="largestringsystemmoleculecontextexample">
						<code>largestRingSize()</code> returns the atom indexes of the largest ring system in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('largestringsystemreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('largestringsystemreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="largestringsystemreactioncontextexample" style="display:none;">
						<code>largestRingSize(product(1))</code> returns the atom indexes of the largest ring system in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="largestringsystemsize"/><a name="largestringsystemsizedesc"/><a name="largestringsystemsizeex"/>largestRingSystemSize</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the size of the largest ring system (number of rings)</td>
				<td>the size of the largest ring system</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('largestringsystemsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('largestringsystemsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="largestringsystemsizemoleculecontextexample">
						<code>largestRingSystemSize()</code> returns the size of the largest ring system in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('largestringsystemsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('largestringsystemsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="largestringsystemsizereactioncontextexample" style="display:none;">
						<code>largestRingSystemSize(reactant(0))</code> returns the size of the largest ring system in the first reactant <code>largestringsystemsize(product(1))</code>
		returns the size of the largest ring system in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="maximalprojectionarea"/><a name="maximalprojectionareadesc"/><a name="maximalprojectionareaex"/>maximalProjectionArea</td>
				<td>Geometry Plugin Group</td>
				<td>returns the maximal projection area</td>
				<td>the maximal projection area</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('maximalprojectionareamoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('maximalprojectionareamoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="maximalprojectionareamoleculecontextexample">
						<code>maximalProjectionArea()</code> returns the maximal projection area
					</div>

					<a class="fakelink" onclick="toggle('maximalprojectionareareactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('maximalprojectionareareactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="maximalprojectionareareactioncontextexample" style="display:none;">
						<code>maximalProjectionArea((reactant(0))</code> returns the maximal projection area of the first reactant
		<code>maximalProjectionArea(product(1))</code> returns the maximal projection area of the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="maximalprojectionradius"/><a name="maximalprojectionradiusdesc"/><a name="maximalprojectionradiusex"/>maximalProjectionRadius</td>
				<td>Geometry Plugin Group</td>
				<td>returns the maximal projection radius</td>
				<td>the maximal projection radius</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('maximalprojectionradiusmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('maximalprojectionradiusmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="maximalprojectionradiusmoleculecontextexample">
						<code>maximalProjectionRadius()</code> returns the maximal projection radius
					</div>

					<a class="fakelink" onclick="toggle('maximalprojectionradiusreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('maximalprojectionradiusreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="maximalprojectionradiusreactioncontextexample" style="display:none;">
						<code>maximalProjectionRadius((reactant(0))</code> returns the maximal projection radius of the first reactant
		<code>maximalProjectionRadius(product(1))</code> returns the maximal projection radius of the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="minimalprojectionarea"/><a name="minimalprojectionareadesc"/><a name="minimalprojectionareaex"/>minimalProjectionArea</td>
				<td>Geometry Plugin Group</td>
				<td>returns the minimal projection area</td>
				<td>the minimal projection area</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('minimalprojectionareamoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('minimalprojectionareamoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="minimalprojectionareamoleculecontextexample">
						<code>minimalProjectionArea()</code> returns the minimal projection area
					</div>

					<a class="fakelink" onclick="toggle('minimalprojectionareareactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('minimalprojectionareareactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="minimalprojectionareareactioncontextexample" style="display:none;">
						<code>minimalProjectionArea((reactant(0))</code> returns the minimal projection area of the first reactant
		<code>minimalProjectionArea(product(1))</code> returns the minimal projection area of the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="minimalprojectionradius"/><a name="minimalprojectionradiusdesc"/><a name="minimalprojectionradiusex"/>minimalProjectionRadius</td>
				<td>Geometry Plugin Group</td>
				<td>returns the minimal projection radius</td>
				<td>the minimal projection radius</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('minimalprojectionradiusmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('minimalprojectionradiusmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="minimalprojectionradiusmoleculecontextexample">
						<code>minimalProjectionRadius()</code> returns the minimal projection radius
					</div>

					<a class="fakelink" onclick="toggle('minimalprojectionradiusreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('minimalprojectionradiusreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="minimalprojectionradiusreactioncontextexample" style="display:none;">
						<code>minimalProjectionRadius((reactant(0))</code> returns the minimal projection radius of the first reactant
		<code>minimalProjectionRadius(product(1))</code> returns the minimal projection radius of the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="mmff94energy"/><a name="mmff94energydesc"/><a name="mmff94energyex"/>mmff94Energy</td>
				<td>Geometry Plugin Group</td>
				<td>returns the MMFF94 energy of the input molecule (conformer)</td>
				<td>the MMFF94 energy</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('mmff94energymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('mmff94energymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="mmff94energymoleculecontextexample">
						<code>mmff94Energy()</code> returns the MMFF94 energy of the input molecule (conformer)
					</div>

					<a class="fakelink" onclick="toggle('mmff94energyreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('mmff94energyreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="mmff94energyreactioncontextexample" style="display:none;">
						<code>mmff94Energy(reactant(0))</code> returns the MMFF94 energy of the first reactant <code>mmff94Energy(product(1))</code>
		returns the MMFF94 energy of the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="plattindex"/><a name="plattindexdesc"/><a name="plattindexex"/>plattIndex</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the Platt index</td>
				<td>the Platt index</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('plattindexmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('plattindexmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="plattindexmoleculecontextexample">
						calculates the Platt index
					</div>

					<a class="fakelink" onclick="toggle('plattindexreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('plattindexreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="plattindexreactioncontextexample" style="display:none;">
						the Platt index
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="randicindex"/><a name="randicindexdesc"/><a name="randicindexex"/>randicIndex</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the Randic index</td>
				<td>the Randic index</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('randicindexmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('randicindexmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="randicindexmoleculecontextexample">
						calculates the Randic index
					</div>

					<a class="fakelink" onclick="toggle('randicindexreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('randicindexreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="randicindexreactioncontextexample" style="display:none;">
						the Randic index
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="ringatom"/><a name="ringatomdesc"/><a name="ringatomex"/>ringAtom</td>
				<td>Geometry Plugin Group</td>
				<td>checks if the specified atom is a ring atom</td>
				<td><code>true</code> for ring atoms,<br />
<code>false</code> for non-ring atoms</td>
				<td><ul>
<li>the atom index / MolAtom object
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('ringatommoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('ringatommoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="ringatommoleculecontextexample">
						<code>ringAtom(2)</code> returns <code>true</code> if atom <code>2</code> of the input molecule is a ring atom,
		<code>false</code> otherwise
					</div>

					<a class="fakelink" onclick="toggle('ringatomreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('ringatomreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="ringatomreactioncontextexample" style="display:none;">
						<code>ringAtom(ratom(2))</code> returns <code>true</code> if the reactant atom matching map <code>2</code> in
		the reaction equation is a ring atom, <code>false</code> otherwise <code>ringAtom(patom(1))</code> returns <code>true</code>
		if the product atom matching map <code>1</code> in the reaction equation is a ring atom, <code>false</code> otherwise
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="ringatomcount"/><a name="ringatomcountdesc"/><a name="ringatomcountex"/>ringAtomCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the ring atom count</td>
				<td>the ring atom count</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('ringatomcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('ringatomcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="ringatomcountmoleculecontextexample">
						<code>ringAtomCount()</code> returns the number of ring atoms in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('ringatomcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('ringatomcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="ringatomcountreactioncontextexample" style="display:none;">
						<code>ringAtomCount(reactant(0))</code> returns the number of ring atoms in the first reactant <code>ringAtomCount(product(1))</code>
		returns the number of ring atoms in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="ringbond"/><a name="ringbonddesc"/><a name="ringbondex"/>ringBond</td>
				<td>Geometry Plugin Group</td>
				<td>checks if two atoms are connected by a ring bond</td>
				<td><code>true</code> if the two atoms are connected by a ring bond, <code>false</code> otherwise</td>
				<td><ul>
<li>the (1-based) atom indexes of the two atoms in a string: "index1-index2" (e.g. '2-3')
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('ringbondmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('ringbondmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="ringbondmoleculecontextexample">
						<code>ringBond('2-3')</code> and <code>ringBond(bond(1, 2))</code> both return <code>true</code> if atoms <code>1</code>
		and <code>2</code> are connected by a ring bond the input molecule
					</div>

					<a class="fakelink" onclick="toggle('ringbondreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('ringbondreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="ringbondreactioncontextexample" style="display:none;">
						<code>ringBond(reactant(0), bond(ratom(1), ratom(2)))</code> returns <code>true</code> if reactant atoms matching maps <code>1</code>
		and <code>2</code> in the reaction equation are connected by a ring bond in the corresponding reactant molecule (<a href="#note1">see note 1</a>)
		<code>ringBond(product(1), bond(patom(2), patom(3)))</code> returns <code>true</code> if product atoms matching maps <code>2</code> and <code>3</code>
		in the reaction equation are connected by a ring bond in the corresponding product molecule (<a href="#note1">see note 1</a>)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="ringbondcount"/><a name="ringbondcountdesc"/><a name="ringbondcountex"/>ringBondCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the ring bond count</td>
				<td>the ring bond count</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('ringbondcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('ringbondcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="ringbondcountmoleculecontextexample">
						<code>ringBondCount()</code> returns the number of ring bonds in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('ringbondcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('ringbondcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="ringbondcountreactioncontextexample" style="display:none;">
						<code>ringBondCount(reactant(0))</code> returns the number of ring bonds in the first reactant <code>ringBondCount(product(1))</code>
		returns the number of ring bonds in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="ringcount"/><a name="ringcountdesc"/><a name="ringcountex"/>ringCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the ring count</td>
				<td>the ring count</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('ringcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('ringcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="ringcountmoleculecontextexample">
						<code>ringCount()</code> returns the number of rings in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('ringcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('ringcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="ringcountreactioncontextexample" style="display:none;">
						<code>ringCount(reactant(0))</code> returns the number of rings in the first reactant <code>ringCount(product(1))</code>
		returns the number of rings in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="ringcountofatom"/><a name="ringcountofatomdesc"/><a name="ringcountofatomex"/>ringCountOfAtom</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of rings passing through an atom</td>
				<td>the number of rings passing through an atom</td>
				<td><ul>
<li>the atom index / MolAtom object
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('ringcountofatommoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('ringcountofatommoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="ringcountofatommoleculecontextexample">
						<code>ringCountOfAtom(2)</code> returns the number of rings passsing through atom <code>2</code> of the input
		molecule
					</div>

					<a class="fakelink" onclick="toggle('ringcountofatomreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('ringcountofatomreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="ringcountofatomreactioncontextexample" style="display:none;">
						<code>ringCountOfAtom(ratom(2))</code> returns the number of rings passsing through the reactant atom matching
		map <code>2</code> in the reaction equation <code>ringCountOfAtom(patom(1))</code> returns the number of rings passsing
		through the product atom matching map <code>1</code> in the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="ringcountofsize"/><a name="ringcountofsizedesc"/><a name="ringcountofsizeex"/>ringCountOfSize</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of rings of given size</td>
				<td>the number of rings of given size</td>
				<td>the ring size</td>
				<td>
					<a class="fakelink" onclick="toggle('ringcountofsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('ringcountofsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="ringcountofsizemoleculecontextexample">
						<code>ringCountOfSize(6)</code> returns the number of rings of size 6 in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('ringcountofsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('ringcountofsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="ringcountofsizereactioncontextexample" style="display:none;">
						<code>ringCountOfSize(reactant(0), 5)</code> returns the number of rings of size 5 in the first reactant
		<code>ringCountOfSize(product(1), 5)</code> returns the number of rings of size 5 in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="rings"/><a name="ringsdesc"/><a name="ringsex"/>rings</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the rings in the molecule</td>
				<td>atom indexes of the rings in the molecule (null if the molecule is acyclic)</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('ringsmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('ringsmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="ringsmoleculecontextexample">
						<code>rings()</code> returns the atom indexes of the rings in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('ringsreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('ringsreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="ringsreactioncontextexample" style="display:none;">
						<code>rings(reactant(0))</code> returns the atom indexes of the rings in the first reactant <code>rings(product(1))</code>
		returns the atom indexes of the rings in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="ringsofsize"/><a name="ringsofsizedesc"/><a name="ringsofsizeex"/>ringsOfSize</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the rings in the molecule having a given size (number of atoms)</td>
				<td>atom indexes of the rings in the molecule having the given size (null if the molecule is acyclic or contains different rings only)</td>
				<td>the ring size</td>
				<td>
					<a class="fakelink" onclick="toggle('ringsofsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('ringsofsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="ringsofsizemoleculecontextexample">
						<code>rings()</code> returns the atom indexes of the rings in the input molecule having the given size
					</div>

					<a class="fakelink" onclick="toggle('ringsofsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('ringsofsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="ringsofsizereactioncontextexample" style="display:none;">
						<code>rings(reactant(0))</code> returns the atom indexes of the rings in the first reactant having the given size <code>rings(product(1))</code>
		returns the atom indexes of the rings in the second product having the given size
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="ringsystemcount"/><a name="ringsystemcountdesc"/><a name="ringsystemcountex"/>ringSystemCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of rings systems</td>
				<td>the number of rings systems</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('ringsystemcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('ringsystemcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="ringsystemcountmoleculecontextexample">
						<code>ringSystemCount()</code> returns the number of ring systems in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('ringsystemcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('ringsystemcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="ringsystemcountreactioncontextexample" style="display:none;">
						<code>ringSystemCount(reactant(0))</code> returns the number of ring systems in the first reactant <code>ringsystemCount(product(1))</code>
		returns the number of ring systems in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="ringsystemcountofsize"/><a name="ringsystemcountofsizedesc"/><a name="ringsystemcountofsizeex"/>ringSystemCountOfSize</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of rings systems of given size</td>
				<td>the number of rings systems of given size</td>
				<td>the ring system size</td>
				<td>
					<a class="fakelink" onclick="toggle('ringsystemcountofsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('ringsystemcountofsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="ringsystemcountofsizemoleculecontextexample">
						<code>ringSystemCountOfSize(4)</code> returns the number of four-membered ring systems in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('ringsystemcountofsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('ringsystemcountofsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="ringsystemcountofsizereactioncontextexample" style="display:none;">
						<code>ringSystemCountOfSize(reactant(0),3)</code> returns the number of three-membered ring systems in the first reactant
		<code>ringsystemCountOfSize(product(1),3)</code> returns the number of three-membered ring systems in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="ringsystems"/><a name="ringsystemsdesc"/><a name="ringsystemsex"/>ringSystems</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the ring systems in the molecule (fused and spiro rings belong to one ring system)</td>
				<td>atom indexes of the ring systems in the molecule (null if the molecule is acyclic)</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('ringsystemsmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('ringsystemsmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="ringsystemsmoleculecontextexample">
						<code>ringSystems()</code> returns the atom indexes of the ring systems in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('ringsystemsreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('ringsystemsreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="ringsystemsreactioncontextexample" style="display:none;">
						<code>ringSystems(reactant(0))</code> returns the atom indexes of the ring systems in the first reactant <code>ringSystems(product(1))</code>
		returns the atom indexes of the ring systems in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="ringsystemsofsize"/><a name="ringsystemsofsizedesc"/><a name="ringsystemsofsizeex"/>ringSystemsOfSize</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the ring systems in the molecule having a given size (number of atoms, fused and spiro rings belong to one ring system)</td>
				<td>atom indexes of the ring systems in the molecule having the given size (null if the molecule is acyclic or contains different ringSystems only)</td>
				<td>the ring size</td>
				<td>
					<a class="fakelink" onclick="toggle('ringsystemsofsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('ringsystemsofsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="ringsystemsofsizemoleculecontextexample">
						<code>ringSystems()</code> returns the atom indexes of the ring systems in the input molecule having the given size
					</div>

					<a class="fakelink" onclick="toggle('ringsystemsofsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('ringsystemsofsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="ringsystemsofsizereactioncontextexample" style="display:none;">
						<code>ringSystems(reactant(0))</code> returns the atom indexes of the ring systems in the first reactant having the given size <code>ringSystems(product(1))</code>
		returns the atom indexes of the ring systems in the second product having the given size
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="rotatablebond"/><a name="rotatablebonddesc"/><a name="rotatablebondex"/>rotatableBond</td>
				<td>Geometry Plugin Group</td>
				<td>checks if two atoms are connected by a rotatable bond</td>
				<td><code>true</code> if the two atoms are connected by a rotatable bond, <code>false</code> otherwise</td>
				<td><ul>
<li>the (1-based) atom indexes of the two atoms in a string: "index1-index2" (e.g. '2-3')
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('rotatablebondmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('rotatablebondmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="rotatablebondmoleculecontextexample">
						<code>rotatableBond('2-3')</code> and <code>rotatableBond(bond(1, 2))</code> both return <code>true</code> if
		atoms <code>1</code> and <code>2</code> are connected by a rotatable bond the input molecule
					</div>

					<a class="fakelink" onclick="toggle('rotatablebondreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('rotatablebondreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="rotatablebondreactioncontextexample" style="display:none;">
						<code>rotatableBond(reactant(0), bond(ratom(1), ratom(2)))</code> returns <code>true</code> if reactant atoms matching maps
		<code>1</code> and <code>2</code> in the reaction equation are connected by a rotatable bond in the corresponding
		reactant molecule (<a href="#note1">see note 1</a>)
		<code>rotatableBond(product(1), bond(patom(2), patom(3)))</code> returns <code>true</code> if product atoms
		matching maps <code>2</code> and <code>3</code> in the reaction equation are connected by a rotatable bond in the
		corresponding product molecule (<a href="#note1">see note 1</a>)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="rotatablebondcount"/><a name="rotatablebondcountdesc"/><a name="rotatablebondcountex"/>rotatableBondCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the rotatable bond count</td>
				<td>the rotatable bond count</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('rotatablebondcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('rotatablebondcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="rotatablebondcountmoleculecontextexample">
						<code>rotatableBondCount()</code> returns the number of rotatable bonds in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('rotatablebondcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('rotatablebondcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="rotatablebondcountreactioncontextexample" style="display:none;">
						<code>rotatableBondCount(reactant(0))</code> returns the number of rotatable bonds in the first reactant <code>rotatableBondCount(product(1))</code>
		returns the number of rotatable bonds in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="shortestpath"/><a name="shortestpathdesc"/><a name="shortestpathex"/>shortestPath</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the length of the shortest path between two atoms</td>
				<td>the length of the shortest path between two atoms, <code>Integer.MAX_VALUE</code> if disconnected</td>
				<td><ul>
<li>the (1-based) atom indexes of the two atoms in a string: "index1-index2" (e.g. '2-3')
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('shortestpathmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('shortestpathmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="shortestpathmoleculecontextexample">
						<code>shortestPath('2-3')</code> and <code>shortestPath(pair(1, 2))</code> both return the shortest path length
		between atoms <code>1</code> and <code>2</code> in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('shortestpathreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('shortestpathreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="shortestpathreactioncontextexample" style="display:none;">
						<code>shortestPath(reactant(0), pair(ratom(1), ratom(2)))</code> returns the length of the shortest path between reactant
		atoms matching maps <code>1</code> and <code>2</code> in the reaction equation (<a href="#note1">see note 1</a>)
		<code>shortestPath(product(1), pair(patom(2), patom(3)))</code> returns the length of the shortest path between product
		atoms matching maps <code>2</code> and <code>3</code> in the reaction equation (<a href="#note1">see note 1</a>)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="smallestatomringsize"/><a name="smallestatomringsizedesc"/><a name="smallestatomringsizeex"/>smallestAtomRingSize</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the size of the smallest ring containing the specified atom</td>
				<td>the size of the smallest ring containing the specified atom</td>
				<td><ul>
<li>the atom index / MolAtom object
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('smallestatomringsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('smallestatomringsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="smallestatomringsizemoleculecontextexample">
						<code>smallestAtomRingSize(0)</code> returns the size of the smallest ring containing atom <code>0</code> in
		the input molecule
					</div>

					<a class="fakelink" onclick="toggle('smallestatomringsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('smallestatomringsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="smallestatomringsizereactioncontextexample" style="display:none;">
						<code>smallestAtomRingSize(ratom(1))</code> returns the size of the smallest ring containing reactant atom
		matching map <code>1</code> in the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="smallestring"/><a name="smallestringdesc"/><a name="smallestringex"/>smallestRing</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the atoms of the smallest ring (number of atoms) in the molecule.</td>
				<td>atom indexes of the smallest ring in the molecule (null when acyclic)</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('smallestringmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('smallestringmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="smallestringmoleculecontextexample">
						<code>smallestRingSize()</code> returns the atom indexes of the smallest ring in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('smallestringreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('smallestringreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="smallestringreactioncontextexample" style="display:none;">
						<code>smallestRingSize(product(1))</code> returns the atom indexes of the smallest ring in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="smallestringsize"/><a name="smallestringsizedesc"/><a name="smallestringsizeex"/>smallestRingSize</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the smallest ring size</td>
				<td>the smallest ring size</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('smallestringsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('smallestringsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="smallestringsizemoleculecontextexample">
						<code>smallestRingSize()</code> returns the size of the smallest ring in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('smallestringsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('smallestringsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="smallestringsizereactioncontextexample" style="display:none;">
						<code>smallestRingSize(reactant(0))</code> returns the size of the smallest ring in the first reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="smallestringsystem"/><a name="smallestringsystemdesc"/><a name="smallestringsystemex"/>smallestRingSystem</td>
				<td>Geometry Plugin Group</td>
				<td>identifies the atoms of the smallest ring system (number of rings) in the molecule.</td>
				<td>atom indexes of the smallest ring system in the molecule (null when acyclic)</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('smallestringsystemmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('smallestringsystemmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="smallestringsystemmoleculecontextexample">
						<code>smallestRingSize()</code> returns the atom indexes of the smallest ring system in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('smallestringsystemreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('smallestringsystemreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="smallestringsystemreactioncontextexample" style="display:none;">
						<code>smallestRingSize(product(1))</code> returns the atom indexes of the smallest ring system in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="smallestringsystemsize"/><a name="smallestringsystemsizedesc"/><a name="smallestringsystemsizeex"/>smallestRingSystemSize</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the size of the smallest ring system (number of rings)</td>
				<td>the size of the smallest ring system</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('smallestringsystemsizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('smallestringsystemsizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="smallestringsystemsizemoleculecontextexample">
						<code>smallestRingSystemSize()</code> returns the size of the smallest ring system in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('smallestringsystemsizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('smallestringsystemsizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="smallestringsystemsizereactioncontextexample" style="display:none;">
						<code>smallestRingSystemSize(reactant(0))</code> returns the size of the smallest ring system in the first reactant <code>smallestringsystemsize(product(1))</code>
		returns the size of the smallest ring system in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="stereodoublebondcount"/><a name="stereodoublebondcountdesc"/><a name="stereodoublebondcountex"/>stereoDoubleBondCount</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the number of stereo double bonds</td>
				<td>the stereo double bond count</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('stereodoublebondcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('stereodoublebondcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="stereodoublebondcountmoleculecontextexample">
						<code>stereoDoubleBondCount()</code> returns the number of stereo double bonds in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('stereodoublebondcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('stereodoublebondcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="stereodoublebondcountreactioncontextexample" style="display:none;">
						<code>stereoDoubleBondCount(reactant(0))</code> returns the number of stereo double bonds in the first
		reactant <code>stereoDoubleBondCount(product(1))</code> returns the number of stereo double bonds in the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="stericeffectindex"/><a name="stericeffectindexdesc"/><a name="stericeffectindexex"/>stericEffectIndex</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the steric effect index of an atom</td>
				<td>the steric effect index of an atom</td>
				<td><ul>
<li>the atom index / MolAtom object
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('stericeffectindexmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('stericeffectindexmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="stericeffectindexmoleculecontextexample">
						calculates the steric effect index of an atom
					</div>

					<a class="fakelink" onclick="toggle('stericeffectindexreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('stericeffectindexreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="stericeffectindexreactioncontextexample" style="display:none;">
						the steric effect index of an atom
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="sterichindrance"/><a name="sterichindrancedesc"/><a name="sterichindranceex"/>stericHindrance</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the steric hindrance of an atom</td>
				<td>the steric hindrance of an atom</td>
				<td><ul>
<li>the atom index / MolAtom object
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('sterichindrancemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('sterichindrancemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="sterichindrancemoleculecontextexample">
						<code>stericHindrance(2)</code> returns the steric hindrance of atom <code>2</code> of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('sterichindrancereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('sterichindrancereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="sterichindrancereactioncontextexample" style="display:none;">
						<code>stericHindrance(ratom(2))</code> returns the steric hindrance of the reactant atom matching map <code>2</code>
		in the reaction equation <code>stericHindrance(patom(1))</code> returns the steric hindrance of the product atom
		matching map <code>1</code> in the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="szegedindex"/><a name="szegedindexdesc"/><a name="szegedindexex"/>szegedIndex</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the Szeged index</td>
				<td>the Szeged index</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('szegedindexmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('szegedindexmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="szegedindexmoleculecontextexample">
						calculates the Szeged index
					</div>

					<a class="fakelink" onclick="toggle('szegedindexreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('szegedindexreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="szegedindexreactioncontextexample" style="display:none;">
						the Szeged index
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="topologicalpolarsurfacearea"/><a name="topologicalpolarsurfaceareadesc"/><a name="topologicalpolarsurfaceareaex"/>topologicalPolarSurfaceArea<br/>PSA</td>
				<td>-</td>
				<td>calculates the topological polar surface area (2D)</td>
				<td>the polar surface area (2D)</td>
				<td><ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('topologicalpolarsurfaceareamoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('topologicalpolarsurfaceareamoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="topologicalpolarsurfaceareamoleculecontextexample">
						<code>topologicalPolarSurfaceArea()</code> returns the polar surface area of the input molecule
		<code>topologicalPolarSurfaceArea('7.4')</code> returns the polar	surface area of the major microspecies taken at pH <code>7.4</code>
					</div>

					<a class="fakelink" onclick="toggle('topologicalpolarsurfaceareareactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('topologicalpolarsurfaceareareactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="topologicalpolarsurfaceareareactioncontextexample" style="display:none;">
						<code>topologicalPolarSurfaceArea(reactant(0))</code> returns the polar surface area of the first reactant
		<code>topologicalPolarSurfaceArea(product(0), '7.4')</code>
		returns the polar surface area of the major microspecies of the first product taken at pH <code>7.4</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="vanderwaalssurfacearea"/><a name="vanderwaalssurfaceareadesc"/><a name="vanderwaalssurfaceareaex"/>vanDerWaalsSurfaceArea</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the van der Waals surface area</td>
				<td>the molecular surface area</td>
				<td><ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('vanderwaalssurfaceareamoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('vanderwaalssurfaceareamoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="vanderwaalssurfaceareamoleculecontextexample">
						<code>vanDerWaalsSurfaceArea()</code> returns the van der Waals surface area of the input molecule
		<code>vanDerWaalsSurfaceArea('7.4')</code> returns the van der Waals accessible surface area of the major microspecies taken at
		pH <code>7.4</code>
					</div>

					<a class="fakelink" onclick="toggle('vanderwaalssurfaceareareactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('vanderwaalssurfaceareareactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="vanderwaalssurfaceareareactioncontextexample" style="display:none;">
						<code>vanDerWaalsSurfaceArea(reactant(0))</code> returns the van der Waals surface area of the first reactant
		<code>vanDerWaalsSurfaceArea(product(0), '7.4')</code> returns the van der Waals surface area of the major microspecies of the
		first product taken at pH <code>7.4</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="wateraccessiblesurfacearea"/><a name="wateraccessiblesurfaceareadesc"/><a name="wateraccessiblesurfaceareaex"/>waterAccessibleSurfaceArea<br/>ASA<br/>solventAccessibleSurfaceArea</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the solvent accessible / water accessible molecular surface area</td>
				<td>the molecular surface area</td>
				<td><ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('wateraccessiblesurfaceareamoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('wateraccessiblesurfaceareamoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="wateraccessiblesurfaceareamoleculecontextexample">
						<code>waterAccessibleSurfaceArea()</code> returns the solvent accessible / water accessible surface area of the input molecule
		<code>solventAccessibleSurfaceArea('7.4')</code>returns the solvent accessible / water accessible surface area of the major microspecies taken at
		pH <code>7.4</code>
					</div>

					<a class="fakelink" onclick="toggle('wateraccessiblesurfaceareareactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('wateraccessiblesurfaceareareactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="wateraccessiblesurfaceareareactioncontextexample" style="display:none;">
						<code>waterAccessibleSurfaceArea(reactant(0))</code> returns the solvent accessible / water accessible surface area of the first
		reactant<code>solventAccessibleSurfaceArea(product(0), '7.4')</code> returns the solvent accessible / water accessible surface area of the major
		microspecies of the first product taken at pH <code>7.4</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="wienerindex"/><a name="wienerindexdesc"/><a name="wienerindexex"/>wienerIndex</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the Wiener index</td>
				<td>the Wiener index</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('wienerindexmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('wienerindexmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="wienerindexmoleculecontextexample">
						<code>wienerIndex()</code> returns the Wiener index of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('wienerindexreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('wienerindexreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="wienerindexreactioncontextexample" style="display:none;">
						<code>wienerIndex(reactant(0))</code> returns the Wiener index of the first reactant <code>wienerIndex(product(1))</code>
		returns the Wiener index of the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="wienerpolarity"/><a name="wienerpolaritydesc"/><a name="wienerpolarityex"/>wienerPolarity</td>
				<td>Geometry Plugin Group</td>
				<td>calculates the Wiener polarity</td>
				<td>the Wiener polarity</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('wienerpolaritymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('wienerpolaritymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="wienerpolaritymoleculecontextexample">
						<code>wienerPolarity()</code> returns the Wiener polarity of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('wienerpolarityreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('wienerpolarityreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="wienerpolarityreactioncontextexample" style="display:none;">
						<code>wienerPolarity(reactant(0))</code> returns the Wiener polarity of the first reactant <code>wienerPolarity(product(1))</code>
		returns the Wiener polarity of the second product
					</div>
				</td>
			</tr>
		</table>
		<p><a href="#contents">Back to Contents</a></p>
		<p>&nbsp;</p>
		<div style="margin-left: auto; margin-right: auto; " class="lenia">&nbsp;</div>
		<h3><a name="hbda_functions"></a>HBDA Functions</h3>
		<table style="margin-left: auto; margin-right: auto; text-align: left;" border="0" cellspacing="0" cellpadding="5" class="grid">
			<tr>
				<th style="width:5%;">Name</th>
				<th style="width:5%;">License</th>
				<th style="width:25%;">Description</th>
				<th style="width:15%;">Return value</th>
				<th style="width:25%;">Parameters</th>
				<th style="width:25%;">Examples</th>
			</tr>
			<tr>
				<td><a name="acceptor"/><a name="acceptordesc"/><a name="acceptorex"/>acceptor<br/>acc</td>
				<td>HBDA Plugin</td>
				<td>calculates atomic hydrogen bond acceptor multiplicity</td>
				<td>the atomic hydrogen bond acceptor multiplicity</td>
				<td><ul>
<li>the atom index / MolAtom object</li>
<li>the major microspecies pH (takes the input molecule itself if omitted)</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('acceptormoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('acceptormoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="acceptormoleculecontextexample">
						<code>acceptor(2)</code> returns the hydrogen bond acceptor multiplicity on atom <code>2</code> of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('acceptorreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('acceptorreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="acceptorreactioncontextexample" style="display:none;">
						<code>acceptor(ratom(2))</code> returns the hydrogen bond acceptor multiplicity on the reactant atom matching map
		<code>2</code> in the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="acceptorcount"/><a name="acceptorcountdesc"/><a name="acceptorcountex"/>acceptorCount</td>
				<td>HBDA Plugin</td>
				<td>calculates molecular hydrogen bond acceptor count (the number of acceptor atoms)</td>
				<td>the molecular hydrogen bond acceptor count</td>
				<td><ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('acceptorcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('acceptorcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="acceptorcountmoleculecontextexample">
						<code>acceptorCount()</code> returns the number of hydrogen bond acceptor atoms in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('acceptorcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('acceptorcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="acceptorcountreactioncontextexample" style="display:none;">
						<code>acceptorCount(reactant(0))</code> returns the number of hydrogen bond acceptor atoms in the first reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="acceptorsitecount"/><a name="acceptorsitecountdesc"/><a name="acceptorsitecountex"/>acceptorSiteCount<br/>accSiteCount</td>
				<td>HBDA Plugin</td>
				<td>calculates molecular hydrogen bond acceptor multiplicity (the sum of atomic multiplicities)</td>
				<td>the molecular hydrogen bond acceptor multiplicity</td>
				<td><ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('acceptorsitecountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('acceptorsitecountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="acceptorsitecountmoleculecontextexample">
						<code>acceptorSiteCount()</code> returns the hydrogen bond acceptor multiplicity of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('acceptorsitecountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('acceptorsitecountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="acceptorsitecountreactioncontextexample" style="display:none;">
						<code>acceptorSiteCount(reactant(0))</code> returns the hydrogen bond acceptor multiplicity of the first reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="donor"/><a name="donordesc"/><a name="donorex"/>donor<br/>don</td>
				<td>HBDA Plugin</td>
				<td>calculates atomic hydrogen bond donor multiplicity</td>
				<td>the atomic hydrogen bond donor multiplicity</td>
				<td><ul>
<li>the atom index / MolAtom object</li>
<li>the major microspecies pH (takes the input molecule itself if omitted)</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('donormoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('donormoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="donormoleculecontextexample">
						<code>donor(1, "7.4")</code> returns the hydrogen bond donor multiplicity on atom <code>1</code> of the major microspecies
		at pH <code>7.4</code>
					</div>

					<a class="fakelink" onclick="toggle('donorreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('donorreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="donorreactioncontextexample" style="display:none;">
						returns the hydrogen bond donor multiplicity on the product atom matching map <code>3</code> in the reaction equation,
		taking the major microspecies of the corresponding product at pH <code>7.4</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="donorcount"/><a name="donorcountdesc"/><a name="donorcountex"/>donorCount</td>
				<td>HBDA Plugin</td>
				<td>calculates molecular hydrogen bond donor count (the number of donor atoms)</td>
				<td>the molecular hydrogen bond donor count</td>
				<td><ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('donorcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('donorcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="donorcountmoleculecontextexample">
						<code>donorCount("7.4")</code> returns the number of hydrogen bond donor atoms in the major microspecies
		at pH <code>7.4</code>
					</div>

					<a class="fakelink" onclick="toggle('donorcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('donorcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="donorcountreactioncontextexample" style="display:none;">
						<code>donorCount(product(1), "7.4")</code> returns the number of hydrogen bond donor atoms in the major microspecies
		of the first product taken at pH <code>7.4</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="donorsitecount"/><a name="donorsitecountdesc"/><a name="donorsitecountex"/>donorSiteCount<br/>donSiteCount</td>
				<td>HBDA Plugin</td>
				<td>calculates molecular hydrogen bond acceptor / donor multiplicity (the sum of atomic multiplicities)</td>
				<td>the molecular hydrogen bond acceptor / donor multiplicity</td>
				<td><ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('donorsitecountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('donorsitecountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="donorsitecountmoleculecontextexample">
						<code>donorSiteCount("7.4")</code> returns the hydrogen bond donor multiplicity of the major microspecies
		at pH <code>7.4</code>
					</div>

					<a class="fakelink" onclick="toggle('donorsitecountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('donorsitecountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="donorsitecountreactioncontextexample" style="display:none;">
						<code>donorSiteCount(product(1), "7.4")</code> returns the hydrogen bond donor multiplicity of the major microspecies
		of the first product taken at pH <code>7.4</code>
					</div>
				</td>
			</tr>
		</table>
		<p><a href="#contents">Back to Contents</a></p>
		<p>&nbsp;</p>
		<div style="margin-left: auto; margin-right: auto; " class="lenia">&nbsp;</div>
		<h3><a name="huckel_functions"></a>Huckel Functions</h3>
		<table style="margin-left: auto; margin-right: auto; text-align: left;" border="0" cellspacing="0" cellpadding="5" class="grid">
			<tr>
				<th style="width:5%;">Name</th>
				<th style="width:5%;">License</th>
				<th style="width:25%;">Description</th>
				<th style="width:15%;">Return value</th>
				<th style="width:25%;">Parameters</th>
				<th style="width:25%;">Examples</th>
			</tr>
			<tr>
				<td><a name="chargedensity"/><a name="chargedensitydesc"/><a name="chargedensityex"/>chargeDensity<br/>totalChargeDensity</td>
				<td>Huckel Analysis Plugin</td>
				<td>calculates the charge density of atoms</td>
				<td>the charge density of the atom, <code>NaN</code> for non-existing values</td>
				<td><ul>
<li>he atom index / MolAtom object</li>
<li>the major microspecies pH (takes the input molecule itself if omitted)</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('chargedensitymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('chargedensitymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="chargedensitymoleculecontextexample">
						<code>chargeDensity(2)</code> returns the charge density of atom 2 of the input molecule, <code>NaN</code>
		for non-existing value
					</div>

					<a class="fakelink" onclick="toggle('chargedensityreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('chargedensityreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="chargedensityreactioncontextexample" style="display:none;">
						<code>chargeDensity(ratom(1))</code> returns the charge density of the reactant atom matching map <code>1</code>
		in the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="electrondensity"/><a name="electrondensitydesc"/><a name="electrondensityex"/>electronDensity<br/>piChargeDensity</td>
				<td>Huckel Analysis Plugin</td>
				<td>calculates the electron density of atoms</td>
				<td>the electron density of the atom, <code>NaN</code> for non-existing values</td>
				<td><ul>
<li>he atom index / MolAtom object</li>
<li>the major microspecies pH (takes the input molecule itself if omitted)</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('electrondensitymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('electrondensitymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="electrondensitymoleculecontextexample">
						<code>electronDensity(2)</code> returns the electron density of atom 2 of the input molecule, <code>NaN</code>
		for non-existing value
					</div>

					<a class="fakelink" onclick="toggle('electrondensityreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('electrondensityreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="electrondensityreactioncontextexample" style="display:none;">
						<code>electronDensity(ratom(1))</code> returns the electron density of the reactant atom matching map <code>1</code>
		in the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="electrophilicity"/><a name="electrophilicitydesc"/><a name="electrophilicityex"/>electrophilicity<br/>energyNu<br/>nucleophilicLocalizationEnergy</td>
				<td>Huckel Analysis Plugin</td>
				<td>calculates electrophilicity of atoms</td>
				<td>the electrophilicity of the atom,<br />
		<code>NaN</code> for non-aromatic atoms</td>
				<td><ul>
<li>the atom index / MolAtom object
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('electrophilicitymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('electrophilicitymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="electrophilicitymoleculecontextexample">
						<code>electrophilicity(2)</code> returns the electrophilicity of atom <code>2</code> of the input
		molecule, <code>NaN</code> if atom <code>2</code> is non-aromatic
					</div>

					<a class="fakelink" onclick="toggle('electrophilicityreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('electrophilicityreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="electrophilicityreactioncontextexample" style="display:none;">
						<code>electrophilicity(ratom(1))</code> returns the electrophilicity of the reactant atom matching
		map <code>1</code> in the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="electrophilicityorder"/><a name="electrophilicityorderdesc"/><a name="electrophilicityorderex"/>electrophilicityOrder<br/>aromaticElectrophilicityOrder<br/>orderE</td>
				<td>Huckel Analysis Plugin</td>
				<td>calculates E(+) order of atoms</td>
				<td>the  E(+) order index of the atom<br />
		(<code>0</code>, <code>1</code>, <code>2</code>, ...),<br /></td>
				<td><ul>
<li>the atom index / MolAtom object
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('electrophilicityordermoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('electrophilicityordermoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="electrophilicityordermoleculecontextexample">
						<code>electrophilicityOrder(2)</code> returns the E(+) order index of atom <code>2</code> of the input molecule, e.g. returns
		<code>0</code> if atom <code>2</code> is the most electrophilic atom, <code>1</code> if atom <code>2</code> is the
		second strongest electrophilic atom, etc.,
					</div>

					<a class="fakelink" onclick="toggle('electrophilicityorderreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('electrophilicityorderreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="electrophilicityorderreactioncontextexample" style="display:none;">
						<code>electrophilicityOrder(ratom(1))</code> returns the E(+) order index of the reactant atom matching map <code>1</code> in
		the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="hmochargedensity"/><a name="hmochargedensitydesc"/><a name="hmochargedensityex"/>hmoChargeDensity</td>
				<td>Huckel Analysis Plugin</td>
				<td>calculates the HMO charge density of atoms</td>
				<td>the charge density of the atom, <code>NaN</code> for non-existing values</td>
				<td><ul>
<li>he atom index / MolAtom object</li>
<li>the major microspecies pH (takes the input molecule itself if omitted)</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('hmochargedensitymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('hmochargedensitymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="hmochargedensitymoleculecontextexample">
						<code>hmoChargeDensity(2)</code> returns the charge density of atom 2 of the input molecule, <code>NaN</code>
		for non-existing value
					</div>

					<a class="fakelink" onclick="toggle('hmochargedensityreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('hmochargedensityreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="hmochargedensityreactioncontextexample" style="display:none;">
						<code>hmoChargeDensity(ratom(1))</code> returns the charge density of the reactant atom matching map <code>1</code>
		in the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="hmoelectrondensity"/><a name="hmoelectrondensitydesc"/><a name="hmoelectrondensityex"/>hmoElectronDensity</td>
				<td>Huckel Analysis Plugin</td>
				<td>calculates the HMO electron density of atoms</td>
				<td>the electron density of the atom, <code>NaN</code> for non-existing values</td>
				<td><ul>
<li>he atom index / MolAtom object</li>
<li>the major microspecies pH (takes the input molecule itself if omitted)</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('hmoelectrondensitymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('hmoelectrondensitymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="hmoelectrondensitymoleculecontextexample">
						<code>hmoElectronDensity(2)</code> returns the electron density of atom 2 of the input molecule, <code>NaN</code>
		for non-existing value
					</div>

					<a class="fakelink" onclick="toggle('hmoelectrondensityreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('hmoelectrondensityreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="hmoelectrondensityreactioncontextexample" style="display:none;">
						<code>hmoElectronDensity(ratom(1))</code> returns the electron density of the reactant atom matching map <code>1</code>
		in the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="hmoelectrophilicityorder"/><a name="hmoelectrophilicityorderdesc"/><a name="hmoelectrophilicityorderex"/>hmoElectrophilicityOrder<br/>hmoOrderE</td>
				<td>Huckel Analysis Plugin</td>
				<td>calculates HMO E(+) order of atoms</td>
				<td>the  E(+) order index of the atom<br />
		(<code>0</code>, <code>1</code>, <code>2</code>, ...),<br /></td>
				<td><ul>
<li>the atom index / MolAtom object
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('hmoelectrophilicityordermoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('hmoelectrophilicityordermoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="hmoelectrophilicityordermoleculecontextexample">
						<code>hmoElectrophilicityOrder(2)</code> returns the E(+) order index of atom <code>2</code> of the input molecule, e.g. returns
		<code>0</code> if atom <code>2</code> is the most electrophilic atom, <code>1</code> if atom <code>2</code> is the
		second strongest electrophilic atom, etc.,
					</div>

					<a class="fakelink" onclick="toggle('hmoelectrophilicityorderreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('hmoelectrophilicityorderreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="hmoelectrophilicityorderreactioncontextexample" style="display:none;">
						<code>hmoElectrophilicityOrder(ratom(1))</code> returns the E(+) order index of the reactant atom matching map <code>1</code> in
		the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="hmoelectrophiliclocalizationenergy"/><a name="hmoelectrophiliclocalizationenergydesc"/><a name="hmoelectrophiliclocalizationenergyex"/>hmoElectrophilicLocalizationEnergy</td>
				<td>Huckel Analysis Plugin</td>
				<td>calculates HMO localization energy L(+) of atoms</td>
				<td>the localization energy L(+) of the atom,<br />
<code>NaN</code> for non-aromatic atoms</td>
				<td><ul>
<li>the atom index / MolAtom object
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('hmoelectrophiliclocalizationenergymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('hmoelectrophiliclocalizationenergymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="hmoelectrophiliclocalizationenergymoleculecontextexample">
						<code>hmoElectrophilicLocalizationEnergy(2)</code> returns the electrophilic L(+) localization energy of atom <code>2</code> of the input
		molecule, <code>NaN</code> if atom <code>2</code> is non-aromatic
					</div>

					<a class="fakelink" onclick="toggle('hmoelectrophiliclocalizationenergyreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('hmoelectrophiliclocalizationenergyreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="hmoelectrophiliclocalizationenergyreactioncontextexample" style="display:none;">
						<code>hmoElectrophilicLocalizationEnergy(ratom(1))</code> returns the electrophilic L(+) localization energy of the reactant atom matching
		map <code>1</code> in the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="hmonucleophilicityorder"/><a name="hmonucleophilicityorderdesc"/><a name="hmonucleophilicityorderex"/>hmoNucleophilicityOrder<br/>hmoOrderNu</td>
				<td>Huckel Analysis Plugin</td>
				<td>calculates HMO Nu(-) order of atoms</td>
				<td>the Nu(-) order index of the atom<br />
		(<code>0</code>, <code>1</code>, <code>2</code>, ...),<br /></td>
				<td><ul>
<li>the atom index / MolAtom object
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('hmonucleophilicityordermoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('hmonucleophilicityordermoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="hmonucleophilicityordermoleculecontextexample">
						<code>hmoNucleophilicityOrder(2)</code> returns the Nu(-) order index of atom <code>2</code> of the input molecule, e.g.
		returns <code>0</code> if atom <code>2</code> is the most nucleophilic atom, <code>1</code> if atom <code>2</code> is
		the second strongest nucleophilic atom, etc.,
					</div>

					<a class="fakelink" onclick="toggle('hmonucleophilicityorderreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('hmonucleophilicityorderreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="hmonucleophilicityorderreactioncontextexample" style="display:none;">
						<code>hmoNucleophilicityOrder(ratom(1))</code> returns the Nu(-) order index of the reactant atom matching map <code>1</code>
		in the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="hmonucleophiliclocalizationenergy"/><a name="hmonucleophiliclocalizationenergydesc"/><a name="hmonucleophiliclocalizationenergyex"/>hmoNucleophilicLocalizationEnergy</td>
				<td>Huckel Analysis Plugin</td>
				<td>calculates HMO localization energy L(-) of atoms</td>
				<td>the localization energy L(-) of the atom,<br />
		<code>NaN</code> for non-aromatic atoms</td>
				<td><ul>
<li>the atom index / MolAtom object
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('hmonucleophiliclocalizationenergymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('hmonucleophiliclocalizationenergymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="hmonucleophiliclocalizationenergymoleculecontextexample">
						<code>hmoNucleophilicLocalizationEnergy(2)</code> returns the nucleophilic L(-) localization energy of atom <code>2</code> of the input
		molecule, <code>NaN</code> if atom <code>2</code> is non-aromatic
					</div>

					<a class="fakelink" onclick="toggle('hmonucleophiliclocalizationenergyreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('hmonucleophiliclocalizationenergyreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="hmonucleophiliclocalizationenergyreactioncontextexample" style="display:none;">
						<code>hmoNucleophilicLocalizationEnergy(ratom(1))</code> returns the nucleophilic L(-) localization energy of the reactant atom matching
		map <code>1</code> in the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="hmopienergy"/><a name="hmopienergydesc"/><a name="hmopienergyex"/>hmoPiEnergy</td>
				<td>Huckel Analysis Plugin</td>
				<td>calculates the HMO pi energy of the molecule</td>
				<td>the pi energy of the molecule</td>
				<td><ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('hmopienergymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('hmopienergymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="hmopienergymoleculecontextexample">
						<code>hmoPiEnergy()</code> returns the pi energy of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('hmopienergyreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('hmopienergyreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="hmopienergyreactioncontextexample" style="display:none;">
						<code>hmoPiEnergy(product(1))</code> returns the pi energy of the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="nucleophilicity"/><a name="nucleophilicitydesc"/><a name="nucleophilicityex"/>nucleophilicity<br/>electrophilicLocalizationEnergy<br/>energyE</td>
				<td>Huckel Analysis Plugin</td>
				<td>calculates nucleophilicity of atoms</td>
				<td>the nucleophilicity of the atom,<br />
<code>NaN</code> for non-aromatic atoms</td>
				<td><ul>
<li>the atom index / MolAtom object
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('nucleophilicitymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('nucleophilicitymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="nucleophilicitymoleculecontextexample">
						<code>nucleophilicity(2)</code> returns the nucleophilicity of atom <code>2</code> of the input
		molecule, <code>NaN</code> if atom <code>2</code> is non-aromatic
					</div>

					<a class="fakelink" onclick="toggle('nucleophilicityreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('nucleophilicityreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="nucleophilicityreactioncontextexample" style="display:none;">
						<code>nucleophilicity(ratom(1))</code> returns the nucleophilicity of the reactant atom matching
		map <code>1</code> in the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="nucleophilicityorder"/><a name="nucleophilicityorderdesc"/><a name="nucleophilicityorderex"/>nucleophilicityOrder<br/>aromaticNucleophilicityOrder<br/>orderNu</td>
				<td>Huckel Analysis Plugin</td>
				<td>calculates Nu(-) order of atoms</td>
				<td>the Nu(-) order index of the atom<br />
		(<code>0</code>, <code>1</code>, <code>2</code>, ...),<br /></td>
				<td><ul>
<li>the atom index / MolAtom object
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('nucleophilicityordermoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('nucleophilicityordermoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="nucleophilicityordermoleculecontextexample">
						<code>nucleophilicityOrder(2)</code> returns the Nu(-) order index of atom <code>2</code> of the input molecule, e.g.
		returns <code>0</code> if atom <code>2</code> is the most nucleophilic atom, <code>1</code> if atom <code>2</code> is
		the second strongest nucleophilic atom, etc.,
					</div>

					<a class="fakelink" onclick="toggle('nucleophilicityorderreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('nucleophilicityorderreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="nucleophilicityorderreactioncontextexample" style="display:none;">
						<code>nucleophilicityOrder(ratom(1))</code> returns the Nu(-) order index of the reactant atom matching map <code>1</code>
		in the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="pienergy"/><a name="pienergydesc"/><a name="pienergyex"/>piEnergy</td>
				<td>Huckel Analysis Plugin</td>
				<td>calculates the pi energy of the molecule. Deprecated.</td>
				<td>the pi energy of the molecule</td>
				<td><ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('pienergymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('pienergymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="pienergymoleculecontextexample">
						<code>piEnergy()</code> returns the pi energy of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('pienergyreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('pienergyreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="pienergyreactioncontextexample" style="display:none;">
						<code>piEnergy(product(1))</code> returns the pi energy of the second product
					</div>
				</td>
			</tr>
		</table>
		<p><a href="#contents">Back to Contents</a></p>
		<p>&nbsp;</p>
		<div style="margin-left: auto; margin-right: auto; " class="lenia">&nbsp;</div>
		<h3><a name="isomers_functions"></a>Isomers Functions</h3>
		<table style="margin-left: auto; margin-right: auto; text-align: left;" border="0" cellspacing="0" cellpadding="5" class="grid">
			<tr>
				<th style="width:5%;">Name</th>
				<th style="width:5%;">License</th>
				<th style="width:25%;">Description</th>
				<th style="width:15%;">Return value</th>
				<th style="width:25%;">Parameters</th>
				<th style="width:25%;">Examples</th>
			</tr>
			<tr>
				<td><a name="alltautomer"/><a name="alltautomerdesc"/><a name="alltautomerex"/>allTautomer</td>
				<td>Isomers Plugin Group</td>
				<td>constructs a tautomeric form</td>
				<td>the tautomer</td>
				<td><ul>
<li>the tautomer index (0-based)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('alltautomermoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('alltautomermoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="alltautomermoleculecontextexample">
						<code>tautomer(0)</code> returns the first tautomer of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('alltautomerreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('alltautomerreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="alltautomerreactioncontextexample" style="display:none;">
						<code>tautomer(reactant(0), 1)</code> returns the second tautomer of the first reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="alltautomers"/><a name="alltautomersdesc"/><a name="alltautomersex"/>allTautomers</td>
				<td>Isomers Plugin Group</td>
				<td>constructs all tautomeric forms</td>
				<td>the tautomer array</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('alltautomersmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('alltautomersmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="alltautomersmoleculecontextexample">
						<code>tautomers()</code> returns all tautomers of the input molecule in an array
					</div>

					<a class="fakelink" onclick="toggle('alltautomersreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('alltautomersreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="alltautomersreactioncontextexample" style="display:none;">
						<code>tautomers(reactant(0))</code> returns all tautomers of the first reactant in an array
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="canonicalresonant"/><a name="canonicalresonantdesc"/><a name="canonicalresonantex"/>canonicalResonant</td>
				<td>-</td>
				<td>constructs the canonical resonant structure</td>
				<td>the canonical resonant structure</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('canonicalresonantmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('canonicalresonantmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="canonicalresonantmoleculecontextexample">
						<code>canonicalResonant()</code> returns the canonical resonant structure of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('canonicalresonantreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('canonicalresonantreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="canonicalresonantreactioncontextexample" style="display:none;">
						<code>canonicalResonant(reactant(0))</code> returns the canonical resonant structure of the first reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="canonicaltautomer"/><a name="canonicaltautomerdesc"/><a name="canonicaltautomerex"/>canonicalTautomer</td>
				<td>Isomers Plugin Group</td>
				<td>constructs the canonical tautomer structure</td>
				<td>the canonical tautomer structure</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('canonicaltautomermoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('canonicaltautomermoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="canonicaltautomermoleculecontextexample">
						<code>canonicalTautomer()</code> returns the canonical tautomer structure of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('canonicaltautomerreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('canonicaltautomerreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="canonicaltautomerreactioncontextexample" style="display:none;">
						<code>canonicalTautomer(reactant(0))</code> returns the canonical tautomer structure of the first reactant/td>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="dominanttautomer"/><a name="dominanttautomerdesc"/><a name="dominanttautomerex"/>dominantTautomer<br/>tautomer</td>
				<td>Isomers Plugin Group</td>
				<td>returns the i-th dominant tautomeric form <!-- dominant tautomers are ordered by distribution) --></td>
				<td>the i-th dominant tautomer</td>
				<td><ul>
<li>the dominant tautomer index (0-based)
			</li><li>the pH value as string (set if pH effect should be considered)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('dominanttautomermoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('dominanttautomermoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="dominanttautomermoleculecontextexample">
						<code>dominantTautomer(0)</code> returns the first dominant tautomer of the input molecule <code>dominantTautomer(1, "2.0")</code> returns
		the second dominant tautomer of the input molecule, considering pH effect at pH <code>2.0</code> <!-- <br><br>Dominant tautomers are ordered by distribution. -->
					</div>

					<a class="fakelink" onclick="toggle('dominanttautomerreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('dominanttautomerreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="dominanttautomerreactioncontextexample" style="display:none;">
						<code>dominantTautomer(reactant(0), 1)</code> returns the second dominant tautomer of the first reactant <code>dominantTautomer(product(1), 0, "7.4")</code>
		returns the first dominant tautomer of the second product, considering pH effect at pH <code>7.4</code> <!-- <br><br>Dominant tautomers are ordered by distribution. -->
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="dominanttautomercount"/><a name="dominanttautomercountdesc"/><a name="dominanttautomercountex"/>dominantTautomerCount</td>
				<td>Isomers Plugin Group</td>
				<td>calculates the number of dominant tautomers</td>
				<td>the number of dominant tautomers</td>
				<td><ul>
<li>the pH value as string (set if pH effect should be considered)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('dominanttautomercountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('dominanttautomercountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="dominanttautomercountmoleculecontextexample">
						<code>dominantTautomerCount()</code> returns the number of dominant tautomers of the input molecule <code>dominantTautomerCount("7.4")</code>
		returns the number of dominant tautomers of the input molecule, considering pH effect at pH <code>7.4</code>
					</div>

					<a class="fakelink" onclick="toggle('dominanttautomercountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('dominanttautomercountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="dominanttautomercountreactioncontextexample" style="display:none;">
						<code>dominantTautomerCount(reactant(0))</code> returns the number of dominant tautomers of the first reactant <code>dominantTautomerCount(product(1), "2.5")</code>
		returns the number of dominant tautomers of the second product, considering pH effect at pH <code>2.5</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="dominanttautomers"/><a name="dominanttautomersdesc"/><a name="dominanttautomersex"/>dominantTautomers<br/>tautomers</td>
				<td>Isomers Plugin Group</td>
				<td>constructs all dominant tautomeric forms (ordered by distribution)</td>
				<td>the dominant tautomer array</td>
				<td><ul>
<li>the pH value as string (set if pH effect should be considered)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('dominanttautomersmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('dominanttautomersmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="dominanttautomersmoleculecontextexample">
						<code>dominantTautomers()</code> returns all dominant tautomers of the input molecule in an array <code>dominantTautomers("2.0")</code>
		returns all dominant tautomers of the input molecule in an array, considering pH effect at pH <code>2.0</code> <!-- <br><br>Dominant tautomers are ordered by distribution. -->
					</div>

					<a class="fakelink" onclick="toggle('dominanttautomersreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('dominanttautomersreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="dominanttautomersreactioncontextexample" style="display:none;">
						<code>dominantTautomers(reactant(0))</code> returns all dominant tautomers of the first reactant in an array <code>dominantTautomers(product(1), "7.4")</code>
		returns all dominant tautomers of the second product in an array, considering pH effect at pH <code>7.4</code> <!-- <br><br>Dominant tautomers are ordered by distribution. -->
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="doublebondstereoisomer"/><a name="doublebondstereoisomerdesc"/><a name="doublebondstereoisomerex"/>doubleBondStereoisomer</td>
				<td>Isomers Plugin Group</td>
				<td>generates a double bond stereoisomer of the molecule</td>
				<td>the double bond stereoisomer</td>
				<td><ul>
<li>the double bond stereoisomer index (0-based)</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('doublebondstereoisomermoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('doublebondstereoisomermoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="doublebondstereoisomermoleculecontextexample">
						<code>doubleBondStereoisomer(0)</code> returns the first double bond stereoisomer of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('doublebondstereoisomerreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('doublebondstereoisomerreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="doublebondstereoisomerreactioncontextexample" style="display:none;">
						<code>doubleBondStereoisomer(reactant(0), 1)</code> returns the second double bond stereoisomer of the first
		reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="doublebondstereoisomercount"/><a name="doublebondstereoisomercountdesc"/><a name="doublebondstereoisomercountex"/>doubleBondStereoisomerCount</td>
				<td>Isomers Plugin Group</td>
				<td>returns the number of generated double bond stereoisomers</td>
				<td>the number of generated double bond stereoisomers</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('doublebondstereoisomercountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('doublebondstereoisomercountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="doublebondstereoisomercountmoleculecontextexample">
						<code>doubleBondStereoisomerCount()</code> returns the number of generated double bond stereoisomers of the
		input molecule
					</div>

					<a class="fakelink" onclick="toggle('doublebondstereoisomercountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('doublebondstereoisomercountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="doublebondstereoisomercountreactioncontextexample" style="display:none;">
						<code>doubleBondStereoisomerCount(reactant(0))</code> returns the number of calculated double bond
		stereoisomers of the first reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="doublebondstereoisomers"/><a name="doublebondstereoisomersdesc"/><a name="doublebondstereoisomersex"/>doubleBondStereoisomers</td>
				<td>Isomers Plugin Group</td>
				<td>generates double bond stereoisomers of the molecule (maximum number of double bond stereoisomers to be
		generated can be set, default: all)</td>
				<td>the double bond stereoisomer array</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('doublebondstereoisomersmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('doublebondstereoisomersmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="doublebondstereoisomersmoleculecontextexample">
						<code>doubleBondStereoisomers()</code> returns double bond stereoisomers of the input molecule in an array
					</div>

					<a class="fakelink" onclick="toggle('doublebondstereoisomersreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('doublebondstereoisomersreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="doublebondstereoisomersreactioncontextexample" style="display:none;">
						<code>doubleBondStereoisomers(reactant(0))</code> returns double bond stereoisomers of the first reactant in an array
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="generictautomer"/><a name="generictautomerdesc"/><a name="generictautomerex"/>genericTautomer</td>
				<td>Isomers Plugin Group</td>
				<td>constructs the generic tautomer structure</td>
				<td>the generic tautomer structure</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('generictautomermoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('generictautomermoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="generictautomermoleculecontextexample">
						<code>genericTautomer()</code> returns the generic tautomer structure of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('generictautomerreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('generictautomerreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="generictautomerreactioncontextexample" style="display:none;">
						<code>genericTautomer(reactant(0))</code> returns the generic tautomer structure of the first reactant/td>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="majortautomer"/><a name="majortautomerdesc"/><a name="majortautomerex"/>majorTautomer</td>
				<td>Isomers Plugin Group</td>
				<td>constructs the major tautomer structure</td>
				<td>the major tautomer structure</td>
				<td><ul>
			<li>the pH value as string (set if pH effect should be considered)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('majortautomermoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('majortautomermoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="majortautomermoleculecontextexample">
						<code>majorTautomer()</code> returns the major tautomer structure of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('majortautomerreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('majortautomerreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="majortautomerreactioncontextexample" style="display:none;">
						<code>majorTautomer(reactant(0))</code> returns the major tautomer structure of the first reactant/td>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="moststabletautomer"/><a name="moststabletautomerdesc"/><a name="moststabletautomerex"/>mostStableTautomer</td>
				<td>Isomers Plugin Group</td>
				<td>deprecated, use majorTautomer instead. constructs the most stable tautomer structure</td>
				<td>the most stable tautomer structure</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('moststabletautomermoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('moststabletautomermoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="moststabletautomermoleculecontextexample">
						<code>mostStableTautomer()</code> returns the most stable tautomer structure of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('moststabletautomerreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('moststabletautomerreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="moststabletautomerreactioncontextexample" style="display:none;">
						<code>mostStableTautomer(reactant(0))</code> returns the most stable tautomer structure of the first reactant/td>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="resonant"/><a name="resonantdesc"/><a name="resonantex"/>resonant</td>
				<td>-</td>
				<td>constructs a resonant structure</td>
				<td>the resonant structure</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('resonantmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('resonantmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="resonantmoleculecontextexample">
						<code>resonant(0)</code> returns the first resonant structure of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('resonantreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('resonantreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="resonantreactioncontextexample" style="display:none;">
						<code>resonant(reactant(0), 1)</code> returns the second resonant structure of the first reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="resonantcount"/><a name="resonantcountdesc"/><a name="resonantcountex"/>resonantCount</td>
				<td>-</td>
				<td>calculates the number of resonant structures</td>
				<td>the number of resonant structures</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('resonantcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('resonantcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="resonantcountmoleculecontextexample">
						<code>resonantCount()</code> returns the number of resonant structures of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('resonantcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('resonantcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="resonantcountreactioncontextexample" style="display:none;">
						<code>resonantCount(reactant(0))</code> returns the number of resonant structures of the first reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="resonants"/><a name="resonantsdesc"/><a name="resonantsex"/>resonants</td>
				<td>-</td>
				<td>constructs all resonant structures</td>
				<td>the resonant structure array</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('resonantsmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('resonantsmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="resonantsmoleculecontextexample">
						<code>resonants()</code> returns all resonants of the input molecule in an array
					</div>

					<a class="fakelink" onclick="toggle('resonantsreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('resonantsreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="resonantsreactioncontextexample" style="display:none;">
						<code>resonants(reactant(0))</code> returns all resonants of the first reactant in an array
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="stereoisomer"/><a name="stereoisomerdesc"/><a name="stereoisomerex"/>stereoisomer</td>
				<td>Isomers Plugin Group</td>
				<td>generates a stereoisomer of the molecule</td>
				<td>the stereoisomer</td>
				<td><ul><li>the stereoisomer index (0-based)</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('stereoisomermoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('stereoisomermoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="stereoisomermoleculecontextexample">
						<code>stereoisomer(0)</code> returns the first stereoisomer of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('stereoisomerreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('stereoisomerreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="stereoisomerreactioncontextexample" style="display:none;">
						<code>stereoisomer(reactant(0), 1)</code> returns the second stereoisomer of the first reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="stereoisomercount"/><a name="stereoisomercountdesc"/><a name="stereoisomercountex"/>stereoisomerCount</td>
				<td>Isomers Plugin Group</td>
				<td>returns the number of generated stereoisomers</td>
				<td>the number of generated stereoisomers</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('stereoisomercountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('stereoisomercountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="stereoisomercountmoleculecontextexample">
						<code>stereoisomerCount()</code> returns the number of generated stereoisomers of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('stereoisomercountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('stereoisomercountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="stereoisomercountreactioncontextexample" style="display:none;">
						<code>stereoisomerCount(reactant(0))</code> returns the number of calculated stereoisomers of the first
		reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="stereoisomers"/><a name="stereoisomersdesc"/><a name="stereoisomersex"/>stereoisomers</td>
				<td>Isomers Plugin Group</td>
				<td>generates stereoisomers of the molecule (maximum number of stereoisomers to be generated can be set, default:
		all)</td>
				<td>the stereoisomer array</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('stereoisomersmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('stereoisomersmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="stereoisomersmoleculecontextexample">
						<code>stereoisomers()</code> returns stereoisomers of the input molecule in an array
					</div>

					<a class="fakelink" onclick="toggle('stereoisomersreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('stereoisomersreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="stereoisomersreactioncontextexample" style="display:none;">
						<code>stereoisomers(reactant(0))</code> returns stereoisomers of the first reactant in an array
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="tautomercount"/><a name="tautomercountdesc"/><a name="tautomercountex"/>tautomerCount</td>
				<td>Isomers Plugin Group</td>
				<td>calculates the number of tautomers</td>
				<td>the number of tautomers</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('tautomercountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('tautomercountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="tautomercountmoleculecontextexample">
						<code>tautomerCount()</code> returns the number of tautomers of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('tautomercountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('tautomercountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="tautomercountreactioncontextexample" style="display:none;">
						<code>tautomerCount(reactant(0))</code> returns the number of tautomers of the first reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="tetrahedralstereoisomer"/><a name="tetrahedralstereoisomerdesc"/><a name="tetrahedralstereoisomerex"/>tetrahedralStereoisomer</td>
				<td>Isomers Plugin Group</td>
				<td>generates a tetrahedral stereoisomer of the molecule</td>
				<td>the tetrahedral stereoisomer</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('tetrahedralstereoisomermoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('tetrahedralstereoisomermoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="tetrahedralstereoisomermoleculecontextexample">
						<code>tetrahedralStereoisomer(0)</code> returns the first tetrahedral stereoisomer of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('tetrahedralstereoisomerreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('tetrahedralstereoisomerreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="tetrahedralstereoisomerreactioncontextexample" style="display:none;">
						<code>tetrahedralStereoisomer(reactant(0), 1)</code> returns the second tetrahedral stereoisomer of the first
		reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="tetrahedralstereoisomercount"/><a name="tetrahedralstereoisomercountdesc"/><a name="tetrahedralstereoisomercountex"/>tetrahedralStereoisomerCount</td>
				<td>Isomers Plugin Group</td>
				<td>returns the number of generated tetrahedral stereoisomers</td>
				<td>the number of generated tetrahedral stereoisomers</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('tetrahedralstereoisomercountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('tetrahedralstereoisomercountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="tetrahedralstereoisomercountmoleculecontextexample">
						<code>tetrahedralStereoisomerCount()</code> returns the number of generated tetrahedral stereoisomers of the
		input molecule
					</div>

					<a class="fakelink" onclick="toggle('tetrahedralstereoisomercountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('tetrahedralstereoisomercountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="tetrahedralstereoisomercountreactioncontextexample" style="display:none;">
						<code>tetrahedralStereoisomerCount(reactant(0))</code> returns the number of calculated tetrahedral
		stereoisomers of the first reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="tetrahedralstereoisomers"/><a name="tetrahedralstereoisomersdesc"/><a name="tetrahedralstereoisomersex"/>tetrahedralStereoisomers</td>
				<td>Isomers Plugin Group</td>
				<td>generates tetrahedral stereoisomers of the molecule (maximum number of tetrahedral stereoisomers to be
		generated can be set, default: all)</td>
				<td>the tetrahedral stereoisomer array</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('tetrahedralstereoisomersmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('tetrahedralstereoisomersmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="tetrahedralstereoisomersmoleculecontextexample">
						<code>tetrahedralStereoisomers()</code> returns tetrahedral stereoisomers of the input molecule in an array
					</div>

					<a class="fakelink" onclick="toggle('tetrahedralstereoisomersreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('tetrahedralstereoisomersreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="tetrahedralstereoisomersreactioncontextexample" style="display:none;">
						<code>tetrahedralStereoisomers(reactant(0))</code> returns tetrahedral stereoisomers of the first reactant in
		an array
					</div>
				</td>
			</tr>
		</table>
		<p><a href="#contents">Back to Contents</a></p>
		<p>&nbsp;</p>
		<div style="margin-left: auto; margin-right: auto; " class="lenia">&nbsp;</div>
		<h3><a name="markush_functions"></a>Markush Functions</h3>
		<table style="margin-left: auto; margin-right: auto; text-align: left;" border="0" cellspacing="0" cellpadding="5" class="grid">
			<tr>
				<th style="width:5%;">Name</th>
				<th style="width:5%;">License</th>
				<th style="width:25%;">Description</th>
				<th style="width:15%;">Return value</th>
				<th style="width:25%;">Parameters</th>
				<th style="width:25%;">Examples</th>
			</tr>
			<tr>
				<td><a name="ismarkush"/><a name="ismarkushdesc"/><a name="ismarkushex"/>isMarkush</td>
				<td>-</td>
				<td>decides whether the given molecule contains any Markush features</td>
				<td><code>true</code> if the molecule contains any Markush features, <code>false</code> otherwise</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('ismarkushmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('ismarkushmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="ismarkushmoleculecontextexample">
						<code>isMarkush()</code> returns <code>true</code> if the given molecule contains any Markush features, <code>false</code> otherwise
					</div>

					<a class="fakelink" onclick="toggle('ismarkushreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('ismarkushreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="ismarkushreactioncontextexample" style="display:none;">
						<code>isMarkush(reactant(1))</code> returns <code>true</code> if the second reactant contains any Markush features,
				<code>false</code> otherwise
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="markushenumerationcount"/><a name="markushenumerationcountdesc"/><a name="markushenumerationcountex"/>markushEnumerationCount<br/>enumerationCount</td>
				<td>Markush Enumeration Plugin</td>
				<td>calculates the number of Markush enumerations</td>
				<td>the number of Markush enumerations</td>
				<td><ul>
<li>the (1-based) atom  indexes of the query atoms to be enumerated (default: all)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('markushenumerationcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('markushenumerationcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="markushenumerationcountmoleculecontextexample">
						<code>markushEnumerationCount()</code> returns the number of Markush enumerated structures of the input molecule <code>markushEnumerationCount('4,5')</code>
		returns the number of Markush enumerated structures of the input molecule, enumerating only atoms 4, 5 (1-based)
					</div>

					<a class="fakelink" onclick="toggle('markushenumerationcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('markushenumerationcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="markushenumerationcountreactioncontextexample" style="display:none;">
						<code>markushEnumerationCount(reactant(0))</code> returns the number of Markush enumerated structures of the first reactant <code>markushEnumerationCount(product(1), atoms(3,4))</code>
		returns the number of Markush enumerated structures of the second product, enumerating only atoms 4, 5 (1-based) (the <code>atoms()</code> function converts 0-based indexes to a '-'-separated 1-based atom index string)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="markushenumerations"/><a name="markushenumerationsdesc"/><a name="markushenumerationsex"/>markushEnumerations<br/>enumeration<br/>enumerations<br/>markushEnumeration</td>
				<td>Markush Enumeration Plugin</td>
				<td>constructs Markush enumerated structures sequentially</td>
				<td>the enumerated structures</td>
				<td><ul>
<li>the number of structures to be returned (default: all)
			</li><li>the (1-based) atom  indexes of the query atoms to be enumerated (default: all)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('markushenumerationsmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('markushenumerationsmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="markushenumerationsmoleculecontextexample">
						<code>markushEnumerations()</code> returns all Markush enumerated structures of the input molecule <code>markushEnumerations(1, '2,3')</code>
		returns one Markush enumerated structure of the input molecule, enumerating atoms 2, 3 (1-based)
					</div>

					<a class="fakelink" onclick="toggle('markushenumerationsreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('markushenumerationsreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="markushenumerationsreactioncontextexample" style="display:none;">
						<code>markushEnumerations(reactant(0), 1)</code> returns one Markush enumerated structure of the first reactant <code>markushEnumerations(product(1), 2, '2,3')</code>
		returns two Markush enumerated structures of the second product, enumerating atoms 2, 3 (1-based)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="markushenumerationsdisplay"/><a name="markushenumerationsdisplaydesc"/><a name="markushenumerationsdisplayex"/>markushEnumerationsDisplay</td>
				<td>Markush Enumeration Plugin</td>
				<td>constructs Markush enumerated structures sequentially with scaffold alignment and scaffold/R-group coloring and enumeration ID</td>
				<td>the enumerated structures with alignment and coloring data and enumeration ID</td>
				<td><ul>
<li>the number of structures to be returned (default: all)
			</li><li>the (1-based) atom  indexes of the query atoms to be enumerated (default: all)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('markushenumerationsdisplaymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('markushenumerationsdisplaymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="markushenumerationsdisplaymoleculecontextexample">
						<code>markushEnumerationsDisplay()</code> returns all Markush enumerated structures of the input molecule, with scaffold alignment and coloring data <code>markushEnumerationsDisplay(1, '2,3')</code>
		returns one Markush enumerated structure of the input molecule, enumerating atoms 2, 3 (1-based), with scaffold alignment and coloring data
					</div>

					<a class="fakelink" onclick="toggle('markushenumerationsdisplayreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('markushenumerationsdisplayreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="markushenumerationsdisplayreactioncontextexample" style="display:none;">
						<code>markushEnumerationsDisplay(reactant(0), 1)</code> returns one Markush enumerated structure of the first reactant, with scaffold alignment and coloring data with scaffold alignment and coloring data <code>markushEnumerationsDisplay(product(1), 2, '2,3')</code>
		returns two Markush enumerated structures of the second product, enumerating atoms 2, 3 (1-based), with scaffold alignment and coloring data
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="markushlibrarymagnitude"/><a name="markushlibrarymagnitudedesc"/><a name="markushlibrarymagnitudeex"/>markushLibraryMagnitude</td>
				<td>Markush Enumeration Plugin</td>
				<td>calculates the Markush library magnitude, no enumeration is done</td>
				<td>the Markush library magnitude</td>
				<td><ul>
<li>the (1-based) atom  indexes of the query atoms to be enumerated (default: all)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('markushlibrarymagnitudemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('markushlibrarymagnitudemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="markushlibrarymagnitudemoleculecontextexample">
						<code>markushLibraryMagnitude()</code> returns the Markush library magnitude for the input molecule <code>markushLibraryMagnitude('4,5')</code>
		returns the Markush library magnitude for the input molecule, presuming only atoms 4, 5 (1-based) are enumerated
					</div>

					<a class="fakelink" onclick="toggle('markushlibrarymagnitudereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('markushlibrarymagnitudereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="markushlibrarymagnitudereactioncontextexample" style="display:none;">
						<code>markushLibraryMagnitude(reactant(0))</code> returns the Markush library magnitude for the first reactant <code>markushLibraryMagnitude(product(1), atoms(3,4))</code>
		returns the Markush library magnitude for the second product, presuming only atoms 4, 5 (1-based) are enumerated (the <code>atoms()</code> function converts 0-based indexes to a '-'-separated 1-based atom index string)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="markushlibrarysize"/><a name="markushlibrarysizedesc"/><a name="markushlibrarysizeex"/>markushLibrarySize</td>
				<td>Markush Enumeration Plugin</td>
				<td>calculates the Markush library size, no enumeration is done</td>
				<td>the Markush library size</td>
				<td><ul>
<li>the (1-based) atom  indexes of the query atoms to be enumerated (default: all)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('markushlibrarysizemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('markushlibrarysizemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="markushlibrarysizemoleculecontextexample">
						<code>markushLibrarySize()</code> returns the Markush library size for the input molecule <code>markushLibrarySize('4,5')</code>
		returns the Markush library size for the input molecule, presuming only atoms 4, 5 (1-based) are enumerated
					</div>

					<a class="fakelink" onclick="toggle('markushlibrarysizereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('markushlibrarysizereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="markushlibrarysizereactioncontextexample" style="display:none;">
						<code>markushLibrarySize(reactant(0))</code> returns the Markush library size for the first reactant <code>markushLibrarySize(product(1), atoms(3,4))</code>
		returns the Markush library size for the second product, presuming only atoms 4, 5 (1-based) are enumerated (the <code>atoms()</code> function converts 0-based indexes to a '-'-separated 1-based atom index string)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="markushlibrarysizeasstring"/><a name="markushlibrarysizeasstringdesc"/><a name="markushlibrarysizeasstringex"/>markushLibrarySizeAsString</td>
				<td>Markush Enumeration Plugin</td>
				<td>calculates the Markush library size and returns it as string, no enumeration is done</td>
				<td>the Markush library size</td>
				<td><ul>
<li>the (1-based) atom  indexes of the query atoms to be enumerated (default: all)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('markushlibrarysizeasstringmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('markushlibrarysizeasstringmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="markushlibrarysizeasstringmoleculecontextexample">
						<code>markushLibrarySize()</code> returns the Markush library size as string for the input molecule <code>markushLibrarySize('4,5')</code>
		returns the Markush library size as string for the input molecule, presuming only atoms 4, 5 (1-based) are enumerated
					</div>

					<a class="fakelink" onclick="toggle('markushlibrarysizeasstringreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('markushlibrarysizeasstringreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="markushlibrarysizeasstringreactioncontextexample" style="display:none;">
						<code>markushLibrarySize(reactant(0))</code> returns the Markush library size as string for the first reactant <code>markushLibrarySize(product(1), atoms(3,4))</code>
		returns the Markush library size as string for the second product, presuming only atoms 4, 5 (1-based) are enumerated (the <code>atoms()</code> function converts 0-based indexes to a '-'-separated 1-based atom index string)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="randommarkushenumerations"/><a name="randommarkushenumerationsdesc"/><a name="randommarkushenumerationsex"/>randomMarkushEnumerations<br/>randomEnumeration<br/>randomEnumerations<br/>randomMarkushEnumeration</td>
				<td>Markush Enumeration Plugin</td>
				<td>constructs Markush enumerated structures randomly</td>
				<td>the enumerated structures</td>
				<td><ul>
<li>the number of structures to be returned (default: 1)
			</li><li>the (1-based) atom  indexes of the query atoms to be enumerated (default: all)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('randommarkushenumerationsmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('randommarkushenumerationsmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="randommarkushenumerationsmoleculecontextexample">
						<code>randomMarkushEnumerations()</code>
		returns a randomly constructed enumerated structure of the input molecule <code>randomMarkushEnumerations(4, '2,3')</code> returns 4 randomly constructed enumerated structures of the input molecule, enumerating only atoms 2, 3 (1-based)
					</div>

					<a class="fakelink" onclick="toggle('randommarkushenumerationsreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('randommarkushenumerationsreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="randommarkushenumerationsreactioncontextexample" style="display:none;">
						<code>randomMarkushEnumerations(reactant(0), 100)</code> returns 100 randomly constructed enumerated structures of the first reactant <code>randomMarkushEnumerations(product(1), '3,4,5')</code>
		returns a randomly constructed enumerated structure of the second product, enumerating atoms 3, 4, 5 (1-based)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="randommarkushenumerationsdisplay"/><a name="randommarkushenumerationsdisplaydesc"/><a name="randommarkushenumerationsdisplayex"/>randomMarkushEnumerationsDisplay</td>
				<td>Markush Enumeration Plugin</td>
				<td>constructs Markush enumerated structures randomly with scaffold alignment and scaffold/R-group coloring and enumeration ID</td>
				<td>the enumerated structures with alignment and coloring data and enumeration ID</td>
				<td><ul>
<li>the number of structures to be returned (default: 1)
			</li><li>the (1-based) atom  indexes of the query atoms to be enumerated (default: all)
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('randommarkushenumerationsdisplaymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('randommarkushenumerationsdisplaymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="randommarkushenumerationsdisplaymoleculecontextexample">
						<code>randomMarkushEnumerationsDisplay()</code>
		returns a randomly constructed enumerated structure of the input molecule, with scaffold alignment and scaffold/R-group coloring data<code>randomMarkushEnumerationsDisplay(4, '2,3')</code> returns 4 randomly constructed enumerated structures of the input molecule, enumerating only atoms 2, 3 (1-based), with scaffold alignment and coloring data
					</div>

					<a class="fakelink" onclick="toggle('randommarkushenumerationsdisplayreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('randommarkushenumerationsdisplayreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="randommarkushenumerationsdisplayreactioncontextexample" style="display:none;">
						<code>randomMarkushEnumerationsDisplay(reactant(0), 100)</code> returns 100 randomly constructed enumerated structures of the first reactant, with scaffold alignment and coloring data <code>randomMarkushEnumerationsDisplay(product(1), '3,4,5')</code>
		returns a randomly constructed enumerated structure of the second product, enumerating atoms 3, 4, 5 (1-based), with scaffold alignment and coloring data
					</div>
				</td>
			</tr>
		</table>
		<p><a href="#contents">Back to Contents</a></p>
		<p>&nbsp;</p>
		<div style="margin-left: auto; margin-right: auto; " class="lenia">&nbsp;</div>
		<h3><a name="match_functions"></a>Match Functions</h3>
		<table style="margin-left: auto; margin-right: auto; text-align: left;" border="0" cellspacing="0" cellpadding="5" class="grid">
			<tr>
				<th style="width:5%;">Name</th>
				<th style="width:5%;">License</th>
				<th style="width:25%;">Description</th>
				<th style="width:15%;">Return value</th>
				<th style="width:25%;">Parameters</th>
				<th style="width:25%;">Examples</th>
			</tr>
			<tr>
				<td><a name="disjointmatchcount"/><a name="disjointmatchcountdesc"/><a name="disjointmatchcountex"/>disjointMatchCount</td>
				<td>-</td>
				<td>performs substructure search, returns the maximal number of pairwise disjoint search hits</td>
				<td>the maximal number of pairwise disjoint search hits</td>
				<td><ul>
			<li>target atom index / MolAtom object (optional)</li>
			<li>query Molecule object / SMARTS string</li>
			<li>query atom map(s) (optional)</li>
		</ul>
		The function returns the maximal number of pairwise disjoint query structures found in the target molecule.
        <br/><b>Warning:</b> if the target atom index and optionally query atom maps are specified then the return value
        can only be <code>0</code> or <code>1</code>, therefore the result is similar to the result of the
        <a href="#matchdesc">match</a> function.</td>
				<td>
					<a class="fakelink" onclick="toggle('disjointmatchcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('disjointmatchcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="disjointmatchcountmoleculecontextexample">
						<code>disjointMatchCount("[#8]C=O")</code> counts the maximal number of pairwise disjoint carboxylic groups in the input molecule
					</div>

					<a class="fakelink" onclick="toggle('disjointmatchcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('disjointmatchcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="disjointmatchcountreactioncontextexample" style="display:none;">
						<code>disjointMatchCount(reactant(0), "[#8]C=O")</code> counts the maximal number of pairwise disjoint carboxylic groups in the first reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="match"/><a name="matchdesc"/><a name="matchex"/>match</td>
				<td>-</td>
				<td>performs substructure search and optionally checks for atom matching</td>
				<td><code>true</code> if matching substructure found, <code>false</code> otherwise</td>
				<td><ul>
			<li>target atom index / MolAtom object (optional)</li>
			<li>query Molecule object / SMARTS string</li>
			<li>query atom map(s) (optional)</li>
		</ul>
		The function returns <code>true</code> if the query structure is found in the target molecule, the hit is required
		to include the target atom if specified, furthermore if query atom map(s) are specified then these mapped atoms should
		match the target atom.</td>
				<td>
					<a class="fakelink" onclick="toggle('matchmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('matchmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="matchmoleculecontextexample">
						<code>match("[#8]C=O")</code> performs substructure search without atom matching requirement, the target is the
		input molecule, the query is the carboxylic group given in the string parameter <code>match(6, "[#8][C:1]=O", 1)</code>
		performs substructure search, checks if target atom <code>6</code> matches the carbon (atom with map <code>1</code>)
		of the carboxylic group query <code>match(6, "[#8:1]C=[O:2]", 1, 2)</code> performs substructure search, checks if
		target atom <code>6</code> of the input molecule is a carboxylic oxygen
					</div>

					<a class="fakelink" onclick="toggle('matchreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('matchreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="matchreactioncontextexample" style="display:none;">
						<code>match(reactant(0), "[#8]C=O")</code> performs substructure search without atom matching requirement, the
		target is the first reactant, the query is the carboxylic group given in the string parameter <code>match(patom(2), "[#8]C=O")</code>
		performs substructure search, checks if product atom matching map <code>2</code> in the reaction equation matches any
		atom of the carboxylic group query <code>match(ratom(1), "[#8:1]C=[O:2]", 1, 2)</code> performs substructure search,
		checks if reactant atom matching map <code>1</code> in the reaction equation is a carboxylic oxygen
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="matchcount"/><a name="matchcountdesc"/><a name="matchcountex"/>matchCount</td>
				<td>-</td>
				<td>performs substructure search and optionally checks for atom matching, counts search hits</td>
				<td>the number of search hits</td>
				<td><ul>
			<li>target atom index / MolAtom object (optional)</li>
			<li>query Molecule object / SMARTS string</li>
			<li>query atom map(s) (optional)</li>
		</ul>
		The function returns the number of query structures found in the target molecule, the hit is required to include
		the target atom if specified, furthermore if query atom map(s) are specified then these mapped atoms should match the
		target atom.</td>
				<td>
					<a class="fakelink" onclick="toggle('matchcountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('matchcountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="matchcountmoleculecontextexample">
						<code>matchCount("[#8]C=O")</code> counts search hits without atom matching requirement, the target is the
		input molecule, the query is the carboxylic group given in the string parameter <code>matchCount(6, "[#8]C=O")</code>
		counts search hits with target atom <code>6</code> matching any atom in a carboxylic group <code>matchCount(6, "[#8:1]C=[O:2]", 1, 2)</code>
		counts search hits with target atom <code>6</code> of the input molecule being a carboxylic oxygen
					</div>

					<a class="fakelink" onclick="toggle('matchcountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('matchcountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="matchcountreactioncontextexample" style="display:none;">
						<code>matchCount(reactant(0), "[#8]C=O")</code> counts search hits without atom matching requirement, the
		target is the first reactant, the query is the carboxylic group given in the string parameter <code>matchCount(patom(2), "[#8]C=O")</code>
		counts search hits, checks if product atom matching map <code>2</code> in the reaction equation matches any atom of
		the carboxylic group query <code>matchCount(ratom(1), "[#8:1]C=[O:2]", 1, 2)</code> counts search hits with reactant
		atom matching map <code>1</code> in the reaction equation being a carboxylic oxygen
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="matchfirst"/><a name="matchfirstdesc"/><a name="matchfirstex"/>matchFirst</td>
				<td>-</td>
				<td>performs substructure search and optionally checks for atom matching</td>
				<td>index of the first matching substructure (1-based indexing)</td>
				<td><ul>
			<li>target atom index / MolAtom object (optional)</li>
			<li>query Molecule objects / SMARTS strings in collection (e.g. {amine,amide,"[#8][C:1]=O"})</li>
			<li>query atom map(s) (optional)</li>
		</ul>
		The function returns the index of the first matching query structure found in the target molecule, the hit is required
		to include the target atom if specified, furthermore if query atom map(s) are specified then these mapped atoms should
		match the target atom.</td>
				<td>
					<a class="fakelink" onclick="toggle('matchfirstmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('matchfirstmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="matchfirstmoleculecontextexample">
						<code>matchFirst({amine,amide,alcohol})</code> performs substructure search without atom matching requirement, 
		the target is the input molecule, the queries are the amine, amide, and alcohol groups given in the string parameter. Function
		returns <code>1</code> if there is an amine group in the input molecule, <code>2</code> if there is no amine, but there is an amide
		group in the input molecule, and returns <code>3</code> if there is no amine or amide group, but there is an alcohol group in the 
		input molecule. Returns <code>0</code> if none of the listed groups is found in input molecule.
		<code>matchFirst(6, {"[#8][C:1]=O","[NX3:2][CX3:1]=[OX1:3]"}, 1)</code> performs substructure search, checks if target 
		atom <code>6</code> matches the carbon (atom with map <code>1</code>) of the carboxylic or amide group in query. Returns the index 
		of the query if match found, <code>0</code> otherwise.
		<code>matchFirst(6, {"[#8:1]C=[O:2]","[#6][OX2:1][CX3](=[O:2])[#6]"}, 1, 2)</code> performs substructure search, checks if
		target atom <code>6</code> of the input molecule is a carboxylic oxygen or an oxigen in an ester group. Returns the index 
		of the query if match found, <code>0</code> otherwise.
					</div>

					<a class="fakelink" onclick="toggle('matchfirstreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('matchfirstreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="matchfirstreactioncontextexample" style="display:none;">
						<code>matchFirst(reactant(0), {amine,amide,alcohol})</code> performs substructure search without atom matching requirement, 
		the target is the first reactant, the queries are the amine, amide, and alcohol groups given in the string parameter. Function
		returns <code>1</code> if there is an amine group in the first reactant, <code>2</code> if there is no amine group, but there is an amide 
		group in the first reactant, and returns <code>3</code> if there is no amine or amide group, but there is an alcohol group in the 
		first reactant. Returns <code>0</code> if none of the listed groups is found in first reactant. 
		<code>matchFirst(patom(2), {"[#8][C]=O","[NX3][CX3]=[OX1]"})</code> performs substructure search, checks if product atom matching map 
		<code>2</code> in the reaction equation matches any atom of the carboxylic or amide group query.  Returns the index	of mathing query, 
		or <code>0</code> if no match found. 
		<code>matchFirst(ratom(1), {"[#8:1]C=[O:2]","[#6][OX2:1][CX3](=[O:2])[#6]"}, 1, 2)</code> performs substructure search, checks if reactant
		atom matching map <code>1</code> in the reaction equation is a carboxylic oxygen or an oxigen in an ester group. Returns the index of the 
		query if match found, <code>0</code> otherwise.
					</div>
				</td>
			</tr>
		</table>
		<h4><a name="predefined"></a>Predefined Molecules and Molecule Sets</h4>
		<p>It is sometimes easier to refer molecules by names rather than explicit SMARTS strings or molecule file paths. For example, you may want to write <i>nitro</i> or <i>carboxyl</i> as query in a <code>match</code> function. Frequently used queries are pre-defined in the <a href="Evaluator_files/functionalgroups.cxsmi">built-in functional groups file</a> (<code>chemaxon/marvin/templates/functionalgroups.cxsmi</code> within <code>MarvinBeans-templates.jar</code>).</p>
<p>You can also define your favourite query SMARTS in <code>marvin/config/marvin/templates/functionalgroups.cxsmi</code> file and in <code>$HOME\chemaxon\marvin\templates\functionalgroups.cxsmi</code> (Windows) or <code>$HOME/.chemaxon/marvin/templates/functionalgroups.cxsmi</code> (UNIX / Linux) file where <code>marvin</code> is the Marvin istallation directory, <code>$HOME</code> is your user home directory.</p>
		<p><a href="#contents">Back to Contents</a></p>
		<p>&nbsp;</p>
		<div style="margin-left: auto; margin-right: auto; " class="lenia">&nbsp;</div>
		<h3><a name="name_functions"></a>Name Functions</h3>
		<table style="margin-left: auto; margin-right: auto; text-align: left;" border="0" cellspacing="0" cellpadding="5" class="grid">
			<tr>
				<th style="width:5%;">Name</th>
				<th style="width:5%;">License</th>
				<th style="width:25%;">Description</th>
				<th style="width:15%;">Return value</th>
				<th style="width:25%;">Parameters</th>
				<th style="width:25%;">Examples</th>
			</tr>
			<tr>
				<td><a name="name"/><a name="namedesc"/><a name="nameex"/>name</td>
				<td>IUPAC Naming Plugin</td>
				<td>returns the preferred IUPAC name of the molecule</td>
				<td>the preferred IUPAC name of the molecule</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('namemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('namemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="namemoleculecontextexample">
						<code>name()</code> returns the preferred IUPAC name of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('namereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('namereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="namereactioncontextexample" style="display:none;">
						N / A (reaction rules are numerical)
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="traditionalname"/><a name="traditionalnamedesc"/><a name="traditionalnameex"/>traditionalName</td>
				<td>IUPAC Naming Plugin</td>
				<td>returns the traditional name of the molecule</td>
				<td>the traditional name of the molecule</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('traditionalnamemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('traditionalnamemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="traditionalnamemoleculecontextexample">
						<code>traditionalName()</code> returns the traditional name of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('traditionalnamereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('traditionalnamereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="traditionalnamereactioncontextexample" style="display:none;">
						N / A (reaction rules are numerical)
					</div>
				</td>
			</tr>
		</table>
		<p><a href="#contents">Back to Contents</a></p>
		<p>&nbsp;</p>
		<div style="margin-left: auto; margin-right: auto; " class="lenia">&nbsp;</div>
		<h3><a name="partitioning_functions"></a>Partitioning Functions</h3>
		<table style="margin-left: auto; margin-right: auto; text-align: left;" border="0" cellspacing="0" cellpadding="5" class="grid">
			<tr>
				<th style="width:5%;">Name</th>
				<th style="width:5%;">License</th>
				<th style="width:25%;">Description</th>
				<th style="width:15%;">Return value</th>
				<th style="width:25%;">Parameters</th>
				<th style="width:25%;">Examples</th>
			</tr>
			<tr>
				<td><a name="logd"/><a name="logddesc"/><a name="logdex"/><a href="http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logd">logD</a></td>
				<td>Partitioning Plugin Group</td>
				<td>calculates log<i>D</i> at specified pH</td>
				<td>the log<i>D</i> value</td>
				<td><ul>
<li>the pH value
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('logdmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('logdmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="logdmoleculecontextexample">
						<code>logD('7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('logdreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('logdreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="logdreactioncontextexample" style="display:none;">
						<code>logD(reactant(1), '7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the second reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="logdklop"/><a name="logdklopdesc"/><a name="logdklopex"/><a href="http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logd">logDKLOP</a></td>
				<td>Partitioning Plugin Group</td>
				<td>calculates log<i>D</i> at specified pH using method "KLOP"</td>
				<td>the log<i>D</i> value</td>
				<td><ul>
<li>the pH value
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('logdklopmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('logdklopmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="logdklopmoleculecontextexample">
						<code>logDKLOP('7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('logdklopreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('logdklopreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="logdklopreactioncontextexample" style="display:none;">
						<code>logDKLOP(reactant(1), '7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the second reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="logdphys"/><a name="logdphysdesc"/><a name="logdphysex"/><a href="http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logd">logDPHYS</a></td>
				<td>Partitioning Plugin Group</td>
				<td>calculates log<i>D</i> at specified pH using method "PHYS"</td>
				<td>the log<i>D</i> value</td>
				<td><ul>
<li>the pH value
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('logdphysmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('logdphysmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="logdphysmoleculecontextexample">
						<code>logDPHYS('7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('logdphysreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('logdphysreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="logdphysreactioncontextexample" style="display:none;">
						<code>logDPHYS(reactant(1), '7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the second reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="logduser"/><a name="logduserdesc"/><a name="logduserex"/><a href="http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logd">logDUser</a></td>
				<td>Partitioning Plugin Group</td>
				<td>calculates log<i>D</i> at specified pH using the user defined method</td>
				<td>the log<i>D</i> value</td>
				<td><ul>
<li>the pH value
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('logdusermoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('logdusermoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="logdusermoleculecontextexample">
						<code>logDUser('7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('logduserreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('logduserreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="logduserreactioncontextexample" style="display:none;">
						<code>logDUser(reactant(1), '7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the second reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="logdvg"/><a name="logdvgdesc"/><a name="logdvgex"/><a href="http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logd">logDVG</a></td>
				<td>Partitioning Plugin Group</td>
				<td>calculates log<i>D</i> at specified pH using method "VG"</td>
				<td>the log<i>D</i> value</td>
				<td><ul>
<li>the pH value
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('logdvgmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('logdvgmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="logdvgmoleculecontextexample">
						<code>logDVG('7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('logdvgreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('logdvgreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="logdvgreactioncontextexample" style="display:none;">
						<code>logDVG(reactant(1), '7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the second reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="logdweighted"/><a name="logdweighteddesc"/><a name="logdweightedex"/><a href="http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logd">logDWeighted</a></td>
				<td>Partitioning Plugin Group</td>
				<td>calculates log<i>D</i> at specified pH using weighted method</td>
				<td>the log<i>D</i> value</td>
				<td><ul>
<li>the pH value
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('logdweightedmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('logdweightedmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="logdweightedmoleculecontextexample">
						<code>logDWeighted('7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('logdweightedreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('logdweightedreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="logdweightedreactioncontextexample" style="display:none;">
						<code>logDWeighted(reactant(1), '7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the second reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="logp"/><a name="logpdesc"/><a name="logpex"/><a href="http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logp">logP</a></td>
				<td>Partitioning Plugin Group</td>
				<td>calculates log<i>P</i></td>
				<td>the log<i>P</i> value</td>
				<td><ul>
<li>the result type:
			<ul>
<li>"logPMicro": the log<i>P</i> of the input molecule itself
				</li><li>"logPNonionic": the log<i>P</i> of the nonionic species
				</li><li>"logDpI": log<i>D</i> at p<i>I</i>
</li><li>"logPTrue": the most typical from the above (default)
			</li></ul>
</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('logpmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('logpmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="logpmoleculecontextexample">
						<code>logP()</code> returns the most typical log<i>P</i> out of the input molecule log<i>P</i>, the log<i>P</i>
		of the nonionic species and log<i>D</i> at p<i>I</i> <code>logP('logPMicro')</code> returns the log<i>P</i> of the input
		molecule itself
					</div>

					<a class="fakelink" onclick="toggle('logpreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('logpreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="logpreactioncontextexample" style="display:none;">
						<code>logP(reactant(1), 'logDpI')</code> returns the log<i>D</i> at p<i>I</i> of the second reactant
		<code>logP(product(1), 'logPNonionic')</code>	returns log<i>P</i> of the nonionic species of the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="logpincrement"/><a name="logpincrementdesc"/><a name="logpincrementex"/><a href="http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logp">logPincrement</a><br/><a href="http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logp">logPi</a></td>
				<td>Partitioning Plugin Group</td>
				<td>calculates the atomic log<i>P</i> increment</td>
				<td>the atomic log<i>P</i> increment</td>
				<td><ul>
<li>the atom index / MolAtom object
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('logpincrementmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('logpincrementmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="logpincrementmoleculecontextexample">
						<code>logPincrement(2)</code> returns the log<i>P</i> increment on atom <code>2</code> of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('logpincrementreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('logpincrementreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="logpincrementreactioncontextexample" style="display:none;">
						<code>logPincrement(ratom(1))</code> returns the log<i>P</i> increment on the reactant atom matching map <code>1</code>
		in the reaction equation
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="logpklop"/><a name="logpklopdesc"/><a name="logpklopex"/><a href="http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logp">logPKLOP</a></td>
				<td>Partitioning Plugin Group</td>
				<td>calculates log<i>P</i> using method "KLOP"</td>
				<td>the log<i>P</i> value</td>
				<td><ul>
<li>the result type:
			<ul>
<li>"logPMicro": the log<i>P</i> of the input molecule itself
				</li><li>"logPNonionic": the log<i>P</i> of the nonionic species
				</li><li>"logDpI": log<i>D</i> at p<i>I</i>
</li><li>"logPTrue": the most typical from the above (default)
			</li></ul>
</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('logpklopmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('logpklopmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="logpklopmoleculecontextexample">
						<code>logPKLOP()</code> returns the most typical log<i>P</i> out of the input molecule log<i>P</i>, the log<i>P</i>
		of the nonionic species and log<i>D</i> at p<i>I</i> <code>logPKLOP('logPMicro')</code> returns the log<i>P</i> of the input
		molecule itself
					</div>

					<a class="fakelink" onclick="toggle('logpklopreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('logpklopreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="logpklopreactioncontextexample" style="display:none;">
						<code>logPKLOP(reactant(1), 'logDpI')</code> returns the log<i>D</i> at p<i>I</i> of the second reactant
		<code>logPKLOP(product(1), 'logPNonionic')</code> returns log<i>P</i> of the nonionic species of the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="logpphys"/><a name="logpphysdesc"/><a name="logpphysex"/><a href="http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logp">logPPHYS</a></td>
				<td>Partitioning Plugin Group</td>
				<td>calculates log<i>P</i> using method "PHYS"</td>
				<td>the log<i>P</i> value</td>
				<td><ul>
<li>the result type:
			<ul>
<li>"logPMicro": the log<i>P</i> of the input molecule itself
				</li><li>"logPNonionic": the log<i>P</i> of the nonionic species
				</li><li>"logDpI": log<i>D</i> at p<i>I</i>
</li><li>"logPTrue": the most typical from the above (default)
			</li></ul>
</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('logpphysmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('logpphysmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="logpphysmoleculecontextexample">
						<code>logPPHYS()</code> returns the most typical log<i>P</i> out of the input molecule log<i>P</i>, the log<i>P</i>
		of the nonionic species and log<i>D</i> at p<i>I</i> <code>logPPHYS('logPMicro')</code> returns the log<i>P</i> of the input
		molecule itself
					</div>

					<a class="fakelink" onclick="toggle('logpphysreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('logpphysreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="logpphysreactioncontextexample" style="display:none;">
						<code>logPPHYS(reactant(1), 'logDpI')</code> returns the log<i>D</i> at p<i>I</i> of the second reactant
		<code>logPPHYS(product(1), 'logPNonionic')</code> returns log<i>P</i> of the nonionic species of the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="logpuser"/><a name="logpuserdesc"/><a name="logpuserex"/><a href="http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logp">logPUser</a></td>
				<td>Partitioning Plugin Group</td>
				<td>calculates log<i>P</i> using the user defined method</td>
				<td>the log<i>P</i> value</td>
				<td><ul>
<li>the result type:
			<ul>
<li>"logPMicro": the log<i>P</i> of the input molecule itself
				</li><li>"logPNonionic": the log<i>P</i> of the nonionic species
				</li><li>"logDpI": log<i>D</i> at p<i>I</i>
</li><li>"logPTrue": the most typical from the above (default)
			</li></ul>
</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('logpusermoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('logpusermoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="logpusermoleculecontextexample">
						<code>logPUser()</code> returns the most typical log<i>P</i> out of the input molecule log<i>P</i>, the log<i>P</i>
		of the nonionic species and log<i>D</i> at p<i>I</i> <code>logPUser('logPMicro')</code> returns the log<i>P</i> of the input
		molecule itself
					</div>

					<a class="fakelink" onclick="toggle('logpuserreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('logpuserreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="logpuserreactioncontextexample" style="display:none;">
						<code>logPUser(reactant(1), 'logDpI')</code> returns the log<i>D</i> at p<i>I</i> of the second reactant
		<code>logPUser(product(1), 'logPNonionic')</code> returns log<i>P</i> of the nonionic species of the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="logpvg"/><a name="logpvgdesc"/><a name="logpvgex"/><a href="http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logp">logPVG</a></td>
				<td>Partitioning Plugin Group</td>
				<td>calculates log<i>P</i> using method "VG"</td>
				<td>the log<i>P</i> value</td>
				<td><ul>
<li>the result type:
			<ul>
<li>"logPMicro": the log<i>P</i> of the input molecule itself
				</li><li>"logPNonionic": the log<i>P</i> of the nonionic species
				</li><li>"logDpI": log<i>D</i> at p<i>I</i>
</li><li>"logPTrue": the most typical from the above (default)
			</li></ul>
</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('logpvgmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('logpvgmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="logpvgmoleculecontextexample">
						<code>logPVG()</code> returns the most typical log<i>P</i> out of the input molecule log<i>P</i>, the log<i>P</i>
		of the nonionic species and log<i>D</i> at p<i>I</i> <code>logPVG('logPMicro')</code> returns the log<i>P</i> of the input
		molecule itself
					</div>

					<a class="fakelink" onclick="toggle('logpvgreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('logpvgreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="logpvgreactioncontextexample" style="display:none;">
						<code>logPVG(reactant(1), 'logDpI')</code> returns the log<i>D</i> at p<i>I</i> of the second reactant
		<code>logPVG(product(1), 'logPNonionic')</code> returns log<i>P</i> of the nonionic species of the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="logpweighted"/><a name="logpweighteddesc"/><a name="logpweightedex"/><a href="http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logp">logPWeighted</a></td>
				<td>Partitioning Plugin Group</td>
				<td>calculates log<i>P</i> using weighted method</td>
				<td>the log<i>P</i> value</td>
				<td><ul>
<li>the result type:
			<ul>
<li>"logPMicro": the log<i>P</i> of the input molecule itself
				</li><li>"logPNonionic": the log<i>P</i> of the nonionic species
				</li><li>"logDpI": log<i>D</i> at p<i>I</i>
</li><li>"logPTrue": the most typical from the above (default)
			</li></ul>
</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('logpweightedmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('logpweightedmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="logpweightedmoleculecontextexample">
						<code>logPWeighted()</code> returns the most typical log<i>P</i> out of the input molecule log<i>P</i>, the log<i>P</i>
		of the nonionic species and log<i>D</i> at p<i>I</i> <code>logPWeighted('logPMicro')</code> returns the log<i>P</i> of the input
		molecule itself
					</div>

					<a class="fakelink" onclick="toggle('logpweightedreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('logpweightedreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="logpweightedreactioncontextexample" style="display:none;">
						<code>logPWeighted(reactant(1), 'logDpI')</code> returns the log<i>D</i> at p<i>I</i> of the second reactant
		<code>logPWeighted(product(1), 'logPNonionic')</code> returns log<i>P</i> of the nonionic species of the second product
					</div>
				</td>
			</tr>
		</table>
		<p><a href="#contents">Back to Contents</a></p>
		<p>&nbsp;</p>
		<div style="margin-left: auto; margin-right: auto; " class="lenia">&nbsp;</div>
		<h3><a name="prediction_functions"></a>Prediction Functions</h3>
		<table style="margin-left: auto; margin-right: auto; text-align: left;" border="0" cellspacing="0" cellpadding="5" class="grid">
			<tr>
				<th style="width:5%;">Name</th>
				<th style="width:5%;">License</th>
				<th style="width:25%;">Description</th>
				<th style="width:15%;">Return value</th>
				<th style="width:25%;">Parameters</th>
				<th style="width:25%;">Examples</th>
			</tr>
			<tr>
				<td><a name="predict"/><a name="predictdesc"/><a name="predictex"/>predict<br/>predictor</td>
				<td>Predictor Plugin</td>
				<td>predicts molecular properties</td>
				<td>the predicted molecular property value</td>
				<td><ul>
<li>the training ID</li>
</ul></td>
				<td>
					<a class="fakelink" onclick="toggle('predictmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('predictmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="predictmoleculecontextexample">
						<code>predict('pampa')</code> returns the predicted property of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('predictreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('predictreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="predictreactioncontextexample" style="display:none;">
						<code>predict(reactant(1), 'pampa')</code> returns the predicted property of the second reactant
					</div>
				</td>
			</tr>
		</table>
		<p><a href="#contents">Back to Contents</a></p>
		<p>&nbsp;</p>
		<div style="margin-left: auto; margin-right: auto; " class="lenia">&nbsp;</div>
		<h3><a name="protonation_functions"></a>Protonation Functions</h3>
		<table style="margin-left: auto; margin-right: auto; text-align: left;" border="0" cellspacing="0" cellpadding="5" class="grid">
			<tr>
				<th style="width:5%;">Name</th>
				<th style="width:5%;">License</th>
				<th style="width:25%;">Description</th>
				<th style="width:15%;">Return value</th>
				<th style="width:25%;">Parameters</th>
				<th style="width:25%;">Examples</th>
			</tr>
			<tr>
				<td><a name="acidicpka"/><a name="acidicpkadesc"/><a name="acidicpkaex"/><a href="http://www.chemaxon.com/marvin/help/calculations/protonation.html#pka">acidicpKa</a><br/><a href="http://www.chemaxon.com/marvin/help/calculations/protonation.html#pka">apKa</a></td>
				<td>Protonation Plugin Group</td>
				<td>calculates acidic p<i>K</i><sub>a</sub> values</td>
				<td>the acidic p<i>K</i><sub>a</sub> values</td>
				<td><ul>
<li>the atom index / MolAtom object,
			</li><li>the strength index as string (e.g. '1' for the strongest, '2' for the second strongest p<i>K</i><sub>a</sub>),
		</li></ul>
		Note, that the strength index is specified between quotation marks.</td>
				<td>
					<a class="fakelink" onclick="toggle('acidicpkamoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('acidicpkamoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="acidicpkamoleculecontextexample">
						<code>acidicpKa(0)</code> returns the acidic p<i>K</i><sub>a</sub> of atom <code>0</code> of the input molecule <code>acidicpKa("2")</code>
		returns the second strongest acidic p<i>K</i><sub>a</sub> value
					</div>

					<a class="fakelink" onclick="toggle('acidicpkareactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('acidicpkareactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="acidicpkareactioncontextexample" style="display:none;">
						<code>acidicpKa(ratom(1))</code> returns the acidic p<i>K</i><sub>a</sub> of reactant atom matching map <code>1</code>
		in the reaction equation <code>acidicpKa(product(0), "1")</code> returns the strongest basic p<i>K</i><sub>a</sub> value in
		the first product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="acidicpkalargemodel"/><a name="acidicpkalargemodeldesc"/><a name="acidicpkalargemodelex"/><a href="http://www.chemaxon.com/marvin/help/calculations/protonation.html#pka">acidicpKaLargeModel</a></td>
				<td>Protonation Plugin Group</td>
				<td>calculates acidic p<i>K</i><sub>a</sub> values using large model (this model is optimized for a large number of ionizable atoms)</td>
				<td>the acidic p<i>K</i><sub>a</sub> values</td>
				<td><ul>
<li>the atom index / MolAtom object,
			</li><li>the strength index as string (e.g. '1' for the strongest, '2' for the second strongest p<i>K</i><sub>a</sub>),
		</li></ul>
		Note, that the strength index is specified between quotation marks.</td>
				<td>
					<a class="fakelink" onclick="toggle('acidicpkalargemodelmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('acidicpkalargemodelmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="acidicpkalargemodelmoleculecontextexample">
						<code>acidicpKaLargeModel(0)</code> returns the acidic p<i>K</i><sub>a</sub> of atom <code>0</code> of the input molecule
		<code>acidicpKaLargeModel("2")</code> returns the second strongest acidic p<i>K</i><sub>a</sub> value
					</div>

					<a class="fakelink" onclick="toggle('acidicpkalargemodelreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('acidicpkalargemodelreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="acidicpkalargemodelreactioncontextexample" style="display:none;">
						<code>acidicpKaLargeModel(ratom(1))</code> returns the acidic p<i>K</i><sub>a</sub> of reactant atom matching map <code>1</code>
		in the reaction equation <code>acidicpKaLargeModel(product(0), "1")</code> returns the strongest basic p<i>K</i><sub>a</sub> value in
		the first product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="acidicpkausecorrection"/><a name="acidicpkausecorrectiondesc"/><a name="acidicpkausecorrectionex"/><a href="http://www.chemaxon.com/marvin/help/calculations/protonation.html#pka">acidicpKaUseCorrection</a></td>
				<td>Protonation Plugin Group</td>
				<td>calculates acidic p<i>K</i><sub>a</sub> values using the correction library</td>
				<td>the acidic p<i>K</i><sub>a</sub> values</td>
				<td><ul>
<li>the atom index / MolAtom object,
			</li><li>the strength index as string (e.g. '1' for the strongest, '2' for the second strongest p<i>K</i><sub>a</sub>),
		</li></ul>
		Note, that the strength index is specified between quotation marks.</td>
				<td>
					<a class="fakelink" onclick="toggle('acidicpkausecorrectionmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('acidicpkausecorrectionmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="acidicpkausecorrectionmoleculecontextexample">
						<code>acidicpKaUseCorrection(0)</code> returns the acidic p<i>K</i><sub>a</sub> of atom <code>0</code> of the input molecule
		<code>"acidicpKaUseCorrection"("2")</code>	returns the second strongest acidic p<i>K</i><sub>a</sub> value
					</div>

					<a class="fakelink" onclick="toggle('acidicpkausecorrectionreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('acidicpkausecorrectionreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="acidicpkausecorrectionreactioncontextexample" style="display:none;">
						<code>"acidicpKaUseCorrection"(ratom(1))</code> returns the acidic p<i>K</i><sub>a</sub> of reactant atom matching map <code>1</code>
		in the reaction equation <code>"acidicpKaUseCorrection"(product(0), "1")</code> returns the strongest basic p<i>K</i><sub>a</sub> value in
		the first product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="averagemicrospeciescharge"/><a name="averagemicrospecieschargedesc"/><a name="averagemicrospecieschargeex"/>averageMicrospeciesCharge</td>
				<td>Protonation Plugin Group</td>
				<td>calculates the average microspecies charge (weighted sum of charges of all the microspecies of the molecule) at the given pH</td>
				<td>the average charge</td>
				<td><ul>
<li>the major microspecies pH</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('averagemicrospecieschargemoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('averagemicrospecieschargemoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="averagemicrospecieschargemoleculecontextexample">
						<code>averageMicrospeciesCharge()</code> returns the average charge of the microspecies of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('averagemicrospecieschargereactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('averagemicrospecieschargereactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="averagemicrospecieschargereactioncontextexample" style="display:none;">
						<code>averageMicrospeciesCharge(reactant(1))</code> returns the average charge of the microspecies of the second reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="basicpka"/><a name="basicpkadesc"/><a name="basicpkaex"/><a href="http://www.chemaxon.com/marvin/help/calculations/protonation.html#pka">basicpKa</a><br/><a href="http://www.chemaxon.com/marvin/help/calculations/protonation.html#pka">bpKa</a></td>
				<td>Protonation Plugin Group</td>
				<td>calculates basic p<i>K</i><sub>a</sub> values</td>
				<td>the basic p<i>K</i><sub>a</sub> values</td>
				<td><ul>
<li>the atom index / MolAtom object,
			</li><li>the strength index as string (e.g. '1' for the strongest, '2' for the second strongest p<i>K</i><sub>a</sub>),
		</li></ul>
		Note, that the strength index is specified between quotation marks.</td>
				<td>
					<a class="fakelink" onclick="toggle('basicpkamoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('basicpkamoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="basicpkamoleculecontextexample">
						<code>basicpKa(0)</code> returns the basic p<i>K</i><sub>a</sub> of atom <code>0</code> of the input molecule
		<code>basicpKa("2")</code> returns the second strongest basic p<i>K</i><sub>a</sub> value
					</div>

					<a class="fakelink" onclick="toggle('basicpkareactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('basicpkareactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="basicpkareactioncontextexample" style="display:none;">
						<code>basicpKa(ratom(1))</code> returns the basic p<i>K</i><sub>a</sub> of reactant atom matching map <code>1</code>
		in the reaction equation <code>basicpKa(product(0), "1")</code> returns the strongest basic p<i>K</i><sub>a</sub> value in
		the first product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="basicpkalargemodel"/><a name="basicpkalargemodeldesc"/><a name="basicpkalargemodelex"/><a href="http://www.chemaxon.com/marvin/help/calculations/protonation.html#pka">basicpKaLargeModel</a></td>
				<td>Protonation Plugin Group</td>
				<td>calculates basic p<i>K</i><sub>a</sub> values using large model (this model is optimized for a large number of ionizable atoms)</td>
				<td>the basic p<i>K</i><sub>a</sub> values</td>
				<td><ul>
<li>the atom index / MolAtom object,
			</li><li>the strength index as string (e.g. '1' for the strongest, '2' for the second strongest p<i>K</i><sub>a</sub>),
		</li></ul>
		Note, that the strength index is specified between quotation marks.</td>
				<td>
					<a class="fakelink" onclick="toggle('basicpkalargemodelmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('basicpkalargemodelmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="basicpkalargemodelmoleculecontextexample">
						<code>basicpKaLargeModel(0)</code> returns the basic p<i>K</i><sub>a</sub> of atom <code>0</code> of the input molecule
		<code>basicpKaLargeModel("2")</code> returns the second strongest basic p<i>K</i><sub>a</sub> value
					</div>

					<a class="fakelink" onclick="toggle('basicpkalargemodelreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('basicpkalargemodelreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="basicpkalargemodelreactioncontextexample" style="display:none;">
						<code>basicpKaLargeModel(ratom(1))</code> returns the basic p<i>K</i><sub>a</sub> of reactant atom matching map <code>1</code>
		in the reaction equation <code>basicpKaLargeModel(product(0), "1")</code> returns the strongest basic p<i>K</i><sub>a</sub> value in
		the first product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="basicpkausecorrection"/><a name="basicpkausecorrectiondesc"/><a name="basicpkausecorrectionex"/><a href="http://www.chemaxon.com/marvin/help/calculations/protonation.html#pka">basicpKaUseCorrection</a></td>
				<td>Protonation Plugin Group</td>
				<td>calculates basic p<i>K</i><sub>a</sub> values using the correction library</td>
				<td>the basic p<i>K</i><sub>a</sub> values</td>
				<td><ul>
<li>the atom index / MolAtom object,
			</li><li>the strength index as string (e.g. '1' for the strongest, '2' for the second strongest p<i>K</i><sub>a</sub>),
		</li></ul>
		Note, that the strength index is specified between quotation marks.</td>
				<td>
					<a class="fakelink" onclick="toggle('basicpkausecorrectionmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('basicpkausecorrectionmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="basicpkausecorrectionmoleculecontextexample">
						<code>basicpKaUseCorrection(0)</code> returns the basic p<i>K</i><sub>a</sub> of atom <code>0</code> of the input molecule
		<code>basicpKaUseCorrection("2")</code> returns the second strongest basic p<i>K</i><sub>a</sub> value
					</div>

					<a class="fakelink" onclick="toggle('basicpkausecorrectionreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('basicpkausecorrectionreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="basicpkausecorrectionreactioncontextexample" style="display:none;">
						<code>basicpKaUseCorrection(ratom(1))</code> returns the basic p<i>K</i><sub>a</sub> of reactant atom matching map <code>1</code>
		in the reaction equation <code>basicpKaUseCorrection(product(0), "1")</code> returns the strongest basic p<i>K</i><sub>a</sub> value in
		the first product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="isoelectricpoint"/><a name="isoelectricpointdesc"/><a name="isoelectricpointex"/>isoelectricPoint<br/>pI</td>
				<td>Protonation Plugin Group</td>
				<td>calculates isoelectric point</td>
				<td>the isoelectric point</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('isoelectricpointmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('isoelectricpointmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="isoelectricpointmoleculecontextexample">
						<code>isoelectricPoint()</code> returns the isoelectric point of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('isoelectricpointreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('isoelectricpointreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="isoelectricpointreactioncontextexample" style="display:none;">
						<code>isoelectricPoint(reactant(1))</code> returns the isoelectric point of the second reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="majormicrospecies"/><a name="majormicrospeciesdesc"/><a name="majormicrospeciesex"/><a href="http://www.chemaxon.com/marvin/help/calculations/protonation.html#ms">majorMicrospecies</a><br/><a href="http://www.chemaxon.com/marvin/help/calculations/protonation.html#ms">majorMs</a></td>
				<td>Protonation Plugin Group</td>
				<td>calculates major microspecies at specified pH</td>
				<td>the major microspecies</td>
				<td><ul>
<li>the pH value as string
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('majormicrospeciesmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('majormicrospeciesmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="majormicrospeciesmoleculecontextexample">
						<code>majorMicrospecies("7.4")</code> returns the major microspecies of the input molecule at pH <code>7.4</code>
					</div>

					<a class="fakelink" onclick="toggle('majormicrospeciesreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('majormicrospeciesreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="majormicrospeciesreactioncontextexample" style="display:none;">
						<code>majorMicrospecies(reactant(0), "7.4")</code> returns the major microspecies of the first reactant at pH <code>7.4</code>
<code>majorMicrospecies(product(1), "7.4")</code> returns the major microspecies of the second product at pH <code>7.4</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="microspecies"/><a name="microspeciesdesc"/><a name="microspeciesex"/><a href="http://www.chemaxon.com/marvin/help/calculations/protonation.html#ms">microspecies</a><br/><a href="http://www.chemaxon.com/marvin/help/calculations/protonation.html#ms">ms</a></td>
				<td>Protonation Plugin Group</td>
				<td>calculates microspecies at specified pH</td>
				<td>the microspecies</td>
				<td><ul>
<li>the pH value as string
			</li><li>the microspecies index by descending order of microspecies distributions
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('microspeciesmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('microspeciesmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="microspeciesmoleculecontextexample">
						<code>microspecies("7.4", 1)</code> returns the microspecies of the input molecule with second largest distribution at pH
		<code>7.4</code>
					</div>

					<a class="fakelink" onclick="toggle('microspeciesreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('microspeciesreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="microspeciesreactioncontextexample" style="display:none;">
						<code>microspecies(reactant(0), "7.4", 2)</code> returns the microspecies of the first reactant with third largest
		distribution at pH <code>7.4</code> <code>microspecies(product(1), "7.4", 1)</code> returns the microspecies of the second product
		with second largest distribution at pH <code>7.4</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="microspeciescount"/><a name="microspeciescountdesc"/><a name="microspeciescountex"/><a href="http://www.chemaxon.com/marvin/help/calculations/protonation.html#ms">microspeciesCount</a><br/><a href="http://www.chemaxon.com/marvin/help/calculations/protonation.html#ms">msCount</a></td>
				<td>Protonation Plugin Group</td>
				<td>calculates the number of microspecies at specified pH</td>
				<td>the number of microspecies</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('microspeciescountmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('microspeciescountmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="microspeciescountmoleculecontextexample">
						<code>microspeciesCount()</code> returns the number of microspecies of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('microspeciescountreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('microspeciescountreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="microspeciescountreactioncontextexample" style="display:none;">
						<code>microspeciesCount(reactant(0))</code> returns the number of microspecies of the first reactant <code>microspeciesCount(product(1))</code>
		returns the number of microspecies of the second product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="microspeciesdistribution"/><a name="microspeciesdistributiondesc"/><a name="microspeciesdistributionex"/><a href="http://www.chemaxon.com/marvin/help/calculations/protonation.html#ms">microspeciesDistribution</a><br/><a href="http://www.chemaxon.com/marvin/help/calculations/protonation.html#ms">msDistr</a></td>
				<td>Protonation Plugin Group</td>
				<td>calculates microspecies distribution at specified pH</td>
				<td>the microspecies distribution</td>
				<td><ul>
<li>the pH value as string
			</li><li>the microspecies index by descending order of microspecies distributions
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('microspeciesdistributionmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('microspeciesdistributionmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="microspeciesdistributionmoleculecontextexample">
						<code>microspeciesDistribution("5.4", 0)</code> returns the largest microspecies distribution of the input molecule at pH <code>5.4</code>
					</div>

					<a class="fakelink" onclick="toggle('microspeciesdistributionreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('microspeciesdistributionreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="microspeciesdistributionreactioncontextexample" style="display:none;">
						<code>microspeciesDistribution(reactant(0), "5.4", 2)</code> returns the third largest microspecies distribution of the first
		reactant at pH <code>5.4</code> <code>microspeciesDistribution(product(1), "3.2", 1)</code> returns the second largest microspecies
		distribution of the second product at pH <code>3.2</code>
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="pka"/><a name="pkadesc"/><a name="pkaex"/><a href="http://www.chemaxon.com/marvin/help/calculations/protonation.html#pka">pKa</a></td>
				<td>Protonation Plugin Group</td>
				<td>calculates p<i>K</i><sub>a</sub> values</td>
				<td>the p<i>K</i><sub>a</sub> values</td>
				<td><ul>
<li>the atom index / MolAtom object,
			</li><li>the strength index as string (e.g., '1' for the strongest, '2' for the second strongest p<i>K</i><sub>a</sub>),
			</li><li>the result type ("acidic", "basic", or "pka" for both (default))
		</li></ul>
		Note, that the strength index is specified between quotation marks. <br />
		In case of strength index the result type can be specified as "acidic" or "basic".<br /><br />
		In case of "pka"result type the returned p<i>K</i><sub>a</sub> values are
		acidic or basic (mixed!), depending on the acidic or basic character of the given atom. Acidic p<i>K</i><sub>a</sub>
		 is returned for an atom, if <i>acidicpKa() &le; 14.8-basicpKa()</i>, otherwise basic p<i>K</i><sub>a</sub> is returned.
		 Specifying "acidic" or "basic" result type is required to get the acidic or basic p<i>K</i><sub>a</sub> values only
		 (also <code>acidicpKa</code> or <code>basicpKa</code> functions can be used alternatively).</td>
				<td>
					<a class="fakelink" onclick="toggle('pkamoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('pkamoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="pkamoleculecontextexample">
						<code>pKa(0)</code> returns the p<i>K</i><sub>a</sub> of atom <code>0</code> of the input molecule <code>pKa("acidic", "2")</code>
		returns the second strongest acidic p<i>K</i><sub>a</sub> value
					</div>

					<a class="fakelink" onclick="toggle('pkareactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('pkareactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="pkareactioncontextexample" style="display:none;">
						<code>pKa(ratom(1))</code> returns the p<i>K</i><sub>a</sub> of reactant atom matching map <code>1</code> in
		the reaction equation <code>pKa(product(0), "basic", "1")</code> returns the strongest basic p<i>K</i><sub>a</sub> value
		in the first product
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="pkausecorrection"/><a name="pkausecorrectiondesc"/><a name="pkausecorrectionex"/><a href="http://www.chemaxon.com/marvin/help/calculations/protonation.html#pka">pKaUseCorrection</a></td>
				<td>Protonation Plugin Group</td>
				<td>calculates p<i>K</i><sub>a</sub> values using the correction library</td>
				<td>the p<i>K</i><sub>a</sub> values</td>
				<td><ul>
<li>the atom index / MolAtom object,
			</li><li>the strength index as string (e.g. '1' for the strongest, '2' for the second strongest p<i>K</i><sub>a</sub>),
			</li><li>the result type ("acidic", "basic", or "pka" for both (default))
		</li></ul>
		Note, that the strength index is specified between quotation marks. <br />
		In case of strength index the result type can be specified as "acidic" or "basic".<br /><br />
		In case of "pka"result type the returned p<i>K</i><sub>a</sub> values are
		acidic or basic (mixed!), depending on the acidic or basic character of the given atom. Acidic p<i>K</i><sub>a</sub>
		 is returned for an atom, if <i>acidicpKa() &le; 14.8-basicpKa()</i>, otherwise basic p<i>K</i><sub>a</sub> is returned.
		 Specifying "acidic" or "basic" result type is required to get the acidic or basic p<i>K</i><sub>a</sub> values only
		 (also <code>acidicpKaUseCorrection()</code> or <code>basicpKaUseCorrection()</code> functions can be used alternatively).</td>
				<td>
					<a class="fakelink" onclick="toggle('pkausecorrectionmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('pkausecorrectionmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="pkausecorrectionmoleculecontextexample">
						<code>pKaUseCorrection(0)</code> returns the p<i>K</i><sub>a</sub> of atom <code>0</code> of the input molecule
		<code>pKaUseCorrection("acidic", "2")</code> 	returns the second strongest acidic p<i>K</i><sub>a</sub> value
					</div>

					<a class="fakelink" onclick="toggle('pkausecorrectionreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('pkausecorrectionreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="pkausecorrectionreactioncontextexample" style="display:none;">
						<code>pKaUseCorrection(ratom(1))</code> returns the p<i>K</i><sub>a</sub> of reactant atom matching map <code>1</code> in
		the reaction equation <code>pKaUseCorrection(product(0), "basic", "1")</code> returns the strongest basic p<i>K</i><sub>a</sub> value
		in the first product
					</div>
				</td>
			</tr>
		</table>
		<p><a href="#contents">Back to Contents</a></p>
		<p>&nbsp;</p>
		<div style="margin-left: auto; margin-right: auto; " class="lenia">&nbsp;</div>
		<h3><a name="refractivity_functions"></a>Refractivity Functions</h3>
		<table style="margin-left: auto; margin-right: auto; text-align: left;" border="0" cellspacing="0" cellpadding="5" class="grid">
			<tr>
				<th style="width:5%;">Name</th>
				<th style="width:5%;">License</th>
				<th style="width:25%;">Description</th>
				<th style="width:15%;">Return value</th>
				<th style="width:25%;">Parameters</th>
				<th style="width:25%;">Examples</th>
			</tr>
			<tr>
				<td><a name="refractivity"/><a name="refractivitydesc"/><a name="refractivityex"/>refractivity<br/>refrac</td>
				<td>Refractivity Plugin</td>
				<td>calculates molar refractivity</td>
				<td>the refractivity value</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('refractivitymoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('refractivitymoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="refractivitymoleculecontextexample">
						<code>refractivity()</code> returns the molar refractivity of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('refractivityreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('refractivityreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="refractivityreactioncontextexample" style="display:none;">
						<code>refractivity(reactant(1))</code> returns the molar refractivity of the second reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="refractivityincrements"/><a name="refractivityincrementsdesc"/><a name="refractivityincrementsex"/>refractivityIncrements<br/>refraci</td>
				<td>Refractivity Plugin</td>
				<td>calculates atomic refractivity increments</td>
				<td>the atomic refractivity increment</td>
				<td><ul>
<li>the atom index / MolAtom object
		</li></ul></td>
				<td>
					<a class="fakelink" onclick="toggle('refractivityincrementsmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('refractivityincrementsmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="refractivityincrementsmoleculecontextexample">
						<code>refractivityIncrements(2)</code> returns the refractivity increment on atom <code>2</code> of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('refractivityincrementsreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('refractivityincrementsreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="refractivityincrementsreactioncontextexample" style="display:none;">
						<code>refractivityIncrements(ratom(1))</code> returns the refractivity increment on the reactant atom matching map <code>1</code>
		in the reaction equation
					</div>
				</td>
			</tr>
		</table>
		<p><a href="#contents">Back to Contents</a></p>
		<p>&nbsp;</p>
		<div style="margin-left: auto; margin-right: auto; " class="lenia">&nbsp;</div>
		<h3><a name="structuralframeworks_functions"></a>StructuralFrameworks Functions</h3>
		<table style="margin-left: auto; margin-right: auto; text-align: left;" border="0" cellspacing="0" cellpadding="5" class="grid">
			<tr>
				<th style="width:5%;">Name</th>
				<th style="width:5%;">License</th>
				<th style="width:25%;">Description</th>
				<th style="width:15%;">Return value</th>
				<th style="width:25%;">Parameters</th>
				<th style="width:25%;">Examples</th>
			</tr>
			<tr>
				<td><a name="bmf"/><a name="bmfdesc"/><a name="bmfex"/>bmf</td>
				<td>Structural Frameworks Plugin</td>
				<td>Returns the Bemis-Murcko framework of the input structure</td>
				<td>BMF of the input structure</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('bmfmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('bmfmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="bmfmoleculecontextexample">
						<code>bmf()</code> returns the Bemis-Murcko framework of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('bmfreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('bmfreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="bmfreactioncontextexample" style="display:none;">
						<code>bmf(reactant(0))</code> returns the Bemis-Murcko framework of the first reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="bmfl"/><a name="bmfldesc"/><a name="bmflex"/>bmfl<br/>bemisMurckoFrameworkLoose</td>
				<td>Structural Frameworks Plugin</td>
				<td>Returns the Bemis-Murcko loose framework of the input structure. Calculated by removing side chains. Exocyclic non single bonded atoms are kept. Remaining atom and bond types are not changed.</td>
				<td>Loose BMF of the input structure</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('bmflmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('bmflmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="bmflmoleculecontextexample">
						<code>bmfl()</code> returns the loose Bemis-Murcko framework of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('bmflreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('bmflreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="bmflreactioncontextexample" style="display:none;">
						<code>bmfl(reactant(0))</code> returns the loose Bemis-Murcko framework of the first reactant
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="bmflp"/><a name="bmflpdesc"/><a name="bmflpex"/>bmflp<br/>bemisMurckoFrameworkLoosePruned</td>
				<td>Structural Frameworks Plugin</td>
				<td>Returns the Bemis-Murcko loose framework of the input structure. Calculated by removing side cains. Atom and bond types are generalized by replacing all atoms with carbons and setting all bond types to single. Exocyclic non single bonded atoms are kept as single bonded carbons.</td>
				<td>Generalized loose BMF of the input structure</td>
				<td>-</td>
				<td>
					<a class="fakelink" onclick="toggle('bmflpmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('bmflpmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="bmflpmoleculecontextexample">
						<code>bmflp()</code> returns the generalized loose Bemis-Murcko framework of the input molecule
					</div>

					<a class="fakelink" onclick="toggle('bmflpreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('bmflpreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="bmflpreactioncontextexample" style="display:none;">
						<code>bmflp(reactant(0))</code> returns the generalized loose Bemis-Murcko framework of the first reactant
					</div>
				</td>
			</tr>
		</table>
		<p><a href="#contents">Back to Contents</a></p>
		<p>&nbsp;</p>
		<div style="margin-left: auto; margin-right: auto; " class="lenia">&nbsp;</div>
		<h3><a name="structure_checker_functions"></a>Structure Checker Functions</h3>
		<table style="margin-left: auto; margin-right: auto; text-align: left;" border="0" cellspacing="0" cellpadding="5" class="grid">
			<tr>
				<th style="width:5%;">Name</th>
				<th style="width:5%;">License</th>
				<th style="width:25%;">Description</th>
				<th style="width:15%;">Return value</th>
				<th style="width:25%;">Parameters</th>
				<th style="width:25%;">Examples</th>
			</tr>
			<tr>
				<td><a name="check"/><a name="checkdesc"/><a name="checkex"/><a href="http://www.chemaxon.com/marvin/help/structurechecker/checker.html">check</a></td>
				<td>-</td>
				<td>checks the structure for errors, according to the configuration</td>
				<td>the error report</td>
				<td>Structure checker/fixer configuration string</td>
				<td>
					<a class="fakelink" onclick="toggle('checkmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('checkmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="checkmoleculecontextexample">
						<code>check("aromaticity..valence")</code> checks for aromaticity and valence errors, and returns the error report
					</div>

					<a class="fakelink" onclick="toggle('checkreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('checkreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="checkreactioncontextexample" style="display:none;">
						<code>check(reactant(0), "aromaticity..valence")</code> checks for aromaticity and valence errors in first reactant, and returns the error report
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="fix"/><a name="fixdesc"/><a name="fixex"/><a href="http://www.chemaxon.com/marvin/help/structurechecker/checker.html">fix</a></td>
				<td>-</td>
				<td>checks the structure for errors, according to the configuration, and then fixes the errors</td>
				<td>the fixed molecule</td>
				<td>Structure checker/fixer configuration string</td>
				<td>
					<a class="fakelink" onclick="toggle('fixmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('fixmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="fixmoleculecontextexample">
						<code>fix("explicitH..isotope->converttoelementalform")</code> searches for explicit hydrogens and isotopes, and removes them or converts them to elemental form
					</div>

					<a class="fakelink" onclick="toggle('fixreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('fixreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="fixreactioncontextexample" style="display:none;">
						<code>fix(product(1), "explicitH..isotope->converttoelementalform")</code> searches for explicit hydrogens and isotopes in second product, and removes them or converts them to elemental form
					</div>
				</td>
			</tr>
			<tr>
				<td><a name="isvalid"/><a name="isvaliddesc"/><a name="isvalidex"/><a href="http://www.chemaxon.com/marvin/help/structurechecker/checker.html">isValid</a></td>
				<td>-</td>
				<td>checks the structure for errors, according to the configuration</td>
				<td>true, if the structure is valid (has no errors), false otherwise</td>
				<td>Structure checker/fixer configuration string</td>
				<td>
					<a class="fakelink" onclick="toggle('isvalidmoleculecontextexample')">Molecule Context</a>
<a class="fakelink" onclick="toggleAll('isvalidmoleculecontextexample','moleculecontextexample')">(All)</a><br/>
					<div id="isvalidmoleculecontextexample">
						<code>isValid("aromaticity..valence")</code> checks for aromaticity and valence errors, and returns if the structure is valid
					</div>

					<a class="fakelink" onclick="toggle('isvalidreactioncontextexample')">Reaction Context</a>
<a class="fakelink" onclick="toggleAll('isvalidreactioncontextexample','reactioncontextexample')">(All)</a><br/>
					<div id="isvalidreactioncontextexample" style="display:none;">
						<code>isValid(reactant(0), "aromaticity..valence")</code> checks for aromaticity and valence errors in first reactant, and returns if it is valid
					</div>
				</td>
			</tr>
		</table>
		<p><a href="#contents">Back to Contents</a></p>
		<p>&nbsp;</p>
		<div style="margin-left: auto; margin-right: auto; " class="lenia">&nbsp;</div>
		<h3><a name="notes"></a>Notes</h3>
		<ol>
			<li><a name="note0"></a>If in reaction context in the parameter list of a Chemical Terms function atoms are referred by atom index - including cases when a Chemical Terms function is used to convert reactant or product atom maps to atom indexes - then the molecule (reactant / product) parameter always have to be specified (e.g. <code>shortestPath(reactant(0), pair(ratom(1), ratom(2)))</code>) and must contain all the atoms referred by atom indexes.</li>
			<li><a name="note1"></a><b>Although the function/calculation names are case-sensitive, the lowercase versions are always accepted.</b> For example, <code>aromaticAtomCount()</code> is equivalent to <code>aromaticatomcount()</code>, but <code>AromaticAtomCount()</code> and <code>AROMATICATOMCOUNT()</code> are not recognized by the parser.</li>
			<li><a name="note2"></a><code>match</code>, <code>matchCount</code>, <code>disjointMatchCount</code> and <code>dissimilarity</code> functions are not available in Marvin, they can be used only if JChem software package is installed.</li>
			<li><a name="note3"></a>Structure based calculations are performed using Calculator Plugins (these calculations are also referred as "plugin calculations").Since Marvin 5.2, all of these functions can have an additional string argument that specifies plugin parameters in "key1:value1 key2:value2 key3:value3 ..." fashion. For example: charge(8, "type:pi pH:3.5") will compute the pi charge of atom 8 in the major microspecies at pH=3.5 of the input molecule, markushEnumerations("code:true max:4","1,2") will generate maximum 4 enumerated structures with enumeration ID, enumerating only atoms 1 and 2 of the input Markush structure. Note, that this feature cannot be combined with the former possibility of specifying the pH in a string argument, for example: charge(8, "type:pi pH:3.5") cannot be written in the form charge(8, "type:pi", "3.5"), while charge("pi", 8, "3.5") is accepted (backward compatibility).</li>
		</ol>
		<div style="font-family: Helvetia, Arial, sans-serif; font-size: x-small; text-align: center;">Copyright &copy; 1999-2013
			<a href="http://www.chemaxon.com">ChemAxon Ltd.</a>&nbsp;&nbsp;&nbsp;All rights reserved.
		</div>
			</body>
</html>
