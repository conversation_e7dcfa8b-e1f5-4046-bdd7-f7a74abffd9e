<?xml version="1.0" encoding="UTF-8"?>

<EvaluatorConfiguration Version="1.0">

	<Functions>
		<Function ID="abs" Class="chemaxon.nfunk.jep.function.Abs">
			<UserParam Name="number" Type="int/double" Mandatory="true"></UserParam>
			<ReturnType>int/double</ReturnType>
			<Description>
				<![CDATA[returns the absolute value of a number]]>
			</Description>
			<ReturnValue>
				<![CDATA[the absolute value]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[integer or real number]]>
			</Parameters>
			<GeneralExample>
				<![CDATA[<code>abs(7)</code>returns <code>7</code><br/><code>abs(-4.9)</code>returns <code>4.9</code>]]>
			</GeneralExample>
			<MoleculeContextExample>
				<![CDATA[<code>abs(7)</code>returns <code>7</code><br/><code>abs(-4.9)</code>returns <code>4.9</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>abs(7)</code>returns <code>7</code><br/><code>abs(-4.9)</code>returns <code>4.9</code>]]>
			</ReactionContextExample>
		</Function>
		<Function ID="booleanToNumber" Class="chemaxon.jep.function.BooleanToNumber">
			<UserParam Name="boolean" Type="boolean" Mandatory="true"></UserParam>
			<ReturnType>int/double</ReturnType>
			<Description>
				<![CDATA[returns the number representation of a boolean value (<code>true</code> = <code>1</code>, <code>false</code> = <code>0</code>),
				or the number itself, if the input is a number]]>
			</Description>
			<ReturnValue>
				<![CDATA[the number representation of a boolean value]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[boolean or number]]>
			</Parameters>
			<GeneralExample>
				<![CDATA[<code>booleanToNumber(1&lt;2)</code>returns <code>1</code><br/><code>booleanToNumber(2+2==5)</code>returns <code>0</code><br/>
				<code>booleanToNumber(5*5)</code>returns <code>25</code>]]>
			</GeneralExample>
			<MoleculeContextExample>
				<![CDATA[<code>booleanToNumber(hasValenceError())</code>returns <code>0</code> for molecules having no valence error]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>booleanToNumber(ringBond(reactant(0), bond(ratom(1), ratom(2))))</code>returns <code>1</code> if reactant atoms
				matching maps 1 and 2 in the reaction equation are connected by a ring bond in the corresponding reactant, <code>0</code> otherwise]]>
			</ReactionContextExample>
		</Function>
		<Function ID="filter" Class="chemaxon.jep.function.Filter">
			<UserParam Name="target" Type="Molecule"></UserParam>
			<UserParam Name="target atom" Type="MolAtom/int" Multiple="true"></UserParam>
			<UserParam Name="expression" Type="String" Mandatory="true"></UserParam>
			<ReturnType>int[]</ReturnType>
			<Description>
				<![CDATA[filters target atoms by filtering condition]]>
			</Description>
			<ReturnValue>
				<![CDATA[target atom indices satisfying the filtering condition]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[target atom indices / objects or index / atom object array (optional, all atoms taken if omitted), filtering
		condition (boolean expression)]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>filter("charge() > 0")</code> returns the indices of atoms with positive partial charge in the input
		molecule <code>filter(6, 7, 8, 9, "match('[#8][C:1]=O', 1)")</code> returns the carboxylic carbons out of atoms <code>6,
		7, 8, 9</code> in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>filter(reactant(0), "charge() > 0")</code> returns the indices of atoms with positive partial charge in
		the first reactant <code>filter(patom(1), patom(2), "match('[#8][C:1]=O', 1)")</code> returns the carboxylic carbons out
		of product atoms matching map <code>1</code> or <code>2</code> in the reaction equation (note, that these atoms are
		supposed to be in the same product molecule)]]>
			</ReactionContextExample>
		</Function>
		<Function ID="array" Class="chemaxon.jep.function.IntArray">
			<UserParam Name="element" Type="MolAtom/int" Multiple="true"></UserParam>
			<ReturnType>int[]</ReturnType>
			<Description>
				<![CDATA[constructs an integer array from its arguments]]>
			</Description>
			<ReturnValue>
				<![CDATA[the integer array]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[integers or MolAtom objects]]>
			</Parameters>
			<GeneralExample>
				<![CDATA[<code>array(2, 5, 6, 8)</code>]]>
			</GeneralExample>
			<MoleculeContextExample>
				<![CDATA[<code>array(2, 5, 6, 8)</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>array(ratom(2), ratom(5), ratom(6), ratom(8))</code> <code>array(patom(2), patom(5), patom(6), patom(8))</code>]]>
			</ReactionContextExample>
		</Function>
		<Function ID="in" Class="chemaxon.jep.function.In">
			<UserParam Name="element" Type="MolAtom/int"></UserParam>
			<UserParam Name="array" Type="int[]"></UserParam>
			<ReturnType>boolean</ReturnType>
			<Description>
				<![CDATA[determines whether an integer / atom index is contained in a given integer array]]>
			</Description>
			<ReturnValue>
				<![CDATA[<code>true</code> if the array contains the specified integer, <code>false</code> otherwise]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[an integer or MolAtom object and an integer array]]>
			</Parameters>
			<GeneralExample>
				<![CDATA[<code>in(5, array(3, 5, 1))</code> returns <code>true</code> <code>in(2, array(3, 5, 1))</code> returns <code>false</code>]]>
			</GeneralExample>
			<MoleculeContextExample>
				<![CDATA[<code>in(3, maxatom("charge()", 2))</code> returns <code>true</code> if the partial charge on atom <code>3</code>
		is within the first <code>2</code> largest partial charges in the input molecule <code>in(3, minatom("pol()", 4))</code>
		returns <code>true</code> if the polarizability on atom <code>3</code> is within the first <code>4</code> smallest
		polarizability values in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>in(ratom(3), maxatom(reactant(0), "charge()", 2))</code> returns <code>true</code> if the partial charge
		on reactant atom matching map <code>3</code> in the reaction equation is within the first <code>2</code> largest
		partial charges in the first reactant <code>in(patom(1), minatom(product(1), "pol()", 4))</code> returns <code>true</code>
		if the polarizability on product atom matching map <code>1</code> in the reaction equation is within the first <code>4</code>
		smallest polarizability values in the second product]]>
			</ReactionContextExample>
		</Function>
		<Function ID="min" Class="chemaxon.jep.function.Min">
			<UserParam Name="element" Type="int/double/Collection" Multiple="true"></UserParam>
			<ReturnType>int/double</ReturnType>
			<Description>
				<![CDATA[takes minimum of its array and/or numerical parameters]]>
			</Description>
			<ReturnValue>
				<![CDATA[the minimum value]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[integers, real numbers, integer arrays, real number arrays]]>
			</Parameters>
			<GeneralExample>
				<![CDATA[<code>min(2, 8, 6)</code> returns <code>2</code> <code>max(3.4, 5.6, 1.2)</code> returns <code>5.6</code>]]>
			</GeneralExample>
			<MoleculeContextExample>
				<![CDATA[<code>min(charge(0), charge(2))</code> returns the least of the partial charge values on atoms <code>0</code>
		and <code>2</code> <code>max(charge())</code> returns the maximal partial charge value on the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>min(charge(ratom(2)), charge(ratom(3)))</code> returns the least of the partial charge values on reactant
		atoms matching maps <code>2</code> and <code>3</code> in the reaction equation <code>max(charge(product(0)))</code>
		returns the maximal partial charge value on the first product]]>
			</ReactionContextExample>
		</Function>
		<Function ID="max" Class="chemaxon.jep.function.Max">
			<UserParam Name="element" Type="int/double/Collection" Multiple="true"></UserParam>
			<ReturnType>int/double</ReturnType>
			<Description>
				<![CDATA[takes maximum of its array and/or numerical parameters]]>
			</Description>
			<ReturnValue>
				<![CDATA[the maximum value]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[integers, real numbers, integer arrays, real number arrays]]>
			</Parameters>
			<GeneralExample>
				<![CDATA[<code>min(2, 8, 6)</code> returns <code>2</code> <code>max(3.4, 5.6, 1.2)</code> returns <code>5.6</code>]]>
			</GeneralExample>
			<MoleculeContextExample>
				<![CDATA[<code>min(charge(0), charge(2))</code> returns the least of the partial charge values on atoms <code>0</code>
		and <code>2</code> <code>max(charge())</code> returns the maximal partial charge value on the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>min(charge(ratom(2)), charge(ratom(3)))</code> returns the least of the partial charge values on reactant
		atoms matching maps <code>2</code> and <code>3</code> in the reaction equation <code>max(charge(product(0)))</code>
		returns the maximal partial charge value on the first product]]>
			</ReactionContextExample>
		</Function>
		<Function ID="minAtom" Class="chemaxon.jep.function.MinAtom">
			<UserParam Name="target" Type="Molecule"></UserParam>
			<UserParam Name="atom" Type="int" Multiple="true"></UserParam>
			<UserParam Name="expression" Type="String"></UserParam>
			<UserParam Name="number of items" Type="int"></UserParam>
			<ReturnType>int/int[]</ReturnType>
			<Description>
				<![CDATA[evaluates objective function for each atom, finds smallest value(s)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the atom index / indices corresponding to the smallest evaluation result(s)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[target atom indices / objects or index / atom object array (optional, all atoms taken if omitted), the
		objective function (as inner expression string), the number of smallest values to be taken (optional, takes
		only one if omitted)]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>minAtom("charge('7.4')")</code> returns the atom index corresponding to minimum partial charge in the
		major microspecies at pH <code>7.4</code> of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>minAtom(reactant(0), "charge('7.4')")</code> returns the atom index corresponding to minimum partial
		charge in the major microspecies at pH <code>7.4</code> of the first reactant]]>
			</ReactionContextExample>
		</Function>
		<Function ID="maxAtom" Class="chemaxon.jep.function.MaxAtom">
			<UserParam Name="target" Type="Molecule"></UserParam>
			<UserParam Name="atom" Type="int" Multiple="true"></UserParam>
			<UserParam Name="expression" Type="String"></UserParam>
			<UserParam Name="number of items" Type="int"></UserParam>
			<ReturnType>int/int[]</ReturnType>
			<Description>
				<![CDATA[evaluates objective function for each atom, finds largest value(s)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the atom index / indices corresponding to the largest evaluation result(s)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[target atom indices / objects or index / atom object array (optional, all atoms taken if omitted), the
		objective function (as inner expression string), the number of largest values to be taken (optional, takes
		only one if omitted)]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>maxAtom(6, 7, 8, 9, "charge('7.4')", 2)</code>
		selects the two largest partial charges on atoms <code>6, 7, 8, 9</code> in the major microspecies at pH <code>7.4</code>
		of the input molecule and returns the corresponding indices]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>maxAtom(patom(1), patom(2), patom(3), "charge('7.4')", 2)</code>
		selects the two largest partial charges on product atoms matching maps <code>1, 2, 3</code> in the major microspecies
		at pH <code>7.4</code> of the product molecule of these atoms and returns the corresponding indices (note, that these
		atoms are supposed to be in the same product molecule)]]>
			</ReactionContextExample>
		</Function>
		<Function ID="minValue" Class="chemaxon.jep.function.MinValue">
			<UserParam Name="target" Type="Molecule"></UserParam>
			<UserParam Name="atom" Type="int" Multiple="true"></UserParam>
			<UserParam Name="expression" Type="String"></UserParam>
			<UserParam Name="number of items" Type="int"></UserParam>
			<ReturnType>int/int[]/double/double[]</ReturnType>
			<Description>
				<![CDATA[evaluates objective function for each atom, finds smallest value(s)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the smallest evaluation result(s)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[target atom indices / objects or index / atom object array (optional, all atoms taken if omitted), the
		objective function (as inner expression string), the number of smallest values to be taken (optional, takes
		only one if omitted)]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>minValue("charge('7.4')")</code> returns the minimum partial charge in the major microspecies at pH <code>7.4</code>
		of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>minValue(reactant(0), "charge('7.4')")</code> returns the minimum partial charge in the major
		microspecies at pH <code>7.4</code> of the first reactant]]>
			</ReactionContextExample>
		</Function>
		<Function ID="maxValue" Class="chemaxon.jep.function.MaxValue">
			<UserParam Name="target" Type="Molecule"></UserParam>
			<UserParam Name="atom" Type="int" Multiple="true"></UserParam>
			<UserParam Name="expression" Type="String"></UserParam>
			<UserParam Name="number of items" Type="int"></UserParam>
			<ReturnType>int/int[]/double/double[]</ReturnType>
			<Description>
				<![CDATA[evaluates objective function for each atom, finds largest value(s)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the largest evaluation result(s)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[target atom indices / objects or index / atom object array (optional, all atoms taken if omitted), the
		objective function (as inner expression string), the number of largest values to be taken (optional, takes
		only one if omitted)]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>maxValue(6, 7, 8, 9, "charge('7.4')", 2)</code> returns the two largest partial charges on
		atoms <code>6, 7, 8, 9</code> in the major microspecies at pH <code>7.4</code> of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>maxValue(patom(1), patom(2), patom(3), "charge('7.4')", 2)</code>
		returns the two largest partial charges on product atoms matching maps <code>1, 2, 3</code> in the major microspecies
		at pH <code>7.4</code> of the product molecule of these atoms (note, that these atoms are supposed to be in the same
		product molecule)]]>
			</ReactionContextExample>
		</Function>
		<Function ID="sortAsc" Class="chemaxon.jep.function.SortAsc">
			<UserParam Name="elements" Type="int[]/double[]/List"></UserParam>
			<ReturnType>int[]/double[]</ReturnType>
			<Description>
				<![CDATA[sorts an array in ascending order]]>
			</Description>
			<ReturnValue>
				<![CDATA[the sorted array]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[integer array or real number array]]>
			</Parameters>
			<GeneralExample>
				<![CDATA[<code>sortAsc(array(3.4, 5.6, 1.2))</code> returns <code>array(1.2, 3.4, 5.6)</code>]]>
			</GeneralExample>
			<MoleculeContextExample>
				<![CDATA[<code>sortAsc(charge())</code> returns the partial charge values in ascending order]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>sortAsc(charge(reactant(1)))</code> returns the partial charge values of the second reactant in ascending
		order]]>
			</ReactionContextExample>
		</Function>
		<Function ID="sortDesc" Class="chemaxon.jep.function.SortDesc">
			<UserParam Name="elements" Type="int[]/double[]/List"></UserParam>
			<ReturnType>int[]/double[]</ReturnType>
			<Description>
				<![CDATA[sorts an array in descending order]]>
			</Description>
			<ReturnValue>
				<![CDATA[the sorted array]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[integer array or real number array]]>
			</Parameters>
			<GeneralExample>
				<![CDATA[<code>sortDesc(array(3.4, 5.6, 1.2))</code> returns <code>array(5.6, 3.4, 1.2)</code>]]>
			</GeneralExample>
			<MoleculeContextExample>
				<![CDATA[<code>sortDesc(pka("basic"))</code> returns the basic p<i>K</i><sub>a</sub> values in descending order]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>sortDesc(pka(product(0), "basic"))</code> returns the basic p<i>K</i><sub>a</sub> values of the first
		product in descending order]]>
			</ReactionContextExample>
		</Function>
		<Function ID="eval" Class="chemaxon.jep.function.AtomEvaluatorFunction"></Function>
		<Function ID="count" Class="chemaxon.jep.function.Count">
			<UserParam Name="elements" Type="int[]/double[]/Collection"></UserParam>
			<ReturnType>int</ReturnType>
			<Description>
				<![CDATA[determines the number of elements in an array]]>
			</Description>
			<ReturnValue>
				<![CDATA[the number of array elements]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[integer array or real number array]]>
			</Parameters>
			<GeneralExample>
				<![CDATA[<code>count(array(3.4, 5.6, 1.2))</code> returns <code>3</code>]]>
			</GeneralExample>
			<MoleculeContextExample>
				<![CDATA[<code>count(filter("charge() > 0"))</code> returns the number of atoms with positive charge <code>count(filter("match('[#8][C:1]=O', 1"))</code>
		returns the number of carboxylic carbons]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>count(filter(reactant(1), "charge() > 0"))</code> returns the number of atoms with positive charge in the
		second reactant <code>count(filter(product(0), "match('[#8][C:1]=O', 1"))</code> returns the number of carboxylic
		carbons in the first product]]>
			</ReactionContextExample>
		</Function>
		<Function ID="sum" Class="chemaxon.jep.function.Sum">
			<UserParam Name="elements" Type="int[]/double[]/Collection"></UserParam>
			<ReturnType>int/double</ReturnType>
			<Description>
				<![CDATA[computes the sum of array elements]]>
			</Description>
			<ReturnValue>
				<![CDATA[the sum]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[integer array or real number array]]>
			</Parameters>
			<GeneralExample>
				<![CDATA[<code>sum(array(3.4, 5.6, 1.2))</code> returns <code>10.2</code>]]>
			</GeneralExample>
			<MoleculeContextExample>
				<![CDATA[<code>sum(charge())</code> returns the sum of charge values <code>sum(pol())</code> returns the sum of atom
		polarizability values]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>sum(charge(reactant(0)))</code> returns the sum of charge values in the first reactant <code>sum(pol(product(0)))</code>
		returns the sum of atom polarizability values in the first product]]>
			</ReactionContextExample>
		</Function>
		<Function ID="atomProp" Class="chemaxon.jep.function.AtomProperties">
		<!--
			<ReturnType>int/boolean</ReturnType>
			<Description>
				<![CDATA[returns various atomic properties:
				<ul>
					<li>formal charge
					</li><li>hydrogen count
					</li><li>bond plus implicit H count
					</li><li>the sum of bond orders and query H atoms
					</li><li>atomic number
					</li><li>atom map
					</li><li>aromatic (boolean)
				</li></ul>]]>
			</Description>
			<ReturnValue>
				<![CDATA[various atomic properties]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[atom index or MolAtom object]]>
			</Parameters>
			<Description>
				<![CDATA[returns a molecule property (SDF field value)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the molecule property]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the property key (SDF field name)]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>formalCharge(0)</code> returns the formal charge of atom <code>0</code> <code>map(3)</code> returns the
		atom map number of atom <code>3</code> <code>connections(2)</code> returns the number of connections of atom <code>2</code>
<code>valence(0)</code> returns the valence of atom <code>0</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>atno(ratom(1))</code> returns the atomic number of the reactant atom matching map <code>1</code> in the
		reaction equation <code>hCount(patom(3))</code> returns the hydrogen count on the product atom matching map <code>3</code>
		in the reaction equation <code>arom(patom(2))</code> returns <code>true</code> if the product atom matching map <code>2</code>
		in the reaction equation has an aromatic bond]]>
			</ReactionContextExample>
		-->
		</Function>
		<Function ID="hCount" Class="chemaxon.jep.function.AtomProperties">
			<Param Name="property" Value="hcount"></Param>
			<UserParam Name="target" Type="Molecule"></UserParam>
			<UserParam Name="atom" Type="MolAtom/int"></UserParam>
			<ReturnType>int</ReturnType>
			<Description>
				<![CDATA[returns the hydrogen count of an atom]]>
			</Description>
			<ReturnValue>
				<![CDATA[the hydrogen count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[atom index or MolAtom object]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>hCount(0)</code> returns the hydrogen count of atom <code>0</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>hCount(patom(3))</code> returns the hydrogen count on the product atom matching map <code>3</code>
		in the reaction equation]]>
			</ReactionContextExample>
		</Function>
		<Function ID="connections" Class="chemaxon.jep.function.AtomProperties">
			<Param Name="property" Value="connections"></Param>
			<UserParam Name="target" Type="Molecule"></UserParam>
			<UserParam Name="atom" Type="MolAtom/int"></UserParam>
			<ReturnType>int</ReturnType>
			<Description>
				<![CDATA[returns the bond plus implicit H count of an atom]]>
			</Description>
			<ReturnValue>
				<![CDATA[the bond plus implicit H count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[atom index or MolAtom object]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>connections(2)</code> returns the number of connections of atom <code>2</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>connections(ratom(1))</code> returns the number of connections of the reactant atom matching map <code>1</code> in the
		reaction equation]]>
			</ReactionContextExample>
		</Function>
		<Function ID="valence" Class="chemaxon.jep.function.AtomProperties">
			<Param Name="property" Value="valence"></Param>
			<UserParam Name="target" Type="Molecule"></UserParam>
			<UserParam Name="atom" Type="MolAtom/int"></UserParam>
			<ReturnType>int</ReturnType>
			<Description>
				<![CDATA[returns the sum of bond orders and query H atoms of an atom]]>
			</Description>
			<ReturnValue>
				<![CDATA[the sum of bond orders and query H atoms]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[atom index or MolAtom object]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>valence(0)</code> returns the valence of atom <code>0</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>valence(ratom(1))</code> returns the valence of the reactant atom matching map <code>1</code> in the
		reaction equation]]>
			</ReactionContextExample>
		</Function>
		<Function ID="radicalCount" Class="chemaxon.jep.function.AtomProperties">
			<Param Name="property" Value="radicalcount"></Param>
			<UserParam Name="target" Type="Molecule"></UserParam>
			<UserParam Name="atom" Type="MolAtom/int"></UserParam>
			<ReturnType>int</ReturnType>
			<Description>
				<![CDATA[returns the radical count of an atom]]>
			</Description>
			<ReturnValue>
				<![CDATA[the radical count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[atom index or MolAtom object]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>radicalCount(0)</code> returns the radical count of atom <code>0</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>radicalCount(patom(3))</code> returns the radical count on the product atom matching map <code>3</code>
		in the reaction equation]]>
			</ReactionContextExample>
		</Function>
		<Function ID="atno" Class="chemaxon.jep.function.AtomProperties">
			<Alias>atomicNumber</Alias>
			<Param Name="property" Value="atno"></Param>
			<UserParam Name="target" Type="Molecule"></UserParam>
			<UserParam Name="atom" Type="MolAtom/int"></UserParam>
			<ReturnType>int</ReturnType>
			<Description>
				<![CDATA[returns the atomic number]]>
			</Description>
			<ReturnValue>
				<![CDATA[the atomic number]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[atom index or MolAtom object]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>atno(0)</code> returns the atomic number of atom <code>0</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>atno(ratom(1))</code> returns the atomic number of the reactant atom matching map <code>1</code>]]>
			</ReactionContextExample>
		</Function>
		<Function ID="molAtom" Class="chemaxon.jep.function.MolAtom">
			<UserParam Name="atomicNumber" Type="int"></UserParam>
			<ReturnType>chemaxon.struc.MolAtom</ReturnType>
			<Description>
				<![CDATA[creates a MolAtom]]>
			</Description>
			<ReturnValue>
				<![CDATA[the MolAtom object]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[atomic number]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>molAtom(6)</code> returns a carbon atom]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>molAtom(6)</code> returns a carbon atom]]>
			</ReactionContextExample>
		</Function>
		<Function ID="map" Class="chemaxon.jep.function.AtomProperties">
			<Param Name="property" Value="map"></Param>
			<UserParam Name="target" Type="Molecule"></UserParam>
			<UserParam Name="atom" Type="MolAtom/int"></UserParam>
			<ReturnType>int</ReturnType>
			<Description>
				<![CDATA[returns the atom map number]]>
			</Description>
			<ReturnValue>
				<![CDATA[the atom map number]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[atom index or MolAtom object]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>map(3)</code> returns the atom map number of atom <code>3</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>map(patom(3))</code> returns the atom map number on the product atom matching map <code>3</code>
		in the reaction equation]]>
			</ReactionContextExample>
		</Function>
		<Function ID="arom" Class="chemaxon.jep.function.AtomProperties">
			<Param Name="property" Value="arom"></Param>
			<UserParam Name="target" Type="Molecule"></UserParam>
			<UserParam Name="atom" Type="MolAtom/int"></UserParam>
			<ReturnType>boolean</ReturnType>
			<Description>
				<![CDATA[returns if the atom has an aromatic bond]]>
			</Description>
			<ReturnValue>
				<![CDATA[<code>true</code> if the atom has an aromatic bond, <code>false</code> otherwise]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[atom index or MolAtom object]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>arom(0)</code> returns if the atom <code>0</code> has an aromatic bond]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>arom(patom(2))</code> returns <code>true</code> if the product atom matching map <code>2</code>
		in the reaction equation has an aromatic bond]]>
			</ReactionContextExample>
		</Function>
		<Function ID="field" Class="chemaxon.jep.function.Property">
			<Alias>property</Alias>
			<UserParam Name="target" Type="Molecule"></UserParam>
			<UserParam Name="property name" Type="String" Mandatory="true"></UserParam>
			<Description>
				<![CDATA[returns a molecule property (SDF field value)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the molecule property]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the property key (SDF field name)]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>field('ACTIVITY')</code> returns the value of the ACTIVITY property (SDF field) <code>field('ACTIVITY') > 2</code>
		returns <code>1</code> if the ACTIVITY value is bigger than <code>2</code>, returns <code>0</code> otherwise]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>field(reactant(1), 'ACTIVITY')</code> returns the ACTIVITY property value of the second reactant <code>field(product(0), 'ACTIVITY') > field(reactant(0), 'ACTIVITY')</code>
		returns <code>1</code> if the ACTIVITY value of the first product is bigger than that of the first reactant, returns <code>0</code>
		otherwise]]>
			</ReactionContextExample>
		</Function>
		<Function ID="fieldAsString" Class="chemaxon.jep.function.PropertyAsString">
			<Alias>propertyAsString</Alias>
			<UserParam Name="target" Type="Molecule"></UserParam>
			<UserParam Name="property name" Type="String" Mandatory="true"></UserParam>
			<ReturnType>String</ReturnType>
			<Description>
				<![CDATA[returns a molecule property (SDF field value) as string]]>
			</Description>
			<ReturnValue>
				<![CDATA[the molecule property as string]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the property key (SDF field name)]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>fieldAsString('ID')</code> returns the value of the ID property (SDF field)]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>field(reactant(0), 'ID')</code> returns the value of the ID property field in first reactant]]>
			</ReactionContextExample>
		</Function>
		<!--
		<Function ID="property" Class="chemaxon.jep.function.Property">
			<UserParam Name="target" Type="Molecule"></UserParam>
			<UserParam Name="property name" Type="String" Mandatory="true"></UserParam>
		</Function>
		 -->
		<Function ID="atoms" Class="chemaxon.jep.function.Atoms">
			<UserParam Name="atom" Type="MolAtom/int" Multiple="true"></UserParam>
			<ReturnType>String</ReturnType>
		</Function>
		<Function ID="pair" Class="chemaxon.jep.function.Atoms">
			<Alias>bond</Alias>
			<Description>
				<![CDATA[converts two atoms or 0-based atom indexes into an "index1-index2" 1-based atom index setter string<br />
		(used for pairing atoms in <a href="#shortestpathdesc">shortestPath</a>)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the generated string]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[two atom indexes or two MolAtom objects]]>
			</Parameters>
			<GeneralExample>
				<![CDATA[<code>pair(2, 5)</code> returns "3-6"]]>
			</GeneralExample>
			<ReactionContextExample>
				<![CDATA[<code>pair(ratom(1), ratom(2))</code> returns "index1-index2" where "index1" and "index2" are the 1-based atom
		indexes of the reactant atoms matching map <code>1</code> and <code>2</code> in the reaction equation <code>bond(patom(2), patom(5)</code>
		returns "index1-index2" where "index1" and "index2" are the 1-based atom indexes of the product atoms matching map <code>2</code>
		and <code>5</code> in the reaction equation]]>
			</ReactionContextExample>
		</Function>
		<!--
		<Function ID="bond" Class="chemaxon.jep.function.Atoms"></Function>
		-->
		<Function ID="formalCharge" Class="chemaxon.jep.function.FormalCharge">
			<Alias>totalCharge</Alias>
			<UserParam Name="target" Type="Molecule"></UserParam>
			<UserParam Name="atom" Type="MolAtom/int"></UserParam>
			<ReturnType>int</ReturnType>
			<Description>
				<![CDATA[calculates formal charge of molecule]]>
			</Description>
			<ReturnValue>
				<![CDATA[the formal charge value]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[atom index or MolAtom object (optional)]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>formalCharge()</code> returns the formal charge of the input molecule
				<code>formalCharge(0)</code> returns the formal charge of atom <code>0</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[formalCharge(reactant(0)) returns the formal charge of the first reactant
				<code>formalCharge(ratom(1))</code> returns the formal charge of the reactant atom matching map <code>1</code>]]>
			</ReactionContextExample>
		</Function>
		<!--
		<Function ID="totalCharge" Class="chemaxon.jep.function.FormalCharge"></Function>
		-->
		<Function ID="molString" Class="chemaxon.jep.function.MolString">
			<Alias>molFormat</Alias>
			<Alias>molConvert</Alias>
			<UserParam Name="target" Type="Molecule/Molecule[]"></UserParam>
			<UserParam Name="format" Type="String" Mandatory="true"></UserParam>
			<UserParam Name="cleanDim" Type="int"></UserParam>
			<ReturnType>String</ReturnType>
			<Description>
				<![CDATA[returns the string representation of a molecule, or an array of molecules, in specified molecule format]]>
			</Description>
			<ReturnValue>
				<![CDATA[the string representation of the molecule(s)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
				<ul>
					<li>the <a href="../formats/formats.html">molecule format</a> (e.g. "mol", "sdf", "mrv", "smiles")</li>
					<li>the clean dimension</li>
				</ul>
				]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>molFormat("mrv")</code> returns the ChemAxon Marvin Document format representation of the input molecule
				<code>molFormat(tautomers(), "sdf", 2)</code> returns the SDF format representation of the tautomers of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[N / A (reaction rules are numerical)]]>
			</ReactionContextExample>
		</Function>
		<!--
		<Function ID="molFormat" Class="chemaxon.jep.function.MolString"></Function>
		-->
		<Function ID="molBinFormat" Class="chemaxon.jep.function.MolBinFormat">
			<Alias>molImage</Alias>
			<UserParam Name="target" Type="Molecule"></UserParam>
			<UserParam Name="format" Type="String"></UserParam>
			<ReturnType>byte[]</ReturnType>
			<Description>
				<![CDATA[returns the binary representation (image, pdf, GZIP compressed molecule file) of the molecule in specified format]]>
			</Description>
			<ReturnValue>
				<![CDATA[the binary representation (image, pdf, GZIP compressed molecule file) of the molecule]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the binary format with options (e.g. "jpeg", "png:w150,h150", "pdf", "gzip:sdf")]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>molImage("jpeg:w100,Q95,#ffff00")</code> returns the 100x100 JPEG image of the input molecule with yellow background, 95% quality]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[N / A (reaction rules are numerical)]]>
			</ReactionContextExample>
		</Function>
		<!--
		<Function ID="molImage" Class="chemaxon.jep.function.MolBinFormat"></Function>
		-->
		<Function ID="importMol" Class="chemaxon.jep.function.ImportMol">
			<UserParam Name="moString" Type="String" Mandatory="true"></UserParam>
			<ReturnType>Molecule</ReturnType>
			<Description>
				<![CDATA[imports and returns the molecule from its string representation]]>
			</Description>
			<ReturnValue>
				<![CDATA[the string representation of the molecule]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the molecule in string representation (e.g. "c1ccccc1")]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>importMol("c1ccccc1")</code> returns benzene molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[N / A (reaction rules are numerical)]]>
			</ReactionContextExample>
		</Function>
		<Function ID="hasValenceError" Class="chemaxon.jep.function.HasValenceError">
			<UserParam Name="target" Type="Molecule"></UserParam>
			<ReturnType>boolean</ReturnType>
			<Description>
				<![CDATA[determines if any atom in the molecule has valence error]]>
			</Description>
			<ReturnValue>
				<![CDATA[<code>true</code> in case of valence error, <code>false</code> otherwise]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>hasValenceError()</code> returns <code>true</code> if any atom in the molecule has valence error, <code>false</code> otherwise]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>hasValenceError(reactant(1))</code> returns <code>true</code> if any atom in the second reactant has valence error,
				<code>false</code> otherwise]]>
			</ReactionContextExample>
		</Function>
		<Function ID="whereIsValenceError" Class="chemaxon.jep.function.WhereIsValenceError">
			<UserParam Name="target" Type="Molecule"></UserParam>
			<ReturnType>int</ReturnType>
			<Description>
				<![CDATA[returns the index of the first atom with valence error, or <code>-1</code> if there is no valence error]]>
			</Description>
			<ReturnValue>
				<![CDATA[the index of the first atom with valence error, or <code>-1</code> if there is no valence error]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>whereIsValenceError()</code> returns the index of the first atom with valence error, or <code>-1</code>
				if there is no valence error in the molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>whereIsValenceError(reactant(1))</code> returns the index of the first atom with valence error in the second reactant,
				or <code>-1</code> f there is no valence error in the second reactant]]>
			</ReactionContextExample>
		</Function>
		<Function ID="hasAromatizationError" Class="chemaxon.jep.function.HasAromatizationError">
			<UserParam Name="target" Type="Molecule"></UserParam>
			<ReturnType>boolean</ReturnType>
			<Description>
				<![CDATA[determines if there is error in the aromatization of the molecule]]>
			</Description>
			<ReturnValue>
				<![CDATA[<code>true</code> if there is an error in the aromatization of the molecule, <code>false</code> otherwise]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>hasAromatizationError()</code> returns <code>true</code> if there is an error in the aromatization of the molecule,
				<code>false</code> otherwise]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>hasAromatizationError(reactant(0))</code> returns <code>true</code> if there is an error in the aromatization of the first reactant,
				<code>false</code> otherwise]]>
			</ReactionContextExample>
		</Function>
		<Function ID="hasRadical" Class="chemaxon.jep.function.HasRadical">
			<UserParam Name="target" Type="Molecule"></UserParam>
			<ReturnType>boolean</ReturnType>
			<Description>
				<![CDATA[determines if any atom in the molecule has radical]]>
			</Description>
			<ReturnValue>
				<![CDATA[<code>true</code> if any atom in the molecule has radical, <code>false</code> otherwise]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>hasRadical()</code> returns <code>true</code> if any atom in the molecule has radical, <code>false</code> otherwise]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>hasRadical(reactant(1))</code> returns <code>true</code> if any atom in the second reactant has radical,
				<code>false</code> otherwise]]>
			</ReactionContextExample>
		</Function>
		<Function ID="hasIsotope" Class="chemaxon.jep.function.HasIsotope">
			<UserParam Name="target" Type="Molecule"></UserParam>
			<ReturnType>boolean</ReturnType>
			<Description>
				<![CDATA[determines if any atom in the molecule is a specific isotope of the element]]>
			</Description>
			<ReturnValue>
				<![CDATA[<code>true</code> if any atom in the molecule is a specific isotope of the element, <code>false</code> otherwise]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>hasIsotope()</code> returns <code>true</code> if any atom in the molecule is a specific isotope of the element, <code>false</code> otherwise]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>hasIsotope(reactant(1))</code> returns <code>true</code> if any atom in the second reactant is a specific isotope of the element,
				<code>false</code> otherwise]]>
			</ReactionContextExample>
		</Function>
		<Function ID="isEmpty" Class="chemaxon.jep.function.IsEmpty">
			<UserParam Name="target" Type="Molecule"></UserParam>
			<ReturnType>boolean</ReturnType>
			<Description>
				<![CDATA[decides whether the given molecule is empty (does not contain any atoms, bonds, or non-empty S-groups)]]>
			</Description>
			<ReturnValue>
				<![CDATA[<code>true</code> if the molecule does not contain any atoms, bonds, or non-empty S-groups, <code>false</code> otherwise]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>isQuery()</code> returns <code>true</code> if the given molecule does not contain any atoms, bonds, or non-empty S-groups, <code>false</code> otherwise]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>isQuery(reactant(1))</code> returns <code>true</code> if the second reactant contains any atoms, bonds, or non-empty S-groups,
				<code>false</code> otherwise]]>
			</ReactionContextExample>
		</Function>
		<Function ID="isQuery" Class="chemaxon.jep.function.IsQuery">
			<UserParam Name="target" Type="Molecule"></UserParam>
			<ReturnType>boolean</ReturnType>
			<Description>
				<![CDATA[decides whether the given molecule contains any query features]]>
			</Description>
			<ReturnValue>
				<![CDATA[<code>true</code> if the molecule contains any query features, <code>false</code> otherwise]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>isQuery()</code> returns <code>true</code> if the given molecule contains any query features, <code>false</code> otherwise]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>isQuery(reactant(1))</code> returns <code>true</code> if the second reactant contains any query features,
				<code>false</code> otherwise]]>
			</ReactionContextExample>
		</Function>
		<Function ID="isMarkush" Class="chemaxon.jep.function.IsMarkush">
			<UserParam Name="target" Type="Molecule"></UserParam>
			<ReturnType>boolean</ReturnType>
			<Category>Markush</Category>
			<Description>
				<![CDATA[decides whether the given molecule contains any Markush features]]>
			</Description>
			<ReturnValue>
				<![CDATA[<code>true</code> if the molecule contains any Markush features, <code>false</code> otherwise]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>isMarkush()</code> returns <code>true</code> if the given molecule contains any Markush features, <code>false</code> otherwise]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>isMarkush(reactant(1))</code> returns <code>true</code> if the second reactant contains any Markush features,
				<code>false</code> otherwise]]>
			</ReactionContextExample>
		</Function>
		<Function ID="fragments" Class="chemaxon.jep.function.Fragments">
			<UserParam Name="target" Type="Molecule"></UserParam>
			<ReturnType>Molecule[]</ReturnType>
			<Description>
				<![CDATA[converts the molecule to its disconnected fragments]]>
			</Description>
			<ReturnValue>
				<![CDATA[the disconnected fragments of the molecule]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>fragments()</code> returns the disconnected fragments of the molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>fragments(reactant(1))</code> returns the disconnected fragments of the second reactant]]>
			</ReactionContextExample>
		</Function>
		<Function ID="elementalAnalysis" Class="chemaxon.jep.function.ElementalAnalysis">
			<UserParam Name="formula" Type="String"></UserParam>
			<UserParam Name="function" Type="String"></UserParam>
			<ReturnType>String</ReturnType>
			<Category>Elemental Analysis</Category>
			<Description>
				<![CDATA[performs elemental analysis on molecule represented by formula]]>
			</Description>
			<ReturnValue>
				<![CDATA[the value calculated by the invoked function (e.g., mass)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
				<ul>
				  <li>molecular formula</li>
				  <li>available functions:
				    <ul>
				      <li>atomCount</li>
				      <li>mass</li>
				      <li>massPrecision</li>
				      <li>exactMass</li>
				      <li>exactMassPrecision</li>
				      <li>formula</li>
				      <li>isotopeFormula</li>
				      <li>dotDisconnectedFormula</li>
				      <li>dotDisconnectedIsotopeFormula</li>
				    </ul>
				  </li>
				</ul>
				]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>elementalAnalysis('C12H25O', 'mass')</code> returns the mass of molecule represented by formula]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[N / A]]>
			</ReactionContextExample>
		</Function>	
		<Function ID="bondCount" Class="chemaxon.jep.function.BondCount">
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the bond count]]>
			</Description>
			<ReturnValue>
				<![CDATA[the bond count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>bondCount()</code> returns the number of bonds in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>bondCount(reactant(0))</code> returns the number of bonds in the first reactant <code>bondCount(product(1))</code>
		returns the number of bonds in the second product]]>
			</ReactionContextExample>
		</Function>
		<Function ID="check" Class="chemaxon.jep.function.Check">
			<UserParam Name="target" Type="Molecule"></UserParam>
			<UserParam Name="config" Type="String" Mandatory="true"></UserParam>
			<ReturnType>String</ReturnType>
			<Category>Structure Checker</Category>
			<Description>
				<![CDATA[checks the structure for errors, according to the configuration]]>
			</Description>
			<ReturnValue>
				<![CDATA[the error report]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[Structure checker/fixer configuration string]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>check("aromaticity..valence")</code> checks for aromaticity and valence errors, and returns the error report]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>check(reactant(0), "aromaticity..valence")</code> checks for aromaticity and valence errors in first reactant, and returns the error report]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/structurechecker/checker.html</UsersGuideURL>
		</Function>
		<Function ID="isValid" Class="chemaxon.jep.function.IsValid">
			<UserParam Name="target" Type="Molecule"></UserParam>
			<UserParam Name="config" Type="String" Mandatory="true"></UserParam>
			<ReturnType>boolean</ReturnType>
			<Category>Structure Checker</Category>
			<Description>
				<![CDATA[checks the structure for errors, according to the configuration]]>
			</Description>
			<ReturnValue>
				<![CDATA[true, if the structure is valid (has no errors), false otherwise]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[Structure checker/fixer configuration string]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>isValid("aromaticity..valence")</code> checks for aromaticity and valence errors, and returns if the structure is valid]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>isValid(reactant(0), "aromaticity..valence")</code> checks for aromaticity and valence errors in first reactant, and returns if it is valid]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/structurechecker/checker.html</UsersGuideURL>
		</Function>
		<Function ID="fix" Class="chemaxon.jep.function.Fix">
			<UserParam Name="target" Type="Molecule"></UserParam>
			<UserParam Name="config" Type="String" Mandatory="true"></UserParam>
			<ReturnType>Molecule</ReturnType>
			<Category>Structure Checker</Category>
			<Description>
				<![CDATA[checks the structure for errors, according to the configuration, and then fixes the errors]]>
			</Description>
			<ReturnValue>
				<![CDATA[the fixed molecule]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[Structure checker/fixer configuration string]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>fix("explicitH..isotope->converttoelementalform")</code> searches for explicit hydrogens and isotopes, and removes them or converts them to elemental form]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>fix(product(1), "explicitH..isotope->converttoelementalform")</code> searches for explicit hydrogens and isotopes in second product, and removes them or converts them to elemental form]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/structurechecker/checker.html</UsersGuideURL>
		</Function>	
	</Functions>

	<!--    Plugins    -->

	<Plugins>
		<UserParam Name="target" Type="Molecule"></UserParam>
		<UserParam Name="atom" Type="MolAtom/int" Multiple="true"></UserParam>
		<UserParam Name="result type" Type="String"></UserParam>
		<UserParam Name="plugin specific argument" Type="String"></UserParam>

		<!--
		<Plugin ID="majorMs" Class="chemaxon.marvin.calculations.MajorMicrospeciesPlugin" JAR="MajorMicrospeciesPlugin.jar"></Plugin>
		-->
		<Plugin ID="majorMicrospecies" Class="chemaxon.marvin.calculations.MajorMicrospeciesPlugin" JAR="MajorMicrospeciesPlugin.jar">
			<Alias>majorMs</Alias>
			<ReturnType>Molecule</ReturnType>
			<Category>Protonation</Category>
			<Description>
				<![CDATA[calculates major microspecies at specified pH]]>
			</Description>
			<ReturnValue>
				<![CDATA[the major microspecies]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the pH value as string
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>majorMicrospecies("7.4")</code> returns the major microspecies of the input molecule at pH <code>7.4</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>majorMicrospecies(reactant(0), "7.4")</code> returns the major microspecies of the first reactant at pH <code>7.4</code>
<code>majorMicrospecies(product(1), "7.4")</code> returns the major microspecies of the second product at pH <code>7.4</code>]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/protonation.html#ms</UsersGuideURL>
		</Plugin>
		<!--
		<Plugin ID="msCount" Class="chemaxon.marvin.calculations.MajorMicrospeciesPlugin" JAR="MajorMicrospeciesPlugin.jar">
			<Param Name="type" Value="ms-count"></Param>
		</Plugin>
		-->
		<Plugin ID="microspeciesCount" Class="chemaxon.marvin.calculations.MajorMicrospeciesPlugin" JAR="MajorMicrospeciesPlugin.jar">
			<Alias>msCount</Alias>
			<Param Name="type" Value="ms-count"></Param>
			<ReturnType>int</ReturnType>
			<Category>Protonation</Category>
			<Description>
				<![CDATA[calculates the number of microspecies at specified pH]]>
			</Description>
			<ReturnValue>
				<![CDATA[the number of microspecies]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>microspeciesCount()</code> returns the number of microspecies of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>microspeciesCount(reactant(0))</code> returns the number of microspecies of the first reactant <code>microspeciesCount(product(1))</code>
		returns the number of microspecies of the second product]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/protonation.html#ms</UsersGuideURL>
		</Plugin>
		<Plugin ID="microspecies" Class="chemaxon.marvin.calculations.MajorMicrospeciesPlugin" JAR="MajorMicrospeciesPlugin.jar">
			<Alias>ms</Alias>
			<Param Name="type" Value="ms"></Param>
			<ReturnType>Molecule</ReturnType>
			<Category>Protonation</Category>
			<Description>
				<![CDATA[calculates microspecies at specified pH]]>
			</Description>
			<ReturnValue>
				<![CDATA[the microspecies]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the pH value as string
			</li><li>the microspecies index by descending order of microspecies distributions
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>microspecies("7.4", 1)</code> returns the microspecies of the input molecule with second largest distribution at pH
		<code>7.4</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>microspecies(reactant(0), "7.4", 2)</code> returns the microspecies of the first reactant with third largest
		distribution at pH <code>7.4</code> <code>microspecies(product(1), "7.4", 1)</code> returns the microspecies of the second product
		with second largest distribution at pH <code>7.4</code>]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/protonation.html#ms</UsersGuideURL>
		</Plugin>
		<!--
		<Plugin ID="ms" Class="chemaxon.marvin.calculations.MajorMicrospeciesPlugin" JAR="MajorMicrospeciesPlugin.jar">
			<Param Name="type" Value="ms"></Param>
		</Plugin>
		-->
		<!--
		<Plugin ID="msDistr" Class="chemaxon.marvin.calculations.MajorMicrospeciesPlugin" JAR="MajorMicrospeciesPlugin.jar">
			<Param Name="type" Value="ms-distr"></Param>
		</Plugin>
		-->
		<Plugin ID="microspeciesDistribution" Class="chemaxon.marvin.calculations.MajorMicrospeciesPlugin" JAR="MajorMicrospeciesPlugin.jar">
			<Alias>msDistr</Alias>
			<Param Name="type" Value="ms-distr"></Param>
			<ReturnType>double</ReturnType>
			<Category>Protonation</Category>
			<Description>
				<![CDATA[calculates microspecies distribution at specified pH]]>
			</Description>
			<ReturnValue>
				<![CDATA[the microspecies distribution]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the pH value as string
			</li><li>the microspecies index by descending order of microspecies distributions
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>microspeciesDistribution("5.4", 0)</code> returns the largest microspecies distribution of the input molecule at pH <code>5.4</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>microspeciesDistribution(reactant(0), "5.4", 2)</code> returns the third largest microspecies distribution of the first
		reactant at pH <code>5.4</code> <code>microspeciesDistribution(product(1), "3.2", 1)</code> returns the second largest microspecies
		distribution of the second product at pH <code>3.2</code>]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/protonation.html#ms</UsersGuideURL>
		</Plugin>
		<Plugin ID="allTautomer" Class="chemaxon.marvin.calculations.TautomerizationPlugin" JAR="MultiformPlugin.jar">
			<Category>Isomers</Category>
			<ReturnType>Molecule</ReturnType>
			<Description>
				<![CDATA[constructs a tautomeric form]]>
			</Description>
			<ReturnValue>
				<![CDATA[the tautomer]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the tautomer index (0-based)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>tautomer(0)</code> returns the first tautomer of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>tautomer(reactant(0), 1)</code> returns the second tautomer of the first reactant]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="canonicalTautomer" Class="chemaxon.marvin.calculations.TautomerizationPlugin" JAR="MultiformPlugin.jar">
			<Param Name="canonical" Value="true"></Param>
			<ReturnType>Molecule</ReturnType>
			<Category>Isomers</Category>
			<Description>
				<![CDATA[constructs the canonical tautomer structure]]>
			</Description>
			<ReturnValue>
				<![CDATA[the canonical tautomer structure]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>canonicalTautomer()</code> returns the canonical tautomer structure of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>canonicalTautomer(reactant(0))</code> returns the canonical tautomer structure of the first reactant/td>]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="genericTautomer" Class="chemaxon.marvin.calculations.TautomerizationPlugin" JAR="MultiformPlugin.jar">
			<Param Name="generic" Value="true"></Param>
			<ReturnType>Molecule</ReturnType>
			<Category>Isomers</Category>
			<Description>
				<![CDATA[constructs the generic tautomer structure]]>
			</Description>
			<ReturnValue>
				<![CDATA[the generic tautomer structure]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>genericTautomer()</code> returns the generic tautomer structure of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>genericTautomer(reactant(0))</code> returns the generic tautomer structure of the first reactant/td>]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="mostStableTautomer" Class="chemaxon.marvin.calculations.TautomerizationPlugin" JAR="MultiformPlugin.jar">
			<Param Name="moststable" Value="true"></Param>
			<ReturnType>Molecule</ReturnType>
			<Category>Isomers</Category>
			<Description>
				<![CDATA[deprecated, use majorTautomer instead. constructs the most stable tautomer structure]]>
			</Description>
			<ReturnValue>
				<![CDATA[the most stable tautomer structure]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>mostStableTautomer()</code> returns the most stable tautomer structure of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>mostStableTautomer(reactant(0))</code> returns the most stable tautomer structure of the first reactant/td>]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="majorTautomer" Class="chemaxon.marvin.calculations.TautomerizationPlugin" JAR="MultiformPlugin.jar">
			<Param Name="major" Value="true"></Param>
			<ReturnType>Molecule</ReturnType>
			<Category>Isomers</Category>
			<Description>
				<![CDATA[constructs the major tautomer structure]]>
			</Description>
			<ReturnValue>
				<![CDATA[the major tautomer structure]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
			<li>the pH value as string (set if pH effect should be considered)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>majorTautomer()</code> returns the major tautomer structure of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>majorTautomer(reactant(0))</code> returns the major tautomer structure of the first reactant/td>]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="allTautomers" Class="chemaxon.marvin.calculations.TautomerizationPlugin" JAR="MultiformPlugin.jar">
			<Param Name="type" Value="structures"></Param>
			<ReturnType>Molecule[]</ReturnType>
			<Category>Isomers</Category>
			<Description>
				<![CDATA[constructs all tautomeric forms]]>
			</Description>
			<ReturnValue>
				<![CDATA[the tautomer array]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>tautomers()</code> returns all tautomers of the input molecule in an array]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>tautomers(reactant(0))</code> returns all tautomers of the first reactant in an array]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="tautomerCount" Class="chemaxon.marvin.calculations.TautomerizationPlugin" JAR="MultiformPlugin.jar">
			<Param Name="type" Value="count"></Param>
			<ReturnType>int</ReturnType>
			<Category>Isomers</Category>
			<Description>
				<![CDATA[calculates the number of tautomers]]>
			</Description>
			<ReturnValue>
				<![CDATA[the number of tautomers]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>tautomerCount()</code> returns the number of tautomers of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>tautomerCount(reactant(0))</code> returns the number of tautomers of the first reactant]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="dominantTautomer" Class="chemaxon.marvin.calculations.TautomerizationPlugin" JAR="MultiformPlugin.jar">
			<Alias>tautomer</Alias>
			<Param Name="type" Value="structure"></Param>
			<Param Name="dominants" Value="true"></Param>
			<ReturnType>Molecule</ReturnType>
			<Category>Isomers</Category>
			<Description>
				<![CDATA[returns the i-th dominant tautomeric form <!-- dominant tautomers are ordered by distribution) --> ]]>
			</Description>
			<ReturnValue>
				<![CDATA[the i-th dominant tautomer]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the dominant tautomer index (0-based)
			</li><li>the pH value as string (set if pH effect should be considered)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>dominantTautomer(0)</code> returns the first dominant tautomer of the input molecule <code>dominantTautomer(1, "2.0")</code> returns
		the second dominant tautomer of the input molecule, considering pH effect at pH <code>2.0</code> <!-- <br><br>Dominant tautomers are ordered by distribution. --> ]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>dominantTautomer(reactant(0), 1)</code> returns the second dominant tautomer of the first reactant <code>dominantTautomer(product(1), 0, "7.4")</code>
		returns the first dominant tautomer of the second product, considering pH effect at pH <code>7.4</code> <!-- <br><br>Dominant tautomers are ordered by distribution. --> ]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="dominantTautomers" Class="chemaxon.marvin.calculations.TautomerizationPlugin" JAR="MultiformPlugin.jar">
			<Alias>tautomers</Alias>
			<Param Name="type" Value="structures"></Param>
			<Param Name="dominants" Value="true"></Param>
			<ReturnType>Molecule[]</ReturnType>
			<Category>Isomers</Category>
			<Description>
				<![CDATA[constructs all dominant tautomeric forms (ordered by distribution)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the dominant tautomer array]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the pH value as string (set if pH effect should be considered)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>dominantTautomers()</code> returns all dominant tautomers of the input molecule in an array <code>dominantTautomers("2.0")</code>
		returns all dominant tautomers of the input molecule in an array, considering pH effect at pH <code>2.0</code> <!-- <br><br>Dominant tautomers are ordered by distribution. --> ]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>dominantTautomers(reactant(0))</code> returns all dominant tautomers of the first reactant in an array <code>dominantTautomers(product(1), "7.4")</code>
		returns all dominant tautomers of the second product in an array, considering pH effect at pH <code>7.4</code> <!-- <br><br>Dominant tautomers are ordered by distribution. --> ]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="dominantTautomerCount" Class="chemaxon.marvin.calculations.TautomerizationPlugin" JAR="MultiformPlugin.jar">
			<Param Name="type" Value="count"></Param>
			<Param Name="dominants" Value="true"></Param>
			<ReturnType>int</ReturnType>
			<Category>Isomers</Category>
			<Description>
				<![CDATA[calculates the number of dominant tautomers]]>
			</Description>
			<ReturnValue>
				<![CDATA[the number of dominant tautomers]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the pH value as string (set if pH effect should be considered)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>dominantTautomerCount()</code> returns the number of dominant tautomers of the input molecule <code>dominantTautomerCount("7.4")</code>
		returns the number of dominant tautomers of the input molecule, considering pH effect at pH <code>7.4</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>dominantTautomerCount(reactant(0))</code> returns the number of dominant tautomers of the first reactant <code>dominantTautomerCount(product(1), "2.5")</code>
		returns the number of dominant tautomers of the second product, considering pH effect at pH <code>2.5</code>]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="resonant" Class="chemaxon.marvin.calculations.ResonancePlugin" JAR="MultiformPlugin.jar">
			<ReturnType>Molecule</ReturnType>
			<Category>Isomers</Category>
			<Description>
				<![CDATA[constructs a resonant structure]]>
			</Description>
			<ReturnValue>
				<![CDATA[the resonant structure]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>resonant(0)</code> returns the first resonant structure of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>resonant(reactant(0), 1)</code> returns the second resonant structure of the first reactant]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="canonicalResonant" Class="chemaxon.marvin.calculations.ResonancePlugin" JAR="MultiformPlugin.jar">
			<Param Name="canonical" Value="true"></Param>
			<ReturnType>Molecule</ReturnType>
			<Category>Isomers</Category>
			<Description>
				<![CDATA[constructs the canonical resonant structure]]>
			</Description>
			<ReturnValue>
				<![CDATA[the canonical resonant structure]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>canonicalResonant()</code> returns the canonical resonant structure of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>canonicalResonant(reactant(0))</code> returns the canonical resonant structure of the first reactant]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="resonants" Class="chemaxon.marvin.calculations.ResonancePlugin" JAR="MultiformPlugin.jar">
			<Param Name="type" Value="structures"></Param>
			<ReturnType>Molecule[]</ReturnType>
			<Category>Isomers</Category>
			<Description>
				<![CDATA[constructs all resonant structures]]>
			</Description>
			<ReturnValue>
				<![CDATA[the resonant structure array]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>resonants()</code> returns all resonants of the input molecule in an array]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>resonants(reactant(0))</code> returns all resonants of the first reactant in an array]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="resonantCount" Class="chemaxon.marvin.calculations.ResonancePlugin" JAR="MultiformPlugin.jar">
			<Param Name="type" Value="count"></Param>
			<ReturnType>int</ReturnType>
			<Category>Isomers</Category>
			<Description>
				<![CDATA[calculates the number of resonant structures]]>
			</Description>
			<ReturnValue>
				<![CDATA[the number of resonant structures]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>resonantCount()</code> returns the number of resonant structures of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>resonantCount(reactant(0))</code> returns the number of resonant structures of the first reactant]]>
			</ReactionContextExample>
		</Plugin>
		<!--
		<Plugin ID="randomMarkushEnumeration" Class="chemaxon.marvin.calculations.MarkushEnumerationPlugin" JAR="MarkushEnumerationPlugin.jar">
			<Param Name="type" Value="structures"></Param>
			<Param Name="calctype" Value="random"></Param>
		</Plugin>
		<Plugin ID="randomEnumeration" Class="chemaxon.marvin.calculations.MarkushEnumerationPlugin" JAR="MarkushEnumerationPlugin.jar">
			<Param Name="type" Value="structures"></Param>
			<Param Name="calctype" Value="random"></Param>
		</Plugin>
		-->
		<Plugin ID="randomMarkushEnumerations" Class="chemaxon.marvin.calculations.MarkushEnumerationPlugin" JAR="MarkushEnumerationPlugin.jar">
			<Alias>randomMarkushEnumeration</Alias>
			<Alias>randomEnumerations</Alias>
			<Alias>randomEnumeration</Alias>
			<Param Name="type" Value="structures"></Param>
			<Param Name="calctype" Value="random"></Param>
			<ReturnType>Molecule[]</ReturnType>
			<Category>Markush</Category>
			<Description>
				<![CDATA[constructs Markush enumerated structures randomly]]>
			</Description>
			<ReturnValue>
				<![CDATA[the enumerated structures]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the number of structures to be returned (default: 1)
			</li><li>the (1-based) atom  indexes of the query atoms to be enumerated (default: all)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>randomMarkushEnumerations()</code>
		returns a randomly constructed enumerated structure of the input molecule <code>randomMarkushEnumerations(4, '2,3')</code> returns 4 randomly constructed enumerated structures of the input molecule, enumerating only atoms 2, 3 (1-based) ]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>randomMarkushEnumerations(reactant(0), 100)</code> returns 100 randomly constructed enumerated structures of the first reactant <code>randomMarkushEnumerations(product(1), '3,4,5')</code>
		returns a randomly constructed enumerated structure of the second product, enumerating atoms 3, 4, 5 (1-based)]]>
			</ReactionContextExample>
		</Plugin>
		<!--
		<Plugin ID="randomEnumerations" Class="chemaxon.marvin.calculations.MarkushEnumerationPlugin" JAR="MarkushEnumerationPlugin.jar">
			<Param Name="type" Value="structures"></Param>
			<Param Name="calctype" Value="random"></Param>
		</Plugin>
		-->
		<Plugin ID="randomMarkushEnumerationsDisplay" Class="chemaxon.marvin.calculations.MarkushEnumerationPlugin" JAR="MarkushEnumerationPlugin.jar">
			<Param Name="type" Value="structures"></Param>
			<Param Name="calctype" Value="random"></Param>
			<Param Name="code" Value="true"></Param>
			<Param Name="alignscaffold" Value="true"></Param>
			<Param Name="coloring" Value="all"></Param>
			<Category>Markush</Category>
			<Description>
				<![CDATA[constructs Markush enumerated structures randomly with scaffold alignment and scaffold/R-group coloring and enumeration ID]]>
			</Description>
			<ReturnValue>
				<![CDATA[the enumerated structures with alignment and coloring data and enumeration ID]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the number of structures to be returned (default: 1)
			</li><li>the (1-based) atom  indexes of the query atoms to be enumerated (default: all)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>randomMarkushEnumerationsDisplay()</code>
		returns a randomly constructed enumerated structure of the input molecule, with scaffold alignment and scaffold/R-group coloring data<code>randomMarkushEnumerationsDisplay(4, '2,3')</code> returns 4 randomly constructed enumerated structures of the input molecule, enumerating only atoms 2, 3 (1-based), with scaffold alignment and coloring data]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>randomMarkushEnumerationsDisplay(reactant(0), 100)</code> returns 100 randomly constructed enumerated structures of the first reactant, with scaffold alignment and coloring data <code>randomMarkushEnumerationsDisplay(product(1), '3,4,5')</code>
		returns a randomly constructed enumerated structure of the second product, enumerating atoms 3, 4, 5 (1-based), with scaffold alignment and coloring data]]>
			</ReactionContextExample>
		</Plugin>
		<!--
		<Plugin ID="markushEnumeration" Class="chemaxon.marvin.calculations.MarkushEnumerationPlugin" JAR="MarkushEnumerationPlugin.jar"></Plugin>
		<Plugin ID="enumerations" Class="chemaxon.marvin.calculations.MarkushEnumerationPlugin" JAR="MarkushEnumerationPlugin.jar"></Plugin>
		<Plugin ID="enumeration" Class="chemaxon.marvin.calculations.MarkushEnumerationPlugin" JAR="MarkushEnumerationPlugin.jar"></Plugin>
		-->
		<Plugin ID="markushEnumerations" Class="chemaxon.marvin.calculations.MarkushEnumerationPlugin" JAR="MarkushEnumerationPlugin.jar">
			<Alias>markushEnumeration</Alias>
			<Alias>enumerations</Alias>
			<Alias>enumeration</Alias>
			<ReturnType>Molecule[]</ReturnType>
			<Category>Markush</Category>
			<Description>
				<![CDATA[constructs Markush enumerated structures sequentially]]>
			</Description>
			<ReturnValue>
				<![CDATA[the enumerated structures]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the number of structures to be returned (default: all)
			</li><li>the (1-based) atom  indexes of the query atoms to be enumerated (default: all)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>markushEnumerations()</code> returns all Markush enumerated structures of the input molecule <code>markushEnumerations(1, '2,3')</code>
		returns one Markush enumerated structure of the input molecule, enumerating atoms 2, 3 (1-based)]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>markushEnumerations(reactant(0), 1)</code> returns one Markush enumerated structure of the first reactant <code>markushEnumerations(product(1), 2, '2,3')</code>
		returns two Markush enumerated structures of the second product, enumerating atoms 2, 3 (1-based)]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="markushEnumerationsDisplay" Class="chemaxon.marvin.calculations.MarkushEnumerationPlugin" JAR="MarkushEnumerationPlugin.jar">
			<Param Name="code" Value="true"></Param>
			<Param Name="alignscaffold" Value="true"></Param>
			<Param Name="coloring" Value="all"></Param>
			<ReturnType>Molecule[]</ReturnType>
			<Category>Markush</Category>
			<Description>
				<![CDATA[constructs Markush enumerated structures sequentially with scaffold alignment and scaffold/R-group coloring and enumeration ID]]>
			</Description>
			<ReturnValue>
				<![CDATA[the enumerated structures with alignment and coloring data and enumeration ID]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the number of structures to be returned (default: all)
			</li><li>the (1-based) atom  indexes of the query atoms to be enumerated (default: all)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>markushEnumerationsDisplay()</code> returns all Markush enumerated structures of the input molecule, with scaffold alignment and coloring data <code>markushEnumerationsDisplay(1, '2,3')</code>
		returns one Markush enumerated structure of the input molecule, enumerating atoms 2, 3 (1-based), with scaffold alignment and coloring data ]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>markushEnumerationsDisplay(reactant(0), 1)</code> returns one Markush enumerated structure of the first reactant, with scaffold alignment and coloring data with scaffold alignment and coloring data <code>markushEnumerationsDisplay(product(1), 2, '2,3')</code>
		returns two Markush enumerated structures of the second product, enumerating atoms 2, 3 (1-based), with scaffold alignment and coloring data ]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="markushEnumerationCount" Class="chemaxon.marvin.calculations.MarkushEnumerationPlugin" JAR="MarkushEnumerationPlugin.jar">
			<Alias>enumerationCount</Alias>
			<Param Name="type" Value="count"></Param>
			<ReturnType>Molecule[]</ReturnType>
			<Category>Markush</Category>
			<Description>
				<![CDATA[calculates the number of Markush enumerations]]>
			</Description>
			<ReturnValue>
				<![CDATA[the number of Markush enumerations]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the (1-based) atom  indexes of the query atoms to be enumerated (default: all)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>markushEnumerationCount()</code> returns the number of Markush enumerated structures of the input molecule <code>markushEnumerationCount('4,5')</code>
		returns the number of Markush enumerated structures of the input molecule, enumerating only atoms 4, 5 (1-based)]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>markushEnumerationCount(reactant(0))</code> returns the number of Markush enumerated structures of the first reactant <code>markushEnumerationCount(product(1), atoms(3,4))</code>
		returns the number of Markush enumerated structures of the second product, enumerating only atoms 4, 5 (1-based) (the <code>atoms()</code> function converts 0-based indexes to a '-'-separated 1-based atom index string)]]>
			</ReactionContextExample>
		</Plugin>
		<!--
		<Plugin ID="enumerationCount" Class="chemaxon.marvin.calculations.MarkushEnumerationPlugin" JAR="MarkushEnumerationPlugin.jar">
			<Param Name="type" Value="count"></Param>
		</Plugin>
		-->
		<Plugin ID="markushLibrarySize" Class="chemaxon.marvin.calculations.MarkushEnumerationPlugin" JAR="MarkushEnumerationPlugin.jar">
			<Param Name="calctype" Value="countonly"></Param>
			<Param Name="mode" Value="large"></Param>
			<ReturnType>int</ReturnType>
			<Category>Markush</Category>
			<Description>
				<![CDATA[calculates the Markush library size, no enumeration is done]]>
			</Description>
			<ReturnValue>
				<![CDATA[the Markush library size]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the (1-based) atom  indexes of the query atoms to be enumerated (default: all)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>markushLibrarySize()</code> returns the Markush library size for the input molecule <code>markushLibrarySize('4,5')</code>
		returns the Markush library size for the input molecule, presuming only atoms 4, 5 (1-based) are enumerated]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>markushLibrarySize(reactant(0))</code> returns the Markush library size for the first reactant <code>markushLibrarySize(product(1), atoms(3,4))</code>
		returns the Markush library size for the second product, presuming only atoms 4, 5 (1-based) are enumerated (the <code>atoms()</code> function converts 0-based indexes to a '-'-separated 1-based atom index string)]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="markushLibrarySizeAsString" Class="chemaxon.marvin.calculations.MarkushEnumerationPlugin" JAR="MarkushEnumerationPlugin.jar">
			<Param Name="calctype" Value="countonly"></Param>
			<Param Name="mode" Value="string"></Param>
			<ReturnType>String</ReturnType>
			<Category>Markush</Category>
			<Description>
				<![CDATA[calculates the Markush library size and returns it as string, no enumeration is done]]>
			</Description>
			<ReturnValue>
				<![CDATA[the Markush library size]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the (1-based) atom  indexes of the query atoms to be enumerated (default: all)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>markushLibrarySize()</code> returns the Markush library size as string for the input molecule <code>markushLibrarySize('4,5')</code>
		returns the Markush library size as string for the input molecule, presuming only atoms 4, 5 (1-based) are enumerated]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>markushLibrarySize(reactant(0))</code> returns the Markush library size as string for the first reactant <code>markushLibrarySize(product(1), atoms(3,4))</code>
		returns the Markush library size as string for the second product, presuming only atoms 4, 5 (1-based) are enumerated (the <code>atoms()</code> function converts 0-based indexes to a '-'-separated 1-based atom index string)]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="markushLibraryMagnitude" Class="chemaxon.marvin.calculations.MarkushEnumerationPlugin" JAR="MarkushEnumerationPlugin.jar">
			<Param Name="calctype" Value="countonly"></Param>
			<Param Name="mode" Value="magnitude"></Param>
			<ReturnType>int</ReturnType>
			<Category>Markush</Category>
			<Description>
				<![CDATA[calculates the Markush library magnitude, no enumeration is done]]>
			</Description>
			<ReturnValue>
				<![CDATA[the Markush library magnitude]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the (1-based) atom  indexes of the query atoms to be enumerated (default: all)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>markushLibraryMagnitude()</code> returns the Markush library magnitude for the input molecule <code>markushLibraryMagnitude('4,5')</code>
		returns the Markush library magnitude for the input molecule, presuming only atoms 4, 5 (1-based) are enumerated]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>markushLibraryMagnitude(reactant(0))</code> returns the Markush library magnitude for the first reactant <code>markushLibraryMagnitude(product(1), atoms(3,4))</code>
		returns the Markush library magnitude for the second product, presuming only atoms 4, 5 (1-based) are enumerated (the <code>atoms()</code> function converts 0-based indexes to a '-'-separated 1-based atom index string)]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="charge" Class="chemaxon.marvin.calculations.ChargePlugin" JAR="ChargePlugin.jar">
			<ReturnType>double</ReturnType>
			<Category>Charge</Category>
			<Description>
				<![CDATA[calculates partial charges on atoms<br />
		for result types "aromaticsystem" / "aromaticring", calculates the sum of partial charges of the atoms in the aromatic
		system / smallest aromatic ring containing the atom]]>
			</Description>
			<ReturnValue>
				<![CDATA[the charge values]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object,
			</li><li>the result type ("total" (default), "pi", "sigma", "aromaticsystem", "aromaticsystemsigma",
			"aromaticsystempi", "aromaticring", "aromaticringsigma", "aromaticringpi"),
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>charge(0)</code> returns the partial charge on atom <code>0</code> of the input molecule <code>charge(2, "pi", "7.4")</code>
		returns the partial "pi" charge on atom <code>2</code> of the major microspecies taken at pH <code>7.4</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>charge(ratom(1), "aromaticsystem")</code> returns the sum of partial pi charges in the aromatic system
		containing the reactant atom matching map <code>1</code> in the reaction equation <code>charge(patom(2), "aromaticring", "7.4")</code>
		returns the sum of partial charges in the smallest aromatic ring containing the product atom matching map <code>2</code>
		in the major microspecies taken at pH <code>7.4</code>]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="resonantCharge" Class="chemaxon.marvin.calculations.ChargePlugin" JAR="ChargePlugin.jar">
			<Param Name="resonance" Value="true"></Param>
			<ReturnType>double</ReturnType>
			<Category>Charge</Category>
			<Description>
				<![CDATA[calculates partial charges on atoms considering resonance effect<br />
		for result types "aromaticsystem" / "aromaticring", calculates the sum of partial charges of the atoms in the aromatic
		system / smallest aromatic ring containing the atom]]>
			</Description>
			<ReturnValue>
				<![CDATA[the charge values]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object,
			</li><li>the result type ("total" (default), "pi", "sigma", "aromaticsystem", "aromaticsystemsigma",
			"aromaticsystempi", "aromaticring", "aromaticringsigma", "aromaticringpi"),
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>resonantCharge(0)</code> returns the partial charge on atom <code>0</code> of the input molecule considering resonance effect<code>resonantCharge(2, "pi", "7.4")</code>
		returns the partial "pi" charge on atom <code>2</code> of the major microspecies taken at pH <code>7.4</code> considering resonance effect]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>resonantCharge(ratom(1), "aromaticsystem")</code> returns the sum of partial pi charges in the aromatic system
		containing the reactant atom matching map <code>1</code> in the reaction equation considering resonance effect<code>resonantCharge(patom(2), "aromaticring", "7.4")</code>
		returns the sum of partial charges in the smallest aromatic ring containing the product atom matching map <code>2</code>
		in the major microspecies taken at pH <code>7.4</code> considering resonance effect]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="ionCharge" Class="chemaxon.marvin.calculations.IonChargePlugin" JAR="IonChargePlugin.jar"></Plugin>
		<!--
		<Plugin ID="sOEN" Class="chemaxon.marvin.calculations.OrbitalElectronegativityPlugin" JAR="OrbitalElectronegativityPlugin.jar">
			<Param Name="type" Value="sigma"></Param>
		</Plugin>
		-->
		<Plugin ID="sigmaOrbitalElectronegativity" Class="chemaxon.marvin.calculations.OrbitalElectronegativityPlugin" JAR="OrbitalElectronegativityPlugin.jar">
			<Alias>sOEN</Alias>
			<Param Name="type" Value="sigma"></Param>
			<ReturnType>double</ReturnType>
			<Category>Charge</Category>
			<Description>
				<![CDATA[calculates atomic sigma orbital electronegativity]]>
			</Description>
			<ReturnValue>
				<![CDATA[the sigma orbital electronegativity values]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object,
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>sigmaOrbitalElectronegativity(0)</code> returns the sigma orbital electronegativity of atom <code>0</code>
		of the input molecule <code>sigmaOrbitalElectronegativity(2, "7.4")</code> returns the sigma orbital electronegativity
		of atom <code>2</code> of the major microspecies taken at pH <code>7.4</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>sOEN(ratom(1))</code> returns the sigma orbital electronegativity of reactant atom matching map <code>1</code>
		in the reaction equation <code>sOEN(patom(2), "7.4")</code> returns the sigma orbital electronegativity of the product
		atom matching map <code>2</code> of the major microspecies taken at pH <code>7.4</code>]]>
			</ReactionContextExample>
		</Plugin>
		<!--
		<Plugin ID="pOEN" Class="chemaxon.marvin.calculations.OrbitalElectronegativityPlugin" JAR="OrbitalElectronegativityPlugin.jar">
			<Param Name="type" Value="pi"></Param>
		</Plugin>
		-->
		<Plugin ID="piOrbitalElectronegativity" Class="chemaxon.marvin.calculations.OrbitalElectronegativityPlugin" JAR="OrbitalElectronegativityPlugin.jar">
			<Alias>pOEN</Alias>
			<Param Name="type" Value="pi"></Param>
			<ReturnType>double</ReturnType>
			<Category>Charge</Category>
			<Description>
				<![CDATA[calculates atomic pi orbital electronegativity]]>
			</Description>
			<ReturnValue>
				<![CDATA[the pi orbital electronegativity values]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object,
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>piOrbitalElectronegativity(0)</code> returns the pi orbital electronegativity of atom <code>0</code> of
		the input molecule <code>piOrbitalElectronegativity(2, "7.4")</code> returns the pi orbital electronegativity of atom <code>2</code>
		of the major microspecies taken at pH <code>7.4</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>pOEN(ratom(1))</code> returns the pi orbital electronegativity of reactant atom matching map <code>1</code>
		in the reaction equation <code>pOEN(patom(2), "7.4")</code> returns the pi orbital electronegativity of the product atom
		matching map <code>2</code> of the major microspecies taken at pH <code>7.4</code>]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="atomicPolarizability" Class="chemaxon.marvin.calculations.PolarizabilityPlugin" JAR="PolarizabilityPlugin.jar">
			<Alias>polarizability</Alias>
			<Alias>atomPol</Alias>
			<Alias>pol</Alias>
			<Param Name="type" Value="atomic"></Param>
			<ReturnType>double</ReturnType>
			<Category>Charge</Category>
			<Description>
				<![CDATA[calculates atomic polarizability]]>
			</Description>
			<ReturnValue>
				<![CDATA[the polarizability values]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object,
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>atomicPolarizability(0)</code> returns the polarizability of atom <code>0</code> of the input molecule <code>atomicPolarizability(2, "7.4")</code>
		returns the polarizability of atom <code>2</code> of the major microspecies taken at pH <code>7.4</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>atomicPolarizability(ratom(1))</code> returns the polarizability of reactant atom matching map <code>1</code> in the
		reaction equation <code>atomicPolarizability(patom(2), "7.4")</code> returns the polarizability of the product atom matching map <code>2</code>
		of the major microspecies taken at pH <code>7.4</code>]]>
			</ReactionContextExample>
		</Plugin>
		<!--
		<Plugin ID="polarizability" Class="chemaxon.marvin.calculations.PolarizabilityPlugin" JAR="PolarizabilityPlugin.jar">
			<Param Name="type" Value="atomic"></Param>
		</Plugin>
		<Plugin ID="pol" Class="chemaxon.marvin.calculations.PolarizabilityPlugin" JAR="PolarizabilityPlugin.jar">
			<Param Name="type" Value="atomic"></Param>
		</Plugin>
		<Plugin ID="atomPol" Class="chemaxon.marvin.calculations.PolarizabilityPlugin" JAR="PolarizabilityPlugin.jar">
			<Param Name="type" Value="atomic"></Param>
		</Plugin>
		-->
		<Plugin ID="molecularPolarizability" Class="chemaxon.marvin.calculations.PolarizabilityPlugin" JAR="PolarizabilityPlugin.jar">
			<Alias>molPol</Alias>
			<Param Name="type" Value="molecular"></Param>
			<ReturnType>double</ReturnType>
			<Category>Charge</Category>
			<Description>
				<![CDATA[calculates molecular polarizability]]>
			</Description>
			<ReturnValue>
				<![CDATA[the polarizability value]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>molecularPolarizability()</code> returns the molecular polarizability of the input molecule <code>molecularPolarizability("7.4")</code>
		returns the molecular polarizability of the major microspecies taken at pH <code>7.4</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>molecularPolarizability(reactant(1))</code> returns the molecular polarizability of the second reactant in the reaction
		equation <code>molecularPolarizability(product(0), "7.4")</code> returns the molecular polarizability of the first product major
		microspecies taken at pH <code>7.4</code>]]>
			</ReactionContextExample>
		</Plugin>
		<!--
		<Plugin ID="molPol" Class="chemaxon.marvin.calculations.PolarizabilityPlugin" JAR="PolarizabilityPlugin.jar">
			<Param Name="type" Value="molecular"></Param>
		</Plugin>
		-->
		<Plugin ID="averagePolarizability" Class="chemaxon.marvin.calculations.PolarizabilityPlugin" JAR="PolarizabilityPlugin.jar">
			<Alias>avgPol</Alias>
			<Alias>averagePol</Alias>
			<Param Name="geom3D" Value="true"></Param>
			<Param Name="type" Value="molecular"></Param>
			<ReturnType>double</ReturnType>
			<Category>Charge</Category>
			<Description>
				<![CDATA[calculates average molecular polarizability component considering 3D geometry]]>
			</Description>
			<ReturnValue>
				<![CDATA[the polarizability value]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>averagePolarizability()</code> returns the average polarizability component of the input molecule <code>averagePolarizability("7.4")</code>
		returns the average polarizability component of the major microspecies taken at pH <code>7.4</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>averagePolarizability(reactant(1))</code> returns the average polarizability component of the second reactant in the
		reaction equation <code>averagePolarizability(product(0), "7.4")</code> returns the average polarizability component of the first
		product major microspecies taken at pH <code>7.4</code>]]>
			</ReactionContextExample>
		</Plugin>
		<!--
		<Plugin ID="avgPol" Class="chemaxon.marvin.calculations.PolarizabilityPlugin" JAR="PolarizabilityPlugin.jar">
			<Param Name="geom3D" Value="true"></Param>
			<Param Name="type" Value="molecular"></Param>
		</Plugin>
		<Plugin ID="averagePol" Class="chemaxon.marvin.calculations.PolarizabilityPlugin" JAR="PolarizabilityPlugin.jar">
			<Param Name="geom3D" Value="true"></Param>
			<Param Name="type" Value="molecular"></Param>
		</Plugin>
		-->
		<Plugin ID="axxPol" Class="chemaxon.marvin.calculations.PolarizabilityPlugin" JAR="PolarizabilityPlugin.jar">
			<Param Name="geom3D" Value="true"></Param>
			<Param Name="type" Value="a(xx)"></Param>
			<ReturnType>double</ReturnType>
			<Category>Charge</Category>
			<Description>
				<![CDATA[calculates principal component of polarizability tensor (a(xx), a(yy), a(zz))]]>
			</Description>
			<ReturnValue>
				<![CDATA[the principal component of polarizability tensor]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>axxPol()</code> returns the principal component <code>a(xx)</code> of polarizability tensor of the input molecule
		<code>axxPol("7.4")</code> returns the principal component <code>a(xx)</code> of polarizability tensor of the major microspecies taken
		at pH <code>7.4</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>axxPol(reactant(1))</code> returns the principal component <code>a(xx)</code> of polarizability tensor of the second
		reactant in the reaction equation <code>axxPol(product(0), "7.4")</code> returns the principal component <code>a(xx)</code> of polarizability
		tensor of the first product major microspecies taken at pH <code>7.4</code>]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="ayyPol" Class="chemaxon.marvin.calculations.PolarizabilityPlugin" JAR="PolarizabilityPlugin.jar">
			<Param Name="geom3D" Value="true"></Param>
			<Param Name="type" Value="a(yy)"></Param>
			<ReturnType>double</ReturnType>
			<Category>Charge</Category>
			<Description>
				<![CDATA[calculates principal component of polarizability tensor (a(xx), a(yy), a(zz))]]>
			</Description>
			<ReturnValue>
				<![CDATA[the principal component of polarizability tensor]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>ayyPol()</code> returns the principal component <code>a(yy)</code> of polarizability tensor of the input molecule
		<code>ayyPol("7.4")</code> returns the principal component <code>a(yy)</code> of polarizability tensor of the major microspecies taken
		at pH <code>7.4</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>ayyPol(reactant(1))</code> returns the principal component <code>a(yy)</code> of polarizability tensor of the second
		reactant in the reaction equation <code>ayyPol(product(0), "7.4")</code> returns the principal component <code>a(yy)</code> of polarizability
		tensor of the first product major microspecies taken at pH <code>7.4</code>]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="azzPol" Class="chemaxon.marvin.calculations.PolarizabilityPlugin" JAR="PolarizabilityPlugin.jar">
			<Param Name="geom3D" Value="true"></Param>
			<Param Name="type" Value="a(zz)"></Param>
			<ReturnType>double</ReturnType>
			<Category>Charge</Category>
			<Description>
				<![CDATA[calculates principal component of polarizability tensor (a(xx), a(yy), a(zz))]]>
			</Description>
			<ReturnValue>
				<![CDATA[the principal component of polarizability tensor]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>azzPol()</code> returns the principal component <code>a(zz)</code> of polarizability tensor of the input molecule
		<code>azzPol("7.4")</code> returns the principal component <code>a(zz)</code> of polarizability tensor of the major microspecies taken
		at pH <code>7.4</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>azzPol(reactant(1))</code> returns the principal component <code>a(zz)</code> of polarizability tensor of the second
		reactant in the reaction equation <code>azzPol(product(0), "7.4")</code> returns the principal component <code>a(zz)</code> of polarizability
		tensor of the first product major microspecies taken at pH <code>7.4</code>]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="pKa" Class="chemaxon.marvin.calculations.pKaPlugin" JAR="pKaPlugin.jar">
			<Param Name="min" Value="-10"></Param>
			<Param Name="max" Value="20"></Param>
			<ReturnType>double/double[]</ReturnType>
			<Category>Protonation</Category>
			<Description>
				<![CDATA[calculates p<i>K</i><sub>a</sub> values]]>
			</Description>
			<ReturnValue>
				<![CDATA[the p<i>K</i><sub>a</sub> values]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object,
			</li><li>the strength index as string (e.g., '1' for the strongest, '2' for the second strongest p<i>K</i><sub>a</sub>),
			</li><li>the result type ("acidic", "basic", or "pka" for both (default))
		</li></ul>
		Note, that the strength index is specified between quotation marks. <br />
		In case of strength index the result type can be specified as "acidic" or "basic".<br /><br />
		In case of "pka"result type the returned p<i>K</i><sub>a</sub> values are
		acidic or basic (mixed!), depending on the acidic or basic character of the given atom. Acidic p<i>K</i><sub>a</sub>
		 is returned for an atom, if <i>acidicpKa() &le; 14.8-basicpKa()</i>, otherwise basic p<i>K</i><sub>a</sub> is returned.
		 Specifying "acidic" or "basic" result type is required to get the acidic or basic p<i>K</i><sub>a</sub> values only
		 (also <code>acidicpKa</code> or <code>basicpKa</code> functions can be used alternatively).]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>pKa(0)</code> returns the p<i>K</i><sub>a</sub> of atom <code>0</code> of the input molecule <code>pKa("acidic", "2")</code>
		returns the second strongest acidic p<i>K</i><sub>a</sub> value]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>pKa(ratom(1))</code> returns the p<i>K</i><sub>a</sub> of reactant atom matching map <code>1</code> in
		the reaction equation <code>pKa(product(0), "basic", "1")</code> returns the strongest basic p<i>K</i><sub>a</sub> value
		in the first product]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/protonation.html#pka</UsersGuideURL>
		</Plugin>
		<Plugin ID="pKaUseCorrection" Class="chemaxon.marvin.calculations.pKaPlugin" JAR="pKaPlugin.jar">
			<Param Name="min" Value="-10"></Param>
			<Param Name="max" Value="20"></Param>
			<Param Name="usecorrectionlibrary" Value="true"></Param>
			<ReturnType>double/double[]</ReturnType>
			<Category>Protonation</Category>
			<Description>
				<![CDATA[calculates p<i>K</i><sub>a</sub> values using the correction library]]>
			</Description>
			<ReturnValue>
				<![CDATA[the p<i>K</i><sub>a</sub> values]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object,
			</li><li>the strength index as string (e.g. '1' for the strongest, '2' for the second strongest p<i>K</i><sub>a</sub>),
			</li><li>the result type ("acidic", "basic", or "pka" for both (default))
		</li></ul>
		Note, that the strength index is specified between quotation marks. <br />
		In case of strength index the result type can be specified as "acidic" or "basic".<br /><br />
		In case of "pka"result type the returned p<i>K</i><sub>a</sub> values are
		acidic or basic (mixed!), depending on the acidic or basic character of the given atom. Acidic p<i>K</i><sub>a</sub>
		 is returned for an atom, if <i>acidicpKa() &le; 14.8-basicpKa()</i>, otherwise basic p<i>K</i><sub>a</sub> is returned.
		 Specifying "acidic" or "basic" result type is required to get the acidic or basic p<i>K</i><sub>a</sub> values only
		 (also <code>acidicpKaUseCorrection()</code> or <code>basicpKaUseCorrection()</code> functions can be used alternatively).]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>pKaUseCorrection(0)</code> returns the p<i>K</i><sub>a</sub> of atom <code>0</code> of the input molecule
		<code>pKaUseCorrection("acidic", "2")</code> 	returns the second strongest acidic p<i>K</i><sub>a</sub> value]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>pKaUseCorrection(ratom(1))</code> returns the p<i>K</i><sub>a</sub> of reactant atom matching map <code>1</code> in
		the reaction equation <code>pKaUseCorrection(product(0), "basic", "1")</code> returns the strongest basic p<i>K</i><sub>a</sub> value
		in the first product]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/protonation.html#pka</UsersGuideURL>
		</Plugin>
		<Plugin ID="acidicpKa" Class="chemaxon.marvin.calculations.pKaPlugin" JAR="pKaPlugin.jar">
			<Alias>apKa</Alias>
			<Param Name="type" Value="acidic"></Param>
			<Param Name="min" Value="-10"></Param>
			<Param Name="max" Value="20"></Param>
			<ReturnType>double/double[]</ReturnType>
			<Category>Protonation</Category>
			<Description>
				<![CDATA[calculates acidic p<i>K</i><sub>a</sub> values]]>
			</Description>
			<ReturnValue>
				<![CDATA[the acidic p<i>K</i><sub>a</sub> values]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object,
			</li><li>the strength index as string (e.g. '1' for the strongest, '2' for the second strongest p<i>K</i><sub>a</sub>),
		</li></ul>
		Note, that the strength index is specified between quotation marks.]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>acidicpKa(0)</code> returns the acidic p<i>K</i><sub>a</sub> of atom <code>0</code> of the input molecule <code>acidicpKa("2")</code>
		returns the second strongest acidic p<i>K</i><sub>a</sub> value]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>acidicpKa(ratom(1))</code> returns the acidic p<i>K</i><sub>a</sub> of reactant atom matching map <code>1</code>
		in the reaction equation <code>acidicpKa(product(0), "1")</code> returns the strongest basic p<i>K</i><sub>a</sub> value in
		the first product]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/protonation.html#pka</UsersGuideURL>
		</Plugin>
		<Plugin ID="basicpKa" Class="chemaxon.marvin.calculations.pKaPlugin" JAR="pKaPlugin.jar">
			<Alias>bpKa</Alias>
			<Param Name="type" Value="basic"></Param>
			<Param Name="min" Value="-10"></Param>
			<Param Name="max" Value="20"></Param>
			<ReturnType>double/double[]</ReturnType>
			<Category>Protonation</Category>
			<Description>
				<![CDATA[calculates basic p<i>K</i><sub>a</sub> values]]>
			</Description>
			<ReturnValue>
				<![CDATA[the basic p<i>K</i><sub>a</sub> values]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object,
			</li><li>the strength index as string (e.g. '1' for the strongest, '2' for the second strongest p<i>K</i><sub>a</sub>),
		</li></ul>
		Note, that the strength index is specified between quotation marks.]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>basicpKa(0)</code> returns the basic p<i>K</i><sub>a</sub> of atom <code>0</code> of the input molecule
		<code>basicpKa("2")</code> returns the second strongest basic p<i>K</i><sub>a</sub> value]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>basicpKa(ratom(1))</code> returns the basic p<i>K</i><sub>a</sub> of reactant atom matching map <code>1</code>
		in the reaction equation <code>basicpKa(product(0), "1")</code> returns the strongest basic p<i>K</i><sub>a</sub> value in
		the first product]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/protonation.html#pka</UsersGuideURL>
		</Plugin>
		<Plugin ID="acidicpKaUseCorrection" Class="chemaxon.marvin.calculations.pKaPlugin" JAR="pKaPlugin.jar">
			<Param Name="type" Value="acidic"></Param>
			<Param Name="min" Value="-10"></Param>
			<Param Name="max" Value="20"></Param>
			<Param Name="usecorrectionlibrary" Value="true"></Param>
			<ReturnType>double/double[]</ReturnType>
			<Category>Protonation</Category>
			<Description>
				<![CDATA[calculates acidic p<i>K</i><sub>a</sub> values using the correction library]]>
			</Description>
			<ReturnValue>
				<![CDATA[the acidic p<i>K</i><sub>a</sub> values]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object,
			</li><li>the strength index as string (e.g. '1' for the strongest, '2' for the second strongest p<i>K</i><sub>a</sub>),
		</li></ul>
		Note, that the strength index is specified between quotation marks.]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>acidicpKaUseCorrection(0)</code> returns the acidic p<i>K</i><sub>a</sub> of atom <code>0</code> of the input molecule
		<code>"acidicpKaUseCorrection"("2")</code>	returns the second strongest acidic p<i>K</i><sub>a</sub> value]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>"acidicpKaUseCorrection"(ratom(1))</code> returns the acidic p<i>K</i><sub>a</sub> of reactant atom matching map <code>1</code>
		in the reaction equation <code>"acidicpKaUseCorrection"(product(0), "1")</code> returns the strongest basic p<i>K</i><sub>a</sub> value in
		the first product]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/protonation.html#pka</UsersGuideURL>
		</Plugin>
		<Plugin ID="basicpKaUseCorrection" Class="chemaxon.marvin.calculations.pKaPlugin" JAR="pKaPlugin.jar">
			<Param Name="type" Value="basic"></Param>
			<Param Name="min" Value="-10"></Param>
			<Param Name="max" Value="20"></Param>
			<Param Name="usecorrectionlibrary" Value="true"></Param>
			<ReturnType>double/double[]</ReturnType>
			<Category>Protonation</Category>
			<Description>
				<![CDATA[calculates basic p<i>K</i><sub>a</sub> values using the correction library]]>
			</Description>
			<ReturnValue>
				<![CDATA[the basic p<i>K</i><sub>a</sub> values]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object,
			</li><li>the strength index as string (e.g. '1' for the strongest, '2' for the second strongest p<i>K</i><sub>a</sub>),
		</li></ul>
		Note, that the strength index is specified between quotation marks.]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>basicpKaUseCorrection(0)</code> returns the basic p<i>K</i><sub>a</sub> of atom <code>0</code> of the input molecule
		<code>basicpKaUseCorrection("2")</code> returns the second strongest basic p<i>K</i><sub>a</sub> value]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>basicpKaUseCorrection(ratom(1))</code> returns the basic p<i>K</i><sub>a</sub> of reactant atom matching map <code>1</code>
		in the reaction equation <code>basicpKaUseCorrection(product(0), "1")</code> returns the strongest basic p<i>K</i><sub>a</sub> value in
		the first product]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/protonation.html#pka</UsersGuideURL>
		</Plugin>
		<Plugin ID="acidicpKaLargeModel" Class="chemaxon.marvin.calculations.pKaPlugin" JAR="pKaPlugin.jar">
			<Param Name="type" Value="acidic"></Param>
			<Param Name="min" Value="-10"></Param>
			<Param Name="max" Value="20"></Param>
			<Param Name="model" Value="large"></Param>
			<ReturnType>double/double[]</ReturnType>
			<Category>Protonation</Category>
			<Description>
				<![CDATA[calculates acidic p<i>K</i><sub>a</sub> values using large model (this model is optimized for a large number of ionizable atoms)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the acidic p<i>K</i><sub>a</sub> values]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object,
			</li><li>the strength index as string (e.g. '1' for the strongest, '2' for the second strongest p<i>K</i><sub>a</sub>),
		</li></ul>
		Note, that the strength index is specified between quotation marks.]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>acidicpKaLargeModel(0)</code> returns the acidic p<i>K</i><sub>a</sub> of atom <code>0</code> of the input molecule
		<code>acidicpKaLargeModel("2")</code> returns the second strongest acidic p<i>K</i><sub>a</sub> value]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>acidicpKaLargeModel(ratom(1))</code> returns the acidic p<i>K</i><sub>a</sub> of reactant atom matching map <code>1</code>
		in the reaction equation <code>acidicpKaLargeModel(product(0), "1")</code> returns the strongest basic p<i>K</i><sub>a</sub> value in
		the first product]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/protonation.html#pka</UsersGuideURL>
		</Plugin>
		<Plugin ID="basicpKaLargeModel" Class="chemaxon.marvin.calculations.pKaPlugin" JAR="pKaPlugin.jar">
			<Param Name="type" Value="basic"></Param>
			<Param Name="min" Value="-10"></Param>
			<Param Name="max" Value="20"></Param>
			<Param Name="model" Value="large"></Param>
			<ReturnType>double/double[]</ReturnType>
			<Category>Protonation</Category>
			<Description>
				<![CDATA[calculates basic p<i>K</i><sub>a</sub> values using large model (this model is optimized for a large number of ionizable atoms)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the basic p<i>K</i><sub>a</sub> values]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object,
			</li><li>the strength index as string (e.g. '1' for the strongest, '2' for the second strongest p<i>K</i><sub>a</sub>),
		</li></ul>
		Note, that the strength index is specified between quotation marks.]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>basicpKaLargeModel(0)</code> returns the basic p<i>K</i><sub>a</sub> of atom <code>0</code> of the input molecule
		<code>basicpKaLargeModel("2")</code> returns the second strongest basic p<i>K</i><sub>a</sub> value]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>basicpKaLargeModel(ratom(1))</code> returns the basic p<i>K</i><sub>a</sub> of reactant atom matching map <code>1</code>
		in the reaction equation <code>basicpKaLargeModel(product(0), "1")</code> returns the strongest basic p<i>K</i><sub>a</sub> value in
		the first product]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/protonation.html#pka</UsersGuideURL>
		</Plugin>
		<Plugin ID="logD" Class="chemaxon.marvin.calculations.logDPlugin" JAR="logDPlugin.jar">
			<ReturnType>double</ReturnType>
			<Category>Partitioning</Category>
			<Description>
				<![CDATA[calculates log<i>D</i> at specified pH]]>
			</Description>
			<ReturnValue>
				<![CDATA[the log<i>D</i> value]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the pH value
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>logD('7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>logD(reactant(1), '7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the second reactant]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logd</UsersGuideURL>
		</Plugin>
		<Plugin ID="logDVG" Class="chemaxon.marvin.calculations.logDPlugin" JAR="logDPlugin.jar">
			<Param Name="method" Value="vg"></Param>
			<ReturnType>double</ReturnType>
			<Category>Partitioning</Category>
			<Description>
				<![CDATA[calculates log<i>D</i> at specified pH using method "VG"]]>
			</Description>
			<ReturnValue>
				<![CDATA[the log<i>D</i> value]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the pH value
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>logDVG('7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>logDVG(reactant(1), '7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the second reactant]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logd</UsersGuideURL>
		</Plugin>
		<Plugin ID="logDKLOP" Class="chemaxon.marvin.calculations.logDPlugin" JAR="logDPlugin.jar">
			<Param Name="method" Value="klop"></Param>
			<ReturnType>double</ReturnType>
			<Category>Partitioning</Category>
			<Description>
				<![CDATA[calculates log<i>D</i> at specified pH using method "KLOP"]]>
			</Description>
			<ReturnValue>
				<![CDATA[the log<i>D</i> value]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the pH value
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>logDKLOP('7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>logDKLOP(reactant(1), '7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the second reactant]]>
			</ReactionContextExample>			
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logd</UsersGuideURL>
		</Plugin>
		<Plugin ID="logDPHYS" Class="chemaxon.marvin.calculations.logDPlugin" JAR="logDPlugin.jar">
			<Param Name="method" Value="phys"></Param>
			<ReturnType>double</ReturnType>
			<Category>Partitioning</Category>
			<Description>
				<![CDATA[calculates log<i>D</i> at specified pH using method "PHYS"]]>
			</Description>
			<ReturnValue>
				<![CDATA[the log<i>D</i> value]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the pH value
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>logDPHYS('7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>logDPHYS(reactant(1), '7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the second reactant]]>
			</ReactionContextExample>			
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logd</UsersGuideURL>
		</Plugin>
		<Plugin ID="logDUser" Class="chemaxon.marvin.calculations.logDPlugin" JAR="logDPlugin.jar">
			<Param Name="method" Value="user"></Param>
			<ReturnType>double</ReturnType>
			<Category>Partitioning</Category>
			<Description>
				<![CDATA[calculates log<i>D</i> at specified pH using the user defined method]]>
			</Description>
			<ReturnValue>
				<![CDATA[the log<i>D</i> value]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the pH value
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>logDUser('7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>logDUser(reactant(1), '7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the second reactant]]>
			</ReactionContextExample>			
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logd</UsersGuideURL>
		</Plugin>
		<Plugin ID="logDWeighted" Class="chemaxon.marvin.calculations.logDPlugin" JAR="logDPlugin.jar">
			<Param Name="method" Value="weighted"></Param>
			<ReturnType>double</ReturnType>
			<Category>Partitioning</Category>
			<Description>
				<![CDATA[calculates log<i>D</i> at specified pH using weighted method]]>
			</Description>
			<ReturnValue>
				<![CDATA[the log<i>D</i> value]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the pH value
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>logDWeighted('7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>logDWeighted(reactant(1), '7.4')</code> returns the log<i>D</i> at pH <code>7.4</code> of the second reactant]]>
			</ReactionContextExample>			
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logd</UsersGuideURL>
		</Plugin>
		<Plugin ID="logP" Class="chemaxon.marvin.calculations.logPPlugin" JAR="logPPlugin.jar">
			<Param Name="type" Value="logPTrue"></Param>
			<ReturnType>double</ReturnType>
			<Category>Partitioning</Category>
			<Description>
				<![CDATA[calculates log<i>P</i>]]>
			</Description>
			<ReturnValue>
				<![CDATA[the log<i>P</i> value]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the result type:
			<ul>
<li>"logPMicro": the log<i>P</i> of the input molecule itself
				</li><li>"logPNonionic": the log<i>P</i> of the nonionic species
				</li><li>"logDpI": log<i>D</i> at p<i>I</i>
</li><li>"logPTrue": the most typical from the above (default)
			</li></ul>
</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>logP()</code> returns the most typical log<i>P</i> out of the input molecule log<i>P</i>, the log<i>P</i>
		of the nonionic species and log<i>D</i> at p<i>I</i> <code>logP('logPMicro')</code> returns the log<i>P</i> of the input
		molecule itself]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>logP(reactant(1), 'logDpI')</code> returns the log<i>D</i> at p<i>I</i> of the second reactant
		<code>logP(product(1), 'logPNonionic')</code>	returns log<i>P</i> of the nonionic species of the second product]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logp</UsersGuideURL>
		</Plugin>
		<Plugin ID="logPVG" Class="chemaxon.marvin.calculations.logPPlugin" JAR="logPPlugin.jar">
			<Param Name="type" Value="logPTrue"></Param>
			<Param Name="method" Value="vg"></Param>
			<Param Name="type" Value="logPTrue"></Param>
			<ReturnType>double</ReturnType>
			<Category>Partitioning</Category>
			<Description>
				<![CDATA[calculates log<i>P</i> using method "VG"]]>
			</Description>
			<ReturnValue>
				<![CDATA[the log<i>P</i> value]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the result type:
			<ul>
<li>"logPMicro": the log<i>P</i> of the input molecule itself
				</li><li>"logPNonionic": the log<i>P</i> of the nonionic species
				</li><li>"logDpI": log<i>D</i> at p<i>I</i>
</li><li>"logPTrue": the most typical from the above (default)
			</li></ul>
</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>logPVG()</code> returns the most typical log<i>P</i> out of the input molecule log<i>P</i>, the log<i>P</i>
		of the nonionic species and log<i>D</i> at p<i>I</i> <code>logPVG('logPMicro')</code> returns the log<i>P</i> of the input
		molecule itself]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>logPVG(reactant(1), 'logDpI')</code> returns the log<i>D</i> at p<i>I</i> of the second reactant
		<code>logPVG(product(1), 'logPNonionic')</code> returns log<i>P</i> of the nonionic species of the second product]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logp</UsersGuideURL>
		</Plugin>
		<Plugin ID="logPKLOP" Class="chemaxon.marvin.calculations.logPPlugin" JAR="logPPlugin.jar">
			<Param Name="type" Value="logPTrue"></Param>
			<Param Name="method" Value="klop"></Param>
			<ReturnType>double</ReturnType>
			<Category>Partitioning</Category>
			<Description>
				<![CDATA[calculates log<i>P</i> using method "KLOP"]]>
			</Description>
			<ReturnValue>
				<![CDATA[the log<i>P</i> value]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the result type:
			<ul>
<li>"logPMicro": the log<i>P</i> of the input molecule itself
				</li><li>"logPNonionic": the log<i>P</i> of the nonionic species
				</li><li>"logDpI": log<i>D</i> at p<i>I</i>
</li><li>"logPTrue": the most typical from the above (default)
			</li></ul>
</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>logPKLOP()</code> returns the most typical log<i>P</i> out of the input molecule log<i>P</i>, the log<i>P</i>
		of the nonionic species and log<i>D</i> at p<i>I</i> <code>logPKLOP('logPMicro')</code> returns the log<i>P</i> of the input
		molecule itself]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>logPKLOP(reactant(1), 'logDpI')</code> returns the log<i>D</i> at p<i>I</i> of the second reactant
		<code>logPKLOP(product(1), 'logPNonionic')</code> returns log<i>P</i> of the nonionic species of the second product]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logp</UsersGuideURL>
		</Plugin>
		<Plugin ID="logPPHYS" Class="chemaxon.marvin.calculations.logPPlugin" JAR="logPPlugin.jar">
			<Param Name="type" Value="logPTrue"></Param>
			<Param Name="method" Value="phys"></Param>
			<ReturnType>double</ReturnType>
			<Category>Partitioning</Category>
			<Description>
				<![CDATA[calculates log<i>P</i> using method "PHYS"]]>
			</Description>
			<ReturnValue>
				<![CDATA[the log<i>P</i> value]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the result type:
			<ul>
<li>"logPMicro": the log<i>P</i> of the input molecule itself
				</li><li>"logPNonionic": the log<i>P</i> of the nonionic species
				</li><li>"logDpI": log<i>D</i> at p<i>I</i>
</li><li>"logPTrue": the most typical from the above (default)
			</li></ul>
</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>logPPHYS()</code> returns the most typical log<i>P</i> out of the input molecule log<i>P</i>, the log<i>P</i>
		of the nonionic species and log<i>D</i> at p<i>I</i> <code>logPPHYS('logPMicro')</code> returns the log<i>P</i> of the input
		molecule itself]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>logPPHYS(reactant(1), 'logDpI')</code> returns the log<i>D</i> at p<i>I</i> of the second reactant
		<code>logPPHYS(product(1), 'logPNonionic')</code> returns log<i>P</i> of the nonionic species of the second product]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logp</UsersGuideURL>
		</Plugin>
		<Plugin ID="logPUser" Class="chemaxon.marvin.calculations.logPPlugin" JAR="logPPlugin.jar">
			<Param Name="type" Value="logPTrue"></Param>
			<Param Name="method" Value="user"></Param>
			<ReturnType>double</ReturnType>
			<Category>Partitioning</Category>
			<Description>
				<![CDATA[calculates log<i>P</i> using the user defined method]]>
			</Description>
			<ReturnValue>
				<![CDATA[the log<i>P</i> value]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the result type:
			<ul>
<li>"logPMicro": the log<i>P</i> of the input molecule itself
				</li><li>"logPNonionic": the log<i>P</i> of the nonionic species
				</li><li>"logDpI": log<i>D</i> at p<i>I</i>
</li><li>"logPTrue": the most typical from the above (default)
			</li></ul>
</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>logPUser()</code> returns the most typical log<i>P</i> out of the input molecule log<i>P</i>, the log<i>P</i>
		of the nonionic species and log<i>D</i> at p<i>I</i> <code>logPUser('logPMicro')</code> returns the log<i>P</i> of the input
		molecule itself]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>logPUser(reactant(1), 'logDpI')</code> returns the log<i>D</i> at p<i>I</i> of the second reactant
		<code>logPUser(product(1), 'logPNonionic')</code> returns log<i>P</i> of the nonionic species of the second product]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logp</UsersGuideURL>
		</Plugin>
		<Plugin ID="logPWeighted" Class="chemaxon.marvin.calculations.logPPlugin" JAR="logPPlugin.jar">
			<Param Name="type" Value="logPTrue"></Param>
			<Param Name="method" Value="weighted"></Param>
			<ReturnType>double</ReturnType>
			<Category>Partitioning</Category>
			<Description>
				<![CDATA[calculates log<i>P</i> using weighted method]]>
			</Description>
			<ReturnValue>
				<![CDATA[the log<i>P</i> value]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the result type:
			<ul>
<li>"logPMicro": the log<i>P</i> of the input molecule itself
				</li><li>"logPNonionic": the log<i>P</i> of the nonionic species
				</li><li>"logDpI": log<i>D</i> at p<i>I</i>
</li><li>"logPTrue": the most typical from the above (default)
			</li></ul>
</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>logPWeighted()</code> returns the most typical log<i>P</i> out of the input molecule log<i>P</i>, the log<i>P</i>
		of the nonionic species and log<i>D</i> at p<i>I</i> <code>logPWeighted('logPMicro')</code> returns the log<i>P</i> of the input
		molecule itself]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>logPWeighted(reactant(1), 'logDpI')</code> returns the log<i>D</i> at p<i>I</i> of the second reactant
		<code>logPWeighted(product(1), 'logPNonionic')</code> returns log<i>P</i> of the nonionic species of the second product]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logp</UsersGuideURL>
		</Plugin>
		<Plugin ID="logPincrement" Class="chemaxon.marvin.calculations.logPPlugin" JAR="logPPlugin.jar">
			<Alias>logPi</Alias>
			<Param Name="type" Value="increments"></Param>
			<ReturnType>double[]</ReturnType>
			<Category>Partitioning</Category>
			<Description>
				<![CDATA[calculates the atomic log<i>P</i> increment]]>
			</Description>
			<ReturnValue>
				<![CDATA[the atomic log<i>P</i> increment]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>logPincrement(2)</code> returns the log<i>P</i> increment on atom <code>2</code> of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>logPincrement(ratom(1))</code> returns the log<i>P</i> increment on the reactant atom matching map <code>1</code>
		in the reaction equation]]>
			</ReactionContextExample>
			<UsersGuideURL>http://www.chemaxon.com/marvin/help/calculations/partitioning.html#logp</UsersGuideURL>
		</Plugin>
		<!--
		<Plugin ID="logPi" Class="chemaxon.marvin.calculations.logPPlugin" JAR="logPPlugin.jar">
			<Param Name="type" Value="increments"></Param>
		</Plugin>
		-->
		<!-- // moved to external configuration file
			<Plugin ID="solubility" Class="chemaxon.marvin.calculations.SolubilityPlugin" JAR="SolubilityPlugin.jar">
			<Param Name="type" Value="logSatFixpH" />
			</Plugin>
			<Plugin ID="logS" Class="chemaxon.marvin.calculations.SolubilityPlugin" JAR="SolubilityPlugin.jar">
			<Param Name="type" Value="logSatFixpH" />
			</Plugin>
			<Plugin ID="solubilityAtIsoelectricPoint" Class="chemaxon.marvin.calculations.SolubilityPlugin" JAR="SolubilityPlugin.jar">
			<Param Name="type" Value="logSTrue" />
			</Plugin>
			<Plugin ID="logSTrue" Class="chemaxon.marvin.calculations.SolubilityPlugin" JAR="SolubilityPlugin.jar">
			<Param Name="type" Value="logSTrue" />
			</Plugin>
			<Plugin ID="solubilityOfMicroSpecies" Class="chemaxon.marvin.calculations.SolubilityPlugin" JAR="SolubilityPlugin.jar">
			<Param Name="type" Value="logSMicro" />
			</Plugin>
			<Plugin ID="logSMicro" Class="chemaxon.marvin.calculations.SolubilityPlugin" JAR="SolubilityPlugin.jar">
			<Param Name="type" Value="logSMicro" />
			</Plugin>
			<Plugin ID="solubilityOfNeutral" Class="chemaxon.marvin.calculations.SolubilityPlugin" JAR="SolubilityPlugin.jar">
			<Param Name="type" Value="logSNeutral" />
			</Plugin>
			<Plugin ID="logSNeutral" Class="chemaxon.marvin.calculations.SolubilityPlugin" JAR="SolubilityPlugin.jar">
			<Param Name="type" Value="logSNeutral" />
			</Plugin>
		-->
		
		<Plugin ID="predict" Class="chemaxon.marvin.calculations.PredictorPlugin" JAR="PredictorPlugin.jar">
			<Alias>predictor</Alias>
			<ReturnType>double</ReturnType>
			<Category>Prediction</Category>
			<Description>
				<![CDATA[predicts molecular properties]]>
			</Description>
			<ReturnValue>
				<![CDATA[the predicted molecular property value]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the training ID</li>
</ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>predict('pampa')</code> returns the predicted property of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>predict(reactant(1), 'pampa')</code> returns the predicted property of the second reactant]]>
			</ReactionContextExample>
		</Plugin>
		
		<Plugin ID="electrophilicityOrder" Class="chemaxon.marvin.calculations.HuckelAnalysisPlugin" JAR="HuckelAnalysisPlugin.jar">
			<Alias>orderE</Alias>
			<Alias>aromaticElectrophilicityOrder</Alias>
			<Param Name="type" Value="order:e"></Param>
			<ReturnType>int[]</ReturnType>
			<Category>Huckel</Category>
			<Description>
				<![CDATA[calculates E(+) order of atoms]]>
			</Description>
			<ReturnValue>
				<![CDATA[the  E(+) order index of the atom<br />
		(<code>0</code>, <code>1</code>, <code>2</code>, ...),<br />]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>electrophilicityOrder(2)</code> returns the E(+) order index of atom <code>2</code> of the input molecule, e.g. returns
		<code>0</code> if atom <code>2</code> is the most electrophilic atom, <code>1</code> if atom <code>2</code> is the
		second strongest electrophilic atom, etc.,]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>electrophilicityOrder(ratom(1))</code> returns the E(+) order index of the reactant atom matching map <code>1</code> in
		the reaction equation]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="hmoElectrophilicityOrder" Class="chemaxon.marvin.calculations.HuckelAnalysisPlugin" JAR="HuckelAnalysisPlugin.jar">
			<Alias>hmoOrderE</Alias>
			<Param Name="type" Value="hmoorder:e"></Param>
			<ReturnType>int[]</ReturnType>
			<Category>Huckel</Category>
			<Description>
				<![CDATA[calculates HMO E(+) order of atoms]]>
			</Description>
			<ReturnValue>
				<![CDATA[the  E(+) order index of the atom<br />
		(<code>0</code>, <code>1</code>, <code>2</code>, ...),<br />]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>hmoElectrophilicityOrder(2)</code> returns the E(+) order index of atom <code>2</code> of the input molecule, e.g. returns
		<code>0</code> if atom <code>2</code> is the most electrophilic atom, <code>1</code> if atom <code>2</code> is the
		second strongest electrophilic atom, etc.,]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>hmoElectrophilicityOrder(ratom(1))</code> returns the E(+) order index of the reactant atom matching map <code>1</code> in
		the reaction equation]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="nucleophilicityOrder" Class="chemaxon.marvin.calculations.HuckelAnalysisPlugin" JAR="HuckelAnalysisPlugin.jar">
			<Alias>orderNu</Alias>
			<Alias>aromaticNucleophilicityOrder</Alias>
			<Param Name="type" Value="order:n"></Param>
			<ReturnType>int[]</ReturnType>
			<Category>Huckel</Category>
			<Description>
				<![CDATA[calculates Nu(-) order of atoms]]>
			</Description>
			<ReturnValue>
				<![CDATA[the Nu(-) order index of the atom<br />
		(<code>0</code>, <code>1</code>, <code>2</code>, ...),<br />]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>nucleophilicityOrder(2)</code> returns the Nu(-) order index of atom <code>2</code> of the input molecule, e.g.
		returns <code>0</code> if atom <code>2</code> is the most nucleophilic atom, <code>1</code> if atom <code>2</code> is
		the second strongest nucleophilic atom, etc., ]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>nucleophilicityOrder(ratom(1))</code> returns the Nu(-) order index of the reactant atom matching map <code>1</code>
		in the reaction equation]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="hmoNucleophilicityOrder" Class="chemaxon.marvin.calculations.HuckelAnalysisPlugin" JAR="HuckelAnalysisPlugin.jar">
			<Alias>hmoOrderNu</Alias>
			<Param Name="type" Value="hmoorder:n"></Param>
			<ReturnType>int[]</ReturnType>
			<Category>Huckel</Category>
			<Description>
				<![CDATA[calculates HMO Nu(-) order of atoms]]>
			</Description>
			<ReturnValue>
				<![CDATA[the Nu(-) order index of the atom<br />
		(<code>0</code>, <code>1</code>, <code>2</code>, ...),<br />]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>hmoNucleophilicityOrder(2)</code> returns the Nu(-) order index of atom <code>2</code> of the input molecule, e.g.
		returns <code>0</code> if atom <code>2</code> is the most nucleophilic atom, <code>1</code> if atom <code>2</code> is
		the second strongest nucleophilic atom, etc., ]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>hmoNucleophilicityOrder(ratom(1))</code> returns the Nu(-) order index of the reactant atom matching map <code>1</code>
		in the reaction equation]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="nucleophilicity" Class="chemaxon.marvin.calculations.HuckelAnalysisPlugin" JAR="HuckelAnalysisPlugin.jar">
			<Alias>electrophilicLocalizationEnergy</Alias>
			<Alias>energyE</Alias>
			<Param Name="type" Value="localizationenergy:e"></Param>
			<ReturnType>double[]</ReturnType>
			<Category>Huckel</Category>
			<Description>
				<![CDATA[calculates nucleophilicity of atoms]]>
			</Description>
			<ReturnValue>
				<![CDATA[the nucleophilicity of the atom,<br />
<code>NaN</code> for non-aromatic atoms]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>nucleophilicity(2)</code> returns the nucleophilicity of atom <code>2</code> of the input
		molecule, <code>NaN</code> if atom <code>2</code> is non-aromatic]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>nucleophilicity(ratom(1))</code> returns the nucleophilicity of the reactant atom matching
		map <code>1</code> in the reaction equation]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="hmoElectrophilicLocalizationEnergy" Class="chemaxon.marvin.calculations.HuckelAnalysisPlugin" JAR="HuckelAnalysisPlugin.jar">
			<Param Name="type" Value="hmolocalizationenergy:e"></Param>
			<ReturnType>double[]</ReturnType>
			<Category>Huckel</Category>
			<Description>
				<![CDATA[calculates HMO localization energy L(+) of atoms]]>
			</Description>
			<ReturnValue>
				<![CDATA[the localization energy L(+) of the atom,<br />
<code>NaN</code> for non-aromatic atoms]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>hmoElectrophilicLocalizationEnergy(2)</code> returns the electrophilic L(+) localization energy of atom <code>2</code> of the input
		molecule, <code>NaN</code> if atom <code>2</code> is non-aromatic]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>hmoElectrophilicLocalizationEnergy(ratom(1))</code> returns the electrophilic L(+) localization energy of the reactant atom matching
		map <code>1</code> in the reaction equation]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="electrophilicity" Class="chemaxon.marvin.calculations.HuckelAnalysisPlugin" JAR="HuckelAnalysisPlugin.jar">
			<Alias>nucleophilicLocalizationEnergy</Alias>
			<Alias>energyNu</Alias>
			<Param Name="type" Value="localizationenergy:n"></Param>
			<ReturnType>double[]</ReturnType>
			<Category>Huckel</Category>
			<Description>
				<![CDATA[calculates electrophilicity of atoms]]>
			</Description>
			<ReturnValue>
				<![CDATA[the electrophilicity of the atom,<br />
		<code>NaN</code> for non-aromatic atoms]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>electrophilicity(2)</code> returns the electrophilicity of atom <code>2</code> of the input
		molecule, <code>NaN</code> if atom <code>2</code> is non-aromatic]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>electrophilicity(ratom(1))</code> returns the electrophilicity of the reactant atom matching
		map <code>1</code> in the reaction equation]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="hmoNucleophilicLocalizationEnergy" Class="chemaxon.marvin.calculations.HuckelAnalysisPlugin" JAR="HuckelAnalysisPlugin.jar">
			<Param Name="type" Value="hmolocalizationenergy:n"></Param>
			<ReturnType>double[]</ReturnType>
			<Category>Huckel</Category>
			<Description>
				<![CDATA[calculates HMO localization energy L(-) of atoms]]>
			</Description>
			<ReturnValue>
				<![CDATA[the localization energy L(-) of the atom,<br />
		<code>NaN</code> for non-aromatic atoms]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object
			</li><li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>hmoNucleophilicLocalizationEnergy(2)</code> returns the nucleophilic L(-) localization energy of atom <code>2</code> of the input
		molecule, <code>NaN</code> if atom <code>2</code> is non-aromatic]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>hmoNucleophilicLocalizationEnergy(ratom(1))</code> returns the nucleophilic L(-) localization energy of the reactant atom matching
		map <code>1</code> in the reaction equation]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="piEnergy" Class="chemaxon.marvin.calculations.HuckelAnalysisPlugin" JAR="HuckelAnalysisPlugin.jar">
			<Param Name="type" Value="pienergy"></Param>
			<ReturnType>double</ReturnType>
			<Category>Huckel</Category>
			<Description>
				<![CDATA[calculates the pi energy of the molecule. Deprecated.]]>
			</Description>
			<ReturnValue>
				<![CDATA[the pi energy of the molecule]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>piEnergy()</code> returns the pi energy of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>piEnergy(product(1))</code> returns the pi energy of the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="hmoPiEnergy" Class="chemaxon.marvin.calculations.HuckelAnalysisPlugin" JAR="HuckelAnalysisPlugin.jar">
			<Param Name="type" Value="hmopienergy"></Param>
			<ReturnType>double</ReturnType>
			<Category>Huckel</Category>
			<Description>
				<![CDATA[calculates the HMO pi energy of the molecule]]>
			</Description>
			<ReturnValue>
				<![CDATA[the pi energy of the molecule]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>hmoPiEnergy()</code> returns the pi energy of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>hmoPiEnergy(product(1))</code> returns the pi energy of the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="electronDensity" Class="chemaxon.marvin.calculations.HuckelAnalysisPlugin" JAR="HuckelAnalysisPlugin.jar">
			<Alias>piChargeDensity</Alias>
			<Param Name="type" Value="electrondensity"></Param>
			<ReturnType>double[]</ReturnType>
			<Category>Huckel</Category>
			<Description>
				<![CDATA[calculates the electron density of atoms]]>
			</Description>
			<ReturnValue>
				<![CDATA[the electron density of the atom, <code>NaN</code> for non-existing values]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>he atom index / MolAtom object</li>
<li>the major microspecies pH (takes the input molecule itself if omitted)</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>electronDensity(2)</code> returns the electron density of atom 2 of the input molecule, <code>NaN</code>
		for non-existing value]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>electronDensity(ratom(1))</code> returns the electron density of the reactant atom matching map <code>1</code>
		in the reaction equation]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="hmoElectronDensity" Class="chemaxon.marvin.calculations.HuckelAnalysisPlugin" JAR="HuckelAnalysisPlugin.jar">
			<Param Name="type" Value="hmoelectrondensity"></Param>
			<ReturnType>double[]</ReturnType>
			<Category>Huckel</Category>
			<Description>
				<![CDATA[calculates the HMO electron density of atoms]]>
			</Description>
			<ReturnValue>
				<![CDATA[the electron density of the atom, <code>NaN</code> for non-existing values]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>he atom index / MolAtom object</li>
<li>the major microspecies pH (takes the input molecule itself if omitted)</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>hmoElectronDensity(2)</code> returns the electron density of atom 2 of the input molecule, <code>NaN</code>
		for non-existing value]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>hmoElectronDensity(ratom(1))</code> returns the electron density of the reactant atom matching map <code>1</code>
		in the reaction equation]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="chargeDensity" Class="chemaxon.marvin.calculations.HuckelAnalysisPlugin" JAR="HuckelAnalysisPlugin.jar">
			<Alias>totalChargeDensity</Alias>
			<Param Name="type" Value="chargedensity"></Param>
			<ReturnType>double[]</ReturnType>
			<Category>Huckel</Category>
			<Description>
				<![CDATA[calculates the charge density of atoms]]>
			</Description>
			<ReturnValue>
				<![CDATA[the charge density of the atom, <code>NaN</code> for non-existing values]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>he atom index / MolAtom object</li>
<li>the major microspecies pH (takes the input molecule itself if omitted)</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>chargeDensity(2)</code> returns the charge density of atom 2 of the input molecule, <code>NaN</code>
		for non-existing value]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>chargeDensity(ratom(1))</code> returns the charge density of the reactant atom matching map <code>1</code>
		in the reaction equation]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="hmoChargeDensity" Class="chemaxon.marvin.calculations.HuckelAnalysisPlugin" JAR="HuckelAnalysisPlugin.jar">
			<Param Name="type" Value="chargedensity"></Param>
			<ReturnType>double[]</ReturnType>
			<Category>Huckel</Category>
			<Description>
				<![CDATA[calculates the HMO charge density of atoms]]>
			</Description>
			<ReturnValue>
				<![CDATA[the charge density of the atom, <code>NaN</code> for non-existing values]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>he atom index / MolAtom object</li>
<li>the major microspecies pH (takes the input molecule itself if omitted)</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>hmoChargeDensity(2)</code> returns the charge density of atom 2 of the input molecule, <code>NaN</code>
		for non-existing value]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>hmoChargeDensity(ratom(1))</code> returns the charge density of the reactant atom matching map <code>1</code>
		in the reaction equation]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="topologicalPolarSurfaceArea" Class="chemaxon.marvin.calculations.TPSAPlugin" JAR="TPSAPlugin.jar">
			<Alias>PSA</Alias>
			<ReturnType>double</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the topological polar surface area (2D)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the polar surface area (2D)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>topologicalPolarSurfaceArea()</code> returns the polar surface area of the input molecule
		<code>topologicalPolarSurfaceArea('7.4')</code> returns the polar	surface area of the major microspecies taken at pH <code>7.4</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>topologicalPolarSurfaceArea(reactant(0))</code> returns the polar surface area of the first reactant
		<code>topologicalPolarSurfaceArea(product(0), '7.4')</code>
		returns the polar surface area of the major microspecies of the first product taken at pH <code>7.4</code>]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="vanDerWaalsSurfaceArea" Class="chemaxon.marvin.calculations.MSAPlugin" JAR="MSAPlugin.jar">
			<Param Name="type" Value="vanderwaals"></Param>
			<ReturnType>double</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the van der Waals surface area]]>
			</Description>
			<ReturnValue>
				<![CDATA[the molecular surface area]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>vanDerWaalsSurfaceArea()</code> returns the van der Waals surface area of the input molecule
		<code>vanDerWaalsSurfaceArea('7.4')</code> returns the van der Waals accessible surface area of the major microspecies taken at
		pH <code>7.4</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>vanDerWaalsSurfaceArea(reactant(0))</code> returns the van der Waals surface area of the first reactant
		<code>vanDerWaalsSurfaceArea(product(0), '7.4')</code> returns the van der Waals surface area of the major microspecies of the
		first product taken at pH <code>7.4</code>]]>
			</ReactionContextExample>
		</Plugin>
		<!--
		<Plugin ID="solventAccessibleSurfaceArea" Class="chemaxon.marvin.calculations.MSAPlugin" JAR="MSAPlugin.jar">
			<Param Name="type" Value="solvent"></Param>
		</Plugin>
		<Plugin ID="waterAccessibleSurfaceArea" Class="chemaxon.marvin.calculations.MSAPlugin" JAR="MSAPlugin.jar">
			<Param Name="type" Value="asa"></Param>
		</Plugin>
		-->
		<Plugin ID="waterAccessibleSurfaceArea" Class="chemaxon.marvin.calculations.MSAPlugin" JAR="MSAPlugin.jar">
			<Alias>ASA</Alias>
			<Alias>solventAccessibleSurfaceArea</Alias>
			<Param Name="type" Value="asa"></Param>
			<ReturnType>double</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the solvent accessible / water accessible molecular surface area]]>
			</Description>
			<ReturnValue>
				<![CDATA[the molecular surface area]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>waterAccessibleSurfaceArea()</code> returns the solvent accessible / water accessible surface area of the input molecule
		<code>solventAccessibleSurfaceArea('7.4')</code>returns the solvent accessible / water accessible surface area of the major microspecies taken at
		pH <code>7.4</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>waterAccessibleSurfaceArea(reactant(0))</code> returns the solvent accessible / water accessible surface area of the first
		reactant<code>solventAccessibleSurfaceArea(product(0), '7.4')</code> returns the solvent accessible / water accessible surface area of the major
		microspecies of the first product taken at pH <code>7.4</code>]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="ASAPlus" Class="chemaxon.marvin.calculations.MSAPlugin" JAR="MSAPlugin.jar">
			<Param Name="type" Value="asa+"></Param>
			<ReturnType>double</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the water accessible molecular surface area of all atoms with positive partial charge]]>
			</Description>
			<ReturnValue>
				<![CDATA[the molecular surface area]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>ASAPlus()</code> returns the water accessible surface area of all atoms of the input molecule with positive partial charge
		<code>ASAPlus('7.4')</code>returns the water accessible surface area of all atoms with positive partial charge of the major microspecies taken at
		pH <code>7.4</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>ASAPlus(reactant(0))</code> returns the water accessible surface area of all atoms of the first reactant with positive partial charge
		<code>ASAPlus(product(0), '7.4')</code> returns the water accessible surface area of all atoms with positive partial charge  of the major
		microspecies of the first product taken at pH <code>7.4</code>]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="ASANegative" Class="chemaxon.marvin.calculations.MSAPlugin" JAR="MSAPlugin.jar">
			<Param Name="type" Value="asa-"></Param>
			<ReturnType>double</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the water accessible molecular surface area of all atoms with negative partial charge]]>
			</Description>
			<ReturnValue>
				<![CDATA[the molecular surface area]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>ASANegative()</code> returns the water accessible surface area of all atoms of the input molecule with negative partial charge
		<code>ASANegative('7.4')</code>returns the water accessible surface area of all atoms with negative partial charge of the major microspecies taken at
		pH <code>7.4</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>ASANegative(reactant(0))</code> returns the water accessible surface area of all atoms of the first reactant with negative partial charge
		<code>ASANegative(product(0), '7.4')</code> returns the water accessible surface area of all atoms with negative partial charge  of the major
		microspecies of the first product taken at pH <code>7.4</code>]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="ASAHydrophobic" Class="chemaxon.marvin.calculations.MSAPlugin" JAR="MSAPlugin.jar">
			<Param Name="type" Value="asa_h"></Param>
			<ReturnType>double</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the water accessible molecular surface area of all hydrophobic atoms]]>
			</Description>
			<ReturnValue>
				<![CDATA[the molecular surface area]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>ASAHydrophobic()</code> returns the water accessible surface area of all atoms of the input molecule with hydrophobic partial charge
		<code>ASAHydrophobic('7.4')</code>returns the water accessible surface area of all atoms with hydrophobic partial charge of the major microspecies taken at
		pH <code>7.4</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>ASAHydrophobic(reactant(0))</code> returns the water accessible surface area of all atoms of the first reactant with hydrophobic partial charge
		<code>ASAHydrophobic(product(0), '7.4')</code> returns the water accessible surface area of all atoms with hydrophobic partial charge  of the major
		microspecies of the first product taken at pH <code>7.4</code>]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="ASAPolar" Class="chemaxon.marvin.calculations.MSAPlugin" JAR="MSAPlugin.jar">
			<Param Name="type" Value="asa_p"></Param>
			<ReturnType>double</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the water accessible molecular surface area of all polar atoms]]>
			</Description>
			<ReturnValue>
				<![CDATA[the molecular surface area]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>ASAPolar()</code> returns the water accessible surface area of all atoms of the input molecule with polar partial charge
		<code>ASAPolar('7.4')</code>returns the water accessible surface area of all atoms with polar partial charge of the major microspecies taken at
		pH <code>7.4</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>ASAPolar(reactant(0))</code> returns the water accessible surface area of all atoms of the first reactant with polar partial charge
		<code>ASAPolar(product(0), '7.4')</code> returns the water accessible surface area of all atoms with polar partial charge  of the major
		microspecies of the first product taken at pH <code>7.4</code>]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="isoelectricPoint" Class="chemaxon.marvin.calculations.IsoelectricPointPlugin" JAR="IsoelectricPointPlugin.jar">
			<Alias>pI</Alias>
			<Param Name="type" Value="pI"></Param>
			<ReturnType>double</ReturnType>
			<Category>Protonation</Category>
			<Description>
				<![CDATA[calculates isoelectric point]]>
			</Description>
			<ReturnValue>
				<![CDATA[the isoelectric point]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>isoelectricPoint()</code> returns the isoelectric point of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>isoelectricPoint(reactant(1))</code> returns the isoelectric point of the second reactant]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="averageMicrospeciesCharge" Class="chemaxon.marvin.calculations.IsoelectricPointPlugin" JAR="IsoelectricPointPlugin.jar">
			<Param Name="type" Value="chargedistr"></Param>
			<ReturnType>double</ReturnType>
			<Category>Protonation</Category>
			<Description>
				<![CDATA[calculates the average microspecies charge (weighted sum of charges of all the microspecies of the molecule) at the given pH]]>
			</Description>
			<ReturnValue>
				<![CDATA[the average charge]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the major microspecies pH</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>averageMicrospeciesCharge()</code> returns the average charge of the microspecies of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>averageMicrospeciesCharge(reactant(1))</code> returns the average charge of the microspecies of the second reactant]]>
			</ReactionContextExample>
		</Plugin>
		<!--
		<Plugin ID="pI" Class="chemaxon.marvin.calculations.IsoelectricPointPlugin" JAR="IsoelectricPointPlugin.jar"></Plugin>
		-->
		<Plugin ID="elemanal" Class="chemaxon.marvin.calculations.ElementalAnalyserPlugin" JAR="ElementalAnalyserPlugin.jar"></Plugin>
		<Plugin ID="mass" Class="chemaxon.marvin.calculations.ElementalAnalyserPlugin" JAR="ElementalAnalyserPlugin.jar">
			<Param Name="type" Value="mass"></Param>
			<ReturnType>double</ReturnType>
			<Category>Elemental Analysis</Category>
			<Description>
				<![CDATA[calculates the molecule mass]]>
			</Description>
			<ReturnValue>
				<![CDATA[the mass]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>mass()</code> returns the mass of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>mass(reactant(0))</code> returns the mass of the first reactant]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="exactMass" Class="chemaxon.marvin.calculations.ElementalAnalyserPlugin" JAR="ElementalAnalyserPlugin.jar">
			<Param Name="type" Value="exactmass"></Param>
			<ReturnType>double</ReturnType>
			<Category>Elemental Analysis</Category>
			<Description>
				<![CDATA[calculates the exact mass of the molecule]]>
			</Description>
			<ReturnValue>
				<![CDATA[the exact mass]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>exactMass()</code> returns the exact mass of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>exactMass(product(1))</code> returns the exact mass of the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="atomCount" Class="chemaxon.marvin.calculations.ElementalAnalyserPlugin" JAR="ElementalAnalyserPlugin.jar">
			<Param Name="type" Value="atomcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Elemental Analysis</Category>
			<Description>
				<![CDATA[calculates the number of atoms (all atoms or specific atoms)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the atom count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>atomic number (optional) and mass number (optional) as a single string, separated by "." (e.g. "6" for the
			number of carbon atoms, "6.12" for the number of carbon atoms with mass number <code>12</code>) - if omitted, all
			atoms are counted, if the mass number is set to <code>0</code> then all non-isotope atoms are counted
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>atomCount()</code> returns the number of atoms in the input molecule <code>atomCount("6")</code> returns
		the number of carbon atoms in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>atomCount(reactant(0), "7")</code> returns the number of nitrogen atoms in the first reactant <code>atomCount(product(1), "7.14")</code>
		returns the number of nitrogen atoms with mass number <code>14</code> in the second product <code>atomCount(product(1), "7.0")</code>
		returns the number of non-isotope nitrogen atoms in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="formula" Class="chemaxon.marvin.calculations.ElementalAnalyserPlugin" JAR="ElementalAnalyserPlugin.jar">
			<Param Name="type" Value="formula"></Param>
			<ReturnType>String</ReturnType>
			<Category>Elemental Analysis</Category>
			<Description>
				<![CDATA[returns the formula]]>
			</Description>
			<ReturnValue>
				<![CDATA[the formula]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>formula()</code> returns the formula of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[N / A (reaction rules are numerical)]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="sortableFormula" Class="chemaxon.marvin.calculations.ElementalAnalyserPlugin" JAR="ElementalAnalyserPlugin.jar">
			<Param Name="type" Value="formula"></Param>
			<Param Name="digits" Value="5"></Param>
			<ReturnType>String</ReturnType>
			<Category>Elemental Analysis</Category>
			<Description>
				<![CDATA[returns the fixed digit sortable formula]]>
			</Description>
			<ReturnValue>
				<![CDATA[fixed digit sortable molecular formula]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>sortableFormula()</code> returns the sortable formula of the input molecule; sortableFormula('digits:4')
				returns the 4 digit sortable formula of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[N / A (reaction rules are numerical)]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="isotopeFormula" Class="chemaxon.marvin.calculations.ElementalAnalyserPlugin" JAR="ElementalAnalyserPlugin.jar">
			<Param Name="type" Value="isotopeformula"></Param>
			<ReturnType>String</ReturnType>
			<Category>Elemental Analysis</Category>
			<Description>
				<![CDATA[returns the isotope formula]]>
			</Description>
			<ReturnValue>
				<![CDATA[the isotope formula]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>isotopeFormula()</code> returns the isotope formula of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[N / A (reaction rules are numerical)]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="dotDisconnectedFormula" Class="chemaxon.marvin.calculations.ElementalAnalyserPlugin" JAR="ElementalAnalyserPlugin.jar">
			<Param Name="type" Value="dotdisconnectedformula"></Param>
			<ReturnType>String</ReturnType>
			<Category>Elemental Analysis</Category>
			<Description>
				<![CDATA[returns the dot-disconnected formula]]>
			</Description>
			<ReturnValue>
				<![CDATA[the dot-disconnected formula]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>dotDisconnectedFormula()</code> returns the dot-disconnected formula of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[N / A (reaction rules are numerical)]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="dotDisconnectedIsotopeFormula" Class="chemaxon.marvin.calculations.ElementalAnalyserPlugin" JAR="ElementalAnalyserPlugin.jar">
			<Param Name="type" Value="dotdisconnectedisotopeformula"></Param>
			<ReturnType>String</ReturnType>
			<Category>Elemental Analysis</Category>
			<Description>
				<![CDATA[returns the dot-disconnected isotope formula]]>
			</Description>
			<ReturnValue>
				<![CDATA[the dot-disconnected isotope formula]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>dotDisconnectedIsotopeFormula()</code> returns the dot-disconnected isotope formula of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[N / A (reaction rules are numerical)]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="composition" Class="chemaxon.marvin.calculations.ElementalAnalyserPlugin" JAR="ElementalAnalyserPlugin.jar">
			<Param Name="type" Value="composition"></Param>
			<ReturnType>String</ReturnType>
			<Category>Elemental Analysis</Category>
			<Description>
				<![CDATA[returns the composition]]>
			</Description>
			<ReturnValue>
				<![CDATA[the composition]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>composition()</code> returns the composition of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[N / A (reaction rules are numerical)]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="isotopeComposition" Class="chemaxon.marvin.calculations.ElementalAnalyserPlugin" JAR="ElementalAnalyserPlugin.jar">
			<Param Name="type" Value="isotopecomposition"></Param>
			<ReturnType>String</ReturnType>
			<Category>Elemental Analysis</Category>
			<Description>
				<![CDATA[returns the isotope composition]]>
			</Description>
			<ReturnValue>
				<![CDATA[the isotope composition]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>isotopeComposition()</code> returns the isotope composition of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[N / A (reaction rules are numerical)]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="topanal" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar"></Plugin>
		<Plugin ID="aliphaticAtomCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="aliphaticatomcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the aliphatic atom count]]>
			</Description>
			<ReturnValue>
				<![CDATA[the aliphatic atom count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>aliphaticAtomCount()</code> returns the number of aliphatic atoms in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>aliphaticAtomCount(reactant(0))</code> returns the number of aliphatic atoms in the first reactant <code>aliphaticAtomCount(product(1))</code>
		returns the number of aliphatic atoms in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="aliphaticBondCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="aliphaticbondcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the aliphatic bond count]]>
			</Description>
			<ReturnValue>
				<![CDATA[the aliphatic bond count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>aliphaticBondCount()</code> returns the number of bonds in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>aliphaticBondCount(reactant(0))</code> returns the number of aliphatic bonds in the first reactant <code>aliphaticBondCount(product(1))</code>
		returns the number of aliphatic bonds in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="aliphaticRingCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="aliphaticringcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the aliphatic ring count]]>
			</Description>
			<ReturnValue>
				<![CDATA[the aliphatic ring count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>aliphaticRingCount()</code> returns the number of aliphatic rings in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>aliphaticRingCount(reactant(0))</code> returns the number of aliphatic rings in the first reactant <code>aliphaticRingCount(product(1))</code>
		returns the number of aliphatic rings in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="aliphaticRingCountOfSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="aliphaticringcountofsize"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of aliphatic rings of given size]]>
			</Description>
			<ReturnValue>
				<![CDATA[the number of aliphatic rings of given size]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the ring size]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>aliphaticRingCountOfSize(6)</code> returns the number of aliphatic rings of size 6 in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>aliphaticRingCountOfSize(reactant(0), 5)</code> returns the number of aliphatic rings of size 5 in the first reactant
		<code>aliphaticRingCountOfSize(product(1), 5)</code> returns the number of aliphatic rings of size 5 in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="aliphaticRings" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="aliphaticRings"></Param>
			<ReturnType>int[][]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the aliphatic rings in the molecule]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the aliphatic rings in the molecule (null if the molecule does not contain aliphatic rings)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>aliphaticRings()</code> returns the atom indexes of the aliphatic rings in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>aliphaticRings(reactant(0))</code> returns the atom indexes of the aliphatic rings in the first reactant <code>aliphaticRings(product(1))</code>
		returns the atom indexes of the aliphatic rings in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="aliphaticRingsOfSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="aliphaticRings"></Param>
			<ReturnType>int[][]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the aliphatic rings in the molecule having a given size (number of atoms)]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the aliphatic rings in the molecule having the given size (null if the molecule does not contain aliphatic rings)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the ring size]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>aliphaticRings()</code> returns the atom indexes of the aliphatic rings in the input molecule having the given size]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>aliphaticRings(reactant(0))</code> returns the atom indexes of the aliphatic rings in the first reactant having the given size <code>aliphaticRings(product(1))</code>
		returns the atom indexes of the aliphatic rings in the second product having the given size]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="aromaticAtomCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="aromaticatomcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the aromatic atom count]]>
			</Description>
			<ReturnValue>
				<![CDATA[the aromatic atom count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>aromaticAtomCount()</code> returns the number of aromatic atoms in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>aromaticAtomCount(reactant(0))</code> returns the number of aromatic atoms in the first reactant <code>aromaticAtomCount(product(1))</code>
		returns the number of aromatic atoms in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="aromaticBondCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="aromaticbondcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the aromatic bond count]]>
			</Description>
			<ReturnValue>
				<![CDATA[the aromatic bond count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>aromaticBondCount()</code> returns the number of aromatic bonds in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>aromaticBondCount(reactant(0))</code> returns the number of aromatic bonds in the first reactant <code>aromaticBondCount(product(1))</code>
		returns the number of aromatic bonds in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="aromaticRingCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="aromaticringcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the aromatic ring count]]>
			</Description>
			<ReturnValue>
				<![CDATA[the aromatic ring count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>aromaticRingCount()</code> returns the number of aromatic rings in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>aromaticRingCount(reactant(0))</code> returns the number of aromatic rings in the first reactant <code>aromaticRingCount(product(1))</code>
		returns the number of aromatic rings in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="aromaticRingCountOfSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="aromaticringcountofsize"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of aromatic rings of given size]]>
			</Description>
			<ReturnValue>
				<![CDATA[the number of aromatic rings of given size]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the ring size]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>aromaticRingCountOfSize(6)</code> returns the number of aromatic rings of size 6 in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>aromaticRingCountOfSize(reactant(0), 5)</code> returns the number of aromatic rings of size 5 in the first reactant
		<code>aromaticRingCountOfSize(product(1), 5)</code> returns the number of aromatic rings of size 5 in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="aromaticRings" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="aromaticRings"></Param>
			<ReturnType>int[][]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the aromatic rings in the molecule]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the aromatic rings in the molecule (null if the molecule does not contain aromatic rings)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>aromaticRings()</code> returns the atom indexes of the aromatic rings in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>aromaticRings(reactant(0))</code> returns the atom indexes of the aromatic rings in the first reactant <code>aromaticRings(product(1))</code>
		returns the atom indexes of the aromatic rings in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="aromaticRingsOfSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="aromaticRings"></Param>
			<ReturnType>int[][]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the aromatic rings in the molecule having a given size (number of atoms)]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the aromatic rings in the molecule having the given size (null if the molecule does not contain aromatic rings)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the ring size]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>aromaticRings()</code> returns the atom indexes of the aromatic rings in the input molecule having the given size]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>aromaticRings(reactant(0))</code> returns the atom indexes of the aromatic rings in the first reactant having the given size <code>aromaticRings(product(1))</code>
		returns the atom indexes of the aromatic rings in the second product having the given size]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="carboaliphaticRingCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="carboaliphaticringcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of carboaliphatic rings in the molecule (aliphatic rings containing carbon atoms only)]]>
			</Description>
			<ReturnValue>
				<![CDATA[number of carboaliphatic rings]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>carboaliphaticRingCount()</code> returns the number of carboaliphatic rings in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>carboaliphaticRingCount(reactant(0))</code> returns the number of carboaliphatic rings in the first
		reactant <code>carboaliphaticRingCount(product(1))</code> returns the number of carboaliphatic rings in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="carboaromaticRingCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="carboaromaticringcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of carboaromatic rings in the molecule (aromatic rings containing carbon atoms only)]]>
			</Description>
			<ReturnValue>
				<![CDATA[number of carboaromatic rings]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>carboaromaticRingCount()</code> returns the number of carboaromatic rings in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>carboaromaticRingCount(reactant(0))</code> returns the number of carboaromatic rings in the first
		reactant <code>carboaromaticRingCount(product(1))</code> returns the number of carboaromatic rings in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="carboRingCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="carboringcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of carbocyclic rings in the molecule (rings containing carbon atoms only)]]>
			</Description>
			<ReturnValue>
				<![CDATA[number of carbocyclic rings]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>carboRingCount()</code> returns the number of carbocyclic rings in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>carboRingCount(reactant(0))</code> returns the number of carbocyclic rings in the first reactant <code>carboRingCount(product(1))</code>
		returns the number of carbocyclic rings in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="carboRingCountOfSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="carboringcountofsize"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of carbocyclic rings of given size (rings containing carbon atoms only) ]]>
			</Description>
			<ReturnValue>
				<![CDATA[the number of carbocyclic rings of given size]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the ring size]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>carboRingCountOfSize(6)</code> returns the number of carbocyclic rings of size 6 in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>carboRingCountOfSize(reactant(0), 5)</code> returns the number of carbocyclic rings of size 5 in the first reactant
		<code>carboRingCountOfSize(product(1), 5)</code> returns the number of carbocyclic rings of size 5 in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="carboRings" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="carboRings"></Param>
			<ReturnType>int[][]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the carbocyclic rings in the molecule (rings containing carbon atoms only)]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the carbocyclic rings in the molecule (null if the molecule does not contain carbocyclic rings)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>carboRings()</code> returns the atom indexes of the carbocyclic rings in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>carboRings(reactant(0))</code> returns the atom indexes of the carbocyclic rings in the first reactant <code>carboRings(product(1))</code>
		returns the atom indexes of the carbocyclic rings in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="carboRingsOfSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="carboRings"></Param>
			<ReturnType>int[][]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the carbocyclic rings in the molecule having a given size (number of atoms)]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the carbocyclic rings in the molecule having the given size (null if the molecule does not carbocyclic carbo rings)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the ring size]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>carboRings()</code> returns the atom indexes of the carbocyclic rings in the input molecule having the given size]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>carboRings(reactant(0))</code> returns the atom indexes of the carbocyclic rings in the first reactant having the given size <code>carboRings(product(1))</code>
		returns the atom indexes of the carbocyclic rings in the second product having the given size]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="rotatableBondCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="rotatablebondcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the rotatable bond count]]>
			</Description>
			<ReturnValue>
				<![CDATA[the rotatable bond count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>rotatableBondCount()</code> returns the number of rotatable bonds in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>rotatableBondCount(reactant(0))</code> returns the number of rotatable bonds in the first reactant <code>rotatableBondCount(product(1))</code>
		returns the number of rotatable bonds in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="fragmentCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="fragmentcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[returns the number of fragments (disconnected parts)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the fragment count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>fragmentCount()</code> returns the number of fragments in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>fragmentCount(reactant(0))</code> returns the number of fragments in the first reactant <code>fragmentCount(product(1))</code>
		returns the number of fragments in the second product]]>
			</ReactionContextExample>
		</Plugin>

		<Plugin ID="fusedAliphaticRingCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="fusedaliphaticringcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of fused aliphatic rings<br />
		(SSSR smallest set of smallest aliphatic rings)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the fused aliphatic ring count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>fusedAliphaticRingCount()</code> returns the number of fused aliphatic rings in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>fusedAliphaticRingCount(reactant(0))</code> returns the number of fused aliphatic rings in the first
		reactant <code>fusedAliphaticRingCount(product(1))</code> returns the number of fused aliphatic rings in the second
		product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="fusedAliphaticRingCountOfSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="fusedaliphaticringcountofsize"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of fused aliphatic rings of given size]]>
			</Description>
			<ReturnValue>
				<![CDATA[the number of fused aliphatic rings of given size]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the ring size]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>fusedAliphaticRingCountOfSize(6)</code> returns the number of fused aliphatic rings of size 6 in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>fusedAliphaticRingCountOfSize(reactant(0), 5)</code> returns the number of fused aliphatic rings of size 5 in the first reactant
		<code>fusedAliphaticRingCountOfSize(product(1), 5)</code> returns the number of fused aliphatic rings of size 5 in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="fusedAliphaticRings" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="fusedAliphaticRings"></Param>
			<ReturnType>int[][]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the fused aliphatic rings in the molecule]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the fused aliphatic rings in the molecule (null if the molecule does not contain fused aliphatic rings)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>fusedAliphaticRings()</code> returns the atom indexes of the fused aliphatic rings in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>fusedAliphaticRings(reactant(0))</code> returns the atom indexes of the fused aliphatic rings in the first reactant <code>fusedAliphaticRings(product(1))</code>
		returns the atom indexes of the fused aliphatic rings in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="fusedAliphaticRingsOfSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="fusedAliphaticRings"></Param>
			<ReturnType>int[][]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the fused aliphatic rings in the molecule having a given size (number of atoms)]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the fused aliphatic rings in the molecule having the given size (null if the molecule does not contain fused aliphatic rings)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the ring size]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>fusedAliphaticRings()</code> returns the atom indexes of the fused aliphatic rings in the input molecule having the given size]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>fusedAliphaticRings(reactant(0))</code> returns the atom indexes of the fused aliphatic rings in the first reactant having the given size <code>fusedAliphaticRings(product(1))</code>
		returns the atom indexes of the fused aliphatic rings in the second product having the given size]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="fusedAromaticRingCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="fusedaromaticringcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of fused aromatic rings<br />
		(SSSR smallest set of smallest aromatic rings)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the fused aromatic ring count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>fusedAromaticRingCount()</code> returns the number of fused aromatic rings in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>fusedAromaticRingCount(reactant(0))</code> returns the number of fused aromatic rings in the first
		reactant <code>fusedAromaticRingCount(product(1))</code> returns the number of fused aromatic rings in the second
		product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="fusedAromaticRingCountOfSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="fusedaromaticringcountofsize"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of fused aromatic rings of given size]]>
			</Description>
			<ReturnValue>
				<![CDATA[the number of fused aromatic rings of given size]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the ring size]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>fusedAromaticRingCountOfSize(6)</code> returns the number of fused aromatic rings of size 6 in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>fusedAromaticRingCountOfSize(reactant(0), 5)</code> returns the number of fused aromatic rings of size 5 in the first reactant
		<code>fusedAromaticRingCountOfSize(product(1), 5)</code> returns the number of fused aromatic rings of size 5 in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="fusedAromaticRings" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="fusedAromaticRings"></Param>
			<ReturnType>int[][]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the fused aromatic rings in the molecule]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the fused aromatic rings in the molecule (null if the molecule does not contain fused aromatic rings)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>fusedAromaticRings()</code> returns the atom indexes of the fused aromatic rings in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>fusedAromaticRings(reactant(0))</code> returns the atom indexes of the fused aromatic rings in the first reactant <code>fusedAromaticRings(product(1))</code>
		returns the atom indexes of the fused aromatic rings in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="fusedAromaticRingsOfSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="fusedAromaticRings"></Param>
			<ReturnType>int[][]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the fused aromatic rings in the molecule having a given size (number of atoms)]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the fused aromatic rings in the molecule having the given size (null if the molecule does not contain fused aromatic rings)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the ring size]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>fusedAromaticRings()</code> returns the atom indexes of the fused aromatic rings in the input molecule having the given size]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>fusedAromaticRings(reactant(0))</code> returns the atom indexes of the fused aromatic rings in the first reactant having the given size <code>fusedAromaticRings(product(1))</code>
		returns the atom indexes of the fused aromatic rings in the second product having the given size]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="heteroaliphaticRingCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="heteroaliphaticringcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of aliphatic heterocyclic rings<br />
		(SSSR smallest set of smallest aliphatic rings)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the aliphatic heterocyclic ring count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>heteroaliphaticRingCount()</code> returns the number of aliphatic heterocyclic rings in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>heteroaliphaticRingCount(reactant(0))</code> returns the number of aliphatic heterocyclic rings in the first
		reactant <code>heteroaliphaticRingCount(product(1))</code> returns the number of aliphatic heterocyclic rings in the second
		product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="heteroaliphaticRingCountOfSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="heteroaliphaticringcountofsize"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of aliphatic heterocyclic rings of given size]]>
			</Description>
			<ReturnValue>
				<![CDATA[the number of aliphatic heterocyclic rings of given size]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the ring size]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>heteroaliphaticRingCountOfSize(6)</code> returns the number of aliphatic heterocyclic rings of size 6 in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>heteroaliphaticRingCountOfSize(reactant(0), 5)</code> returns the number of aliphatic heterocyclic rings of size 5 in the first reactant
		<code>heteroaliphaticRingCountOfSize(product(1), 5)</code> returns the number of aliphatic heterocyclic rings of size 5 in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="heteroaliphaticRings" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="heteroaliphaticRings"></Param>
			<ReturnType>int[][]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the aliphatic heterocyclic rings in the molecule]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the aliphatic heterocyclic rings in the molecule (null if the molecule does not contain aliphatic heterocyclic rings)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>heteroaliphaticRings()</code> returns the atom indexes of the aliphatic heterocyclic rings in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>heteroaliphaticRings(reactant(0))</code> returns the atom indexes of the aliphatic heterocyclic rings in the first reactant <code>heteroaliphaticRings(product(1))</code>
		returns the atom indexes of the aliphatic heterocyclic rings in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="heteroaliphaticRingsOfSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="heteroaliphaticRings"></Param>
			<ReturnType>int[][]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the aliphatic heterocyclic rings in the molecule having a given size (number of atoms)]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the aliphatic heterocyclic rings in the molecule having the given size (null if the molecule does not contain aliphatic heterocyclic rings)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the ring size]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>heteroaliphaticRings()</code> returns the atom indexes of the aliphatic heterocyclic rings in the input molecule having the given size]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>heteroaliphaticRings(reactant(0))</code> returns the atom indexes of the aliphatic heterocyclic rings in the first reactant having the given size <code>heteroaliphaticRings(product(1))</code>
		returns the atom indexes of the aliphatic heterocyclic rings in the second product having the given size]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="heteroaromaticRingCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="heteroaromaticringcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of aromatic heterocyclic rings<br />
		(SSSR smallest set of smallest aromatic rings)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the aromatic heterocyclic ring count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>heteroaromaticRingCount()</code> returns the number of aromatic heterocyclic rings in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>heteroaromaticRingCount(reactant(0))</code> returns the number of aromatic heterocyclic rings in the first
		reactant <code>heteroaromaticRingCount(product(1))</code> returns the number of aromatic heterocyclic rings in the second
		product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="heteroaromaticRingCountOfSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="heteroaromaticringcountofsize"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of aromatic heterocyclic rings of given size]]>
			</Description>
			<ReturnValue>
				<![CDATA[the number of aromatic heterocyclic rings of given size]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the ring size]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>heteroaromaticRingCountOfSize(6)</code> returns the number of aromatic heterocyclic rings of size 6 in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>heteroaromaticRingCountOfSize(reactant(0), 5)</code> returns the number of aromatic heterocyclic rings of size 5 in the first reactant
		<code>heteroaromaticRingCountOfSize(product(1), 5)</code> returns the number of aromatic heterocyclic rings of size 5 in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="heteroaromaticRings" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="heteroaromaticRings"></Param>
			<ReturnType>int[][]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the aromatic heterocyclic rings in the molecule]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the aromatic heterocyclic rings in the molecule (null if the molecule does not contain aromatic heterocyclic rings)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>heteroaromaticRings()</code> returns the atom indexes of the aromatic heterocyclic rings in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>heteroaromaticRings(reactant(0))</code> returns the atom indexes of the aromatic heterocyclic rings in the first reactant <code>heteroaromaticRings(product(1))</code>
		returns the atom indexes of the aromatic heterocyclic rings in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="heteroaromaticRingsOfSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="heteroaromaticRings"></Param>
			<ReturnType>int[][]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the aromatic heterocyclic rings in the molecule having a given size (number of atoms)]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the aromatic heterocyclic rings in the molecule having the given size (null if the molecule does not contain aromatic heterocyclic rings)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the ring size]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>heteroaromaticRings()</code> returns the atom indexes of the aromatic heterocyclic rings in the input molecule having the given size]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>heteroaromaticRings(reactant(0))</code> returns the atom indexes of the aromatic heterocyclic rings in the first reactant having the given size <code>heteroaromaticRings(product(1))</code>
		returns the atom indexes of the aromatic heterocyclic rings in the second product having the given size]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="heteroRingCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="heteroringcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of heterocyclic rings<br />
		(SSSR smallest set of smallest rings)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the heterocyclic ring count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>heteroRingCount()</code> returns the number of heterocyclic rings in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>heteroRingCount(reactant(0))</code> returns the number of heterocyclic rings in the first
		reactant <code>heteroRingCount(product(1))</code> returns the number of heterocyclic rings in the second
		product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="heteroRingCountOfSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="heteroringcountofsize"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of heterocyclic rings of given size]]>
			</Description>
			<ReturnValue>
				<![CDATA[the number of heterocyclic rings of given size]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the ring size]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>heteroRingCountOfSize(6)</code> returns the number of heterocyclic rings of size 6 in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>heteroRingCountOfSize(reactant(0), 5)</code> returns the number of heterocyclic rings of size 5 in the first reactant
		<code>heteroRingCountOfSize(product(1), 5)</code> returns the number of heterocyclic rings of size 5 in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="heteroRings" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="heteroRings"></Param>
			<ReturnType>int[][]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the heterocyclic rings in the molecule]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the heterocyclic rings in the molecule (null if the molecule does not contain heterocyclic rings)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>heteroRings()</code> returns the atom indexes of the heterocyclic rings in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>heteroRings(reactant(0))</code> returns the atom indexes of the heterocyclic rings in the first reactant <code>heteroRings(product(1))</code>
		returns the atom indexes of the heterocyclic rings in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="heteroRingsOfSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="heteroRings"></Param>
			<ReturnType>int[][]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the heterocyclic rings in the molecule having a given size (number of atoms)]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the heterocyclic rings in the molecule having the given size (null if the molecule does not contain heterocyclic rings)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the ring size]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>heteroRings()</code> returns the atom indexes of the heterocyclic rings in the input molecule having the given size]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>heteroRings(reactant(0))</code> returns the atom indexes of the heterocyclic rings in the first reactant having the given size <code>heteroRings(product(1))</code>
		returns the atom indexes of the heterocyclic rings in the second product having the given size]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="largestRing" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="largestring"></Param>
			<ReturnType>int[]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the atoms of the largest ring (number of atoms) in the molecule.]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the largest ring in the molecule (null when acyclic)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>largestRingSize()</code> returns the atom indexes of the largest ring in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>largestRingSize(product(1))</code> returns the atom indexes of the largest ring in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="largestRingSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="largestringsize"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the largest ring size]]>
			</Description>
			<ReturnValue>
				<![CDATA[the largest ring size]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>largestRingSize()</code> returns the size of the largest ring in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>largestRingSize(product(1))</code> returns the size of the largest ring in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="largestRingSystem" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="largestRingSystem"></Param>
			<ReturnType>int[][]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the atoms of the largest ring system (number of rings) in the molecule.]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the largest ring system in the molecule (null when acyclic)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>largestRingSize()</code> returns the atom indexes of the largest ring system in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>largestRingSize(product(1))</code> returns the atom indexes of the largest ring system in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="largestRingSystemSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="largestringsystemsize"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the size of the largest ring system (number of rings)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the size of the largest ring system]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>largestRingSystemSize()</code> returns the size of the largest ring system in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>largestRingSystemSize(reactant(0))</code> returns the size of the largest ring system in the first reactant <code>largestringsystemsize(product(1))</code>
		returns the size of the largest ring system in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="ringAtomCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="ringatomcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the ring atom count]]>
			</Description>
			<ReturnValue>
				<![CDATA[the ring atom count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>ringAtomCount()</code> returns the number of ring atoms in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>ringAtomCount(reactant(0))</code> returns the number of ring atoms in the first reactant <code>ringAtomCount(product(1))</code>
		returns the number of ring atoms in the second product]]>
			</ReactionContextExample>
			<MoleculeContextExample>
				<![CDATA[<code>ringAtomCount()</code> returns the number of ring atoms in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>ringAtomCount(reactant(0))</code> returns the number of ring atoms in the first reactant <code>ringAtomCount(product(1))</code>
		returns the number of ring atoms in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="ringBondCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="ringbondcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the ring bond count]]>
			</Description>
			<ReturnValue>
				<![CDATA[the ring bond count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>ringBondCount()</code> returns the number of ring bonds in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>ringBondCount(reactant(0))</code> returns the number of ring bonds in the first reactant <code>ringBondCount(product(1))</code>
		returns the number of ring bonds in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="ringCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="ringcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the ring count]]>
			</Description>
			<ReturnValue>
				<![CDATA[the ring count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>ringCount()</code> returns the number of rings in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>ringCount(reactant(0))</code> returns the number of rings in the first reactant <code>ringCount(product(1))</code>
		returns the number of rings in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="ringCountOfSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="ringcountofsize"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of rings of given size]]>
			</Description>
			<ReturnValue>
				<![CDATA[the number of rings of given size]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the ring size]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>ringCountOfSize(6)</code> returns the number of rings of size 6 in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>ringCountOfSize(reactant(0), 5)</code> returns the number of rings of size 5 in the first reactant
		<code>ringCountOfSize(product(1), 5)</code> returns the number of rings of size 5 in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="rings" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="rings"></Param>
			<ReturnType>int[][]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the rings in the molecule]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the rings in the molecule (null if the molecule is acyclic)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>rings()</code> returns the atom indexes of the rings in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>rings(reactant(0))</code> returns the atom indexes of the rings in the first reactant <code>rings(product(1))</code>
		returns the atom indexes of the rings in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="ringsOfSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="ringsOfSize"></Param>
			<ReturnType>int[][]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the rings in the molecule having a given size (number of atoms)]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the rings in the molecule having the given size (null if the molecule is acyclic or contains different rings only)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the ring size]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>rings()</code> returns the atom indexes of the rings in the input molecule having the given size]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>rings(reactant(0))</code> returns the atom indexes of the rings in the first reactant having the given size <code>rings(product(1))</code>
		returns the atom indexes of the rings in the second product having the given size]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="ringSystems" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="ringSystems"></Param>
			<ReturnType>int[][]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the ring systems in the molecule (fused and spiro rings belong to one ring system)]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the ring systems in the molecule (null if the molecule is acyclic)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>ringSystems()</code> returns the atom indexes of the ring systems in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>ringSystems(reactant(0))</code> returns the atom indexes of the ring systems in the first reactant <code>ringSystems(product(1))</code>
		returns the atom indexes of the ring systems in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="ringSystemsOfSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="ringSystemsOfSize"></Param>
			<ReturnType>int[][]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the ring systems in the molecule having a given size (number of atoms, fused and spiro rings belong to one ring system)]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the ring systems in the molecule having the given size (null if the molecule is acyclic or contains different ringSystems only)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the ring size]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>ringSystems()</code> returns the atom indexes of the ring systems in the input molecule having the given size]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>ringSystems(reactant(0))</code> returns the atom indexes of the ring systems in the first reactant having the given size <code>ringSystems(product(1))</code>
		returns the atom indexes of the ring systems in the second product having the given size]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="smallestRing" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="smallestring"></Param>
			<ReturnType>int[]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the atoms of the smallest ring (number of atoms) in the molecule.]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the smallest ring in the molecule (null when acyclic)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>smallestRingSize()</code> returns the atom indexes of the smallest ring in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>smallestRingSize(product(1))</code> returns the atom indexes of the smallest ring in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="smallestRingSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="smallestringsize"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the smallest ring size]]>
			</Description>
			<ReturnValue>
				<![CDATA[the smallest ring size]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>smallestRingSize()</code> returns the size of the smallest ring in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>smallestRingSize(reactant(0))</code> returns the size of the smallest ring in the first reactant]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="smallestRingSystem" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="smallestRingSystem"></Param>
			<ReturnType>int[][]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[identifies the atoms of the smallest ring system (number of rings) in the molecule.]]>
			</Description>
			<ReturnValue>
				<![CDATA[atom indexes of the smallest ring system in the molecule (null when acyclic)]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>smallestRingSize()</code> returns the atom indexes of the smallest ring system in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>smallestRingSize(product(1))</code> returns the atom indexes of the smallest ring system in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="smallestRingSystemSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="smallestringsystemsize"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the size of the smallest ring system (number of rings)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the size of the smallest ring system]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>smallestRingSystemSize()</code> returns the size of the smallest ring system in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>smallestRingSystemSize(reactant(0))</code> returns the size of the smallest ring system in the first reactant <code>smallestringsystemsize(product(1))</code>
		returns the size of the smallest ring system in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="chainAtomCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="chainatomcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the chain atom count]]>
			</Description>
			<ReturnValue>
				<![CDATA[the chain atom count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>chainAtomCount()</code> returns the number of chain atoms in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>chainAtomCount(reactant(0))</code> returns the number of chain atoms in the first reactant <code>chainAtomCount(product(1))</code>
		returns the number of chain atoms in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="chainBondCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="chainbondcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the chain bond count]]>
			</Description>
			<ReturnValue>
				<![CDATA[the chain bond count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>chainBondCount()</code> returns the number of chain bonds in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>chainBondCount(reactant(0))</code> returns the number of chain bonds in the first reactant <code>chainBondCount(product(1))</code>
		returns the number of chain bonds in the second product]]>
			</ReactionContextExample>
		</Plugin>


		<Plugin ID="fusedRingCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="fusedringcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of fused rings<br />
		(SSSR smallest set of smallest rings)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the fused ring count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>fusedRingCount()</code> returns the number of fused rings in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>fusedRingCount(reactant(0))</code> returns the number of fused rings in the first reactant <code>fusedRingCount(product(1))</code>
		returns the number of fused rings in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="ringSystemCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="ringsystemcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of rings systems]]>
			</Description>
			<ReturnValue>
				<![CDATA[the number of rings systems]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>ringSystemCount()</code> returns the number of ring systems in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>ringSystemCount(reactant(0))</code> returns the number of ring systems in the first reactant <code>ringsystemCount(product(1))</code>
		returns the number of ring systems in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="ringSystemCountOfSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="ringsystemcountofsize"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of rings systems of given size]]>
			</Description>
			<ReturnValue>
				<![CDATA[the number of rings systems of given size]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[the ring system size]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>ringSystemCountOfSize(4)</code> returns the number of four-membered ring systems in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>ringSystemCountOfSize(reactant(0),3)</code> returns the number of three-membered ring systems in the first reactant
		<code>ringsystemCountOfSize(product(1),3)</code> returns the number of three-membered ring systems in the second product]]>
			</ReactionContextExample>
		</Plugin>


		<Plugin ID="asymmetricAtomCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="asymmetricatomcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of asymmetric atoms]]>
			</Description>
			<ReturnValue>
				<![CDATA[the asymmetric atom count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>asymmetricAtomCount()</code> returns the number of asymmetric atoms in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>asymmetricAtomCount(reactant(0))</code> returns the number of asymmetric atoms in the first reactant <code>asymmetricAtomCount(product(1))</code>
		returns the number of asymmetric atoms in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="asymmetricAtoms" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="asymmetricatoms"></Param>
			<ReturnType>int[]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[determines the asymmetric atoms]]>
			</Description>
			<ReturnValue>
				<![CDATA[indexes of asymmetric atoms]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>asymmetricAtoms()</code> returns the indexes of asymmetric atoms in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>asymmetricAtoms(reactant(0))</code> returns the indexes of asymmetric atoms in the first reactant <code>asymmetricAtoms(product(1))</code>
		returns the indexes of asymmetric atoms in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="chiralCenterCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="chiralcentercount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of tetrahedral stereogenic center atoms]]>
			</Description>
			<ReturnValue>
				<![CDATA[the tetrahedral stereogenic center count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>chiralCenterCount()</code> returns the number of tetrahedral stereogenic centers in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>chiralCenterCount(reactant(0))</code> returns the number of tetrahedral stereogenic centers in the first
		reactant <code>chiralCenterCount(product(1))</code> returns the number of tetrahedral stereogenic centers in the second
		product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="chiralCenters" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="chiralcenters"></Param>
			<ReturnType>int[]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[determines the chiral center atoms]]>
			</Description>
			<ReturnValue>
				<![CDATA[indexes of chiral center atoms]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>chiralCenters()</code> returns the indexes of chiral center atoms in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>chiralCenters(reactant(0))</code> returns the indexes of chiral center atoms in the first
		reactant <code>chiralCenters(product(1))</code> returns the indexes of chiral center atoms in the second
		product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="stereoDoubleBondCount" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="stereoDoubleBondCount"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of stereo double bonds]]>
			</Description>
			<ReturnValue>
				<![CDATA[the stereo double bond count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>stereoDoubleBondCount()</code> returns the number of stereo double bonds in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>stereoDoubleBondCount(reactant(0))</code> returns the number of stereo double bonds in the first
		reactant <code>stereoDoubleBondCount(product(1))</code> returns the number of stereo double bonds in the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="aromaticAtom" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="aromaticatom"></Param>
			<ReturnType>boolean</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[checks if the specified atom is aromatic]]>
			</Description>
			<ReturnValue>
				<![CDATA[<code>true</code> for aromatic atoms,<br />
<code>false</code> for non-aromatic atoms]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>aromaticAtom(2)</code> returns <code>true</code> if atom <code>2</code> of the input molecule is
		aromatic, <code>false</code> otherwise]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>aromaticAtom(ratom(2))</code> returns <code>true</code> if the reactant atom matching map <code>2</code>
		in the reaction equation is aromatic, <code>false</code> otherwise <code>aromaticAtom(patom(1))</code> returns <code>true</code>
		if the product atom matching map <code>1</code> in the reaction equation is aromatic, <code>false</code> otherwise]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="aliphaticAtom" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="aliphaticatom"></Param>
			<ReturnType>boolean</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[checks if the specified atom is aliphatic]]>
			</Description>
			<ReturnValue>
				<![CDATA[<code>true</code> for aliphatic atoms,<br />
<code>false</code> for non-aliphatic atoms]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>aliphaticAtom(2)</code> returns <code>true</code> if atom <code>2</code> of the input molecule is
		aliphatic, <code>false</code> otherwise]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>aliphaticAtom(ratom(2))</code> returns <code>true</code> if the reactant atom matching map <code>2</code>
		in the reaction equation is aliphatic, <code>false</code> otherwise <code>aliphaticAtom(patom(1))</code> returns <code>true</code>
		if the product atom matching map <code>1</code> in the reaction equation is aliphatic, <code>false</code> otherwise]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="chainAtom" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="chainatom"></Param>
			<ReturnType>boolean</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[checks if the specified atom is a chain atom]]>
			</Description>
			<ReturnValue>
				<![CDATA[<code>true</code> for chain atoms,<br />
<code>false</code> for non-chain atoms]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>chainAtom(2)</code> returns <code>true</code> if atom <code>2</code> of the input molecule is a chain
		atom, <code>false</code> otherwise]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>chainAtom(ratom(2))</code> returns <code>true</code> if the reactant atom matching map <code>2</code> in
		the reaction equation is a chain atom, <code>false</code> otherwise <code>chainAtom(patom(1))</code> returns <code>true</code>
		if the product atom matching map <code>1</code> in the reaction equation is a chain atom, <code>false</code> otherwise]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="ringAtom" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="ringatom"></Param>
			<ReturnType>boolean</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[checks if the specified atom is a ring atom]]>
			</Description>
			<ReturnValue>
				<![CDATA[<code>true</code> for ring atoms,<br />
<code>false</code> for non-ring atoms]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>ringAtom(2)</code> returns <code>true</code> if atom <code>2</code> of the input molecule is a ring atom,
		<code>false</code> otherwise]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>ringAtom(ratom(2))</code> returns <code>true</code> if the reactant atom matching map <code>2</code> in
		the reaction equation is a ring atom, <code>false</code> otherwise <code>ringAtom(patom(1))</code> returns <code>true</code>
		if the product atom matching map <code>1</code> in the reaction equation is a ring atom, <code>false</code> otherwise]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="asymmetricAtom" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="asymmetricatom"></Param>
			<ReturnType>boolean</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[checks if the specified atom is an asymmetric atom]]>
			</Description>
			<ReturnValue>
				<![CDATA[<code>true</code> for asymmetric atoms,<br />
<code>false</code> for symmetric atoms]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>asymmetricAtom(2)</code> returns <code>true</code> if atom <code>2</code> of the input molecule is an
		asymmetric atom, <code>false</code> otherwise]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>asymmetricAtom(ratom(2))</code> returns <code>true</code> if the reactant atom matching map <code>2</code>
		in the reaction equation is an asymmetric atom, <code>false</code> otherwise <code>asymmetricAtom(patom(1))</code>
		returns <code>true</code> if the product atom matching map <code>1</code> in the reaction equation is an asymmetric
		atom, <code>false</code> otherwise]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="chiralCenter" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="chiralcenter"></Param>
			<ReturnType>boolean</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[checks if the specified atom is a tetrahedral stereogenic center]]>
			</Description>
			<ReturnValue>
				<![CDATA[<code>true</code> for tetrahedral stereogenic center atoms]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>chiralCenter(2)</code> returns <code>true</code> if atom <code>2</code> of the input molecule is a
		tetrahedral stereogenic center, <code>false</code> otherwise]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>chiralCenter(ratom(2))</code> returns <code>true</code> if the reactant atom matching map <code>2</code>
		in the reaction equation is a tetrahedral stereogenic center, <code>false</code> otherwise <code>chiralCenter(patom(1))</code>
		returns <code>true</code> if the product atom matching map <code>1</code> in the reaction equation is a tetrahedral
		stereogenic center, <code>false</code> otherwise]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="cyclomaticNumber" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="cyclomaticNumber"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the cyclomatic number]]>
			</Description>
			<ReturnValue>
				<![CDATA[the cyclomatic number]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>cyclomaticNumber()</code> returns the cyclomatic number of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>cyclomaticNumber(reactant(0))</code> returns the cyclomatic number of the first reactant <code>cyclomaticNumber(product(1))</code>
		returns the cyclomatic number of the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="plattIndex" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="plattIndex"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the Platt index]]>
			</Description>
			<ReturnValue>
				<![CDATA[the Platt index]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[calculates the Platt index]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[the Platt index]]>
			</ReactionContextExample>
			<MoleculeContextExample>
				<![CDATA[<code>plattIndex()</code> returns the Platt index of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>plattIndex(reactant(0))</code> returns the Platt index of the first reactant <code>plattIndex(product(1))</code>
		returns the Platt index of the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="randicIndex" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="randicIndex"></Param>
			<ReturnType>double</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the Randic index]]>
			</Description>
			<ReturnValue>
				<![CDATA[the Randic index]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[calculates the Randic index]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[the Randic index]]>
			</ReactionContextExample>
			<MoleculeContextExample>
				<![CDATA[<code>randicIndex()</code> returns the Randic index of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>randicIndex(reactant(0))</code> returns the Randic index of the first reactant <code>randicIndex(product(1))</code>
		returns the Randic index of the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="balabanIndex" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="balabanIndex"></Param>
			<ReturnType>double</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the Balaban index]]>
			</Description>
			<ReturnValue>
				<![CDATA[the Balaban index]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[calculates the Balaban index]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[the Balaban index]]>
			</ReactionContextExample>
			<MoleculeContextExample>
				<![CDATA[<code>balabanIndex()</code> returns the Balaban index of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>balabanIndex(reactant(0))</code> returns the Balaban index of the first reactant <code>balabanIndex(product(1))</code>
		returns the Balaban index of the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="distanceDegree" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="distanceDegree"></Param>
			<ReturnType>double[]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the distance degree of an atom]]>
			</Description>
			<ReturnValue>
				<![CDATA[the distance degree]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>distanceDegree(2)</code> returns the distance degree of atom <code>2</code> of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>distanceDegree(ratom(2))</code> returns the distance degree of the reactant atom matching map <code>2</code>
		in the reaction equation <code>distanceDegree(patom(1))</code> returns the distance degree of the product atom matching
		map <code>1</code> in the reaction equation]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="eccentricity" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="eccentricity"></Param>
			<ReturnType>int[]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the eccentricity of an atom]]>
			</Description>
			<ReturnValue>
				<![CDATA[the eccentricity of an atom]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>eccentricity(2)</code> returns the eccentricity of atom <code>2</code> of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>eccentricity(ratom(2))</code> returns the eccentricity of the reactant atom matching map <code>2</code>
		in the reaction equation <code>eccentricity(patom(1))</code> returns the distance degree of the product atom matching
		map <code>1</code> in the reaction equation]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="hararyIndex" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="hararyIndex"></Param>
			<ReturnType>double</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the Harary index]]>
			</Description>
			<ReturnValue>
				<![CDATA[the Harary index]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[calculates the Harary index]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[the Harary index]]>
			</ReactionContextExample>
			<MoleculeContextExample>
				<![CDATA[<code>hararyIndex()</code> returns the Harary index of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>hararyIndex(reactant(0))</code> returns the Harary index of the first reactant <code>hararyIndex(product(1))</code>
		returns the Harary index of the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="hyperWienerIndex" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="hyperWienerIndex"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the Hyper Wiener index]]>
			</Description>
			<ReturnValue>
				<![CDATA[the Hyper Wiener index]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[calculates the Hyper Wiener index]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[the Hyper Wiener index]]>
			</ReactionContextExample>
			<MoleculeContextExample>
				<![CDATA[calculates the Wiener index]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[the Wiener index]]>
			</ReactionContextExample>
			<MoleculeContextExample>
				<![CDATA[<code>hyperWienerIndex()</code> returns the Hyper Wiener index of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>hyperWienerIndex(reactant(0))</code> returns the Hyper Wiener index of the first reactant <code>hyperWienerIndex(product(1))</code>
		returns the Hyper Wiener index of the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="szegedIndex" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="szegedIndex"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the Szeged index]]>
			</Description>
			<ReturnValue>
				<![CDATA[the Szeged index]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[calculates the Szeged index]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[the Szeged index]]>
			</ReactionContextExample>
			<MoleculeContextExample>
				<![CDATA[<code>szegedIndex()</code> returns the Szeged index of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>szegedIndex(reactant(0))</code> returns the Szeged index of the first reactant <code>szegedIndex(product(1))</code>
		returns the Szeged index of the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="wienerIndex" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="wienerIndex"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the Wiener index]]>
			</Description>
			<ReturnValue>
				<![CDATA[the Wiener index]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>wienerIndex()</code> returns the Wiener index of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>wienerIndex(reactant(0))</code> returns the Wiener index of the first reactant <code>wienerIndex(product(1))</code>
		returns the Wiener index of the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="wienerPolarity" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="wienerPolarity"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the Wiener polarity]]>
			</Description>
			<ReturnValue>
				<![CDATA[the Wiener polarity]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>wienerPolarity()</code> returns the Wiener polarity of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>wienerPolarity(reactant(0))</code> returns the Wiener polarity of the first reactant <code>wienerPolarity(product(1))</code>
		returns the Wiener polarity of the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="stericEffectIndex" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="stericEffectIndex"></Param>
			<ReturnType>double/double[]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the steric effect index of an atom]]>
			</Description>
			<ReturnValue>
				<![CDATA[the steric effect index of an atom]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[calculates the steric effect index of an atom]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[the steric effect index of an atom]]>
			</ReactionContextExample>
			<MoleculeContextExample>
				<![CDATA[<code>stericEffectIndex(2)</code> returns the steric effect index of atom <code>2</code> of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>stericEffectIndex(ratom(2))</code> returns the steric effect index of the reactant atom matching map <code>2</code>
		in the reaction equation <code>stericEffectIndex(patom(1))</code> returns the steric effect index of the product atom
		matching map <code>1</code> in the reaction equation]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="smallestAtomRingSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="smallestatomringsize"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the size of the smallest ring containing the specified atom]]>
			</Description>
			<ReturnValue>
				<![CDATA[the size of the smallest ring containing the specified atom]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>smallestAtomRingSize(0)</code> returns the size of the smallest ring containing atom <code>0</code> in
		the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>smallestAtomRingSize(ratom(1))</code> returns the size of the smallest ring containing reactant atom
		matching map <code>1</code> in the reaction equation]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="largestAtomRingSize" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="largestatomringsize"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the size of the largest ring containing the specified atom]]>
			</Description>
			<ReturnValue>
				<![CDATA[the size of the largest ring containing the specified atom]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>largestAtomRingSize(1)</code> returns the size of the largest ring containing atom <code>1</code>
		in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>largestAtomRingSize(patom(2))</code> returns the size of the largest ring containing product atom
		matching map <code>2</code> in the reaction equation]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="shortestPath" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="shortestpath"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the length of the shortest path between two atoms]]>
			</Description>
			<ReturnValue>
				<![CDATA[the length of the shortest path between two atoms, <code>Integer.MAX_VALUE</code> if disconnected]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the (1-based) atom indexes of the two atoms in a string: "index1-index2" (e.g. '2-3')
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>shortestPath('2-3')</code> and <code>shortestPath(pair(1, 2))</code> both return the shortest path length
		between atoms <code>1</code> and <code>2</code> in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>shortestPath(reactant(0), pair(ratom(1), ratom(2)))</code> returns the length of the shortest path between reactant
		atoms matching maps <code>1</code> and <code>2</code> in the reaction equation (<a href="#note1">see note 1</a>)
		<code>shortestPath(product(1), pair(patom(2), patom(3)))</code> returns the length of the shortest path between product
		atoms matching maps <code>2</code> and <code>3</code> in the reaction equation (<a href="#note1">see note 1</a>)]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="connected" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="connected"></Param>
			<ReturnType>boolean</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[checks if two atoms are connected]]>
			</Description>
			<ReturnValue>
				<![CDATA[<code>true</code> if the two atoms belong to the same connected component, <code>false</code> otherwise]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the (1-based) atom indexes of the two atoms in a string: "index1-index2" (e.g. '2-3')
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>connected('2-3')</code> and <code>connected(pair(1, 2))</code> both return <code>true</code> if atoms <code>1</code>
		and <code>2</code> are in the same connected component of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>connected(reactant(0), pair(ratom(1), ratom(2)))</code> returns <code>true</code> if reactant atoms matching maps <code>1</code>
		and <code>2</code> in the reaction equation are connected in the corresponding reactant molecule (<a href="#note1">see note 1</a>)
		<code>connected(product(1), pair(patom(2), patom(3)))</code> returns <code>true</code> if product atoms matching maps <code>2</code>
		and <code>3</code> in the reaction equation are connected in the corresponding product molecule (<a href="#note1">see note 1</a>)]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="connectedGraph" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="connectedGraph"></Param>
			<ReturnType>boolean</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[checks whether the molecule graph is connected]]>
			</Description>
			<ReturnValue>
				<![CDATA[<code>true</code> if the molecule graph is connected, <code>false</code> otherwise]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>connectedGraph()</code> returns <code>true</code> if the input molecule graph is connected]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>connectedGraph(reactant(0))</code> returns <code>true</code> if the first reactant is connected <code>connectedGraph(product(1))</code>
		returns <code>true</code> if the second product is connected]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="bondType" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="bondType"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[returns the bond type between two atoms]]>
			</Description>
			<ReturnValue>
				<![CDATA[the bond type between two atoms, <code>-1</code> if there is no bond between the two atoms]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the (1-based) atom indexes of the two atoms in a string: "index1-index2" (e.g. '2-3')
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>bondType('2-3')</code> and <code>bondType(bond(1, 2))</code> both return the bond type between atoms <code>1</code>
		and <code>2</code> in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>bondType(reactant(0), bond(ratom(1), ratom(2)))</code> returns the bond type between reactant atoms matching maps <code>1</code>
		and <code>2</code> in the reaction equation (<a href="#note1">see note 1</a>)<code>bondType(product(1), bond(patom(2), patom(3)))</code>
		returns the bond type between product atoms matching maps <code>2</code> and <code>3</code> in the reaction equation
		(<a href="#note1">see note 1</a>)]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="chainBond" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="chainBond"></Param>
			<ReturnType>boolean</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[checks if two atoms are connected by a chain bond]]>
			</Description>
			<ReturnValue>
				<![CDATA[<code>true</code> if the two atoms are connected by a chain bond, <code>false</code> otherwise]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the (1-based) atom indexes of the two atoms in a string: "index1-index2" (e.g. '2-3')
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>chainBond('2-3')</code> and <code>chainBond(bond(1, 2))</code> both return <code>true</code> if atoms <code>1</code>
		and <code>2</code> are connected by a chain bond the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>chainBond(reactant(0), bond(ratom(1), ratom(2)))</code> returns <code>true</code> if reactant atoms matching maps <code>1</code>
		and <code>2</code> in the reaction equation are connected by a chain bond in the corresponding reactant molecule (<a href="#note1">see note 1</a>)
		<code>chainBond(product(1), bond(patom(2), patom(3)))</code> returns <code>true</code> if product atoms matching maps <code>2</code> and <code>3</code> in the
		reaction equation are connected by a chain bond in the corresponding product molecule (<a href="#note1">see note 1</a>)]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="ringBond" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="ringBond"></Param>
			<ReturnType>boolean</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[checks if two atoms are connected by a ring bond]]>
			</Description>
			<ReturnValue>
				<![CDATA[<code>true</code> if the two atoms are connected by a ring bond, <code>false</code> otherwise]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the (1-based) atom indexes of the two atoms in a string: "index1-index2" (e.g. '2-3')
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>ringBond('2-3')</code> and <code>ringBond(bond(1, 2))</code> both return <code>true</code> if atoms <code>1</code>
		and <code>2</code> are connected by a ring bond the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>ringBond(reactant(0), bond(ratom(1), ratom(2)))</code> returns <code>true</code> if reactant atoms matching maps <code>1</code>
		and <code>2</code> in the reaction equation are connected by a ring bond in the corresponding reactant molecule (<a href="#note1">see note 1</a>)
		<code>ringBond(product(1), bond(patom(2), patom(3)))</code> returns <code>true</code> if product atoms matching maps <code>2</code> and <code>3</code>
		in the reaction equation are connected by a ring bond in the corresponding product molecule (<a href="#note1">see note 1</a>)]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="rotatableBond" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="rotatableBond"></Param>
			<ReturnType>boolean</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[checks if two atoms are connected by a rotatable bond]]>
			</Description>
			<ReturnValue>
				<![CDATA[<code>true</code> if the two atoms are connected by a rotatable bond, <code>false</code> otherwise]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the (1-based) atom indexes of the two atoms in a string: "index1-index2" (e.g. '2-3')
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>rotatableBond('2-3')</code> and <code>rotatableBond(bond(1, 2))</code> both return <code>true</code> if
		atoms <code>1</code> and <code>2</code> are connected by a rotatable bond the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>rotatableBond(reactant(0), bond(ratom(1), ratom(2)))</code> returns <code>true</code> if reactant atoms matching maps
		<code>1</code> and <code>2</code> in the reaction equation are connected by a rotatable bond in the corresponding
		reactant molecule (<a href="#note1">see note 1</a>)
		<code>rotatableBond(product(1), bond(patom(2), patom(3)))</code> returns <code>true</code> if product atoms
		matching maps <code>2</code> and <code>3</code> in the reaction equation are connected by a rotatable bond in the
		corresponding product molecule (<a href="#note1">see note 1</a>)]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="ringCountOfAtom" Class="chemaxon.marvin.calculations.TopologyAnalyserPlugin" JAR="TopologyAnalyserPlugin.jar">
			<Param Name="type" Value="ringCountOfAtom"></Param>
			<ReturnType>int</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the number of rings passing through an atom]]>
			</Description>
			<ReturnValue>
				<![CDATA[the number of rings passing through an atom]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>ringCountOfAtom(2)</code> returns the number of rings passsing through atom <code>2</code> of the input
		molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>ringCountOfAtom(ratom(2))</code> returns the number of rings passsing through the reactant atom matching
		map <code>2</code> in the reaction equation <code>ringCountOfAtom(patom(1))</code> returns the number of rings passsing
		through the product atom matching map <code>1</code> in the reaction equation]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="HBDA" Class="chemaxon.marvin.calculations.HBDAPlugin" JAR="HBDAPlugin.jar"></Plugin>
		<Plugin ID="acceptor" Class="chemaxon.marvin.calculations.HBDAPlugin" JAR="HBDAPlugin.jar">
			<Alias>acc</Alias>
			<Param Name="type" Value="acc"></Param>
			<ReturnType>int</ReturnType>
			<Category>HBDA</Category>
			<Description>
				<![CDATA[calculates atomic hydrogen bond acceptor multiplicity]]>
			</Description>
			<ReturnValue>
				<![CDATA[the atomic hydrogen bond acceptor multiplicity]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object</li>
<li>the major microspecies pH (takes the input molecule itself if omitted)</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>acceptor(2)</code> returns the hydrogen bond acceptor multiplicity on atom <code>2</code> of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>acceptor(ratom(2))</code> returns the hydrogen bond acceptor multiplicity on the reactant atom matching map
		<code>2</code> in the reaction equation]]>
			</ReactionContextExample>
		</Plugin>
		<!--
		<Plugin ID="acc" Class="chemaxon.marvin.calculations.HBDAPlugin" JAR="HBDAPlugin.jar">
			<Param Name="type" Value="acc"></Param>
		</Plugin>
		-->
		<!--
		<Plugin ID="don" Class="chemaxon.marvin.calculations.HBDAPlugin" JAR="HBDAPlugin.jar">
			<Param Name="type" Value="don"></Param>
		</Plugin>
		-->
		<Plugin ID="donor" Class="chemaxon.marvin.calculations.HBDAPlugin" JAR="HBDAPlugin.jar">
			<Alias>don</Alias>
			<Param Name="type" Value="don"></Param>
			<ReturnType>int</ReturnType>
			<Category>HBDA</Category>
			<Description>
				<![CDATA[calculates atomic hydrogen bond donor multiplicity]]>
			</Description>
			<ReturnValue>
				<![CDATA[the atomic hydrogen bond donor multiplicity]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object</li>
<li>the major microspecies pH (takes the input molecule itself if omitted)</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>donor(1, "7.4")</code> returns the hydrogen bond donor multiplicity on atom <code>1</code> of the major microspecies
		at pH <code>7.4</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[returns the hydrogen bond donor multiplicity on the product atom matching map <code>3</code> in the reaction equation,
		taking the major microspecies of the corresponding product at pH <code>7.4</code>]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="acceptorSiteCount" Class="chemaxon.marvin.calculations.HBDAPlugin" JAR="HBDAPlugin.jar">
			<Alias>accSiteCount</Alias>
			<Param Name="type" Value="accsitecount"></Param>
			<ReturnType>int</ReturnType>
			<Category>HBDA</Category>
			<Description>
				<![CDATA[calculates molecular hydrogen bond acceptor multiplicity (the sum of atomic multiplicities)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the molecular hydrogen bond acceptor multiplicity]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>acceptorSiteCount()</code> returns the hydrogen bond acceptor multiplicity of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>acceptorSiteCount(reactant(0))</code> returns the hydrogen bond acceptor multiplicity of the first reactant]]>
			</ReactionContextExample>
		</Plugin>
		<!--
		<Plugin ID="accSiteCount" Class="chemaxon.marvin.calculations.HBDAPlugin" JAR="HBDAPlugin.jar">
			<Param Name="type" Value="accsitecount"></Param>
		</Plugin>
		-->
		<Plugin ID="donorSiteCount" Class="chemaxon.marvin.calculations.HBDAPlugin" JAR="HBDAPlugin.jar">
			<Alias>donSiteCount</Alias>
			<Param Name="type" Value="donsitecount"></Param>
			<ReturnType>int</ReturnType>
			<Category>HBDA</Category>
			<Description>
				<![CDATA[calculates molecular hydrogen bond acceptor / donor multiplicity (the sum of atomic multiplicities)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the molecular hydrogen bond acceptor / donor multiplicity]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>donorSiteCount("7.4")</code> returns the hydrogen bond donor multiplicity of the major microspecies
		at pH <code>7.4</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>donorSiteCount(product(1), "7.4")</code> returns the hydrogen bond donor multiplicity of the major microspecies
		of the first product taken at pH <code>7.4</code>]]>
			</ReactionContextExample>
		</Plugin>
		<!--
		<Plugin ID="donSiteCount" Class="chemaxon.marvin.calculations.HBDAPlugin" JAR="HBDAPlugin.jar">
			<Param Name="type" Value="donsitecount"></Param>
		</Plugin>
		-->
		<Plugin ID="acceptorCount" Class="chemaxon.marvin.calculations.HBDAPlugin" JAR="HBDAPlugin.jar">
			<Param Name="type" Value="acceptorcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>HBDA</Category>
			<Description>
				<![CDATA[calculates molecular hydrogen bond acceptor count (the number of acceptor atoms)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the molecular hydrogen bond acceptor count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>acceptorCount()</code> returns the number of hydrogen bond acceptor atoms in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>acceptorCount(reactant(0))</code> returns the number of hydrogen bond acceptor atoms in the first reactant]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="donorCount" Class="chemaxon.marvin.calculations.HBDAPlugin" JAR="HBDAPlugin.jar">
			<Param Name="type" Value="donorcount"></Param>
			<ReturnType>int</ReturnType>
			<Category>HBDA</Category>
			<Description>
				<![CDATA[calculates molecular hydrogen bond donor count (the number of donor atoms)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the molecular hydrogen bond donor count]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the major microspecies pH (takes the input molecule itself if omitted)</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>donorCount("7.4")</code> returns the number of hydrogen bond donor atoms in the major microspecies
		at pH <code>7.4</code>]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>donorCount(product(1), "7.4")</code> returns the number of hydrogen bond donor atoms in the major microspecies
		of the first product taken at pH <code>7.4</code>]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="refractivity" Class="chemaxon.marvin.calculations.RefractivityPlugin" JAR="RefractivityPlugin.jar">
			<Alias>refrac</Alias>
			<ReturnType>double</ReturnType>
			<Category>Refractivity</Category>
			<Description>
				<![CDATA[calculates molar refractivity]]>
			</Description>
			<ReturnValue>
				<![CDATA[the refractivity value]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>refractivity()</code> returns the molar refractivity of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>refractivity(reactant(1))</code> returns the molar refractivity of the second reactant]]>
			</ReactionContextExample>
		</Plugin>
		<!--
		<Plugin ID="refrac" Class="chemaxon.marvin.calculations.RefractivityPlugin" JAR="RefractivityPlugin.jar"></Plugin>
		-->
		<Plugin ID="refractivityIncrements" Class="chemaxon.marvin.calculations.RefractivityPlugin" JAR="RefractivityPlugin.jar">
			<Alias>refraci</Alias>
			<Param Name="type" Value="increments"></Param>
			<ReturnType>double/double[]</ReturnType>
			<Category>Refractivity</Category>
			<Description>
				<![CDATA[calculates atomic refractivity increments]]>
			</Description>
			<ReturnValue>
				<![CDATA[the atomic refractivity increment]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>refractivityIncrements(2)</code> returns the refractivity increment on atom <code>2</code> of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>refractivityIncrements(ratom(1))</code> returns the refractivity increment on the reactant atom matching map <code>1</code>
		in the reaction equation]]>
			</ReactionContextExample>
		</Plugin>
		<!--
		<Plugin ID="refraci" Class="chemaxon.marvin.calculations.RefractivityPlugin" JAR="RefractivityPlugin.jar">
			<Param Name="type" Value="increments"></Param>
		</Plugin>
		-->
		<Plugin ID="conformer" Class="chemaxon.marvin.calculations.ConformerPlugin" JAR="ConformerPlugin.jar">
			<ReturnType>Molecule</ReturnType>
			<Category>Conformation</Category>
			<Description>
				<![CDATA[calculates a conformer of the molecule]]>
			</Description>
			<ReturnValue>
				<![CDATA[the conformer]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the conformer index (0-based)
			</li></ul>]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>conformer(0)</code> returns the first conformer of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>conformer(reactant(0), 1)</code> returns the second conformer of the first reactant]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="conformers" Class="chemaxon.marvin.calculations.ConformerPlugin" JAR="ConformerPlugin.jar">
			<Param Name="type" Value="structures"></Param>
			<ReturnType>Molecule[]</ReturnType>
			<Category>Conformation</Category>
			<Description>
				<![CDATA[calculates conformers of the molecule (maximum number of conformers to be calculated can be set, default:
		100)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the conformer array]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>conformers()</code> returns conformers of the input molecule in an array]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>conformers(reactant(0))</code> returns conformers of the first reactant in an array]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="conformerCount" Class="chemaxon.marvin.calculations.ConformerPlugin" JAR="ConformerPlugin.jar">
			<Param Name="type" Value="count"></Param>
			<ReturnType>int</ReturnType>
			<Category>Conformation</Category>
			<Description>
				<![CDATA[returns the number of calculated conformers]]>
			</Description>
			<ReturnValue>
				<![CDATA[the number of calculated conformers]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>conformerCount()</code> returns the number of calculated conformers of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>conformerCount(reactant(0))</code> returns the number of calculated conformers of the first reactant]]>
			</ReactionContextExample>
		</Plugin>
		<!--
		<Plugin ID="lowestEnergyConformer" Class="chemaxon.marvin.calculations.ConformerPlugin" JAR="ConformerPlugin.jar">
			<Param Name="type" Value="structure"></Param>
		</Plugin>
		-->
		<Plugin ID="lowestEnergyConformer" Class="chemaxon.marvin.calculations.ConformerPlugin" JAR="ConformerPlugin.jar">
			<Alias>leconformer</Alias>
			<Param Name="type" Value="structure"></Param>
			<ReturnType>Molecule</ReturnType>
			<Category>Conformation</Category>
			<Description>
				<![CDATA[calculates the lowest energy conformer of the molecule]]>
			</Description>
			<ReturnValue>
				<![CDATA[the lowest energy conformer]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>lowestEnergyConformer()</code> returns the lowest energy conformer of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>lowestEnergyConformer(reactant(0))</code> returns the lowest energy conformer of the first reactant
		<code>lowestEnergyConformer(product(1))</code> returns the lowest energy conformer of the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="mmff94OptimizedStructure" Class="chemaxon.marvin.calculations.ConformerPlugin" JAR="ConformerPlugin.jar">
			<Param Name="type" Value="structure"></Param>
			<Param Name="leconformercalculation" Value="true"></Param>
			<Param Name="mmff94optimization" Value="true"></Param>
			<Param Name="prehydrogenize" Value="true"></Param>
			<ReturnType>Molecule</ReturnType>
			<Category>Conformation</Category>
			<Description>
				<![CDATA[calculates the MMFF94 optimized lowest energy conformer]]>
			</Description>
			<ReturnValue>
				<![CDATA[the MMFF94 optimized lowest energy conformer]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>mmff94OptimizedStructure()</code> returns the MMFF94 optimized lowest energy conformer of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>mmff94OptimizedStructure(reactant(0))</code> returns the MMFF94 optimized lowest energy conformer of the first reactant
		<code>mmff94OptimizedStructure(product(1))</code> returns the MMFF94 optimized lowest energy conformer of the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="hasValidConformer" Class="chemaxon.marvin.calculations.ConformerPlugin" JAR="ConformerPlugin.jar">
			<Param Name="type" Value="hasvalidconformer"></Param>
			<ReturnType>boolean</ReturnType>
			<Category>Conformation</Category>
			<Description>
				<![CDATA[returns true if the input molecule exist in 3D space (has a valid conformer)]]>
			</Description>
			<ReturnValue>
				<![CDATA[true if the input molecule exist in 3D space]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>hasValidConformer()</code> returns true if the input molecule exists in 3D space (has a valid conformer)]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>hasValidConformer(reactant(0))</code> returns true the if the first reactant exists in 3D space <code>hasValidConformer(product(1))</code>
		returns true the if the second product exist in 3D space]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="stereoisomer" Class="chemaxon.marvin.calculations.StereoisomerPlugin" JAR="StereoisomerPlugin.jar">
			<Param Name="stereoisomerism" Value="both"></Param>
			<Param Name="type" Value="structure"></Param>
			<ReturnType>Molecule</ReturnType>
			<Category>Isomers</Category>
			<Description>
				<![CDATA[generates a stereoisomer of the molecule]]>
			</Description>
			<ReturnValue>
				<![CDATA[the stereoisomer]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[<ul><li>the stereoisomer index (0-based)</li></ul>]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>stereoisomer(0)</code> returns the first stereoisomer of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>stereoisomer(reactant(0), 1)</code> returns the second stereoisomer of the first reactant]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="stereoisomers" Class="chemaxon.marvin.calculations.StereoisomerPlugin" JAR="StereoisomerPlugin.jar">
			<Param Name="stereoisomerism" Value="both"></Param>
			<Param Name="type" Value="structures"></Param>
			<ReturnType>Molecule[]</ReturnType>
			<Category>Isomers</Category>
			<Description>
				<![CDATA[generates stereoisomers of the molecule (maximum number of stereoisomers to be generated can be set, default:
		all)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the stereoisomer array]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>stereoisomers()</code> returns stereoisomers of the input molecule in an array ]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>stereoisomers(reactant(0))</code> returns stereoisomers of the first reactant in an array]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="stereoisomerCount" Class="chemaxon.marvin.calculations.StereoisomerPlugin" JAR="StereoisomerPlugin.jar">
			<Param Name="stereoisomerism" Value="both"></Param>
			<Param Name="type" Value="count"></Param>
			<ReturnType>int</ReturnType>
			<Category>Isomers</Category>
			<Description>
				<![CDATA[returns the number of generated stereoisomers]]>
			</Description>
			<ReturnValue>
				<![CDATA[the number of generated stereoisomers]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>stereoisomerCount()</code> returns the number of generated stereoisomers of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>stereoisomerCount(reactant(0))</code> returns the number of calculated stereoisomers of the first
		reactant]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="doubleBondStereoisomer" Class="chemaxon.marvin.calculations.StereoisomerPlugin" JAR="StereoisomerPlugin.jar">
			<Param Name="stereoisomerism" Value="doublebond"></Param>
			<Param Name="type" Value="structure"></Param>
			<ReturnType>Molecule</ReturnType>
			<Category>Isomers</Category>
			<Description>
				<![CDATA[generates a double bond stereoisomer of the molecule]]>
			</Description>
			<ReturnValue>
				<![CDATA[the double bond stereoisomer]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the double bond stereoisomer index (0-based)</li></ul>]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>doubleBondStereoisomer(0)</code> returns the first double bond stereoisomer of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>doubleBondStereoisomer(reactant(0), 1)</code> returns the second double bond stereoisomer of the first
		reactant]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="doubleBondStereoisomers" Class="chemaxon.marvin.calculations.StereoisomerPlugin" JAR="StereoisomerPlugin.jar">
			<Param Name="stereoisomerism" Value="doublebond"></Param>
			<Param Name="type" Value="structures"></Param>
			<ReturnType>Molecule[]</ReturnType>
			<Category>Isomers</Category>
			<Description>
				<![CDATA[generates double bond stereoisomers of the molecule (maximum number of double bond stereoisomers to be
		generated can be set, default: all)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the double bond stereoisomer array]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>doubleBondStereoisomers()</code> returns double bond stereoisomers of the input molecule in an array]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>doubleBondStereoisomers(reactant(0))</code> returns double bond stereoisomers of the first reactant in an array]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="doubleBondStereoisomerCount" Class="chemaxon.marvin.calculations.StereoisomerPlugin" JAR="StereoisomerPlugin.jar">
			<Param Name="stereoisomerism" Value="doublebond"></Param>
			<Param Name="type" Value="count"></Param>
			<ReturnType>int</ReturnType>
			<Category>Isomers</Category>
			<Description>
				<![CDATA[returns the number of generated double bond stereoisomers]]>
			</Description>
			<ReturnValue>
				<![CDATA[the number of generated double bond stereoisomers]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>doubleBondStereoisomerCount()</code> returns the number of generated double bond stereoisomers of the
		input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>doubleBondStereoisomerCount(reactant(0))</code> returns the number of calculated double bond
		stereoisomers of the first reactant]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="tetrahedralStereoisomer" Class="chemaxon.marvin.calculations.StereoisomerPlugin" JAR="StereoisomerPlugin.jar">
			<Param Name="stereoisomerism" Value="tetrahedral"></Param>
			<Param Name="type" Value="structure"></Param>
			<ReturnType>Molecule</ReturnType>
			<Category>Isomers</Category>
			<Description>
				<![CDATA[generates a tetrahedral stereoisomer of the molecule]]>
			</Description>
			<ReturnValue>
				<![CDATA[the tetrahedral stereoisomer]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>tetrahedralStereoisomer(0)</code> returns the first tetrahedral stereoisomer of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>tetrahedralStereoisomer(reactant(0), 1)</code> returns the second tetrahedral stereoisomer of the first
		reactant]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="tetrahedralStereoisomers" Class="chemaxon.marvin.calculations.StereoisomerPlugin" JAR="StereoisomerPlugin.jar">
			<Param Name="stereoisomerism" Value="tetrahedral"></Param>
			<Param Name="type" Value="structures"></Param>
			<ReturnType>Molecule[]</ReturnType>
			<Category>Isomers</Category>
			<Description>
				<![CDATA[generates tetrahedral stereoisomers of the molecule (maximum number of tetrahedral stereoisomers to be
		generated can be set, default: all)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the tetrahedral stereoisomer array]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>tetrahedralStereoisomers()</code> returns tetrahedral stereoisomers of the input molecule in an array]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>tetrahedralStereoisomers(reactant(0))</code> returns tetrahedral stereoisomers of the first reactant in
		an array]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="tetrahedralStereoisomerCount" Class="chemaxon.marvin.calculations.StereoisomerPlugin" JAR="StereoisomerPlugin.jar">
			<Param Name="stereoisomerism" Value="tetrahedral"></Param>
			<Param Name="type" Value="count"></Param>
			<ReturnType>int</ReturnType>
			<Category>Isomers</Category>
			<Description>
				<![CDATA[returns the number of generated tetrahedral stereoisomers]]>
			</Description>
			<ReturnValue>
				<![CDATA[the number of generated tetrahedral stereoisomers]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>tetrahedralStereoisomerCount()</code> returns the number of generated tetrahedral stereoisomers of the
		input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>tetrahedralStereoisomerCount(reactant(0))</code> returns the number of calculated tetrahedral
		stereoisomers of the first reactant]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="dreidingEnergy" Class="chemaxon.marvin.calculations.GeometryPlugin" JAR="GeometryPlugin.jar">
			<Param Name="type" Value="dreidingenergy"></Param>
			<ReturnType>double</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[returns the Dreiding energy of the input molecule (conformer)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the dreiding energy]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>dreidingEnergy()</code> returns the dreiding energy of the input molecule (conformer)]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>dreidingEnergy(reactant(0))</code> returns the dreiding energy of the first reactant <code>dreidingEnergy(product(1))</code>
		returns the dreiding energy of the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="mmff94Energy" Class="chemaxon.marvin.calculations.GeometryPlugin" JAR="GeometryPlugin.jar">
			<Param Name="type" Value="mmff94energy"></Param>
			<ReturnType>double</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[returns the MMFF94 energy of the input molecule (conformer)]]>
			</Description>
			<ReturnValue>
				<![CDATA[the MMFF94 energy]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>mmff94Energy()</code> returns the MMFF94 energy of the input molecule (conformer)]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>mmff94Energy(reactant(0))</code> returns the MMFF94 energy of the first reactant <code>mmff94Energy(product(1))</code>
		returns the MMFF94 energy of the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="minimalProjectionArea" Class="chemaxon.marvin.calculations.GeometryPlugin" JAR="GeometryPlugin.jar">
			<Param Name="type" Value="minimalprojectionarea"></Param>
			<ReturnType>double</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[returns the minimal projection area]]>
			</Description>
			<ReturnValue>
				<![CDATA[the minimal projection area]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>minimalProjectionArea()</code> returns the minimal projection area]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>minimalProjectionArea((reactant(0))</code> returns the minimal projection area of the first reactant
		<code>minimalProjectionArea(product(1))</code> returns the minimal projection area of the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="maximalProjectionArea" Class="chemaxon.marvin.calculations.GeometryPlugin" JAR="GeometryPlugin.jar">
			<Param Name="type" Value="maximalprojectionarea"></Param>
			<ReturnType>double</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[returns the maximal projection area]]>
			</Description>
			<ReturnValue>
				<![CDATA[the maximal projection area]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>maximalProjectionArea()</code> returns the maximal projection area]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>maximalProjectionArea((reactant(0))</code> returns the maximal projection area of the first reactant
		<code>maximalProjectionArea(product(1))</code> returns the maximal projection area of the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="minimalProjectionRadius" Class="chemaxon.marvin.calculations.GeometryPlugin" JAR="GeometryPlugin.jar">
			<Param Name="type" Value="minimalprojectionradius"></Param>
			<ReturnType>double</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[returns the minimal projection radius]]>
			</Description>
			<ReturnValue>
				<![CDATA[the minimal projection radius]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>minimalProjectionRadius()</code> returns the minimal projection radius]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>minimalProjectionRadius((reactant(0))</code> returns the minimal projection radius of the first reactant
		<code>minimalProjectionRadius(product(1))</code> returns the minimal projection radius of the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="maximalProjectionRadius" Class="chemaxon.marvin.calculations.GeometryPlugin" JAR="GeometryPlugin.jar">
			<Param Name="type" Value="maximalprojectionradius"></Param>
			<ReturnType>double</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[returns the maximal projection radius]]>
			</Description>
			<ReturnValue>
				<![CDATA[the maximal projection radius]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>maximalProjectionRadius()</code> returns the maximal projection radius]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>maximalProjectionRadius((reactant(0))</code> returns the maximal projection radius of the first reactant
		<code>maximalProjectionRadius(product(1))</code> returns the maximal projection radius of the second product]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="distance" Class="chemaxon.marvin.calculations.GeometryPlugin" JAR="GeometryPlugin.jar">
			<Param Name="type" Value="distance"></Param>
			<ReturnType>double</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the distance between two atoms]]>
			</Description>
			<ReturnValue>
				<![CDATA[the distance between two atoms]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the (1-based) atom indexes of the two atoms in a string: "index1-index2" (e.g. '2-3')
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>distance('1-2')</code> and <code>distance(pair(0, 1))</code> both return the distance between atoms <code>1</code>
		and <code>2</code> in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>distance(reactant(0), pair(ratom(1), ratom(2)))</code> returns the distance between reactant atoms matching maps <code>1</code>
		and <code>2</code> in the reaction equation (<a href="#note1">see note 1</a>)
		<code>distance(product(1), pair(patom(2), patom(3)))</code> returns the distance between product atoms matching maps <code>2</code> and <code>3</code>
		in the reaction equation (<a href="#note1">see note 1</a>)]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="angle" Class="chemaxon.marvin.calculations.GeometryPlugin" JAR="GeometryPlugin.jar">
			<Param Name="type" Value="angle"></Param>
			<ReturnType>double</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the angle between three atoms]]>
			</Description>
			<ReturnValue>
				<![CDATA[the angle between three atoms]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the (1-based) atom indexes of the three atoms in a string: "index1-index2-index3" (e.g. '2-3-5')
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>angle('1-2-3')</code> and <code>angle(atoms(0, 1, 2))</code> both return the angle between atoms <code>1</code>,
		<code>2</code> and <code>3</code> in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>angle(reactant(0), atoms(ratom(1), ratom(2), ratom(3)))</code> returns the angle between reactant atoms matching maps
		<code>1</code>, <code>2</code> and <code>3</code> in the reaction equation (<a href="#note1">see note 1</a>)
		<code>angle(product(1), atoms(patom(2), patom(3), patom(4)))</code> returns the angle between product atoms matching maps <code>2</code>,
		<code>3</code> and <code>4</code> in the reaction equation (<a href="#note1">see note 1</a>)]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="dihedral" Class="chemaxon.marvin.calculations.GeometryPlugin" JAR="GeometryPlugin.jar">
			<Param Name="type" Value="dihedral"></Param>
			<ReturnType>double</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the dihedral of four atoms]]>
			</Description>
			<ReturnValue>
				<![CDATA[the dihedral of four atoms]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the (1-based) atom indexes of the four atoms in a string: "index1-index2-index3-index4" (e.g. '2-3-7-4')
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>dihedral('1-2-3-4')</code> and <code>dihedral(atoms(0, 1, 2, 3))</code> both return the dihedral of atoms <code>1</code>,
		<code>2</code>, <code>3</code> and <code>4</code> in the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>dihedral(reactant(0), atoms(ratom(1), ratom(2), ratom(3), ratom(4)))</code> returns the dihedral of reactant atoms
		matching maps <code>1</code>, <code>2</code>, <code>3</code> and <code>4</code> in the reaction equation (<a href="#note1">see note 1</a>)
		<code>dihedral(product(1), atoms(patom(2), patom(3), patom(5), patom(6)))</code> returns the dihedral of product atoms matching maps <code>2</code>,
		<code>3</code>, <code>5</code> and <code>6</code> in the reaction equation (<a href="#note1">see note 1</a>)]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="stericHindrance" Class="chemaxon.marvin.calculations.GeometryPlugin" JAR="GeometryPlugin.jar">
			<Param Name="type" Value="hindrance"></Param>
			<ReturnType>double/double[]</ReturnType>
			<Category>Geometry</Category>
			<Description>
				<![CDATA[calculates the steric hindrance of an atom]]>
			</Description>
			<ReturnValue>
				<![CDATA[the steric hindrance of an atom]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[
<ul>
<li>the atom index / MolAtom object
		</li></ul>
]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>stericHindrance(2)</code> returns the steric hindrance of atom <code>2</code> of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[<code>stericHindrance(ratom(2))</code> returns the steric hindrance of the reactant atom matching map <code>2</code>
		in the reaction equation <code>stericHindrance(patom(1))</code> returns the steric hindrance of the product atom
		matching map <code>1</code> in the reaction equation]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="name" Class="chemaxon.marvin.calculations.IUPACNamingPlugin" JAR="IUPACNamingPlugin.jar">
			<ReturnType>String</ReturnType>
			<Category>Name</Category>
			<Description>
				<![CDATA[returns the preferred IUPAC name of the molecule]]>
			</Description>
			<ReturnValue>
				<![CDATA[the preferred IUPAC name of the molecule]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>name()</code> returns the preferred IUPAC name of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[N / A (reaction rules are numerical)]]>
			</ReactionContextExample>
		</Plugin>
		<Plugin ID="traditionalName" Class="chemaxon.marvin.calculations.IUPACNamingPlugin" JAR="IUPACNamingPlugin.jar">
			<Param Name="type" Value="traditional"></Param>
			<ReturnType>String</ReturnType>
			<Category>Name</Category>
			<Description>
				<![CDATA[returns the traditional name of the molecule]]>
			</Description>
			<ReturnValue>
				<![CDATA[the traditional name of the molecule]]>
			</ReturnValue>
			<Parameters>
				<![CDATA[-]]>
			</Parameters>
			<MoleculeContextExample>
				<![CDATA[<code>traditionalName()</code> returns the traditional name of the input molecule]]>
			</MoleculeContextExample>
			<ReactionContextExample>
				<![CDATA[N / A (reaction rules are numerical)]]>
			</ReactionContextExample>
		</Plugin>
        <Plugin ID="bmf" Class="chemaxon.marvin.calculations.StructuralFrameworksPlugin" JAR="StructuralFrameworksPlugin.jar">
            <Param Name="type" Value="bmf"/>
            <ReturnType>Molecule</ReturnType>
            <Category>StructuralFrameworks</Category>
			<Description><![CDATA[Returns the Bemis-Murcko framework of the input structure]]></Description>
            <ReturnValue><![CDATA[BMF of the input structure]]></ReturnValue>
			<Parameters><![CDATA[-]]></Parameters>
			<MoleculeContextExample><![CDATA[<code>bmf()</code> returns the Bemis-Murcko framework of the input molecule]]></MoleculeContextExample>
			<ReactionContextExample><![CDATA[<code>bmf(reactant(0))</code> returns the Bemis-Murcko framework of the first reactant]]></ReactionContextExample>
        </Plugin>
        <Plugin ID="bmfl" Class="chemaxon.marvin.calculations.StructuralFrameworksPlugin" JAR="StructuralFrameworksPlugin.jar">
	    <Alias>bemisMurckoFrameworkLoose</Alias>
            <Param Name="type" Value="bmfl"/>
            <ReturnType>Molecule</ReturnType>
            <Category>StructuralFrameworks</Category>
  	    <Description><![CDATA[Returns the Bemis-Murcko loose framework of the input structure. Calculated by removing side chains. Exocyclic non single bonded atoms are kept. Remaining atom and bond types are not changed.]]></Description>
            <ReturnValue><![CDATA[Loose BMF of the input structure]]></ReturnValue>
			<Parameters><![CDATA[-]]></Parameters>
			<MoleculeContextExample><![CDATA[<code>bmfl()</code> returns the loose Bemis-Murcko framework of the input molecule]]></MoleculeContextExample>
			<ReactionContextExample><![CDATA[<code>bmfl(reactant(0))</code> returns the loose Bemis-Murcko framework of the first reactant]]></ReactionContextExample>
        </Plugin>
        <Plugin ID="bmflp" Class="chemaxon.marvin.calculations.StructuralFrameworksPlugin" JAR="StructuralFrameworksPlugin.jar">
	    <Alias>bemisMurckoFrameworkLoosePruned</Alias>
            <Param Name="type" Value="bmfl"/>
	    <Param Name="pruneout" Value="true"/>
            <ReturnType>Molecule</ReturnType>
            <Category>StructuralFrameworks</Category>
  	    <Description><![CDATA[Returns the Bemis-Murcko loose framework of the input structure. Calculated by removing side cains. Atom and bond types are generalized by replacing all atoms with carbons and setting all bond types to single. Exocyclic non single bonded atoms are kept as single bonded carbons.]]></Description>
            <ReturnValue><![CDATA[Generalized loose BMF of the input structure]]></ReturnValue>
			<Parameters><![CDATA[-]]></Parameters>
			<MoleculeContextExample><![CDATA[<code>bmflp()</code> returns the generalized loose Bemis-Murcko framework of the input molecule]]></MoleculeContextExample>
			<ReactionContextExample><![CDATA[<code>bmflp(reactant(0))</code> returns the generalized loose Bemis-Murcko framework of the first reactant]]></ReactionContextExample>
        </Plugin>
	</Plugins>


	<!-- JChem only BEGIN -->
	<Matching ID="match" Class="chemaxon.jep.function.Match">
		<UserParam Name="target" Type="Molecule" />
		<UserParam Name="target atom" Type="MolAtom/int" />
		<UserParam Name="query" Type="Molecule/String/Collection" Mandatory="true" />
		<UserParam Name="query atom map" Type="int" Multiple="true" />
		<ReturnType>boolean</ReturnType>
		<Category>Match</Category>
		<Description>
			<![CDATA[performs substructure search and optionally checks for atom matching]]>
		</Description>
		<ReturnValue>
			<![CDATA[<code>true</code> if matching substructure found, <code>false</code> otherwise]]>
		</ReturnValue>
		<Parameters>
			<![CDATA[
		<ul>
			<li>target atom index / MolAtom object (optional)</li>
			<li>query Molecule object / SMARTS string</li>
			<li>query atom map(s) (optional)</li>
		</ul>
		The function returns <code>true</code> if the query structure is found in the target molecule, the hit is required
		to include the target atom if specified, furthermore if query atom map(s) are specified then these mapped atoms should
		match the target atom.]]>
		</Parameters>
		<MoleculeContextExample>
			<![CDATA[<code>match("[#8]C=O")</code> performs substructure search without atom matching requirement, the target is the
		input molecule, the query is the carboxylic group given in the string parameter <code>match(6, "[#8][C:1]=O", 1)</code>
		performs substructure search, checks if target atom <code>6</code> matches the carbon (atom with map <code>1</code>)
		of the carboxylic group query <code>match(6, "[#8:1]C=[O:2]", 1, 2)</code> performs substructure search, checks if
		target atom <code>6</code> of the input molecule is a carboxylic oxygen]]>
		</MoleculeContextExample>
		<ReactionContextExample>
			<![CDATA[<code>match(reactant(0), "[#8]C=O")</code> performs substructure search without atom matching requirement, the
		target is the first reactant, the query is the carboxylic group given in the string parameter <code>match(patom(2), "[#8]C=O")</code>
		performs substructure search, checks if product atom matching map <code>2</code> in the reaction equation matches any
		atom of the carboxylic group query <code>match(ratom(1), "[#8:1]C=[O:2]", 1, 2)</code> performs substructure search,
		checks if reactant atom matching map <code>1</code> in the reaction equation is a carboxylic oxygen]]>
		</ReactionContextExample>
	</Matching>
	<Matching ID="matchCount" Class="chemaxon.jep.function.Match" Type="count">
		<UserParam Name="target" Type="Molecule" />
		<UserParam Name="target atom" Type="MolAtom/int" />
		<UserParam Name="query" Type="Molecule/String/Collection" Mandatory="true" />
		<UserParam Name="query atom map" Type="int" Multiple="true" />
		<ReturnType>int</ReturnType>
		<Category>Match</Category>
		<Description>
			<![CDATA[performs substructure search and optionally checks for atom matching, counts search hits]]>
		</Description>
		<ReturnValue>
			<![CDATA[the number of search hits]]>
		</ReturnValue>
		<Parameters>
			<![CDATA[
		<ul>
			<li>target atom index / MolAtom object (optional)</li>
			<li>query Molecule object / SMARTS string</li>
			<li>query atom map(s) (optional)</li>
		</ul>
		The function returns the number of query structures found in the target molecule, the hit is required to include
		the target atom if specified, furthermore if query atom map(s) are specified then these mapped atoms should match the
		target atom.]]>
		</Parameters>
		<MoleculeContextExample>
			<![CDATA[<code>matchCount("[#8]C=O")</code> counts search hits without atom matching requirement, the target is the
		input molecule, the query is the carboxylic group given in the string parameter <code>matchCount(6, "[#8]C=O")</code>
		counts search hits with target atom <code>6</code> matching any atom in a carboxylic group <code>matchCount(6, "[#8:1]C=[O:2]", 1, 2)</code>
		counts search hits with target atom <code>6</code> of the input molecule being a carboxylic oxygen]]>
		</MoleculeContextExample>
		<ReactionContextExample>
			<![CDATA[<code>matchCount(reactant(0), "[#8]C=O")</code> counts search hits without atom matching requirement, the
		target is the first reactant, the query is the carboxylic group given in the string parameter <code>matchCount(patom(2), "[#8]C=O")</code>
		counts search hits, checks if product atom matching map <code>2</code> in the reaction equation matches any atom of
		the carboxylic group query <code>matchCount(ratom(1), "[#8:1]C=[O:2]", 1, 2)</code> counts search hits with reactant
		atom matching map <code>1</code> in the reaction equation being a carboxylic oxygen]]>
		</ReactionContextExample>
	</Matching>
	<Matching ID="disjointMatchCount" Class="chemaxon.jep.function.Match" Type="disjointcount">
		<UserParam Name="target" Type="Molecule" />
		<UserParam Name="target atom" Type="MolAtom/int" />
		<UserParam Name="query" Type="Molecule/String/Collection" Mandatory="true" />
		<UserParam Name="query atom map" Type="int" Multiple="true" />
		<ReturnType>int</ReturnType>
		<Category>Match</Category>
		<Description>
			<![CDATA[performs substructure search, returns the maximal number of pairwise disjoint search hits]]>
		</Description>
		<ReturnValue>
			<![CDATA[the maximal number of pairwise disjoint search hits]]>
		</ReturnValue>
		<Parameters>
			<![CDATA[
		<ul>
			<li>target atom index / MolAtom object (optional)</li>
			<li>query Molecule object / SMARTS string</li>
			<li>query atom map(s) (optional)</li>
		</ul>
		The function returns the maximal number of pairwise disjoint query structures found in the target molecule.
        <br/><b>Warning:</b> if the target atom index and optionally query atom maps are specified then the return value
        can only be <code>0</code> or <code>1</code>, therefore the result is similar to the result of the
        <a href="#matchdesc">match</a> function.]]>
		</Parameters>
		<MoleculeContextExample>
			<![CDATA[<code>disjointMatchCount("[#8]C=O")</code> counts the maximal number of pairwise disjoint carboxylic groups in the input molecule]]>
		</MoleculeContextExample>
		<ReactionContextExample>
			<![CDATA[<code>disjointMatchCount(reactant(0), "[#8]C=O")</code> counts the maximal number of pairwise disjoint carboxylic groups in the first reactant]]>
		</ReactionContextExample>
	</Matching>
		<Matching ID="matchFirst" Class="chemaxon.jep.function.Match" Type="matchfirst">
		<UserParam Name="target" Type="Molecule" />
		<UserParam Name="target atom" Type="MolAtom/int" />
		<UserParam Name="query" Type="Molecule/String/Collection" Mandatory="true" />
		<UserParam Name="query atom map" Type="int" Multiple="true" />
		<ReturnType>int</ReturnType>
		<Category>Match</Category>
		<Description>
			<![CDATA[performs substructure search and optionally checks for atom matching]]>
		</Description>
		<ReturnValue>
			<![CDATA[index of the first matching substructure (1-based indexing)]]>
		</ReturnValue>
		<Parameters>
			<![CDATA[
		<ul>
			<li>target atom index / MolAtom object (optional)</li>
			<li>query Molecule objects / SMARTS strings in collection (e.g. {amine,amide,"[#8][C:1]=O"})</li>
			<li>query atom map(s) (optional)</li>
		</ul>
		The function returns the index of the first matching query structure found in the target molecule, the hit is required
		to include the target atom if specified, furthermore if query atom map(s) are specified then these mapped atoms should
		match the target atom.]]>
		</Parameters>
		<MoleculeContextExample>
			<![CDATA[<code>matchFirst({amine,amide,alcohol})</code> performs substructure search without atom matching requirement, 
		the target is the input molecule, the queries are the amine, amide, and alcohol groups given in the string parameter. Function
		returns <code>1</code> if there is an amine group in the input molecule, <code>2</code> if there is no amine, but there is an amide
		group in the input molecule, and returns <code>3</code> if there is no amine or amide group, but there is an alcohol group in the 
		input molecule. Returns <code>0</code> if none of the listed groups is found in input molecule.
		<code>matchFirst(6, {"[#8][C:1]=O","[NX3:2][CX3:1]=[OX1:3]"}, 1)</code> performs substructure search, checks if target 
		atom <code>6</code> matches the carbon (atom with map <code>1</code>) of the carboxylic or amide group in query. Returns the index 
		of the query if match found, <code>0</code> otherwise.
		<code>matchFirst(6, {"[#8:1]C=[O:2]","[#6][OX2:1][CX3](=[O:2])[#6]"}, 1, 2)</code> performs substructure search, checks if
		target atom <code>6</code> of the input molecule is a carboxylic oxygen or an oxigen in an ester group. Returns the index 
		of the query if match found, <code>0</code> otherwise.]]>
		</MoleculeContextExample>
		<ReactionContextExample>
			<![CDATA[<code>matchFirst(reactant(0), {amine,amide,alcohol})</code> performs substructure search without atom matching requirement, 
		the target is the first reactant, the queries are the amine, amide, and alcohol groups given in the string parameter. Function
		returns <code>1</code> if there is an amine group in the first reactant, <code>2</code> if there is no amine group, but there is an amide 
		group in the first reactant, and returns <code>3</code> if there is no amine or amide group, but there is an alcohol group in the 
		first reactant. Returns <code>0</code> if none of the listed groups is found in first reactant. 
		<code>matchFirst(patom(2), {"[#8][C]=O","[NX3][CX3]=[OX1]"})</code> performs substructure search, checks if product atom matching map 
		<code>2</code> in the reaction equation matches any atom of the carboxylic or amide group query.  Returns the index	of mathing query, 
		or <code>0</code> if no match found. 
		<code>matchFirst(ratom(1), {"[#8:1]C=[O:2]","[#6][OX2:1][CX3](=[O:2])[#6]"}, 1, 2)</code> performs substructure search, checks if reactant
		atom matching map <code>1</code> in the reaction equation is a carboxylic oxygen or an oxigen in an ester group. Returns the index of the 
		query if match found, <code>0</code> otherwise.]]>
		</ReactionContextExample>
	</Matching>

	<Dissimilarity ID="dissimilarity" Class="chemaxon.jep.function.Dissimilarity">
		<UserParam Name="descriptor:metric" Type="String" Default="CF:Tanimoto" />
		<UserParam Name="mol1" Type="Molecule/String" Mandatory="true" />
		<UserParam Name="mol2" Type="Molecule/String" />
		<Descriptors>
			<Descriptor Name="ChemicalFingerprint" />
			<Descriptor Name="PharmacophoreFingerprint" />
			<Descriptor Name="BCUT" />
			<Descriptor Name="HDon" />
			<Descriptor Name="HAcc" />
			<Descriptor Name="Heavy" />
			<Descriptor Name="LogD" />
			<Descriptor Name="LogP" />
			<Descriptor Name="Mass" />
			<Descriptor Name="TPSA" />
			<Descriptor Name="ECFP" />
		</Descriptors>
		<ReturnType>double</ReturnType>
		<Category>Dissimilarity</Category>
		<Description>
			<![CDATA[computes the dissimilarity value between two molecules
	    <br/><b>Note 1:</b> Dissimilarity function requires JChem.
	    <br/><b>Note 2:</b> The dissimilarity values returned by dissimilarity function and similarity search in JChem database may differ because of the applied fingerprints. Dissimilarity function uses the default options of the chemical 
		fingerprints (length=1024, bonds=7, bits=2) optimized for similarity search. Database 
		similarity search uses fingerprints specified during the generation of the given table, optimized for substructure (and related) search types.]]>
		</Description>
		<ReturnValue>
			<![CDATA[the dissimilarity value]]>
		</ReturnValue>
		<Parameters>
			<![CDATA[<a href="#descriptors">descriptor:metric</a> or descriptor (with default metric) (optional, chemical
		fingerprint with Tanimoto metric is taken by default), one or two molecules (if only one is specified then the other
		one is taken from the context)]]>
		</Parameters>
		<MoleculeContextExample>
			<![CDATA[<code>dissimilarity("PF", "c1ccccc1", "C1CCCCC1")</code> returns the dissimilarity value between the benzene
		ring and cyclohexane, computed with pharmacophore fingerprint and its default metric (Tanimoto) <code>dissimilarity("c1ccccc1", "C1CCCCC1")</code>
		returns the dissimilarity value between the benzene ring and cyclohexane, computed with default fingerprint and its
		default metric (chemical fingerprint with Tanimoto)
		<code>dissimilarity("PF:Euclidean", "c1ccccc1")</code> returns the dissimilarity value between the benzene ring
		and the input molecule, computed with pharmacophore fingerprint and Euclidean metric <code>dissimilarity("LogD", "c1ccccc1")</code>
		returns the dissimilarity value between the benzene ring and the input molecule, computed with the LogD descriptor and
		its default <code>AbsDiff</code> metric]]>
		</MoleculeContextExample>
		<ReactionContextExample>
			<![CDATA[<code>dissimilarity("CF:Euclidean", "c1ccccc1", reactant(0))</code> returns the dissimilarity value between the
		benzene ring and the first reactant, computed with chemical fingerprint and Euclidean metric <code>dissimilarity(reactant(0), product(0))</code>
		returns the dissimilarity value between the first reactant and the first product, computed with default fingerprint
		and its default metric (chemical fingerprint with Tanimoto)]]>
		</ReactionContextExample>
	</Dissimilarity>
	<!-- JChem only END -->

</EvaluatorConfiguration>
