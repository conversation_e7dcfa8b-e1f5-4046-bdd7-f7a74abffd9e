<!-- 
    Document   : iupac_functions
    Created on : Sep 10, 2008, 3:22:08 PM
    Author     : <PERSON>ni 
	Modified by Efi September 2011
-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
	<meta HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<title>Name to Structure Conversion</title>
  <link rel="stylesheet" type="text/css" href="../marvinmanuals.css">
  </head>
  <body>
  
  <h1>Name to Structure Conversion</h1>
  ChemAxon’s naming toolkit capabilities allow you to generate chemical structures from IUPAC, trivial, drug, CAS names and from CAS numbers.
  <h3>Supported names include</h3>
<ul>
<li>IUPAC names, CAS names and generally systematic names
<li>Common names (e.g. Toluene)
<li>Drug names (e.g. Paracetamol, Doliprane)
<li>Acronyms (e.g. ATP for "Adenosine Triphosphate")
<li>CAS numbers (Note, this feature uses a Web service.)</li>
<li>For systematic names:
<ul>
<li>Chains</li>
<li>Monocycles</li>
<li>Retained/traditional names for ring systems with and without heteroatoms</li>
<li>Spiro ring systems</li>
<li>All cases of von Baeyer nomenclature for bridged ring systems</li>
<li>Fused ring systems</li>
<li>Ethers, esters, oximes, ...</li>
<li>Common characteristic groups</li>
<li>Ionic compounds</li>
<li>Compounds with one radical</li>
<li>Unlimited number of atoms and rings</li>
<li>All atom types</li>
<li>Substitutive and multiplicative nomenclatures</li>
<li>Isotopes</li>
<li>Stereochemistry</li>
</ul>
</ul>

<h3>Current limitations</h3>

<ul>
<li>Molecules containing multiple radicals (e.g. <code>ethane-1,2-diyl</code>) are not supported yet.
</ul>
<h3>Supporting corporate IDs and custom dictionaries</h3> 
<ul>
<li>It is possible to extend the name to structure conversion, for instance
to support corporate IDs such as ABC0001234, or to make use of common name dictionaries
in addition to the default one. This can be done by <a href="n2s_webservice.html">connecting to a webservice</a> or
by <a href="../naming/naming_customdic.html">creating a custom dictionary file</a>.
</ul>

 
  
  <h2><a name="name2str" class="anchor">Name to Structure Conversion in MarvinSketch</a></h2>
  <p>There are different ways how you can import a name directly into MarvinSketch and convert it to a chemical structure. 
  
  <ul>
    <li>A simple method is to select the name in the text of any page and drag&amp;drop or copy&amp;paste 
      it to MarvinSketch.
     </li> OR<br>
    <li>Select the "Import Name" (Ctrl+Shift+N) option from the File menu, and write the name into the text field and click the "Import" button (Ctrl+I).
    <p><img src="iupac_images/importsource_66.png" width="482" height="505" alt="importsource"></p>
    </li>
  </ul>
	<h2><a name="name2str_MView" class="anchor">Name to Structure Conversion in MarvinView</a></h2>
    <p>Open a text file (.name) containing IUPAC names (one per row). MarvinView will open 
    all the structures. Opening the same file in MarvinSketch, the program will ask you to select one structure (by its index number).
    </p>
<h2><a name="name2str_MView" class="anchor">Name to structure conversion from command line</a></h2>
As a commandline tool, you can use <a href="../applications/molconvert.html">MolConverter</a> for name to structure conversion.  
Examples:
<ol>
<li> Converting "test.name" name file to MOL file:
<pre> molconvert mol test.name -o test.mol</pre></li>
<li>Converting "test.name" name file to "test.smi" SMILES file which also contains the name of the structures:
<pre>molconvert smiles:n test.name>test.smi</pre>
</ol>

The behavior of name to structure can be controlled using
<a href="../formats/name-doc.html#import">format options</a>.

  </ul>
  
<h3>Marvin can also convert <a href="../calculations/s2n.html">structures to names</a>.<h3>

<h2>See also</h2>
<ul>
<li><a href="../formats/name-doc.html#import">name format options</a></li>
<li><a href="../developer/naming.html">Developer documentation for naming</a></li>
<li><a href="../d2s/d2s.html">Document to Structure</a> is used to find names in documents and free text</li>
</ul>

<h2>License information</h2>
</ul>
<li>Name import is only available for a single molecule with the free MarvinSketch desktop 
application. For batch conversion (with MarvinView, MolConverter, API, ...) you need the "Name to Structure" licence.</li>
</ul>

</body>
</html>
