<html>

<head>
    <meta NAME="author" CONTENT="<PERSON><PERSON>, <PERSON><PERSON><PERSON>">
    <LINK REL ="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css">
    <TITLE>MarvinSpace</TITLE>
</head>

<H1><a name="trouble">Troubleshooting</a></H1>
Please read this section if MarvinSpace does not work for you.

<br>
Look at the Java Console for the error message. (you can reach it from the menubar of your browser or by right clicking on the applet)
<ul>
<li><b>Problem: </b>UnsatisfiedLinkError <b>or</b> an <font color="#ff0000"><b>X</b></font> on the grey window <b>or</b> no jogl in java.library.path error.<br>
<b>Solution: </b>JOGL was not installed properly. On some machines one applet can be open at a time, the others have this error. Until fixing this, please be sure to have just one applet open at a time. It may be necessary to restart your browser.<br></li>
<li><b>Problem: </b>Seems frozen or is very slow with seconds between events.<br>
<b>Solution: </b>We experienced this behavior on some machines and we are working on it. Ususally this occures after AWT events (clicking on toolbar, menubar or selection panel). Please wait a bit instead of generating many more events with a lot of clicking. After processing all events it will be ok again.<br></li>
<li><b>Problem: </b>The browser closes all of its windows.<br>
<b>Solution: </b>That is a known bug of the applet with older versions of Firefox and Mozilla. Installing Firefox 1.5 will solve this problem. With Internet Explorer this problem was not experienced.
<br></li>
<li><b>Problem: </b>The applet is not loaded though it already loaded some time before. The consol shows this error message: java.lang.UnsatisfiedLinkError: Native Library ... already loaded in another classloader.<br>
<b>Solution: </b>This is due to the known issue: one applet works at a time. Closing all browser windows and restarting solves this.</li>
</ul>

<h1><a name="issues">Known Issues</a></h1>
<ul>
<li>Selection can be very slow on some machines. Updating the graphic driver will likely solve the problem.</li>
<li>Applet may crash the browser (particularly when closing or resizing window). This behavior is experienced with certain versions of Mozilla and Firefox. Updating Java will likely solve the problem.</li>
<li>Just one applet works at a time. This can be handled if all the applets have the same codebase. For example MarvinSpace applet examples at chemaxon.com have the same codebase, so they can run syncronously and they can also be reloaded.</li>
<li>Surface transparency is not perfect, does some flickering during rotation.</li>
<!--<li>Same rendering modes for motion and standing view. E.g. that's why anti-alias should be turned off during rotation and zoom.</li>
<li>Monitors have very simple graphics, and the position of their labels may not be the best.</li>-->
</ul>

<hr>
<p>
<center>
    <font size="-2" face="helvetica">
        Copyright &copy; 2004-2013
        <a href="http://www.chemaxon.com">ChemAxon Ltd.</a>
        &nbsp;&nbsp;&nbsp;All rights reserved.
    </font>
</center>

</html>