<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
        <META name="author" content="Tamas Vertse">
	<TITLE>Running the Applications</TITLE>
 <link rel=stylesheet type="text/css" href="../marvinmanuals.css">
</HEAD>
<BODY>

<h1>Running the Applications</h1>

<h3>Windows</h3>

<blockquote>
<table cellpadding="4">
<tr>
<td valign="top"><img src="../images/cursor.png"/></td>
<td>Double click on the <code>exe</code> file in the directory of Marvin Beans or its shortcut on the <em>Desktop</em> or in the <em>Start menu</em>.</td>
</tr>
<tr>
<td valign="top"><img src="../images/cmd.png"/></td>
<td>Start the <em>Command prompt</em> from the <em>Start menu</em>. Type<br>
        <blockquote>
	<code>msketch.bat</code><br>
        </blockquote>

If it says, "No such file or directory", include the <em>bin</em> subdirectory of the Marvin Beans directory
    in the PATH environment variable, or give the full path of the file.<br>
    E.g.: <code>C:\Program Files\ChemAxon\MarvinBeans\bin\msketch.bat</code><br>
    For more details about changing environment variables, please consult the Windows Help.
</td>    
</tr>    
</table>
</blockquote>

<h3>Mac OS X</h3>

<blockquote>
<table cellpadding="4">
<tr>
<td valign="top"><img src="../images/cursor.png"/></td>
<td>Double click on the application in the directory of Marvin Beans. You can find the shortcut icon on the <em>Desktop</em> or on the <em>Dock</em> panel.</td>
</tr>
<tr>
<td valign="top"><img src="../images/cmd.png"/></td>
<td>Start the <em>Terminal</em> application to be able to work in UNIX shell then type the name of the application, for example<br>
    <blockquote>
	<code>msketch</code>
    </blockquote>
For more details, please see the <a href="#unix">Unix / Linux command line</a> section below. </td>
</tr>
</table>
</blockquote>


<a name="unix"></a><h3>UNIX / Linux</h3>

<blockquote>
<table cellpadding="4">
<tr>
<td valign="top"><img src="../images/cursor.png"/></td>
<td>Double click on the application in the directory of Marvin Beans or its shortcut on <em>Desktop</em>.</td>
</tr>
<tr>
<td valign="top"><img src="../images/cmd.png"/></td>
<td>Bring up a terminal and type
	<pre>	msketch </pre>
	If it says, "command not found", add the <code>bin</code>
	subdirectory of Marvin Beans directory to the PATH. For more details, please consult
	the manual of the current shell. If you do not put it in the PATH, you can launch
	the application the following way:<br>
	Change the directory where the application is located
	then run application:
	<pre>
	cd &lt;MARVINBEANSHOME&gt;/bin
	msketch
	</pre>
	or give the full path of the application to start it:
	<pre>	&lt;MARVINBEANSHOME&gt;/bin/msketch</pre>
	<a name="note"></a><small>* "&lt;MARVINBEANSHOME&gt;" indicates the location
	where Marvin Beans was installed.
	Substitute this value with the home directory of Marvin Beans.
	E.g.: <code>/home/<USER>/ChemAxon/MarvinBeans</code>.</small>
</td>
</tr>
</table>
</blockquote>

<p>&nbsp;</p>

<center><div class="lenia">&nbsp;</div></center>
<p ALIGN=CENTER><small>Copyright &copy; 1998-2013
<a HREF="http://www.chemaxon.com" TARGET="_top">ChemAxon Ltd.</a>
&nbsp;&nbsp;&nbsp;All rights reserved.</small></p>


</BODY>
</HTML>