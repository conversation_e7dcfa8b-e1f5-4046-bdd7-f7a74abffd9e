<html>
<head>
<meta NAME="author" CONTENT="Peter Csizmadia">
<link REL ="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css" TITLE="Style">
<title>MarvinView</title>
</head>
<body>

<h1 align=center>MarvinView Application Options</h1>
<h3 align=center>Version @MARVINVERSION@</h3>

The Marvin Beans package contains the MarvinView application.
<p>

<h2>Usage</h2>
<blockquote>
<font COLOR="#333399"><strong>mview</strong></font>
[<em>options</em>] [<em>structure file</em>]
</blockquote>
<p>
<h3>Options</h3>
<blockquote>
<table CELLSPACING=0 CELLPADDING=4 BORDER="0" class="grid" width="70%">
<tr><td><code>-h</code><br><code>--help</code></td>
    <td VALIGN=TOP>
	Print command line help
	</td></tr>
<tr><td valign=top><code>-</code></td>
    <td>
	import structures from standard input	
	</td></tr>
<tr><td valign="TOP"><code>--geometry <i>W</i>x<i>H</i>+<i>X</i>+<i>Y</i></code>
	</td>
    <td>set preferred window size (<i>W</i> x <i>H</i>)
	and location (<i>X</i>, <i>Y</i>)
	</td></tr>
<tr><td valign=top><code>--real-time</code></td>
    <td>
	display input stream in real time
	</td></tr>
<tr><td valign="TOP"><code>--spreadsheet</code></td>
    <td>use spreadsheet-like viewer with one row per input record
	(default if the input file contains data fields)</td></tr>
<tr><td valign="TOP"><code>--gridbag</code></td>
    <td>use compact matrix-like viewer with more molecules in a row
	(default if the input file does not contain data fields)</td></tr>
<tr><td valign="TOP"><code>-c &lt;number&gt;</code></td>
    <td>maximum number of visible columns for GridBagView (default: 2)
	</td></tr>
<tr><td valign="TOP"><code>-r &lt;number&gt;</code></td>
    <td>maximum number of visible rows for GridBagView (default: 2)
	</td></tr>
<tr><td valign="TOP"><code>-n &lt;number&gt;</code></td>
    <td>maximum number of molecules to import
	</td></tr>

<tr><td valign=top><code>-S</code></td>
    <td>
	display unique SMILES code
	</td></tr>

<tr><td valign=top><code>-f &lt;f1&gt;:&lt;f2&gt;</code></td>
    <td>
	show specified fields (&lt;f1&gt;, &lt;f2&gt;, ...)
	in the given order from an SDfile.
	Field names are separated by colons.
	</td></tr>
</table>
</blockquote>

Options for displaying additional atomic properties stored in SDfile:

<blockquote>
<table CELLSPACING=0 CELLPADDING=4 BORDER="0" class="grid" width="70%">

<tr><td valign=top><code>-p &lt;file&gt;</code></td>
    <td>
	palette definition property file*
	</td></tr>

<tr><td valign=top><code>-t&nbsp;&lt;tagName&gt;</code></td>
    <td>
	name of the tag in the SDfile that contains property symbols*
	</td></tr>
</table>
<p><small>
* See the <a href="#colors">Property colors in MarvinView</a> section
</small></p>
</blockquote>
<p>You can also pass <a href="javaoptions.html">options to Java VM</a>
when you run the application from command line.</p>

<h2>Examples</h2>
<ol>
<li>Displaying the first 1000 structures from str.smi:
     <pre>mview str.smi</pre>
<li>Viewing 500 structures starting at the 9500-th one from str.smi:
     <pre>mview -n 500 -s 9500 str.smi</pre>
<li>Showing structures and 3 fields (ID, name, and stock) from stock.sdf:
     <pre>mview -f "ID:name:stock" stock.sdf</pre>
<li>Display in 2 columns, show SMILES code:
     <pre>mview -Sc 2 stock.sdf</pre>
<li>Search using
    <a href="http://www.chemaxon.com/jchem" target="_blank">JChem</a>'s
    command line search module and display the
   structure and the ID field from the results:
     <pre>jcsearch -q 'Clc1ccccc1' -f sdf input.sdf | mview -f ID -</pre>
<li><a class="text" NAME="opt_real-time">Displaying the progress</a> of a command line
    molecular dynamics simulation in real time:
     <pre>program_producing_molfiles_on_stdout | mview --real-time -</pre>
<li>Atom coloring according to property symbols in SDFile:
     <pre>mview -p colors.ini -t PPL stock.sdf</pre>

</ol>
<h2><a class="anchor" name="colors">Property colors in MarvinView</a></h2>
<h3>The property list</h3>

It is possible to color molecules in MarvinView by certain properties of their atoms.
These properties are stored in an additional data field in an SDfile as a property list.
The name of the data field can be specified for MarvinView by the <code>-t</code> parameter
The property list contains one or more property symbols for each atom, by the order
of the atoms. The properties of different atoms are separated by semicolon.
The number of specified atoms must match the number of atoms in the molecule.
If an atom has two or more properties, they are separated by slash.<br>

<h4>An example describing the atomic properties of a molecule containing 5 atoms:</h4>

<code>;;a;b/c;</code>

<ul>
<li>the first two atoms have no properties.</li>
<li>the third atom has property denoted by symbol "a"</li>
<li>the fourth atom has both property b and c</li>
<li>the fifth atom has no properties.</li>
</ul>

<h3>The color configuration file</h3>

The color profile for each data field labels is stored in a configuration file in "symbol  = value" format.
The file name of the color profile is an input parameter of MarvinView (-p).

<h4>Example:</h4>

<pre>
a = red
b = blue
c = green
d = black
e = navy
f = maroon
a/d = purple
b/d = lime
a/c = aqua
empty = #808080
other = fuchsia
</pre>

All property symbols and their combinations (separated by slash character) can have own colors assigned.
There are two special symbols: "empty" and "other".
Atoms not having any property will be displayed with the color of "empty".
The rest of atoms are displayed with the color of "other" (undefined combinations).
MarvinView supports the 16 color names of the HTML standard (see below),
but it is allowed to assign any valid RGB color value to symbols.

<h4>Supported color names:</h4>

<table border=0 cellPadding=5 cellSpacing=0 id="grid">

<tr>
	<td>Name</td>
	<td>Code</td>
	<td>Sample</td>
</tr>

<tr>
	<td>Black</td>
	<td>#000000</td>
	<td bgcolor=#000000>&nbsp;</td>
</tr>

<tr>
	<td>Green</td>
	<td>#008000</td>
	<td bgcolor=#008000>&nbsp;</td>
</tr>


<tr>
	<td>Silver</td>
	<td>#C0C0C0</td>
	<td bgcolor=#C0C0C0>&nbsp;</td>
</tr>

<tr>
	<td>Lime</td>
	<td>#00FF00</td>
	<td bgcolor=#00FF00>&nbsp;</td>
</tr>

<tr>
	<td>Gray</td>
	<td>#808080</td>
	<td bgcolor=#808080>&nbsp;</td>
</tr>

<tr>
	<td>Olive</td>
	<td>#808000</td>
	<td bgcolor=#808000>&nbsp;</td>
</tr>

<tr>
	<td>White</td>
	<td>#FFFFFF</td>
	<td bgcolor=#FFFFFF>&nbsp;</td>
</tr>

<tr>
	<td>Yellow</td>
	<td>#FFFF00</td>
	<td bgcolor=#FFFF00>&nbsp;</td>
</tr>

<tr>
	<td>Maroon</td>
	<td>#808080</td>
	<td bgcolor=#808080>&nbsp;</td>
</tr>

<tr>
	<td>Navy</td>
	<td>#000080</td>
	<td bgcolor=#000080>&nbsp;</td>
</tr>

<tr>
	<td>Red</td>
	<td>#FF0000</td>
	<td bgcolor=#FF0000>&nbsp;</td>
</tr>

<tr>
	<td>Blue</td>
	<td>#0000FF</td>
	<td bgcolor=#0000FF>&nbsp;</td>
</tr>

<tr>
	<td>Purple</td>
	<td>#800080</td>
	<td bgcolor=#800080>&nbsp;</td>
</tr>

<tr>
	<td>Teal</td>
	<td>#008080</td>
	<td bgcolor=#008080>&nbsp;</td>
</tr>

<tr>
	<td>Fuchsia</td>
	<td>#FF00FF</td>
	<td bgcolor=#FF00FF>&nbsp;</td>
</tr>

<tr>
	<td>Aqua</td>
	<td>#00FFFF</td>
	<td bgcolor=#00FFFF>&nbsp;</td>
</tr>

</table>

</body>
</html>
