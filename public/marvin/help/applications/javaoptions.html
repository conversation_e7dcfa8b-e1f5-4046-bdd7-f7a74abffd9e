<html>
<head>
<title>JAVA VM options</title>
<meta NAME="author" CONTENT="Tamas Vertse">
<link REL="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css" TITLE="Style">
</head>
<body>
<h1>Java VM options</h1>
<p>If you run ChemAxon applications from command line (through ChemAxon shell scripts), 
then apart form the application specific parameters, 
you can specify some <a href="#options">options</a> to Java VM (like Java heap size). E.g.:</p>
<pre>
    mview -Xmx128m huge-file.sdf
</pre>
<p>sets the max memory of the Java VM to 128Mb (the default
value is 64Mb). This can be useful if we want to load a huge molecule
file into the MarvinView application.
</p>
<h3><a class="anchor" name="options">Options</a></h3>
<table CELLSPACING=5 CELLPADDING=0>
<tr valign=TOP>
    <td><code>-client</code></td><td>&nbsp;</td>
    <td>to select the "client" VM</td></tr>
<tr valign=TOP>
    <td><code>-server</code></td><td>&nbsp;</td>
    <td>to select the "server" VM</td></tr>
<tr valign=TOP><td><code>-Xmx&lt;size&gt;</code></td><td>&nbsp;</td>
    <td>set maximum Java heap size</td></tr>
<tr valign=TOP><td><code>-X</code></td><td>&nbsp;</td>
    <td>print help on non-standard options. Different Java VM-s support
    various non-standard options. After printing the help, the Java VM
    exits immediatelly. Thus, the application will not start if the
    command line includes this option.<br>
    The ChemAxon shell scripts can evalute all non-standard options starting with
    <code>-X</code>.</td></tr>
</table>
</body>
</html>
