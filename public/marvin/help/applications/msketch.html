<html>
<head>
<meta NAME="author" CONTENT="Peter Csizmadia">
<link REL ="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css" TITLE="Style">
<title>MarvinSketch</title>
</head>
<body BGCOLOR="#ffffff" TEXT="#333366">

<h1 align=center>MarvinSketch Application Options</h1>
<h3 align=center>Version @MARVINVERSION@</h3>

The Marvin Beans package contains the MarvinSketch application.
<p>

<h2>Usage</h2>
<blockquote>
<font COLOR="#333399"><strong>msketch</strong></font>
[<em>options</em>] [<em>files or URLs...</em>]
</blockquote>
<p>
<h3>Options</h3>
<blockquote>
<table CELLSPACING=0 CELLPADDING=4 BORDER="0" class="grid">
<tr><td><code>-h</code><br><code>--help</code></td>
    <td VALIGN=TOP>
	Print command line help
	</td></tr>
<tr><td><code>-</code></td>
    <td>Import a structure from standard input</td></tr>
<tr><td><code>--debug</code></td>
    <td>
	Verbose debugging messages for cut/copy/paste and drag &amp; drop
    </td></tr>
<tr><td><code>--imageImportServiceURL=[URL]</code></td>
    <td>
	Specifies the URL of an image import service for the Sketcher to use.
    </td></tr>
</table>
</blockquote>    
<p>You can also pass <a href="javaoptions.html">options to Java VM</a>
when you run the application from command line.</p>

<h2>Examples</h2>

<ol>
<li>Start MarvinSketch
    with an empty sketcher window:
    <blockquote>
    <code>msketch</code>
    </blockquote>
    </li>
<li>Start MarvinSketch by loading two molfiles in two windows:
    <blockquote>
    <code>msketch caffeine.mol l-adrenaline.mol</code>
    </blockquote>
    </li>
</ol>

</body>
</html>
