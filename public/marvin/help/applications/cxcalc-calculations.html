<html>
<head>
<meta name="description" content="cxcalc calculations">
<meta name="author" content="Tamas Vertse">
<link rel="stylesheet" type="text/css" href="../marvinmanuals.css" title="Style">
<title>cxcalc calculations</title>
</head>
<body>
<h1><code>cxcalc</code> calculations</h1>
<ul>
<center><h3>Version 6.0.1</h3></center>
<table>
<tr>
	<th align="left" valign="top">&nbsp;<a href="#Elemental_Analysis">Elemental&nbsp;Analysis</a>:&nbsp;&nbsp;&nbsp;</th>
	<td><a href="#atomcount">atomcount</a>, 
	<a href="#composition">composition</a>, 
	<a href="#dotdisconnectedformula">dotdisconnectedformula</a>, 
	<a href="#dotdisconnectedisotopeformula">dotdisconnectedisotopeformula</a>, 
	<a href="#elemanal">elemanal</a>, 
	<a href="#elementalanalysistable">elementalanalysistable</a>, 
	<a href="#exactmass">exactmass</a>, 
	<a href="#formula">formula</a>, 
	<a href="#icomposition">icomposition</a>, 
	<a href="#iformula">iformula</a>, 
	<a href="#isotopecomposition">isotopecomposition</a>, 
	<a href="#isotopeformula">isotopeformula</a>, 
	<a href="#mass">mass</a>, 
	<a href="#sortableformula">sortableformula</a></td></tr>
<tr>
	<th align="left" valign="top">&nbsp;<a href="#Charge">Charge</a>:&nbsp;&nbsp;&nbsp;</th>
	<td><a href="#atomicpolarizability">atomicpolarizability</a>, 
	<a href="#atompol">atompol</a>, 
	<a href="#averagemolecularpolarizability">averagemolecularpolarizability</a>, 
	<a href="#averagepol">averagepol</a>, 
	<a href="#avgpol">avgpol</a>, 
	<a href="#axxpol">axxpol</a>, 
	<a href="#ayypol">ayypol</a>, 
	<a href="#azzpol">azzpol</a>, 
	<a href="#charge">charge</a>, 
	<a href="#formalcharge">formalcharge</a>, 
	<a href="#ioncharge">ioncharge</a>, 
	<a href="#molecularpolarizability">molecularpolarizability</a>, 
	<a href="#molpol">molpol</a>, 
	<a href="#oen">oen</a>, 
	<a href="#orbitalelectronegativity">orbitalelectronegativity</a>, 
	<a href="#pol">pol</a>, 
	<a href="#polarizability">polarizability</a>, 
	<a href="#tholepolarizability">tholepolarizability</a>, 
	<a href="#tpol">tpol</a>, 
	<a href="#tpolarizability">tpolarizability</a></td></tr>
<tr>
	<th align="left" valign="top">&nbsp;<a href="#Conformation">Conformation</a>:&nbsp;&nbsp;&nbsp;</th>
	<td><a href="#conformers">conformers</a>, 
	<a href="#hasvalidconformer">hasvalidconformer</a>, 
	<a href="#leconformer">leconformer</a>, 
	<a href="#lowestenergyconformer">lowestenergyconformer</a>, 
	<a href="#moldyn">moldyn</a>, 
	<a href="#moleculardynamics">moleculardynamics</a></td></tr>
<tr>
	<th align="left" valign="top">&nbsp;<a href="#Geometry">Geometry</a>:&nbsp;&nbsp;&nbsp;</th>
	<td><a href="#aliphaticatom">aliphaticatom</a>, 
	<a href="#aliphaticatomcount">aliphaticatomcount</a>, 
	<a href="#aliphaticbondcount">aliphaticbondcount</a>, 
	<a href="#aliphaticringcount">aliphaticringcount</a>, 
	<a href="#aliphaticringcountofsize">aliphaticringcountofsize</a>, 
	<a href="#angle">angle</a>, 
	<a href="#aromaticatom">aromaticatom</a>, 
	<a href="#aromaticatomcount">aromaticatomcount</a>, 
	<a href="#aromaticbondcount">aromaticbondcount</a>, 
	<a href="#aromaticringcount">aromaticringcount</a>, 
	<a href="#aromaticringcountofsize">aromaticringcountofsize</a>, 
	<a href="#asa">asa</a>, 
	<a href="#asymmetricatom">asymmetricatom</a>, 
	<a href="#asymmetricatomcount">asymmetricatomcount</a>, 
	<a href="#asymmetricatoms">asymmetricatoms</a>, 
	<a href="#balabanindex">balabanindex</a>, 
	<a href="#bondcount">bondcount</a>, 
	<a href="#bondtype">bondtype</a>, 
	<a href="#carboaliphaticringcount">carboaliphaticringcount</a>, 
	<a href="#carboaromaticringcount">carboaromaticringcount</a>, 
	<a href="#carboringcount">carboringcount</a>, 
	<a href="#chainatom">chainatom</a>, 
	<a href="#chainatomcount">chainatomcount</a>, 
	<a href="#chainbond">chainbond</a>, 
	<a href="#chainbondcount">chainbondcount</a>, 
	<a href="#chiralcenter">chiralcenter</a>, 
	<a href="#chiralcentercount">chiralcentercount</a>, 
	<a href="#chiralcenters">chiralcenters</a>, 
	<a href="#connected">connected</a>, 
	<a href="#connectedgraph">connectedgraph</a>, 
	<a href="#cyclomaticnumber">cyclomaticnumber</a>, 
	<a href="#dihedral">dihedral</a>, 
	<a href="#distance">distance</a>, 
	<a href="#distancedegree">distancedegree</a>, 
	<a href="#dreidingenergy">dreidingenergy</a>, 
	<a href="#eccentricity">eccentricity</a>, 
	<a href="#fragmentcount">fragmentcount</a>, 
	<a href="#fusedaliphaticringcount">fusedaliphaticringcount</a>, 
	<a href="#fusedaromaticringcount">fusedaromaticringcount</a>, 
	<a href="#fusedringcount">fusedringcount</a>, 
	<a href="#hararyindex">hararyindex</a>, 
	<a href="#heteroaliphaticringcount">heteroaliphaticringcount</a>, 
	<a href="#heteroaromaticringcount">heteroaromaticringcount</a>, 
	<a href="#heteroringcount">heteroringcount</a>, 
	<a href="#hindrance">hindrance</a>, 
	<a href="#hyperwienerindex">hyperwienerindex</a>, 
	<a href="#largestatomringsize">largestatomringsize</a>, 
	<a href="#largestringsize">largestringsize</a>, 
	<a href="#largestringsystemsize">largestringsystemsize</a>, 
	<a href="#maximalprojectionarea">maximalprojectionarea</a>, 
	<a href="#maximalprojectionradius">maximalprojectionradius</a>, 
	<a href="#maximalprojectionsize">maximalprojectionsize</a>, 
	<a href="#minimalprojectionarea">minimalprojectionarea</a>, 
	<a href="#minimalprojectionradius">minimalprojectionradius</a>, 
	<a href="#minimalprojectionsize">minimalprojectionsize</a>, 
	<a href="#mmff94energy">mmff94energy</a>, 
	<a href="#molecularsurfacearea">molecularsurfacearea</a>, 
	<a href="#msa">msa</a>, 
	<a href="#plattindex">plattindex</a>, 
	<a href="#polarsurfacearea">polarsurfacearea</a>, 
	<a href="#psa">psa</a>, 
	<a href="#randicindex">randicindex</a>, 
	<a href="#ringatom">ringatom</a>, 
	<a href="#ringatomcount">ringatomcount</a>, 
	<a href="#ringbond">ringbond</a>, 
	<a href="#ringbondcount">ringbondcount</a>, 
	<a href="#ringcount">ringcount</a>, 
	<a href="#ringcountofatom">ringcountofatom</a>, 
	<a href="#ringcountofsize">ringcountofsize</a>, 
	<a href="#ringsystemcount">ringsystemcount</a>, 
	<a href="#ringsystemcountofsize">ringsystemcountofsize</a>, 
	<a href="#rotatablebond">rotatablebond</a>, 
	<a href="#rotatablebondcount">rotatablebondcount</a>, 
	<a href="#shortestpath">shortestpath</a>, 
	<a href="#smallestatomringsize">smallestatomringsize</a>, 
	<a href="#smallestringsize">smallestringsize</a>, 
	<a href="#smallestringsystemsize">smallestringsystemsize</a>, 
	<a href="#stereodoublebondcount">stereodoublebondcount</a>, 
	<a href="#stericeffectindex">stericeffectindex</a>, 
	<a href="#sterichindrance">sterichindrance</a>, 
	<a href="#szegedindex">szegedindex</a>, 
	<a href="#topanal">topanal</a>, 
	<a href="#topologyanalysistable">topologyanalysistable</a>, 
	<a href="#vdwsa">vdwsa</a>, 
	<a href="#volume">volume</a>, 
	<a href="#wateraccessiblesurfacearea">wateraccessiblesurfacearea</a>, 
	<a href="#wienerindex">wienerindex</a>, 
	<a href="#wienerpolarity">wienerpolarity</a></td></tr>
<tr>
	<th align="left" valign="top">&nbsp;<a href="#Isomers">Isomers</a>:&nbsp;&nbsp;&nbsp;</th>
	<td><a href="#canonicaltautomer">canonicaltautomer</a>, 
	<a href="#dominanttautomerdistribution">dominanttautomerdistribution</a>, 
	<a href="#doublebondstereoisomercount">doublebondstereoisomercount</a>, 
	<a href="#doublebondstereoisomers">doublebondstereoisomers</a>, 
	<a href="#generictautomer">generictautomer</a>, 
	<a href="#majortautomer">majortautomer</a>, 
	<a href="#moststabletautomer">moststabletautomer</a>, 
	<a href="#stereoisomercount">stereoisomercount</a>, 
	<a href="#stereoisomers">stereoisomers</a>, 
	<a href="#tautomercount">tautomercount</a>, 
	<a href="#tautomers">tautomers</a>, 
	<a href="#tetrahedralstereoisomercount">tetrahedralstereoisomercount</a>, 
	<a href="#tetrahedralstereoisomers">tetrahedralstereoisomers</a></td></tr>
<tr>
	<th align="left" valign="top">&nbsp;<a href="#Markush_Enumerations">Markush&nbsp;Enumerations</a>:&nbsp;&nbsp;&nbsp;</th>
	<td><a href="#enumerationcount">enumerationcount</a>, 
	<a href="#enumerations">enumerations</a>, 
	<a href="#markushenumerationcount">markushenumerationcount</a>, 
	<a href="#markushenumerations">markushenumerations</a>, 
	<a href="#randommarkushenumerations">randommarkushenumerations</a></td></tr>
<tr>
	<th align="left" valign="top">&nbsp;<a href="#Name">Name</a>:&nbsp;&nbsp;&nbsp;</th>
	<td><a href="#name">name</a></td></tr>
<tr>
	<th align="left" valign="top">&nbsp;<a href="#Partitioning">Partitioning</a>:&nbsp;&nbsp;&nbsp;</th>
	<td><a href="#logd">logd</a>, 
	<a href="#logp">logp</a></td></tr>
<tr>
	<th align="left" valign="top">&nbsp;<a href="#Predictor">Predictor</a>:&nbsp;&nbsp;&nbsp;</th>
	<td><a href="#predict">predict</a>, 
	<a href="#predictor">predictor</a></td></tr>
<tr>
	<th align="left" valign="top">&nbsp;<a href="#Protonation">Protonation</a>:&nbsp;&nbsp;&nbsp;</th>
	<td><a href="#averagemicrospeciescharge">averagemicrospeciescharge</a>, 
	<a href="#chargedistribution">chargedistribution</a>, 
	<a href="#isoelectricpoint">isoelectricpoint</a>, 
	<a href="#majormicrospecies">majormicrospecies</a>, 
	<a href="#majorms">majorms</a>, 
	<a href="#microspeciesdistribution">microspeciesdistribution</a>, 
	<a href="#msdistr">msdistr</a>, 
	<a href="#pi">pi</a>, 
	<a href="#pka">pka</a></td></tr>
<tr>
	<th align="left" valign="top">&nbsp;<a href="#Other">Other</a>:&nbsp;&nbsp;&nbsp;</th>
	<td><a href="#acc">acc</a>, 
	<a href="#acceptor">acceptor</a>, 
	<a href="#acceptorcount">acceptorcount</a>, 
	<a href="#acceptormultiplicity">acceptormultiplicity</a>, 
	<a href="#acceptorsitecount">acceptorsitecount</a>, 
	<a href="#acceptortable">acceptortable</a>, 
	<a href="#accsitecount">accsitecount</a>, 
	<a href="#aromaticelectrophilicityorder">aromaticelectrophilicityorder</a>, 
	<a href="#aromaticnucleophilicityorder">aromaticnucleophilicityorder</a>, 
	<a href="#canonicalresonant">canonicalresonant</a>, 
	<a href="#chargedensity">chargedensity</a>, 
	<a href="#don">don</a>, 
	<a href="#donor">donor</a>, 
	<a href="#donorcount">donorcount</a>, 
	<a href="#donormultiplicity">donormultiplicity</a>, 
	<a href="#donorsitecount">donorsitecount</a>, 
	<a href="#donortable">donortable</a>, 
	<a href="#donsitecount">donsitecount</a>, 
	<a href="#electrondensity">electrondensity</a>, 
	<a href="#electrophilicityorder">electrophilicityorder</a>, 
	<a href="#electrophiliclocalizationenergy">electrophiliclocalizationenergy</a>, 
	<a href="#frameworks">frameworks</a>, 
	<a href="#hbda">hbda</a>, 
	<a href="#hbonddonoracceptor">hbonddonoracceptor</a>, 
	<a href="#hmochargedensity">hmochargedensity</a>, 
	<a href="#hmoelectrondensity">hmoelectrondensity</a>, 
	<a href="#hmoelectrophilicityorder">hmoelectrophilicityorder</a>, 
	<a href="#hmoelectrophiliclocalizationenergy">hmoelectrophiliclocalizationenergy</a>, 
	<a href="#hmohuckel">hmohuckel</a>, 
	<a href="#hmohuckeleigenvalue">hmohuckeleigenvalue</a>, 
	<a href="#hmohuckeleigenvector">hmohuckeleigenvector</a>, 
	<a href="#hmohuckelorbitals">hmohuckelorbitals</a>, 
	<a href="#hmohuckeltable">hmohuckeltable</a>, 
	<a href="#hmolocalizationenergy">hmolocalizationenergy</a>, 
	<a href="#hmonucleophilicityorder">hmonucleophilicityorder</a>, 
	<a href="#hmonucleophiliclocalizationenergy">hmonucleophiliclocalizationenergy</a>, 
	<a href="#hmopienergy">hmopienergy</a>, 
	<a href="#huckel">huckel</a>, 
	<a href="#huckeleigenvalue">huckeleigenvalue</a>, 
	<a href="#huckeleigenvector">huckeleigenvector</a>, 
	<a href="#huckelorbitals">huckelorbitals</a>, 
	<a href="#huckeltable">huckeltable</a>, 
	<a href="#localizationenergy">localizationenergy</a>, 
	<a href="#msacc">msacc</a>, 
	<a href="#msdon">msdon</a>, 
	<a href="#nucleophilicityorder">nucleophilicityorder</a>, 
	<a href="#nucleophiliclocalizationenergy">nucleophiliclocalizationenergy</a>, 
	<a href="#pichargedensity">pichargedensity</a>, 
	<a href="#pienergy">pienergy</a>, 
	<a href="#refractivity">refractivity</a>, 
	<a href="#resonantcount">resonantcount</a>, 
	<a href="#resonants">resonants</a>, 
	<a href="#totalchargedensity">totalchargedensity</a></td></tr>
</table>
<p>&nbsp;</p>
<hr />
<a class="anchor" name="Elemental_Analysis"><h2>Elemental&nbsp;Analysis</h2></a>
	<a class="anchor" name="atomcount"><h3>atomcount</h3></a>
	<p>Number of atoms in the molecule:  no atno: counts all atoms in the molecule; atno, but no massno: counts atoms of the given type in the molecule; atno, massno: counts atoms of the given isotope type in the molecule; atno, massno=0: counts atoms of the given type in the molecule,                 but excludes its isotopes.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-z,&nbsp;--atno&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;atomic number&gt;</td>
	</tr>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--massno&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;mass number&gt;</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc atomcount -z 7 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="composition"><h3>composition</h3></a>
	<p>Elemental composition calculation (w/w%).</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc composition -s true test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="dotdisconnectedformula"><h3>dotdisconnectedformula</h3></a>
	<p>Dot-disconnected molecular formula calculation.</p>
	<h5>Options:</h5>
No options	<h5>Example:</h5>
	<pre>cxcalc dotdisconnectedformula test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="dotdisconnectedisotopeformula"><h3>dotdisconnectedisotopeformula</h3></a>
	<p>Dot-disconnected molecular formula calculation, isotopes included.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-D,&nbsp;--symbolD&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] use D / T symbols for Deuterium / Tritium
(default: true)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc dotdisconnectedisotopeformula test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="elemanal"><h3>elemanal</h3></a>
	<p>Molecule data calculation: formula, isotopeformula, dotdisconnectedformula, dotdisconnectedisotopeformula, mass, exactmass, composition, isotopecomposition, atomcount.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--type&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[formula|isotopeformula|dotdisconnectedformula|
dotdisconnectedisotopeformula|mass|exactmass|composition|
isotopecomposition|atomcount] (default: all)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc elemanal -t "mass,composition,formula" test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="elementalanalysistable"><h3>elementalanalysistable</h3></a>
	<p>Molecule data calculation: formula, isotopeformula, dotdisconnectedformula, dotdisconnectedisotopeformula, mass, exactmass, composition, isotopecomposition, atomcount.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--type&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[formula|isotopeformula|dotdisconnectedformula|
dotdisconnectedisotopeformula|mass|exactmass|composition|
isotopecomposition|atomcount] (default: all)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc elementalanalysistable -t "mass,composition,formula" test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="exactmass"><h3>exactmass</h3></a>
	<p>Exact molecule mass calculation based on the most frequent natural isotopes of the elements.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt;
(default: precision of the least precise atomic mass)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc exactmass test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="formula"><h3>formula</h3></a>
	<p>Molecular formula calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc formula -s true test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="icomposition"><h3>icomposition</h3></a>
	<p>Elemental composition calculation, isotopes included (w/w%).</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-D,&nbsp;--symbolD&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] use D / T symbols for Deuterium / Tritium
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc icomposition -s true test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="iformula"><h3>iformula</h3></a>
	<p>Molecular formula calculation, isotopes included.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-D,&nbsp;--symbolD&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] use D / T symbols for Deuterium / Tritium
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc iformula -s true test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="isotopecomposition"><h3>isotopecomposition</h3></a>
	<p>Elemental composition calculation, isotopes included (w/w%).</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-D,&nbsp;--symbolD&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] use D / T symbols for Deuterium / Tritium
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc isotopecomposition -s true test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="isotopeformula"><h3>isotopeformula</h3></a>
	<p>Molecular formula calculation, isotopes included.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-D,&nbsp;--symbolD&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] use D / T symbols for Deuterium / Tritium
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc isotopeformula -s true test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="mass"><h3>mass</h3></a>
	<p>Molecule mass calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt;
(default: precision of the least precise atomic mass)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc mass test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="sortableformula"><h3>sortableformula</h3></a>
	<p>Calculates a fixed digit sortable molecular formula.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-d,&nbsp;--digits&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;minimum number of digits in proportionate
number of atoms&gt; (default: 5)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc sortableformula -d 4 test.mol</pre>
<p>&nbsp;</p>
<hr />
<a class="anchor" name="Charge"><h2>Charge</h2></a>
	<a class="anchor" name="atomicpolarizability"><h3>atomicpolarizability</h3></a>
	<p>Atomic polarizability calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc atomicpolarizability test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="atompol"><h3>atompol</h3></a>
	<p>Atomic polarizability calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc atompol test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="averagemolecularpolarizability"><h3>averagemolecularpolarizability</h3></a>
	<p>Average molecular polarizability calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc averagemolecularpolarizability test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="averagepol"><h3>averagepol</h3></a>
	<p>Average molecular polarizability calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc averagepol test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="avgpol"><h3>avgpol</h3></a>
	<p>Average molecular polarizability calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc avgpol test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="axxpol"><h3>axxpol</h3></a>
	<p>Calculation of principal component of polarizability tensor axx.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc axxpol test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="ayypol"><h3>ayypol</h3></a>
	<p>Calculation of principal component of polarizability tensor ayy.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc ayypol test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="azzpol"><h3>azzpol</h3></a>
	<p>Calculation of principal component of polarizability tensor azz.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc azzpol test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="charge"><h3>charge</h3></a>
	<p>Partial charge calculation. Types aromaticsystem / aromaticring calculate the sum of charges in the aromatic system / aromatic ring containing the atom.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--type&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[sigma|pi|total|implh|
aromaticsystem|aromaticsystemsigma|aromaticsystempi|
aromaticring|aromaticringsigma|aromaticringpi]
(default: total)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-i,&nbsp;--implh&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] implicit H charge sum shown in brackets
(for sigma and total charge only) (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-r,&nbsp;--resonance&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: take resonant structures (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc -S -o result.sdf -t myCHARGE charge -t "pi,total" -p 3 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="formalcharge"><h3>formalcharge</h3></a>
	<p>Formal charge calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc formalcharge test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="ioncharge"><h3>ioncharge</h3></a>
	<p>Partial charge(s):   A) on the ionic forms with distribution percentage not less than      the minimum percentage specified in the min-percent parameter, or else   B) on the ionic form with maximal distribution      if the min-percent parameter is omitted.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; (default: 7)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-n,&nbsp;--max-ions&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">max number of ionizable atoms
to be considered (default: 9)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--min-percent&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;min occurrence percentage of ionic form to be considered&gt;
(optional, if omitted then only the ionic form
with max percentage is considered)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--charge-type&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[single|accumulated] charge type,
accumulated means that charges of attached H atoms
should be added (default: single)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc ioncharge -n 6 -H 8 -m 1 -t accumulated test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="molecularpolarizability"><h3>molecularpolarizability</h3></a>
	<p>Molecular polarizability calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc molecularpolarizability test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="molpol"><h3>molpol</h3></a>
	<p>Molecular polarizability calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc molpol test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="oen"><h3>oen</h3></a>
	<p>Orbital electronegativity calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--type&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[sigma|pi]
sigma: sigma orbital electronegativity
pi: pi orbital electronegativity
(default: sigma,pi)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-r,&nbsp;--resonance&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: take resonant structures (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc oen -t sigma test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="orbitalelectronegativity"><h3>orbitalelectronegativity</h3></a>
	<p>Orbital electronegativity calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--type&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[sigma|pi]
sigma: sigma orbital electronegativity
pioen: pi orbital electronegativity
(default: sigma,pi)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-r,&nbsp;--resonance&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: take resonant structures (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc orbitalelectronegativity -p 3 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="pol"><h3>pol</h3></a>
	<p>Atomic and molecular polarizability calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--type&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[molecular|atomic] (default: both)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc pol -p 3 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="polarizability"><h3>polarizability</h3></a>
	<p>Atomic and molecular polarizability calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--type&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[molecular|atomic] (default: both)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc polarizability -p 3 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="tholepolarizability"><h3>tholepolarizability</h3></a>
	<p>Calculation of average molecular polarizability and principal components of polarizability tensor (axx, ayy, azz).</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc tholepolarizability test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="tpol"><h3>tpol</h3></a>
	<p>Calculation of average molecular polarizability and principal components of polarizability tensor (axx, ayy, azz).</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc tpol test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="tpolarizability"><h3>tpolarizability</h3></a>
	<p>Calculation of average molecular polarizability and principal components of polarizability tensor (axx, ayy, azz).</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc tpolarizability test.mol</pre>
<p>&nbsp;</p>
<hr />
<a class="anchor" name="Conformation"><h2>Conformation</h2></a>
	<a class="anchor" name="conformers"><h3>conformers</h3></a>
	<p>Calculates the conformers of the molecule.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt; should be a 3D format (default: sdf)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-x,&nbsp;--forcefield&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[dreiding|mmff94] forcefield used for calculation
(default: dreiding)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--maxconformers&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;maximum number of conformers to be generated&gt;
(default: 100)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-d,&nbsp;--diversity&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;diversity limit&gt; (default: 0.1)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--saveconfdesc&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] if true a single conformer is saved
with a property containing conformer information
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-e,&nbsp;--hyperfine&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] if true hyperfine option is set
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-y,&nbsp;--prehydrogenize&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] if true prehydrogenize is done before
calculation, if false calculation is done without
hydrogens (default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--timelimit&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;timelimit for calculation in sec&gt; (default: 900)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-O,&nbsp;--optimization&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[0|1|2|3] conformer generation optimiztaion limit
for different enviroments
{0}: very loose  (limit=0.01)
{1}: normal      (limit=0.0010)
{2}: strict      (limit=1.0E-4)
{3}: very strict (limit=1.0E-5)
(default: 1)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc conformers -m 250 -s true test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="hasvalidconformer"><h3>hasvalidconformer</h3></a>
	<p>Calculates if the molecule has a conformer.</p>
	<h5>Options:</h5>
No options	<h5>Example:</h5>
	<pre>cxcalc hasvalidconformer test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="leconformer"><h3>leconformer</h3></a>
	<p>Calculates the lowest energy conformer of the molecule.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt; should be a 3D format (default: sdf)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-x,&nbsp;--forcefield&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[dreiding|mmff94] forcefield used for calculation
(default: dreiding)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-e,&nbsp;--hyperfine&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] if true hyperfine option is set
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-y,&nbsp;--prehydrogenize&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] if true prehydrogenize is done before
calculation, if false calculation is done without
hydrogens (default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--timelimit&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;timelimit for calculation in sec&gt; (default: 900)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-O,&nbsp;--optimization&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[0|1|2|3] conformer generation optimiztaion limit for
different enviroments
{0}: very loose  (limit=0.01)
{1}: normal      (limit=0.0010)
{2}: strict      (limit=1.0E-4)
{3}: very strict (limit=1.0E-5)
(default: 1)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--multifrag&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]  in case of multi-fragment molecules and
if mmff94 forcefield selected:
takes largest fragment if false,
takes whole molecule if true (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc leconformer -f mrv test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="lowestenergyconformer"><h3>lowestenergyconformer</h3></a>
	<p>Calculates the lowest energy conformer of the molecule.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt; should be a 3D format (default: sdf)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-x,&nbsp;--forcefield&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[dreiding|mmff94] forcefield used for calculation
(default: dreiding)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-e,&nbsp;--hyperfine&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] if true hyperfine option is set
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-y,&nbsp;--prehydrogenize&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] if true prehydrogenize is done before
calculation, if false calculation is done without
hydrogens (default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--timelimit&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;timelimit for calculation in sec&gt; (default: 900)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-O,&nbsp;--optimization&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[0|1|2|3] conformer generation optimiztaion limit for
different enviroments
{0}: very loose  (limit=0.01)
{1}: normal      (limit=0.0010)
{2}: strict      (limit=1.0E-4)
{3}: very strict (limit=1.0E-5)
(default: 1)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--multifrag&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules and
if mmff94 forcefield selected:
takes largest fragment if false,
takes whole molecule if true (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc lowestenergyconformer -f mrv test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="moldyn"><h3>moldyn</h3></a>
	<p>Molecular Dynamics.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt; should be a 3D format (default: sdf)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-x,&nbsp;--forcefield&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[dreiding|mmff94] forcefield used for calculation
(default: dreiding)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-i,&nbsp;--integrator&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[positionverlet|velocityverlet|leapfrog]
integrator type used for calculation
(default: velocityverlet)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-n,&nbsp;--stepno&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;number of simulation steps&gt; (default: 1000)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--steptime&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;time between steps in femtoseconds&gt; (default: 0.1)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-T,&nbsp;--temperature&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;temperature in Kelvin&gt; (default: 300 K)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--samplinginterval&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;sampling interval in femtoseconds&gt; (default: 10)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc moldyn test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="moleculardynamics"><h3>moleculardynamics</h3></a>
	<p>Molecular Dynamics.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt; should be a 3D format (default: sdf)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-x,&nbsp;--forcefield&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[dreiding|mmff94] forcefield used for calculation
(default: dreiding)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-i,&nbsp;--integrator&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[positionverlet|velocityverlet|leapfrog]
integrator type used for calculation
(default: velocityverlet)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-n,&nbsp;--stepno&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;number of simulation steps&gt; (default: 1000)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--steptime&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;time between steps in femtoseconds&gt; (default: 0.1)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-T,&nbsp;--temperature&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;temperature in Kelvin&gt; (default: 300 K)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--samplinginterval&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;sampling interval in femtoseconds&gt; (default: 10)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc moleculardynamics test.mol</pre>
<p>&nbsp;</p>
<hr />
<a class="anchor" name="Geometry"><h2>Geometry</h2></a>
	<a class="anchor" name="aliphaticatom"><h3>aliphaticatom</h3></a>
	<p>Checks if a specified atom is aliphatic.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a&nbsp;--arom&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[general|basic|loose] sets aromatization method</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc aliphaticatom test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="aliphaticatomcount"><h3>aliphaticatomcount</h3></a>
	<p>Aliphatic atom count.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a&nbsp;--arom&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[general|basic|loose] sets aromatization method</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc aliphaticatomcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="aliphaticbondcount"><h3>aliphaticbondcount</h3></a>
	<p>Aliphatic bond count.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a&nbsp;--arom&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[general|basic|loose] sets aromatization method</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc aliphaticbondcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="aliphaticringcount"><h3>aliphaticringcount</h3></a>
	<p>Aliphatic ring count.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a&nbsp;--arom&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[general|basic|loose] sets aromatization method</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc aliphaticringcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="aliphaticringcountofsize"><h3>aliphaticringcountofsize</h3></a>
	<p>Aliphatic ring count of size.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a&nbsp;--arom&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[general|basic|loose] sets aromatization method</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-z,&nbsp;--size&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;ring size&gt; size of rings to count</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc aliphaticringcountofsize -z 5 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="angle"><h3>angle</h3></a>
	<p>Angle of three atoms.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--atoms&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[&lt;atom1&gt;-&lt;atom2&gt;-&lt;atom3&gt;] (1-based) atom indexes
of the atom pair</td>
	</tr>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-o,&nbsp;--optimization&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[0|1|2|3] conformer generation optimiztaion limit for
different enviroments
{0}: very loose  (limit=0.01)
{1}: normal      (limit=0.0010)
{2}: strict      (limit=1.0E-4)
{3}: very strict (limit=1.0E-5)
(default: 1)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--calcforleconformer&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[if2D|never|always] (default: if2D)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc angle -a 2-4-6 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="aromaticatom"><h3>aromaticatom</h3></a>
	<p>Checks if a specified atom is aromatic.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a&nbsp;--arom&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[general|basic|loose] sets aromatization method</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc aromaticatom test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="aromaticatomcount"><h3>aromaticatomcount</h3></a>
	<p>Aromatic atom count.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a&nbsp;--arom&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[general|basic|loose] sets aromatization method</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc aromaticatomcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="aromaticbondcount"><h3>aromaticbondcount</h3></a>
	<p>Aromatic bond count.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a&nbsp;--arom&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[general|basic|loose] sets aromatization method</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc aromaticbondcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="aromaticringcount"><h3>aromaticringcount</h3></a>
	<p>Aromatic ring count.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a&nbsp;--arom&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[general|basic|loose] sets aromatization method</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc aromaticringcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="aromaticringcountofsize"><h3>aromaticringcountofsize</h3></a>
	<p>Aromatic ring count of size.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a&nbsp;--arom&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[general|basic|loose] sets aromatization method</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-z,&nbsp;--size&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;ring size&gt; size of rings to count</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc aromaticringcountofsize -z 6 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="asa"><h3>asa</h3></a>
	<p>Water Accessible Surface Area calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-r,&nbsp;--solventradius&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;solvent radius: 0.0-5.0&gt; (default: 1.4)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-i,&nbsp;--increments&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] show incremental surface area on atoms
(default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc asa test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="asymmetricatom"><h3>asymmetricatom</h3></a>
	<p>Checks if a specified atom is an asymmetric atom.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc asymmetricatom test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="asymmetricatomcount"><h3>asymmetricatomcount</h3></a>
	<p>The number of asymmetric atoms.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc asymmetricatomcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="asymmetricatoms"><h3>asymmetricatoms</h3></a>
	<p>The asymmetric atoms.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc asymmetricatoms test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="balabanindex"><h3>balabanindex</h3></a>
	<p>The Balaban index.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc balabanindex test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="bondcount"><h3>bondcount</h3></a>
	<p>Bond count.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc bondcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="bondtype"><h3>bondtype</h3></a>
	<p>The bond type between two atoms.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a&nbsp;--arom&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[general|basic|loose] sets aromatization method</td>
	</tr>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--atoms&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[&lt;atom1&gt;-&lt;atom2&gt;] (1-based) atom indexes of the bond atoms</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc bondtype -a 2-3 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="carboaliphaticringcount"><h3>carboaliphaticringcount</h3></a>
	<p>Carboaliphatic ring count.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a&nbsp;--arom&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[general|basic|loose] sets aromatization method</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc carboaliphaticringcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="carboaromaticringcount"><h3>carboaromaticringcount</h3></a>
	<p>Carboaromatic ring count.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a&nbsp;--arom&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[general|basic|loose] sets aromatization method</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc carboaromaticringcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="carboringcount"><h3>carboringcount</h3></a>
	<p>Carbo ring count.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc carboringcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="chainatom"><h3>chainatom</h3></a>
	<p>Checks if a specified atom is a chain atom.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc chainatom test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="chainatomcount"><h3>chainatomcount</h3></a>
	<p>Chain atom count.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc chainatomcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="chainbond"><h3>chainbond</h3></a>
	<p>Checks if the bond is a chain bond.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--atoms&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[&lt;atom1&gt;-&lt;atom2&gt;] (1-based) atom indexes of the bond atoms</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc chainbond -a 2-3 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="chainbondcount"><h3>chainbondcount</h3></a>
	<p>Chain bond count.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc chainbondcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="chiralcenter"><h3>chiralcenter</h3></a>
	<p>Checks if a specified atom is a tetrahedral stereogenic center.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc chiralcenter test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="chiralcentercount"><h3>chiralcentercount</h3></a>
	<p>The number of tetrahedral stereogenic center atoms.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc chiralcentercount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="chiralcenters"><h3>chiralcenters</h3></a>
	<p>The the chiral center atoms.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc chiralcenters test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="connected"><h3>connected</h3></a>
	<p>Checks if two atoms are in the same connected component.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--atoms&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[&lt;atom1&gt;-&lt;atom2&gt;] (1-based) atom indexes of the atom pair</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc connected -a 2-3 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="connectedgraph"><h3>connectedgraph</h3></a>
	<p>Checks if the molecule graph is connected.</p>
	<h5>Options:</h5>
No options	<h5>Example:</h5>
	<pre>cxcalc connectedgraph test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="cyclomaticnumber"><h3>cyclomaticnumber</h3></a>
	<p>The cyclomatic number.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc cyclomaticnumber test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="dihedral"><h3>dihedral</h3></a>
	<p>Dihedral of four atoms.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--atoms&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[&lt;atom1&gt;-&lt;atom2&gt;-&lt;atom3&gt;-&lt;atom4&gt;] (1-based) atom
indexes of the atom pair</td>
	</tr>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-o,&nbsp;--optimization&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[0|1|2|3] conformer generation optimiztaion limit for
different enviroments
{0}: very loose  (limit=0.01)
{1}: normal      (limit=0.0010)
{2}: strict      (limit=1.0E-4)
{3}: very strict (limit=1.0E-5)
(default: 1)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--calcforleconformer&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[if2D|never|always] (default: if2D)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc dihedral -a 1-2-4-6 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="distance"><h3>distance</h3></a>
	<p>Distance between two atoms.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--atoms&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[&lt;atom1&gt;-&lt;atom2&gt;] (1-based) atom indexes of the atom pair</td>
	</tr>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-o,&nbsp;--optimization&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[0|1|2|3] conformer generation optimiztaion limit for
different enviroments
{0}: very loose  (limit=0.01)
{1}: normal      (limit=0.0010)
{2}: strict      (limit=1.0E-4)
{3}: very strict (limit=1.0E-5)
(default: 1)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--calcforleconformer&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[if2D|never|always] (default: if2D)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc distance -a 2-4 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="distancedegree"><h3>distancedegree</h3></a>
	<p>Distance degree of atom.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc distancedegree test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="dreidingenergy"><h3>dreidingenergy</h3></a>
	<p>Calculates the dreiding energy of a conformer of the molecule in kcal/mol.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-o,&nbsp;--optimization&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[0|1|2|3] conformer generation optimiztaion limit for
different enviroments
{0}: very loose  (limit=0.01)
{1}: normal      (limit=0.0010)
{2}: strict      (limit=1.0E-4)
{3}: very strict (limit=1.0E-5)
(default: 1)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--calcforleconformer&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[if2D|never|always] (default: if2D)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc dreidingenergy -p 1 -l always test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="eccentricity"><h3>eccentricity</h3></a>
	<p>Eccentricity of atom.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc eccentricity test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="fragmentcount"><h3>fragmentcount</h3></a>
	<p>Fragment count.</p>
	<h5>Options:</h5>
No options	<h5>Example:</h5>
	<pre>cxcalc fragmentcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="fusedaliphaticringcount"><h3>fusedaliphaticringcount</h3></a>
	<p>The number of fused aliphatic rings (SSSR smallest set of smallest aliphatic rings).</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a&nbsp;--arom&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[general|basic|loose] sets aromatization method</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc fusedaliphaticringcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="fusedaromaticringcount"><h3>fusedaromaticringcount</h3></a>
	<p>The number of fused aromatic rings (SSSR smallest set of smallest aromatic rings).</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a&nbsp;--arom&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[general|basic|loose] sets aromatization method</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc fusedaromaticringcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="fusedringcount"><h3>fusedringcount</h3></a>
	<p>The number of fused rings (SSSR smallest set of smallest rings).</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc fusedringcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="hararyindex"><h3>hararyindex</h3></a>
	<p>Harary index.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc hararyindex test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="heteroaliphaticringcount"><h3>heteroaliphaticringcount</h3></a>
	<p>Heteroaliphatic ring count.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a&nbsp;--arom&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[general|basic|loose] sets aromatization method</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc heteroaliphaticringcount test.mol	</pre>
<p>&nbsp;</p>
	<a class="anchor" name="heteroaromaticringcount"><h3>heteroaromaticringcount</h3></a>
	<p>Heteroaromatic ring count.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a&nbsp;--arom&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[general|basic|loose] sets aromatization method</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc heteroaromaticringcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="heteroringcount"><h3>heteroringcount</h3></a>
	<p>Hetero ring count.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc heteroringcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="hindrance"><h3>hindrance</h3></a>
	<p>Steric hindrance.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-o,&nbsp;--optimization&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[0|1|2|3] conformer generation optimiztaion limit for
different enviroments
{0}: very loose  (limit=0.01)
{1}: normal      (limit=0.0010)
{2}: strict      (limit=1.0E-4)
{3}: very strict (limit=1.0E-5)
(default: 1)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--calcforleconformer&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[if2D|never|always] (default: if2D)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc hindrance test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="hyperwienerindex"><h3>hyperwienerindex</h3></a>
	<p>Hyper Wiener index.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc hyperwienerindex test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="largestatomringsize"><h3>largestatomringsize</h3></a>
	<p>Size of largest ring containing a specified atom.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc largestatomringsize test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="largestringsize"><h3>largestringsize</h3></a>
	<p>Largest ring size.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc largestringsize test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="largestringsystemsize"><h3>largestringsystemsize</h3></a>
	<p>Largest ring system size.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc largestringsystemsize test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="maximalprojectionarea"><h3>maximalprojectionarea</h3></a>
	<p>Calculates the maximal projection area.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--scalefactor&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;radius scale factor&gt;</td>
	</tr>
	<tr>
		<th align="left" valign="top">-o,&nbsp;--optimization&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[0|1|2|3] conformer generation optimiztaion limit
for different enviroments
{0}: very loose  (limit=0.01)
{1}: normal      (limit=0.0010)
{2}: strict      (limit=1.0E-4)
{3}: very strict (limit=1.0E-5)
(default: 1)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-O,&nbsp;--optimizeprojection&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] sets projection optimization
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--calcforleconformer&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[if2D|never|always] (default: if2D)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc maximalprojectionarea test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="maximalprojectionradius"><h3>maximalprojectionradius</h3></a>
	<p>Calculates the maximal projection radius.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--scalefactor&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;radius scale factor&gt;</td>
	</tr>
	<tr>
		<th align="left" valign="top">-o,&nbsp;--optimization&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[0|1|2|3] conformer generation optimiztaion limit
for different enviroments
{0}: very loose  (limit=0.01)
{1}: normal      (limit=0.0010)
{2}: strict      (limit=1.0E-4)
{3}: very strict (limit=1.0E-5)
(default: 1)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-O,&nbsp;--optimizeprojection&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] sets projection optimization
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--calcforleconformer&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[if2D|never|always] (default: if2D)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc maximalprojectionradius test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="maximalprojectionsize"><h3>maximalprojectionsize</h3></a>
	<p>Calculates the size of the molecule perpendicular to the maximal projection area surface.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-o,&nbsp;--optimization&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[0|1|2|3] conformer generation optimiztaion limit
for different enviroments
{0}: very loose  (limit=0.01)
{1}: normal      (limit=0.0010)
{2}: strict      (limit=1.0E-4)
{3}: very strict (limit=1.0E-5)
(default: 1)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-O,&nbsp;--optimizeprojection&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] sets projection optimization
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--calcforleconformer&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[if2D|never|always] (default: if2D)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc maximalprojectionsize test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="minimalprojectionarea"><h3>minimalprojectionarea</h3></a>
	<p>Calculates the minimal projection area.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--scalefactor&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;radius scale factor&gt;</td>
	</tr>
	<tr>
		<th align="left" valign="top">-o,&nbsp;--optimization&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[0|1|2|3] conformer generation optimiztaion limit
for different enviroments
{0}: very loose  (limit=0.01)
{1}: normal      (limit=0.0010)
{2}: strict      (limit=1.0E-4)
{3}: very strict (limit=1.0E-5)
(default: 1)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-O,&nbsp;--optimizeprojection&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] sets projection optimization
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--calcforleconformer&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[if2D|never|always] (default: if2D)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc minimalprojectionarea test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="minimalprojectionradius"><h3>minimalprojectionradius</h3></a>
	<p>Calculates the minimal projection radius.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--scalefactor&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;radius scale factor&gt;</td>
	</tr>
	<tr>
		<th align="left" valign="top">-o,&nbsp;--optimization&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[0|1|2|3] conformer generation optimiztaion limit
for different enviroments
{0}: very loose  (limit=0.01)
{1}: normal      (limit=0.0010)
{2}: strict      (limit=1.0E-4)
{3}: very strict (limit=1.0E-5)
(default: 1)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-O,&nbsp;--optimizeprojection&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] sets projection optimization
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--calcforleconformer&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[if2D|never|always] (default: if2D)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc minimalprojectionradius test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="minimalprojectionsize"><h3>minimalprojectionsize</h3></a>
	<p>Calculates the size of the molecule perpendicular to the minimal projection area surface.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-o,&nbsp;--optimization&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[0|1|2|3] conformer generation optimiztaion limit
for different enviroments
{0}: very loose  (limit=0.01)
{1}: normal      (limit=0.0010)
{2}: strict      (limit=1.0E-4)
{3}: very strict (limit=1.0E-5)
(default: 1)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-O,&nbsp;--optimizeprojection&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] sets projection optimization
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--calcforleconformer&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[if2D|never|always] (default: if2D)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc minimalprojectionsize test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="mmff94energy"><h3>mmff94energy</h3></a>
	<p>Calculates the MMFF94 energy of the molecule in kcal/mol.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--calcforleconformer&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[if2D|never|always] (default: if2D)</td>
	</tr>
	<tr>
		<th align="left" valign="top">&nbsp;&nbsp;&nbsp;&nbsp;--mmff94optimization&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] sets MFF94 optimization
(default: false) </td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc mmff94energy --mmff94optimization test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="molecularsurfacearea"><h3>molecularsurfacearea</h3></a>
	<p>Molecular Surface Area calculation (3D).</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--type&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[vanderwaals|ASA|ASA+|ASA-|ASA_H|ASA_P]
(default: vanderwaals)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-i,&nbsp;--increments&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] show incremental surface area on atoms
(default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc molecularsurfacearea -t ASA+ -i true -H 7.4 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="msa"><h3>msa</h3></a>
	<p>Molecular Surface Area calculation (3D).</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--type&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[vanderwaals|ASA|ASA+|ASA-|ASA_H|ASA_P]
(default: vanderwaals)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-i,&nbsp;--increments&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] show incremental surface area on atoms
(default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc msa -t ASA+ -i true -H 7.4 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="plattindex"><h3>plattindex</h3></a>
	<p>The Platt index.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc plattindex test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="polarsurfacearea"><h3>polarsurfacearea</h3></a>
	<p>Topological Polar Surface Area calculation (2D).</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-S,&nbsp;--excludesulfur&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude sulfur atom from calculation
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-P,&nbsp;--excludephosphorus&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude phosphorus atom from calculation
(default: true)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc -S -t myPSA polarsurfacearea test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="psa"><h3>psa</h3></a>
	<p>Topological Polar Surface Area calculation (2D).</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-S,&nbsp;--excludesulfur&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude sulfur atom from calculation
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-P,&nbsp;--excludephosphorus&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude phosphorus atom from calculation
(default: true)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc -S -t myPSA psa test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="randicindex"><h3>randicindex</h3></a>
	<p>The Randic index.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc randicindex test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="ringatom"><h3>ringatom</h3></a>
	<p>Checks if a specified atom is a ring atom.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc ringatom test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="ringatomcount"><h3>ringatomcount</h3></a>
	<p>Ring atom count.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc ringatomcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="ringbond"><h3>ringbond</h3></a>
	<p>Checks if the bond is a ring bond.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--atoms&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[&lt;atom1&gt;-&lt;atom2&gt;] (1-based) atom indexes of the bond atoms</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc ringbond -a 2-3 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="ringbondcount"><h3>ringbondcount</h3></a>
	<p>Ring bond count.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc ringbondcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="ringcount"><h3>ringcount</h3></a>
	<p>Ring count.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc ringcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="ringcountofatom"><h3>ringcountofatom</h3></a>
	<p>Ring counts of atoms.</p>
	<h5>Options:</h5>
No options	<h5>Example:</h5>
	<pre>cxcalc ringcountofatom test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="ringcountofsize"><h3>ringcountofsize</h3></a>
	<p>Ring count of size.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a&nbsp;--arom&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[general|basic|loose] sets aromatization method</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-z,&nbsp;--size&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;ring size&gt; size of rings to count</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc ringcountofsize -z 5 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="ringsystemcount"><h3>ringsystemcount</h3></a>
	<p>The number of ring systems.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc ringsystemcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="ringsystemcountofsize"><h3>ringsystemcountofsize</h3></a>
	<p>Ring system count of size.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-z,&nbsp;--size&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;size&gt; size of ring systems to count</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc ringsystemcountofsize -z 3 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="rotatablebond"><h3>rotatablebond</h3></a>
	<p>Checks if the bond is a rotatable bond.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--atoms&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[&lt;atom1&gt;-&lt;atom2&gt;] (1-based) atom indexes of the bond atoms</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc rotatablebond -a 2-3 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="rotatablebondcount"><h3>rotatablebondcount</h3></a>
	<p>Rotatable bond count.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc rotatablebondcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="shortestpath"><h3>shortestpath</h3></a>
	<p>Length of shortest path between two atoms.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--atoms&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[&lt;atom1&gt;-&lt;atom2&gt;] (1-based) atom indexes of the atom pair</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc shortestpath -a 2-3 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="smallestatomringsize"><h3>smallestatomringsize</h3></a>
	<p>Size of smallest ring containing a specified atom.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc smallestatomringsize test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="smallestringsize"><h3>smallestringsize</h3></a>
	<p>Smallest ring size.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc smallestringsize test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="smallestringsystemsize"><h3>smallestringsystemsize</h3></a>
	<p>Smallest ring system size.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes smallest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc smallestringsystemsize test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="stereodoublebondcount"><h3>stereodoublebondcount</h3></a>
	<p>The number of stereo double bonds.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc stereodoublebondcount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="stericeffectindex"><h3>stericeffectindex</h3></a>
	<p>Steric effect index.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc stericeffectindex test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="sterichindrance"><h3>sterichindrance</h3></a>
	<p>Steric hindrance.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-o,&nbsp;--optimization&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[0|1|2|3] conformer generation optimiztaion limit for
different enviroments
{0}: very loose  (limit=0.01)
{1}: normal      (limit=0.0010)
{2}: strict      (limit=1.0E-4)
{3}: very strict (limit=1.0E-5)
(default: 1)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--calcforleconformer&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[if2D|never|always] (default: if2D)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc sterichindrance test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="szegedindex"><h3>szegedindex</h3></a>
	<p>Szeged index.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc szegedindex test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="topanal"><h3>topanal</h3></a>
	<p>Molecule topology data calculation: atomcount,aliphaticatomcount, aromaticatomcount,bondcount,aliphaticbondcount,aromaticbondcount, rotatablebondcount,ringcount,aliphaticringcount,aromaticringcount, heteroringcount,heteroaliphaticringcount,heteroaromaticringcount, ringatomcount,ringbondcount,chainatomcount,chainbondcount, smallestringsize,largestringsize.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a&nbsp;--arom&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[general|basic|loose] sets aromatization method</td>
	</tr>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--type&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[atomcount|aliphaticatomcount|aromaticatomcount|
bondcount|aliphaticbondcount|aromaticbondcount|
rotatablebondcount|ringcount|aliphaticringcount|
aromaticringcount|heteroringcount|heteroaliphaticringcount|
heteroaromaticringcount|ringatomcount|ringbondcount|
chainatomcount|chainbondcount|
smallestringsize|largestringsize] (default: all)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc topanal test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="topologyanalysistable"><h3>topologyanalysistable</h3></a>
	<p>Molecule topology data calculation: atomcount,aliphaticatomcount, aromaticatomcount,bondcount,aliphaticbondcount,aromaticbondcount, rotatablebondcount,ringcount,aliphaticringcount,aromaticringcount, heteroringcount,heteroaliphaticringcount,heteroaromaticringcount, ringatomcount,ringbondcount,chainatomcount,chainbondcount, smallestringsize,largestringsize.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a&nbsp;--arom&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[general|basic|loose] sets aromatization method</td>
	</tr>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--type&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[atomcount|aliphaticatomcount|aromaticatomcount|
bondcount|aliphaticbondcount|aromaticbondcount|
rotatablebondcount|ringcount|aliphaticringcount|
aromaticringcount|heteroringcount|heteroaliphaticringcount|
heteroaromaticringcount|ringatomcount|ringbondcount|
chainatomcount|chainbondcount|
smallestringsize|largestringsize] (default: all)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc topologyanalysistable test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="vdwsa"><h3>vdwsa</h3></a>
	<p>Van der Waals Surface Area calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-i,&nbsp;--increments&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] show incremental surface area on atoms
(default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc vdwsa -H 7.4 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="volume"><h3>volume</h3></a>
	<p>Calculates the van der Waals volume of the molecule.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-o,&nbsp;--optimization&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[0|1|2|3] conformer generation optimiztaion limit
for different enviroments
{0}: very loose  (limit=0.01)
{1}: normal      (limit=0.0010)
{2}: strict      (limit=1.0E-4)
{3}: very strict (limit=1.0E-5)
(default: 1)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--calcforleconformer&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[if2D|never|always] (default: if2D)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc volume test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="wateraccessiblesurfacearea"><h3>wateraccessiblesurfacearea</h3></a>
	<p>Water Accessible Surface Area calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-r,&nbsp;--solventradius&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;solvent radius: 0.0-5.0&gt; (default: 1.4)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-i,&nbsp;--increments&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] show incremental surface area on atoms
(default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc wateraccessiblesurfacearea test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="wienerindex"><h3>wienerindex</h3></a>
	<p>Wiener index.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc wienerindex test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="wienerpolarity"><h3>wienerpolarity</h3></a>
	<p>Wiener polarity.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--single&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] in case of multi-fragment molecules:
takes largest fragment if true,
takes whole molecule if false (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc wienerpolarity test.mol</pre>
<p>&nbsp;</p>
<hr />
<a class="anchor" name="Isomers"><h2>Isomers</h2></a>
	<a class="anchor" name="canonicaltautomer"><h3>canonicaltautomer</h3></a>
	<p>Canonical tautomer.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt;
(default: smiles table, multiple
molecule output if specified)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-R,&nbsp;&nbsp;--rational&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: generates only rational
tautomers (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--protectaromaticity&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect aromaticity
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-C,&nbsp;--protectcharge&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect charge (default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-e,&nbsp;--excludeantiaroma&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: exclude antiaromatic compounds
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-P,&nbsp;--protectdoublebondstereo&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect double bond stereo
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-T,&nbsp;--protectalltetrahedralcenters&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect all tetrahedral stereo
centers (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-L,&nbsp;--protectlabeledtetrahedralcenters&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect labeled tetrahedral
stereo centers (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-E,&nbsp;--protectestergroups&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect ester groups
(default: true)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc canonicaltautomer -f sdf test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="dominanttautomerdistribution"><h3>dominanttautomerdistribution</h3></a>
	<p>Dominant tautomer distribution.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number
of fractional digits: 0-8 or inf&gt;
 (default: 0)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--pathlength&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;length&gt; maximum allowed length of the
tautomerization path in chemical bonds
(default: 4)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; considers pH effect at
this pH. (default: do not consider
pH effect)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--protectaromaticity&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect aromaticity
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-C,&nbsp;--protectcharge&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect charge (default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-e,&nbsp;--excludeantiaroma&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: exclude antiaromatic compounds
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-P,&nbsp;--protectdoublebondstereo&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect double bond stereo
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-T,&nbsp;--protectalltetrahedralcenters&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect all tetrahedral stereo
centers (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-L,&nbsp;--protectlabeledtetrahedralcenters&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect labeled tetrahedral
stereo centers (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-E,&nbsp;--protectestergroups&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect ester groups
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt; (default: sdf:-a)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--tag&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;SDF/MRV tag to store
the distribution value&gt;
(default: TAUTOMER_DISTRIBUTION)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc dominanttautomerdistribution test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="doublebondstereoisomercount"><h3>doublebondstereoisomercount</h3></a>
	<p>The number of double-bond stereoisomers of the molecule.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--maxstereoisomers&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;maximum number of double bond stereoisomers
to be generated&gt; (default: 1000)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-D,&nbsp;--protectdoublebondstereo&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect double bond stereo
(default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc doublebondstereoisomercount test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="doublebondstereoisomers"><h3>doublebondstereoisomers</h3></a>
	<p>Generates double-bond stereoisomers of the molecule.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt; (default: sdf)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--maxstereoisomers&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;maximum number of double bond stereoisomers
to be generated&gt; (default: 1000)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-D,&nbsp;--protectdoublebondstereo&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect double bond stereo
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-v,&nbsp;--verify3d&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] if true invalid 3D structures of
genereated stereoisomers are filtered</td>
	</tr>
	<tr>
		<th align="left" valign="top">-3,&nbsp;--in3d&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] if true 3D structures are
generated (invalid 3D structures are filtered)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc doublebondstereoisomers -f mrv test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="generictautomer"><h3>generictautomer</h3></a>
	<p>Generic tautomer.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt;
(default: smiles table, multiple
molecule output if specified)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--pathlength&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;length&gt; maximum allowed length of
the tautomerization path in chemical
bonds (default: 4)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--protectaromaticity&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect aromaticity
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-C,&nbsp;--protectcharge&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect charge (default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-e,&nbsp;--excludeantiaroma&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: exclude antiaromatic compounds
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-P,&nbsp;--protectdoublebondstereo&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect double bond stereo
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-T,&nbsp;--protectalltetrahedralcenters&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect all tetrahedral stereo
centers (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-L,&nbsp;--protectlabeledtetrahedralcenters&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect labeled tetrahedral
stereo centers (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-E,&nbsp;--protectestergroups&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect ester groups
(default: true)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc generictautomer -f sdf test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="majortautomer"><h3>majortautomer</h3></a>
	<p>Major tautomer.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt;
(default: smiles table, multiple
molecule output if specified)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--pathlength&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;length&gt; maximum allowed length of
the tautomerization path in chemical
bonds (default: 4)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; considers pH effect at
this pH. (default: do not consider
pH effect)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--protectaromaticity&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect aromaticity
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-C,&nbsp;--protectcharge&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect charge (default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-e,&nbsp;--excludeantiaroma&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: exclude antiaromatic compounds
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-P,&nbsp;--protectdoublebondstereo&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect double bond stereo
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-T,&nbsp;--protectalltetrahedralcenters&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect all tetrahedral stereo
centers (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-L,&nbsp;--protectlabeledtetrahedralcenters&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect labeled tetrahedral
stereo centers (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-E,&nbsp;--protectestergroups&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect ester groups
(default: true)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc majortautomer -H 7.4 -f sdf test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="moststabletautomer"><h3>moststabletautomer</h3></a>
	<p>Most stable tautomer. Depreacated, use "majortautomer" instead.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt;
(default: smiles table, multiple
molecule output if specified)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--pathlength&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;length&gt; maximum allowed length of
the tautomerization path in chemical
bonds (default: 4)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--protectaromaticity&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect aromaticity
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-C,&nbsp;--protectcharge&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect charge (default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-e,&nbsp;--excludeantiaroma&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: exclude antiaromatic compounds
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-P,&nbsp;--protectdoublebondstereo&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect double bond stereo
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-T,&nbsp;--protectalltetrahedralcenters&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect all tetrahedral stereo
centers (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-L,&nbsp;--protectlabeledtetrahedralcenters&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect labeled tetrahedral
stereo centers (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-E,&nbsp;--protectestergroups&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect ester groups
(default: true)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc moststabletautomer -f sdf test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="stereoisomercount"><h3>stereoisomercount</h3></a>
	<p>The number of stereoisomers of the molecule.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--maxstereoisomers&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;maximum number of double bond stereoisomers
to be generated&gt; (default: 1000)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-D,&nbsp;--protectdoublebondstereo&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect double bond stereo
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-T,&nbsp;--protecttetrahedralstereo&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect tetrahedral stereo centers
(default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc stereoisomercount test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="stereoisomers"><h3>stereoisomers</h3></a>
	<p>Generates stereoisomers of the molecule.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt; (default: sdf)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--maxstereoisomers&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;maximum number of stereoisomers to be
generated&gt; (default: 1000)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-D,&nbsp;--protectdoublebondstereo&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect double bond stereo
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-T,&nbsp;--protecttetrahedralstereo&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect tetrahedral stereo centers
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-v,&nbsp;--verify3d&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] if true invalid 3D structures of
genereated stereoisomers are filtered</td>
	</tr>
	<tr>
		<th align="left" valign="top">-3,&nbsp;--in3d&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] if true 3D structures are
generated (invalid 3D structures are filtered)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc stereoisomers -v true test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="tautomercount"><h3>tautomercount</h3></a>
	<p>The number of tautomers.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-d,&nbsp;--dominants&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: take dominant tautomers
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-R,&nbsp;&nbsp;--rational&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: takes only rational tautomers
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--max&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;count&gt; max. number of structures to
be generated (default: 200)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--pathlength&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;length&gt; maximum allowed length of the
tautomerization path in chemical bonds</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; considers pH effect at this
pH. Only has effect when dominant
tautomers are generated.
(default: do not consider pH effect)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--protectaromaticity&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect aromaticity
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-C,&nbsp;--protectcharge&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect charge (default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-e,&nbsp;--excludeantiaroma&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: exclude antiaromatic compounds
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--symfilter&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: filter out symmetrical
structures
false: allow duplicates
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-P,&nbsp;--protectdoublebondstereo&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect double bond stereo
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-T,&nbsp;--protectalltetrahedralcenters&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect all tetrahedral stereo
centers (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-L,&nbsp;--protectlabeledtetrahedralcenters&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect labeled tetrahedral
stereo centers (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-E,&nbsp;--protectestergroups&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect ester groups
(default: true)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc tautomerCount -s false test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="tautomers"><h3>tautomers</h3></a>
	<p>Tautomers.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt;
(default: 0)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-c,&nbsp;--canonical&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: take canonical tautomer
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-R,&nbsp;&nbsp;--rational&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: generates only rational
tautomers (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-g,&nbsp;--generic&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: take generic tautomer
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-M,&nbsp;--major&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: take major tautomer
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-d,&nbsp;--dominants&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: take dominant tautomers
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-D,&nbsp;--distribution&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: calculate dominant tautomer
distribution (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--max&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;count&gt; maximum number of structures
to be generated (default: 200)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--pathlength&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;length&gt; maximum allowed length of the
tautomerization path in chemical bonds
(default: 4)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; considers pH effect at this
pH. Only has effect when dominant
tautomers are generated.
(default: do not consider pH effect)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--protectaromaticity&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect aromaticity
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-C,&nbsp;--protectcharge&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect charge (default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-e,&nbsp;--excludeantiaroma&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: exclude antiaromatic compounds
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-P,&nbsp;--protectdoublebondstereo&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect double bond stereo
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-T,&nbsp;--protectalltetrahedralcenters&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect all tetrahedral stereo
centers (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-L,&nbsp;--protectlabeledtetrahedralcenters&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect labeled tetrahedral
stereo centers (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-E,&nbsp;--protectestergroups&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect ester groups
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--symfilter&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: filter out symmetrical
structures false: allow duplicates
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt;
(default: fused smiles,
multiple molecule output if specified)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--tag&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;SDF/MRV tag to store the
distribution value&gt;
(default: TAUTOMER_DISTRIBUTION)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-r,&nbsp;--ring&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
Enable/disable ring tautomers.
Default false.</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc tautomers -f sdf test.mol
  cxcalc tautomers --dominants false --rational true test.mol --format smiles</pre>
<p>&nbsp;</p>
	<a class="anchor" name="tetrahedralstereoisomercount"><h3>tetrahedralstereoisomercount</h3></a>
	<p>The number of tetrahedral stereoisomers of the molecule.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--maxstereoisomers&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;maximum number of double bond stereoisomers
to be generated&gt; (default: 1000)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-T,&nbsp;--protecttetrahedralstereo&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect tetrahedral stereo centers
(default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc tetrahedralstereoisomercount test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="tetrahedralstereoisomers"><h3>tetrahedralstereoisomers</h3></a>
	<p>Generates tetrahedral stereoisomers of the molecule.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt; (default: sdf)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--maxstereoisomers&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;maximum number of tetrahedral stereoisomers
to be generated&gt; (default: 1000)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-T,&nbsp;--protecttetrahedralstereo&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: protect tetrahedral stereo centers
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-v,&nbsp;--verify3d&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] if true invalid 3D structures of
genereated stereoisomers are filtered</td>
	</tr>
	<tr>
		<th align="left" valign="top">-3,&nbsp;--in3d&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] if true 3D structures are
generated (invalid 3D structures are filtered)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc tetrahedralstereoisomers -3 true test.sdf</pre>
<p>&nbsp;</p>
<hr />
<a class="anchor" name="Markush_Enumerations"><h2>Markush&nbsp;Enumerations</h2></a>
	<a class="anchor" name="enumerationcount"><h3>enumerationcount</h3></a>
	<p>Number of Markush enumerated structures.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--atoms&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[atom1,atom2,atom3,...]
(1-based) atom indexes of the atoms
to be enumerated (default: all)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--magnitude&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] display magnitude if &gt;= 100 000
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-g,&nbsp;--enumhomology&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] enumerate homology groups (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc enumerationcount -m true test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="enumerations"><h3>enumerations</h3></a>
	<p>Markush enumerated structures.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--max&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;count&gt; max. number of structures to be generated
(default: all)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-v,&nbsp;--valencecheck&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] valence filter is on if true
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--atoms&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[atom1,atom2,atom3,...]
(1-based) atom indexes of the atoms
to be enumerated (default: all)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--alignscaffold&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] align scaffold (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-c,&nbsp;--coloring&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[none|all|scaffold|rgroups] structure coloring
(default: none)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-r,&nbsp;--random&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] random enumeration
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-g,&nbsp;--enumhomology&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] enumerate homology groups (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-o,&nbsp;--code&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] generate Markush code (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-i,&nbsp;--structureid&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[id or tag name]
structure ID or SDF/MRV tag name storing the ID
(default: no structure ID)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt; (default: concatenated smiles)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-C,&nbsp;--clean&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;dim[:opts]&gt; clean dimension with options
(default: no clean)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc enumerations -f sdf -C 2:t3000 -a 2,3,5 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="markushenumerationcount"><h3>markushenumerationcount</h3></a>
	<p>Number of Markush enumerated structures.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--atoms&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[atom1,atom2,atom3,...]
(1-based) atom indexes of the atoms
to be enumerated (default: all)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--magnitude&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] display magnitude if &gt;= 100 000
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-g,&nbsp;--enumhomology&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] enumerate homology groups (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc markushenumerationcount -m true test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="markushenumerations"><h3>markushenumerations</h3></a>
	<p>Markush enumerated structures.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--max&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;count&gt; max. number of structures to be generated
(default: all)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-v,&nbsp;--valencecheck&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] valence filter is on if true
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--atoms&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[atom1,atom2,atom3,...]
(1-based) atom indexes of the atoms
to be enumerated (default: all)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--alignscaffold&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] align scaffold (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-c,&nbsp;--coloring&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[none|all|scaffold|rgroups] structure coloring
(default: none)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-r,&nbsp;--random&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] random enumeration
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-g,&nbsp;--enumhomology&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] enumerate homology groups (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-o,&nbsp;--code&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] generate Markush code (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-i,&nbsp;--structureid&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[id or tag name]
structure ID or SDF/MRV tag name storing the ID
(default: no structure ID)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt; (default: concatenated smiles)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-C,&nbsp;--clean&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;dim[:opts]&gt; clean dimension with options
(default: no clean)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc markushenumerations -f sdf -C 2:t3000 -a 2,3,5 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="randommarkushenumerations"><h3>randommarkushenumerations</h3></a>
	<p>Randomly constructed Markush enumerated structures.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--max&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;count&gt; max. number of structures to be generated
(default: all)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-v,&nbsp;--valencecheck&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] valence filter is on if true
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--atoms&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[atom1,atom2,atom3,...]
(1-based) atom indexes of the atoms
to be enumerated (default: all)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--alignscaffold&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] align scaffold (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-c,&nbsp;--coloring&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[none|all|scaffold|rgroups] structure coloring
(default: none)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-g,&nbsp;--enumhomology&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] enumerate homology groups (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-o,&nbsp;--code&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] generate Markush code (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-i,&nbsp;--structureid&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[id or tag name]
structure ID or SDF/MRV tag name storing the ID
(default: no structure ID)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt; (default: concatenated smiles)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-C,&nbsp;--clean&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;dim[:opts]&gt; clean dimension with options
(default: no clean)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc randommarkushenumerations -f sdf -C 2:t5000 test.mol</pre>
<p>&nbsp;</p>
<hr />
<a class="anchor" name="Name"><h2>Name</h2></a>
	<a class="anchor" name="name"><h3>name</h3></a>
	<p>Generates the IUPAC name of the molecule.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--type&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[preferred|traditional] (default: preferred)
preferred: Preferred IUPAC Name
traditional: traditional name</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc name test.sdf</pre>
<p>&nbsp;</p>
<hr />
<a class="anchor" name="Partitioning"><h2>Partitioning</h2></a>
	<a class="anchor" name="logd"><h3>logd</h3></a>
	<p>logD calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--method&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[vg|klop|phys|user|weighted]
(default: weighted)</td>
	</tr>
	<tr>
		<th align="left" valign="top">&nbsp;&nbsp;&nbsp;&nbsp;--logptrainingid&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;logP training id&gt;
</td>
	</tr>
	<tr>
		<th align="left" valign="top">-w,&nbsp;--weights&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;wVG:wKLOP:wPHYS:wUSER&gt; method weights
(default: 1:1:1:0)
wVG: weight of the VG method
wKLOP: weight of the KLOP method
wPHYS: weight of the PHYS method
wUSER: weight of the user defined method</td>
	</tr>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--anion&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;Cl- concentration&gt;
(default: 0.1, range: [0.0, 0.25])</td>
	</tr>
	<tr>
		<th align="left" valign="top">-k,&nbsp;--kation&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;Na+ K+ concentration&gt;
(default: 0.1, range: [0.0, 0.25])</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes logD at this pH
(default: no single pH, takes pH values in
interval [lower, upper] by given step size)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--lower&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH lower limit&gt; (default: 0)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-u,&nbsp;--upper&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH upper limit&gt; (default: 14)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--step&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH step size&gt; (default: 1)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-1,&nbsp;--ref1&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH reference 1&gt; (default: none)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-2,&nbsp;--ref2&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH reference 2&gt; (default: none)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-3,&nbsp;--ref3&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH reference 3&gt; (default: none)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-4,&nbsp;--ref4&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH reference 4&gt; (default: none)</td>
	</tr>
	<tr>
		<th align="left" valign="top">&nbsp;&nbsp;&nbsp;&nbsp;--considertautomerization&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] consider tautomerization
and resonance(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">&nbsp;&nbsp;&nbsp;&nbsp;--pkacorrectionlibrary&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pKa correction library ID&gt;</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc -i ID logd -l 2 -u 3 -s 0.5 test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="logp"><h3>logp</h3></a>
	<p>logP calculation: for type logPTrue: logP of uncharged species, or, in the case of zwitterions, logD at pI; for type logPMicro: logP of the input species.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--method&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[vg|klop|phys|user|weighted]
(default: weighted)</td>
	</tr>
	<tr>
		<th align="left" valign="top">&nbsp;&nbsp;&nbsp;&nbsp;--trainingid&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;training id&gt;
</td>
	</tr>
	<tr>
		<th align="left" valign="top">-w,&nbsp;--weights&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;wVG:wKLOP:wPHYS:wUSER&gt; method weights
(default: 1:1:1:0)
wVG: weight of the VG method
wKLOP: weight of the KLOP method
wPHYS: weight of the PHYS method
wUSER: weight of the user defined method</td>
	</tr>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--anion&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;Cl- concentration&gt;
(default: 0.1, range: [0.0, 0.25])</td>
	</tr>
	<tr>
		<th align="left" valign="top">-k,&nbsp;--kation&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;Na+ K+ concentration&gt;
(default: 0.1, range: [0.0, 0.25])</td>
	</tr>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--type&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[increments|logPMicro|logPTrue]
(default: logPTrue)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-i,&nbsp;--increments&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] show atomic increments
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">&nbsp;&nbsp;&nbsp;&nbsp;--considertautomerization&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] consider tautomerization
and resonance (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; gets logp of the major
microspecies at this pH (default:
no pH, use given protonation state)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc -S -t myLOGP logp -a 0.15 -k 0.05 test.mol</pre>
<p>&nbsp;</p>
<hr />
<a class="anchor" name="Predictor"><h2>Predictor</h2></a>
	<a class="anchor" name="predict"><h3>predict</h3></a>
	<p>Predicts molecular properties.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-I,&nbsp;--trainingid&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;training id&gt; sets the training</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc predict --trainingid pampa test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="predictor"><h3>predictor</h3></a>
	<p>Predicts molecular properties.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-I,&nbsp;--trainingid&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;training id&gt; sets the training</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc predictor --trainingid pampa test.mol</pre>
<p>&nbsp;</p>
<hr />
<a class="anchor" name="Protonation"><h2>Protonation</h2></a>
	<a class="anchor" name="averagemicrospeciescharge"><h3>averagemicrospeciescharge</h3></a>
	<p>Average microspecies charge calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; calculates average charge at this pH
(default: 7.4)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc averagemicrospeciescharge test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="chargedistribution"><h3>chargedistribution</h3></a>
	<p>Charge distribution calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; calculates average charge at this pH
(default: no single pH, takes pH values in
interval [lower, upper] by given step size)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--lower&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH lower limit&gt; (default: 0)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-u,&nbsp;--upper&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH upper limit&gt; (default: 14)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--step&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH step size&gt; (default: 1)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc chargedistribution test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="isoelectricpoint"><h3>isoelectricpoint</h3></a>
	<p>Isoelectric point calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc isoelectricpoint test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="majormicrospecies"><h3>majormicrospecies</h3></a>
	<p>Major microspecies at given pH.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; gets major microspecies at this pH
(default: no pH, all microspecies)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt; (default: smiles)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-M,&nbsp;--majortautomer&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] take major tautomeric form
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-K,&nbsp;--keephydrogens&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
keep explicit hydrogen on result molecule
(default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc majormicrospecies -H 3.5 -f mol test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="majorms"><h3>majorms</h3></a>
	<p>Major microspecies at given pH.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; gets major microspecies at this pH
(default: no pH, all microspecies)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt; (default: smiles)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-M,&nbsp;--majortautomer&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] take major tautomeric form
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-K,&nbsp;--keephydrogens&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
keep explicit hydrogen on result molecule
(default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc majorms -H 3.5 -f mol test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="microspeciesdistribution"><h3>microspeciesdistribution</h3></a>
	<p>Microspecies list with distributions at given pH.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; gets major microspecies at this pH
(default: 7.4)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt; (default: sdf:-a)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--tag&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;SDF/MRV tag to store the distribution value&gt;
(default: MSDISTR[pH=...])</td>
	</tr>
	<tr>
		<th align="left" valign="top">-M,&nbsp;--majortautomer&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] take major tautomeric form
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-K,&nbsp;--keephydrogens&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
keep explicit hydrogen on result molecule
(default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc microspeciesdistribution -H 3.5 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="msdistr"><h3>msdistr</h3></a>
	<p>Microspecies list with distributions at given pH.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; gets major microspecies at this pH
(default: 7.4)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt; (default: sdf:-a)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--tag&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;SDF/MRV tag to store the distribution value&gt;
(default: MSDISTR[pH=...])</td>
	</tr>
	<tr>
		<th align="left" valign="top">-M,&nbsp;--majortautomer&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] take major tautomeric form
(default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-K,&nbsp;--keephydrogens&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
keep explicit hydrogen on result molecule
(default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc msdistr -H 3.5 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="pi"><h3>pi</h3></a>
	<p>Isoelectric point calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc pI test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="pka"><h3>pka</h3></a>
	<p>pKa calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--type&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[pKa|acidic|basic] (default: pKa)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--mode&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[macro|micro] (default: macro)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-P,&nbsp;--prefix&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[static|dynamic] (default: static)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-d,&nbsp;--model&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[small|large] calculation model
small: optimized for at most 8 ionizable atoms
large: optimized for a large number of
ionizable atoms (default: small)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-i,&nbsp;--min&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;min basic pKa&gt; (default: -10)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-x,&nbsp;--max&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;max acidic pKa&gt; (default: 20)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-T,&nbsp;--temperature&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;temperature in Kelvin&gt; (default: 298 K)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-a,&nbsp;--na&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;number of acidic pKa values displayed&gt;
(default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-b,&nbsp;--nb&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;number of basic pKa values displayed&gt;
(default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">&nbsp;&nbsp;&nbsp;&nbsp;--considertautomerization&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] consider tautomerization 
and resonance (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-L,&nbsp;--correctionlibrary&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;correction library ID&gt;</td>
	</tr>
	<tr>
		<th align="left" valign="top">-P,&nbsp;--correctionlibrarypath&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;path of the correction library&gt;
use this parameter when the correction library
not stored on the default location</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc pka -i -15 -x 25 -a 3 -b 3 -d large test.mol</pre>
<p>&nbsp;</p>
<hr />
<a class="anchor" name="Other"><h2>Other</h2></a>
	<a class="anchor" name="acc"><h3>acc</h3></a>
	<p>Hydrogen bond acceptor multiplicity calculation on atoms.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule</td>
	</tr>
	<tr>
		<th align="left" valign="top">-e,&nbsp;--excludesulfur&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude sulfur atom from acceptors
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-x,&nbsp;--excludehalogens&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude halogens from acceptors
(default: true)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc acc test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="acceptor"><h3>acceptor</h3></a>
	<p>Hydrogen bond acceptor calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-e,&nbsp;--excludesulfur&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude sulfur atom from acceptors
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-x,&nbsp;--excludehalogens&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude halogens from acceptors
(default: true)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc acceptor test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="acceptorcount"><h3>acceptorcount</h3></a>
	<p>Hydrogen bond acceptor atom count in molecule.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-e,&nbsp;--excludesulfur&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude sulfur atom from acceptors
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-x,&nbsp;--excludehalogens&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude halogens from acceptors
(default: true)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc acceptorcount -H 7.4 test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="acceptormultiplicity"><h3>acceptormultiplicity</h3></a>
	<p>Hydrogen bond acceptor multiplicity calculation on atoms.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-e,&nbsp;--excludesulfur&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude sulfur atom from acceptors
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-x,&nbsp;--excludehalogens&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude halogens from acceptors
(default: true)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc acceptormultiplicity test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="acceptorsitecount"><h3>acceptorsitecount</h3></a>
	<p>Hydrogen bond acceptor multiplicity in molecule.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-e,&nbsp;--excludesulfur&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude sulfur atom from acceptors
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-x,&nbsp;--excludehalogens&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude halogens from acceptors
(default: true)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc acceptorsitecount test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="acceptortable"><h3>acceptortable</h3></a>
	<p>Hydrogen bond acceptor calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-e,&nbsp;--excludesulfur&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude sulfur atom from acceptors
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-x,&nbsp;--excludehalogens&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude halogens from acceptors
(default: true)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc acceptortable test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="accsitecount"><h3>accsitecount</h3></a>
	<p>Hydrogen bond acceptor multiplicity in molecule.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-e,&nbsp;--excludesulfur&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude sulfur atom from acceptors
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-x,&nbsp;--excludehalogens&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude halogens from acceptors
(default: true)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc accsitecount test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="aromaticelectrophilicityorder"><h3>aromaticelectrophilicityorder</h3></a>
	<p>Order in E(+) attack. Deprecated.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc aromaticelectrophilicityorder -H 7.4 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="aromaticnucleophilicityorder"><h3>aromaticnucleophilicityorder</h3></a>
	<p>Order in Nu(-) attack. Deprecated.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc aromaticnucleophilicityorder -H 7.4 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="canonicalresonant"><h3>canonicalresonant</h3></a>
	<p>Canonical resonant structure.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt; (default: smiles)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc canonicalResonant -f sdf test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="chargedensity"><h3>chargedensity</h3></a>
	<p>Charge density.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc chargedensity -p 4 -H 6.5 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="don"><h3>don</h3></a>
	<p>Hydrogen bond donor multiplicity calculation on atoms.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc don test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="donor"><h3>donor</h3></a>
	<p>Hydrogen bond donor calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc donor test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="donorcount"><h3>donorcount</h3></a>
	<p>Hydrogen bond donor atom count in molecule.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc donorcount -H 7.4 test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="donormultiplicity"><h3>donormultiplicity</h3></a>
	<p>Hydrogen bond donor multiplicity calculation on atoms.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc don test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="donorsitecount"><h3>donorsitecount</h3></a>
	<p>Hydrogen bond donor multiplicity in molecule.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc donorsitecount test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="donortable"><h3>donortable</h3></a>
	<p>Hydrogen bond donor calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc donortable test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="donsitecount"><h3>donsitecount</h3></a>
	<p>Hydrogen bond donor multiplicity in molecule.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc donsitecount test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="electrondensity"><h3>electrondensity</h3></a>
	<p>Electron density.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc electrondensity -p 4 -H 6.5 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="electrophilicityorder"><h3>electrophilicityorder</h3></a>
	<p>Order in E(+) attack. Deprecated.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc electrophilicityorder -H 7.4 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="electrophiliclocalizationenergy"><h3>electrophiliclocalizationenergy</h3></a>
	<p>Electrophilic localization energy L(+).</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc electrophiliclocalizationenergy test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="frameworks"><h3>frameworks</h3></a>
	<p>Calculates different structural frameworks (Bemis-Murcko, MCS, etc) of the molecule</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--type&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[bmf|bmfl|mcs|largestring|allringsystems|
largestringsystem|sssr|cssr|keep] Framework type
to calculate</td>
	</tr>
	<tr>
		<th align="left" valign="top">-i,&nbsp;--lfin&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] Process only the largest fragment
of input structure (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--prunein&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] Prune input: generalize input atom
and bond types (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-h,&nbsp;--hydrogenize&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|fase] Add explicit hydrogens to the input
structure (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-d,&nbsp;--dehydrogenize&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] Remove explicit hydrogens from the
input structure (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-r,&nbsp;--pruneout&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] Prune results: generalize result
atom and bond types (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-o,&nbsp;--lfout&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] Return only the largest fragment of
the result (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-q,&nbsp;--oeqcheck&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] Remove topologically equivalent
output fragments (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--keepsingleatom&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] Return a single atom for non-empty
acyclic input structures (default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt; (default: sdf)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc frameworks -t bmf -s true test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="hbda"><h3>hbda</h3></a>
	<p>Hydrogen bond acceptor-donor calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--type&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[acc|don|accsitecount|donsitecount|
acceptorcount|donorcount|msacc|msdon]
(default: acceptorcount,donorcount,accsitecount,
donsitecount)
acc: acceptor multiplicity on atoms
don: donor multiplicity on atoms
accsitecount: acceptor multiplicity in molecule
donsitecount: donor multiplicity in molecule
acceptorcount: number of acceptor atoms in molecule
donorcount: number of donor atoms in molecule
msacc: average acceptor multiplicity
    over microspecies by pH
msdon: average donor multiplicity
    over microspecies by pH</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--lower&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH lower limit&gt; (default: 0)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-u,&nbsp;--upper&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH upper limit&gt; (default: 14)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--step&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH step size&gt; (default: 1)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule))</td>
	</tr>
	<tr>
		<th align="left" valign="top">-e,&nbsp;--excludesulfur&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude sulfur atom from acceptors
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-x,&nbsp;--excludehalogens&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude halogens from acceptors
(default: true)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc hbda -t "msacc,msdon" -l 2 -u 12 -s 0.5 test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="hbonddonoracceptor"><h3>hbonddonoracceptor</h3></a>
	<p>Hydrogen bond acceptor-donor calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--type&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[acc|don|accsitecount|donsitecount|
acceptorcount|donorcount|msacc|msdon]
(default: acceptorcount,donorcount,accsitecount,
donsitecount)
acc: acceptor multiplicity on atoms
don: donor multiplicity on atoms
accsitecount: acceptor multiplicity in molecule
donsitecount: donor multiplicity in molecule
acceptorcount: number of acceptor atoms in molecule
donorcount: number of donor atoms in molecule
msacc: average acceptor multiplicity
    over microspecies by pH
msdon: average donor multiplicity
    over microspecies by pH</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--lower&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH lower limit&gt; (default: 0)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-u,&nbsp;--upper&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH upper limit&gt; (default: 14)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--step&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH step size&gt; (default: 1)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule))</td>
	</tr>
	<tr>
		<th align="left" valign="top">-e,&nbsp;--excludesulfur&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude sulfur atom from acceptors
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-x,&nbsp;--excludehalogens&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude halogens from acceptors
(default: true)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc hbonddonoracceptor -t "msacc,msdon" -l 2 -u 12 -s 0.5 test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="hmochargedensity"><h3>hmochargedensity</h3></a>
	<p>HMO Charge density.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc hmochargedensity -p 4 -H 6.5 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="hmoelectrondensity"><h3>hmoelectrondensity</h3></a>
	<p>HMO Electron density.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc hmoelectrondensity -p 4 -H 6.5 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="hmoelectrophilicityorder"><h3>hmoelectrophilicityorder</h3></a>
	<p>Order in E(+) attack (HMO).</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc hmoelectrophilicityorder -H 7.4 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="hmoelectrophiliclocalizationenergy"><h3>hmoelectrophiliclocalizationenergy</h3></a>
	<p>HMO Electrophilic localization energy L(+).</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc hmoelectrophiliclocalizationenergy test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="hmohuckel"><h3>hmohuckel</h3></a>
	<p>HMO Huckel analysis parameters.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--type&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[hmoorder|hmoorder:e|hmoorder:n|
hmolocalizationenergy|
hmolocalizationenergy:e|hmolocalizationenergy:n|
hmopienergy|hmoelectrondensity|hmochargedensity]
(default: hmoorder,hmolocalizationenergy,
hmopienergy,hmoelectrondensity,hmochargedensity)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc -S -o result.sdf hmohuckel -H 7.4 -p 3 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="hmohuckeleigenvalue"><h3>hmohuckeleigenvalue</h3></a>
	<p>HMO Huckel eigenvalue.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc hmohuckeleigenvalue test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="hmohuckeleigenvector"><h3>hmohuckeleigenvector</h3></a>
	<p>HMO Huckel eigenvector.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc hmohuckeleigenvector test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="hmohuckelorbitals"><h3>hmohuckelorbitals</h3></a>
	<p>HMO Huckel orbital coefficients.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc hmohuckelorbitals test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="hmohuckeltable"><h3>hmohuckeltable</h3></a>
	<p>HMO Huckel analysis parameters.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--type&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[hmoorder|hmoorder:e|hmoorder:n|
hmolocalizationenergy|
hmolocalizationenergy:e|hmolocalizationenergy:n|
hmopienergy|hmoelectrondensity|hmochargedensity]
(default: hmoorder,hmolocalizationenergy,
hmopienergy,hmoelectrondensity,hmochargedensity)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc -S -o result.sdf hmohuckeltable -H 7.4 -p 3 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="hmolocalizationenergy"><h3>hmolocalizationenergy</h3></a>
	<p>HMO Localization energy L(+)/L(-).</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--subtype&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[e|n|en] e: electrophilic, n: nucleophilic, en: both (default: en)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc hmolocalizationenergy test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="hmonucleophilicityorder"><h3>hmonucleophilicityorder</h3></a>
	<p>Order in Nu(-) attack (HMO).</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc hmonucleophilicityorder -H 7.4 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="hmonucleophiliclocalizationenergy"><h3>hmonucleophiliclocalizationenergy</h3></a>
	<p>HMO Nucleophilic localization energy L(-).</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc hmonucleophiliclocalizationenergy test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="hmopienergy"><h3>hmopienergy</h3></a>
	<p>HMO Pi energy.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc hmopienergy test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="huckel"><h3>huckel</h3></a>
	<p>Huckel analysis parameters.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--type&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[order|order:e|order:n|
localizationenergy|
localizationenergy:e|localizationenergy:n|
pienergy|electrondensity|chargedensity]
(default: order,localizationenergy,
pienergy,electrondensity,chargedensity)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc -S -o result.sdf huckel -H 7.4 -p 3 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="huckeleigenvalue"><h3>huckeleigenvalue</h3></a>
	<p>Huckel eigenvalue. Deprecated.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc huckeleigenvalue test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="huckeleigenvector"><h3>huckeleigenvector</h3></a>
	<p>Huckel eigenvector. Deprecated.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc huckeleigenvector test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="huckelorbitals"><h3>huckelorbitals</h3></a>
	<p>Huckel orbital coefficients. Deprecated.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc huckelorbitals test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="huckeltable"><h3>huckeltable</h3></a>
	<p>Huckel analysis parameters.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--type&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[order|order:e|order:n|
localizationenergy|
localizationenergy:e|localizationenergy:n|
pienergy|electrondensity|chargedensity]
(default: order,localizationenergy,
pienergy,electrondensity,chargedensity)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc -S -o result.sdf huckeltable -H 7.4 -p 3 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="localizationenergy"><h3>localizationenergy</h3></a>
	<p>Localization energy L(+)/L(-).</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--subtype&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[e|n|en] e: electrophilic, n: nucleophilic, en: both (default: en)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc localizationenergy test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="msacc"><h3>msacc</h3></a>
	<p>Hydrogen bond acceptor average multiplicity over microspecies by pH.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--lower&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH lower limit&gt; (default: 0)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-u,&nbsp;--upper&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH upper limit&gt; (default: 14)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--step&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH step size&gt; (default: 1)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-e,&nbsp;--excludesulfur&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude sulfur atom from acceptors
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-x,&nbsp;--excludehalogens&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] exclude halogens from acceptors
(default: true)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc msacc test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="msdon"><h3>msdon</h3></a>
	<p>Hydrogen bond donor average multiplicity over microspecies by pH.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-l,&nbsp;--lower&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH lower limit&gt; (default: 0)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-u,&nbsp;--upper&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH upper limit&gt; (default: 14)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--step&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH step size&gt; (default: 1)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc msdon test.sdf</pre>
<p>&nbsp;</p>
	<a class="anchor" name="nucleophilicityorder"><h3>nucleophilicityorder</h3></a>
	<p>Order in Nu(-) attack. Deprecated.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc nucleophilicityorder -H 7.4 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="nucleophiliclocalizationenergy"><h3>nucleophiliclocalizationenergy</h3></a>
	<p>Nucleophilic localization energy L(-).</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc nucleophiliclocalizationenergy test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="pichargedensity"><h3>pichargedensity</h3></a>
	<p>Pi charge density. Deprecated, use "electrondensity" calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc pichargedensity -p 4 -H 6.5 test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="pienergy"><h3>pienergy</h3></a>
	<p>Pi energy. Deprecated.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc pienergy test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="refractivity"><h3>refractivity</h3></a>
	<p>Refractivity calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-t,&nbsp;--type&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[increments|inch|refractivity] (default: refractivity)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-i,&nbsp;--inch&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false] refractivity on H atoms shown in brackets
(for incremental refractivity only) (default: false)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc refractivity -p 3 -t refractivity,increments test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="resonantcount"><h3>resonantcount</h3></a>
	<p>The number of resonant structures.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-r,&nbsp;--mcontrib&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: take major contributors
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--max&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;count&gt; max. number of structures to be generated
(default: 200)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--symfilter&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: filter out symmetrical structures
false: allow duplicates
(default: true)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc resonantCount test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="resonants"><h3>resonants</h3></a>
	<p>Resonant structures.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-c,&nbsp;--canonical&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: take canonical resonant form (default: false)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-r,&nbsp;--mcontrib&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: take major contributors
(default: true)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-m,&nbsp;--max&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;count&gt; max. number of structures to be generated
(default: 200)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-f,&nbsp;--format&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;output format&gt; (default: fused smiles,
multiple molecule output if specified)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-s,&nbsp;--symfilter&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">[true|false]
true: filter out symmetrical structures
false: allow duplicates
(default: true)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc resonants -f sdf test.mol</pre>
<p>&nbsp;</p>
	<a class="anchor" name="totalchargedensity"><h3>totalchargedensity</h3></a>
	<p>Total charge density. Deprecated, use "chargedensity" calculation.</p>
	<h5>Options:</h5>
	<table>
	<tr>
		<th align="left" valign="top">-p,&nbsp;--precision&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;floating point precision as number of
fractional digits: 0-8 or inf&gt; (default: 2)</td>
	</tr>
	<tr>
		<th align="left" valign="top">-H,&nbsp;--pH&nbsp;&nbsp;&nbsp;</th>
		<td  valign="top">&lt;pH value&gt; takes major microspecies at this pH
(default: no pH, takes the input molecule)</td>
	</tr>
	</table>
	<h5>Example:</h5>
	<pre>cxcalc totalchargedensity -p 4 -H 6.5 test.mol</pre>
<p>&nbsp;</p>
</body>
</html>
