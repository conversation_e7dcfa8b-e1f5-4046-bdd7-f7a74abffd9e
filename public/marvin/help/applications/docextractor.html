<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
  <meta name="author" content="<PERSON><PERSON><PERSON>">
  <link rel="stylesheet" type="text/css" href="../marvinmanuals.css"
 title="Style">
  <title>Document Extractor</title>
</head>
<body>
<h1 align="center">Compound search in texts with Document Extractor</h1>
<h3 align=center>Version @MARVINVERSION@</h3>

<p>Document Extractor is an API class that searches for compound names (IUPAC or traditional) in text file types as
 html, txt, xml and pdf and converts them to chemical structures. This class can also be called 
on the command-line. It then expects the name of a plain text file as the first argument 
(or from the standard input when absent). The list of hits is printed on the standard output. 

<h4>API documentation</h4>
See the <a href="../developer/beans/api/chemaxon/naming/DocumentExtractor.html">API documentation here</a>.

</body>
</html>
