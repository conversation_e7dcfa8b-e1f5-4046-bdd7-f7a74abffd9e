<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
  <meta name="author" content="Peter C<PERSON>">
  <link rel="stylesheet" type="text/css" href="../marvinmanuals.css"
 title="Style">
  <title>MolConverter</title>
</head>
<body>
<h1 align="center">Molecule File Conversion with MolConverter</h1>
<h3 align=center>Version @MARVINVERSION@</h3>

MolConverter is a command line program in Marvin Beans and JChem
that converts between various file types.
 
<h2>Usage</h2>
<blockquote> <font color="#333399"><strong>molconvert</strong></font>
[options]
outformat[<font color="#333399"><strong>:</strong></font><a href="../formats/basic-export-opts.html">exportoptions</a>]
[files...]
</blockquote>
<p>
<a name="outformat">The <i>outformat</i></a>
argument must be one of the following strings:
</p>
<blockquote>
  <table class="grid" cellspacing="0" cellpadding="4" >
    <tbody>
      <tr valign="top">
        <td valign="top"> <a href="../formats/mrv-doc.html"><code>mrv</code></a>
        <td valign="top">(document formats)</td>
      </tr>
      <tr>
        <td valign="top">
	<a href="../formats/mol-csmol-doc.html#mol"><code>mol</code>,
	    <code>rgf</code>, <code>sdf</code>, <code>rdf</code></a>,
	<a href="../formats/mol-csmol-doc.html#csmol"><code>csmol</code>,
	    <code>csrgf</code>, <code>cssdf</code>, <code>csrdf</code></a>,<br>
        <a href="../formats/cml-doc.html"><code>cml</code></a>,
	<a href="../formats/smiles-doc.html"><code>smiles</code></a>,
	<a href="../formats/cxsmiles-doc.html"><code>cxsmiles</code></a>,
	<a href="../formats/abbrevgroup-doc.html"><code>abbrevgroup</code></a>,
	<a href="../formats/seq-doc.html"><code>peptide</code></a>,<br>
	<a href="../formats/sybyl-doc.html"><code>sybyl</code></a>,
	<a href="../formats/mol2-doc.html"><code>mol2</code></a>,
	<a href="../formats/pdb-doc.html"><code>pdb</code></a>,
        <a href="../formats/xyz-doc.html"><code>xyz</code></a>,
	<a href="../formats/inchi-doc.html"><code>inchi</code></a>,
	<a href="../formats/name-doc.html">name</code></a>,
           <a href="../formats/cdx-doc.html"><code>cdx</code></a>,
           <a href="../formats/cdx-doc.html"><code>cdxml</code></a>,
	<a href="../formats/skc-doc.html"><code>skc</code></a>
	</td>
        <td valign="top">(molecule file formats)</td>
      </tr>
      <tr>
        <td valign="top">
	<a HREF="../formats/jpeg-doc.html"><code>jpeg</code></a>,
        <a HREF="../formats/msbmp-doc.html"><code>msbmp</code></a>,
        <a HREF="../formats/png-doc.html"><code>png</code></a>,
	<a HREF="../formats/pov-doc.html"><code>pov</code></a>,
	<a HREF="../formats/ppm-doc.html"><code>ppm</code></a>,
        <a HREF="../formats/svg-doc.html"><code>svg</code></a>,
        <a HREF="../formats/emf-doc.html"><code>emf</code></a></td>
        <td valign="top">(graphics formats)</td>
      </tr>
      <tr>
        <td valign="top"> <a href="../formats/gzip-doc.html"><code>gzip</code></a>,
        <a href="../formats/base64-doc.html"><code>base64</code></a></td>
        <td valign="top">(compression and encoding)</td>
      </tr>
    </tbody>
  </table>
</blockquote>
<a name="query-encoding">Alternatively</a>, use
<blockquote><font color="#333399"><strong>molconvert</strong></font>
[options] query-encoding [files...]
</blockquote>
to query the automatically detected encodings of the specified molecule files.

<p>
 From files having 
doc, docx, ppt, pptx, xls, xls, odt, pdf, xml, html or txt format, 
Molconvert is able to recognize the name of compounds and convert it to any of
the above mentioned output formats.
</p>

<h3>Options</h3>
<blockquote>
<table cellspacing="0" cellpadding="4" class="grid">
  <tbody>
    <tr VALIGN="TOP">
      <td><code>-o</code> <i>file</i></td>
      <td>Write output to specified file instead of standard output</td>
    </tr>
    <tr VALIGN="TOP">
      <td><code>-m</code></td>
      <td>Produce multiple output files</td>
    </tr>
    <tr VALIGN="TOP">
      <td><code>-e</code> <i>charset</i></td>
      <td>Set the input character encoding.
	  The encoding must be supported by Java.</td>
    </tr>
    <tr VALIGN="TOP">
      <td><code>-e</code> [<i>in</i><code>]..[</code><i>out</i>]</td>
      <td>Set the input (<i>in</i>) and/or output (<i>out</i>) character
	  encodings. Examples: UTF-8, ASCII, Cp1250 (Windows Eastern
       European), Cp1252 (Windows Latin 1), ms932 (Windows Japanese).</td>
    </tr>
    <tr VALIGN="TOP">
      <td><code>-s</code> <i>string</i></td>
      <td>Read molecule from specified SMILES, SMARTS or peptide
	  string (try to recognize its format)</td>
    </tr>
    <tr VALIGN="TOP">
      <td><code>-s</code> <i>string</i><code>{</code><i>format</i><code>:</code><i>options</i><code>}</code></td>
      <td>Read molecule from the string in the specified <i>format</i>
	  (can be omitted), using the specified import <i>options</i>
	  (can be omitted)</td>
    </tr>
    <tr VALIGN="TOP">
      <td><code>--smiles</code> <i>string</i></td>
      <td>Read molecule from specified SMILES string</td>
    </tr>
    <tr VALIGN="TOP">
      <td><code>--smarts</code> <i>string</i></td>
      <td>Read molecule from specified SMARTS string</td>
    </tr>
    <tr VALIGN="TOP">
      <td><code>--peptide</code> <i>string</i></td>
      <td>Read molecule from specified peptide string</td>
    </tr>
    <tr VALIGN="TOP">
      <td><code>-g</code></td>
      <td>Continue with next molecule on error
          (default: exit on error)</td>
    </tr>
    <tr VALIGN="TOP">
      <td><code>-Y</code></td>
      <td>Remove explicit H atoms</td>
    </tr>
    <tr VALIGN="TOP">
    <td><code>-I</code> <i>&lt;range&gt;</i></td>       
    <td>process input molecules with molecule index (1-based) falling into the specified range (e.g. 5-8,15 refers to molecules 5,6,7,8,15)</td>
    </tr>
    <tr VALIGN="TOP">
    <td><code>-U</code></td>       
    <td>fuse input molecules and output the union</td>
    </tr>
    <tr VALIGN="TOP">
    <td><code>-R</code>  <i>&lt;file&gt;[:&lt;range&gt;]</i></td>       
    <td>fuse fragments to input molecule(s) from file with specified mol index range range syntax: "-5,10-20,25,26,38-"
    (e.g. -R frags.mrv:20-)</td>
    </tr>
    <tr VALIGN="TOP">
    <td><code>-R&lt;i&gt;</code>  <i>&lt;file&gt;[:&lt;range&gt;]</i></td>    
    <td>fuse R&lt;i&gt; definition members to input molecule(s) from file in specified index range
    (e.g. -R1 rdef1.mrv:5-8,19)</td>
    </tr>
    <tr VALIGN="TOP">
    <td><code>-R&lt;i&gt;:&lt;1|2&gt;</code>  <i>&lt;file&gt;[:&lt;range&gt;]</i></td>   
    <td>fuse R&lt;i&gt; definition members to input molecule(s) from file in specified index range, filter molecules
    having 1 (2, resp.) attachment points
    (e.g. -R1:2 rdef1.mrv:-3,8-10)</td>
    </tr>
    <tr VALIGN="TOP">
      <td><code>-F</code></td>
      <td>Remove small fragments, keep the largest</td>
    </tr>
<!--
Commented out since this option works only for smiles output, and smiles export supports this also.    
	<tr VALIGN="TOP">
      <td><a name="option_T"></a><code>-T</code> <i>f1</i>:<i>f2</i>...</td>
      <td>Print a table containing fields from SDF separated by tab in
	  case of SMILES export.<br>
	  Colon characters in field names can be escaped by backslash.</td>
    </tr>
    <tr VALIGN="TOP">
      <td><a name="option_TT"></a><code>-T</code> "*"</td>
      <td>Print a table containing all fields from SDF separated by tab in
	  case of SMILES export. This option does not work for I/O redirection.</td>
    </tr>-->
    <tr VALIGN="TOP">
      <td nowrap><code>-c</code> "<i>f1</i><i>OP</i><i>value</i>&amp;<i>f2</i><i>OP</i><i>value</i>..."</td>
      <td>Filtering by the values of fields in the case of SDF import.<br>
      <i>OP</i> may be: =,&lt;,&gt;,&lt;=,&gt;=</td>
    </tr>
    <tr VALIGN="TOP">
      <td><code>--mol-fields-to-records</code></td>
      <td>Convert molecule type fields to separate records.</td>
    </tr>
    <tr VALIGN="TOP">
      <td><code>-v</code></td>
      <td>Verbose</td>
    </tr>
    <tr VALIGN="TOP">
      <td><code>-vv</code></td>
      <td>Very verbose (print stack trace at error)</td>
    </tr>
    <tr VALIGN="TOP">
      <td><code>-n</code></td>
      <td>Skip valence checking</td>
    </tr>
    <tr valign="top">
      <td><code>-2</code><i>[</i><code>:</code><i>options]</i><i>[</i><code>:</code><i>F&lt;i1&gt;&lt;i2&gt;...,&lt;iN&gt;]</i></td>
      <td>Calculate 2D coordinates 
      <a href="../sci/cleanoptions.html#2d">Options</a> for coordinate
calculation. <br> Performs partial clean with fixed atom coordinates for atoms 
      <i>&lt;i1&gt;&lt;i2&gt;...,&lt;iN&gt;</i> (1-based indexes) 
      if the <i>F</i> parameter is specified.</td>
    </tr>
    <tr valign="top">
      <td><code>-3</code><i>[</i><code>:</code><i>options]</i></td>
      <td>Calculate 3D coordinates<br>
      <a href="../sci/cleanoptions.html#3d">Options</a> for coordinate
calculation. </td>
    </tr>
    <tr valign="top">
      <td><code>-<span style="font-style: italic;">H3D</span></code></td>
      <td>Detailed list on <a href="../sci/cleanoptions.html#3d">Clean3D options</a><br>
      </td>
    </tr>
  </tbody>
</table>
</blockquote>

<i>Import options </i>can be specified between braces, in one of the following
forms:
<blockquote>
  <table cellspacing="0" cellpadding="4" class="grid">
    <tbody>
      <tr>
        <td>filename{options}</td>
        <td><br>
        </td>
      </tr>
      <tr>
        <td>filename{MULTISET,options}</td>
        <td>to merge molecules into one that contains multiple atom sets</td>
      </tr>
      <tr>
        <td>filename{format:}</td>
        <td>to skip automatic format recognition</td>
      </tr>
      <tr>
        <td>filename{format:options}</td>
        <td><br>
        </td>
      </tr>
      <tr>
        <td>filename{format:MULTISET,options}</td>
        <td><br>
        </td>
      </tr>
    </tbody>
  </table>
</blockquote>
<p> 
</p>
<p><i>Export options </i>are the same as the
   <a href="../formats/cml-doc.html#export">cml</a> and 
   <a href="../formats/mrv-doc.html#export">mrv</a> export options</a>.</p>
<p>You can also pass <a href="javaoptions.html">options to Java VM</a> 
when you run the application from command line.</p>

<h2>Examples</h2>
<ol>
  <li>Printing the SMILES string of a molecule in a molfile:
    <blockquote> <code>molconvert smiles caffeine.mol</code> </blockquote>
  </li>
  <li>Dearomatizing an aromatic molecule:</li>
  <blockquote> <code>molconvert smiles:<a
 href="../formats/basic-export-opts.html#minus_a">-a</a> -s "c1ccccc1"</code>
  </blockquote>
  <li>Aromatizing a molecule:</li>
  <blockquote> <code>molconvert smiles:<a href="../formats/basic-export-opts.html#a">a</a>
-s "C1=CC=CC=C1"</code> </blockquote>
   (The default general aromatization is used.)
   <li>Aromatizing a molecule using the
	basic algorithm:</li>
  <blockquote> <code>molconvert smiles:<a
 href="../formats/basic-export-opts.html#a_basic">a_bas</a> -s
"CN1C=NC2=C1C(=O)N(C)C(=O)N2C"</code> </blockquote>
  <li>Converting a SMILES file to MDL Molfile:
    <blockquote> <code>molconvert mol caffeine.smiles -o caffeine.mol</code>
    </blockquote>
  </li>
  <li>Making an SDF from molfiles:
    <blockquote> <code>molconvert sdf *.mol -o molecules.sdf</code> </blockquote>
  </li>
  <li>Printing the encodings of SDfiles in the working directory:
    <blockquote> <code>molconvert query-encoding *.sdf</code> </blockquote>
  </li>
  <li>SMILES to Molfile with optimized 2D coordinate calculation,
converting double bonds with unspecified cis/trans to "either"
    <blockquote> <code>molconvert -2:2e mol caffeine.smiles -o
caffeine.mol</code> </blockquote>
  </li>
  <li>2D coordinate calculation with optimization and fixed atom coordinates for atoms 1, 5, 6:
    <blockquote> <code>molconvert -2:2:F1,5,6 mol caffeine.mol</code> </blockquote>
  </li>
  <li>Import a file as XYZ, do not try to recognize the file format:
    <blockquote> <code>molconvert smiles "foo.xyz{xyz:}"</code> </blockquote>
    <strong>Note:</strong> This is just an example. XYZ and other
formats known by Marvin are always recognized (send us a bug report
otherwise), so the specification of the input format is usually not
needed. It is only relevant if a user-defined import module is used.
    <p> </p>
  </li>
  <li>Import a file as XYZ, with bond-length cut-off = 1.4, and max.
number of Carbon connections = 4, export to SMILES:
    <blockquote> <code>molconvert smiles "foo.xyz{f1.4C4}"</code> </blockquote>
  </li>
  <li>Import a file as Gzipped XYZ, with the same import options as in
the previous example:
    <blockquote> <code>molconvert smiles "foo.xyz.gz{gzip:xyz:f1.4C4}"</code>
    </blockquote>
  </li>
  <li>Like the previous example but merge the molecules into one
molecule that contains multiple atom sets. MDL molfile is exported.
    <blockquote> <code>molconvert mol
"foo.xyz.gz{gzip:xyz:MULTISET,f1.4C4}"</code> </blockquote>
  </li>
  <li>Import an SDF and export a table containing selected molecules
with columns: SMILES, ID, and logP:
    <blockquote> <code>molconvert smiles -c
"ID&lt;=1000&amp;logP&gt;=-2&amp;logP&lt;=4" -T ID:logP foo.sdf</code> </blockquote>
  </li>
  <li>Fuse R2 definition from file, filter fragments with 1 attachment point:
    <blockquote> <code>molconvert mrv in.mrv -R2:1 rdef.mrv</code> </blockquote>
  </li>
  <li>Fuse fragments from file (note, that the input molecule, which the fragments are fused to, should also be specified:
    <blockquote> <code>molconvert mrv in.mrv -R frags.mrv</code> </blockquote>
  </li>
   <li>Generate all common names for a structure:
    <blockquote> <code>molconvert "name:common,all" -s tylenol</code> </blockquote>
  </li>
  </li>
   <li>Generate the most popular common name for a structure (It fails if none is known.):
    <blockquote> <code>molconvert name:common -s viagra</code> </blockquote>
  </li>
  <li>
    Generate SMILES from those molecules that names are mentioned in a file 
    foo.html:
    <blockquote>
      <code> molconvert smiles foo.html </code>
    </blockquote>
  </li>
</ol>
</body>
</html>
