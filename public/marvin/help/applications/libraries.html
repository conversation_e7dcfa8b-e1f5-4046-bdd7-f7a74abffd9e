<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2//EN">
<HTML>
<HEAD>
    <META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
    <META name="author" content="<PERSON><PERSON>, Ju<PERSON>">
    <TITLE>MarvinSketch Help</TITLE>
    <link rel=stylesheet type="text/css" href="../marvinmanuals.css">
</HEAD>
<BODY>
<h1>Marvin Resources</h1>
<h3>Main JAR</h3>
<table class="colored-grid" cellspacing="0" cellpadding="4" width="80%">
	<tr>
		<th width="50%">Jar file name</th>
		<th>Description</th>
	</tr>
	<tr>
		<td>MarvinBeans.jar</td>
		<td>core packages (chemaxon.struc and certain util packages)</td>
	</tr>
</table>
<h3>Mandatory JAR files</h3>
<table class="colored-grid" cellspacing="0" cellpadding="4" width="80%">
    <tr>
        <th width="50%">Jar file name</th>
        <th>Description</th>
    </tr>
	<tr>
		<td>MarvinBeans-beans.jar</td>
		<td>chemaxon.marvin and chemaxon.marvin.beans package.</td>
	</tr>
	<tr>
		<td>MarvinBeans-gui.jar</td>
		<td>common classes for Marvin GUI components.</td>
	</tr>
	<tr>
		<td>MarvinBeans-jcw.jar</td>
		<td>Used for OLE and EMF copy.</td>
	</tr>
	<tr>
		<td>MarvinBeans-templates.jar</td>
		<td>Marvin templates.</td>
	</tr>
	<tr>
		<td>MarvinBeans-view.jar</td>
		<td>MarvinView resorce.</td>
	</tr>
	<tr>
		<td>MarvinBeans-sketch.jar</td>
		<td>MarvinSketch resources.</td>
	</tr>
	<tr>
		<td>MarvinBeans-help.jar</td>
		<td>Marvin User Guide.</td>
	</tr>
	<tr>
		<td>MarvinBeans-space.jar</td>
		<td>MarvinSpace resorces.</td>
	</tr>
	<tr>
		<td>MarvinBeans-formats.jar</td>
		<td>Common resources for input and export modules.</td>
	</tr>
	<tr>
		<td>MarvinBeans-formats-tripos.jar</td>
		<td>Tripos file format.</td>
	</tr>
	<tr>
		<td>MarvinBeans-formats-jtf.jar</td>
		<td>JFT file format.</td>
	</tr>
	<tr>
		<td>MarvinBeans-formats-xyz.jar</td>
		<td>XYZ file format.</td>
	</tr>
	<tr>
		<td>MarvinBeans-formats-peptide.jar</td>
		<td>Peptide file format.</td>
	</tr>
	<tr>
		<td>MarvinBeans-formats-inchi.jar</td>
		<td>IUPAC InChi format.</td>
	</tr>
	<tr>
		<td>MarvinBeans-formats-smiles.jar</td>
		<td>SMILES format.</td>
	</tr>
	<tr>
		<td>MarvinBeans-formats-vmn.jar</td>
		<td>VMN format.</td>
	</tr>
	<tr>
		<td>MarvinBeans-formats-pdb.jar</td>
		<td>PDB format.</td>
	</tr>
	<tr>
		<td>MarvinBeans-formats-gaussian.jar</td>
		<td>Gaussian formats.</td>
	</tr>
	<tr>
		<td>MarvinBeans-formats-cml.jar</td>
		<td>CML and MRV formats.</td>
	</tr>
	<tr>
		<td>MarvinBeans-formats-mdl.jar</td>
		<td>MDL file formats.</td>
	</tr>
	<tr>
		<td>MarvinBeans-formats-cdx.jar</td>
		<td>CDX formats.</td>
	</tr>
	<tr>
		<td>MarvinBeans-formats-abbrevgroup.jar</td>
		<td>Abbrevgroup format.</td>
	</tr>
	<tr>
		<td>MarvinBeans-formats-name.jar</td>
		<td>IUPAC Name format.</td>
	</tr>
	<tr>
		<td>MarvinBeans-formats-image.jar</td>
		<td>Image export.</td>
	</tr>
	<tr>
		<td>MarvinBeans-formats-skc.jar</td>
		<td>SKC format.</td>
	</tr>
	<tr>
		<td>MarvinBeans-license.jar</td>
		<td>chemaxon.license package.</td>
	</tr>
	<tr>
		<td>MarvinBeans-plugin.jar</td>
		<td>Calculator Plugins.</li>
	</tr>
	<tr>
		<td>MarvinBeans-modelling.jar</td>
		<td>3D modelling.</td>
	</tr>
	<tr>
		<td>MarvinBeans-jep.jar</td>
		<td>chemaxon.jep and chemaxon.nfunk packages.</td>
	</tr>
	<tr>
		<td>MarvinBeans-enumeration.jar</td>
		<td>chemaxon.enumeration package.</td>
	</tr>
	<tr>
		<td>MarvinBeans-checkers.jar</td>
		<td>Structure checkers.</td>
	</tr>
	<tr>
		<td>MarvinBeans-math.jar</td>
		<td>chemaxon.math package.</td>
	</tr>
	<tr>
		<td>MarvinBeans-jnbadapter.jar</td>
		<td>Adapter for JNBridge.</td>
	</tr>
	<tr>
		<td>MarvinBeans-codeassist.jar</td>
		<td>Code compilation utility for Chemical Terms.</td>
	</tr>
	<tr>
		<td>MarvinBeans-alignment.jar</td>
		<td>3D molecular alignment package.</td>
	</tr>
	<tr>
		<td>MarvinBeans-concurrent.jar</td>
		<td>chemaxon.util.concurrent package.</td>
	</tr>
	<tr>
		<td>MarvinBeans-mprop.jar</td>
		<td>chemaxon.marvin.modules.mprop package.</td>
	</tr>
	<tr>
		<td>MarvinBeans-smarts.jar</td>
		<td>For partial interpretation of smarts atoms.</td>
	</tr>
	<tr>
		<td>MarvinBeans-diverse-modules.jar</td>
		<td>Rest of Marvin modules.</td>
	</tr>
    <tr>
        <td>aloe.jar</td>
        <td>Swing Extension Package.</td>
    </tr>
    <tr>
        <td>forms-1.1.0.jar</td>
        <td>JGoodies Form Layout.</td>
    </tr>

</table>

<h3>Mandatory JAR files of MarvinSpace</h3>
<table class="colored-grid" cellspacing="0" cellpadding="4" width="80%">
<tr>
    <th width="50%">Jar file name</th>
    <th>Description</th>
</tr>
<tr>
    <td>jextexp.jar</td>
    <td>DualThumbSlider component of GNF</td>
</tr>
<tr>
    <td>gluegen-rt.jar</td>
    <td>Java interface to OpenGL used by JOGL</td>
</tr>
    <tr>
    <td>jogl.jar</td>
    <td>Java interface to OpenGL used by JOGL</td>
</tr>
<tr>
    <td>jogl_1.1.0-rc2/gluegen-rt-natives-*.jar</td>
    <td>Java interface to OpenGL used by JOGL</td>
</tr>
    <tr>
    <td>jogl_1.1.0-rc2/jogl-natives-*.jar</td>
    <td>Java interface to OpenGL used by JOGL</td>
</tr>
</table>


<h3>JAR files covering frequent functionalities</h3>
<table class="colored-grid" cellspacing="0" cellpadding="4" width="80%">
    <tr>
        <th width="50%">Jar file name</th>
        <th>Description</th>
    </tr>
    <tr>
        <td>chart.jar</td>
        <td>Used by several Calculator Plugins</td>
    </tr>
    <tr>
        <td>jh.jar</td>
        <td>JavaHelp resource</td>
    </tr>

</table>

<h3>JAR files optionally needed for special functionalities</h3>
<table class="colored-grid" cellspacing="0" cellpadding="4" width="80%">
    <tr>
        <th width="50%">Jar file name</th>
        <th>Description</th>
    </tr>
    <tr>
        <td>batik-core.jar</td>
        <td>Used for SVG Image export</td>
    </tr>
    <tr>
        <td>backport-util-concurrent.jar</td>
        <td>Used for Calculator Plugins in batch mode</td>
    <tr>
        <td>freehep-export-2.1.1.jar</td>
        <td>Used for EMF/PDF export</td>
    </tr>
    <tr>
        <td>freehep-graphics2d-2.1.1.jar</td>
        <td>Used for EMF/PDF export</td>
    </tr>
    <tr>
        <td>freehep-graphicsio-2.1.1.jar</td>
        <td>Used for EMF/PDF export</td>
    </tr>
    <tr>
        <td>freehep-graphicsio-emf-2.1.1.jar</td>
        <td>Used for EMF export</td>
    </tr>
    <tr>
        <td>freehep-graphicsio-pdf-2.1.1.jar</td>
        <td>Used for PDF export</td>
    </tr>
    <tr>
        <td>freehep-io-2.0.2.jar</td>
        <td>Used for EMF/PDF export</td>
    </tr>
    <tr>
        <td>freehep-swing-2.0.3.jar</td>
        <td>Used for EMF/PDF export</td>
    </tr>
    <tr>
        <td>freehep-util-2.0.2.jar</td>
        <td>Used for EMF/PDF export</td>
    </tr>
    <tr>
        <td>inchi-native-*.jar</td>
        <td>Used for IUPAC inchi import/export</td>
    </tr>
    <tr>
        <td>jacob-1.15/jacob-1.15.jar</td>
        <td>Java-COM bridge used for various windows specific functionalities (e.g.: OLE, EMF generation)</td>
    </tr>
    <tr>
        <td>jacob-1.15/jacob-1.15-native-x86.jar</td>
        <td>32-bit native implementations for jacob</td>
    </tr>
    <tr>
        <td>jacob-1.15/jacob-1.15-native-x64.jar</td>
        <td>64-bit native implementations for jacob</td>
    </tr>
    <tr>
        <td>jnbtools.jar</td>
        <td>Required for JNI Bridge</td>
    </tr>
    <tr>
        <td>looks-2.1.4.jar</td>
        <td>Supported JGoodies Look&Feel</td>
    </tr>
    <tr>
        <td>openide-lookup-1.9-patched-1.0.jar</td>
        <td>Used for EMF/PDF export</td>
    </tr>
    <tr>
        <td>registry/cxnregistry-x86.jar</td>
        <td>Natives for manipulating and query the windows registry thru jacob in 32 bit environments.</td>
    </tr>
    <tr>
        <td>registry/cxnregistry-x64.jar</td>
        <td>Natives for manipulating and query the windows registry thru jacob in 64 bit environments.</td>
    </tr>

</table>
</BODY>
</HTML>
