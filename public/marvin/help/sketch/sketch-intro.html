<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2//EN">
<html>
<head>
<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
<TITLE>Introduction to MarvinSketch</TITLE>
<link rel=stylesheet type="text/css" href="../marvinmanuals.css">
</head>
<body>
<h1>Introduction to <PERSON><PERSON><PERSON><PERSON></h1>
<p>
MarvinSketch is an advanced chemical editor for drawing chemical structures, queries and reactions. It has a rich (and growing) list of editing features, is chemically aware and is able to call ChemAxon's structure based calculation plugins for structures on the canvas.
</p>
<strong>Rich editing:</strong>
<ul>
        <li>wide range of file types supported: MOL, MOL2, SDF, RXN, RDF (V2000/V3000), SMILES, SMARTS/SMIRKS (recursive), MRV, InChi, CML, PDB, etc.
        <li>Copy and paste between different editors
        <li>Abbreviated groups
        <li>Pre-loaded structure templates and "My Templates"
        <li>Fog effect in 3D viewing mode
        <li>3D editing
        <li>3D geometry and conformer generation
        <li>2D cleaning and conformer generation
        <li>Advanced query features (generic atoms and bonds, atom lists/not lists, query properties, pseudo atoms, multiple groups, Link nodes, etc.)
        <li>Creating and editing molecule sets (without a database)
        <li>Multipage documents and printing support
        <li>Drawing and formatting shapes, arrows and text boxes
        <li>Structure annotation
        <li>User definable customisable styles (colours, structure representations, etc.)
</ul>

<strong>Chemically aware</strong>
<ul>
        <li>Structure based calculations can be called directly from MarvinSketch. For a complete listing of functions please see the Calculator Plugins section
        <li>Error checking (valence and reaction error checking)
        <li>Structure query design (R-logic, SMARTS properties, etc.)
        <li>Isotopes, charges radicals, lone pairs and aliases are supported
        <li>Manual and automapping for reaction drawing
        <li>Advanced stereochemistry functions (E/Z double bonds, R/S chirality, ABS/OR/AND enhanced stereo, etc.)
</ul>

<strong>Cross platorm delivery</strong>

<ul>
        <li>Marvin can run on all major operating systems, it is available in the following distributions:
        <ul>
        <li><strong>Java Applets</strong> can easily be implemented into Java enabled web pages without the need for the user to install software or plugins</li>
        <li><strong>Java Beans</strong> can be directly installed to give standalone desktop applications and can also be used to integrate Marvin into Java based applications</li>
        <li><strong>Java Web Start</strong> enables web delivery of end user applications</li>
        <li><strong>.NET package</strong> makes it available to integrate Marvin into .NET applications</li>
        </ul>
</ul>

</body>
</html>
