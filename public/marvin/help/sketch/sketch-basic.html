<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>MarvinSketch Help</TITLE>
 <link rel=stylesheet type="text/css" href="../marvinmanuals.css">
</HEAD>
<BODY>
<H1>How to Use Basic MarvinSketch Functions</H1>
<P><a HREF="sketch-index.html">Table of Contents
</a>
</P>

<H2><a class="anchor" NAME="create-molecule">Creating a New Molecule</a></H2>

<P>A new, blank molecule is created when you first launch MarvinSketch. You
can immediately begin working with this molecule. A MarvinSketch window can
hold only one molecule at a time, so all work you do within the
canvas is considered part of the same molecule. You can create a new,
blank molecule at any time during your session by choosing 
<B class="buttonName">File &gt; New &gt; Clear Desk</B> from the menu bar. This
will clear the desk and discard any unsaved changes to the molecule you were
previously working with (but you can get it back using the Undo option). </P>
<P>The application allows you to work with
multiple molecules in multiple windows by choosing <B class="buttonName">File
&gt; New &gt; New Window</B>.</P>

<H2><a class="anchor" name="open-molecule">Opening and Saving a Molecule File</a></H2>

<H3><a class="anchor" name="open-molecule">To Open an Existing Molecule File</a></H3>

<P>You can open existing molecule files
(from supported <a href="../formats/formats.html">file formats</a>) by
choosing <B class="buttonName">File &gt; Open...</B> on the menu bar.
It will load the content of the molecule file into Marvin and discard any
unsaved changes.</P>
<p>Tick the <b>Show preview</b> checkbox to see the contents of the file (molecules,
reactions, queries). A single item is displayed in the preview window;
the text field at the bottom shows the index of the current structure
and the number of structures in the file. When a multiple structure file is selected
(<i>e.g.</i>, mrv, sdf), the navigation buttons become active. Their functions are: go to first, 
go to previous, go to next, go to last. Note that the preview window allows you only to check the contents of a file, but not to select the structures you would like to open. 
For this purpose the <b>Select</b> textbox has to be used, where you can write the serial numbers of the molecules to open. The numbers have to be separated either by 
commas or by a dash. (Clicking on the <b>Info</b> button in this row will present tips for specifying the desired molecules.) Leaving this textbox empty means that every molecule in
the file will be loaded onto the canvas. Currently this is the default behavior of file loading in MarvinSketch.<br>
In order to open a pdf file containing chemical names in MarvinSketch, you need to apply <a href="../d2s/d2s.html">Document to Structure (d2s)</a> conversion. If your pdf file conatins images of compounds, 
you need OSRA to be installed on your computer in order to import the structures into Marvin (possible since version 5.3.0). Please <a href="../formats/imageimport.html">consult this page for details</a>.
</p>
<p>
The <b class="buttonName">File &gt; Insert file</b> menu option has to be chosen if you want to open a file, but, at the same time, you do not want to lose the molecule(s) currently on 
the canvas. The <b>Insert</b> dialog window works in the same way as the <b>Open</b> dialog.
</p>
<p>You can also <a href="../datatransfer.html">Paste</a> existing structures from other windows, as well as <a href="../datatransfer.html">Drag &amp; Drop</a> 
	a chemical structure into the MarvinSketch window. Both actions will add the new structure to the currently loaded
    ones without overwriting the contents of the canvas.</p>
<p>Importing structures from image files or pasting them from the clipboard with the help of <a href="../formats/imageimport.html">OSRA</a> is also possible (since version 5.3.0). 
	Select <b>File &gt; Import Image</b> if you want to open an image file. The supported formats are: bmp, png, jpg, gif, svg, and tif. 
	Alternatively, the <b> File &gt; Open...</b> menu option can also be used for opening an image file. In this case you should select <b>All Files</b> from the list of <b>Files of Type</b>.</p>

<H3><a class="anchor" name="save2molformat">To Save a Molecule File</a></H3>

<P>You can save the molecule in any of the supported 
<a href="../formats/formats.html">file formats.</a>
This will allow you to open and work with this molecule later. In case of a single structure, the
default behavior of the <B class="buttonName">Save</B>
menu is to save the molecule to the same file as it was opened from,
in the same format. If you want to change the filename or format,
choose <B class="buttonName">Save As</B>.
If you are working with a new molecule, <B class="buttonName">Save</B>
will function the same way as <B class="buttonName">Save As</B>. If you are working with a multistructure file, both <B class="buttonName">Save</B> and <B class="buttonName">Save As</B> will 
open a dialog window where you have to define the parameters for saving.<br>
On the dialog window used for saving files, a <B>Saving method</B> tab can be opened with the help of the <B>Advanced</B> checkbox. The first half of the tab offers the following choices:<br>
	<ul>
		<li><b>All structures TOGETHER:</b> Saves all structures together in a single file.<br>
				This is the default behavior of saving. If you do not open the <B>Saving method</B> tab, this option will be applied.</li>
		<li><b>All structures SEPARATELY:</b> Saves all structures separately in a single file.</li>
		<li><b>Into SEPARATE FILES:</b> Saves each structure into a separate file.<br>
				In this last option, the numbering of molecule files begins with the molecule in the upper left corner.</li>
	</ul>
	<img src="gui/gui-files/save1.png"><br>
</P>
<P>The second option remains inactive unless the chosen file type supports multimolecule files (such as mrv or sdf). Similarly, for one molecule only files, only the first option is enabled. 
The same applies to reactions and structures containing R-group definitions: in these cases only the "All structures TOGETHER" option is available as well.<br>
When the given parameters for saving (filename, format, and route) are the same as for an already existing file, the outcome will be determined by the other two radio buttons of 
the dialog window. If you select the <B class="buttonName">Overwrite file</B> option, the original contents of the file will be replaced. However, 
selecting the <b class="buttonName">Append to end of file</b> radio button will add the new structures to the original file, so its content will be preserved. In the first case, a new dialog 
window will be displayed, where you can reinforce or change your decision about overwriting the file. 
If the chosen file format does not support multiple structures, the <B>Overwrite</B> option is applied automatically.
</P>
<H3><a class="anchor" name="property_editor">To Edit and Save atom, bond, and molecule properties</a></H3>
<P>
You can add, edit, and save properties of atoms, bonds, and molecule; just select the relevant part and choose "Edit properties..." option from the contextual menu.
The added properties will be saved in the file which supports properties such as MRV or SDF (in case of the molecule property only). You can display atomic properties, setting 
<B class="buttonName">Menu&gt;View&gt;Misc&gt;Atom Properties</B>; bond and molecule properties cannot be displayed on the canvas currently.

</P>


<H3><a class="anchor" name="export-as-image">To Save as an Image File</a></H3>

<P STYLE="margin-bottom: 0">The <B class="buttonName">Export to Image</B> choice
in the <B class="buttonName">File</B> menu allows you to save an image of the
molecule in the sketcher. Marvin supports the following image
formats: JPG, PNG, PPM, SVG, BMP, and SVG. Please note that the saved image
cannot be edited in Marvin.</P>

<H2><a class="anchor" name="print">Printing</a></H2>

<P>You can print an image of the current molecule by choosing
<B class="buttonName">File &gt; Print</B>. If you print from a single page
document, if the size of your molecule(s) is bigger then the paper size, it will
be shrunked to one page.</P> If you want to print your molecule(s) to multiple
pages, you have to change the document type to Multipage Document in
<B class="buttonName">File &gt; Document Settings...</B>, and arrange the
structures on the pages.

<H2><a class="anchor" name="howto-multipage">Working with Multipage Molecular Documents</a></H2>

<H3><a class="anchor" name="createandset">How to create a multipage molecular document</a></H3>
Multipage molecular documents help to work with large drawings by dividing them into pages.
You can create a multipage molecular document by choosing 
<B class="buttonName">File &gt; Document Settings...</B>, then checking in the
<B class="buttonName">Multipage document</B> checkbox.<br>

<IMG SRC="gui/gui-files/dialogs/document_settings.png"
    NAME="Graphic2" ALT="document settings dialog" ALIGN=MIDDLE WIDTH=231
    HEIGHT=355 BORDER=0>
<BR>
You can set the number of horizontal and vertical pages in the
<B class="buttonName">Document Grid</B> part, and you can also define the title,
the page size and the margins in the corresponding sections of this dialog window.
After pushing the OK button, the following controls become automatically available:
<ul>
    <li>The items in the <B class="buttonName">View &gt; Pages</B> menu are enabled</li>
    <li>A navigation status bar appears on the bottom of the window</li>
    <li>The frame of the pages appear on the canvas, while the title, the margins and
	the page numbers are displayed on each page</li>
</ul>
<IMG SRC="gui/gui-files/multipage.png" 
    NAME="Graphic2" ALT="Multipage document" ALIGN=MIDDLE width=534 height=517 BORDER=0>

<H3><a class="anchor" name="navigation">How to navigate in multipage molecular documents</a></H3>

<P>The navigation status bar and the items in the <B class="buttonName">View &gt; Pages</B> menu are
available only if the <B class="buttonName">Multipage document</B> checkbox is set.
The status bar contains information about
the current page number in a text field and the number of all pages on a label.
It also contains a collection of buttons to 
aid your quick navigation in the document. You can go the first, previous, next, and last page
using them. Alternatively, you can go directly to a specific page
by entering a number in the current page field and pressing enter.</P>
<P>All the navigation possibilities: go to first, previous, next, last, specific
pages are available from the <B class="buttonName">View &gt; Pages </B>  menu as
well. In addition, some automatic page zooming functions are also available in this menu, such as:</P>
<ul>
	<li><B class="buttonName">View &gt; Pages &gt; Fit page height</B> adjusts the
height of the current page to the height of the canvas.</li>
	   <li><B class="buttonName">View &gt; Pages &gt; Fit page width</B> adjusts the
width of the current page to the width of the canvas.</li>
	  <li><B class="buttonName">View &gt; Pages &gt; Fit page</B> adjusts the height
and/or the width of the current page to see the whole page, and places it
centralized within the canvas.</li>
</ul>

<H2><a class="anchor" name="howto-draw">Drawing Structures</a></H2>

<P>You can create structures using atoms, bonds, and templates.</P>

<H3><a class="anchor" name="howto-draw.atoms">How to Draw Atoms</a></H3>

<uL>
    <LI>Select an atom from the <a href="gui/toolbars.html#atoms">Atoms Toolbar</a>,
        the <a href="gui/dialogs.html#periodic">Periodic Table</a> dialog window, or by
        <a HREF="gui/shortcuts.html">shortcut</a>.</LI>
    <LI>Move the mouse into the molecule
	canvas. You will see the symbol of the selected item at the tip of
	your cursor. It can be placed in the molecule by left-clicking on
	the desired location.
	<UL>
            <LI>Marvin is chemically intelligent.
	    It will account for implicit hydrogens and set the charge according
        	to valence rules.</LI>
	</UL>

    In case of metals, the following rules apply: metallic elements from the 1st
    and 14-16th groups are added hydrogens, other appear as simple elements as default.

    </LI>
    <LI>You can replace any atom in the molecule by placing a new
	atom on top of it.
    </LI>
	
	<li>
		You can represent atoms in a molecule with practically any arbitrary symbol instead 
		of standard atomic symbols by using <a href="#alias">Atom Aliases</a>. 
		An alias can be defined on the <a href="gui/dialogs.html#advanced">Advanced</a> tab of the 
		<b>Periodic Table</b>, among the <a href="#custom.properties">Custom Properties</a>.	
	</li>
</uL>

<H3><a class="anchor" name="howto-draw.bonds">How to Draw Bonds</a></H3>

<uL>
    <LI>Select a bond type using the
        <B class="buttonName">Bond</B> toolbar button or by
        <a HREF="gui/shortcuts.html">shortcut</a>.</LI>
    <LI>To link two existing atoms, click
	on one then drag the cursor to the other.
        <UL>
	    <LI>Marvin will allow you to draw a
		bond between any two atoms in the molecule. Valence errors
                will be highlighted (if that option is enabled).</LI>
        </UL></LI>
    <LI>To draw a bond from a single atom,
	simply click the atom. A carbon atom will be added at the other end
	of the bond.</LI>
    <LI>If you add a bond to empty canvas
	space, a carbon atom will be added to each end of the bond.</LI>
    <LI>You can replace any bond in the
	molecule by placing a new bond type on top of it.</LI>
    <LI>Bond types can also be changed using the
        <a HREF="gui/popups.html#bond.popup">Bond pop-up menu.</a> Single
        bonds can be changed to Double or Triple by
	left-clicking on them.</LI>
    <LI><a class="anchor" name="boldaromatic"></a>Bold Tool, <img src="gui/gui-files/icons/bond-bold-tool24.png">, is intended to be used for graphical presentations of molecules. Activate the tool, click on a bond and it will be changed to bold. In case of single, double, or aromatic bond the tool keeps the type of the bond during multiple clicking. In case of aromatic bond, Bold Tool has four positions: bold single, bold aromatic up, reversed bold aromatic up, and single. Note: your mouse must point to the same position of the bond. In case of other bond types (e.g., triple, wedges) multiple clicking has a different result; it does not retain the original bond type, but undoing the action does. Bold Tool is located in the Bond Menu, Tools toolbar, and bond PopUp menu by default.</li> 
 <b>Example </b><p><img src="sketch-basic_files/boldaromatic.png" width="70" height="148" alt="boldaromatic"/></p>
 <LI>You can make the selected bond hashed: Choose the <img src="gui/gui-files/icons/hashed-bond-24.png"> icon from the "Tools" toolbar or the <b>Bond</b> &gt;<b>Hashed</b> menu. It only retains single original bond type.</LI>
</uL>

<H3><a class="anchor" name="howto-draw.chains">How to Draw Chains</a></H3>

<uL>
You can draw carbon chains easily selecting the <b>Insert</b> &gt;<b> Chain</b> menu or clicking on the "Draw Chain" (<img src="gui/gui-files/icons/chain24.png">) icon of the "Tools" toolbar. For curved chains click on the <img src="gui/gui-files/icons/curved-chain24.png"> icon. The direction of the chain growth follows the mouse path. The number of carbon atoms can be increased or decreased by dragging the mouse. The chain drawing direction is mirrored based on the direction of the mouse movements.
 </uL>
 
<H2><a class="anchor" name="templates">Templates</a></H2>

<p>MarvinSketch provides several predefined chemical structures, called <b>templates</b> or <b>structure templates.</b> They are categorized into template groups, like Amino Acids, Polycyclics e.t.c.<br>
The following template groups are available in MarvinSketch by default:</p>
<ul>
    <li>Generic</li>
    <li>Rings</li>
    <li>Amino Acids</li>
    <li>Aromatics</li>
    <li>Bicyclics</li>
    <li>Bridged Polycyclics</li>
    <li>Crown Ethers</li>
    <li>Cycloalkanes</li>
    <li>Heterocycles</li>
    <li>Polycyclics</li>
    <li>Homology Groups</li>
	<li>Alpha D sugars</li>
	<li>Beta D sugars</li>
	<li>Deoxynucleosides</li>
	<li>Flavonoids</li>
	<li>Nucleobases</li>
	<li>Nucleosides</li>
	<li>Organometallics</li>
	<li>Protoalkaloids</li>
	<li>True Alkaloids</li>
	<li>Vitamins</li>
	<li>My Templates</li>
</ul>
<P>The templates can be accessed via the <a href="gui/toolbars.html#templates">Advanced Templates Toolbar</a> or through the <a href="gui/dialogs.html#template_library"><b>Insert &gt; Template</b></a> menu item.</P>

<p><H3>How to use Templates:</H3></p>
<uL>
    <LI>Select a template using the
	Template Library or the Advanced Templates Toolbar area.</LI>
    <li>In case the template structure contains any S-groups, the group(s)
    can be optionally expanded or contracted by pressing the Shift button.</li>
    <LI>Place the template structure by left-clicking on the
	desired location.</LI>
</uL>
<p>The <a href="gui/dialogs.html#template_library"><b>Template Library Manager</b></a> dialog contains buttons that customize template handling. </p>

<h3><a class="anchor" name="newfragment">New substituent (fragment) editing</a></h3>

<p>In some cases, you will find it difficult to add new fragments to your molecule file, for example if you already have structures cleaned in 3D. To add a new fragment to the canvas, follow these steps:</p>
 <ol><li>Choose the <img src="sketch-basic_files/entering_new_substituent_fragment16.png" width="16" height="16" alt="newsubstituent"/> New substituent from the Insert menu.
    <li>Draw the structure in the new canvas. If you would like to transfer and match it to your original 3D molecule, do a 3D clean on the new fragment (Structure/Clean 3D).
    <li>Click the <img src="sketch-basic_files/transfer16.png" width="16" height="16" alt="transfer"/> Transfer button in the top left corner to return to the original canvas and place the new fragment.
</ol>

<h3><a class="anchor" name="sprouting">Sprouting</a></h3>

<p><b>Atom  sprouting:</b> 
<ul><li>Click an atom symbol on the toolbar or in the Periodic Table.
    <li>Place the cursor over the atom where you would like to add the atom.
    <li>Press the Shift key on the keyboard then click the atom. The new atom will
    be attached to that atom.
</ul>
<p><img src="sketch-basic_files/sproutatom.png" width="186" height="90" alt="sproutatom"/>

<p><b>Template sprouting:</b> you can add the template connected by a bond formed between the selected non-primary atom and the attachment point of the  template. This way adding a substituent will only replace a hydrogen atom on the selected atom, not the atom itself. This feature is limited to the use of symmetrical templates where attaching the template has only one possibility (e.g. as for phenyl).

<ul><li>Select a template from the toolbar or from the Template Library
    <li>Moving the cursor over an atom, a grey colored image will show you the positioning of the template.
    <li>Left-click on the atom will place the template. </ul>
<p>To change the connection type (no sprouting):</p>
<ul>
    <li>Select a template from the toolbar or the template library.
    <li>Move the cursor to the canvas and hover over an atom.
    <li>Press the Shift key and while holding it down, click the atom.
</ul>
In both cases, you can change the bond angle by rotating the template: holding down the left mouse button, move the mouse to rotate the molecule, and release it when desired position is reached.<p>
<table><tr><td width="50%">Adding a cyclohexane template to a secondary carbon atom:</td>
        <td> <img src="sketch-basic_files/sprout1.png" width="240" height="159" alt="sprout1"/></td></tr>
    <tr><td>Adding a cyclohexane template to a secondary carbon atom while holding down the Shift key:</td>
        <td><img src="sketch-basic_files/sprout2.png" width="237" height="130" alt="sprout2"/></td></tr>
</table>

<p>Notes: <br>
<ol><li>Abbreviated groups will be extended when holding down the Shift key, its attachment
    is not affected in terms of sprouting.
    <li>The grey outlined template will not be shown if the creation of a new bond would
    lead to the valence error of the atom but will be added if you click the atom.
</ol></p>

<h3><a class="anchor" name="merge">Merging structures</a></h3>

<p>If you would like to form a new structure by combining two already drawn molecules, you have the possibility to merge them in few steps. This starts with defining the merging points in both the template and the substituent molecules (1, 2 or 3 pairs of them). The template molecule' coordinates are not changed, only the substituent is resized, rotated (in three dimensions) and moved to fit the template.</p>

<ol><li>Select <img src="sketch-basic_files/merging_mapper_tool16.png" width="16" height="16" alt="assign_atoms"/> Assign Atoms from the Structure menu, Directed Merge submenu.
    <li>Click and drag the arrow from the atom of the substituent to the template molecule. The arrow will be numbered.
    <li>Repeat the assigning (the Assign Atoms action is still active, no need to re-select the command) once ore twice to 
define more merging points.
    <li>Merge the molecules by selecting the <img src="sketch-basic_files/directed_merge_in_3d16.png" width="16" height="16" alt="merge_atoms"/> Merge command from the Structure menu, Directed Merge submenu.
    <li>In case of assigning 1 or 2 atom pairs, the subsituent is selected after the merge and the Rotate in 3D 
mode is active, and you can rotate the substituent around an axis: 
<br><b>1 atompair</b>
<ul><li>In case of one bond, this bond is the axis; 
<li>In case two bonds, the axis is the bisector of the angle of these bonds;
<li>In case of 3 bonds have to choose the x,y, or z axis, or define the axis by selecting two atoms.</ul>
<b>2 atompairs</b>
<ul><li>The rotation axis is defined by the two connection points of the template and the substituent. Now the user can rotate the substituent, and if any atom pairs fall in the merging range after the 3D rotation, they will be merged.
</ul>

<p>After the 3D rotation, any atom pair that falls in the merging range are merged. If this second merge happens only on one atom pair, the substituent remains selected, and is subject to a second 3D rotation action, where the rotation axis is defined by the original and the new connection points. Now rotate the substituent around this new axis, and again, if any atom pairs fall in the merging range after the 3D rotation, they will be merged.</p>
<p>Note: pressing the Shift key on your keyboard offers an alternative rotation axis</p>
<li>To finish merging, click anywhere on the blank canvas.
</ol>

<h4>How to merge structures</h4>

<table>
<tr><th></th>
<th>Assigned atoms</th>
<th>Selected for rotation</th>
<th>Merge product</th></tr>
<tr><td width="20%"><b>1 atom pair</b></td>
<td align="center"><img src="sketch-basic_files/1atom_assign.png" width="264" height="114" alt="2atom_assign"/></td>
<td align="center"><img src="sketch-basic_files/1atom_rot.png" width="165" height="111" alt="2atom_rot"/></td>
<td align="center"><img src="sketch-basic_files/1atom_result.png" width="148" height="122" alt="2atom_result"/></td></tr>
<tr><td width="20%"><b>2 atom pairs</b></td>
<td align="center"><img src="sketch-basic_files/2atom_assign.png" width="225" height="110" alt="2atom_assign"/></td>
<td align="center"><img src="sketch-basic_files/2atom_rot.png" width="180" height="148" alt="2atom_rot"/></td>
<td align="center"><img src="sketch-basic_files/2atom_result.png" width="159" height="120" alt="2atom_result"/></td></tr>
<tr><td width="20%"><b>3 atom pairs</b></td>
<td align="center"><img src="sketch-basic_files/3atom_assign.png" width="227" height="140" alt="3atom_assign"/></td>
<td></td>
<td align="center"><img src="sketch-basic_files/3atom_result.png" width="174" height="186" alt="3atom_result"/></td></tr>
    </table>

<H2><a class="anchor" name="coordination-compounds">Coordination compounds</a></H2>

You can use coordinate bond to represent coordination compounds (ferrocenes, metallocenes).
For example:
<br><img src="sketch-basic_files/coordination1.png" width="175" height="112"> <br>
The coordinate bond type can represent the connection between an atom and a group of atoms.
The coordinate bond has two kinds of appearance according to IUPAC recommendation:
<UL>
<LI>arrow between two atoms,</LI>
<LI>dashed line between an atom and a group of atoms.</LI>
</UL>
In the <b>Edit &gt;Preferences &gt;Bonds</b> menu item you can change the default line type of coordinate bonds to solid.
To draw a bond between two atoms just choose the coordinate bond from
the bond list and draw the bond by specifying the required direction. To draw a
bond between an atom and a group  of atoms you need to create
a multi-center attachment point to represent the group of atoms.

<h4>To draw a coordinate bond between an atom and a group of atoms</h4>

<uL>
	<LI> Select the atoms to be represented at one end of the coordinate bond by a multi-center.</LI>
	<LI> Choose "Structure>Add>Multi-center" from the main menu or "Add/Multi-center" from the contextual menu. A multi-center represented by a "*" will be added. If you move the cursor to the multi-center the represented atoms are	highlighted (blue circle around the atom labels).</LI>
	<LI> Draw a coordinate bond from the multi-center and edit the other end of the bond if required.
	The "*" representing the multi-center disappears after bond drawing.</LI>
	<LI> Repeat steps 2-4 to draw further multi-centers and coordinate bonds if required.</LI>
</uL>

<H2><a class="anchor" name="biomolecule-displaying">Importing and displaying biomolecules</a></H2>

<P>You can import RNA, DNA and peptide sequences from the menu choosing <B class="buttonName">File &gt; Import As </B> or through the <B class="buttonName">Edit &gt; Source </B> panel.</P>
<P>In the second case you have to select the import mode ('Peptide Sequence', 'DNA Sequence', or 'RNA sequence') if it is not possible to decide whether the sequence belongs to a peptide or to a nucleic acid.
Peptides can be entered using their 1- or 3-letter amino acid codes (<a href="../formats/seq-doc.html">see documentation on peptide import</a>). 
DNA nucleic acid sequences can be imported in four different formats: ACGT, A-C-G-T, dAdCdGdT or dA-dC-dG-dT. RNA nucleic acid import accepts sequences in two formats: ACGU and A-C-G-U.
DNA/RNA sequences are displayed with their 1-letter code on the canvas.</P>

<P>For peptides/proteins MarvinSkecth offers you the possibility to display them as their 1-letter or 3-letter codes selecting the <B class="buttonName"> View &gt; Peptide Display </B> menu item.
</P>

<br> <img src="sketch-basic_files/peptide_display.png">
<P>You can expand an amino acid group selecting 'Expand Groups' from the contextual menu over the group. To expand all groups of the peptide you should select 'Expand Groups' from the contextual menu over the canvas.
Nucleotides of a DNA/RNA sequence can also be expanded in the same way.</P>

<h2><a class="anchor" name="transformation">Geometric Transformations of Structures</a></h2>

<p>Geometric transformation functions (<b>Flip</b>, <b>Mirror</b>, <b>Invert</b>) can be used from the <b>'Edit > Transformation'</b> main menu on the whole molecule or only on the selected part of the structure.</p>

<h3><a class="anchor" name="flip">Flip a molecule</a></h3>

<p>You might need to flip the whole or parts of the structures. These operations
are located in the <b>Edit &gt; Transformation menu</b>. If no selection is made, the operation
will be executed on the whole structure (except for Group Flip). The flip operation is equal 
to a rotation of 180&deg; around a horizontal or vertical axis in the plane of 
the drawing. <br>
All flips result in stereocenter retention.</p>

<table><tr><td width="40%" valign="top">
<b>Horizontal flip <br>(around <i>y</i> axis)</b></td>
    <td><img src="sketch-basic_files/flip_horizontal.png" width="438" height="148" alt="flip_horizontal"/></td></tr>
    <tr><td valign="top">
<b>Vertical flip <br>(around <i>x</i> axis)</b></td>
    <td><img src="sketch-basic_files/flip_vertical.png" width="203" height="241" alt="flip_vertical"/></td></tr>
    <tr><td valign="top">
<b>Rotate 180&deg; <br>(in canvas plane, around <i>z</i> axis)</b></td>
    <td><img src="sketch-basic_files/flip_canvas.png" width="359" height="236" alt="flip_canvas"/></td></tr>
</table>

<h4>Horizontal Flip, Vertical Flip, Rotate 180&deg;</h4>
   
   <p>Flip a selection: 
   <ol>
       <li>Select part of the structure.</li>
       <li>Right-click on the structure or go to <b>Edit &gt; Transformation menu.</b></li>
       <li>Click on the command.</li>
   </ol></p>

   <p>Flip the whole structure without selection:
   <ol>
       <li>Go to the <b>Edit &gt; Transformation menu.</b></li>
       <li>Click on the command.</li>
   </ol></p>
   <p>Note: If no structure is selected, the right-click on the canvas will not offer the
   flip command.</p>
   
<h4>Group Flip</h4>

<p>The Group Flip operation can be executed only on a selected structure connected 
to the rest of the molecule by only one bond (of any type): the selection 
can not be in the 'middle' of a molecule. The selection is not permitted for disjunctive structures either.</p>
 <p>This operation rotates the selected group by 180&deg; around an axis 
 set on the bond connecting the selection to the rest of the molecule. Stereocenters 
 in the molecules are retained, the wedge bond styles change to keep the stereo information.</p>
 <table><tr><td width="10%">Group flip</td>
    <td><img src="sketch-basic_files/flip_group.png" width="374" height="221" alt="flip_group"/></td></tr>
    </table>
 <p>
     <ol>
         <li>Select part of the molecule.</li>
         <li>Right-click on the canvas and select <b>Transformation &gt; Group Flip</b>; or go to 
         the <b>Edit &gt; Transformation menu.</b></li>
         <li>Select <b>Flip &gt; Group.</b></li>
 </ol></p>

<h3><a class="anchor" name="mirror">Mirror a molecule</a></h3>

Apart from flipping Marvin is able to produce mirror images of the molecules or parts of.
These operations can be found in the <b>Edit &gt; Transformation menu</b>. If no selection
is made, the operation will be executed on all the structures present in the canvas. Stereocenters will be 
inversed. Mirroring horizontally means that the theoretical mirror is horizontal and 
placed perpendicular to the canvas (left-to-right mirroring); the vertical
mirroring means the mirror is vertical and perpendicular to the canvas 
(upside-down mirroring).
<table><tr><td width="50%">Horizontal mirroring (to <i>yz</i> plane)</td>
    <td><img src="sketch-basic_files/mirror_horizontal.png" width="269" height="149" alt="mirror_horizontal"/></td></tr>
    <tr><td>Vertical mirroring (to <i>xz</i> plane)</td>
    <td><img src="sketch-basic_files/mirror_vertical.png" width="128" height="216" alt="mirror_vertical"/></td></tr>
<tr><td>Mirroring to canvas plane (to <i>xy</i> plane)</td>
    <td><img src="sketch-basic_files/mirror_canvas.png" width="288" height="219" alt="mirror_canvas"/></td></tr>
</table>

 <p>Mirror a selection: </p>
<ol>
    <li>Select part the molecule.</li>
    <li>Right-click on the canvas or go to the <b>Edit &gt; Transformation menu.</b></li>
    <li>Click the command.</li>
</ol>
<p>Mirror the whole structure without selection:</p>
   <ol>
       <li>Go to the <b>Edit &gt; Transformation menu.</b></li>
       <li>Click on the command.</li>
   </ol>

<h4>Group Mirror</h4>

<p>In case of only one connecting bond between the selected and unselected parts of 
the structure, the Group mirror command is available. The group is mirrored to the plane perpendicular
 to the plane defined by the two atoms of the above mentioned connecting bond plus a neighboring atom 
(in the group) that is not collinear with the connecting bond.</p>

<h3><a class="anchor" name="inversion">Central inversion of a molecule</a></h3>

<p>This feature mirrors all compounds on the canvas or selected fragments in 3D to a selected inversion center. 
The chirality is changed, all R is inversed to S, and vica versa.
<li>The inversion center is the geometric center of the selected atoms: if there are more than one selected fragment, then
all fragments are inversed separately to their geometric center. </li>
<li>The inversion center is a selected atom: all fragments are mirrored to the selected atom.</p>
<table><tr>
    <td><img src="sketch-basic_files/inversion.png" width="475" height="320" alt="inversion"/></td></tr>
    </table>

<H2><a class="anchor" name="reactions">Reactions</a></H2>

<H3><a class="anchor" name="howto-draw.reactions">How to Draw Reactions</a></H3>

<P>You can place a reaction arrow on the canvas at any time, even on
a blank canvas. Only one reaction is allowed per molecule.</P>
<OL>
    <LI>Select the
        <a href="gui/toolbars.html#reaction" class="buttonName">Insert Reaction Arrow</a>
	button.  You will see the reaction arrow on the tip of the cursor when
        you move the mouse into the canvas area.</LI>
    <LI>Click the location of the tail of
	the arrow.
    <LI>Drag the mouse and release at the location of the head.</LI>
</OL>
<P STYLE="margin-bottom: 0">Once you have placed a reaction arrow
on the canvas, MarvinSketch considers each part of the molecule in
relation to the reaction.
All parts of the molecule that are before the arrow are considered reactants.
Every molecule after the arrow is a product, and the ones placed along the
arrow are considered agents. You can align and/or distribute the objects of the reaction scheme by selecting the relevant option in <b>Edit</b> &gt; <b>Object</b> menu. The centers of the objects will be considered during the alignment/distribution of objects.  <b>Note</b> Selected agent fragments and texts are moved together with the arrow while keeping the distance between them. </P>

<H3><a class="anchor" name="howto-map.reactions">How to Map Reactions</a></H3>

<P>The arrow tool provides the easiest manual way to map corresponding reactant
 and product atoms. Select the arrow tool, hold down the left mouse button on a
 reactant atom, and drag it to the corresponding product atom. The same map
number is added to both atoms marking, that they represent the same atom on the
two sides of the reaction scheme. Similar tool <a href="../sketch/gui/customization.html#manual_atom_map">"Manual Atom Map"</a> can be added by customization. There are also keyboard shortcuts for
mapping. Type m8, for example, and click on an atom. Atom map 8 is assigned to
that atom.</P>
<P> Marvin contains an automapper tool as well (available as
Structure &gt; Mapping &gt; Map Atoms) assigning map numbers to all selected atoms of
a reaction automatically.</P>
<P> Map numbers of the selected atoms can be removed by the
Structure &gt; Mapping &gt; Unmap Atoms menu item, or by typing m0 for the selected
atoms.</P>

<H3><a class="anchor" name="howto-draw.eflowarrow">How to Draw Electron Flow Arrows</a></H3>

<P>Electron flow arrow shows the actual direction of motion of the electrons. It can point from an atom or bond to an other atom or bond or even an incipient bond (formed after the electron transition).
<ol>
 <li>Select the arrow type (single electron flow or electron pair flow).  (Menu: <b>Insert</b> > <b>Electron Flow</b>) </li>
 <li>Move the cursor onto a bond or an atom of the structure on the canvas, left-click on it. (It will be the source of the electron flow.) </li>
 <li>Select the destination: move the mouse to the destination and click on it.</li>
</ol>
<img src="gui/gui-files/electronflow.png" width="350" height="402"><br>
<a href="gui/electronflow.html">See detailes of handling and displaying endpoints here.</a>
</P>

<!-- -->

<H2><a class="anchor" name="query.structures">Query Structures</a></H2>

<P>There are molecules that cannot be represented by a single structure. Although it is possible to run multiple structure searches in cascade, it is much more efficient to run a search only once using a 
well designed query structure. This structure often contains query features, possibly including complex conditional expressions for atoms and bonds. For a more detailed description of this please see 
the <a HREF="http://www.chemaxon.com/jchem/doc/user/query_features.html">Query Guide.</a></P>
<P>The easiest way to use <a href="../sketch/sketch-chem.html#generic-atoms">Query Atoms</a>, different Query Groups 
and <a href="../sketch/sketch-chem.html#atom-props">Query Atom Properties</a> is to find them on the <a HREF="gui/dialogs.html#periodic">Periodic Table</a>'s <a HREF="gui/dialogs.html#advanced">Advanced</a> tab. <br>

<H3><a class="anchor" name="atom.lists">Atom Lists and NOT Lists</a></H3>

<P>It is possible to define the type of an atom in a custom atom list. If the type of the corresponding atom in the target molecular structure is a member of the list, it is considered a matching atom. NOT lists can be used to specify atoms to be excluded in the search.</P>
<P><a href="../sketch/sketch-chem.html#atomlist">"Atom List"</a> and <a href="../sketch/sketch-chem.html#notlist">"NOT List"</a> can be reached from the Periodic Table's <a HREF="gui/dialogs.html#periodic">Periodic Table</a> dialog. </P>

<H3><a class="anchor" name="custom.properties">Custom Properties</a></H3>
<p>Values can be added to the following "Custom Property" types from the <a HREF="gui/dialogs.html#periodic">Periodic Table</a>'s <a HREF="gui/dialogs.html#advanced">Advanced</a> tab.:
<ul><b><a class="anchor" name="r-group">R-group</a></b>	Converts the atom to an R-group with an index of the input "Value". 
		(Only numerical values are allowed, with a maximum number of 32767.) 
		This atom can be used to describe an unknown or unspecified molecule part 
		or to draw an <a HREF="#howto-draw.rgroups">R-group query</a> or a <a HREF="#markush.structures">Markush structure</a>.</ul>
<ul><b><a class="anchor" name="alias">Alias</a></b>	Converts the Alias value to the atom label. 
		The input of the textbox is displayed as the atom label, 
		but the atom itself does not change.</ul>
<ul><b><a class="anchor" name="pseudo">Pseudo</a></b> Converts the input of the textbox "Value" to Pseudo atom type. 
		The input of the textbox is displayed as the atom label, 
		but the atom is replaced by an <i>"Any"</i> type <a HREF="sketch-chem.html#generic-atoms">Query atom</a>.</ul>

<ul><b><a class="anchor" name="smarts">SMARTS</a></b> Converts the input of the textbox "Value" to a complex 
		SMARTS query molecule or atom. 
		If the cursor is kept over the canvas during typing, 
		the conversion can be seen on-the-fly.</ul>

<ul><b><a class="anchor" name="value">Value</a></b> Adds the input of the textbox "Value" to the atom as its
		"Atom Value" property.</ul>

<H3><a class="anchor" name="query.groups">Homology Groups</a></H3>

<P>'Built-in' <a HREF="../calculations/homologygroups.html#definition">Homology groups</a> can be found in the '<a HREF="gui/dialogs.html#special.nodes">Special Nodes</a>' section of the 
<a HREF="gui/dialogs.html#periodic">Periodic Table</a>'s <a HREF="gui/dialogs.html#advanced">Advanced</a> tab, as a dropdown list, starting with "Alkyl". </P>
<P>More deatils of some other <a HREF="#query.structures">Query features</a> and <a HREF="../calculations/homologygroups.html#definition">Homology groups</a> are in the 
<a HREF="http://www.chemaxon.com/jchem/doc/user/query_features.html">Query Guide.</a></P>

<!--  -->

<H3><a class="anchor" name="howto-draw.rgroups">R-group Query</a></H3>

<P>An <a HREF="http://www.chemaxon.com/jchem/doc/user/query_special_types.html">R-group query</a> describes a set of derivatives in one query structure (substitution
variation). It can be drawn the following way: First
draw the root structure and place some R atoms either from the Periodic Table dialog window,
from the popup menu or by typing a corresponding label such as "R1" on the
keyboard.
Then draw the variable R-group ligands and select those substituting the R1 atom.
If you type "R1" now, the selected groups will be marked with "R1". Additional
R-group conditions (Occurrence, RestH, If-then) can be set in the R-logic dialog
window available from the <b>Structure &gt; Attributes</b> menu.</P>

<p>To draw the attachment points for the R-definitions, you can use menu
"<i>Atom > R-group Attachment</i>" from the menu (or <i>R-group Attachment</i> from the 
popup menu), or alternatively,
when you draw the R-definitions and the mouse cursor still shows "R1",
clicking on an atom of the definition will toggle the attachment point on that
atom. (Please note that divalent R-groups must have two attachment points
defined.)</p>

<H4>How to draw R-group queries -- Step by step example</H4>
<ol>
<li>Draw the root structure first.<br>
<img src="sketch-basic_files/r-group-step1.png" width="181" height="131"></li>
<li>Move the cursor to the atom where you would like to place the R-group.
(In this example, we place R-groups in place of the terminal carbon atoms.)
When the atom is highlighted (blue circle around the atom label), type the
shortcut of the required R-group ID (e.g. R1). Alternative solution is
selecting the ID from <em>R-group</em> sub-menu of the popup menu by pressing right
mouse button over the atom.<br>
<img src="sketch-basic_files/r-group-step2.png" width="180" height="122"></li>
<li>Draw an alternative ligand with an R-group connection: Move the cursor to an empty place on the canvas (take care that nothing is selected) then press
the shortcut of the next R-group (R2). The "in hand" object changes to the ID of the R-group (R2). (In this example, we add a ligand to terminal oxygen atom.)
Click the terminal oxygen, then drag the mouse. You will
see that the new bond is displayed and its orientation follows the cursor.
Release the mouse button when the bond stands in the right direction.<br>
<img src="sketch-basic_files/r-group-step3.png" width="193" height="121"></li>
<li>Draw new fragments to the canvas (separately from the root structure), which
will be the R-group definitions. (In this example, we draw the fragments for
the first R-group definition to the right side and the second R-group
    definition will be placed below the root structure.)<br>
<img src="sketch-basic_files/r-group-step4.png" width="365" height="220"></li>
<li>Next, define the R-group definitions. To do this, select those fragments
that the first R-group should contain (on the right side).
After the selection, press the shortcut of the R-group ID (R1). The ID and equal sign (R1=) will display beside the selected set and the "in hand" object
will be the R-group ID.</li>
<li>Define attachment point to R-group members: Click the left mouse button on
atoms where you would like place the attachment points.
Repeat this operation on the other definitions of the R-group.
(In this example, at the third definition, we select the left oxygen atom
for attachment.) Alternatively, you can define R-group attachment points
via the popup menu (by selecting <em>R-group Attachment</em> option on an atom of an R-group definition).<br>
<img src="sketch-basic_files/r-group-step5.png" width="367" height="247"></li>
<li>Create the second R-group by repeating the last two steps on the two
    remaining fragments.<br>
<img src="sketch-basic_files/r-group-step6.png" width="391" height="258"></li>
<li>In case of one attachment point, the connections are not numbered, only marked by a wavy line on the substituent side.
<br>In case of more than 1 attachment point, the connections are marked by numbers
 on the scaffold. Connection points on the substituents are marked with a wavy line, and
the order is indicated by numbers (except for the 1st).<br>
<img src="sketch-basic_files/r-group-attachmentpoints.png" width="413" height="277">
<br>If two R-groups are connected by a bond, the ligand order may be changed simply by the Bond &gt; Ligand order command. Simply select the bond in question and select the combination in the menu (also available upon mouseover in the context menu).
</li>
<br>
<img src="sketch-basic_files/ligandorder.png" width="404" height="406">
<li>You can define additional conditions, such as occurrence, rest H and if-then
expressions to R-groups in the R-logic dialog window. To do this,
select menu option <b>Structure &gt; Attributes &gt; R-logic</b>.
After setting the conditions in the <em>R-logic</em> dialog window, press the
<em>OK</em> button to apply the changes. R-logic can be visualized
 by switching on the Display &gt; Misc &gt; R-logic option.</li>
<img src="sketch-basic_files/r-group-step7.png" width="294" height="114">
</ol>

<H2><a class="anchor" name="markush.structures">Markush structures</a></H2>

A Markush structure is a description of compound classes by generic notations.
They are often used for patent claims and for combinatorial libraries.
Link R-groups, link nodes, atom lists, position variation and
 repeating units with repetition ranges are commonly used features in the representation of Markush structures.
<H4><a class="anchor" name="positionvariation">Position Variation</a> (variable point of attachment)</H4>
You can create a variable point of attachment to represent a variable connection point to a group of atoms.
The representation is similar to the above mentioned multi-center bonds.
For example:
<br><img src="sketch-basic_files/markush3.png" width="204" height="143"> <br>
The alternative attachment points are displayed with grey shadow.
If you move the cursor to the center (the bond ending in the ring) the represented atoms are
highlighted (blue circle around the atom labels).

<b>How to draw Position Variation:</b>
<ul>
	<LI> Draw the structure that will include the position variation.</LI>
	<LI> Select the alternative connection point atoms.</LI>
	<LI> Choose "Structure/Add/Multi-center" from the main menu or
	"Add/Multi-center" from the contextual menu. A multi-center represented by a "*" will
	be added. If you move the cursor to the multi-center the represented atoms are
	highlighted (blue circle around the atom labels).</LI>
	<LI> Draw a bond from the center and edit the bond if required. The represented atoms
	are displayed with grey shadow after this step. The "*" representing the multi-center
	disappears after bond drawing.</LI>
	<LI> Repeat step 2-4 to draw further variable points if required.</LI>
</uL>

<H4><a name="repeatingunit"></a>Frequency variation (Repeating unit with repetition ranges)</H4>

A sequence of ranges to specify the repetition can also be used in a special
 group called repeating unit with repetition ranges.  For example:
<br><img src="sketch-basic_files/markush2.png" width="141" height="141"> <br>
Here the repetition range is "3,5-7". The repetition count for the included structure
(enclosed by the brackets) can be: 3,5,6 or 7.
See <a href="sketch-basic.html#repeatingunits">Repeating units with repetition ranges</a>
for further information on drawing this feature.

<h4><a class="anchor" name="homology_groups"></a>Homology groups in a Markush structure</h4>

<p>The simplest way is to insert homology groups from the Periodic Table's Advanced tab.
<table>
	<tr>
		<td><img src="sketch-basic_files/homology_periodic.png" width="157" height="204"></td>
	</tr>
</table>
<ol>
<li>Open the Periodic Table (toolbar or from the Atom menu), choose the Advanced tab.</li>
<li>In the Special nodes section, choose the homology group from the dropdown list.</li>
<li>The homology group stays at the mouse pointer, you can click the atom(s) on the canvas. 
You don't need to close the Periodic Table to continue drawing.</li>
</ol>

<p><b>Editing the homology group properties in MarvinSketch</b>
<p>Select the homology group and right-click. Choose Edit Properties... Set the group properties in the dialog box. 
<p>
<table>
	<tr>
		<td><img src="sketch-basic_files/homology_properties_menu.png" width="340" height="143"></td>
	</tr>
</table>
</p>
<p>Here is an example of the property dialog window for a cycloalkyl group:
<p>
<table>
	<tr>
		<td><img src="sketch-basic_files/homology_properties_dialog.png" width="472" height="263"></td>
	</tr>
</table>
</p>

<p>By default, the atom and homology group properties are not shown. You can switch it on by checking the
 View &gt; Advanced &gt; Atom Properties menu.


<H2><a class="anchor" name="superatomSgoups">
<a class="anchor" name="howto-draw.sgroups">
How to Create Groups</a></a></H2>

<p>You can create a group easily from a structure. There are two possibilities:
<ul>
    <li>Select the molecule or part of the molecule. Click the Create Group button in the
    toolbar and edit the group properties in the dialog window.</li>
    <li>Click the Create Group button in the toolbar then select parts of the
        group. Upon releasing the mouse button, the Group dialog pops up.</li>
</ul>
<b>Command shortcut</b>: Ctrl-G
<p>Edit groups: (since version 5.3) right-click on the group, select Edit group
    from the contextual menu and the group dialog opens.<br>Alternatively, select
    the group atoms and select Edit Group from the structure menu, Group submenu.
<p><b>Group types</b>: In the dropdown list of the group type only those types are allowed which are enabled for the
 actual selection in the molecule (to enable all types: go to Edit &gt; Preferences &gt; Structure tab 
and uncheck the 'Validate S-groups at creation' box.)<br>
Enabling/disabling a group type depends on:
	<ul><li> The number of crossing bonds it would have.
	<li> The embedding of groups into each other: several conditions are checked here for the group to be created
		<ul>
		<li> whether it can be embedded into the groups which would contain it,
		<br>e.g. polymer S-groups can not be embedded into multiple S-groups
		<li> whether it can embed all the groups which would be contained by it,
		<br>e.g. structural repeating unit S-groups (SRU) can not embed monomers
		<li> whether it can be embedded directly into the group which would be its direct embedder,
		<br>e.g. component S-groups can be directly embedded only into ordered or unordered mixtures
		<li> whether it can directly embed the groups which would be embedded by it directly,
		<br>e.g. mixtures can directly embed only components.
		</ul>
	<li> Expandable S-groups are not allowed to be embedded into each other.
	</ul>

Since those group types which are allowed only for whole fragments (mixtures, components and monomers) are always extended to whole fragments, thus these types are allowed even if only fragment parts where selected, if they are correct when extended to the whole fragment. 
<br>
Extension to whole fragment is not allowed if the group type is changed by editing an existing group: in this case mixtures (etc.) are not allowed for fragment parts.

<H3><a class="anchor" name="abbreviatedgroups">Abbreviated (superatom) groups</a></H3>

Abbreviated groups are used to represent a part of a structure with a text abbreviation.
<ul><li><b>Insert an abbreviated group</b> into your sketch: type the name of the abbreviation<!-- 
(see list <a href="abbrevgroup_table.html">here</a>)-->, to complete a longer name, 
press ENTER or END after typing the first few characters. Typing group abbreviations is case sensitive: <i>e.g.</i>, 
typing either "NO" or "no" both lead to the nitrosyl (NO) functional group, while typing "No" results in the 
atom symbol of nobelium. If the cursor was placed 
over an atom, it will be automatically changed to the abbreviated group. If no atom was selected, 
the abbreviation is placed on the cursor. Click on the canvas to place it. If you would 
like to ungroup an S-group before placing it to the canvas, 
press the <i>SHIFT</i> button before you release the mouse on the desired 
location.</li>
<li><b>Create an abbreviated group</b>: Click the Create Group button in the toolbar then select the group atoms and bonds. 
	   Upon releasing the mouse button, the Group dialog window pops up (this dialog window may be opened from the 
	   Structure &gt; Group submenu as well). Name the group in the dialog window.</li></ul>
	   
<p>You can retrieve the hidden structure from the text abbreviation with the "Expand"
function and hide the structure with the "Contract" function.
Manipulation with abbreviated groups is possible with "Expand", "Contract",
"Ungroup", and "Remove" from the Group submenu.</p>
<p>A short animation about abbreviated groups: 
<a href="http://www.chemaxon.com/library/expand-and-ungroup-abbreviated-groups/">Expand and ungroup abbreviated groups</a>.</p>

<h4><a class="anchor" name="Sgoups_to_template">Add attachment points to abbreviated groups</a></h4>

<p>After creating the abbreviated group (see previous section), right-click the corresponding
atom and choose "Add S-group attachment", or select the atom and use the same option in the Atom menu. The attachment point is 
marked by a number in a green rectangle. This way, you
defined a connection point of this group. (Please note that attachment points can be added only to abbreviated groups, so 
it is important to define the group first.)
<br>There is no limit to how many attachment points can be added to an abbreviated group; 
they will be numbered in the order of their creation. Crossing bonds will connect to group atoms through their first free 
attachment point. Only attachment points not occupied by crossing bonds are marked by numbers in the expanded abbreviated group. 
Similarly, crossing bonds connect to a contracted abbreviated group through the first free attachment point of the whole group.
<br>Removal of an attachment point works the following way: Select the "Remove S-group attachment" option either from the pop-up 
menu or from the Atom menu to erase the attachment point with the highest number on the atom in question.<br>
When you defined an abbreviated group, you can add it to the templates. Select the group, right-click and press "Add to My Templates". 
The template can be inserted by typing its name and clicking on the canvas.</p>

<h4>Syntax of the abbreviated group name</h4>

Numbers are automatically subscripted unless "\n" is used or at the start of string.
Charges (+, -, ++, --, 3+ etc.) are automatically superscripted at end of string
 or if the following character is a closing parenthesis. <br>
Allowed control sequences in the abbreviated group name:
<UL>
<LI>\s - subscript </LI>
<LI>\S - superscript </LI>
<LI>\n - normal mode. </LI>
</UL>
Example: \S13CH4

<h4><a class="anchor" name="User_defined_abbreviated_groups">User-defined abbreviated groups</a></h4>

<p>Besides the default abbreviated groups you can also set up your own user-defined groups or redefine the default ones. 
Marvin stores its default groups in a formatted .txt file named <b>default.abbrevgroup</b> and by adding your own group file you can complement the default. 
To assemble your own <b>.abbrevgroup</b> file you should strictly follow <a href="../formats/abbrevgroup-doc.html">abbreviated groups file format</a>. 
The newly defined file must be named <b>user.abbrevgroup</b> and should be stored in the <a href="library_location.html">chemaxon folder of your home directory</a> in your file system. 
Note that Marvin gives priority to the user-defined abbreviated groups and overrides the default after redefinition.</p>
 
<H3><a class="anchor" name="multiplegroups">Multiple groups</a></H3>

Multiple groups are used to represent a repeating part in a structure with a shorter
form. To create a multiple group, click the Group tool on the toolbar, then select the structure involved.
Here you can specify a positive repeating count depending on how many times you want the structure to be repeated.
You can retrieve the whole structure from the condensed form with the "Expand"
function and shorten the structure with the "Contract" function.
Manipulation with multiple groups is possible with "Expand", "Contract",
"Ungroup", "Edit Group", and "Remove" from the Group submenu.

<h3><a class="anchor" name="comBrackets">Components, Unordered Mixtures and Ordered Mixtures</a></h3>

These features can be expressed by brackets (groups) of type component,
unordered mixture (also called mixture) and ordered mixture (also called formulation).
A component here is a set of atoms contained by a component bracket.

<h4>Ordered and unordered mixtures</h4>

An unordered mixture (denoted by "mix" at the bottom of the right bracket)
consists of several unordered components (denoted by "c" at the bottom of the right bracket).
For these types of mixtures, the order of addition during the
preparation  is not important. Example:
<p>
<img src="sketch-basic_files/mix01.png" width="360" height="181">
<p>
Ordered mixtures, on the other hand contain ordered
components, which define the order of addition. Example:
<img src="sketch-basic_files/for01.png" width="525" height="350"></p>

<h4>To draw an unordered component</h4>

<OL>
	<LI> Draw the structures that form the mixture.</LI>
	<LI> To define a structure as a component in a mixture, click the Group tool on the toolbar, then select the structure.</LI>
	<LI> In the "Create Group" dialog window choose "Component (c)" from the "Type" list.</LI>
	<LI> The "Order" field should be empty or should contain "none".
   	If the "Order" field already contains a number, just
   	delete it (you can type in "none" as well).</LI>
	<LI> Click OK.
</OL>

<h4>To draw an ordered component</h4>

<OL>
	<LI> Draw the structures that form the mixture.</LI>
	<LI> To define a structure as a component in a mixture, click the Group tool on the toolbar, then select the structure.</LI>
	<LI> In the "Create Group" dialog window choose "Component (c)" from the "Type" list.</LI>
	<LI> If this is the first component of the mixture,
   	click the "Order" field and enter "1" in place of "none".
   	If the "Order" field already contains a number Marvin will automatically increment the
   	"Order" field for subsequent components.</LI>
	<LI> Click OK.
</OL>

<h4>To draw a mixture</h4>

<OL><LI> Create the components to form the mixture.</LI>
	<LI> Click the Group tool on the toolbar, then select the structures.</LI>
	<LI> In the "Create Group" dialog window choose the type
	("Ordered mixture(f)" or "Unordered mixture(for)") from the "Type" combobox.</LI>
	<LI> Click OK. </LI>
</OL>

<h4>To change the type of a mixture</h4>

<OL>
	<LI> Hover the mouse over the group. </LI>
	<LI> Choose "Edit Group" from the contextual menu
	(right mouse click on the selected mixture). </LI>
	<LI> Change the type of the mixture. </LI>
	<LI> Click OK.</LI>
</OL>

<h4>To add a new component to a mixture</h4>

<OL>
	<LI>Draw the new component. </LI>
	<LI> Drag one part of the bracket and move it to enclose the new component.</LI>
</OL>

<h4>To delete a component from a mixture</h4>

<OL>
	<LI>Select the component. </LI>
	<LI> Press the Delete button on your keyboard or select the Erase tool.</LI>
	</OL>

<H3><a class="anchor" name="polymers">Polymers</a></H3>

The polymer structure consists of structural fragments. These fragments are enclosed by polymer
brackets. The meaning of a polymer bracket is that the fragment within the brackets can
repeat with itself. The fragment within the bracket is called repeating unit.
Polymers can be represented as structure-based or source-based polymers depending on how
much structural detail is known.

<H4><a class="anchor" name="source-based">Source-based representation of polymers</a></H4>

You can use the monomer (mon) or mer (mer) repeating unit types to draw a polymer where only the
source-based representation is known. For example:
<p>
<img src="sketch-basic_files/polymer1.png" width="118" height="111"></p>
<p>
To draw a repeating unit, click the Group tool on the tooolbar, then select the atoms you want to be included.</p>

<H4><a class="anchor" name="structure-based">Structure-based representation of polymers</a></H4>

You can use the structural repeating unit type (SRU) to draw a polymer where the structure-based
representation is known.
Each SRU S-group has two or more dedicated bonds, called
crossing bonds, which cross the brackets. The crossing bonds of an SRU show
how the repeating units may connect in several ways to each other within the polymer.
Depending on the number of crossing
bonds and brackets we differentiate the following polymers and connectivities within the polymer:
<ul>
<li>Polymers with two crossing bonds.
     If the polymer has one crossing bond on each bracket of the SRU
     there are three possibilities for the repeating pattern:
     <ul>
      <li> head-to-tail </li>
      <li> head-to-head </li>
      <li> either/unknown </li>
     </ul>
<p>
<img src="sketch-basic_files/polymer2.png" width="126" height="83"></li></p>
<p><li>Ladder-Type Polymers.
      Polymers with paired brackets and with two crossing bonds on each bracket are
      called ladder-type polymers. Here it must be specified how the two crossing bonds
      on each bracket connect to the corresponding bonds of the adjoining repeating units.
      Additionally to the head-to-tail, head-to-head connectivity information
      there is flip information to specify whether the repeating unit flips around the polymer
      backbone when it connects to the adjoining SRU.
      These types of information are handled only in case of brackets
      with exactly two crossing bonds on both side (head and tail side).
      We differentiate the following polymer connectivities:
      <ul>
      <li> head-to-tail with no flip
      <li> head-to-tail with flip
      <li> head-to-head with no flip
      <li> head-to-head with flip
      <li> either/unknown
      </ul></p>
<img src="sketch-basic_files/polymer3.png" width="134" height="106"></li>
<li>Polymers with three or more brackets.
      If the polymer has three or more bonds with a separated bracket on each bond,
      the polymer always has the either/unknown repeating pattern.
</ul>
The end groups of polymers are often unknown or unspecified which are represented by star atoms (*).
The modified (mod), grafted (grf) and crosslinked (xl) form of a structural repeating unit can be drawn as well.

<H4><a name="copolymers" class="anchor">Copolymers</a></H4>

If the structure consists of more than one repeating unit (mon, mer) or structural repeating unit,
 Copolymer (co)  brackets/groups can be used to represent the structure. Copolymers might contain crossing bonds and
star atoms.
The following copolymers can be drawn:
<ul>
<li>random(ran)
<li>alternating(alt)
<li>block with or without junction unit (blk)
<li>copolymer to represent modified polymers (mod)
<li>copolymer to represent grafted polymers (grf)
<li>copolymer to represent cross-linked polymers (xl)
</ul>
 For example:
<br>
<img src="sketch-basic_files/polymer4.png" width="278" height="215">

<h4>To draw a simple polymer</h4>

<OL>
	<LI> Draw the structure that forms the polymer. </LI>
	<LI> Click the Group tool on the toolbar, and select the structure. Leave out the atoms that should be replaced by "*" (star atoms).</LI>
	<LI> In the "Create Group" dialog window choose the appropriate type from the "Type" list.</LI>
	<LI> Set the polymer repeat pattern if necessary.</LI>
	<LI> Click OK. The star atoms ("*") will be added automatically.</LI>
</OL>

<h4>To draw a ladder-type polymer</h4>

<OL>
	<LI> Draw the structure that forms the polymer. </LI>
	<LI> Click the Group tool on the toolbar, and select the structure. Leave out the atoms that should be replaced by "*" (star atoms).</LI>
	<LI> In the "Create Group" dialog window choose the "SRU polymer" type from the "Type" list.</LI>
	<LI> Set the polymer repeat pattern if necessary.</LI>
	<LI> Click OK. The star atoms ("*") will be added automatically.</LI>
	<LI> To create a bracket that crosses two bonds select the two brackets
            each crossing a bond and click <b>Merge Brackets</b> in the contextual menu.
</OL>

<h4>To draw a copolymer</h4>

<OL>
	<LI> Create the components to form the copolymer.</LI>
	<LI> Click the Group tool on the toolbar then select the components to be included.</LI>
	<LI> In the "Create Group" dialog window choose the type
	("Copolymer (co)", "Copolymer, alternating (alt)", "Copolymer, block (blk)" or "Copolymer, statistical (stat)") from the "Type" list.</LI>
	<LI> Click OK. </LI>
</OL>

<h4>To change the type of a polymer</h4>

<OL>
	<LI> Hover the mouse over the group. </LI>
	<LI> Choose "Edit Group" from the contextual menu
	(right mouse click on the selected mixture). </LI>
	<LI> Change the type of the polymer. </LI>
	<LI> Click OK.</LI>
</OL>

<h4>To add a new subpolymer to a copolymer</h4>

<OL>
	<LI> Draw the subpolymer to add outside of the bracket. </LI>
	<LI> Drag one part of the bracket to include the new subpolymer. The new molecule should be marked with blue circles when you hover the mouse cursor over it.</LI>
</OL>

<h4>To delete a subgroup from a copolymer</h4>

<OL>
	<LI> Select the subpolymer to delete. </LI>
	<LI> Press the Delete button on your keyboard or with the Erase tool. </LI>
</OL>

<H3><a class="anchor" name="repeatingunits">Repeating units with repetition ranges</a></H3>

A sequence of ranges to specify the repetition can also be used in a special group called <b>repeating unit with repetition ranges</b>.	For example:
<br><img src="sketch-basic_files/markush1.png" width="349" height="143"> <br>
Here the repetition range is "3,5-7". The repetition count for the included structure can be: 3,5,6 or 7.

<h4>Syntax of the repetition ranges</h4>

The <b>repetition ranges</b> consist of ranges separated by commas. A range can be either a simple non-negative number (e.g. 3)
or two non-negative numbers separated by "-" (e.g. 5-7).

<h4>To draw a repeating unit with repetition ranges</h4>

<OL>
	<LI> Draw the structure that forms or contains the repeating unit. </LI>
	<LI>  Click the Group tool on the toolbar, and select the structure. </LI>
	<LI> In the "Create Group" dialog window choose the type "Repeating unit with repetition ranges" from the "Type" list.</LI>
	<LI> Set the repetition ranges.</LI>
	<LI> Click OK.</LI>
</OL>

<h3><a class="anchor" name="groupcharge">Charge of the group</a></h3>

<p>Four types of groups can be assigned a charge sign: generic, component, monomer and mer groups.
During group creation, you have the option to display the charge on the charged
atom itself or the whole group. In the latter case, the charge will be displayed
outside of the bracket on the right. If any additional charges are added (negative
or positive) the net charge will be calculated and displayed. The charge-bearing
atom can be revealed by pointing the cursor over the group (in select mode). To
replace the charge, select the group and go to the Structure menu, Group submenu and click
Edit Group (or right-click the selected group, and select Edit Group).
<br><img src="sketch-basic_files/groupcharge.png" width="317" height="128" alt="groupcharge"/>
</p>

<H2><a class="anchor" name="howto.draw.graphic">How to Draw Graphic Objects
and Text Boxes</a></H2>

<p>To draw a (poly)line, rectangle or text box, use the
<B class="buttonName">Insert</B> menu or the toolbar (if visible). These objects
are depicted in blue color outlines to indicate that any object here does not 
bear any chemical meaning like reaction arrows or S-group brackets (in black). 
 Point the mouse to the
desired position on the canvas, click and hold the left button, move the mouse
and release the button. To create a small rectangle or text box click again.</p>
<p> The shape of an object is changeable or resizable by dragging one of its points to do it.</p>


<p><a class="anchor" name="howto.insert.text"></a>After placing a text box,
you can immediately use the keyboard to type a text. Symbols can be inserted directly through the Insert symbol tool, <img src="gui/gui-files/icons/symbol16.png">.  The tool contains the list of the most commonly used symbols by default. This list will be updated according to your latest selections. Click on the relevant symbol and it will appear in the textbox. If the desired symbol is not on the list, click on More Symbols for the full character list.</p>
<p><img src="gui/gui-files/insertsymbol.png" width="620" height="172" alt="insertsymbol"></p>
<p>To change the contents of a text box, choose
<b class="buttonName">Select</b> mode, click on the box, then use the
keyboard.</p>
<p>You can place a text box with the IUPAC name(s) from the <b> Structure > Structure to Name > Place IUPAC Name</b> menu command and it will be automatically inserted under the structure. The name will be updated in real-time.</p>

<H2><a class="anchor" name="howto.draw.linkAtom">How to Draw a Link atom</a></H2>

<p>You can draw link atoms using the popup menu in two ways:</p>
<ol>
    <li>Right-click on the atom to bring up the popup menu. Select the required
    repetition number from the "Link node" submenu. Marvin will find out the
    outer (non-repeating) bonds for you.</li>
    <li>Select the atom you would like to be the link node and two neighboring
    bonds for outer (non-repeating) bonds. Right-click anywhere on the canvas
    to bring up the popup menu. Select the required repetition number from the
    "Link node" submenu.</li>
</ol>

<p>Marvin will advise you if it is not possible to create a link node for the
specified configuration (for example at ring fusions).</p>

<p>Outer (non-repeating) bonds will be denoted by brackets crossing them, and
the repetition numbers will be put on the atom. All portion of the molecule
connected to the link atom through non-outer bonds are supposed to repeat
together with the atom. See examples below.</p>

<table cellpadding=5 cellspacing="0" id=grid>
<tr>
    <td align=center> Molecule with link node </td>
    <td align=center> Meaning </td>
</tr>
<tr>
    <td align=center><img src="sketch-basic_files/ln02.png" width="67" height="42"></td>
    <td align=center><img src="sketch-basic_files/ln02_m.png" width="165" height="29"></td>
</tr>
<tr>
    <td align=center><img src="sketch-basic_files/ln03.png" width="150" height="67"></td>
    <td align=center><img src="sketch-basic_files/ln03_m.png" width="243" height="334"></td>
</tr>
<tr>
    <td align=center><img src="sketch-basic_files/ln01.png" width="71" height="103"></td>
    <td align=center><img src="sketch-basic_files/ln01_m.png" width="291" height="177"></td>
</tr>
</table>

<p>To edit a link node repetition number or change outer bonds, repeat the
drawing steps above. To make a link atom ordinary atom again, select "Off" from
the "Link node" submenu.</p>

<H2><a class="anchor" name="howto.select.structure">How to Select a Structure</a></H2>

<OL>
    <LI>Set Selection mode on by clicking one of the three available
        <a href="gui/toolbars.html#select"
        class="buttonName">Selection buttons</a>.
	<OL TYPE=A>
            <LI>To select a single atom, click
                on it.</LI>
            <LI>To select two joined atoms,
                click on the bond that links them.</LI>
            <LI>To select a rectangular region, choose Rectangle Selection,
		click at one corner of the desired region and drag the mouse
                to the opposite corner. While the mouse button is pressed
                down, a guide will be displayed to aid you.</LI>
            <LI><a name="lasso"></a>To select a non-rectangular
		region, choose Lasso Selection, press the left mouse button to start selecting,
                and draw the region with your mouse without releasing the mouse button.
                A blue guide line appears along the selection region. A pink line
                will connect the start and end points.</LI> 
            <li>To select a fragment
                <ul>
                    <li>double-click on an atom or bond using Rectangle or Lasso selection,</li>
                    <li>or use the Structure Selection button and single-click on an atom or bond.</li>
                </ul>
            </li>
        </OL>
    </LI>
</OL>
<P>You can unselect all by clicking an empty area of the canvas.</P>

<H2><a class="anchor" name="howto.delete.structure">How to Delete a Structure</a></H2>

<P>Using the <a href="gui/toolbars.html#erase" class="buttonName">Erase</a>
button:</P>
<OL>
    <LI><P STYLE="margin-bottom: 0">Set Erase mode on by clicking the
	<B class="buttonName">Erase </B> button.</P>
	<OL TYPE=A>
            <LI><P STYLE="margin-bottom: 0">To erase a single atom or bond,
		click on it. The deletion of the terminal bond deletes the terminal atom by default. 
		Pressing the Alt button while deleting the bond, the terminal atom is not deleted. To 
		change the default behaviour, go to Edit &gt; Preferences &gt; Bonds tab and choose the desired 
		Terminal Bond Deletion Method.</P>
            <LI><P STYLE="margin-bottom: 0">To erase a rectangular region,
		click at one corner of the desired region and drag the mouse
                to the opposite corner. While the mouse button is pressed down,
                 a guide will be displayed to aid you.</P>
            <LI><P STYLE="margin-bottom: 0">To select a non-rectangular
		region, use the <a href="#lasso">lasso selection</a> function first, then press the Erase button.</P>
            </LI>
	</OL>
    </LI>
</OL>
<P>Using Selection mode:</P>
<OL>
    <LI><P STYLE="margin-bottom: 0">Select a portion of the structure.</P>
    </LI>
    <LI><P>Click the <B class="buttonName">Cut</B> button or use the DELETE
        button on your keyboard.</P></LI>
</OL>
<P>Using pop-up menus:</P>
<OL>
    <LI><P STYLE="margin-bottom: 0">Right click on an atom or bond.</P></LI>
    <LI><P>Select <B class="buttonName">Remove</B> from the pop-up menu.</P>
    </LI>
</OL>

<H2><a class="anchor" name="howto-alter">How to Work with Structures</a></H2>

<H3><a class="anchor" name="edit.visual">Visually Editing the Structure</a></H3>

<P>You can edit a molecule using the methods described in
<a HREF="#howto-draw">How to Draw Structures</a> and
<a HREF="#howto.delete.structure">How to Delete a Structure</a>.</P>

<H3><a class="anchor" name="edit.source">Editing the Source</a></H3>

<P>You can alter a molecule by directly editing its source in the
Edit Source Window. You can view and edit the source in any of the
supported file formats. To change format, simply select the desired
one from the <B class="buttonName">View</B>
Menu. If there are multiple molecules on the canvas, checking <b>View as multiple molecules</b> in the 
<B class="buttonName">View</B> Menu leads to each molecule appearing in a separate block in the source.
This feature works only, if the selected format is able to handle multiple structures.</p>
<p>To reload the molecule described by the text in this window
into the MarvinSketch canvas (including any changes you may have
made), select <B class="buttonName">File &gt;
Import As</B>. If the automatic format 
recognition detects a file format (checking it by a priority list), it will be
offered in the Select Import Mode field (Import as Recognized, indicating the file type in brackets).
If the structure is associated to a file type of higher priority than your choice, 
choose the Import As option to set the file format. <br>
    For example, you want to create the seryl-asparagine dipeptide: write "SN" in the Source, 
    then select Import. The automatic option detects 
    it as SMILES, but if you select the Import As option, and then the 
    "Peptide Sequence" from the list, it will be imported correctly.<br>
In addition, there are some cases when the automatic recognition cannot detect 
the file format, even though the entered text is correct (although it is very 
rare). In this case the Import As Recognized option is disabled and you 
have to choose the format from the list of the Import As option.</p>

<H3><a class="anchor" name="clean">Cleaning</a></H3>

<P>Marvin allows you to clean your molecule in either 2D or 3D. Cleaning
will calculate new coordinates for the atoms. Generating conformers and
choosing the favored one is also supported.
You can initiate cleaning via the <B class="buttonName">Structure
&gt; Clean2D/3D</B> submenu. For more information on molecule cleaning, please visit
<a href="http://www.chemaxon.com/marvin/doc/user/cleanoptions.html">this link</a>.</P>
<h4>Submenus</h4>
<ul><li>Clean 2D
    <ul>
      <li>Clean in 2D: cleans the molecule(s) in 2D</li>
      <li>Hydrogenize Chiral Cneter: adds an explicit hydrogen with a wedge bond to
      chiral centers which have no terminal atoms as substituents</li>
      <li>Clean Wedge Bonds: changes wedge bonds for convention display</li>
    </ul>
  </li>
  <li>Clean 3D
    <ul>
      <li>Clean in 3D: cleans the molecule(s) in 3D</li>
      <li>Cleaning Method: choosing from various methods</li>
      <li> Display Stored Conformers: works only if conformers of the sketched molecule had 
        been generated with the help of the Conformer plugin, choosing the 
        'Store conformer information in property field' option. See details in the 
        <a href="http://www.chemaxon.com/marvin/help/calculations/conformation.html#conformer">
        plugin's documentation</a>.
      </li>
    </ul>
  </li>
</ul>

<H3><a class="anchor" name="aromatic">Aromatic Rings</a></H3>

<P>You can toggle the display of rings as aromatic using the
<B class="buttonName">Structure &gt; Aromatization </B>submenu.</P>

<H2><a class="anchor" name="display-structure">Structure Display Options</a></H2>

<P>There is a wide range of functions related to the display of the
molecules. These settings can be found in the
<a HREF="gui/menubar.html" class="buttonName">View menu</a> and
the <a href="gui/dialogs.html#preferences" class="buttonName">Preferences dialog window</a>.
Additionally, you can move, rotate, and zoom in/out on the structure.</P>

<H3><a class="anchor" name="move-rotate">Moving and Rotating</a></H3>

<P>You can move or rotate a selected structure.</P>
<P STYLE="margin-bottom: 0">First, select the part of the structure
	you wish to move. </P>
    <ol TYPE=A>
        <LI><P STYLE="margin-bottom: 0"><b>Moving the selection:</b></P>
            <OL>
                <LI><P STYLE="margin-bottom: 0">Move the mouse pointer toward
			the center of the selected structure until a blue
                        rectangle appears. <br>
                        (You can also use the Space key to change between transformation modes.)</P></LI>
                <LI><P STYLE="margin-bottom: 0">Translate the selection by
			dragging the mouse.</P></LI>
            </OL></LI>
        <LI><P STYLE="margin-bottom: 0"><b>Rotating the selection:</b></P>
            <OL>
                <LI><P STYLE="margin-bottom: 0">Move the mouse pointer toward
			the outline of the molecule until a blue gear appears.<br>
                        (You can also use the Space key to change between transformation modes.)
                    </P>
                </LI>
                <LI><P>Rotate the selection by dragging the mouse.</P></LI>
            </OL></LI>

        <li><P STYLE="margin-bottom: 0"><b>Rotating the selection in 3D:</b></P>
Rotation in 3D of the following structural parts is possible:
 <ul><li>all compounds on the canvas, <li>selected fragments, <li>selected groups. </ul>
<p>Rotation of all compunds on the canvas in 3D can be accomplished by the View &gt; Mouse mode &gt; Rotate in 3D menu option.<br>
The axis of the 3D rotation for selected objects can be determined in the Edit &gt; Transform &gt; Rotate in 3D menu (or from the contextual menu) by choosing from the following list:
<ul>
	<li>Around an arbitrary axis defined by two atoms: in this case you are asked to select the atoms prior to the rotation.
	<li>Around x axis: horizontal axis in the plane of the canvas
	<li>Around y axis: vertical axis in the plane of the canvas
	<li>Around z axis: axis perpendicular to the plane of the canvas
	<li>Free 3D rotation: the rotation follows the movement of the mouse (click&amp;drag). 
	<br>(Note: 3D rotation mode until version 5.3.x: pressing the Space key 3 times initiates the free 3D rotation.)
	<li>Group Rotate: available only for a selected group in a molecule. The connecting bond(s) is recognized between the 
	selected and unselected parts of the structure and selects the rotation axis accordingly. 
</ul>
</p>		   
<p>The rotations are visualized by the fog effect: parts of the molecule behind the canvas are of 
lighter colour than the parts on the canvas. To see best the 3D view, use white background (View &gt; Colors &gt; White Background).</p>
 </li>
 <li><P STYLE="margin-bottom: 0"><b>Customized tool: 3D plane:</b></P>
            <ol>
                <LI><P STYLE="margin-bottom: 0">Select 3 atoms in the molecule.</P>
                </LI>
                <LI><P>Click the 3D Plane button or select Edit &gt; Transformation &gt; 3D Plane.
    <img src="sketch-basic_files/3dplane.png" width="82" height="25" alt="3dplane"></td>
 The selected 3 atoms will lie in the plane of the canvas. 
The coordinates are changed, not only the view of the structure.</P></LI>
            </ol> </li>    
</ol>
<ul> <li>Note: currently 3D coordinates of brackets (e.g. monomer, component type groups)
                    are not correctly updated when rotating the molecule in 3D mode. Avoid when possible.</ul>

<H3><a class="anchor" name="scalling">Scaling</a></H3>

<P>Set the magnification of the molecule on the canvas by the
<a HREF="gui/toolbars.html#general">Zoom buttons</a>. If you have a mouse with a wheel, hold down the Ctrl key, and then scroll the wheel to zoom in or out.
When a molecule is loaded into the sketcher, it is scaled
automatically to fit the window.</P>
<P>Individual objects (bonds, reaction arrows, graphical objects, text boxes) or sets of objects can be scaled, too. Selecting these objects, corners of 
a bounding rectangle will appear. Dragging one of these corners, the selection will be scaled proportionally. In case of bond scaling, the percentage of the current bond 
length relative to the default value will be visible. The same result can be achieved by opening the <a href="gui/dialogs.html#format"><b>Format...</b></a> dialog either through the 
<b>File &gt; Document Style</b> option or from the pop-up menu.</P>

<H3><a class="anchor" name="format">Molecule Format</a></H3>

<P>You can set the display format for the molecule and screen
resolution using the <B class="buttonName">View
&gt; Structure Display</B> submenu. Available molecule formats are
<B class="buttonName">Wireframe, Wireframe
with Knobs, Sticks, Ball and Stick,</B> and <B class="buttonName">Spacefill</B>.
You can set the resolution to low or high via the <B class="buttonName">Quality</B>
submenu.</P>

<H3><a class="anchor" name="molcolor">Colors</a></H3>

<P>The <B class="buttonName">View &gt;Colors</B>
submenu allows you to specify the <a HREF="../developer/sketchman.html#parameters.colorScheme">color
scheme</a> of the molecules. The available options are:</P>
<UL>
	<LI><P STYLE="margin-bottom: 0">Monochrome
	</P>
	<LI><P STYLE="margin-bottom: 0">CPK
	</P>
	<LI><P STYLE="margin-bottom: 0">Shapely - based on RasMol's
	shapely color scheme for nucleic and amino acids
	</P>
	<LI><P STYLE="margin-bottom: 0">Group - based on PDB residue
	numbers
	</P>
	<LI><P>Atom Set
	</P>
</UL>

<H3><a class="anchor" name="implicit-explicit-H">Implicit/Explicit Hydrogens</a></H3>

<P>Marvin has a number of options for the display of implicit and
explicit hydrogens. Because Marvin is chemically intelligent, it will
automatically add hydrogens as necessary within the structure.
Generally, these will be implicit and displayed based on the options
set in the <B class="buttonName">View</B>
menu.</P>
<P>To view all hydrogens explicitly, displayed as atoms with bonds to
neighbors, chose <B class="buttonName">Structure
&gt; Add &gt; Add Explicit Hydrogens</B>. The <B class="buttonName">Structure
&gt; Remove &gt; Remove Explicit Hydrogens</B> will return to the previous display mode.</P>
<P>To view implicit hydrogens by symbol, use the <B class="buttonName">View
&gt; Implicit Hydrogens </B>menu group. This option
is disabled in Spacefill and Ball &amp; Stick display modes.</P>

<H3><a class="anchor" name="carbon-displaying">Displaying the label of carbon atoms</a></H3>

<P>Displaying the label of carbon atoms in structures is possible the following way:
<ul>
    <li>Always - Always show the atom labels of carbon atoms.</li> 
    <li>Never - Never show the atom labels of carbon atoms.</li> 
    <li>At straight angles and at impl. Hs - Show the atom labels of carbon atoms at straight angles and at implicit Hydrogens.</li>
</ul>
This option can be set in the  <B class="buttonName">Display</B> tab of the <B class="buttonName">Edit
&gt; Preferences </B> box.</P>

<H3><a class="anchor" name="error-hilight">Error Highlighting</a></H3>

<P>Marvin can not automatically correct all valence errors or any reaction errors. Instead,
these errors are highlighted and you may make the
appropriate corrections yourself.
This option can be enabled and disabled through the <B class="buttonName">Edit
&gt; Preferences </B> box.</P>

<H2 ALIGN=LEFT><a class="anchor" name="preferences">Saving Display Options</a></H2>

<p>Many of the display settings in Marvin are saved and reloaded the next time you start the program.
    Background color, molecule color scheme, and hydrogen visibility can be set
    from the <B class="buttonName">View menu</B> and will be saved automatically when you exit the program.
    Other options, including look & feel, error highlighting, and object visibility
    can be set using the <B class="buttonName">Preferences</B> dialog window from
    the <B class="buttonName">Edit menu</B>.</p>

<H2><a class="anchor" name="other-windows">Launching Other Windows</a></H2>

<H3>2D and 3D Viewer Windows</H3>

<P>Choosing <B class="buttonName">View &gt;Open 2D Viewer</B> or
<B class="buttonName">Open 3D Viewer</B>
launches a MarvinView window containing the current molecule of MarvinSketch.</P>

<!--<H3>Sketcher Window</H3>
When working with applets within a web page, it may be helpful to view your
work in a MarvinSketch window outside of the html.
<BR>
To open your work in a separate Sketch window from
applets, choose <B class="buttonName">View &gt; Sketcher Window</B>.-->

<H2><a class="anchor" name="styles">How to customize structure drawing styles</a></H2>

<P>More advanced display format can be obtained for the molecule by applying format styles.
Format styles in Marvin include the setting of the following attributes:</p>
<ul>
<li>type of atom font,</li>
<li>size of atom font,</li>
<li>color of atoms,</li>
<li>thickness of bonds,</li>
<li>color of bonds,</li>
<li>length of bonds.</li>
</ul>
All these options can be collectively set using styles.
To load or define styles use the <B class="buttonName">File &gt; Document Style</B> menu.
This menu brings up the <a href="gui/dialogs.html#format">"Format of
the current document" dialog</a> in which atom and bond format options can be specified.
The original attributes for
atoms and bonds can be restored by using the <B class="buttonName">Reset</B>
functions of the dialog at any time.
<p>When loading a molecule all atoms/bonds belong to the default atom/bond set
if no styles were applied previously.
After selecting an atom/bond set and applying a style for it, the selected atoms/bonds are
removed from the default atom/bond set and a new set is created from the atoms/bonds with new style.
All the atoms/bonds, whose style were not yet modified by selection and applying a style on them,
still belong to the default atom/bond set.</p>
<p>Your changes might be applied for a set of atoms/bonds:</p>
<ul>
<li>for the selected atom/bond set,</li>
<li>for the default atom/bond set,</li>
<li>for all the atoms/bonds.</li>
</ul>
The top three radio buttons specify the target of the format settings being edited
 in the dialog.
The "Apply changes for all the atoms/bonds" option allows loading of predefined styles
 or creation of custom styles using the <B class="buttonName">Load Style</B> and <B class="buttonName">Save Style</B> buttons.

<H3>Loading a style</H3>
<P>After pressing the <B class="buttonName">Load Style</B> button, you can load a style from a combo box or browse amongst the previously defined
style files. The chosen style will be loaded into the "Structure Drawing Properties" in the "Format of the current document" dialog.</P>

<H3>Saving a style</H3>
<P>Set the "Structure Drawing Properties" you wish to save and press the
<B class="buttonName">Save Style</B> button to get to the "Save" dialog where you can enter the name of the style file and save the style.
All your own saved files will be stored under the <a href="library_location.html">chemaxon/styles/ directory</a> of your home directory and will be added to the combobox items.
A new style file can be added to the chemaxon/marvin/styles directory under the Marvin installation directory.
This new style file has to be listed in file chemaxon/marvin/styles/styleFileList.properties. The new style file
will be copied to the <a href="library_location.html">chemaxon/styles/ library</a> in your home directory and appear in the combobox of the "Loading of a journal style" dialog.
 (Existing style files will not be overwritten.)</P>

<P ALIGN="CENTER"><a HREF="#">Return to Top</a></P>
<p ALIGN="CENTER">
Copyright &copy; 1998-2013
<a HREF="http://www.chemaxon.com">ChemAxon Ltd.</a>
</p>
<p ALIGN="CENTER">
<a HREF="http://www.chemaxon.com/marvin">http://www.chemaxon.com/marvin</a>
</p>

</BODY>
</HTML>
