<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2//EN">
<HTML>
<HEAD>
    <META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
    <TITLE>MarvinSketch Graphical User Interface</TITLE>
    <link rel=stylesheet type="text/css" href="../marvinmanuals.css">
</HEAD>
<BODY>
<H1>MarvinSketch Graphical User Interface</H1>
<P><A HREF="sketch-index.html">Table of Contents</A> </P>

<P>The default layout of the MarvinSketch user interface is shown in the following picture.</P>
<P><IMG SRC="gui/gui-files/msketch.png" WIDTH="500" Height="480" NAME="Graphic1"
    ALT="MarvinSketch Main Window" ALIGN=MIDDLE WIDTH=524 height="516" BORDER=0>
</P>

<p>
It consists of the following primary components:</p>
<ul>
    <li><a href="gui/menubar.html">Menu Bar:</a> It is located at the top of the main frame, containing menu titles that describe the content of each menu.<br><br></li>
    <li><a href="gui/canvas.html">Canvas:</a> This is the main area where chemical structures, queries and reactions are drawn.<br><br></li>
    <li><a href="gui/toolbars.html#general">General Toolbar:</a> This toolbar contains buttons for freqently used commands.<br><br></li>
    <li><a href="gui/toolbars.html#tools">Tools Toolbar:</a> Contains basic elements for stucture drawing like bond, chain, reaction arrow, graphics, etc.<br><br></li>
    <li><a href="gui/toolbars.html#atoms">Atoms Toolbar:</a> Location of the most freqent atom types and the <a href="gui/dialogs.html#periodic">Periodic System</a> button.<br><br></li>
    <li><a href="gui/toolbars.html#templates">Advanced Templates Toolbar:</a> This special toolbar is a container of structure templates. The templates are rotatable by pressing and holding down the left mouse button while dragging. <br><br></li>
    <li><a href="gui/statusbar.html">Status Bar:</a> Shows file status, contains navigation buttons and the dimension button.<br>
        The Status Bar appears at the bottom of the main frame, and unlike toolbars, it cannot be customized or moved.<br>
        Some buttons of the Status Bar appear dynamically when you invoke the corresponding command, like enabling multipage molecular documents.
    </li>
</ul>

<P ALIGN=CENTER><A HREF="#">Return to Top</A></P>
<p ALIGN=CENTER>
Copyright &copy; 1998-2013
<a HREF="http://www.chemaxon.com">ChemAxon Ltd.</a>
</p>
<p ALIGN=CENTER>
<a HREF="http://www.chemaxon.com/marvin">http://www.chemaxon.com/marvin</a>
</p>

</BODY>
</HTML>
