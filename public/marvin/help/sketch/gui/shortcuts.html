<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2//EN">
<html>
<head>
<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
<TITLE>Shortcuts of MarvinSketch</TITLE>
<link rel=stylesheet type="text/css" href="../../marvinmanuals.css">
</head>
<body>
<h1>Shortcuts of MarvinSketch</h1>
<P>The table below contains a list of the available shortcuts in
MarvinSketch.</P>

<p>The way of changing the default shortcuts is described in the
    <a href="customization.html#shorcuts">Customization</a> section.     
</p>
<TABLE WIDTH=95% CELLPADDING=2 CELLSPACING=0 id="grid">
<thead>
<TR>
    <TH>
	<P ALIGN=CENTER>Keyboard shortcut</P>
    </TH>
    <TH WIDTH=80%>
	<P ALIGN=CENTER>Function</P>
    </TH>
</TR>
</thead>
<tbody>
<TR>
    <TD>
	<P>Mouse Wheel</P>
    </TD>
    <TD>
	<P>Scrolls canvas vertically.</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>Shift+Mouse Wheel</P>
    </TD>
    <TD>
	<P>Scrolls canvas horizontally.</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>Ctrl+Mouse Wheel</P>
    </TD>
    <TD>
	<P>Zooms canvas in and out.</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>Arrow Keys, Ctrl+Arrow Keys</P>
    </TD>
    <TD>
	<P>Scrolls canvas in the proper direction if no object is selected on the canvas.</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>Arrow Keys</P>
    </TD>
    <TD>
	<P>Moves the seleted object if an item is selected on the canvas. (You can scroll the canvas with Ctrl+Arrow Keys in this case.)</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>Shift+Arrow Keys</P>
    </TD>
    <TD>
	<P>Move the selected object on the canvas in greater units.</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>Delete</P>
    </TD>
    <TD>
	<P>Removes the selected element.</P>
    </TD>
</TR>

<TR>
    <TD>
	<P>Ctrl+A</P>
    </TD>
    <TD>
	<P>Select All</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>Ctrl+C, Ctrl+Insert</P>
    </TD>
    <TD>
	<P>Copy</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>Ctrl+K</P>
    </TD>
    <TD>
	<P>Copy As</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>Ctrl+L</P>
    </TD>
    <TD>
	<P>Copy As Smiles</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>Ctrl+X, Ctrl+Shift+Delete</P>
    </TD>
    <TD>
	<P>Cut</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>Ctrl+V, Ctrl+Shift+Insert</P>
    </TD>
    <TD>
	<P>Paste</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>Ctrl+Y</P>
    </TD>
    <TD>
	<P>Redo</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>Ctrl+Z, Alt+Backspace</P>
    </TD>
    <TD>
	<P>Undo</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>Ctrl+L</P>
    </TD>
    <TD>
	<P>Copy as SMILES</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>Ctrl+O</P>
    </TD>
    <TD>
	<P>File open (if available)</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>Ctrl+S</P>
    </TD>
    <TD>
    	<P>Save to file (if available)</P>
    </TD>
</TR>
<tr>
    <td><p>Ctrl+Shift+S</p></td>
    <td><p>Save as... (if available)</p></td>
</tr>
<TR>
    <TD>
	<P>Ctrl+P</P>
    </TD>
    <TD>
	<P>Print (if available)</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>Ctrl+M</P>
    </TD>
    <TD>
	<P>Display Periodic System dialog (More window)</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>Ctrl+N</P>
    </TD>
    <TD>
	<P>Create a new window</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>Ctrl+Delete</P>
    </TD>
    <TD>
	<P>Clear Desk</P>
    </TD>
</TR><TR>
    <TD>
	<P>Ctrl+W</P>
    </TD>
    <TD>
	<P>Close current window</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>Ctrl+Q</P>
    </TD>
    <TD>
    	<P>Exit from the application</P>
    </TD>
</TR>
<tr>
    <td><p>Ctrl+G</p></td>
    <td><p>Create Group</p></td>
</tr>
<tr>
    <td><p>Ctrl+2</p></td>
    <td><p>Clean in 2D</td>
</tr>
<tr>
    <td><p>Ctrl+B</p></td>
    <td><p>Clean Wedge Bonds</p></td>
</tr>
<tr>
    <td><p>Ctrl+3</p></td>
    <td><p>Clean in 3D</td>
</tr>
<tr>
    <td><p>Ctrl+F</p></td>
    <td><p>Select conformer</p></td>
</tr>
<tr>
    <td><p>Ctrl+T</p></td>
    <td><p>Opens the Template Library</p></td>
</tr>
<tr>
    <td><p>Ctrl+R</p></td>
    <td><p>Checks and corrects chemical structures.</p></td>
</tr>
<tr>
    <td><p>Ctrl+Shift+N</p></td>
    <td><p>You can view the name of the current structure, and enter a new name to be imported.</p></td>
</tr>
<tr>
    <td><p>Ctrl+Shift+M</p></td>
    <td><p>Open MarvinSpace</p></td>
</tr>

<tr>
    <td><p>F5</p></td>
    <td><p>Exit transformation mode and return to <i>Sketching</i> mode.</p></td>
</tr>
<tr>
    <td><p>F6</p></td>
    <td><p>Switch on the <i>Zoom</i> mode.</p></td>
</tr>
<tr>
    <td><p>F7</p></td>
    <td><p>Enter into the <i>Rotate in 3D</i> mode.</p></td>
</tr>
<tr>
    <td><p>F11</p></td>
    <td><p>Sets the visibility of the main menubar.</p></td>
</tr>
<tr>
    <td><p>Space</p></td>
    <td><p>Changes transformation mode from Drag to Rotate in 2D, Rotate in 2D to Rotate in 3D, while Rotate in 3D to Drag.</p></td>
</tr>

<TR>
    <TD>
	<P>-</P>
    </TD>
    <TD>
	<P>Negative charge</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>+</P>
    </TD>
    <TD>
	<P>Positive charge</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>1</P>
    </TD>
    <TD>
        <P>Single bond</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>2</P>
    </TD>
    <TD>
	<P>Double bond</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>3</P>
    </TD>
    <TD>
	<P>Triple bond</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>4</P>
    </TD>
    <TD>
	<P>Aromatic bond</P>
    </TD>
</TR>
<TR>
	<TD>
	<P>5</P>
	</TD>
	<TD>
	<P>Single up bond</P>
	</TD>
</TR>
<TR>
	<TD>
	<P>6</P>
	</TD>
	<TD>
	<P>Single down bond</P>
	</TD>
</TR>
<TR>
	<TD>
	<P>7</P>
	</TD>
	<TD>
	<P>Single up or down bond</P>
	</TD>
</TR>
<TR>
    <TD>
	<P>12</P>
    </TD>
    <TD>
	<P>Single or double bond</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>14</P>
    </TD>
    <TD>
	<P>Single or aromatic bond</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>24</P>
    </TD>
    <TD>
	<P>Double or aromatic bond</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>0</P>
    </TD>
    <TD>
	<P>Any bond</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>*</P>
    </TD>
    <TD>
	<P>Any atom</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>Q</P>
    </TD>
    <TD>
	<P>Hetero atom</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>C, N, H, ...</P>
    </TD>
    <TD>
	<P>carbon, nitrogen or hydrogen atom. For the other elements, type
	    the mark of the element, e.g.: <b>Cl</b> for Chlorine.
            (Also works in lower case: <b>n</b>, <b>cl</b> etc.)
        </P>
    </TD>
</TR>
<TR>
    <TD>
	<P>Au,Ag,Pt,...</P>
    </TD>
    <TD>
	<P>Atom List can be defined by typing chemical symbols separated by commas.
            (Also works in lower case: <b>au</b>,<b>ag</b>,<b>pt</b>,...)
        </P>
    </TD>
</TR>
<TR>
    <TD>
	<P>!Au,Ag,Pt,...</P>
    </TD>
    <TD>
	<P>Not List can be defined by starting the atom list with an exclamation mark.
            (Also works in lower case: !<b>au</b>,<b>ag</b>,<b>pt</b>,...)
        </P>
    </TD>
</TR>
<TR>
    <TD>
	<P>R1, R2, ..., R32767</P>
    </TD>
    <TD>
	<P>R-group label with specified number. To define a set of
	fragments as R-group, select the fragments before the shortcut. To
	create an attachment point in the R-group, select an atom in the
	R-group and type the name of the R-group (e.g.: R5)
        (Also works in lower case.)<br>
        To define a set of fragments as R-group 5, select the fragments
        then type <b>R5</b>. After then, you can choose an attachment
        point on R-group 4, just type <b>R5</b> and click on the atom.</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>M1, M2, ...</P>
    </TD>
    <TD>
	<P>Atom maps for reactions. (Also in lower case.)</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>M0</P>
    </TD>
    <TD>
	<P>Unmap</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>M= or M+</P>
    </TD>
    <TD>
	<P>Unique atom map labels. Assigns unique atom map numbers
	to individual atoms picked by the mouse or to selected atoms
	in selection mode.</P>
    </TD>
</TR>
<TR>
    <TD>
	<P>11, 22, ..., 77</P>
    </TD>
    <TD>
	<P>Select a template. Select first, second, ..., or 7th element
	from the actual template list from the toolbar (if the referred
	index is not out of range).</P>
    </TD>
</TR>
<tr>
    <td>
        <p>abs, or1, or2, and1, and2</p>
    </td>
    <td>
        <p>Stereochemical groups: <b>abs</b> (ABSOLUTE),\
        <b>or1</b>,<b>or2</b>,...,<b>or10</b>,... (OR <i>n</i>),
        <b>and1</b>,<b>and2</b>,...,<b>and10</b>,...,<b>&1</b>,<b>&2</b>,...,
        <b>&10</b>,... (AND <i>n</i>)
    </td>
</tr>
<TR>
    <TD>
	<P>AcAc, Acm, Ade, ...</P>
    </TD>
    <TD>
	<P>The abbreviated group denoted by the abbreviation. You can ungroup
        the abbreviated group if you press the SHIFT button when you place it
        to the canvas. (Also in lower case.) To complete a longer name,
        press <b>ENTER</b> or <b>END</b> after typing the first few characters.
        </P>
    </TD>
</TR>
<tr>
    <td>
        <p>.a,.A,.u,.H0,H1</p>
    </td>
    <td>
        <p>Special atom properties:
        <b>.a</b> (aromatic),
        <b>.A</b> (aliphatic),
        <b>.u</b> (unsaturated),
        <b>.H0</b>,
        <b>.H1</b>, ... (number of hydrogens),
        <b>.h0</b>,
        <b>.h1</b>, ... (implicit hydrogens),
        <b>.X0</b>,
        <b>.X1</b>, ... (connectivity),
        <b>.D0</b>,
        <b>.D1</b>, ... (degree),
        <b>.R0</b>,
        <b>.R1</b>, ... (rings),
        <b>.r3</b>,
        <b>.r4</b>, ... (smallest ring size),
        <b>.s*</b>,
        <b>.s0</b>,
        <b>.s1</b>, ... (substitution count),
        <b>.v0</b>,
        <b>.v1</b>, ... (valence),
        <b>.rb*</b>,
        <b>.rb0</b>,
        <b>.rb1</b>, ... (ring bond count).
        </p>
    </td>
</tr>
</tbody>
</TABLE>
</body>
</html>