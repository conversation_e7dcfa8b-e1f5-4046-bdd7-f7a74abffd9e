/**
 * Copyright (c) 1998-2013 ChemAxon Ltd. All Rights Reserved.
 */

import java.awt.event.*;
import java.awt.BorderLayout;
import javax.swing.JFrame;
import javax.swing.JPanel;
import javax.swing.AbstractAction;
import javax.swing.Action;

import chemaxon.marvin.beans.MSketchPane;
import chemaxon.marvin.common.UserSettings;

public class NewActionInSketch extends JPanel {
    private final Action newFooAction = new AbstractAction("Foo") {
        public void actionPerformed(ActionEvent ev) {
            System.err.println(ev);
        }
    };

    private final Action newToggleAction = new ToggleAction("Toggle") {
        public void actionPerformed(ActionEvent ev) {
            System.err.println("Cmd=Toggle,Selected="+getValue(Action.SELECTED_KEY));
        }
    };

    private final Action newRadioAction = new ToggleAction("Radio", true) {
        public void actionPerformed(ActionEvent ev) {
            System.err.println("Cmd=Radio,Selected="+getValue(Action.SELECTED_KEY));
        }
    };
    
    private NewActionInSketch() {
	// Creating the MSketchPane JavaBean component
	UserSettings settings = new UserSettings();
	settings.setProperty("menuconfig","foo.xml");
	MSketchPane msketchPane = new MSketchPane(settings);

	// setting the toggle action's selected state to TRUE
	newToggleAction.putValue(Action.SELECTED_KEY, Boolean.TRUE);
	
	// custom action
	msketchPane.getActionMap().put("foo", newFooAction);
	msketchPane.getActionMap().put("toggle", newToggleAction);
	msketchPane.getActionMap().put("radio", newRadioAction);

	// available custom actions 
	msketchPane.setParams("useComponentActions=true\n");

	// Adding the bean to the panel
        setLayout(new BorderLayout());
        add(msketchPane, BorderLayout.CENTER);
    }

    /**
     * Create the GUI and show it. For thread safety,
     * this method should be invoked from the event-dispatching thread.
     */
    private static void createAndShowGUI() {
        //Create and set up the window.
        JFrame frame = new JFrame();
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);

        final NewActionInSketch sketcher = new NewActionInSketch();
        frame.getContentPane().setLayout(new BorderLayout());
        frame.getContentPane().add(sketcher, BorderLayout.CENTER);

        frame.pack();
        frame.setLocationRelativeTo(null);
        frame.setVisible(true);
    }
    
    public static void main(String[] args) {
        //Schedule a job for the event-dispatching thread:
        //creating and showing this application's GUI.
        javax.swing.SwingUtilities.invokeLater(new Runnable(){
            public void run() {
                createAndShowGUI();
            }
        });
    }
    
    //Abstract toggle action
    private static abstract class ToggleAction extends AbstractAction {
        public ToggleAction(String name) {
            this(name, false);
        }

        public ToggleAction(String name, boolean radio) {
            super(name);
            putValue(Action.SELECTED_KEY, Boolean.FALSE);
            putValue("Radio", Boolean.valueOf(radio));
        }
    }
}