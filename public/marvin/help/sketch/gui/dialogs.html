<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2//EN">
<html>
<head>
<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
<TITLE>Dialogs of MarvinSketch</TITLE>
<link rel=stylesheet type="text/css" href="../../marvinmanuals.css">
</head>
<body>
<h1>Dialogs of MarvinSketch</h1>

<h3>Contents</h3>
<ul>
	<li><a href="#customize">Customize</a></li>
    <li><a href="#preferences">Preferences</a></li>
    <li><a href="#source">Edit Source</a></li>
    <li><a href="#format">Format</a></li>
    <li><a href="#periodic">Periodic Table</a></li>
    <li><a href="#template_library">Template Library Manager</a></li>
    <li><a href="#group">Create Group</a></li>
    <li><a href="#attachdata">Attach data</a></li>
    <li><a href="#document_settings">Document Settings</a></li>
</ul>

<h2><a class="anchor" name="customize">Customize</a></h2>
<p>
	The Customize dialog window, located in the <b>View &gt; Editor style</b> menu, provides options for altering the user interface by adding, removing, or reorganizing its elements. 
	For a detailed description, please consult <a href="customization.html">this page.</a>
</p>
	<blockquote><img src="gui-files/dialogs/customize.png" width="456" height="498"></blockquote>

<h2><a class="anchor" name="preferences">Preferences</a></h2>
<p>The Preferences dialog window is located at the <b>Edit</b> menu.
It allows you to change many of the MarvinSketch display settings, including look & feel, error highlighting, and object visibility.<br>
All settings are saved and used when the application is restarted.
</p>

<h4><a class="anchor" name="display">Display</a></h4>
    <blockquote><img src="gui-files/dialogs/preferences1.png" width="517" height="388"></blockquote>
<ul>
    <li><b>Atom & Bond labels</b> are used as the default font type and size to labels
        such as C/T label of bonds, atom query property labels of atoms, etc.</li>
    <li><b>Double bond spacing</b> is a gap between two lines/sticks representing a double or triple bond measured in Angstroms.</li>
    <li><b>Wireframe bond thickness</b> is the width of bonds in wireframe mode. It is measured in Angstroms.</li>
    <li><b>Stick diameter</b> is the width of bonds in stick mode in Angstroms.</li>
    <li><b>Ball radius</b> is the size of atom spheres in Ball draw type, measured in Angstroms.</li>
    <li><b>Look & Feel</b> allows changing the visual appearance of GUI components.
        The available options are: Java Metal, Motif, JGoodies Plastic, JGoodies Plastic XP,
        and the native Look & Feels (Windows, Aqua) based on the underlying operating system.</li>
	<li><b>MarvinView Layout</b> sets the default layout to Automatic, Molecule matrix or Spreadsheet.</li>	
    <li><b>Show Bond in Hand</b> when checked, bond types are shown under the mouse cursor like template structures.</li>
	<li><b>Show Lone Pair as Line</b> when checked, lone pairs on the canvas are shown as lines.</li>
	<li><b>Show Charge in Circle</b> when checked, a circle is displayed around the charge.</li>
    <li><b>Circled Charge labels</b> are used as the font type and size of the circled charge symbols.
	<li><b>Fog effect factor:</b> manual setting of the fading strength. No fog: all regions of the structure is displayed with the same line strength and color. Strong effect: the fading is at its maximum (molecule is only slightly visible at the far end).</li></ul>
<h4>Bonds</h4>
    <blockquote><img src="gui-files/dialogs/preferences2.png" width="517" height="388"><br></blockquote>
    <ul>
        <li><b>Down Wedge Orientation</b> allows changing the wedge bond display convention.
        Down wedge points downward in MDL's convention, upward (at the chiral center) in Daylight's.</li>
        <li><b>Any Bond Line Style</b> offers three different modes to display bonds of unknown types: Automatic, Dashed and Solid.
        This option can be separately set to be used in MarvinSketch and MarvinView.</li>
        <li><b>Terminal Bond Deletion Method</b> offers 2 ways to delete the terminal bond of a molecule: only the bond is 
	deleted or the terminal atom disappears with the bond.</li>
    	<li><b>"Coordinate" Bond Line Style</b> allows changing the type of coordinate bonds from the default ones (arrow for single atom and hashed for multicenter) to solid.</li>
	</ul>
<h4>Structure</h4>
    <blockquote><img src="gui-files/dialogs/preferences3.png" width="517" height="388"><br></blockquote>
    <ul>
        <li><b>Highlighting Valence Errors</b> highlights atoms having wrong valences with red underline when it is checked.</li>
        <li><b>Automatic Lone Pair Calculation</b> calculates lone pairs automatically. Make sure 
		<b>View</b> > <b>Misc</b> > <b>Lone Pairs</b> is checked to see the result.</li>
	<li><b>Validate S-groups At Creation</b> disables the S-group types in the drop-down list which would not yield a 
	chemically correct structure. <a href="../sketch-basic.html#howto-draw.sgroups">Usage in MarvinSketch.</a></li>
        <li><b>Carbon Labels</b> options determine the condition of displaying C labels on Carbon atoms.
            <table>
                <tr><td><img src="gui-files/dialogs/carbon1.png" alt="Always"></td>
                    <td><img src="gui-files/dialogs/carbon2.png" alt="Never"></td>
                    <td><img src="gui-files/dialogs/carbon3.png" alt="Optional"></td>
                </tr>
                <tr align="center"><td>Always</td><td>Never</td><td>At straight angles<br> and implicit H atoms</td></tr>
            </table>                          
        </li>
<li><b>Ligand Orders</b> 
        <ul><li>Always</li>
	<li>Never</li>       
	<li>On R-groups with definitions</li>	   </ul>         
        </li>
    </ul>
<h4>Checkers</h4>
    <blockquote><img src="gui-files/dialogs/preferences4.png" width="517" height="388"><br></blockquote>
    <ul>
        <li><b>Move up/down the checker items:</b> the fixing process may
            depend on the sequence of the checkers. Checking order can be
            set using the Up/Down buttons on selected checkers.</li>
        <li><b>Add checkers to the list:</b> the default list can be modified by adding other checkers.
        <li><b>Remove checkers from the list:</b> the default list can be modified by removing checkers not needed.
        <li><b>Open checker configuration from URL</b> open a checker configuration from URL.
        <li><b>Open checker configuration:</b> open your custom checker configuration from file.
		<li><b>Save checker configuration:</b> save your custom checker configuration to file.
		<li><b>Configure external checkers/fixers:</b> add external checkers/fixers; save or load external checker/fixer configuration.</li>
    </ul>
<h4><a class="anchor" name="services"></a>Services</h4>	
	<blockquote><img src="gui-files/dialogs/preferences5.png" width="517" height="388"><br></blockquote>
	<p>The <a href="../../developer/services.html">Services module</a> provides seamless 
		integration of third-party calculations into Marvin Sketch. You can add and configure the desired 
		calculations in the <i>Services</i> tab. The set service(s) can be used from the <a href="../../calculations/services_menu.html">Tools > Services</a> menu afterwards. 
	<ul>
		<li><b>Set the order</b> of services by moving them up and down using the Up/Down buttons.</li>
		<li><b>Add a new service</b> to the list by the add button. The preference window of the new service will pop up. Read more about <a href="setting_services.html">setting different services</a>.</li>  
		<li><b>Remove the selected service</b> from the list by the remove button.</li>
		<li><b>Open</b> Service Configuration from URL. Specify a previously set configuration of services with its URL.</li>
		<li><b>Import</b> Service Configuration from file. Import a previously set configuration <code>XML</code> file.</li>
		<li><b>Export</b> Service Configuration to file. You can export the set services to a 
			configuration <code>XML</code> file.</li>
	</ul>

<h4><a class="anchor" name="SaveLoad">Save/Load</a></h4>
    <blockquote><img src="gui-files/dialogs/preferences6.png" width="517" height="388"><br></blockquote>
    <ul>
        <li><b>Default location:</b> the folder from which to load or to save molecules may be set by the user.
        <ul><li>Startup directory: the folder where the command to start the application was given.</li>
            <li>Last location: the last folder used for opening or saving a structure.</li>
            <li>Custom working directory: a user-defined folder. If
            a molecule is loaded from another folder, then the file's location will be offered for saving.</li></ul>
        </li>
        <li><b>Default file format</b> determines which type is offered by default when structures are saved to file.</li>
        <li><b>Save/Load settings</b>
            <ul><li><b>Save/Load GUI settings (.MRV format)</b> allows storing and loading
            parameters like background color, font type, etc. beside the structures.
            This option can only be used with the
            <a href="../../formats/mrv-doc.html">MRV format</a>.</li>
            <li><b>Save/Load zoom factor (.MRV format)</b> stores and loads the
            zooming scale of the structures.
        This option can only be used with the <a href="../../formats/mrv-doc.html">MRV format</a>.</li></ul>
        </li>
        <li><b>Recent file entries</b> defines the number of files in the Recent files list in the File menu,
        with values between 1 and 10.</li>
		<li><b>Image import service URL</b> URL of a server on which a chemical structure recognition program runs can be given.</li>
		<li><b>Name import service URL</b> URL of a server on which a chemical name recognition program runs can be given.</li>
    </ul>
<h4>OLEServer</h4>
    <p><blockquote><img src="gui-files/dialogs/preferences7.png" width="517" height="388"><br></blockquote></p>
    
    
<h2><a class="anchor" name="source">Edit Source</a></h2>
<P>You can alter a molecule by directly editing its source in the Edit Source dialog window.<br>
The dialog window provides standard clipboard operations and it is also possible to send the source text to the console.<br>
You can view and edit the source in any of the supported file formats.
You can also convert it to Java String which allows easy integration of the structure to a custom Java application code.<br>
To change the format of the source, simply select one from the <B class="buttonName">View</B>
Menu. If there are more than one molecule on the canvas, setting <b>View as multiple molecules</b> in the <B class="buttonName">View</B> Menu causes 
each molecule to appear in a separate block in the source. This feature works only in those cases where the selected format is able to handle multiple 
fragments.</P>
<blockquote><img src ="gui-files/dialogs/edit_source2.png" width="574" height="540"></blockquote>
<P>After editing the source text, you can send the structure back to the
MarvinSketch canvas by invoking <B class="buttonName">File &gt;
Import As</B>, and pressing <b>Import</b> on the appearing <a class="anchor" name="import_dialog">dialog window</a>. This will close the Edit Source dialog window.
</P>
<blockquote><img src="gui-files/dialogs/edit_source3.png" width="372" height="283"></blockquote>

<h2><a class="anchor" name="format">Format</a></h2>
<h4>Atoms and Bonds</h4>
<p>
On this panel there are many options to change the drawing properties of atoms and bonds.
</p>
<blockquote><img src="gui-files/dialogs/format1.png"></blockquote>
<p>For more information about using structure drawing styles, please
    visit <a href="../sketch-basic.html#styles">this link</a>.
</p>

<h4>Graphics Objects</h4>
<p>
The drawing properties of graphics objects (text boxes, brackets, lines, etc.) can be changed on this panel.
</p>
<blockquote><img src="gui-files/dialogs/format2.png" width="589" height="443"><br></blockquote>

<h2><a class="anchor" name="periodic">Periodic Table of Chemical Elements</a></h2>
<h4>Periodic Table</h4>

    <blockquote><img src="gui-files/dialogs/periodic1.png" width="544" height="522"><br></blockquote>
<p>Chemical elements are available as buttons on the Periodic Table panel of MarvinSketch.<br>
Atom buttons are arranged according to the standard periodic table layout.<br>
When the mouse cursor is over a specific atom button, the information panel displays
the name of the atom, the atomic number, mass, electronegativity and the oxidation states.<br>
When one of the atom buttons is pressed, the corresponding atom can be placed on the canvas.
The atom symbol appears under the mouse cursor, while the button is highlighted in this case.</p>
<p>
The <b>Atom List</b> and <b>NOT List</b> buttons can be used to create special atom lists that can be used in queries.
When one of these buttons is pressed, atoms can be added to the list by pressing atom buttons one after the other.
The lists are not cleared when the list buttons become unselected.
The atoms of the list are also shown under the mouse cursor above the canvas.
See <a href="http://www.chemaxon.com/jchem/doc/user/query_features.html#atlist">Query Guide</a>
    for more details about <i>atom lists</i> and <i>not lists</i>.
</p>

<p>Four different <b>coloring schemas</b> can be chosen:
<ol>
    <li>CPK: colors the atoms according to the Corey-Pauling-Kultun coloring scheme</li>
    <li>Standard state: colors according to the standard state of the element (gas, liquid, solid)</li>
    <li>Blocks: colors elements according to the highest-energy electron's orbital (s-, p-, d- or f-block)</li>
    <li>Metals/Nonmetals: colors according to the metallic character of the elements
    (alkali, alkaline earth, metalloid, transition metal, other metal, nonmetal)</li>
</ol>


<h4><a class="anchor" name="advanced">Advanced</a></h4>
<blockquote><img src="gui-files/dialogs/periodic2.png" width="544" height="522"><br></blockquote>
<p>
For the meanings of the buttons on the Advanced tab please refer to the <a href="http://www.chemaxon.com/jchem/doc/user/query_features.html">Query Guide</a>.<br>
When the mouse cursor is over a button, a short description appears on the information panel.
</p>

<h5>Generic query atoms</h5>
<blockquote><table class="grid" cellspacing="0" cellpadding="4">
    <tr>
        <th>Name</th><th>Description</th>
    </tr>
    <tr>
        <td>A</td><td>Any (any atom except hydrogen)</td>
    </tr>
    <tr>
        <td>AH</td><td>Any atom, including hydrogen</td>
    </tr>
    <tr>
        <td>Q</td><td>Hetero (any atom except hydrogen and carbon)</td>
    </tr>
    <tr>
        <td>QH</td><td>Hetero atom or hydrogen (any atom except carbon)</td>
    </tr>
    <tr>
        <td>M</td><td>Metal (contains alkali metals, alkaline earth metals, transition metals, actinides, lanthanides, poor(basic) metals, Ge, Sb and Po)</td>
    </tr>
    <tr>
        <td>MH</td><td>Metal or hydrogen</td>
    </tr>
    <tr>
        <td>X</td><td>Halogen (F,Cl,Br or I)</td>
    </tr>
    <tr>
        <td>XH</td><td>Halogen or hydrogen</td>
    </tr>
</table></blockquote>

<h5><a class="anchor" name="queryprops">Atom query properties</a></h5>
<a href=../sketch-chem.html#atom-props>Adding query properties to structures.</a>
    <blockquote><table class="grid" cellspacing="0" cellpadding="4">
        <tr>
            <th>Name</th><th>Description</th>
        </tr>
    <tr>
        <td>.H+</td><td>Increase number of <em>total hydrogens</em> (total number of hydrogen substituents)</td>
    </tr>
   
    <tr>
        <td>.H-</td><td>Decrease number of <em>total hydrogens</em> (total number of hydrogen substituents)</td>
    </tr>
    <tr>
        <td>.v+</td><td>Increase number of <em>valence</em> (total bond order)</td>
    </tr>
   
    <tr>
        <td>.v-</td><td>Decrease number of <em>valence</em> (total bond order)</td>
    </tr>
    <tr>
        <td>.X+</td><td>Increase number of <em>connections</em> (number of substituents including hydrogens)</td>
    </tr>
    <tr>
        <td>.X-</td><td>Decrease number of <em>connections</em> (number of substituents including hydrogens)</td>
    </tr>
    <tr>
        <td>.R-</td><td>Increase number of<em>rings</em> (number of rings the atom is a member of)</td>
    </tr>
    <tr>
        <td>.R+</td><td>Decrease number of <em>rings</em> (number of rings the atom is a member of)</td>
    </tr>
    <tr>
        <td>.r+</td><td>Increase <em>smallest ring size</em> (size of the smallest ring the atom is a member of)</td>
    </tr>
    <tr>
        <td>.r-</td><td>Decrease<em>smallest ring size</em> (size of the smallest ring the atom is a member of)</td>
    </tr>
    <tr>
        <td>.rb+</td><td>Increase <em>ring bond count</em> (number of ring bonds next to the atom)</td>
    </tr>
    <tr>
        <td>.rb-</td><td>Decrease <em>ring bond count</em> (number of ring bonds next to the atom)</td>
    </tr>
    <tr>
        <td>.s+</td><td>Increase <em>substitution count</em> (number of non-H substituents)</td>
    </tr>
    <tr>
        <td>.s-</td><td>Decrease <em>substitution count</em> (number of non-H substituents)</td>
    </tr>
    <tr>
        <td>.h+</td><td>Increase number of <em>implicit hydrogens</em> (number of implicit hydrogen substituents)</td>
    </tr>
    <tr>
        <td>.h-</td><td>Decrease number of <em>implicit hydrogens</em> (number of implicit hydrogen substituents)</td>
    </tr>
    <tr>
        <td>.D+</td><td>Increase <em>degree</em> (number of explicit connections; default for "n" is one)</td>
    </tr>
    <tr>
        <td>.D-</td><td>Decrease <em>degree</em> (number of explicit connections; default for "n" is one)</td>
    </tr>
    <tr>
        <td>.u</td><td>Mark as <em>unsaturated atom</em> (atom has double, triple or aromatic bond)</td>
    </tr>
    <tr>
        <td>.a/A</td><td>Mark as <em>aromatic/aliphatic</em> (has aromatic bond)</td>
    </tr>
  
</table></blockquote>

<h5>Periodic Table Groups</h5>
<blockquote>
    <table class="grid" cellspacing="0" cellpadding="4">
    <tr>
        <th>Name</th><th>Description</th>
    </tr>
    <tr><td>Group 1 (IA,IA)</td><td>the alkali metals or hydrogen family/lithium family</td></tr>
    <tr><td>Group 2 (IIA,IIA)</td><td>the alkaline earth metals or beryllium family</td></tr>
    <tr><td>Group 3 (IIIA,IIIB)</td><td>the scandium family</td></tr>
    <tr><td>Group 4 (IVA,IVB)</td><td>the titanium family</td></tr>
    <tr><td>Group 5 (VA,VB)</td><td>the vanadium family</td></tr>
    <tr><td>Group 6 (VIA,VIB)</td><td>the chromium family</td></tr>
    <tr><td>Group 7 (VIIA,VIIB)</td><td>the manganese family</td></tr>
    <tr><td>Group 8 (VIII)</td><td>the iron family</td></tr>
    <tr><td>Group 9 (VIII)</td><td>the cobalt family</td></tr>
    <tr><td>Group 10 (VIII)</td><td>the nickel family</td></tr>
    <tr><td>Group 11 (IB,IB)</td><td>the coinage metals or copper family</td></tr>
    <tr><td>Group 12 (IIB,IIB)</td><td>the zinc family</td></tr>
    <tr><td>Group 13 (IIIB,IIIA)</td><td>the boron family</td></tr>
    <tr><td>Group 14 (IVB,IVA)</td><td>the carbon family</td></tr>
    <tr><td>Group 15 (VB,VA)</td><td>the pnictogens or nitrogen family</td></tr>
    <tr><td>Group 16 (VIB,VIA)</td><td>the chalcogens or oxygen family</td></tr>
    <tr><td>Group 17 (VIIB,VIIA)</td><td>the halogens or fluorine family</td></tr>
    <tr><td>Group 18 (Group 0)</td><td>the noble gases or helium family/neon family</td></tr>
</table>
</blockquote>

<h5>Special nodes</h5>
<blockquote>
    <table class="grid" cellspacing="0" cellpadding="4">
    <tr>
        <th>Name</th><th>Description</th>
    </tr>
    <tr><td>LP</td><td>Lone Pair. This button changes the selected atom to an explicit lone electron pair.</td></tr>
    <tr><td>Pol</td><td>Pseudo atom 'Pol'. This button changes the selected atom to a pseudo atom labelled Pol (polymer).</td></tr>
    <tr><td>*</td><td>This button creates a '*' atom, which indicates an unspecified end group in polymers.</td></tr>    
    <tr><td>homology groups</td><td>The drop-down list contains the default homology groups. <a href="../../calculations/homologygroups.html">Detailed list.</a></td></tr>
    </table>
</blockquote>

<h5>R-groups</h5>

These atoms can be used to describe unknown or unspecified molecule parts or to draw R-group queries or Markush structures.

<h5>Custom Property</h5>
<blockquote>
    <table class="grid" cellspacing="0" cellpadding="4">
    <tr>
        <th>Name</th><th>Description</th>
    </tr>
    <tr><td>R-group</td><td>Converts the atom to an R-group with the given number (only numerical characters are allowed). Maximum index is 32767. This atom can be used to describe an unknown or unspecified molecule part or to draw an R-group query or Markush structure.</td></tr>
    <tr><td>Alias</td><td>The given value is shown as atom label but the atom itself does not change.</td></tr>
    <tr><td>Pseudo</td><td>The given value is shown as atom label and the type of the atom is changed to 'Any'.</td></tr>
    <tr><td>SMARTS</td><td>Converts the given value to a complex SMARTS query molecule or atom. If the cursor is kept over the canvas during typing, the conversion can be seen on-the-fly.</td></tr>
    <tr><td>Value</td><td>Adds the given value to an atom as a custom property ("Atom value").</td></tr>
    </table>
</blockquote>

<h2><a class="anchor" name="template_library">Template Library Manager</a></h2>
<p>The Template Library is a hierarchic display of template sets.</p>
<p>It contains several template sets by default (such as Generic, Rings, Amino Acids, etc),
    and a special set called My Templates.</p>

<blockquote><img src="gui-files/dialogs/template_library.png" width="682" height="421"></blockquote>
The  dialog has buttons to customize template handling. With the help of these buttons you can add and remove template sets to/from the template library, can change order of a given template  set, or open the "Options" dialog.
The cleaning options of the templates can be set using the last 
three buttons on the toolbar. With these buttons you can specify how the template will be placed onto the canvas (NoClean, 2D, or 3D). These options can be set separately for each template category. Note, that the 
buttons are synchronized with the "Template Options" dialog settings.

<h4>Template Options</h4>

<blockquote><img src="gui-files/dialogs/template_options.png" width="373" height="200"></blockquote>
<ul>
    <li><b>Maximum number of molecules:</b> template sets can contain large number of molecules.
        This option maximizes the number of structures being loaded from a template set when
        it is selected in the Template Library. For example if the option is set to 100,
        only 100 structures will be loaded to memory and displayed in the library,
        even if the underlying molecule file contained 25000 structures.</li>
    <li><b>Coordinates:</b> this option is to specify an operation affecting the coordinates when the templates are placed on the canvas.<br>
        For example result of placing L-Alanine to the canvas with different options:
        <table cellpadding="4">
            <tr><td><img src="gui-files/dialogs/template_coordinates1.png" alt="As defined"></td>
                <td><img src="gui-files/dialogs/template_coordinates2.png" alt="Clean 2D"></td>
                <td><img src="gui-files/dialogs/template_coordinates3.png" alt="Clean 3D"></td>
            </tr>
            <tr align="center"><td>As in the original<br>structures</td><td>Always perform<br>Clean2D</td><td>Always perform<br>Clean3D</td></tr>
        </table>
    </li>
    <li><b>Size of templates:</b> the size with which each template is displayed in the library, measured in pixels.</li>

    <li><b>Maximum number of buttons:</b> this determines the maximum number of buttons allowed on the <a href="toolbars.html#templates">Advanced Templates Toolbar</a></li>
</ul>

<h4>Adding a new template set to the library</h4>

It is possible to add new template sets to the library using the Add Template Set button on the tool bar.<br>
Using the Browse button you can select a directory or a file of the file system.
Specifying a directory will create a hierarchic template set containing all subdirectories and files.
<br>
<blockquote><img src="gui-files/dialogs/template_add1.png" width="368" height="109"></blockquote>
<p>It is also possible to specify a location with ftp protocol. The underlying subdirectories
    and files will be displayed as with the local file system.</p>
<p>Please note that protocols other than file and ftp are not supported. However remote file systems can help to overcome this restriction.</p>
<blockquote><img src="gui-files/dialogs/template_add2.png" width="368" height="109"></blockquote>

<h4>Removing a template set from the library</h4>

You can remove template sets from the library using the Remove Template Set button on the tool bar.<br>
The template set will only be removed from the library, without modifying files on the file system.
<br>
<blockquote><img src="gui-files/dialogs/template_remove.png" width="311" height="115"></blockquote>

<h2><a class="anchor" name="group">Create Group</a></h2>
This dialog makes it possible to create a number of groups along with setting their properties. 
<a href="../sketch-basic.html#howto-draw.sgroups">Group drawing in MarvinSketch.</a>
<blockquote><img src="gui-files/dialogs/group.png" width="471" height="211"></blockquote>
The available groups and their detailed description are available by clicking on the links below:
<ul>
    <li>Anypolymer (anyp)</li>
    <li><a href="../sketch-basic.html#comBrackets">Component (c)</a></li>
    <li><a href="../sketch-basic.html#copolymers">Copolymers</a></li>
    <li><a href="../sketch-basic.html#structure-based">Crosslink (xl)</a></li>
    <li>Generic ()</li>
    <li><a href="../sketch-basic.html#structure-based">Graft</a></li>
    <li><a href="../sketch-basic.html#source-based">Mer (mer)</a></li>
    <li><a href="../sketch-basic.html#structure-based">Modification (mod)</a></li>
    <li><a href="../sketch-basic.html#source-based">Monomer (mon)</a></li>
    <li><a href="../sketch-basic.html#multiplegroups">Multiple group</a></li>
    <li><a href="../sketch-basic.html#comBrackets">Ordered mixture (f)</a></li>
    <li><a href="../sketch-basic.html#repeatingunits">Repeating units with repetition ranges</a></li>
    <li><a href="../sketch-basic.html#howto-draw.rgroups">R-group</a></li>
    <li><a href="../sketch-basic.html#structure-based">SRU polymer(n)</a></li>
    <li><a href="../sketch-basic.html#abbreviatedgroups">Superatom (abbreviation)</a></li>
    <li><a href="../sketch-basic.html#comBrackets">Unordered mixture (mix)</a></li>
</ul>

Displaying charges on groups is described <a href="../sketch-basic.html#groupcharge">here</a>.

<h2><a class="anchor" name="attachdata">Attach data</a></h2>
<p>Attached data is a custom field assigned to atoms or brackets. It has an identifier string (name) and a value. Furthermore, a query operator can describe different restrictions in queries.<br>
The dialog provides interface to set the properties of such a field.</p>
<blockquote><img src="gui-files/dialogs/attachdata.png" width="433" height="362" alt="attachdata"/></blockquote>
<p>Further details of attached data can be found in the <a href="http://www.chemaxon.com/jchem/doc/user/query_features.html#attached_data">Query guide</a>.</p>
<p>Attaching data in MarvinSketch is described <a href="../sketch-chem.html#attacheddata">here</a>.
<p>The Attach Data dialog is customizable; configuration options and a small example can be found <a href="../sketch-attachDataConfig.html">here</a>.


<h2><a class="anchor" name="editproperties">Edit properties</a></h2>
<p>Properties may be added and viewed in MarvinSketch. Various properties can be added, but the value is displayed only on the canvas. 
<a href="../sketch-chem.html#atom-props">Detailed description</a>
<blockquote><img src="gui-files/dialogs/edit_properties.png" width="472" height="263" alt="editproperties"/></blockquote>

<h2><a class="anchor" name="document_settings">Document Settings</a></h2>
You can set the number of horizontal and vertical pages in the Document Grid part,
and you can also define the title, the page size and the margins in the corresponding
sections of this dialog window. Henceforward, the given title will specify the "molecule title" property. 
<blockquote><img src="gui-files/dialogs/document_settings.png" width="231" height="355"><br></blockquote>
</body>
</html>