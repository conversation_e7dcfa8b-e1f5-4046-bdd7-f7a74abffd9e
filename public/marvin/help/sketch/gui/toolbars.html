<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2//EN">
<html>
<head>
<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
<TITLE>Toolbars of MarvinSketch</TITLE>
<link rel=stylesheet type="text/css" href="../../marvinmanuals.css">
</head>
<body>
<h1>Toolbars of MarvinSketch</h1>
The toolbars provide buttons that access some of the frequently used commands in the menus.
To activate a command, click its toolbar button. If a command is unavailable, its button appears grayed-out.

<p><b>Note:</b> Place the mouse cursor over a toolbar button to see the tooltip describing its use.</p>

<h3><a class="anchor" name="general">General <PERSON>bar</a></h3>
<table class="grid" cellspacing="0">
    <tr>
        <td width="5%"><img src="gui-files/icons/select-rectangle24.png"></td>
        <td width="20%"><a name="select" class="text">Rectangle Selection</a></td>
        <td width="75%">Allows selection in rectangle mode on mouse drag.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/select-lasso24.png"></td>
        <td>Lasso Selection</td>
        <td>Allows selection in lasso mode on mouse drag.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/select-structure24.png"></td>
        <td>Structure Selection</td>
        <td>Allows selection in structure selection mode on mouse drag. With this selection mode only whole fragments can be selected.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/delete-mode24.png"></td>
        <td><a name="erase" class="text">Erase</a></td>
        <td>Removes all structures upon selection.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/undo24.png"></td>
        <td><a name="undo" class="text">Undo</a></td>
        <td>Reverses the last command or the last entry you typed.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/redo24.png"></td>
        <td><a name="redo" class="text">Redo</a></td>
        <td>Reverses the action of the last Undo command.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/cut24.png"></td>
        <td>Cut</td>
        <td>Removes and copies the selection to the clipboard.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/copy24.png"></td>
        <td>Copy</td>
        <td>Copies the selection to the clipboard.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/paste24.png"></td>
        <td>Paste</td>
        <td>Inserts the contents of the clipboard at the location of the cursor, without replacing selection.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/check-structure24.png"></td>
        <td>Check Structure</td>
        <td>Checks and corrects chemical structures. See <a href="../../structurechecker/checker.html">Structure Checker in MarvinSketch</a> for more details.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/zoom-in24.png"></td>
        <td>Zoom In</td>
        <td>Increases the canvas's magnification.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/zoom-out24.png"></td>
        <td>Zoom Out</td>
        <td>Decreases the canvas's magnification.</td>
    </tr>
    <tr>
        <td><img src="gui-files/zoomtool.png"></td>
        <td>Zoom Tool</td>
        <td>Changes the canvas's magnification to a specific value.
            It can also do autoscale using named values: All, Selection, Scaffold, R-groups.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/help24.png"></td>
        <td>Help Contents</td>
        <td>Shows MarvinSketch User's Guide.</td>
    </tr>
</table>

<h3><a class="anchor" name="tools">Tools Toolbar</a></h3>

<p>The tools consist of various command groups. The tools having chemical meaning 
(like bond or reaction arrow) are drawn in black lines, while strictly graphical objects 
are in blue. You can place for example only ONE reaction arrow on the canvas, but 
as many graphical arrows as you wish and they will look completely identical.</p>

<table cellspacing="0" class="grid">
    <tr>
        <td width="5%"><img src="gui-files/icons/bonds.png"></td>
        <td width="20%">Insert Bond</td>
        <td width="75%">Places various bond types on the canvas.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/chain24.png"></td>
        <td>Insert Chain</td>
        <td>Places a carbon chain on the canvas. The number of carbon atoms can be increased or decreased by dragging the mouse. Selection of straight or curved chain drawing is available.</td>
    </tr>
	 <tr>
        <td width="5%"><img src="gui-files/icons/bond-bold-tool24.png"></td>
        <td width="20%">Bold Tool</td>
        <td width="75%">Thickens the selected bond. See details on <a href="../sketch-basic.html#boldaromatic">bold tool</a> function.</td>
    </tr>  
    <tr>
        <td width="5%"><img src="gui-files/icons/hashed-bond-24.png"></td>
        <td width="20%">Hashed Bond Tool</td>
        <td width="75%">Makes the selected bond hashed. It only retains single original bond type.</td>
    </tr> 
	<tr>
        <td><img src="gui-files/icons/text24.png"></td>
        <td>Insert Text</td>
        <td>Places a Text object on the canvas. Allows changing text properties on the appearing toolbar.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/rarrows.png"></td>
        <td><a name="reaction"></a>Insert Reaction Arrow</td>
        <td>Places various reaction arrow objects on the canvas.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/group24.png"></td>
        <td>Create Group</td>
        <td>Creates a custom abbreviation group.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/brackets.png"></td>
        <td>Insert Brackets</td>
        <td>Places brackets, parentheses, chevrons or braces on the canvas.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/graphics.png"></td>
        <td>Insert Graphics</td>
        <td>Places various graphical objects on the canvas.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/charge-plus24.png"></td>
        <td>Increase Charge</td>
        <td>Increases the charge of the selected atom. The number of implicit hydrogens will be adjusted if possible to accommodate the new charge. Valence errors will be highlighted in red.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/charge-minus24.png"></td>
        <td>Decrease Charge</td>
        <td>Decreases the charge of the selected atom. The number of implicit hydrogens will be adjusted if possible to accommodate the new charge. Valence errors will be highlighted in red.</td>
    </tr>

</table>

<h3><a class="anchor" name="atoms">Atoms Toolbar</a></h3>
<table cellspacing="0" class="grid">
    <tr>
        <td width="4%"><img src="gui-files/icons/periodic24.png"></td>
        <td width="21%">Periodic System</td>
        <td width="75%">Shows periodic system and query/atom property drawing window.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/atoms/h.png"></td>
        <td>Insert Hydrogen</td>
        <td>Places Hydrogen atom on the canvas.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/atoms/c.png"></td>
        <td>Insert Carbon</td>
        <td>Places Carbon atom on the canvas.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/atoms/n.png"></td>
        <td>Insert Nitrogen</td>
        <td>Places Nitrogen atom on the canvas.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/atoms/o.png"></td>
        <td>Insert Oxygen</td>
        <td>Places Oxygen atom on the canvas.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/atoms/s.png"></td>
        <td>Insert Sulfur</td>
        <td>Places Sulfur atom on the canvas.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/atoms/f.png"></td>
        <td>Insert Fluorine</td>
        <td>Places Fluorine atom on the canvas.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/atoms/p.png"></td>
        <td>Insert Phosphorus</td>
        <td>Places Phosphorus atom on the canvas.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/atoms/cl.png"></td>
        <td>Insert Chlorine</td>
        <td>Places Chlorine atom on the canvas.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/atoms/br.png"></td>
        <td>Insert Bromine</td>
        <td>Places Bromine atom on the canvas.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/atoms/i.png"></td>
        <td>Insert Iodine</td>
        <td>Places Iodine atom on the canvas.</td>
    </tr>
</table>

<h3><a class="anchor" name="chemical">Chemical Toolbar</a></h3>
This toolbar contains chemical functions and it is not visible by default.<br>
To make it visible, choose <b>View > Toolbars > Chemical</b>.
<br><br>
<table cellspacing="0" class="grid">
    <tr>
        <td><img src="gui-files/icons/chemical/clean2D24.png"></td>
        <td>Clean 2D</td>
        <td>Calculates new 2D coordinates for the molecule.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/chemical/clean3D24.png"></td>
        <td>Clean 3D</td>
        <td>Calculates new 3D coordinates for the molecule. Clean3D builds up conformers of fragments from which the best, i.e. the lowest energy conformer is given back. The quality of the structures is measured by a simple energy function (Dreiding type molecular mechanics).</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/chemical/aromatize24.png"></td>
        <td>Convert to Aromatic Form</td>
        <td>Transforms the molecule to aromatic representation using the transformation method set.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/chemical/dearomatize24.png"></td>
        <td>Convert to Kekul&eacute; Form</td>
        <td>Transforms the molecule to non-aromatic representation.</td>
    </tr>
</table>

<h3><a class="anchor" name="markush">Markush Toolbar</a></h3>
This toolbar contains funtions that help to work with Markush structures and it is not visible by default.<br>
To make it visible, choose <b>View > Toolbars > Markush</b>.
<br><br>
<table cellspacing="0" class="grid">
    <tr>
        <td><img src="gui-files/icons/markushbond24.png"></td>
        <td>Position Variation Bond</td>
        <td>Creates a variable point of attachment to represent a connection point to a group of atoms.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/groupr24.png"></td>
        <td>Frequency Variation</td>
        <td>Creates a Repeating Unit with Repetition Ranges.</td>
    </tr>
  <tr>
        <td><img src="gui-files/icons/attachment_point_24.png"></td>
        <td>R-group attachment</td>
        <td>Adds an attachment to the structure.</td>
    </tr>

</table>

<h3><a class="anchor" name="templates">Advanced Templates Toolbar</a></h3>

This toolbar contains special buttons holding <a href="../sketch-basic.html#templates">structure templates</a>.<br>
Additional functions of this toolbar:
<ol>
    <li><b>The toolbar can show different template groups.</b>
        <ul>
            <li>General and My Templates:<br><br> <img src="gui-files/templates1.png"></li>
            <li>Crown Ethers and Bridged Polycyclics:<br><br> <img src="gui-files/templates2.png"></li>
            <li>To control which template sets are displayed on the toolbar, use the Properties panel in the Template Library (Ctrl+t): <br><br>
            <img src="gui-files/templates3b.png"></li>
Checking the 'Use molecules as templates at 2D cleaning' checkbox will effect the structures containing that template 
during cleaning of the structure: the default cleaning form is overwritten by the template structure. This way, you can cutomize your drawings: add or draw a set of templates and check this option.
        </ul>
        
    </li>
    <li><b>Any structure can be added to the My Templates group.</b>
        <ul>
            <li>Using Drag &amp; Drop to the toolbar<br><img src="gui-files/templates5.png"></li>
            <li>Using the Pop-up menu<br><img src="gui-files/templates6.png" >
            </li>
        </ul>

    </li>
	<li><b>Set the name of the new template.</b>
		<ul>
			<li>Right-click on the template icon on the template toolbar and select <b>Properties</b>.</li>
			<li>Set the name and/or the abbreviation of the template in the Template Properties box.</li>
			<li>After that the template is identified with its name and/or abbreviation.<br><img src="gui-files/templates7.png"></li>
		</ul>
	
	</li>
	<li><b>Templates without a name</b>
		<ul>
			<li>If the template does not have a name, hovering the cursor over its icon on the template toolbar magnifies the image on the icon.
			This improves the visibility of the template icon, especially for big structures.<br>
			<img src="gui-files/templates8.png"></li>
		</ul>
		
	</li>
	<li><b>The template can be removed from the toolbar.</b>
		<ul>
		<li>Right-click on the template icon and select <b>Remove</b> to remove the template from the toolbar and from the My Templates list.</li>
		</ul>
	
	</li>
    
</ol>

<h3><a class="anchor" name="simple">Simple Templates Toolbar</a></h3>
If you only wish to use the 6 generic template structures without additional functions, you can use the Simple Templates Toolbar.
This toolbar is not visible by default. To make it visible, choose <b>View > Toolbars > Simple Templates</b>.
<br><br>
<table cellspacing="0" class="grid">
    <tr>
        <td><img src="gui-files/icons/template-cyclopentane-house24.png"></td>
        <td>Cyclopentane (house)</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/template-pyrrole24.png"></td>
        <td>Pyrrole</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/template-cyclopentane24.png"></td>
        <td>Cyclopentane</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/template-cyclohexane24.png"></td>
        <td>Cyclohexane</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/template-benzene24.png"></td>
        <td>Benzene</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/template-naphtalene24.png"></td>
        <td>Naphthalene</td>
    </tr>
</table>

<h3><a class="anchor" name="3d_editing">3D editing Toolbar</a></h3>
<table cellspacing="0" class="grid">
    <tr>
        <td><img src="gui-files/icons/merging_mapper_tool24.png"></td>
        <td>Maps atoms to merge.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/directed_merge_in_3d24.png"></td>
        <td>Merges assigned atoms.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/3d_plane_tool24.png"></td>
        <td>Alters the coordinates of the molecule in order to put the 3 selected atoms of the molecule onto the plane of the canvas.</td>
    </tr>
    <tr>
        <td><img src="gui-files/icons/entering_new_substituent_fragment24.png"></td>
        <td>Adds new fragment to the canvas.</td>
    </tr>
    
</table>

</body>
</html>
