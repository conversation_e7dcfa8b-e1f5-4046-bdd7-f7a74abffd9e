<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2//EN">
<html>
<head>
<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
<TITLE>Status Bar of MarvinSketch</TITLE>
<link rel=stylesheet type="text/css" href="../../marvinmanuals.css">
</head>
<body>
<h1>Status Bar of MarvinSketch</h1>

The Status Bar appears at the bottom of the main frame, and unlike toolbars, it cannot be customized or moved.
<br><br>
The Status Bar consists of 3 parts:<br>
<img src="gui-files/statusbar.png">
<ol>                  
    <li><b>Dimension Button</b><br>
        Switches between 2D and 3D modes.
        If the current structure is represented in 3D, then switching to 2D mode performs a 2D cleaning upon confirmation.
    </li>
    <li><b>File Status Indicator</b><br>
        This sign appears dynamically if there are unsaved modifications on the current structure, and disappears upon a Save command.
    </li>

    <li><b>Structure Checker Status</b><br>
        By default it is disabled as seen on the first image. To enable manual checking double-click on it.
        Right-click enables automatic checking. The status bar displays different images when there is no problem, if
        checking is in progress or if problems were found.
    </li>

    <li><b>Navigation Buttons</b><br>
        The Navigation Buttons appearing on the Status Bar dynamically using multipage
        molecular documents provide a quick way to navigate between pages.<br>
        For information about how to enable multipage molecular documents please visit
        <a href="../sketch-basic.html#howto-multipage">this link</a>.
    </li>
</ol>



</body>
</html>