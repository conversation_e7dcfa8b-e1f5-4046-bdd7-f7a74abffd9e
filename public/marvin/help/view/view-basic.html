<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>MarvinView Help: Features</TITLE>
<link REL="stylesheet"  TYPE="text/css" HREF="../marvinmanuals.css">
</HEAD>
<BODY><H1 CLASS="title">How To Use MarvinView Features</H1>
<a href="view-index.html">Table Of Contents</a>

<a name="loading"></a><h2>Loading Molecules into MarvinView</h2>
<PERSON><PERSON>iew can open molecule files saved in any of the <a href="../formats/formats.html">supported formats</a> for viewing. Choose 
<b>File &gt; Open...</b> and browse for the file in the <b>Open file</b> dialog.<br>
Tick the <b>Show preview</b> checkbox to see the contents of the file (molecules, reactions, queries). A single item is dispalyed in the 
preview window; the text field at the bottom shows the index of the current structure and the number of structures in the file. When a 
multiple structure file is selected (<i>e.g.</i>, MRV or SDF), the navigation buttons become active. Their functions are: go to first, go 
to previous, go to next, go to last. Note that the preview window allows you only to check the file content, but not to select the molecules 
you would like to open. For this purpose the <b>Select</b> textbox has to be used, where you can write the serial numbers of the molecules to 
open. The numbers have to be separated either by commas or by a dash. (Clicking on the <b>Info</b> button in this row will present tips for 
specifying the desired molecules.) Leaving this textbox empty means that every molecule in the file will be loaded into MarvinView. Currently 
this is the default behavior of file loading in Marvin.<br>
Importing image files containing chemical structures is also possible with the help of OSRA (since version 5.3.0). Please 
<a href="../formats/imageimport.html">consult this page for details</a>. The supported image formats are: bmp, png, jpg, gif, svg, and tif. 
Furthermore, you can import chemical structures from ps and pdf files, too. In order to open an image file go to the <b>File &gt; Open...</b> 
menu and select <b>All Files</b> from the list of <b>Files of Type</b>.<br>
You can also take advantage of the <a href="../datatransfer.html">Cut/Copy/Paste and Drag &amp; Drop Functionality</a>
of Marvin to move a molecule from another chemical drawing application into MarvinView.

<a name="save"></a><h2>Saving Molecules</h2>
<P>First and foremost, MarvinView is designed for viewing molecules and for editing them. However, you can save your molecules in any of the 
<a href="../formats/formats.html">supported structure file formats</a> in MarvinView, too. In case your have multiple chemical 
objects you would like to save, you should use the <b>File &gt; Save All...</b> menu option.<br>
In the dialog window used for saving a <b>Saving method</b> tab can be opened with the help of the <b>Advanced</b> checkbox. The first half of the 
tab offers the following choices:
<ul>
	<li><b>All structures SEPARATELY:</b> Saves all structures separately in a single file.<br>
			This is the deafult behavior of saving. Without opening the <b>Saving method</b> tab, this option will be applied.</li>
	<li><b>Into SEPARATE FILES:</b> Saves each structure to a separate file.<br>
			In this option the numbering of molecule files begins with the molecule in the upper left corner.</li>
</ul>
<img src="gui/save_view.png"></P>
<P>The first option remains inactive unless the chosen file type supports multimolecule files (such as MRV or SDF). Similarly, for one molecule only 
files these options are disabled.<br>
When the given parameters for saving (filename, format, and route) are the same as for an already existing file, the outcome will be determined by the 
other two radio buttons of the dialog window. If you select the <b>Overwrite file</b> option, the original contents of the file will be replaced. 
However, selecting the <b>Append to end of file</b> radio button will add the new structures to the original file, so its contents will be preserved. 
In the first case, a new dialog window will be displayed, where you can reinforce or change your 
decision about overwriting the file. If the chosen file format does not support multiple structures, the <b>Overwrite</b> option is applied automatically.</P>

<P>You can create a new file from a subset of molecules selected from a larger file. Choosing the <b>File &gt; Save Selection...</b> menu point opens a dialog 
window where you can specify the molecules you want to save in the new file. Please note that if you have selected a molecule previously in your MarvinView table, 
the dialog window will not appear, and only the selected molecule will be saved in the new file.</P>

<P>MarvinView allows you to save an image of a selected molecule in the Viewer. When you have selected a molecule, go to <b>File &gt; Save As Image...</b>. 
You can select from JPEG, PNG, PPM, SVG, BMP, and EMF image file formats.</P>

<a name="print"></a><h2>Printing Molecules</h2>
You can print the molecule using the <B>Print</B> item in either the
<a href="gui/menubar.html#file">File Menu</a> or the <a href="view-gui.html#popup-menu">Pop-up Menu</a>.<br>
    The Print dialog box contains two sets of options: first, you choose the structures 
    to print: all molecules, all visible molecules in the window or the selected molecule. 
    Second, you may choose print (send to printer), print to pdf file or see the print preview.
 

<a name="edit"></a><h2>Editing Molecules</h2>
MarvinView is intended to be used mainly to display molecules. However, there
is a limited set of functionality available to edit the molecule while you view 
it.  To make major changes to the molecule, it can be opened in either a 
MarvinSketch window or a Source window.  In either case, the alterations made
can be imported back to the Viewer.

<a name="edit.view"></a><h3>Editing Options in MarvinView</h3>
Marvin allows you to Clean your molecule in either 2D or 3D. This
will recalculate the coordinates of the atoms and bonds to the most
appropriate location, based on the type of cleaning you select. You
can set cleaning options via the <B>Structure
&gt; Clean</B> submenu or from the <a href="view-gui.html#popup-menu">Pop-up Menu</a>.
<BR>
You can toggle the display of rings as aromatic using the <B>Structure
&gt; Aromatic Form </B>submenu.
<BR>
Using the <B>Misc</B> submenu from the <B>Edit
Menu </B> or from the <a href="view-gui.html#popup-menu">Pop-up Menu</a>, you can toggle the display of options such as Atomic Symbols, Map Numbers, and E/Z steroconfiguration labels.

<a name="edit.sketch"></a><h3>Editing in MarvinSketch</h3>
MarvinView allows you to launch a MarvinSketch window for editing the current molecule.  By selecting <B>Edit > Structure</B>, the molecule will be opened in MarvinSketch.
While you are editing the molecule in the sketcher, you will be unable to use the viewer.  
When you close the sketcher or use its Transfer button, you will be able to use the viewer again and all changes you have made in the sketcher will appear in the viewer.

<a name="edit.source"></a><h3>Editing Source</h3>
Choosing <B>Source</B> from the <B>Edit</B> menu (in the Menubar or Pop-up Menu) opens the Edit Source window. The The Edit Source window opens the file for the current molecule as text. You can view and edit the molecule file in any of the supported formats (SMILES, molfile, XYZ, etc). 
<BR>
You can alter a molecule by directly editing its source in the
Edit Source Window. You can view and edit the source in any of the
supported file formats. To change format, simply select the desired
one from the <B>Format</B>
Menu. To reload the molecule described by the text in this window
into the MarvinSketch canvas (including any changes you may have
made), select <B>File &gt;
Import</B>. This will close the Edit Source Window.

<a name="display-structure"></a><h2>Structure Display Options</h2>
<P>There is a wide range of functions related to the display of the
molecules. These settings can be found in the
<A HREF="gui/menubar.html#view" class="buttonName">View menu</A> and in
the <A href="gui/dialogs.html#preferences"
class="buttonName">Preferences dialog</a>. Additionally, you can <a href="view-basic.html#manipulate">move, rotate,
and zoom in/out</a> on the structure.</P>

<A NAME="format"></A><H3>Molecule Format</H3>
<P>You can set the display format for the molecule and screen
resolution using the <B class="buttonName">View
&gt; Display</B> submenu. Available molecule formats are
<B class="buttonName">Wireframe, Wireframe
with Knobs, Sticks, Ball and Stick,</B> and <B class="buttonName">Spacefill</B>.
You can set the resolution to low or high via the <B class="buttonName">Quality</B>
submenu.
</P>

<A NAME="molcolor"></A><H3>Colors</H3>
<P>The <B class="buttonName">View &gt;Colors</B>
submenu allows you to specify the <A HREF="../developer/sketchman.html#parameters.colorScheme">color
scheme</A> of the molecules. The available options are:
</P>
<UL>
	<LI><P STYLE="margin-bottom: 0">Monochrome
	</P>
	<LI><P STYLE="margin-bottom: 0">CPK
	</P>
	<LI><P STYLE="margin-bottom: 0">Shapely - based on RasMol's
	shapely color scheme for nucleic and amino acids
	</P>
	<LI><P STYLE="margin-bottom: 0">Group - based on PDB residue
	numbers
	</P>
	<LI><P>Atom Set
	</P>
</UL>

<A NAME="implicit-explicit-H"></A><H3>Implicit/Explicit Hydrogens</H3>
<P>Marvin has a number of options for the display of implicit and
explicit hydrogens. Because Marvin is chemically intelligent, it will
automatically add hydrogens as necessary within the structure.
They are called implicit hydrogen atoms, are always displayed without bonds and based on the options
set in the <B class="buttonName">View</B> menu. In case of Ball &amp; Stick and Spacefill modes, the
impilcit Hydrogen atoms are never displayed.    
</P>
<P>To view all hydrogen atoms explicitly, displayed as atoms with bonds connected to
neighbors, chose <B class="buttonName">Structure
&gt; Add &gt; Explicit H Atoms</B>. The <B class="buttonName">Structure
&gt; Remove &gt; Explicit H atoms</B> will return to the previous display mode.
</P>

<A NAME="marking_atoms"></A><H3>Marking Atoms</H3>
<P>
Hover the cursor over an atom and it will be marked by light blue circle. Clicking on it will select the atom in question and the circle will turn dark blue. At the same time, you can mark one atom, and select another one even if they are in different molecules. 
A property change event is sent on every atom mark. <a href="../developer/viewman.html">See developer guide for details.</a>

<P STYLE="margin-bottom: 0"><IMG SRC="gui/hover_over.png" 
    NAME="Marking Atoms" ALT="marking atoms" ALIGN=MIDDLE WIDTH=613
    HEIGHT=379 BORDER=0>
</P>

<A NAME="error-hilight"></A><H3>Error Highlighting</H3>
<P>Marvin can not automatically correct all valence errors or any reaction errors. Instead,
these errors are highlighted and you may make the
appropriate corrections yourself.
This option can be enabled and disabled using the the <B class="buttonName">Edit
&gt; Preferences </B> dialog window.
</P>

<H2><A name="preferences"></a>Saving Display Options</H2>
<p>
Many of the display settings in Marvin are saved and reloaded the next time you start the program.
    Background color, molecule color scheme, and hydrogen visibility can be set
    from the <B class="buttonName">View menu</B> and will be saved automatically when you exit the program.
    Other options, including look & feel, error highlighting, and object visibility
    can be set using the <B class="buttonName">Preferences</B> dialog window from
    the <B class="buttonName">Edit menu</B>.
</p>

<h2><a name="manipulate"></a>Manipulating the Molecule</h2>
You can translate, rotate, or resize the molecule on the canvas with a simple mouse drag.
The <a href="gui/menubar.html#view">View Menu</a> and <a href="view-gui.html#popup-menu">Pop-up Menu</a> both contains a <B>Transform</B> submenu that allows you to set the type of motion to apply to the molecule.
<BR>
By Selecting <B>Translate or Drag</B>, dragging the mouse across the canvas will result in a same displacement of the molecule.
<BR>
Selecting <B>Zoom</B> allows you to resize the molecule.  Dragging the mouse upward will zoom out, dragging downward will zoom in.
<BR>
Selecting <B>Rotate in 2D</B> allows you to rotate the molecule in 2D.  The center of the molecule becomes the center of rotation and the molecule will rotate around this point in the direction of the mouse drag.
<BR>
Selecting <B>Rotate in 3D</B> allows you to rotate the molecule in 3D around a point at the 3D center of the molecule.
<BR>
If you select the <B>Zoom/Rotate</B> option and drag the mouse
on the canvas, you can zoom the content of the canvas and/or rotate it in 2D.

<P>
The <a href="gui/menubar.html#view">View Menu</a> and the <a href="view-gui.html#popup-menu">Pop-up Menu</a> also contains the <B>Animation</B> menu, which allows you to start and stop an automatic 3D rotation of the molecule.
<P> 

<H2><A NAME="howto-multipage"></A>How to Work with Multipage Molecular Document</H2>
Multipage molecular documents help to work with large drawings by dividing them into pages.
To use the navigation tools of multipage molecular document doubleclick on the choosen cell.
<P STYLE="margin-bottom: 0"><IMG SRC="multipageView1.gif"
    NAME="Graphic2" ALT="multipage view1" ALIGN=MIDDLE WIDTH=463
    HEIGHT=501 BORDER=0>
<BR>
<BR>   
A new window will open and the following controls will automatically 
appear on the window's interface:
<ul>
    <li><B class="buttonName">Pages </B> menu on the menubar</li>
    <li>Navigation statusbar under the canvas</li>
    <li>Frame of pages, margin, title and page numbers on the canvas</li>
</ul>
<BR>
<P STYLE="margin-bottom: 0"><IMG SRC="multipageView2.gif" 
    NAME="Graphic2" ALT="multipage View2" ALIGN=MIDDLE WIDTH=447
    HEIGHT=540 BORDER=0>
<BR>
<P>
The statusbar contains information about 
the current page number in a textfield and the number of all pages on a label.
It also contains a collection of buttons to 
aid your quick navigation in the document. You can go the the first, previous, next, and last page 
using them. Alternatively you can go directly to a specific page
by entering a number in the text field and pressing enter.</P>
<BR>
<CENTER>
<TABLE WIDTH=450 BORDER=5 CELLPADDING=5 cellspacing="0" id="grid">
<TR>
    <Th COLSPAN=2>
        <P ALIGN=CENTER>Statusbar Elements</P>
    </Th>
</TR>
<TR VALIGN=TOP>
    <TD WIDTH=20%>
	<P>First page button</P>
    </TD>
    <TD>
	<P>Clicking on the <B class="buttonName">First page</B>
	button allows you to go to the first page of multipage molecular document.<BR> 
	</P>
    </TD>
</TR>
<TR VALIGN=TOP>
    <TD>
	<P>Previous page button</P>
    </TD>
    <TD>
	<P>Clicking on the <B class="buttonName">Previous page</B>
	button allows you to go to the previous page of multipage molecular document.<BR> 
	</P>
    </TD> 
</TR>
<TR VALIGN=TOP>
    <TD>
	<P>Current page field</P>
    </TD>
    <TD>
	<P>The <B class="buttonName">Current page</B>
	textfield shows you the number of current page and allows 
        you to go to a specific page of multipage molecular document 
        by entering a number in the text field and pressing enter.<BR> 
	</P>
    </TD> 
</TR>
<TR VALIGN=TOP>
    <TD>
	<P>Next page button</P>
    </TD>
    <TD>
	<P>Clicking on the 
	button allows you to go to the next page of multipage molecular document.<BR> 
	</P>
    </TD> 
</TR>
<TR VALIGN=TOP>
    <TD>
	<P>Last page button</P>
    </TD>
    <TD>
	<P>Clicking on the <B class="buttonName">Last page</B>
	button allows you to go to the last page of multipage molecular document.<BR> 
	</P>
    </TD> 
</TR>
<TR VALIGN=TOP>
    <TD>
	<P>All pages label</P>
    </TD>
    <TD>
	<P>The <B class="buttonName">All pages</B>
	label shows you the total number of pages in multipage molecular document.<BR> 
	</P>
    </TD> 
</TR>
</TABLE>
</CENTER>
<BR>
<BR>
All the navigation possibilities: go to first, previous, next, last, specific pages are available from
the <B class="buttonName">Pages </B>  menu too 
with some automatic page zooming functions.
<B class="buttonName">Pages &gt; Fit page height</B> adjusts the height of the current page to the height of the canvas.
<B class="buttonName">Pages &gt; Fit page width</B> adjusts the width of the current page to the width of the canvas.
<B class="buttonName">Pages &gt; Fit page</B> adjusts the current page so that the whole current
 page will be placed
centralized within the canvas.

<P></P>


<P ALIGN=CENTER><A HREF="#">Return to Top</A></P>
<p ALIGN=CENTER>
Copyright &copy; 1998-2013
<a HREF="http://www.chemaxon.com">ChemAxon Ltd.</a>
</p>
<p ALIGN=CENTER>
<a HREF="http://www.chemaxon.com/marvin">http://www.chemaxon.com/marvin</a>
</p>

</BODY>
</HTML>
