<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2//EN">
<html>
<head>
<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
<TITLE>Dialogs of MarvinView</TITLE>
<link rel=stylesheet type="text/css" href="../../marvinmanuals.css">
</head>
<body>
<h1>Dialogs of MarvinView</h1>

<h2><a class="anchor" name="preferences">Preferences</a></h2>
<p>The Preferences dialog window is located at the <b>Edit</b> menu.
It allows you to change many of the MarvinView display settings, including look & feel, error highlighting, and object visibility.<br>
All settings are saved and used when the application is restarted.
</p>

<h4>Display</h4>
    <blockquote><img src="../../sketch/gui/gui-files/dialogs/preferences1.png" width="579" height="388"></blockquote>
<ul>
    <li><b>Atom & Bond labels</b> are used as the default font type and size of labels
        such as C/T label of bonds, atom query property labels of atoms, etc.</li>
    <li><b>Double bond spacing</b> is a gap between two lines/sticks representing a double or triple bond measured in Angstroms.</li>
    <li><b>Wireframe bond thickness</b> is the width of bonds in wireframe mode. It is measured in Angstroms.</li>
    <li><b>Stick diameter</b> is the width of bonds in stick mode in Angstroms.</li>
    <li><b>Ball radius</b> is the size of atom spheres in Ball draw type, measured in Angstroms.</li>
    <li><b>Look & Feel</b> allows changing the visual appearance of GUI components.
        The available options are: Java Metal, Motif, JGoodies Plastic, JGoodies Plastic XP,
        and the native Look & Feels (Windows, Aqua) based on the underlying operating system.</li>
	<li><b>MarvinView Layout</b> sets the default layout to Automatic, Molecule matrix or Spreadsheet.</li>	
    <li><b>Show Bond in Hand</b> when checked, bond types are shown under the mouse cursor like template structures.</li>
	<li><b>Show Lone Pair as Line</b> when checked, lone pairs on the canvas are shown as lines.</li>
	<li><b>Show Charge in Circle</b> when checked, a circle is displayed around the charge.</li>
    <li><b>Circled Charge labels</b> are used as the font type and size of the circled charge symbols.
	<li><b>Fog effect factor:</b> manual setting of the fading strength. No fog: all regions of the structure is displayed with the same line strenght and color. Strong effect: the fading is at its maximum (molecule is only slightly visible at the far end).</li></ul>
<h4>Bonds</h4>
    <blockquote><img src="../../sketch/gui/gui-files/dialogs/preferences2.png" width="579" height="388"><br></blockquote>
    <ul>
        <li><b>Down Wedge Orientation</b> allows changing the wedge bond display convention.
        Down wedge points downward in MDL's convention, upward (at the chiral center) in Daylight's.</li>
        <li><b>Any Bond Line Style</b> offers three different modes to display bonds of unkown types: Automatic, Dashed and Solid.
        This option can be separately set to be used in MarvinSketch and MarvinView.</li>
        <li><b>Terminal Bond Deletion Method</b> offers 2 ways to delete the terminal bond of a molecule: only the bond is 
	deleted or the terminal atom disappears with the bond.</li>
		<li><b>"Coordinate" Bond Line Style</b> allows changing the type of coordinate bonds from the default ones (arrow for single atom and hashed for multicenter) to solid.</li>
    </ul>
<h4>Structure</h4>
    <blockquote><img src="../../sketch/gui/gui-files/dialogs/preferences3.png" width="579" height="388"><br></blockquote>
    <ul>
        <li><b>Highlighting Valence Errors</b> highlights atoms having wrong valences with red underline when it is checked.</li>
        <li><b>Automatic Lone Pair Calculation</b> calculates lone pairs automatically. Make sure View > Misc > Lone Pairs is checked to see the result.</li>
	<li><b>Validate S-groups At Creation</b> disables the S-group types in the drop-down list which would not yield a 
	chemically correct structure. <a href="../sketch-basic.html#howto-draw.sgroups">Usage in MarvinSketch.</a></li>
        <li><b>Carbon Labels</b> options determine the condition of displaying C labels on Carbon atoms.
            <table>
                <tr><td><img src="../../sketch/gui/gui-files/dialogs/carbon1.png" alt="Always"></td>
                    <td><img src="../../sketch/gui/gui-files/dialogs/carbon2.png" alt="Never"></td>
                    <td><img src="../../sketch/gui/gui-files/dialogs/carbon3.png" alt="Optional"></td>
                </tr>
                <tr align="center"><td>Always</td><td>Never</td><td>At straight angles<br> and implicit H atoms</td></tr>
            </table>                          
        </li>
<li><b>Ligand Orders</b> 
        <ul><li>Always</li>
	<li>Never</li>       
	<li>On R-groups with definitions</li>	   </ul>         
        </li>
    </ul>
<h4>Checkers</h4>
    <blockquote><img src="../../sketch/gui/gui-files/dialogs/preferences4.png" width="579" height="388"><br></blockquote>
    <ul>
        <li><b>Move up/down the checker items:</b> the fixing process may
            depend on the sequence of the checkers. Checking order can be
            set using the Up/Down buttons on selected checkers.</li>
        <li><b>Add checkers to the list:</b> the default list can be modified by adding other checkers.
        <li><b>Remove checkers from the list:</b> the default list can be modified by removing checkers not needed.
        <li><b>Open checker configuration from URL</b> open a checker configuration from URL.
        <li><b>Open checker configuration:</b> open your custom checker configuration from file.
		<li><b>Save checker configuration:</b> save your custom checker configuration to file.
		<li><b>Configure external checkers/fixers:</b> add external checkers/fixers; save or load external checker/fixer configuration.</li>
    </ul>
<h4>Save/Load</h4>
    <blockquote><img src="../../sketch/gui/gui-files/dialogs/preferences6.png" width="517" height="388"><br></blockquote>
    <ul>
        <li><b>Default location:</b> the folder from which to load or to save molecules may be set by the user.
        <ul><li>Startup directory: the folder where the command to start the application was given.</li>
            <li>Last location: the last folder used for opening or saving a structure.</li>
            <li>Custom working directory: a user-defined folder. If
            a molecule is loaded from another folder, then the file's location will be offered for saving.</li></ul>
        </li>
        <li><b>Default file format</b> determines which type is offered by default when structures are saved to file.</li>
        <li><b>Save/Load settings</b>
            <ul><li><b>Save/Load GUI settings (.MRV format)</b> allows storing and loading
            parameters like background color, font type, etc. beside the structures.
            This option can only be used with the
            <a href="../../formats/mrv-doc.html">MRV format</a>.</li>
            <li><b>Save/Load zoom factor (.MRV format)</b> stores and loads the
            zooming scale of the structures.
        This option can only be used with the <a href="../../formats/mrv-doc.html">MRV format</a>.</li></ul>
        </li>
        <li><b>Recent file entries</b> defines the number of files in the Recent files list in the File menu,
        with values between 1 and 10.</li>
		<li><b>Image import service URL</b> URL of a server on which a chemical structure recognition program runs can be given.</li>
		<li><b>Name import service URL</b> URL of a server on which a chemical name recognition program runs can be given.</li>
    </ul>
<h4>OLEServer</h4>
    <blockquote><img src="../../sketch/gui/gui-files/dialogs/preferences7.png" width="579" height="388"><br></blockquote>
   
    
<h2><a class="anchor" name="source">Edit Source</a></h2>
<P>You can alter a molecule by directly editing its source in the Edit Source dialog window.<br>
The dialog window provides standard clipboard operations and it is also possible to send the source text to the console.<br>
You can view and edit the source in any of the supported file formats.
You can also convert it to Java String which allows easy integration of the structure to a custom Java application code.<br>
To change the format of the source, simply select the desired one from the <B class="buttonName">View</B>
Menu. After editing the source text, you can send the structure back to the
MarvinSketch canvas by invoking <B class="buttonName">File &gt;
Import As</B>, and pressing <b>Import</b> on the appearing dialog window. This will close the Edit Source dialog window.
</P>
<blockquote><img src="../../sketch/gui/gui-files/dialogs/edit_source.png" width="416" height="397"></blockquote>
<h2><a class="anchor" name="source">Table Options</a></h2>
<P>You can adjust the layout and display options of the table from the <B class="buttonName">Table</B>
Menu. 
<ul>
<li><b>Molecule subset to display</b>: You can define the starting index and the number of molecules to be displayed. 
By default, the number of molecules is set to zero i.e., all the molecules of the opened file will be shown.</li>
<li><b>Table type</b>: Set the general layout of MarvinView. Two layouts are available: <a href="../view-gui.html">molecule matrix or spreadsheet</a>.
Automatic layout will choose the best arrangement of molecules according to the type of molecule file.</li>
<li><b>Size of visible part</b>:
<ul><li>Set the number of columns and rows of molecule matrix view.</li>
<li>Set the size of the structure display field of spreadsheet view.</li></ul> 
</li>
<li><b>Fields</b>: Besides structure, optional fields can be displayed as well. Check the requested fields, and set font sizes. 
When the molecule file has additional fields, they are listed in the <b>Available</b> and <b>Selected</b> windows. 
Set the preferred fields in the <b>Selected</b> window using add and remove buttons. 

</P>
<blockquote><img src="../../sketch/gui/gui-files/dialogs/table_optionsMvw.png" width="320" height="477"></blockquote>
</body>
</html>