<html>
<head>
<meta NAME="author" CONTENT="Peter C<PERSON>">
<title>MarvinView Quick Help</title>
<link REL="stylesheet"  TYPE="text/css" HREF="../marvinmanuals.css">
</head>
<body>

<h1>MarvinView Quick Help</h1>
<table BORDER=0 CELLPADDING=0 CELLSPACING=10>
<tr>
<td VALIGN=TOP WIDTH="50%">
<strong>Clicks</strong>
<ul>
<li>Right click for <font COLOR="#333399">pop-up</font> menu.<br>
    (Control+click on <i>Mac</i>)</li>
<li>Double click to view the structure in a separate window.</li>
</ul>
<strong>Hydrogens</strong>
<ul>
<li>In <font COLOR="#333399">View</font> or the
    <font COLOR="#333399">pop-up</font> menu, select the
    <font COLOR="#333399">Hydrogens</font> submenu
    to change the way H labels and atoms are shown.
    </li>
<li>Add explicit hydrogens using
    <font COLOR="#333399">Edit</font> -&gt;
    <font COLOR="#333399">Explicit H atoms</font> -&gt;
    <font COLOR="#333399">Add</font>.
</ul>
<strong>3D features</strong>
<ul>
<li>To start a static molecule <font COLOR="#993333">spinning</font>, select
    <font COLOR="#333399">Animation</font> -&gt;
    <font COLOR="#333399">Play</font> in the pop-up or the
    <font COLOR="#333399">View</font> menu.
    </li>
<li>To start animating an XYZ sequence, select
    <font COLOR="#333399">Animation</font> -&gt;
    <font COLOR="#333399">Play</font>.</li>
<li>To stop spinning while continuing the animation, uncheck
    <font COLOR="#333399">Animation</font> -&gt;
    <font COLOR="#333399">Drag to Spin</font>.</li>
<li>Use the <font COLOR="#333399">Display</font> submenu to
    select a rendering style from <font COLOR="#333399">Wireframe</font>,
    <font COLOR="#333399">Sticks</font>,
    <font COLOR="#333399">Ball &amp; Stick</font> or
    <font COLOR="#333399">Spacefill</font>.
    </li>
<li>To generate bonds for an XYZ structure with a different bond length
    cut-off, select
    <font COLOR="#333399">Edit</font> -&gt; <font COLOR="#333399">Bonds</font>
    -&gt; <font COLOR="#333399">Regenerate</font>.</li>
</ul>

<p>
<strong>Colors</strong>
<ul>
<li>In <font COLOR="#333399">View</font> or the
    <font COLOR="#333399">pop-up</font> menu, use the
    <font COLOR="#333399">Colors</font> submenu to select monochrome or
    <font COLOR="#666666">C</font><font COLOR="#990099">P</font><font COLOR="#009999">K</font>
    coloring, or to change the background color.
    </li>
</ul>

<p>
<strong>Printing</strong> 
<ul>
    <li>To print a molecule select <font COLOR="#333399">Print</font> -&gt; 
	<font COLOR="#333399">This Molecule</font> in the pop-up menu.</li>
    <li>To print the visible molecules of a  molecule table select 
	<font COLOR="#333399">Print</font> -&gt; <font COLOR="#333399">
	All Visible</font> in the pop-up menu or <font COLOR="#333399">
	Print</font> in the <font COLOR="#333399">File</font> menu.</li>
    <li>To print all molecules loaded in a molecule table select 
	<font COLOR="#333399">Print</font> -&gt; <font COLOR="#333399">All
	</font> in the pop-up menu.</li>
</ul>
</td>

<td VALIGN=TOP WIDTH="50%">
<strong>Mouse drag and navigation</strong>
<ul>
<li>Translation, zooming and rotation:
    <ol>
    <li>Use the keyboard to select mode:<br>
	<font COLOR="#993333">F5</font> -
	<font COLOR="#333399">Translate or Drag:</font>
	Dragging the mouse across the canvas will result in a same displacement of the molecule.
	<br>
	<font COLOR="#993333">F6</font> -
	<font COLOR="#333399">Zoom:</font>
	Hold down mouse button while dragging mouse
	vertically to zoom.<br>
	<font COLOR="#993333">F7</font> -
	<font COLOR="#333399">Rotate in 3D:</font>Allows you to rotate the molecule in 3D around a point at the 3D center of the molecule.<br>
	<font COLOR="#993333">Shift-F7</font> -
	<font COLOR="#333399">Rotate in 2D:</font> The center of the molecule becomes the center of rotation and the molecule will rotate around this point in the direction of the mouse drag.<br>
	<font COLOR="#993333">F8</font> -
	<font COLOR="#333399">Zoom/Rotate:</font>
	Hold down mouse button while dragging mouse
	vertically (to zoom) or horizontally (to rotate).<br>
	<small>(alternatively: right click for the
	<font COLOR="#333399">pop-up</font> menu, select
	the <font COLOR="#333399">Transform</font> submenu, then select
	<font COLOR="#333399">Translate or Drag</font>,
	<font COLOR="#333399">Zoom</font>,
	<font COLOR="#333399">Rotate in 3D</font>,
	<font COLOR="#333399">Rotate in 2D</font>, or
	<font COLOR="#333399">Zoom</font>),</small>
	</li>
    <li>drag the structure with the mouse.</li>
    </ol>
    </li>
<li>To <font COLOR="#993333">translate</font> without using the
    menu, press <font COLOR="#333399">Ctrl</font> while dragging.</li>
<li>To <font COLOR="#993333">zoom or rotate</font> without using the
    menu, press <font COLOR="#333399">Shift</font> while dragging.</li>
</ul>
<strong>Drag &amp Drop</strong>
<p>
Molecules can be moved also between different Marvin windows,
or from a file manager to Marvin.
To perform a <font COLOR="#993333">Drag &amp; Drop</font> operation,
<ol>
<li>press <font COLOR="#993333">F5</font> for
    <font COLOR="#333399">Translate or Drag</font> mode<br>
    <small>(alternatively: right click for the
    <font COLOR="#333399">pop-up</font> menu and select
    <font COLOR="#333399">Translate or Drag</font> in the
    <font COLOR="#333399">Transform</font> submenu)</small></li>
<li>drag the structure with the mouse</li>
<li>drop it over another viewer cell or over a sketcher window</li>
</ol>
<small><b>Note:</b><br>
Drop may not work in the JMView <i>applet</i> running in a browser window,
because drop events are received by the browser in that case.
</small>
</td>
</tr>
</table>

</body>
</html>
