<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2//EN">
<html>
<head>
<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
<TITLE>Introduction to Marvin<PERSON>iew</TITLE>
<link rel=stylesheet type="text/css" href="../marvinmanuals.css">
</head>
<body>
<h1>Introduction to <PERSON><PERSON><PERSON><PERSON></h1>
<p>
MarvinView is an advanced chemical viewer for single and multiple chemical structures, queries and reactions. It has a rich (and growing) list of vizualization features, is chemically aware and is able to call ChemAxon’s structure based calculation plugins for viewed structures.
</p>
<strong>Rich viewing:</strong>
<ul>
        <li>wide range of file types supported: MOL, MOL2, SDF, RXN, RDF (V2000 / V3000), SMILES, SMARTS/SMIRKS (recursive), MRV, InChi, CML, PDB etc
        <li>Copy and paste between different viewers/editors 
        <li>Table and single molecule views 
        <li>2D/3D representation and animation 
        <li>Outputs graphic formats (JPG, PNG, BMP, POV, SVG, EPS, PDF, EMF) 
        <li>Non-chemical data such as SDF fields can be displayed 
        <li>User and developer definable visualization styles (colours, structure representations, etc)
</ul>

<strong>Chemically aware</strong>
<ul>
        <li>Structure based calculations can be called directly from MarvinView. For a complete listing of functions please see the Calculator Plugins section
        <li>Advanced stereochemistry functions (E/Z double bonds, R/S chirality, ABS/OR/AND enhanced stereo, etc) 
        <li>2D cleaning 
        <li>3D conformer generation 
        <li>Isotopes, charges radicals, lone pairs and aliases are supported
</ul>

<strong>Cross platorm delivery</strong>

<ul>
        <li>Marvin can run on all major operating systems, it is available in the following distributions:
        <ul>
        <li><strong>Java Applets</strong> can easily be implemented into Java enabled web pages without the need for the user to install software or plugins</li>
        <li><strong>Java Beans</strong> can be directly installed to give standalone desktop applications and can also be used to integrate Marvin into Java based applications</li>
        <li><strong>Java Web Start</strong> enables web delivery of end user applications</li>
        <li><strong>.NET package</strong> makes it available to integrate Marvin into .NET applications</li>
        </ul>
</ul>

</body>
</html>
