<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>MarvinView Graphical User Interface</TITLE>
	 <link rel=stylesheet type="text/css" href="../marvinmanuals.css">
</HEAD>
<BODY><H1>MarvinView Graphical User Interface</H1>
<P><A HREF="view-index.html">Table of Contents</A> 
</P>

<P>The default layout of the MarvinView user interface is shown in the following picture.</P>
<P align="center"><IMG SRC="gui/mview.png" NAME="Graphic1"
    ALT="MarvinSView Main Window" WIDTH="408" height="304" BORDER=0>
</P>

<p>
It consists of the following primary components:</p>
<ul>
    <li><A NAME="canvas"></A>Canvas: This is the main viewing area where chemical structures, queries and reactions are displayed.</li>
    <li><a href="gui/menubar.html">Menu Bar:</a> It is located at the top of the main frame, containing menu titles that describe the content of each menu.</li>
    <li><A NAME="popup-menu"></A>Pop-up Menus: A pop-up menu (also called contextual menu) is a menu that appears upon right clicking over the canvas with the mouse. </li>
</ul>

<h2>Table Layout</h2>

MarvinView can display a large number of molecules and their corresponding chemical data fields in tabular format.
There are two main layouts available. Both layouts allow specifying which chemical data fields to display beside the molecules.

<h3>Molecule Matrix Layout</h3>

<P align="center"><IMG SRC="gui/moleculematrix.png" NAME="Graphic1"
    ALT="MarvinSView Main Window" WIDTH="688" height="515" BORDER=0>
</P>

<h3>Spreadsheet Layout</h3>

<P align="center"><IMG SRC="gui/spreadsheet.png" NAME="Graphic1"
    ALT="MarvinSView Main Window" WIDTH="688" height="515" BORDER=0>
</P>


<P ALIGN=CENTER><A HREF="#">Return to Top</A></P>
<p ALIGN=CENTER>
Copyright &copy; 1998-2013
<a HREF="http://www.chemaxon.com">ChemAxon Ltd.</a>
</p>
<p ALIGN=CENTER>
<a HREF="http://www.chemaxon.com/marvin">http://www.chemaxon.com/marvin</a>
</p>

</BODY>
</HTML>
