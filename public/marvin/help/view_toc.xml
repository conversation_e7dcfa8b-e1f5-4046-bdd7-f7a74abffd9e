<?xml version="1.0" encoding="ISO-8859-1" standalone="no"?>
  <!DOCTYPE toc PUBLIC "-//Sun Microsystems Inc.//DTD JavaHelp TOC Version 1.0//EN" "http://java.sun.com/products/javahelp/toc_1_0.dtd">
  <toc version="1.0">
    <tocitem target="View" text="MarvinView">
    <tocitem target="View.Intro" text="Introduction to MarvinView"/>
    <tocitem target="View.Install" text="Installation/System Requirements"/>
    <tocitem target="View.Launch" text="Application instructions"/>
    <tocitem target="View.License" text="License management"/>
    <tocitem target="View.Quick" text="Quick Help"/>
    <tocitem target="View.Gui" text="MarvinView Graphical User Interface">
        <tocitem target="View.Gui.Menubar" text="Menu Reference"/>
        <tocitem target="View.Gui.Popup" text="Dialogs"/>
    </tocitem>

    <tocitem target="View.Basic" text="How to Use MarvinView Features">
	  <tocitem target="View.Basic.Load" text="Loading Molecules into MarvinView"/>
          <tocitem target="View.Basic.Save" text="Saving Molecules"/>
          <tocitem target="View.Basic.Print" text="Printing Molecules"/>
          <tocitem target="View.Basic.Edit" text="Editing Molecules"/>
               <tocitem target="View.Basic.Edit.Options" text="Editing Options in MarvinView"/>
               <tocitem target="View.Basic.Edit.Sketch" text="Editing in MarvinSketch"/>
               <tocitem target="View.Basic.Edit.Source" text="Editing Source"/>
          <tocitem target="View.Basic.DisplayOptions" text="Structure Display Options"/>
               <tocitem target="View.Basic.DisplayOptions.Format" text="Molecule Format"/>
               <tocitem target="View.Basic.DisplayOptions.Colors" text="Colors"/>
               <tocitem target="View.Basic.DisplayOptions.Hydrogens" text="Implicit/Explicit Hydrogens"/>
               <tocitem target="View.Basic.DisplayOptions.Errors" text="Error Highlighting"/>
               <tocitem target="View.Basic.DisplayOptions.Save" text="Saving Display Options"/>
          <tocitem target="View.Basic.Work" text="Manipulating the Molecule"/>
          <tocitem target="View.Basic.Datatransfer" text="Cut/Copy/Paste, Drag-and-Drop Functionality"/>
          <tocitem target="View.Basic.Stereo" text="Stereochemistry"/>
    </tocitem>

      <tocitem target="Plugins" text="Calculator Plugins">
        <tocitem target="Plugins.elemanal" text="Elemental Analysis Plugin"/>
        <tocitem target="Plugins.iupacnaming" text="Naming Plugin"/>
        <tocitem target="Plugins.protonation" text="Protonation">
            <tocitem target="Plugins.protonation.pka" text="pKa Plugin"/>
            <tocitem target="Plugins.protonation.ms" text="Major Microspecies Plugin"/>
            <tocitem target="Plugins.protonation.isopoint" text="Isoelectric Point Plugin"/>
        </tocitem>
        <tocitem target="Plugins.partitioning" text="Partitioning">
            <tocitem target="Plugins.partitioning.logp" text="logP Plugin"/>
            <tocitem target="Plugins.partitioning.logd" text="logD Plugin"/>
        </tocitem>
        <tocitem target="Plugins.chargegroup" text="Charge">
            <tocitem target="Plugins.chargegroup.charge" text="Charge Plugin"/>
            <tocitem target="Plugins.chargegroup.polarizability" text="Polarizability Plugin"/>
            <tocitem target="Plugins.chargegroup.oen" text="Orbital Electronegativity Plugin"/>
        </tocitem>
        <tocitem target="Plugins.isomers" text="Isomers">
            <tocitem target="Plugins.isomers.tautomer" text="Tautomerization Plugin"/>
            <tocitem target="Plugins.isomers.resonance" text="Resonance Plugin"/>
            <tocitem target="Plugins.isomers.stereoisomer" text="Stereoisomer Plugin"/>
        </tocitem>
        <tocitem target="Plugins.conformation" text="Conformation">
            <tocitem target="Plugins.conformation.conformer" text="Conformer Plugin"/>
            <tocitem target="Plugins.conformation.moldyn" text="Molecular Dynamics Plugin"/>
            <tocitem target="Plugins.conformation.align" text="3D Alignment"/>
        </tocitem>
        <tocitem target="Plugins.geometrygroup" text="Geometry">
            <tocitem target="Plugins.geometrygroup.topolanal" text="Topology Analysis Plugin"/>
            <tocitem target="Plugins.geometrygroup.geometry" text="Geometry Plugin"/>
            <tocitem target="Plugins.geometrygroup.TPSA" text="Polar Surface Area Plugin (2D)"/>
            <tocitem target="Plugins.geometrygroup.MSA" text="Molecular Surface Area Plugin (3D)"/>
        </tocitem>
        <tocitem target="Plugins.markushenum" text="Markush Enumeration Plugin"/>
        <tocitem target="Plugins.other" text="Other">
            <tocitem target="Plugins.other.HBDA" text="Hydrogen Bond Donor-Acceptor Plugin"/>
            <tocitem target="Plugins.other.huckel" text="Huckel Analysis Plugin"/>
            <tocitem target="Plugins.other.refractivity" text="Refractivity Plugin"/>
<!--            <tocitem target="Plugins.other.framework" text="Structural Frameworks Plugin"/>	-->
        </tocitem>
        <tocitem target="Plugins.validations" text="Test Results"/>
        <tocitem target="Plugins.references" text="References"/>
      </tocitem>
<!--	  <tocitem target="Name2Structure" text="Name to Structure"/>	-->
	  
<!--      <tocitem target="Document2Structure" text="Document to Structure"/>	-->
	  
      <tocitem target="Acknowledgements" text="Acknowledgements"/>

    </tocitem>
  </toc>