<?xml version="1.0" encoding="ISO-8859-1" standalone="no"?>
  <!DOCTYPE map PUBLIC "-//Sun Microsystems Inc.//DTD JavaHelp Map Version 1.0//EN" "http://java.sun.com/products/javahelp/map_1_0.dtd">
  <map version="1.0">
    <mapID target="Sketch" url="sketch/sketch-index.html"/>
    <mapID target="Sketch.Intro" url="sketch/sketch-intro.html"/>
    <mapID target="Sketch.Install" url="applications/install.html"/>
    <mapID target="Sketch.Launch" url="applications/msketch.html"/>
    <mapID target="Sketch.License" url="licensedoc/index.html"/>
    <mapID target="Sketch.Basic" url="sketch/sketch-basic.html"/>
        <mapID target="Sketch.Formats" url="formats/formats.html"/>
        <mapID target="Sketch.DataTransfer" url="datatransfer.html"/>
        <mapID target="Sketch.Basic.Create" url="sketch/sketch-basic.html#create-molecule"/>
        <mapID target="Sketch.Basic.Open" url="sketch/sketch-basic.html#open-molecule"/>
        <mapID target="Sketch.Basic.Save" url="sketch/sketch-basic.html#save-molecule"/>
        <mapID target="Sketch.Basic.Print" url="sketch/sketch-basic.html#print"/>
        <mapID target="Sketch.Basic.Multipage" url="sketch/sketch-basic.html#howto-multipage"/>
        <mapID target="Sketch.Basic.DrawStructures" url="sketch/sketch-basic.html#howto-draw"/>
            <mapID target="Sketch.Basic.DrawAtoms" url="sketch/sketch-basic.html#howto-draw.atoms"/>
            <mapID target="Sketch.Basic.DrawBonds" url="sketch/sketch-basic.html#howto-draw.bonds"/>
            <mapID target="Sketch.Basic.DrawTemplates" url="sketch/sketch-basic.html#templates"/>
			<mapID target="Sketch.Basic.NewSubstituent" url="sketch/sketch-basic.html#newfragment"/>
			<mapID target="Sketch.Basic.Sprouting" url="sketch/sketch-basic.html#sprouting"/>
            <mapID target="Sketch.Basic.MergeStructures" url="sketch/sketch-basic.html#Merging structures"/>
			<mapID target="Sketch.Basic.DrawCCompounds" url="sketch/sketch-basic.html#coordination-compounds"/>
            <mapID target="Sketch.Basic.DrawMarkush" url="sketch/sketch-basic.html#markush-structures"/>
            <mapID target="Sketch.Basic.Flip" url="sketch/sketch-basic.html#flip"/>
            <mapID target="Sketch.Basic.Mirror" url="sketch/sketch-basic.html#mirror"/>
			<mapID target="Sketch.Basic.Inversion" url="sketch/sketch-basic.html#inversion"/>

        <mapID target="Sketch.Basic.Reactions" url="sketch/sketch-basic.html#reactions"/>
        <mapID target="Sketch.Basic.Mapping" url="sketch/sketch-basic.html#howto-map.reactions"/>
		<mapID target="Sketch.Basic.ElectronFlow" url="sketch/sketch-basic.html#howto-draw.eflowarrow"/>
		<mapID target="Sketch.Basic.DrawRQuery" url="sketch/sketch-basic.html#howto-draw.rgroups"/>
        <mapID target="Sketch.Basic.DrawSGroup" url="sketch/sketch-basic.html#howto-draw.sgroups"/>
        <mapID target="Sketch.Basic.DrawGraphics" url="sketch/sketch-basic.html#howto.draw.graphic"/>
        <mapID target="Sketch.Basic.DrawLinkAtom" url="sketch/sketch-basic.html#howto.draw.linkAtom"/>
        <mapID target="Sketch.Basic.Select" url="sketch/sketch-basic.html#howto.select.structure"/>
        <mapID target="Sketch.Basic.Delete" url="sketch/sketch-basic.html#howto.delete.structure"/>
        <mapID target="Sketch.Basic.Work" url="sketch/sketch-basic.html#howto-alter"/>
        <mapID target="Sketch.Basic.Stereo" url="sci/stereo-doc.html"/>
    <mapID target="Sketch.Basic.DisplayOptions" url="sketch/sketch-basic.html#display-structure"/>
        <mapID target="Sketch.Basic.LaunchViewers" url="sketch/sketch-basic.html#other-windows"/>
        <mapID target="Sketch.Basic.DrawingStyles" url="sketch/sketch-basic.html#styles"/>

    <mapID target="Sketch.Gui" url="sketch/sketch-gui.html"/>
        <mapID target="Sketch.Gui.Canvas" url="sketch/gui/canvas.html"/>
		<mapID target="Sketch.Gui.Menus" url="sketch/gui/menubar.html"/>
        <mapID target="Sketch.Gui.Toolbars" url="sketch/gui/toolbars.html"/>
        <mapID target="Sketch.Gui.Popups" url="sketch/gui/popups.html"/>
        <mapID target="Sketch.Gui.Statusbar" url="sketch/gui/statusbar.html"/>
        <mapID target="Sketch.Gui.Dialogs" url="sketch/gui/dialogs.html"/>
        <mapID target="Gui.Dialogs.Preferences" url="sketch/gui/dialogs.html#preferences"/>
        <mapID target="Sketch.Gui.Shortcuts" url="sketch/gui/shortcuts.html"/>
        <mapID target="Sketch.Gui.Customize" url="sketch/gui/customization.html"/>
        <mapID target="Sketch.Gui.Configurations" url="sketch/gui/configurations.html"/>
        <mapID target="Sketch.Gui.Customize_Server" url="sketch/gui/customization_server_side.html"/>


    <mapID target="Sketch.Chem" url="sketch/sketch-chem.html"/>
        <mapID target="Sketch.Chem.valence-check" url="sketch/sketch-chem.html#valence-check"/>
        <mapID target="Sketch.Chem.structurecheck" url="sketch/sketch-chem.html#structurechecker"/>
		<mapID target="Sketch.Chem.charges" url="sketch/sketch-chem.html#charges"/>
        <mapID target="Sketch.Chem.reactions" url="sketch/sketch-chem.html#reactions"/>
        <mapID target="Sketch.Chem.mapping" url="sketch/sketch-chem.html#mapping"/>
        <mapID target="Sketch.Chem.isotopes" url="sketch/sketch-chem.html#isotopes"/>
        <mapID target="Sketch.Chem.e-z" url="sketch/sketch-chem.html#e-z"/>
        <mapID target="Sketch.Chem.sgroups" url="sketch/sketch-chem.html#sgroups"/>
        <mapID target="Sketch.Chem.link-nodes" url="sketch/sketch-chem.html#link-nodes"/>
        <mapID target="Sketch.Chem.groups" url="sketch/sketch-chem.html#groups"/>
        <mapID target="Sketch.Chem.radicals" url="sketch/sketch-chem.html#radicals"/>
        <mapID target="Sketch.Chem.query-guide" url="sketch/sketch-chem.html#query-guide"/>
        <mapID target="Sketch.Chem.rgroups" url="sketch/sketch-chem.html#rgroups"/>
        <mapID target="Sketch.Chem.atomlist" url="sketch/sketch-chem.html#atomlist"/>
        <mapID target="Sketch.Chem.notlist" url="sketch/sketch-chem.html#notlist"/>
        <mapID target="Sketch.Chem.generic-atoms" url="sketch/sketch-chem.html#generic-atoms"/>
        <mapID target="Sketch.Chem.atom-props" url="sketch/sketch-chem.html#atom-props"/>
		<mapID target="Sketch.Chem.attacheddata" url="sketch/sketch-chem.html#attacheddata"/>
    
    <mapID target="Plugins" url="calculations/calculator-plugins.html"/>
        <mapID target="Plugins.elemanal" url="calculations/elemanal.html"/>
        <mapID target="Plugins.iupacnaming" url="calculations/s2n.html"/>
        <mapID target="Plugins.protonation" url="calculations/protonation.html"/>
            <mapID target="Plugins.protonation.pka" url="calculations/protonation.html#pka"/>
            <mapID target="Plugins.protonation.ms" url="calculations/protonation.html#ms"/>
            <mapID target="Plugins.protonation.isopoint" url="calculations/protonation.html#isopoint"/>

        <mapID target="Plugins.partitioning" url="calculations/partitioning.html"/>
            <mapID target="Plugins.partitioning.logp" url="calculations/partitioning.html#logp"/>
            <mapID target="Plugins.partitioning.logd" url="calculations/partitioning.html#logd"/>

        <mapID target="Plugins.chargegroup" url="calculations/chargegroup.html"/>
            <mapID target="Plugins.chargegroup.charge" url="calculations/chargegroup.html#charge"/>
            <mapID target="Plugins.chargegroup.polarizability" url="calculations/chargegroup.html#polarizability"/>
            <mapID target="Plugins.chargegroup.oen" url="calculations/chargegroup.html#oen"/>
            <mapID target="Plugins.chargegroup.dipole" url="calculations/chargegroup.html#dipole"/>

		<mapID target="Plugins.nmrgroup" url="calculations/nmr.html"/>
            <mapID target="Plugins.nmrgroup.cnmr" url="calculations/nmrpredict.html"/>
            <mapID target="Plugins.nmrgroup.hnmr" url="calculations/nmrpredict.html"/>
            <mapID target="Plugins.nmrgroup.nmrview" url="calculations/nmrview.html"/>
			
        <mapID target="Plugins.isomers" url="calculations/isomers.html"/>
            <mapID target="Plugins.isomers.tautomer" url="calculations/isomers.html#tautomer"/>
            <mapID target="Plugins.isomers.stereoisomer" url="calculations/isomers.html#stereoisomer"/>

        <mapID target="Plugins.conformation" url="calculations/conformation.html"/>
            <mapID target="Plugins.conformation.conformer" url="calculations/conformation.html#conformer"/>
            <mapID target="Plugins.conformation.moldyn" url="calculations/conformation.html#moldyn"/>
            <mapID target="Plugins.conformation.align" url="calculations/conformation.html#align"/>

        <mapID target="Plugins.geometrygroup" url="calculations/geometrygroup.html"/>
            <mapID target="Plugins.geometrygroup.topolanal" url="calculations/geometrygroup.html#topolanal"/>
            <mapID target="Plugins.geometrygroup.geometry" url="calculations/geometrygroup.html#geometry"/>
            <mapID target="Plugins.geometrygroup.TPSA" url="calculations/geometrygroup.html#TPSA"/>
            <mapID target="Plugins.geometrygroup.MSA" url="calculations/geometrygroup.html#MSA"/>
     
	    <mapID target="Plugins.markushenum" url="calculations/markush.html"/>
     
		<mapID target="Plugins.other" url="calculations/other.html"/>
            <mapID target="Plugins.other.HBDA" url="calculations/other.html#HBDA"/>
            <mapID target="Plugins.other.huckel" url="calculations/other.html#huckel"/>
            <mapID target="Plugins.other.refractivity" url="calculations/other.html#refractivity"/>
            <mapID target="Plugins.other.resonance" url="calculations/other.html#resonance"/>
            <mapID target="Plugins.other.framework" url="calculations/other.html#framework"/>

        <mapID target="Plugins.services" url="calculations/services_menu.html"/>
		
		<mapID target="Plugins.validations" url="calculations/Validations.html"/>
        <mapID target="Plugins.references" url="calculations/references.html"/>
	<mapID target="Name2Structure" url="naming/n2s.html"/>
	<mapID target="Document2Structure" url="d2s/d2s.html"/>
  

    <mapID target="Acknowledgements" url="acknowledgements.html"/>
</map>