<html>
<head>
<meta NAME="author" CONTENT="Peter <PERSON>">
<link REL ="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css" TITLE="Style">
<title>Image export</title>
</head>
<body>

<h1>Image export in Marvin</h1>
<h2>Contents</h2>
<ul>
<li><a href="#image">Image formats</a></li>
<li><a href="#options">Export options</a></li>
</ul>
<p>
<h2><a class="anchor" name="image">Image formats</a></h2>
<PERSON> is able to export bitmap image files like
<a HREF="jpeg-doc.html">JPEG</a>, <a HREF="msbmp-doc.html">MS BMP</a>,
<a HREF="png-doc.html">PNG</a>, <a HREF="ppm-doc.html">PPM</a> and
vector graphics files like 
<a HREF="svg-doc.html">SVG</a>, <a HREF="pdf-doc.html">PDF</a> and 
<a HREF="emf-doc.html">EMF</a>.
Restrictions:
<ol>
<li>These are binary file formats, so the
    applet.getMol(...) function does not work.
<li>Instead of the molecule.toFormat(...) function, the
    molecule.<a HREF="../developer/beans/api/chemaxon/struc/Molecule.html#toBinFormat(java.lang.String)">toBinFormat(...)</a>
    method should be used.</li>
<li>Command line creation of image files using
    <a HREF="molconvert.html">MolConverter</a> works only in
    Java 1.2 or later JVMs.</li>
</ol>
<h2><a class="anchor" NAME="options">Export options</a></h2>
Image export options are separated by commas
in format descriptor strings.<br>
The following common options are recognized by all image export modules:
<blockquote>
<table CELLSPACING="0" CELLPADDING="5" border="0">
<tr VALIGN=TOP><td>...</td>
    <td><a HREF="basic-export-opts.html">Basic aromatization and H atom
	adding/removal options.</a></td></tr>
<tr VALIGN=TOP><td><a class="text" NAME="H_off"><code>H_off</code></a></td>
    <td>Do not show implicit Hydrogen labels.</td></tr>
<tr VALIGN=TOP><td><code>H_hetero</code></td>
    <td>Implicit Hydrogen labels on heteroatoms only.</td></tr>
<tr VALIGN=TOP><td><code>H_heteroterm</code></td>
    <td>Implicit Hydrogen labels on hetero- and terminal atoms (default).
	</td></tr>
<tr VALIGN=TOP><td><code>H_all</code></td>
    <td>Implicit Hydrogen labels on all atoms.</td></tr>
<tr VALIGN=TOP><td><a class="text" NAME="chiral"><code>chiral_off</code></a></td>
    <td>Switch off chirality support, do not show R/S labels (default).</td></tr>
<tr VALIGN=TOP><td><code>chiral_selected</code></td>
    <td>Show R/S if the chiral flag is set for the molecule.</td></tr>
<tr VALIGN=TOP><td><code>chiral_all</code></td>
    <td>Show R/S for any molecule.</td></tr>
<tr VALIGN=TOP><td><code>w...</code><br><code>h...</code></td>
    <td>Image width and height in pixels. If only one from w and h is
	specified, then the other will have the same value.
	If none of them is specified, then their values are calculated from
	<a HREF="#scale">scale</a>. If scale is not specified, then the
	default size is 200x200.</td></tr>
<tr VALIGN=TOP><td><a class="text" NAME="scale"><code>scale...</code></a></td>
    <td>Magnification. 1.54&Aring; (C-C bond length) is <em>scale</em> pixels.
	</td></tr>
<tr VALIGN=TOP><td><a class="text" NAME="maxscale"><code>maxscale...</code></a></td>
    <td>Maximizes the magnification to prevent overscaling of small molecules.<br>
        It is usually set to 28, which is the scale factor for 100% magnification.
        </td></tr>
<tr VALIGN=TOP><td><a class="text" NAME="atsiz"><code>atsiz...</code></a></td>
    <td>Atom label font size in C-C bond length units.
	Default: 0.4<br>
	<center>
	<em>atsiz</em>*1.54 &Aring; = <em>atsiz</em>*<em>scale</em> points
	</center>
	</td></tr>
<tr VALIGN=TOP><td><a class="text" NAME="bondw"><code>bondw...</code></a></td>
    <td>Width of double bond in C-C bond length units.
	Default: 0.18<br>
	<center>
	<em>bondw</em>*1.54 &Aring; = <em>bondw</em>*<em>scale</em> pixels
	</center>
	</td></tr>
<tr VALIGN=TOP><td><a class="text" NAME="wireThickness"><code>wireThickness...</code></a></td>
    <td>Bond thickness in wireframe mode.
	Default: 0.064
    </td></tr>
<tr VALIGN=TOP><td><a class="text" NAME="stickThickness"><code>stickThickness...</code></a></td>
    <td>The stick diameter for ball and stick mode.
	Default: 0.1
    </td></tr>
<tr VALIGN=TOP><td><a class="text" NAME="ballRadius"><code>ballRadius...</code></a></td>
    <td>Ball radius for ball and stick mode.
	Default: 0.5
    </td></tr>
        
<tr VALIGN=TOP><td><code>#rrggbb</code></td>
    <td>Background color. It also determines the brightness of the CPK palette
	(for atoms and bonds); lighter colors are choosen automatically
	for dark background and conversely.
	Default: &quot;#ffffff&quot;</td></tr>
<tr VALIGN=TOP><td><code>#aarrggbb</code></td>
    <td>Background color with alpha value.
	Use alpha=0 for transparent background, e.g. #00ffffff.
	Note that the alpha channel is not supported by all image formats.
	Default: &quot;#ffffffff&quot;</td></tr>
<tr VALIGN=TOP><td><code>transbg</code></td>
    <td>Sets the image background to transparent.</td></tr>
<tr VALIGN=TOP><td><code>mono</code></td>
    <td>Black &amp; white.</td></tr>
<tr VALIGN=TOP><td><code>cpk</code></td>
    <td>Use CPK colors (default).</td></tr>
<tr VALIGN=TOP><td><code>shapely</code></td>
    <td>Use the <a HREF="../developer/shapely-scheme.html">shapely</a> color scheme.</td></tr>
<tr VALIGN=TOP><td><code>group</code></td>
    <td>Use coloring based on residue sequence numbers.</td></tr>
<tr VALIGN=TOP><td><a class="text" NAME="setcolors"><code>setcolors</code></a><br>
    <code>setcolors:</code>...</td>
    <td>Use atom/bond set colors. Colors can be specified as a colon
	separated list of values.
	Use "a<i>k</i>:#rrggbb" for atom set <i>k</i>,
	"b<i>k</i>:#rrggbb" for bond set <i>k</i>. The hashmark "#"
	can be omitted. Human-readable color names like "red", "green", "blue"
	can also be used.
	</td></tr>
<tr VALIGN=TOP><td><code>wireframe</code></td>
    <td>Wireframe rendering style (default for 2D).</td></tr>
<tr VALIGN=TOP><td><code>wireknobs</code></td>
    <td>Wireframe with knobs.</td></tr>
<tr VALIGN=TOP><td><code>ballstick</code></td>
    <td>&quot;Ball &amp; stick&quot; rendering style (default for 3D).</td></tr>
<tr VALIGN=TOP><td><code>spacefill</code></td>
    <td>Spacefill rendering style.</td></tr>
<tr VALIGN=TOP><td><code>noantialias</code></td>
    <td>Switch off antialiasing.</td></tr>
<!--<tr VALIGN=TOP><td><code>transbg</code></td>
    <td>Transparent background (only EMF, SVG and PNG format support it).</td></tr>-->
<tr VALIGN=TOP><td><a class="text" name="amap"><code>amap</code></a></td>
    <td>Displays atom mapping.</td></tr>
<tr VALIGN=TOP><td><a class="text" name="anum"><code>anum</code></a></td>
    <td>Displays atom numbers.</td></tr>
<tr VALIGN=TOP><td><a class="text" name="lp"><code>lp</code></a></td>
    <td>Displays lone pairs.</td></tr>
<tr VALIGN=TOP><td><a class="text" name="lpexpl"><code>lpexpl</code></a></td>
    <td>Display the explicit lone pairs instead of the implicit lone pairs 
    if lone pair displaying is switched on. See the <code>lp</code> parameter.</td></tr>
<tr VALIGN=TOP><td><code>lonePairsAsLine</code></td>
    <td>Display lone pairs as a line instead of the default two dots. This parameter 
    has effect only if the <code>lp</code> parameter is also specified.
	</td></tr>    
<tr VALIGN=TOP><td><a class="text" name="downwedge_mdl"><code>downwedge_mdl</code></a></td>
    <td>Down wedge orientation points downward (MDL). (default)
<tr VALIGN=TOP><td><a class="text" name="downwedge_daylight"><code>downwedge_daylight</code></a></td>
    <td>Down wedge orientation points upward (Daylight).</td></tr>
<tr VALIGN=TOP><td><a class="text" NAME="anybond_auto"><code>anybond_auto</code></a></td>
    <td>Draw any bonds with dashed lines in most cases.
    If all bonds are generated from atom coordinates, any bonds are displayed
    with solid lines. (default)</td></tr>
<tr VALIGN=TOP><td><code>anybond_dashed</code></td>
    <td>Draw any bonds with dashed lines.</td></tr>
<tr VALIGN=TOP><td><code>anybond_solid</code></td>
    <td>Draw any bond with solid lines.
	</td></tr>
<tr VALIGN=TOP><td><code>noatsym</code></td>
    <td>Hide atom symbols in 3D mode.</td></tr>
<tr VALIGN=TOP><td><code>valprop</code></td>
    <td>Show valence property on atoms that have the valence property explicitly set.</td></tr>
<tr VALIGN=TOP><td><code>ez</code></td>
    <td>Show E/Z.</td></tr>
<tr VALIGN=TOP><td><a class="text" NAME="cv_on"><code>cv_on</code></a></td>
    <td>Always show the atom labels of carbon atoms.</td></tr>
<tr VALIGN=TOP><td><code>cv_off</code></td>
    <td>Never show the atom labels of carbon atoms.</td></tr>
<tr VALIGN=TOP><td><code>cv_inChain</code></td>
    <td>Show the atom labels of carbon atoms at straight angles and at implicit Hydrogens.
	</td></tr>    
<tr VALIGN=TOP><td><code>grinvVisible</code></td>
    <td>Displays graph invariants.
	</td></tr>    
<tr VALIGN=TOP><td><code>bondLengthVisible</code></td>
    <td>Displays the length of bonds in Angstroms.
	</td></tr>    
<tr VALIGN=TOP><td><code>valenceErrorVisible</code></td>
    <td>Displays valence errors.
	</td></tr>    
<tr VALIGN=TOP><td><code>absLabelVisible</code></td>
    <td>Sets the Abolute label visibility to true.
	</td></tr>    
<tr VALIGN=TOP><td><code>ligandOrderVisibility_withDef</code></td>
    <td>Active by default. Show ligand order on images only when the R-group definition is present.
	</td></tr>    
<tr VALIGN=TOP><td><code>ligandOrderVisibility_on</code></td>
    <td>Show all ligand order on images for R-groups.
	</td></tr>    
<tr VALIGN=TOP><td><code>ligandOrderVisibility_off</code></td>
    <td>Never show ligand order on images for R-groups.
	</td></tr>    
<tr VALIGN=TOP><td><code>valprop</code></td>
    <td>Show valence property on atoms.
	</td></tr>    
<tr VALIGN=TOP><td><code>liganderr</code></td>
    <td>Show ligand errors on R-groups.
	</td></tr>    
<tr VALIGN=TOP><td><code>coordBondStyle_solid</code></td>
    <td>Display coordinate bond as a single bond.
	</td></tr>    
<tr VALIGN=TOP><td><code>coordBondStyle_arrow</code></td>
    <td>Display coordinate bond as an arrow.
	</td></tr>    
<tr VALIGN=TOP><td><code>coordBondStyleAtMulticenter_dashed</code></td>
    <td>Display coordinate bond as a dashed bond when it connects to a multicenter atom.
	</td></tr>    
<tr VALIGN=TOP><td><code>coordBondStyleAtMulticenter_solid</code></td>
    <td>Display coordinate bond as a single bond when it connects to a multicenter atom.
	</td></tr>    
<tr VALIGN=TOP><td><code>chargeWithCircle</code></td>
    <td>Display charge symbols in a circle.
	</td></tr>    
<tr VALIGN=TOP><td><code>oneLetterPeptideDisplay</code></td>
    <td>Display peptides with their one letter abbreviation instead of the three letter
    abbreviation which is the default.
	</td></tr>    

</table>
</blockquote>
2D defaults: <code>H_heteroterm,w200,h200,#ffffffff,cpk,wireframe</code>
<p>
3D defaults: <code>H_heteroterm,w200,h200,#ff000000,cpk,ballstick</code>
<p>
Examples:
<blockquote>
<table CELLSPACING="0" CELLPADDING="5" border="0">
<tr><td><code>jpeg</code></td>
    <td>Default settings: 200x200 pixels, white background
	(or black in 3D).</td></tr>
<tr><td><code>jpeg:w100,#ffff00</code></td>
    <td>100x100 JPEG with yellow background.</td></tr>
<tr><td><code>jpeg:w100,h150</code></td>
    <td>100x150 JPEG with default background.</td></tr>
</table>
</blockquote>

</body>
</html>
