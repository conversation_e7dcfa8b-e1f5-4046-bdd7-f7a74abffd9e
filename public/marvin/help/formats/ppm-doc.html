<html>
<head>
<meta NAME="author" CONTENT="Peter <PERSON>">
<title>PPM image export in Marvin</title>
<link REL ="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css">
</head>
<body>

<h1>PPM</h1>

<p>
Codename: <strong>ppm</strong>

<p>
<PERSON> is able to export PPM
<a HREF="images-doc.html">image files</a>.
The PpmExport module recognizes
the <a HREF="images-doc.html#options">common image export options</a>.
Examples:
<blockquote>
<table CELLSPACING="10" CELLPADDING="0">
<tr VALIGN=TOP><td><code>ppm</code></td>
    <td>Default settings: 200x200 pixels,
	white background (or black in 3D).</td></tr>
<tr VALIGN=TOP><td><code>ppm:w100,#ffff00</code></td>
    <td>100x100 PPM with yellow background.</td></tr>
</table>
</blockquote>
<p>

<p ALIGN=LEFT>
<small>PpmExport uses the <em>PPM encoder</em>,
<a HREF="../license/PpmEncoder.license.txt">Copyright &copy; Jef Poskanzer</a>.</small>
</p>

</body>
</html>
