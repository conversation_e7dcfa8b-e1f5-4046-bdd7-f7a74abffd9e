<html>
<head>
<link REL ="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css" TITLE="Style">
<title>EMF export in Marvin</title>
</head>
<body>

<h1>Windows Enhanced MetaFile</h1>
<p>
Codename: <strong>emf</strong>

<p>
The Marvin beans and applications are able to export in EMF format (as a vector
graphical <a HREF="images-doc.html">image</a>). 
This function does not work in
the applets (except signed applets). EMF export requires Java 1.4 (or higher version).

<p>
The EmfExport module recognizes
the <a HREF="images-doc.html#options">common image export options</a>.
<p>
<h2>Known Issues</h2>
<ul>
<li>Sometimes, multi color bonds are curved.</li>
<li>In &quot;Ball and Stick&quot; and in &quot;Spacefill&quot; rendering mode, Marvin applets
generates a zero length file when you save the molecule in EMF format.
From Marvin Beans API or applications, copying and saving in EMF format works 
fine.</li>
<li>Marvin copy restricts &quot;Ball and Stick&quot; and &quot;Spacfill&quot;
rendering mode at EMF copy. In this case, the copied image will be drawn
in &quot;Wireframe&quot; or &quot;Sticks&quot; rendering mode.</li>
</ul>

<h2>References</h2>
<ul>
<li><a href="http://en.wikipedia.org/wiki/Enhanced_Metafile" target="_top">Windows Metafile definition (in WikiPedia)</a>
<li><a HREF="http://java.freehep.org/vectorgraphics/" TARGET="_top">VectorGraphics (2D Vector and Image Graphics Libary)</a></li>
<li><a HREF="http://java.freehep.org/
" TARGET="_top">FreeHEP Java Library</a></li>
</ul>

<p ALIGN=LEFT>
<small>EmfExport uses the <em>VectorGraphics</em> package of <em>FreeHEP Java Library</em> that is an &quot;Open Source&quot; library distributed under the terms of the <a HREF="http://www.gnu.org/copyleft/lesser.html">GNU Lesser General 
Public License (LGLP)</a>.
You can freely modify and recompile the 
<a HREF="http://java.freehep.org/vectorgraphics/SourceDistributions.html">source</a> 
of this library, then replace the binaries in Marvin with your own version,
according to the requirements of the <em>LGLP</em>.<br>
To update Marvin with your own version of <em>FreeHep library</em>, substitute the proper jar files (<code>freehep-base.jar, freehep-graphics2d.jar, freehep-graphicsio.jar, freehep-graphicsio-emf.jar</code>) in your Marvin package.
</small>
</p>

</body>
</html>
