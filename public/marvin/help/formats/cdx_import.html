<!-- 
    Document   : cdx_import
    Created on : Nov 27, 2008, 3:08:30 PM
    Author     : <PERSON><PERSON><PERSON>
    Edited by  : <PERSON>
-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <title>CDX Import options details</title>
    <link REL ="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css" TITLE="Style">
  </head>
  <body>
  <h1>CDX Import options details</h1>
 
 <h2><a name="generic">Generic labels</a></h2>
 <p>The generic labels from the CDX file format ar imported to <PERSON> as:</p>
 
   <table border="1">
            <tr><th width="50">Label</th><th width="220">ChemDraw</th><th width="120">Marvin</th></tr>
            <tr><td>A</td><td>Any Atom</td><td>Any atom type</td></tr>
            <tr><td>M</td><td>Metal</td><td>Pseudo atom: M</td></tr>
            <tr><td>Q</td><td>Non-hydrogen heteroatom</td><td>Hetero atom type</td></tr>
            <tr><td>X</td><td>Halogen</td><td>Pseudo atom type</td></tr>
            <tr><td>R</td><td>Unspecified</td><td>R-group atom type</td></tr>
          </table>
 
 
 <h2><a name="atoms">Atoms</a></h2>
 <p>The atom features from the CDX file format import limitations are the following:</p>
  
  <p>  <table border="1">
<thead>
<tr>
<th>ChemBioDraw</th>
<th>Marvin</th>
</tr>
</thead>
<tbody>

<tr><td>Charge</td><td>	Supported</td></tr>
<tr><td>Isotope	</td><td>Supported</td></tr>
<tr><td>Isotopic Abundance</td><td>	Not supported</td></tr>
<tr><td>Substituents up to	</td><td>'s&#60;n&#62;' query property up to 6</td></tr>
<tr><td>Substituents Exactly</td><td>	's&#60;n&#62;' query property up to 6</td></tr>
<tr><td>Free Sites	</td><td>*0 is converted to 's*', others are converted to 's&#60;n&#62;', calculated: Free Sites-number of bonds.  </td></tr>
<tr><td>Unsaturation	</td><td>'u' query property if set to 'Must be absent'</td></tr>
<tr><td>Reaction Change	</td><td>Not supported</td></tr>
<tr><td>Reaction Stereo	</td><td>Supported</td></tr>
<tr><td>Translation	</td><td>Not supported</td></tr>
<tr><td>Abnormal Valence	</td><td>Not supported</td></tr>
<tr><td>Enhanced Stereochemistry	</td><td>Supported</td></tr>
<tr><td>Radical	</td><td>Supported</td></tr>
<tr><td>Implicit Hydrogens	</td><td>If not allowed, the atom gets a 'h0' query property</td></tr>
<tr><td>Ring Bond Count	</td><td>Supported</td></tr>
<tr><td>Hdot / HDash	</td><td>Imported as explicit hydrogens</td></tr>

</tbody>
</table>
</p>
 
 <h2><a name="bonds">Bonds</a></h2>
  <p>The following bond features from the CDX file format are imported to Marvin as:</p>
  
  <p>  <table border="1">
<thead>
<tr>
<th>ChemBioDraw</th>
<th>Marvin</th>
</tr>
</thead>
<tbody>
<tr><td colspan="2"><b>Single bond</b>
<tr>
<td>Plain</td><td>Single bond</td></tr>
<tr>
<td>Dashed</td><td>	Single bond Down</td></tr>
<tr>
<td>Hashed	</td><td>Single bond Down</td></tr>
<tr>
<td>Hashed Wedged</td><td>	Single bond Down</td></tr>
<tr>
<td>Bold</td><td>	Bold bond</td></tr>
<tr>
<td>Bold Wedged	</td><td>Single bond Up</td></tr>
<tr>
<td>Hollow Wedged	</td><td>Single bond Up</td></tr>
<tr>
<td>Dative</td><td>	Coordinative bond</td></tr>
<tr>
<td>Wavy	</td><td>Single bond Up or Down</td></tr>
<tr>
<td colspan="2"><b>Double bonds	</b></td></tr>
<tr>
<td>Plain</td><td>	Double bond</td></tr>
<tr>
<td>Bold	</td><td>Double bond</td></tr>
<tr>
<td>Double Either</td><td>	Double bond</td></tr>
<tr>
<td>Tautomeric</td><td>	Single or double query bond type</td></tr>
<tr>
<td>Aromatic</td><td>	Aromatic bond</td></tr>
<tr>
<td>Triple bond	</td><td>Triple bond</td></tr>
<tr>
<td>Quadruple bond</td><td>	Any bond</td></tr>
<tr>
<td colspan="2">Bond properties	</td></tr>
<tr>
<td colspan="2"><b>Query bonds</b>	</td></tr>
<tr>
<td>Any	</td><td>Any bond</td></tr>
<tr>
<td>S/D	</td><td>Single or double query bond type</td></tr>
<tr>
<td>S/A</td><td>	Single or aromatic query bond type</td></tr>
<tr>
<td>D/A	</td><td>Double or aromatic query bond type</td></tr>
<tr>
<td colspan="2"><b>Topology</b>	</td></tr>
<tr>
<td>Ring</td><td>	Bond is in ring</td></tr>
<tr>
<td>Chain</td><td>	Bond is in chain</td></tr>
<tr>
<td>Ring or Chain	</td><td>No topology</td></tr>
<tr>
<td colspan="2"><b>Reaction center</b>	</td></tr>
<tr>
<td>Center	</td><td>The bond is a reacting center.</td></tr>
<tr>
<td>Make/Break</td><td>	The bond is made or broken in the reaction</td></tr>
<tr>
<td>Change	</td><td>The bond (order) has changed in the reaction</td></tr>
<tr>
<td>Make and Change</td><td>	The bond is created and changed</td></tr>
<tr>
<td>Not Center</td><td>	The bond is not a reacting center</td></tr>
<tr>
<td>Not modified</td><td>	The bond is not modified in the reaction</td></tr>
</tbody>
</table>
</p>

 <h2><a name="reaction">Reaction arrow</a></h2>
<p>The following reaction arrows from the CDX file format are imported to Marvin as:</p>

<p>
<table border="1">
<thead>
<tr>
<th>ChemBioDraw</th>
<th>Marvin</th>
</tr>
</thead>
<tbody>
<tr><td>Solid</td><td>	Single reaction arrow type</td></tr>
<tr><td>Bold	</td><td>Single reaction arrow type</td></tr>
<tr><td>Dashed	</td><td>Single reaction arrow type</td></tr>
<tr><td>One-Sided	</td><td>Single reaction arrow type</td></tr>
<tr><td>Hollow	</td><td>Single reaction arrow type</td></tr>
<tr><td>No Go	</td><td>Single reaction arrow type</td></tr>
<tr><td>Retrosynthetic	</td><td>Double reaction arrow type</td></tr>
<tr><td>Resonance	</td><td>Resonance arrow type</td></tr>
<tr><td>Equilibrium	</td><td>Equilibrium arrow type</td></tr>
<tr><td>Degree arrows	</td><td>Not yet supported, will be converted to electron flows</td></tr>

</tbody>
</table>
<p>Note that files containing multistep reactions are imported with graphical arrows, thus these won't be recognized as single reactions in Marvin.</p>

 
 <h2><a name="groups">Groups</a></h2>
<p>The following groups from the CDX file format are imported to Marvin as:</p>

<p>
<table border="1">
<thead>
<tr>
<th>ChemBioDraw</th>
<th>Marvin</th>
</tr>
</thead>
<tbody>
<tr><td colspan="2"><b>Bracket Usage</b>	</td></tr>
<tr><td>Unspecified</td><td>	Generic S-group type</td></tr>
<tr><td>Anypolymer (anyp)	</td><td>Any polymer S-group type</td></tr>
<tr><td>Component (c)	</td><td>Component S-group type</td></tr>
<tr><td>Copolymer (co)	</td><td>Copolymer S-group type</td></tr>
<tr><td>Copolymer, alternating (alt)	</td><td>Copolymer S-group type with alternating polymer S-group subtype</td></tr>
<tr><td>Copolymer, block (blk)	</td><td>Copolymer S-group type with block polymer S-group subtype</td></tr>
<tr><td>Copolymer, random (ran)	</td><td>Copolymer S-group type with random polymer S-group subtype</td></tr>
<tr><td>Crosslink (xl)	</td><td>Crosslink S-group type</td></tr>
<tr><td>Generic ()	</td><td>Generic S-group type</td></tr>
<tr><td>Graft (grf)	</td><td>Graft S-group type</td></tr>
<tr><td>Mer (mer)	</td><td>Mer S-group type</td></tr>
<tr><td>Mixture, ordered (f)	</td><td>Formulation S-group type.</td></tr>
<tr><td>Mixture, unordered (mix)	</td><td>Mixture S-group type</td></tr>
<tr><td>Modification (mod)	</td><td>Modification S-group type</td></tr>
<tr><td>Monomer (mon)	</td><td>Monomer S-group type</td></tr>
<tr><td>Multiple Group (#)	</td><td>Multiple group S-group type</td></tr>
<tr><td>SRU (n)	</td><td>SRU S-group type</td></tr>
<tr><td colspan="2"><b>Repeat pattern</b>	</td></tr>
<tr><td>Head-to-Tail	</td><td>Head-to-tail S-group connectivity</td></tr>
<tr><td>Head-to-Head	</td><td>Head-to-head S-group connectivity</td></tr>
<tr><td>Either/Unknown	</td><td>Either unknown S-group connectivity</td></tr>
<tr><td>Flip Type</td><td>	Supported</td></tr>

</tbody>
</table>

<h2><a name="symbols">Symbols</a></h2>
<table border="1">
<thead>
<tr>
<th>ChemBioDraw</th>
<th>Marvin</th>
</tr>
</thead>
<tbody>
    <tr><td>Lone Pairs</td><td>Imported as lone pairs, but automatic lone pair calculation writes them over. It can be turned off at the Edit > Preferences... > Structure tab. </td></tr>
    <tr><td>Abs label</td><td>If there is a label in the cdx file, the absolute stereo flag is set to the molecule.</td></tr>
</tbody>
</table>

<h2><a name="graphics">Complex graphics</a></h2>
<table border="1">
<thead>
<tr>
<th>ChemBioDraw</th>
<th>Marvin</th>
</tr>
</thead>
<tbody>
    <tr><td>Tables</td><td>Tables are imported as graphical objects. The cell size and relative positions are retained, and 
the cells visualized by rectangles.</td></tr>
    <tr><td>TLC plates</td><td>TLC plate drawing is imported with graphical elements, keeping original positions.</td></tr>
</tbody>
</table>


<p><center><a href="cdx-doc.html">Back to CDX file format page</a></center></p>

  </body>
</html>
