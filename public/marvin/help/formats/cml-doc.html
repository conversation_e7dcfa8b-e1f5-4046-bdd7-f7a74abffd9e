<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=windows-1252">
	<TITLE>CML format in Marvin</TITLE>
	<META NAME="GENERATOR" CONTENT="LibreOffice 3.4  (Win32)">
	<META NAME="AUTHOR" CONTENT="Peter Csizmadia">
	<META NAME="CREATED" CONTENT="0;0">
	<META NAME="CHANGED" CONTENT="20111110;11241943">
	<META NAME="DESCRIPTION" CONTENT="CML format in Marvin">
	<META NAME="KEYWORDS" CONTENT="CML, Java, Marvin">
	<STYLE TYPE="text/css">
	<!--
		TD P { color: #333333; font-family: "<PERSON><PERSON><PERSON>", "<PERSON>l", "Helvetica", sans-serif; font-size: 12pt; line-height: 150% }
		H1 { margin-top: 0.11cm; margin-bottom: 0.11cm; background: #e4f1f1; border: 1px solid #cae4e4; padding: 0.06cm; color: #077179; line-height: 150%; text-align: center }
		P { color: #333333; font-family: "Verdana", "Arial", "Helvetica", sans-serif; font-size: 12pt; line-height: 150% }
		PRE { color: #333333; font-family: monospace; font-size: 10pt; line-height: 150% }
		H2 { color: #077179; line-height: 150% }
		H2.cjk { font-family: "SimSun" }
		H2.ctl { font-family: "Mangal" }
		H4 { color: #077179; line-height: 150% }
		H4.cjk { font-family: "SimSun" }
		H4.ctl { font-family: "Mangal" }
		TH P { color: #333333; font-family: "Verdana", "Arial", "Helvetica", sans-serif; font-size: 12pt; line-height: 150% }
		A:link { color: #6b6f40 }
		CODE { font-family: monospace; font-size: 10pt }
	-->
	</STYLE>
</HEAD>
<BODY LANG="hu-HU" TEXT="#333333" LINK="#6b6f40" BGCOLOR="#ffffff" DIR="LTR">
<H1>CML</H1>
<P>Codename: <STRONG>cml</STRONG> 
</P>
<H2 CLASS="western">Contents</H2>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#cml">CML format</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#export">Export options</A>
		</P>
	<LI><P><A HREF="#references">References</A> 
	</P>
</UL>
<H2 CLASS="western"><A NAME="cml"></A>CML format</H2>
<P>Marvin is capable of importing and exporting files in the Chemical
Markup Language. Special atom types are lost during export. 
</P>
<P><EM>Import.</EM> The following CML2 standard tags are recognized: 
</P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><B>&lt;cml&gt;</B><BR>Children: 
	</P>
	<UL>
		<LI><P STYLE="margin-bottom: 0cm"><A HREF="#molecule"><B>&lt;molecule&gt;</B></A>
				</P>
		<LI><P STYLE="margin-bottom: 0cm"><A HREF="#reaction"><B>&lt;reaction&gt;</B></A>
				</P>
	</UL>
	<LI><P STYLE="margin-bottom: 0cm"><A NAME="molecule"></A><B>&lt;molecule&gt;</B><BR>Attributes:
		</P>
	<UL>
		<LI><P STYLE="margin-bottom: 0cm"><I>title</I> 
		</P>
	</UL>
	<P STYLE="margin-bottom: 0cm">Children: 
	</P>
	<UL>
		<LI><P STYLE="margin-bottom: 0cm"><A NAME="propertyList"></A><B>&lt;propertyList&gt;</B>
				</P>
		<UL>
			<LI><P STYLE="margin-bottom: 0cm"><A NAME="property"></A><B>&lt;property</B>
			dictRef=&quot;<I>name</I>&quot; title=&quot;<I>name</I>&quot;<B>&gt;</B>
			- A property.</P>
			<UL>
				<LI><P STYLE="margin-bottom: 0cm"><B>&lt;scalar</B>
				[dataType=&quot;<I>dataType</I>&quot;]<B>&gt;</B> <I>value</I><B>&lt;/scalar&gt;</B>
				- Supported data types: &quot;string&quot; (default), &quot;boolean&quot;,
				&quot;integer&quot;, &quot;double&quot;, &quot;ENTITY&quot; (for
				special property classes). 
				</P>
				<LI><P STYLE="margin-bottom: 0cm"><B>&lt;array</B>
				dataType=&quot;<I>dataType</I>&quot; [delimiter=&quot;<I>delimiter</I>&quot;]<B>&gt;</B><I>list
				of values</I><B>&lt;/array&gt;</B> - Currently, only the
				&quot;integer&quot; data type is supported. The default delimiter
				is the space character. 
				</P>
			</UL>
		</UL>
		<LI><P STYLE="margin-bottom: 0cm"><A HREF="#property"><B>&lt;property&gt;</B></A>
				</P>
		<LI><P STYLE="margin-bottom: 0cm"><B>&lt;atomArray&gt;</B><BR>Attributes:
				</P>
		<UL>
			<LI><P STYLE="margin-bottom: 0cm"><I>atomID</I>, <I>elementType</I>,
			<I>x2</I>, <I>y2</I>, <I>x3</I>, <I>y3</I>, <I>z3</I>,
			<I>formalCharge</I>, <I>hydrogenCount</I>, <I>isotope</I> 
			</P>
		</UL>
		<P STYLE="margin-bottom: 0cm">Children: 
		</P>
		<UL>
			<LI><P STYLE="margin-bottom: 0cm"><B>&lt;atom&gt;</B><BR>Attributes:
						</P>
			<UL>
				<LI><P STYLE="margin-bottom: 0cm"><I>id</I>, <I>elementType</I>,
				<I>x2</I>, <I>y2</I>, <I>x3</I>, <I>y3</I>, <I>z3</I>,
				<I>formalCharge</I>, <I>hydrogenCount</I>, <I>isotope</I> 
				</P>
			</UL>
		</UL>
		<LI><P STYLE="margin-bottom: 0cm"><B>&lt;bondArray&gt;</B><BR>Attributes:
				</P>
		<UL>
			<LI><P STYLE="margin-bottom: 0cm"><I>atomRefs1</I>, <I>atomRefs2</I>,
			<I>order</I> 
			</P>
		</UL>
		<P STYLE="margin-bottom: 0cm">Children: 
		</P>
		<UL>
			<LI><P STYLE="margin-bottom: 0cm"><B>&lt;bond</B> atomRefs2=&quot;<I>a1</I>
			<I>a2</I>&quot; order=&quot;<I>order</I>&quot; <B>&gt;</B><BR>The
			atom references <I>a1</I> and <I>a2</I> must be valid atom ids.
			The <I>order</I> value can be &quot;1&quot;, &quot;S&quot;
			(single), &quot;2&quot;, &quot;D&quot; (double), &quot;3&quot;,
			&quot;T&quot; (triple) or &quot;A&quot; (aromatic).<BR>Children: 
			</P>
			<UL>
				<LI><P STYLE="margin-bottom: 0cm"><B>&lt;bondStereo</B>&gt;<I>value</I><B>&lt;/bondStereo&gt;</B><BR>-
				Value can be &quot;W&quot; (wedge - up), &quot;H&quot; (hatch -
				down), &quot;C&quot; (cis) or &quot;T&quot; (trans) 
				</P>
				<LI><P STYLE="margin-bottom: 0cm"><A NAME="grid"></A><B>&lt;bondStereo</B>
				convention=&quot;<I>convention</I>&quot; conventionValue=&quot;<I>value</I>&quot;
				<B>/&gt;</B></P>
				<TABLE CELLPADDING=5 CELLSPACING=0>
					<TR>
						<TH STYLE="border: none; padding: 0cm">
							<P STYLE="border: none; padding: 0cm">convention</P>
						</TH>
						<TH STYLE="; border: none; padding: 0cm">
							<P STYLE="border: none; padding: 0cm">conventionValue</P>
						</TH>
						<TH STYLE="; border: none; padding: 0cm">
							<P STYLE="border: none; padding: 0cm">meaning</P>
						</TH>
					</TR>
					<TR>
						<TD STYLE="; border: none; padding: 0cm">
							<P STYLE="border: none; padding: 0cm">MDL</P>
						</TD>
						<TD STYLE="; border: none; padding: 0cm">
							<P STYLE="border: none; padding: 0cm">1</P>
						</TD>
						<TD STYLE="; border: none; padding: 0cm">
							<P STYLE="border: none; padding: 0cm">up</P>
						</TD>
					</TR>
					<TR>
						<TD STYLE="; border: none; padding: 0cm">
							<P STYLE="border: none; padding: 0cm">MDL</P>
						</TD>
						<TD STYLE="; border: none; padding: 0cm">
							<P STYLE="border: none; padding: 0cm">6</P>
						</TD>
						<TD STYLE="; border: none; padding: 0cm">
							<P STYLE="border: none; padding: 0cm">down</P>
						</TD>
					</TR>
					<TR>
						<TD STYLE="; border: none; padding: 0cm">
							<P STYLE="border: none; padding: 0cm">MDL</P>
						</TD>
						<TD STYLE="; border: none; padding: 0cm">
							<P STYLE="border: none; padding: 0cm">4</P>
						</TD>
						<TD STYLE="; border: none; padding: 0cm">
							<P STYLE="border: none; padding: 0cm">either</P>
						</TD>
					</TR>
					<TR>
						<TD STYLE="; border: none; padding: 0cm">
							<P STYLE="border: none; padding: 0cm">MDL</P>
						</TD>
						<TD STYLE="; border: none; padding: 0cm">
							<P STYLE="border: none; padding: 0cm">3</P>
						</TD>
						<TD STYLE="; border: none; padding: 0cm">
							<P STYLE="border: none; padding: 0cm">cis or trans</P>
						</TD>
					</TR>
					<TR>
						<TD STYLE="; border: none; padding: 0cm">
							<P STYLE="border: none; padding: 0cm">ChemAxon</P>
						</TD>
						<TD STYLE="; border: none; padding: 0cm">
							<P STYLE="border: none; padding: 0cm">CTUnspec</P>
						</TD>
						<TD STYLE="; border: none; padding: 0cm">
							<P STYLE="border: none; padding: 0cm">cis/trans or unspecified</P>
						</TD>
					</TR>
				</TABLE>
			</UL>
			<P STYLE="margin-bottom: 0cm">A &lt;bond&gt; tag is recognized at
			import even if the bondArray container is not present.</P>
		</UL>
	</UL>
	<LI><P STYLE="margin-bottom: 0cm"><A NAME="reaction"></A><B>&lt;reaction&gt;</B><BR>Attributes:
		</P>
	<UL>
		<LI><P STYLE="margin-bottom: 0cm"><I>title</I> 
		</P>
	</UL>
	<P STYLE="margin-bottom: 0cm">Children: 
	</P>
	<UL>
		<LI><P STYLE="margin-bottom: 0cm"><B>&lt;reactantList&gt;</B> 
		</P>
		<UL>
			<LI><P STYLE="margin-bottom: 0cm"><B>&lt;molecule&gt;</B> 
			</P>
		</UL>
		<LI><P STYLE="margin-bottom: 0cm"><B>&lt;productList&gt;</B> 
		</P>
		<UL>
			<LI><P STYLE="margin-bottom: 0cm"><B>&lt;molecule&gt;</B> 
			</P>
		</UL>
		<LI><P><A HREF="#propertyList"><B>&lt;propertyList&gt;</B></A> -
		Reaction properties 
		</P>
	</UL>
</UL>
<H2 CLASS="western"><A NAME="export"></A>Export options</H2>
<P>The argument of MolConverter, MolExporter and the <CODE>getMol</CODE>/<CODE>getM</CODE>
functions (of the applets and beans) is the format string. The format
specification (&quot;cml&quot;) is followed by &quot;:&quot; and the
selected option(s) for cml export.
</P>
<TABLE WIDTH=850 CELLPADDING=0 CELLSPACING=0>
	<COL WIDTH=103>
	<COL WIDTH=748>
	<TR VALIGN=TOP>
		<TD WIDTH=103 STYLE="border: none; padding: 0cm">
			<P STYLE="border: none; padding: 0cm">:...</P>
		</TD>
		<TD WIDTH=748 STYLE="; border: none; padding: 0cm">
			<P STYLE="border: none; padding: 0cm"><A HREF="basic-export-opts.html">Basic
			options for aromatization and H atom adding/removal.</A></P>
		</TD>
	</TR>
	<TR VALIGN=TOP>
		<TD WIDTH=103 STYLE="; border: none; padding: 0cm">
			<P STYLE="border: none; padding: 0cm">:A &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</P>
		</TD>
		<TD WIDTH=748 STYLE="; border: none; padding: 0cm">
			<P STYLE="border: none; padding: 0cm">Atom attributes are stored
			in arrays. For 2D molecules, only the x, y coordinates are stored.
			This is a more compact form of storage than the default (using
			<CODE>&lt;atom&gt;</CODE> tags). 
			</P>
		</TD><TR VALIGN=TOP>
		<TD WIDTH=103 STYLE="; border: none; padding: 0cm">
			<P STYLE="border: none; padding: 0cm">:P</P>
		</TD>
		<TD WIDTH=748 STYLE="; border: none; padding: 0cm">
			<P STYLE="border: none; padding: 0cm">Create human readable
			output: put new xml elements in new lines and indent for embedded
			elements.</P>
		</TD>
	</TR>
	</TR>
	<TR VALIGN=TOP>
		<TD WIDTH=103 STYLE="; border: none; padding: 0cm">
			<P STYLE="border: none; padding: 0cm">:C<i>N</i></P>
		</TD>
		<TD WIDTH=748 STYLE="; border: none; padding: 0cm">
			<P STYLE="border: none; padding: 0cm"> 
	          The accuracy of the exported coordinates can be given: 
	          <i>N</i> is the length of the decimals of the coordinate, 
	          0 &lt; <i>N</i> &le; 9
	 	    </P>
		</TD>
	</TR>
	<TR VALIGN=TOP>
		<TD WIDTH=103 STYLE="; border: none; padding: 0cm">
			<P STYLE="border: none; padding: 0cm">:D</P>
		</TD>
		<TD WIDTH=748 STYLE="; border: none; padding: 0cm">
			<P STYLE="border: none; padding: 0cm"> 
	         <P STYLE="border: none; padding: 0cm"> 
	         This option is important if the molecule has parity information and has 0 dimension. 
	         By default during the export, a clean method is invoked on the structure
	         and the generated coordinates and wedge information are exported into CML format 
	         but <i>NOT</i> the parity information. However, using this option coordinates and wedge information
	         are not generated but parity information is exported.<br>
	         <font color="red">
	          Attention: When a cml file containing parity information is imported to 
	          Marvin older than 5.8, the parity information will be displayed wrongly!
	         </font>
	 	    </P>
		</TD>
	</TR>
	<TR VALIGN=TOP>
		<TD WIDTH=103 STYLE="; border: none; padding: 0cm">
			<P STYLE="border: none; padding: 0cm">:I</P>
		</TD>
		<TD WIDTH=748 STYLE="; border: none; padding: 0cm">
			<P STYLE="border: none; padding: 0cm"> 
	         <P STYLE="border: none; padding: 0cm"> 
	         Ignore unexportable molecule properties. Without this option the exporter will throw an exception
	         when reach an unexportable property.
	         </font>
	 	    </P>
		</TD>
	</TR>	
	<TR VALIGN=TOP>
		<TD WIDTH=103 STYLE="; border: none; padding: 0cm">
			<P STYLE="border: none; padding: 0cm">:BOM</P>
		</TD>
		<TD WIDTH=748 STYLE="; border: none; padding: 0cm">
			<P STYLE="border: none; padding: 0cm"> 
	         <P STYLE="border: none; padding: 0cm"> 
		     Write the UTF-8 <i>byte order mark</i> (BOM), if the given or the system's encoding is UTF-8.
	         </font>
	 	    </P>
		</TD>
	</TR>	
</TABLE>
<p>For example: cml:A or cml:C5.</p>
<P>Example of CML file exported without options: 
</P>
<PRE STYLE="margin-left: 1cm; margin-right: 1cm">&lt;?xml version=&quot;1.0&quot;?&gt;&lt;cml xmlns=&quot;http://www.xml-cml.org/schema&quot; xmlns:convention=&quot;http://www.xml-cml.org/convention&quot; convention=&quot;convention:molecular&quot; xmlns:marvin=&quot;http://www.chemaxon.com/marvin/marvinDictRef&quot;&gt;
&lt;molecule title=&quot;Ethane&quot; id=&quot;m1&quot;&gt;&lt;atomArray&gt;&lt;atom id=&quot;a1&quot; elementType=&quot;C&quot; x2=&quot;0.0&quot; y2=&quot;0.0&quot;&gt;&lt;/atom&gt;&lt;atom id=&quot;a2&quot; elementType=&quot;C&quot; x2=&quot;0.0&quot; y2=&quot;1.54&quot;&gt;&lt;/atom&gt;&lt;/atomArray&gt;&lt;bondArray&gt;&lt;bond atomRefs2=&quot;a1 a2&quot; order=&quot;1&quot;&gt;&lt;/bond&gt;&lt;/bondArray&gt;&lt;/molecule&gt;
&lt;/cml&gt;
</PRE><P>
Example of CML file exported with option &quot;A&quot;: 
</P>
<PRE STYLE="margin-left: 1cm; margin-right: 1cm">&lt;?xml version=&quot;1.0&quot;?&gt;&lt;cml xmlns=&quot;http://www.xml-cml.org/schema&quot; xmlns:convention=&quot;http://www.xml-cml.org/convention&quot; convention=&quot;convention:molecular&quot; xmlns:marvin=&quot;http://www.chemaxon.com/marvin/marvinDictRef&quot;&gt;
&lt;molecule title=&quot;Ethane&quot; id=&quot;m1&quot;&gt;&lt;atomArray atomID=&quot;a1 a2&quot; elementType=&quot;C C&quot; x2=&quot;0.0 0.0&quot; y2=&quot;0.0 1.54&quot;&gt;&lt;/atomArray&gt;&lt;bondArray&gt;&lt;bond atomRefs2=&quot;a1 a2&quot; order=&quot;1&quot;&gt;&lt;/bond&gt;&lt;/bondArray&gt;&lt;/molecule&gt;
&lt;/cml&gt;
</PRE><P>
Example of CML file exported with option &quot;P&quot;: 
</P>
<PRE STYLE="margin-left: 1cm; margin-right: 1cm">&lt;?xml version=&quot;1.0&quot;?&gt;
&lt;cml xmlns=&quot;http://www.xml-cml.org/schema&quot; xmlns:convention=&quot;http://www.xml-cml.org/convention&quot; convention=&quot;convention:molecular&quot; xmlns:marvin=&quot;http://www.chemaxon.com/marvin/marvinDictRef&quot;&gt;
&lt;molecule title=&quot;Ethane&quot; id=&quot;m1&quot;&gt;
  &lt;atomArray&gt;
    &lt;atom id=&quot;a1&quot; elementType=&quot;C&quot; x2=&quot;0.0&quot; y2=&quot;0.0&quot;/&gt;
    &lt;atom id=&quot;a2&quot; elementType=&quot;C&quot; x2=&quot;0.0&quot; y2=&quot;1.54&quot;/&gt;
  &lt;/atomArray&gt;
  &lt;bondArray&gt;
    &lt;bond atomRefs2=&quot;a1 a2&quot; order=&quot;1&quot;/&gt;
  &lt;/bondArray&gt;
&lt;/molecule&gt;
&lt;/cml&gt;
</PRE><P>
Example of CML file exported with options &quot;AP&quot;: 
</P>
<PRE STYLE="margin-left: 1cm; margin-right: 1cm">&lt;?xml version=&quot;1.0&quot;?&gt;
&lt;cml xmlns=&quot;http://www.xml-cml.org/schema&quot; xmlns:convention=&quot;http://www.xml-cml.org/convention&quot; convention=&quot;convention:molecular&quot; xmlns:marvin=&quot;http://www.chemaxon.com/marvin/marvinDictRef&quot;&gt;
&lt;molecule title=&quot;Ethane&quot; id=&quot;m1&quot;&gt;
  &lt;atomArray atomID=&quot;a1 a2&quot; elementType=&quot;C C&quot; x2=&quot;0.0 0.0&quot; y2=&quot;0.0 1.54&quot;/&gt;
  &lt;bondArray&gt;
    &lt;bond atomRefs2=&quot;a1 a2&quot; order=&quot;1&quot;/&gt;
  &lt;/bondArray&gt;
&lt;/molecule&gt;
&lt;/cml&gt;
</PRE><H4 CLASS="western">
<A NAME="others"></A>CML in other programs:</H4>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><EM>JChemPaint</EM> (last version
	tested: 1.9.8) and Marvin imports each other's CML files. 
	</P>
	<LI><P><EM>Jmol</EM> 8 imports CML files exported by Marvin that
	contain 3D structures. Jmol is a 3D viewer that does not import
	bonds but calculates them from atomic distances. This means that
	bonds are not imported correctly if the molecule is 2D. 
	</P>
</UL>
<H2 CLASS="western"><A NAME="references"></A>References</H2>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="http://cml.sourceforge.net/" TARGET="_top">cml.sourceforge.net
	&mdash; OpenSource Site for CML</A> 
	</P>
	<LI><P><A HREF="http://cml.sourceforge.net/schema/">cml.sourceforge.net/schema/
	&mdash; Schemas and documentation</A> 
	</P>
</UL>
</BODY>
</HTML>
