<html>
<head>
<link REL ="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css" TITLE="Style">
<title>SVG export in Marvin</title>
</head>
<body>

<h1>Scalable Vector Graphics</h1>
<p>
Codename: <strong>svg</strong>

<p>
The Marvin beans and applications are able to export SVG
<a HREF="images-doc.html">image files</a>. This function does not work in
the applets (except signed applets).

<p>
The <a HREF="../developer/modules.html#JpegExport">SVG Import/Export</a> module recognizes
the <a HREF="images-doc.html#options">common image export options</a>, and the
following special option:
<blockquote>
<table CELLSPACING="0" CELLPADDING="5" border="0">
<tr><td><code>nosource</code></td>
    <td>Omits saving of molecule source into image as comment.</td></tr>
    <tr><td><code>headless</code></td>
    <td>Generates svg without the xml headers. This output can be embed in HTML documents.</td></tr>
</table>
</blockquote>
<p>

<h2>References</h2>
<ul>
<li><a HREF="http://www.w3.org/TR/SVG/" TARGET="_top">Scalable Vector Graphics (SVG) Specification (http://www.w3.org/TR/SVG/)</a></li>
<li><a HREF="http://xml.apache.org/batik/" TARGET="_top">Apache Batik SVG Toolkit (http://xml.apache.org/batik/)</a></li>
</ul>

<p ALIGN=LEFT>
<small>SvgExport uses the <em>Apache Batik SVG Toolkit</em>,
<a HREF="../license/LICENSE.batik.txt">Copyright &copy;
The Apache Software Foundation</a>
</small>
</p>

</body>
</html>
