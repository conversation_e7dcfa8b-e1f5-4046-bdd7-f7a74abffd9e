<!-- 
    Document   : cdx-doc
    Created on : Nov 20, 2008, 1:27:32 PM
    Author     : <PERSON><PERSON><PERSON>
    Edited by  : <PERSON>
-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta NAME="description" CONTENT="CDX file format in Marvin">
    <meta NAME="keywords" CONTENT="CDX, file format, Java, Marvin">
    <link REL ="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css" TITLE="Style">
    <title>CDX format</title>
  </head>
  <body>
    
    <h1>CDX</h1>
    <p>
      Codename: <strong>cdx, cdxml</strong>
    </p>
    
    <p>The cdx format of CambridgeSoft's ChemDraw is imported and exported by
        <PERSON>. The import of cdxml format is supported, but export isn't.
    
    <h2>Import</h2>
    <p>The following node types are imported:
    <ul>
      <li>Element</li>
      <li>Element List</li>
      <li>Nickname</li>
      <li>Fragment</li>
      <li>Generic Label</li>
      <li>Alternative Group</li>
      <li>Attachment Point</li>
      <li>Link Node</li>
      <li>Variable Attachment</li>
      <li>Multi Attachment</li>
      <li>Anonymous Alternative Group</li>
    </ul>
    
    <p>We do not support these types at the moment:
      <ul>
        <li>ElementListNickname</li>
      </ul>
      
      <h3>Detailed import features</h3> 
      
      <dl>	
        <dt>Element</dt>
        <dd>The 'Element' type node is read as a single atom. For the supported 
        properties see the supported Atom properties list.</dd>
        
        <dt>Element List</dt>
        <dd>Element Lists are read as Marvin's Atom List (or not list).  For the supported
        properties see the supported node properties list.</dd>
        
        <dt>Label</dt>
        <dd>Labels are converted to an S-group and appear contracted in the sketch.</dd>
        
        <dt>Generic Label</dt>
        <dd>Generic Labels are read as Generic query atoms. <a href="cdx_import.html#generic">See details.</a></dd>
        
        <dt>Atom properties</dt>
        <dd>Following properties are supported: charge, isotope, substituents, free sites, 
        unsaturation, reaction stereo, enhanced stereochemistry, radical, ring bond count. 
        <a href="cdx_import.html#atoms">See details.</a></dd>
        
        <dt>Attachment Points</dt>
        <dd>Attachment Point is imported as atom property, unlimited number.</dd>
        
        <dt>Bond types</dt>
        <dd>Single bonds, double bonds, query bonds, topology, reaction centers are imported.
        <a href="cdx_import.html#bonds">See details.</a></dd>
        
        <dt>Reaction arrows</dt>
        <dd>All types of arrows are imported, but only one arrow per file. <a href="cdx_import.html#reaction">See details.</a></dd>
        
        <dt>Bracketed groups</dt>
        <dd>All groups are imported. <a href="cdx_import.html#groups">See details.</a></dd>
        
        <dt>Alternative Group</dt>
        <dd>Alternative Groups are read as R-groups, up to two connections per R-group.</dd>
        
        <dt>Anonymous Alternative Group</dt>
        <dd>An Anonymous Alternative Group is imported as R-group and is assigned the
        R-group number n+1 where n is the largest R-group number in the file.</dd>
        
        <dt>Link Node</dt>
        <dd>Link Node is read as Link Node.</dd>
        
        <dt>Variable Attachment and Multi-Center Attachment</dt>
        <dd>These are imported as Multicenter S-groups.</dd>
        
        <dt>Text Box</dt>
        <dd>Marvin reads the position and the formatted text.</dd>
        
        <dt>Basic Graphic Objects</dt>
        <dd>Ellipses, rectangles and some of the symbols are read. <a href="cdx_import.html#symbols">See details.</a></dd>

        <dt>Graphic Objects</dt>
        <dd>Tables and TLC plate drawings are imported as graphics. <a href="cdx_import.html#graphics">See details.</a></dd>
      </dl>

    <h4>Notes</h4>
    <ul><li>ElectronFlow is not supported.</li>
    <li>Only ASCII characters are imported.</li>
    <li>Complex graphical objects (e.g. laboratory equipment drawings, biological drawings)
    are not imported.</li>
    <li>R-logic import is supported, export not yet.
    </ul>

    <h2>Export</h2>

    The following list contains the exported features:
    <ul><li>Chemical structures</li>
    <li>Formatted text </li>
    <li>R-groups</li>
    <li>S-groups <a href="cdx_export.html#groups">See details.</a></li>
    <li>Atom and bond query properties <a href="cdx_export.html#query">See details.</a></li>
    <li>Rectangles, rounded rectangles and ellipses</li>
    <li>Reactions <a href="cdx_export.html#reaction">See details.</a></li>
    </ul>

  </body>
</html>
