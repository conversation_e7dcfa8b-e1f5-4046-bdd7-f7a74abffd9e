<html xmlns:xs="http://www.w3.org/2001/XMLSchema">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta NAME="description" CONTENT="mrv file format">
<meta NAME="keywords" CONTENT="mrv, schema, marvin, file format">
<meta NAME="author" CONTENT="<PERSON>">
<link REL="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css" TITLE="Style">
<title>The mrv file format</title>
</head>
<body>
<h1> The mrv file format </h1>
<h2> Contents</h2>
<ul>
<li><a href="#format">Marvin Document Format </a></li>
<li><a href="#escape">Escape Characters </a></li>
<li><a href="#schema">Schema </a></li>
<li><a href="#old">Old mrv format documentation</a></li>
<li><a href="#export">Export Options </a></li>
</ul>
<h2>
<a name="format"></a> Marvin Document Format </h2>
    An mrv file may contain four elements,
    <a href="#cml">cml</a>, 
    <a href="#MDocument">MDocument</a>, 
    <a href="#molecule">molecule</a>, 
    <a href="#reaction">reaction</a>,
    all of which has complex type.
    The elements of the complex types are shown as list items,
    while its attributes are written with italic under the name of
    the corresponding complex type name.
    <ul>
<h3>
<a name="cml"></a> 
        Element: cml</h3>
<table><tr>
<td><i>version</i></td>
<td>
					Version information (file format version number and
					generation version number).
				</td>
</tr></table>
<ul><li>MHead:
            
						Header containing global properties.
					<table></table>
<ul><li>MarvinGUI:
            
			          Description of Marvin GUI properties
			      <table></table>
<ul><li>mprop:
            
			          Marvin GUI property type. 
			       <table>
<tr>
<td><i>dataType</i></td>
<td>
		         Type name of marvin GUI dat
		      </td>
</tr>
<tr>
<td><i>name</i></td>
<td>
		        Name tag of the marvin GUI properties.
		      </td>
</tr>
<tr>
<td><i>value</i></td>
<td>
		        Value of Marvin GUI property
		     </td>
</tr>
</table>
</li></ul>
</li></ul>
</li></ul>
<ul><li>
<a href="#MDocument">MDocument</a>:
            
							Main marvin document element containing
							molecules, graphical objects, reactions,
							etc.
						</li></ul>
<ul><li>
<a href="#molecule">molecule</a>:
            
							Element containing the description of a
							molecule.
						</li></ul>
<ul><li>
<a href="#reaction">reaction</a>:
            
							Element containing the description of a
							reaction.
						</li></ul>
<h3>
<a name="MDocument"></a> 
        Element: MDocument</h3>
<table>
<tr>
<td><i>atomSetFont</i></td>
<td>
					Font type of atom sets.
				</td>
</tr>
<tr>
<td><i>atomSetRGB</i></td>
<td>
					Atom set colors:
	Comma separated list of entries in
					"
					k:color
					" format, where
					k
					is the set sequence number and
					color
					is the color specification
					(in one of the following forms:
					"#RRGGBB" - RGB components
					as a 6-digit hexadecimal
					number, "D" - default set color,
					"N" - no set
					color (normal atom/bond coloring is used).
				</td>
</tr>
<tr>
<td><i>bondSetLineThickness</i></td>
<td>
					Thickness of bonds in the bond sets.
				</td>
</tr>
<tr>
<td><i>bondSetRGB</i></td>
<td>
					Bond set colors:
	Comma separated list of entries in
					"
					k:color
					" format, where
					k
					is the set sequence number and
					color
					is the color specification
					(in one of the following forms:
					"#RRGGBB" - RGB components
					as a 6-digit hexadecimal
					number, "D" - default set color,
					"N" - no set
					color (normal atom/bond coloring is used).
				</td>
</tr>
<tr>
<td><i>extraLabelSetRGB</i></td>
<td>
					Color setting of the extra label.
				</td>
</tr>
<tr>
<td><i>multipageBottom</i></td>
<td>
					Bottom margin of a page in multipage molecular
					document. Its value is "d" where d is a floating point number.
				</td>
</tr>
<tr>
<td><i>multipageColumnCount</i></td>
<td>
					Number of columns in multipage molecular document.
					Its value is "k" where
					k is a positive integer.
				</td>
</tr>
<tr>
<td><i>multipageEnabled</i></td>
<td>
					Enables the multipage molecular document. Its
					value is "true" or "false".
				</td>
</tr>
<tr>
<td><i>multipageHeight</i></td>
<td>
					Height of a page in multipage molecular document.
					Its value is "d" where
					d is a floating point number.
				</td>
</tr>
<tr>
<td><i>multipageLeft</i></td>
<td>
					Left margin of a page in multipage molecular
					document. Its value is "d"
					where d is a floating point number.
				</td>
</tr>
<tr>
<td><i>multipageRight</i></td>
<td>
					Right margin of a page in multipage molecular
					document. Its value is "d" where
					d is a floating point number.
				</td>
</tr>
<tr>
<td><i>multipageRowCount</i></td>
<td>
					Number of rows in multipage molecular document.
					Its value is "k" where k
					is a positive integer.
				</td>
</tr>
<tr>
<td><i>multipageSelectedPage</i></td>
<td>
					The selected page in multipage molecular document.
					Its value is "k"
					where k is a positive integer.
				</td>
</tr>
<tr>
<td><i>multipageTop</i></td>
<td>
					Top margin of a page in multipage molecular
					document. Its value is "d"
					where d is a floating point number.
				</td>
</tr>
<tr>
<td><i>multipageWidth</i></td>
<td>
					Width of a page in multipage molecular document.
					Its value is "d" where
					d is a floating point number.
				</td>
</tr>
</table>
<ul><li>propertyList:
            
					 List containing Marvin GUI attributes.
				<table></table>
<ul><li>property:
            
				        Marvin document properties.
					<table>
<tr>
<td><i>dictRef</i></td>
<td>
						Key of the properties related to the document.
					</td>
</tr>
<tr>
<td><i>title</i></td>
<td>
						Title of a Marvin document.
					</td>
</tr>
</table>
<ul><li>scalar:
            
						Scalar Marvin document property: it contains a
						single data value.
					<table><tr>
<td><i>dataType</i></td>
<td>
				        Data type of the scalar tag value.
					</td>
</tr></table>
</li></ul>
<ul><li>array:
            
						Array Marvin document property: it contains an
						array of double values.
					<table>
<tr>
<td><i>dataType</i></td>
<td>
						Data type of the array tag values.
					</td>
</tr>
<tr>
<td><i>size</i></td>
<td>
						Size of the array.
					</td>
</tr>
</table>
</li></ul>
</li></ul>
</li></ul>
<ul><li>MChemicalStruct:
            
						Description of a chemical Structure.
				<table></table>
<ul><li>
<a href="#molecule">molecule</a>:
            
								Element containing the description of a
								molecule.
					</li></ul>
<ul><li>
<a href="#reaction">reaction</a>:
            
								Element containing the description of a
								reaction.
						</li></ul>
<ul><li>Rgroup:
            
							Element containing the description of an
							R-group.
						<table>
<tr>
<td><i>restH</i></td>
<td>
					It is a condition for an R-group: If it is set for
					an R-group, the
					hit molecules do not contain ligands on that atom
					other than
					hydrogen or those specified as R-group. If RestH
					condition is
					false, then R-group sites can contain any additional
					non-hydrogen
					ligands as well.
			</td>
</tr>
<tr>
<td><i>rgroupID</i></td>
<td>
				ID of the R-group.
			</td>
</tr>
<tr>
<td><i>rlogicRange</i></td>
<td>
					It shows that the given R-group how many times can
					occur in the
					target. (For example: 1-3,6 means the R-group can occur
					one, two
					three or six time in the target.).
			</td>
</tr>
<tr>
<td><i>thenR</i></td>
<td>
					It is a condition for an R-group:R-group has to be
					satisfied only
					when conditions of another R-group are satisfied.
			</td>
</tr>
</table>
<ul><li>
<a href="#molecule">molecule</a>:
            
						Element containing the description of a molecule.
					</li></ul>
</li></ul>
</li></ul>
<ul><li>MMoleculeMovie:
            
					Animation of a chemical process.
				<table></table>
<ul><li>MChemicalStruct:
            
						Element containing the description of a chemical
						structure.
				<table></table>
<ul><li>
<a href="#molecule">molecule</a>:
            
								Element containing the description of a
								molecule.
					</li></ul>
<ul><li>
<a href="#reaction">reaction</a>:
            
								Element containing the description of a
								reaction.
						</li></ul>
<ul><li>Rgroup:
            
							Element containing the description of an
							R-group.
						<table>
<tr>
<td><i>restH</i></td>
<td>
					It is a condition for an R-group: If it is set for
					an R-group, the
					hit molecules do not contain ligands on that atom
					other than
					hydrogen or those specified as R-group. If RestH
					condition is
					false, then R-group sites can contain any additional
					non-hydrogen
					ligands as well.
			</td>
</tr>
<tr>
<td><i>rgroupID</i></td>
<td>
				ID of the R-group.
			</td>
</tr>
<tr>
<td><i>rlogicRange</i></td>
<td>
					It shows that the given R-group how many times can
					occur in the
					target. (For example: 1-3,6 means the R-group can occur
					one, two
					three or six time in the target.).
			</td>
</tr>
<tr>
<td><i>thenR</i></td>
<td>
					It is a condition for an R-group:R-group has to be
					satisfied only
					when conditions of another R-group are satisfied.
			</td>
</tr>
</table>
<ul><li>
<a href="#molecule">molecule</a>:
            
						Element containing the description of a molecule.
					</li></ul>
</li></ul>
</li></ul>
</li></ul>
<ul><li>MBracket:
            
					Bracket graphical object.
				<table>
<tr>
<td><i>orientation</i></td>
<td>
							Determines whether bracket is in pairs or a
							standalone
							bracket. "Single" means that the bracket can be moved
							or
							resized alone, "double" means that the pair of brackets
							are
							treated as one entity, can be moved and resiyed only
							together.
					</td>
</tr>
<tr>
<td><i>tcenter</i></td>
<td>
							Geometric center of the object for
							transformations like
							rotation. Identifies corners and
							middle-points with
							points of the compass, e.g. NW (North West)
							identifies
							the top right corner. Possible values are the eight
							points of the compass or center.
					</td>
</tr>
<tr>
<td><i>toption</i></td>
<td>
				It shows that the object can be rotated or not.
					</td>
</tr>
<tr>
<td><i>type</i></td>
<td>
				Type of the bracket.
						</td>
</tr>
</table>
<ul><li>MPoint:
            <table>
<tr>
<td><i>x</i></td>
<td>
				X coordinate.
						</td>
</tr>
<tr>
<td><i>y</i></td>
<td>
				Y coordinate.
						</td>
</tr>
<tr>
<td><i>z</i></td>
<td>
				Z coordinate.
						</td>
</tr>
</table>
</li></ul>
</li></ul>
<ul><li>MEFlow:
            
						Curved electron flow arrow. MEFlow is a subclass
						of MPolyline thus it
						has
						the same attributes, but it can only
						contain two points.
				<table><tr>
<td><i>arcAngle</i></td>
<td>
				Angle of the electron flow arrow arc.
						</td>
</tr></table>
<ul><li>MEFlowBasePoint:
            
									Starting point of the electron flow arrow if
									the source is an atom.
								<table><tr>
<td><i>atomRef</i></td>
<td></td>
</tr></table>
</li></ul>
<ul><li>MAtomSetPoint:
            
							It represents an atom or atom pair
									(bond or
									incipient bond).
							<table>
<tr>
<td><i>atomRefs</i></td>
<td></td>
</tr>
<tr>
<td><i>weights</i></td>
<td></td>
</tr>
</table>
</li></ul>
<ul><li>MAtomSetPoint:
            
				It represents an atom or atom pair
								(bond or
								incipient bond).
							<table>
<tr>
<td><i>atomRefs</i></td>
<td></td>
</tr>
<tr>
<td><i>weights</i></td>
<td></td>
</tr>
</table>
</li></ul>
</li></ul>
<ul><li>MEllipse:
            
					Ellipse graphical object,
						it is derived from
						MRectangle, inheriting its attributes.
				<table>
<tr>
<td><i>background</i></td>
<td>
				Background color.
						</td>
</tr>
<tr>
<td><i>tcenter</i></td>
<td>
							Geometric center of the object for
							transformations like
							rotation. Identifies corners and
							middle-points with
							points of the compass, e.g. NW (North West)
							identifies
							the top right corner. Possible values are the eight
							points of the compass or center.
						</td>
</tr>
<tr>
<td><i>toption</i></td>
<td>
				It shows that the object can be rotated or not.
						</td>
</tr>
</table>
<ul><li>MPoint:
            
								Basic point of a polyline object.
						<table>
<tr>
<td><i>x</i></td>
<td>
				X coordinate.
						</td>
</tr>
<tr>
<td><i>y</i></td>
<td>
				Y coordinate.
						</td>
</tr>
<tr>
<td><i>z</i></td>
<td>
				Z coordinate.
						</td>
</tr>
</table>
</li></ul>
</li></ul>
<ul><li>MPolyline:
            
					Line, arc, polyline and/or graphical arrow.
				<table><tr>
<td><i>arcAngle</i></td>
<td>
				Angle of arc.
						</td>
</tr></table>
<ul><li>MPoint:
            
								Basic Point of a polyline object.
							<table>
<tr>
<td><i>x</i></td>
<td>
				X coordinate.
						</td>
</tr>
<tr>
<td><i>y</i></td>
<td>
				Y coordinate.
						</td>
</tr>
<tr>
<td><i>z</i></td>
<td>
				Z coordinate.
						</td>
</tr>
</table>
</li></ul>
<ul><li>MRectanglePoint:
            
								Point of an object connected to a point
								of another graphical object which is a
								polyline.
							<table>
<tr>
<td><i>pos</i></td>
<td>
					Identifies to which point of a rectangle this
					point is
					connected.
						</td>
</tr>
<tr>
<td><i>rectRef</i></td>
<td>
					Reference name of the rectangle to which this
					point of the
					object is connected.
						</td>
</tr>
</table>
</li></ul>
<ul><li>MMidPoint:
            
								Point of an object connected to a middle
								point of another graphical object which
								is a polyline.
							<table>
<tr>
<td><i>pos</i></td>
<td>
					Identifies to which point of a polyline this point
					is
					connected.
						</td>
</tr>
<tr>
<td><i>lineRef</i></td>
<td>
					Reference name of the line to which this point of
					the
					object is connected.
						</td>
</tr>
</table>
</li></ul>
<ul><li>MAtomSetPoint:
            
								It represents an atom or atom pair (bond
								or incipient bond).
							<table>
<tr>
<td><i>atomRefs</i></td>
<td></td>
</tr>
<tr>
<td><i>weights</i></td>
<td></td>
</tr>
</table>
</li></ul>
</li></ul>
<ul><li>MRectangle:
            
					Rectangle graphical Object.
				<table>
<tr>
<td><i>background</i></td>
<td>
				Background color.
						</td>
</tr>
<tr>
<td><i>tcenter</i></td>
<td>
							Geometric center of the object for
							transformations like
							rotation. Identifies corners and
							middle-points with
							points of the compass, e.g. NW (North West)
							identifies
							the top right corner. Possible values are the eight
							points of the compass or center.
						</td>
</tr>
<tr>
<td><i>toption</i></td>
<td>
				It shows that the object can be rotated or not.
						</td>
</tr>
</table>
<ul><li>MPoint:
            
								Basic point of a polyline object.
						<table>
<tr>
<td><i>x</i></td>
<td>
				X coordinate.
						</td>
</tr>
<tr>
<td><i>y</i></td>
<td>
				Y coordinate.
						</td>
</tr>
<tr>
<td><i>z</i></td>
<td>
				Z coordinate.
						</td>
</tr>
</table>
</li></ul>
</li></ul>
<ul><li>MRoundedRectangle:
            
					Rounded cornered rectangle graphical object.
				<table>
<tr>
<td><i>arcHeight</i></td>
<td>
				The height of the arc of the rounded corner.
					</td>
</tr>
<tr>
<td><i>arcWidth</i></td>
<td>
				The width of the arc of the rounded corner.
					</td>
</tr>
</table>
</li></ul>
<ul><li>MTextBox:
            
					Text box.
				<table>
<tr>
<td><i>autoSize</i></td>
<td>
							It aranges the text box size to the size
							automatically.
						</td>
</tr>
<tr>
<td><i>background</i></td>
<td>
				Background color.
						</td>
</tr>
<tr>
<td><i>color</i></td>
<td>
				Color.
						</td>
</tr>
<tr>
<td><i>fontScale</i></td>
<td>
				The size of the font.
						</td>
</tr>
<tr>
<td><i>halign</i></td>
<td>
							Horizontal alignment of the text box.
						</td>
</tr>
<tr>
<td><i>tcenter</i></td>
<td>
							Geometric center of the object for
							transformations like
							rotation. Identifies corners and
							middle-points with
							points of the compass, e.g. NW (North West)
							identifies
							the top right corner. Possible values are the eight
							points of the compass or center.
						</td>
</tr>
<tr>
<td><i>toption</i></td>
<td>
				It shows that the object can be rotated or not.
						</td>
</tr>
<tr>
<td><i>valign</i></td>
<td>
							Vertical alignment of the text Box.
						</td>
</tr>
</table>
<ul><li>Field:
            
								Element containing the text of the text box.
						<table><tr>
<td><i>name</i></td>
<td>
							Name of the text field, usually has value
							"text".
						</td>
</tr></table>
</li></ul>
<ul><li>MPoint:
            
				Basic Point of a polyline object.
						<table>
<tr>
<td><i>x</i></td>
<td>
				X coordinate.
						</td>
</tr>
<tr>
<td><i>y</i></td>
<td>
				Y coordinate.
						</td>
</tr>
<tr>
<td><i>z</i></td>
<td>
				Z coordinate.
						</td>
</tr>
</table>
</li></ul>
</li></ul>
<ul><li>MNameTextBox:
            
					Text object that contains molecule name;
						it is
						derived from MTextBox, thus it inherits its attributes.
				<table>
<tr>
<td><i>autoAlign</i></td>
<td>
							It shows that auto alignment function of the
							textbox is on or off.
					</td>
</tr>
<tr>
<td><i>autoResize</i></td>
<td>
							It shows that auto resize function of the
							textbox is on or off.
					</td>
</tr>
<tr>
<td><i>preferredWidth</i></td>
<td>
					The prefered width of the name text box.
					</td>
</tr>
</table>
</li></ul>
<h3>
<a name="molecule"></a> 
        Element: molecule</h3>
<table>
<tr>
<td><i>absStereo</i></td>
<td>
					It shows that absolute stereo label is on or off.
					</td>
</tr>
<tr>
<td><i>id</i></td>
<td>
					ID of the molecule in CML format.
										</td>
</tr>
<tr>
<td><i>molID</i></td>
<td>
					ID number of the molecule in the MRV file.
					</td>
</tr>
<tr>
<td><i>title</i></td>
<td>
					Title of the molecule.
					</td>
</tr>
</table>
<ul><li>name:
            
					Name of the molecule. 
					<table></table>
</li></ul>
<ul><li>propertyList:
            
					Property list of the molecule.
					<table></table>
<ul><li>property:
            
						Molecule property type.
					<table>
<tr>
<td><i>dictRef</i></td>
<td>
					Key of the properties related to the document.
					</td>
</tr>
<tr>
<td><i>title</i></td>
<td></td>
</tr>
</table>
<ul><li>scalar:
            
						Scalar type molecular property.
					<table><tr>
<td><i>dataType</i></td>
<td>
							Scalar data types
					</td>
</tr></table>
</li></ul>
<ul><li>array:
            
						Array type molecular property.
					<table><tr>
<td><i>delimiter</i></td>
<td>
							Delimiter string of molecule array properties.
					</td>
</tr></table>
</li></ul>
</li></ul>
</li></ul>
<ul><li>atomArray:
            
						Array of atoms according to their label.
					<table>
<tr>
<td><i>atomID</i></td>
<td>
					This argument is a space separated list of atoms.
				</td>
</tr>
<tr>
<td><i>atomType</i></td>
<td>
					List marking if atom is MolAtom or LeavingGroupAtom.
					Values: "a" atom; "l" leaving group atom.  
				</td>
</tr>
<tr>
<td><i>attachmentOrder</i></td>
<td>
					Attachment point order value in the case of R-group
					attachment point.
				</td>
</tr>
<tr>
<td><i>attachmentPoint</i></td>
<td>
					List of attachment points.
				</td>
</tr>
<tr>
<td><i>elementType</i></td>
<td>
					Element in the Periodic Table.
				</td>
</tr>
<tr>
<td><i>formalCharge</i></td>
<td>
					It is the charge assigned to an atom in a molecule,
					assuming that electrons in a chemical bond are
					shared equally between atoms, regardless of relative
					electronegativity.
				</td>
</tr>
<tr>
<td><i>hydrogenCount</i></td>
<td>
					Number of implicit hydrogen if needed. In the cases
					of 5-6 membered aromatic rings containing nitrogen
					atoms, it is not obvious which N atoms have implicit
					hydrogens.
				</td>
</tr>
<tr>
<td><i>isSelected</i></td>
<td>
					Boolean type variable which shows that the object is
					selected or not.
				</td>
</tr>
<tr>
<td><i>isotope</i></td>
<td>Atomic mass number.</td>
</tr>
<tr>
<td><i>lonePair</i></td>
<td>
					Number of lone pairs.
				</td>
</tr>
<tr>
<td><i>mrvAlias</i></td>
<td>Atom alias.</td>
</tr>
<tr>
<td><i>mrvExtraLabel</i></td>
<td>
					List of Atom extra labels.
				</td>
</tr>
<tr>
<td><i>mrvLinkNodeOut</i></td>
<td>
					Outer bond references for a link node in comma
					separated list of bond indices (amongst bonds of the
					link atom) leading to the outer atoms (non-repeating
					neighbours) of the link nodes, "-" means no outer
					bonds.
				</td>
</tr>
<tr>
<td><i>mrvLinkNodeRep</i></td>
<td>
					Number of repetitions for a link node in format
					"
					n
					" (maximum number of repetitions) or "
					m
					-
					n
					" (minimum and maximum).
				</td>
</tr>
<tr>
<td><i>mrvMap</i></td>
<td>
					The map corresponding to the given atom
					
						mapping
					
					.
				</td>
</tr>
<tr>
<td><i>mrvPseudo</i></td>
<td>
					List of pseudoatom names.
				</td>
</tr>
<tr>
<td><i>mrvQueryProps</i></td>
<td>
					List of query atom properties
				</td>
</tr>
<tr>
<td><i>mrvSetExtraLabelSeq</i></td>
<td>
					Atom set extra label numbers.
				</td>
</tr>
<tr>
<td><i>mrvSetSeq</i></td>
<td>
					Atom set sequence number.
				</td>
</tr>
<tr>
<td><i>mrvSpecIsotopeSymbolPreferred</i></td>
<td>
					Special symbols are preferred for Hydrogen isotopes
					(D, T) if the value is 1, normal element symbol (H)
					is used if the value is 0.
				</td>
</tr>
<tr>
<td><i>mrvStereoGroup</i></td>
<td>
					MDL enchanced stereo group representation.
				</td>
</tr>
<tr>
<td><i>mrvValence</i></td>
<td>Valence list.</td>
</tr>
<tr>
<td><i>radical</i></td>
<td>
					List of the radicals.
				</td>
</tr>
<tr>
<td><i>reactionStereo</i></td>
<td>
					List of reaction stereo properties.
				</td>
</tr>
<tr>
<td><i>residueAtomName</i></td>
<td>PDB atom name.</td>
</tr>
<tr>
<td><i>residueId</i></td>
<td>
					List of residue Ids.
				</td>
</tr>
<tr>
<td><i>residueType</i></td>
<td>
					List of residue types.
				</td>
</tr>
<tr>
<td><i>rgroupRef</i></td>
<td>
					List of R-group reference values.
				</td>
</tr>
<tr>
<td><i>sgroupAttachmentPoint</i></td>
<td>
					List of attachment points.
				</td>
</tr>
<tr>
<td><i>sgroupRef</i></td>
<td>
					List of S-group references.
				</td>
</tr>
<tr>
<td><i>x2</i></td>
<td>
					X coordinates in two-dimensional representation.
				</td>
</tr>
<tr>
<td><i>x3</i></td>
<td>
					X coordinates in three-dimensional representation.
				</td>
</tr>
<tr>
<td><i>y2</i></td>
<td>
					Y coordinates in two-dimensional representation.
				</td>
</tr>
<tr>
<td><i>y3</i></td>
<td>
					Y coordinates in three-dimensional representation.
				</td>
</tr>
<tr>
<td><i>z3</i></td>
<td>
					Z coordinates in three-dimensional representation.
				</td>
</tr>
</table>
<ul><li>atom:
            Atom type.<table>
<tr>
<td><i>attachmentOrder</i></td>
<td>
					Attachment point order value in the case of R-group
					attachment point.
				</td>
</tr>
<tr>
<td><i>attachmentPoint</i></td>
<td>
					Attachment point value.
				</td>
</tr>
<tr>
<td><i>atomType</i></td>
<td>
					Type of the atom. It is written out only when the atom 
					is leaving group atom, its value is "l";
				</td>
</tr>
<tr>
<td><i>elementType</i></td>
<td>
					Element in the Periodic Table.
				</td>
</tr>
<tr>
<td><i>formalCharge</i></td>
<td>
					It is the charge assigned to an atom in a molecule,
					assuming that electrons in a chemical bond are
					shared equally between atoms, regardless of relative
					electronegativity.
				</td>
</tr>
<tr>
<td><i>hydrogenCount</i></td>
<td>
					Number of implicit hydrogen if needed. In the cases
					of 5-6 membered aromatic rings containing nitrogen
					atoms, it is not obvious which N atoms have implicit
					hydrogens.
				</td>
</tr>
<tr>
<td><i>id</i></td>
<td>Atom ID.</td>
</tr>
<tr>
<td><i>isotope</i></td>
<td>Atomic mass number.</td>
</tr>
<tr>
<td><i>isSelected</i></td>
<td>
					Boolean type variable which shows that the object is
					selected or not.
				</td>
</tr>
<tr>
<td><i>leavingGroupAtom</i></td>
<td>
					List of leaving group atoms.</td>
</tr>
<tr>
<td><i>ligandOrder</i></td>
<td>
					Order of ligands connected to an R-group atom: list
					of atom identifiers.
				</td>
</tr>
<tr>
<td><i>lonePair</i></td>
<td>
					Number of lone pairs.
				</td>
</tr>
<tr>
<td><i>mrvAlias</i></td>
<td>Atom alias.</td>
</tr>
<tr>
<td><i>mrvExtraLabel</i></td>
<td>Atom extra label.</td>
</tr>
<tr>
<td><i>mrvLinkNodeOut</i></td>
<td>
					Outer bond references for a link node in comma
					separated list of bond indices (amongst bonds of the
					link atom) leading to the outer atoms (non-repeating
					neighbours) of the link nodes, "-" means no outer
					bonds.
				</td>
</tr>
<tr>
<td><i>mrvLinkNodeRep</i></td>
<td>
					Number of repetitions for a link node in format
					"
					n
					" (maximum number of repetitions) or "
					m
					-
					n
					" (minimum and maximum).
				</td>
</tr>
<tr>
<td><i>mrvMap</i></td>
<td>
					The map corresponding to the given atom
					
						mapping.
					</td>
</tr>
<tr>
<td><i>mrvPseudo</i></td>
<td>Pseudoatom name.</td>
</tr>
<tr>
<td><i>mrvQueryProps</i></td>
<td>
					Query atom properties.
				</td>
</tr>
<tr>
<td><i>mrvSetExtraLabelSeq</i></td>
<td>
					Atom set extra label numbers.
				</td>
</tr>
<tr>
<td><i>mrvSetSeq</i></td>
<td>
					Atom set sequence number.
				</td>
</tr>
<tr>
<td><i>mrvSpecIsotopeSymbolPreferred</i></td>
<td>
					Special symbols are preferred for Hydrogen isotopes
					(D, T) if the value is 1, normal element symbol (H)
					is used if the value is 0.
				</td>
</tr>
<tr>
<td><i>mrvStereoGroup</i></td>
<td>
					MDL enchanced stereo group representation.
				</td>
</tr>
<tr>
<td><i>mrvValence</i></td>
<td>Valence.</td>
</tr>
<tr>
<td><i>radical</i></td>
<td>
					Name of the radical center.
				</td>
</tr>
<tr>
<td><i>reactionStereo</i></td>
<td>
					Reaction stereo value.
				</td>
</tr>
<tr>
<td><i>residueAtomName</i></td>
<td>PDB atom name.</td>
</tr>
<tr>
<td><i>residueId</i></td>
<td>Name of residue ID.</td>
</tr>
<tr>
<td><i>residueType</i></td>
<td>
					Name of residue type
				</td>
</tr>
<tr>
<td><i>rgroupRef</i></td>
<td>
					R-group reference value.
				</td>
</tr>
<tr>
<td><i>sgroupAttachmentPoint</i></td>
<td>
					S-group attachment point: "1", (on first site) "2"
					(on second site) or "both" (on both sites).
				</td>
</tr>
<tr>
<td><i>sgroupRef</i></td>
<td>
					S-group reference name.
				</td>
</tr>
<tr>
<td><i>x2</i></td>
<td>
					X coordinates in two-dimensional representation.
				</td>
</tr>
<tr>
<td><i>y2</i></td>
<td>
					Y coordinates in two-dimensional representation.
				</td>
</tr>
<tr>
<td><i>x3</i></td>
<td>
					X coordinates in three-dimensional representation.
				</td>
</tr>
<tr>
<td><i>y3</i></td>
<td>
					Y coordinates in three-dimensional representation.
				</td>
</tr>
<tr>
<td><i>z3</i></td>
<td>
					Z coordinates in three-dimensional representation.
				</td>
</tr>
</table>
<ul><li>atomParity:
            Atom parity.<table><tr>
<td><i>atomRefs4</i></td>
<td>
							Represents the parity value of the stereocenter
							according to the given four
							atom reference frame.
			</td>
</tr></table>
</li></ul>
<ul><li>scalar:
            
						List of the atom special scalar properties.
					<table>
<tr>
<td><i>convention</i></td>
<td>
					Name of the convention where the property is being
					interpeted.
				</td>
</tr>
<tr>
<td><i>dataType</i></td>
<td>
					Type of the property.
				</td>
</tr>
<tr>
<td><i>id</i></td>
<td>
					ID of the atom property.

				</td>
</tr>
<tr>
<td><i>title</i></td>
<td>
					String usually same as string.
				</td>
</tr>
</table>
</li></ul>
<ul><li>atomBicycloStereo:
            
						Bicyclostereo (endo- exo-) information of the
						atom. Possible values: If the ligand is oriented
						towards the bridge that contains the higer
						atomic index, it gets the THB (Towards Higher
						Bridge) label; if it's oriented towards the
						other bridge it gets the TLB (Towards Lower
						Bridge) label; if the position is undefined, it
						gets either value.
					<table>
<tr>
<td><i>connectionAtom</i></td>
<td>
						The ID of the connecting atom. 
						</td>
</tr>
<tr>
<td><i>highBridge</i></td>
<td>
						The atom reference list of the high bridge.
						</td>
</tr>
<tr>
<td><i>lowBridge</i></td>
<td>
						The atom reference list of the low bridge.
						</td>
</tr>
</table>
</li></ul>
</li></ul>
</li></ul>
<ul><li>bondArray:
            
					Array of bonds according to bond tags.
					<table></table>
<ul><li>bond:
            <table>
<tr>
<td><i>atomRefs2</i></td>
<td>
					List containing two atom references (a1,a2).
						</td>
</tr>
<tr>
<td><i>convention</i></td>
<td>
					A number refering the MDL stereo type of the bond
					or "cxn:coord" in
					case of coordinative bond.
						</td>
</tr>
<tr>
<td><i>mrvBold</i></td>
<td>
				Bold bond attribute.
						</td>
</tr>
<tr>
<td><i>mrvHashed</i></td>
<td>
				Hashed bond attribute.
						</td>
</tr>
<tr>
<td><i>mrvQueryProps</i></td>
<td>
				Query bond properties.
						</td>
</tr>
<tr>
<td><i>mrvReactingCenter</i></td>
<td>Reacting center bond query feature
					.
					Possible values:
					-1: not center, 1: center, 4: make or break, 8:
					change, 12: make and change.
				</td>
</tr>
<tr>
<td><i>mrvReactionCenter</i></td>
<td></td>
</tr>
<tr>
<td><i>mrvSetSeq</i></td>
<td>
				Atom set sequence number.
						</td>
</tr>
<tr>
<td><i>order</i></td>
<td>
					Name of bond order (e.g., single, double, triple,
					aromatic etc.).
						</td>
</tr>
<tr>
<td><i>queryType</i></td>
<td>
				Name of query bond type.
						</td>
</tr>
<tr>
<td><i>topology</i></td>
<td>
					Defines that the bond is a part of a ring or a
					chain.
						</td>
</tr>
</table>
<ul><li>bondStereo:
            <table>
<tr>
<td><i>convention</i></td>
<td>
							Stereo representation framework type.
						</td>
</tr>
<tr>
<td><i>conventionValue</i></td>
<td>
							Stereo Convention Value: It can be
							MDLStereoValue or
							ChemAxonStereoValue.
					</td>
</tr>
</table>
</li></ul>
<ul><li>scalar:
            
				List of the bond special scalar properties. <table>
<tr>
<td><i>convention</i></td>
<td>
					Name of the convention where the property is being
					interpeted.
				</td>
</tr>
<tr>
<td><i>dataType</i></td>
<td>
					Type of the property.
				</td>
</tr>
<tr>
<td><i>id</i></td>
<td>
					ID of the bond property.
				</td>
</tr>
<tr>
<td><i>title</i></td>
<td>
					String usually same as string.
				</td>
</tr>
</table>
</li></ul>
</li></ul>
</li></ul>
<ul><li>molecule:
            
						Submolecule (S-groups).
					<table>
<tr>
<td><i>atomRefs</i></td>
<td>
					Reference to the atoms in the S-Groups.
				</td>
</tr>
<tr>
<td><i>bondList</i></td>
<td>
					List of bonds
				</td>
</tr>
<tr>
<td><i>center</i></td>
<td>
					Atom reference of center atom
				</td>
</tr>
<tr>
<td><i>charge</i></td>
<td>
					Place of the charge. It's value can be "onAtoms"
					or "onBracket"
				</td>
</tr>
<tr>
<td><i>connect</i></td>
<td>
					Type of monomer connection in case of copolymers.
				</td>
</tr>
<tr>
<td><i>context</i></td>
<td>
					Context of the data field.
				</td>
</tr>
<tr>
<td><i>correspondence</i></td>
<td>
					S-group correspondence
				</td>
</tr>
<tr>
<td><i>dataDetached</i></td>
<td>
					Boolean type variable showing if the data is
					detached or not.
				</td>
</tr>
<tr>
<td><i>displayedChars</i></td>
<td>
					Number of characters displayed per line
				</td>
</tr>
<tr>
<td><i>displayedLines</i></td>
<td>
					Number of the lines displayed
				</td>
</tr>
<tr>
<td><i>fieldData</i></td>
<td>
					First field of the data.
				</td>
</tr>
<tr>
<td><i>fieldData1</i></td>
<td>
					Second field of the data
				</td>
</tr>
<tr>
<td><i>fieldData2</i></td>
<td>
					Third field of the data
				</td>
</tr>
<tr>
<td><i>fieldData3</i></td>
<td>
					Fourth field of the data
				</td>
</tr>
<tr>
<td><i>fieldData4</i></td>
<td>
					Fifth field of the data
				</td>
</tr>
<tr>
<td><i>fieldData5</i></td>
<td>
					Sixth field of the data
				</td>
</tr>
<tr>
<td><i>fieldData6</i></td>
<td>
					Seventh field of the data
				</td>
</tr>
<tr>
<td><i>fieldData7</i></td>
<td>
					Eighth field of the data
				</td>
</tr>
<tr>
<td><i>fieldData8</i></td>
<td>
					Ninth field of the data
				</td>
</tr>
<tr>
<td><i>fieldData9</i></td>
<td>
					Tenth field of the data
				</td>
</tr>
<tr>
<td><i>fieldName</i></td>
<td>
					Name of the field in Data S-Groups.
				</td>
</tr>
<tr>
<td><i>fieldType</i></td>
<td>
					Type of the field
				</td>
</tr>
<tr>
<td><i>id</i></td>
<td>
					ID number of the S-group
				</td>
</tr>
<tr>
<td><i>labelCenter</i></td>
<td>
					Defines the atom of the S-group 
					to which the bond points, 
					if flipping effect is not used.  
				</td>
</tr>
<tr>
<td><i>leftName</i></td>
<td>
				    Alternative name of the abbreviated group
				    when flipping effect is used, and the name 
				    group is on the left hand side of the molecule.
				    (e.g. the leftName of "COOH" is HOOC).
  				</td>
</tr>
<tr>
<td><i>molID</i></td>
<td>
					ID number of the molecule in the MRV file.
				</td>
</tr>
<tr>
<td><i>placement</i></td>
<td>
					Defines if the placement of the data is absolute
					or relative in data S-groups.
				</td>
</tr>
<tr>
<td><i>pos</i></td>
<td>
					Identifies to which point of a rectangle this
					point is
					connected.
				</td>
</tr>
<tr>
<td><i>queryOp</i></td>
<td>
					Query operator in data S-groups.
				</td>
</tr>
<tr>
<td><i>queryType</i></td>
<td>
					Name of the query bond type.
				</td>
</tr>
<tr>
<td><i>rightName</i></td>
<td>
				    Alternative name of the abbreviated group
				    when flipping effect is used, and the name 
				    group is on the right hand side of the molecule
				    (e.g. the rightName of "MeO" is OMe).
				</td>
</tr>
<tr>
<td><i>role</i></td>
<td>
					Name of the Sgroup type (e.g. SRU).
				</td>
</tr>
<tr>
<td><i>tag</i></td>
<td>
					Tag of the fieldData.
				</td>
</tr>
<tr>
<td><i>title</i></td>
<td>
					Title of the S-group. That is the string written
					in the subscript.
				</td>
</tr>
<tr>
<td><i>units</i></td>
<td>
					Unit of the data in Data S-group.
				</td>
</tr>
<tr>
<td><i>unitsDisplayed</i></td>
<td>
					Shows if the unit of data is displayed in data
					S-group.
				</td>
</tr>
<tr>
<td><i>x</i></td>
<td>
					X coordinate.
				</td>
</tr>
<tr>
<td><i>y</i></td>
<td>
					Y coordinate.
				</td>
</tr>
</table>
<ul><li>propertyList:
            
						List of molecule property attributes.
					<table></table>
<ul><li>property:
            
						Molecule property type.
					<table>
<tr>
<td><i>dictRef</i></td>
<td>
					Key of the properties related to the document.
					</td>
</tr>
<tr>
<td><i>title</i></td>
<td></td>
</tr>
</table>
<ul><li>scalar:
            
						Scalar type molecular property.
					<table><tr>
<td><i>dataType</i></td>
<td>
							Scalar data types
					</td>
</tr></table>
</li></ul>
<ul><li>array:
            
						Array type molecular property.
					<table><tr>
<td><i>delimiter</i></td>
<td>
							Delimiter string of molecule array properties.
					</td>
</tr></table>
</li></ul>
</li></ul>
</li></ul>
<ul><li>atomArray:
            
						List of atoms in the S-group.
					<table>
<tr>
<td><i>atomID</i></td>
<td>
					This argument is a space separated list of atoms.
				</td>
</tr>
<tr>
<td><i>atomType</i></td>
<td>
					List marking if atom is MolAtom or LeavingGroupAtom.
					Values: "a" atom; "l" leaving group atom.  
				</td>
</tr>
<tr>
<td><i>attachmentOrder</i></td>
<td>
					Attachment point order value in the case of R-group
					attachment point.
				</td>
</tr>
<tr>
<td><i>attachmentPoint</i></td>
<td>
					List of attachment points.
				</td>
</tr>
<tr>
<td><i>elementType</i></td>
<td>
					Element in the Periodic Table.
				</td>
</tr>
<tr>
<td><i>formalCharge</i></td>
<td>
					It is the charge assigned to an atom in a molecule,
					assuming that electrons in a chemical bond are
					shared equally between atoms, regardless of relative
					electronegativity.
				</td>
</tr>
<tr>
<td><i>hydrogenCount</i></td>
<td>
					Number of implicit hydrogen if needed. In the cases
					of 5-6 membered aromatic rings containing nitrogen
					atoms, it is not obvious which N atoms have implicit
					hydrogens.
				</td>
</tr>
<tr>
<td><i>isSelected</i></td>
<td>
					Boolean type variable which shows that the object is
					selected or not.
				</td>
</tr>
<tr>
<td><i>isotope</i></td>
<td>Atomic mass number.</td>
</tr>
<tr>
<td><i>lonePair</i></td>
<td>
					Number of lone pairs.
				</td>
</tr>
<tr>
<td><i>mrvAlias</i></td>
<td>Atom alias.</td>
</tr>
<tr>
<td><i>mrvExtraLabel</i></td>
<td>
					List of Atom extra labels.
				</td>
</tr>
<tr>
<td><i>mrvLinkNodeOut</i></td>
<td>
					Outer bond references for a link node in comma
					separated list of bond indices (amongst bonds of the
					link atom) leading to the outer atoms (non-repeating
					neighbours) of the link nodes, "-" means no outer
					bonds.
				</td>
</tr>
<tr>
<td><i>mrvLinkNodeRep</i></td>
<td>
					Number of repetitions for a link node in format
					"
					n
					" (maximum number of repetitions) or "
					m
					-
					n
					" (minimum and maximum).
				</td>
</tr>
<tr>
<td><i>mrvMap</i></td>
<td>
					The map corresponding to the given atom
					
						mapping
					
					.
				</td>
</tr>
<tr>
<td><i>mrvPseudo</i></td>
<td>
					List of pseudoatom names.
				</td>
</tr>
<tr>
<td><i>mrvQueryProps</i></td>
<td>
					List of query atom properties
				</td>
</tr>
<tr>
<td><i>mrvSetExtraLabelSeq</i></td>
<td>
					Atom set extra label numbers.
				</td>
</tr>
<tr>
<td><i>mrvSetSeq</i></td>
<td>
					Atom set sequence number.
				</td>
</tr>
<tr>
<td><i>mrvSpecIsotopeSymbolPreferred</i></td>
<td>
					Special symbols are preferred for Hydrogen isotopes
					(D, T) if the value is 1, normal element symbol (H)
					is used if the value is 0.
				</td>
</tr>
<tr>
<td><i>mrvStereoGroup</i></td>
<td>
					MDL enchanced stereo group representation.
				</td>
</tr>
<tr>
<td><i>mrvValence</i></td>
<td>Valence list.</td>
</tr>
<tr>
<td><i>radical</i></td>
<td>
					List of the radicals.
				</td>
</tr>
<tr>
<td><i>reactionStereo</i></td>
<td>
					List of reaction stereo properties.
				</td>
</tr>
<tr>
<td><i>residueAtomName</i></td>
<td>PDB atom name.</td>
</tr>
<tr>
<td><i>residueId</i></td>
<td>
					List of residue Ids.
				</td>
</tr>
<tr>
<td><i>residueType</i></td>
<td>
					List of residue types.
				</td>
</tr>
<tr>
<td><i>rgroupRef</i></td>
<td>
					List of R-group reference values.
				</td>
</tr>
<tr>
<td><i>sgroupAttachmentPoint</i></td>
<td>
					List of attachment points.
				</td>
</tr>
<tr>
<td><i>sgroupRef</i></td>
<td>
					List of S-group references.
				</td>
</tr>
<tr>
<td><i>x2</i></td>
<td>
					X coordinates in two-dimensional representation.
				</td>
</tr>
<tr>
<td><i>x3</i></td>
<td>
					X coordinates in three-dimensional representation.
				</td>
</tr>
<tr>
<td><i>y2</i></td>
<td>
					Y coordinates in two-dimensional representation.
				</td>
</tr>
<tr>
<td><i>y3</i></td>
<td>
					Y coordinates in three-dimensional representation.
				</td>
</tr>
<tr>
<td><i>z3</i></td>
<td>
					Z coordinates in three-dimensional representation.
				</td>
</tr>
</table>
<ul><li>atom:
            Atom type.<table>
<tr>
<td><i>attachmentOrder</i></td>
<td>
					Attachment point order value in the case of R-group
					attachment point.
				</td>
</tr>
<tr>
<td><i>attachmentPoint</i></td>
<td>
					Attachment point value.
				</td>
</tr>
<tr>
<td><i>atomType</i></td>
<td>
					Type of the atom. It is written out only when the atom 
					is leaving group atom, its value is "l";
				</td>
</tr>
<tr>
<td><i>elementType</i></td>
<td>
					Element in the Periodic Table.
				</td>
</tr>
<tr>
<td><i>formalCharge</i></td>
<td>
					It is the charge assigned to an atom in a molecule,
					assuming that electrons in a chemical bond are
					shared equally between atoms, regardless of relative
					electronegativity.
				</td>
</tr>
<tr>
<td><i>hydrogenCount</i></td>
<td>
					Number of implicit hydrogen if needed. In the cases
					of 5-6 membered aromatic rings containing nitrogen
					atoms, it is not obvious which N atoms have implicit
					hydrogens.
				</td>
</tr>
<tr>
<td><i>id</i></td>
<td>Atom ID.</td>
</tr>
<tr>
<td><i>isotope</i></td>
<td>Atomic mass number.</td>
</tr>
<tr>
<td><i>isSelected</i></td>
<td>
					Boolean type variable which shows that the object is
					selected or not.
				</td>
</tr>
<tr>
<td><i>leavingGroupAtom</i></td>
<td>
					List of leaving group atoms.</td>
</tr>
<tr>
<td><i>ligandOrder</i></td>
<td>
					Order of ligands connected to an R-group atom: list
					of atom identifiers.
				</td>
</tr>
<tr>
<td><i>lonePair</i></td>
<td>
					Number of lone pairs.
				</td>
</tr>
<tr>
<td><i>mrvAlias</i></td>
<td>Atom alias.</td>
</tr>
<tr>
<td><i>mrvExtraLabel</i></td>
<td>Atom extra label.</td>
</tr>
<tr>
<td><i>mrvLinkNodeOut</i></td>
<td>
					Outer bond references for a link node in comma
					separated list of bond indices (amongst bonds of the
					link atom) leading to the outer atoms (non-repeating
					neighbours) of the link nodes, "-" means no outer
					bonds.
				</td>
</tr>
<tr>
<td><i>mrvLinkNodeRep</i></td>
<td>
					Number of repetitions for a link node in format
					"
					n
					" (maximum number of repetitions) or "
					m
					-
					n
					" (minimum and maximum).
				</td>
</tr>
<tr>
<td><i>mrvMap</i></td>
<td>
					The map corresponding to the given atom
					
						mapping.
					</td>
</tr>
<tr>
<td><i>mrvPseudo</i></td>
<td>Pseudoatom name.</td>
</tr>
<tr>
<td><i>mrvQueryProps</i></td>
<td>
					Query atom properties.
				</td>
</tr>
<tr>
<td><i>mrvSetExtraLabelSeq</i></td>
<td>
					Atom set extra label numbers.
				</td>
</tr>
<tr>
<td><i>mrvSetSeq</i></td>
<td>
					Atom set sequence number.
				</td>
</tr>
<tr>
<td><i>mrvSpecIsotopeSymbolPreferred</i></td>
<td>
					Special symbols are preferred for Hydrogen isotopes
					(D, T) if the value is 1, normal element symbol (H)
					is used if the value is 0.
				</td>
</tr>
<tr>
<td><i>mrvStereoGroup</i></td>
<td>
					MDL enchanced stereo group representation.
				</td>
</tr>
<tr>
<td><i>mrvValence</i></td>
<td>Valence.</td>
</tr>
<tr>
<td><i>radical</i></td>
<td>
					Name of the radical center.
				</td>
</tr>
<tr>
<td><i>reactionStereo</i></td>
<td>
					Reaction stereo value.
				</td>
</tr>
<tr>
<td><i>residueAtomName</i></td>
<td>PDB atom name.</td>
</tr>
<tr>
<td><i>residueId</i></td>
<td>Name of residue ID.</td>
</tr>
<tr>
<td><i>residueType</i></td>
<td>
					Name of residue type
				</td>
</tr>
<tr>
<td><i>rgroupRef</i></td>
<td>
					R-group reference value.
				</td>
</tr>
<tr>
<td><i>sgroupAttachmentPoint</i></td>
<td>
					S-group attachment point: "1", (on first site) "2"
					(on second site) or "both" (on both sites).
				</td>
</tr>
<tr>
<td><i>sgroupRef</i></td>
<td>
					S-group reference name.
				</td>
</tr>
<tr>
<td><i>x2</i></td>
<td>
					X coordinates in two-dimensional representation.
				</td>
</tr>
<tr>
<td><i>y2</i></td>
<td>
					Y coordinates in two-dimensional representation.
				</td>
</tr>
<tr>
<td><i>x3</i></td>
<td>
					X coordinates in three-dimensional representation.
				</td>
</tr>
<tr>
<td><i>y3</i></td>
<td>
					Y coordinates in three-dimensional representation.
				</td>
</tr>
<tr>
<td><i>z3</i></td>
<td>
					Z coordinates in three-dimensional representation.
				</td>
</tr>
</table>
<ul><li>atomParity:
            Atom parity.<table><tr>
<td><i>atomRefs4</i></td>
<td>
							Represents the parity value of the stereocenter
							according to the given four
							atom reference frame.
			</td>
</tr></table>
</li></ul>
<ul><li>scalar:
            
						List of the atom special scalar properties.
					<table>
<tr>
<td><i>convention</i></td>
<td>
					Name of the convention where the property is being
					interpeted.
				</td>
</tr>
<tr>
<td><i>dataType</i></td>
<td>
					Type of the property.
				</td>
</tr>
<tr>
<td><i>id</i></td>
<td>
					ID of the atom property.

				</td>
</tr>
<tr>
<td><i>title</i></td>
<td>
					String usually same as string.
				</td>
</tr>
</table>
</li></ul>
<ul><li>atomBicycloStereo:
            
						Bicyclostereo (endo- exo-) information of the
						atom. Possible values: If the ligand is oriented
						towards the bridge that contains the higer
						atomic index, it gets the THB (Towards Higher
						Bridge) label; if it's oriented towards the
						other bridge it gets the TLB (Towards Lower
						Bridge) label; if the position is undefined, it
						gets either value.
					<table>
<tr>
<td><i>connectionAtom</i></td>
<td>
						The ID of the connecting atom. 
						</td>
</tr>
<tr>
<td><i>highBridge</i></td>
<td>
						The atom reference list of the high bridge.
						</td>
</tr>
<tr>
<td><i>lowBridge</i></td>
<td>
						The atom reference list of the low bridge.
						</td>
</tr>
</table>
</li></ul>
</li></ul>
</li></ul>
<ul><li>bondArray:
            
						List of bonds in the S-group.
					<table></table>
<ul><li>bond:
            <table>
<tr>
<td><i>atomRefs2</i></td>
<td>
					List containing two atom references (a1,a2).
						</td>
</tr>
<tr>
<td><i>convention</i></td>
<td>
					A number refering the MDL stereo type of the bond
					or "cxn:coord" in
					case of coordinative bond.
						</td>
</tr>
<tr>
<td><i>mrvBold</i></td>
<td>
				Bold bond attribute.
						</td>
</tr>
<tr>
<td><i>mrvHashed</i></td>
<td>
				Hashed bond attribute.
						</td>
</tr>
<tr>
<td><i>mrvQueryProps</i></td>
<td>
				Query bond properties.
						</td>
</tr>
<tr>
<td><i>mrvReactingCenter</i></td>
<td>Reacting center bond query feature
					.
					Possible values:
					-1: not center, 1: center, 4: make or break, 8:
					change, 12: make and change.
				</td>
</tr>
<tr>
<td><i>mrvReactionCenter</i></td>
<td></td>
</tr>
<tr>
<td><i>mrvSetSeq</i></td>
<td>
				Atom set sequence number.
						</td>
</tr>
<tr>
<td><i>order</i></td>
<td>
					Name of bond order (e.g., single, double, triple,
					aromatic etc.).
						</td>
</tr>
<tr>
<td><i>queryType</i></td>
<td>
				Name of query bond type.
						</td>
</tr>
<tr>
<td><i>topology</i></td>
<td>
					Defines that the bond is a part of a ring or a
					chain.
						</td>
</tr>
</table>
<ul><li>bondStereo:
            <table>
<tr>
<td><i>convention</i></td>
<td>
							Stereo representation framework type.
						</td>
</tr>
<tr>
<td><i>conventionValue</i></td>
<td>
							Stereo Convention Value: It can be
							MDLStereoValue or
							ChemAxonStereoValue.
					</td>
</tr>
</table>
</li></ul>
<ul><li>scalar:
            
				List of the bond special scalar properties. <table>
<tr>
<td><i>convention</i></td>
<td>
					Name of the convention where the property is being
					interpeted.
				</td>
</tr>
<tr>
<td><i>dataType</i></td>
<td>
					Type of the property.
				</td>
</tr>
<tr>
<td><i>id</i></td>
<td>
					ID of the bond property.
				</td>
</tr>
<tr>
<td><i>title</i></td>
<td>
					String usually same as string.
				</td>
</tr>
</table>
</li></ul>
</li></ul>
</li></ul>
<ul><li>AttachmentPointArray:
            
						Array of the attachment points of the superatom
						S-group in the
						multiple attachment point representation.
					<table></table>
<ul><li>attachmentPoint:
            
						AttachmentPoint of the Superatom S-group.
					<table>
<tr>
<td><i>atom</i></td>
<td>
					The atom on which the attachment point is placed.
				</td>
</tr>
<tr>
<td><i>order</i></td>
<td>
					The order of the attachment point.
				</td>
</tr>
<tr>
<td><i>bond</i></td>
<td>
					The crossing bond of the attachment point.
				</td>
</tr>
<tr>
<td><i>crossingBondType</i></td>
<td>
						The type of the crossing bond.
				</td>
</tr>
</table>
<ul><li>leavingGroup:
            
						Leaving group atom of the attachment point.
					<table>
<tr>
<td><i>atomID</i></td>
<td>
					This argument is the id of the leaving group atom.
				</td>
</tr>
<tr>
<td><i>alternativeName</i></td>
<td>
					The alternative name of the leaving group atom.
				</td>
</tr>
<tr>
<td><i>elementType</i></td>
<td>
					Element in the Periodic Table.
				</td>
</tr>
<tr>
<td><i>formalCharge</i></td>
<td>
					It is the charge assigned to an atom in a molecule,
					assuming that electrons in a chemical bond are
					shared equally between atoms, regardless of relative
					electronegativity.
				</td>
</tr>
<tr>
<td><i>hydrogenCount</i></td>
<td>
					Number of implicit hydrogen if needed. In the cases
					of 5-6 membered aromatic rings containing nitrogen
					atoms, it is not obvious which N atoms have implicit
					hydrogens.
				</td>
</tr>
<tr>
<td><i>isSelected</i></td>
<td>
					Boolean type variable which shows that the object is
					selected or not.
				</td>
</tr>
<tr>
<td><i>isotope</i></td>
<td>Atomic mass number.</td>
</tr>
<tr>
<td><i>lonePair</i></td>
<td>
					Number of lone pairs.
				</td>
</tr>
<tr>
<td><i>mrvAlias</i></td>
<td>Atom alias.</td>
</tr>
<tr>
<td><i>mrvExtraLabel</i></td>
<td>
					List of Atom extra labels.
				</td>
</tr>
<tr>
<td><i>mrvLinkNodeOut</i></td>
<td>
					Outer bond references for a link node in comma
					separated list of bond indices (amongst bonds of the
					link atom) leading to the outer atoms (non-repeating
					neighbours) of the link nodes, "-" means no outer
					bonds.
				</td>
</tr>
<tr>
<td><i>mrvLinkNodeRep</i></td>
<td>
					Number of repetitions for a link node in format
					"
					n
					" (maximum number of repetitions) or "
					m
					-
					n
					" (minimum and maximum).
				</td>
</tr>
<tr>
<td><i>mrvMap</i></td>
<td>
					The map corresponding to the given atom
					
						mapping
					
					.
				</td>
</tr>
<tr>
<td><i>mrvPseudo</i></td>
<td>
					List of pseudoatom names.
				</td>
</tr>
<tr>
<td><i>mrvQueryProps</i></td>
<td>
					List of query atom properties
				</td>
</tr>
<tr>
<td><i>mrvSetExtraLabelSeq</i></td>
<td>
					Atom set extra label numbers.
				</td>
</tr>
<tr>
<td><i>mrvSetSeq</i></td>
<td>
					Atom set sequence number.
				</td>
</tr>
<tr>
<td><i>mrvSpecIsotopeSymbolPreferred</i></td>
<td>
					Special symbols are preferred for Hydrogen isotopes
					(D, T) if the value is 1, normal element symbol (H)
					is used if the value is 0.
				</td>
</tr>
<tr>
<td><i>mrvStereoGroup</i></td>
<td>
					MDL enchanced stereo group representation.
				</td>
</tr>
<tr>
<td><i>mrvValence</i></td>
<td>Valence list.</td>
</tr>
<tr>
<td><i>radical</i></td>
<td>
					List of the radicals.
				</td>
</tr>
<tr>
<td><i>reactionStereo</i></td>
<td>
					List of reaction stereo properties.
				</td>
</tr>
<tr>
<td><i>sgroupRef</i></td>
<td>
				List of S-group references.</td>
</tr>
<tr>
<td><i>x2</i></td>
<td>
					X coordinates in two-dimensional representation.
				</td>
</tr>
<tr>
<td><i>x3</i></td>
<td>
					X coordinates in three-dimensional representation.
				</td>
</tr>
<tr>
<td><i>y2</i></td>
<td>
					Y coordinates in two-dimensional representation.
				</td>
</tr>
<tr>
<td><i>y3</i></td>
<td>
					Y coordinates in three-dimensional representation.
				</td>
</tr>
<tr>
<td><i>z3</i></td>
<td>
					Z coordinates in three-dimensional representation.
				</td>
</tr>
</table>
<ul><li>atomParity:
            Atom parity.<table><tr>
<td><i>atomRefs4</i></td>
<td>
							Represents the parity value of the stereocenter
							according to the given four
							atom reference frame.
			</td>
</tr></table>
</li></ul>
<ul><li>scalar:
            
						List of the atom special scalar properties.
					<table>
<tr>
<td><i>convention</i></td>
<td>
					Name of the convention where the property is being
					interpeted.
				</td>
</tr>
<tr>
<td><i>dataType</i></td>
<td>
					Type of the property.
				</td>
</tr>
<tr>
<td><i>id</i></td>
<td>
					ID of the atom property.

				</td>
</tr>
<tr>
<td><i>title</i></td>
<td>
					String usually same as string.
				</td>
</tr>
</table>
</li></ul>
<ul><li>atomBicycloStereo:
            
						Bicyclostereo (endo- exo-) information of the
						atom. Possible values: If the ligand is oriented
						towards the bridge that contains the higer
						atomic index, it gets the THB (Towards Higher
						Bridge) label; if it's oriented towards the
						other bridge it gets the TLB (Towards Lower
						Bridge) label; if the position is undefined, it
						gets either value.
					<table>
<tr>
<td><i>connectionAtom</i></td>
<td>
						The ID of the connecting atom. 
						</td>
</tr>
<tr>
<td><i>highBridge</i></td>
<td>
						The atom reference list of the high bridge.
						</td>
</tr>
<tr>
<td><i>lowBridge</i></td>
<td>
						The atom reference list of the low bridge.
						</td>
</tr>
</table>
</li></ul>
</li></ul>
</li></ul>
</li></ul>
<ul><li>MBracket:
            
				Bracket object.
					<table>
<tr>
<td><i>isSelected</i></td>
<td>
					Boolean type variable which shows that the object
					is selected or not.
						</td>
</tr>
<tr>
<td><i>lineColor</i></td>
<td>
				Line color.
						</td>
</tr>
<tr>
<td><i>orientation</i></td>
<td>
					Determines whether bracket is in pairs or a
					standalone
					bracket. "Single" means that the bracket can be moved
					or
					resized alone, "double" means that the pair of brackets
					are treated
					as one entity, can be moved and resiyed only together.
						</td>
</tr>
<tr>
<td><i>tcenter</i></td>
<td>
					Geometric center of the object for transformations
					like
					rotation. Identifies corners and middle-points with
					points of
					the compass, e.g. NW (North West) identifies
					the top right corner.
					Possible values are the eight
					points of the compass or center.
						</td>
</tr>
<tr>
<td><i>thickness</i></td>
<td>
				Thickness.
						</td>
</tr>
<tr>
<td><i>toption</i></td>
<td>
				It shows that the object can be rotated or not.
						</td>
</tr>
<tr>
<td><i>type</i></td>
<td>
				Type of the bracket.
						</td>
</tr>
</table>
<ul><li>MPoint:
            
				Basic Point of a polyline object.
					<table>
<tr>
<td><i>x</i></td>
<td>
				X coordinate.
						</td>
</tr>
<tr>
<td><i>y</i></td>
<td>
				Y coordinate.
						</td>
</tr>
<tr>
<td><i>z</i></td>
<td>
				Z coordinate.
						</td>
</tr>
</table>
</li></ul>
</li></ul>
</li></ul>
<h3>
<a name="reaction"></a> 
        Element: reaction</h3>
<table>
<tr>
<td><i>absStereo</i></td>
<td>
					It shows that absolute stereo label is on or off.
					</td>
</tr>
<tr>
<td><i>title</i></td>
<td>
					Title.
					</td>
</tr>
</table>
<ul><li>arrow:
            
				Reaction arrow type.
					<table>
<tr>
<td><i>type</i></td>
<td>
					Type of reaction arrow. E.g., EQUILIBRIUM.
						</td>
</tr>
<tr>
<td><i>x1</i></td>
<td>
					The X coordinate of the staring point of the
					reaction arrow.
						</td>
</tr>
<tr>
<td><i>x2</i></td>
<td>
					The X coordinate of the endpoint of the reaction
					arrow.
						</td>
</tr>
<tr>
<td><i>y1</i></td>
<td>
					The Y coordinate of the staring point of the
					reaction arrow.
						</td>
</tr>
<tr>
<td><i>y2</i></td>
<td>
					The Y coordinate of the endpoint of the reaction
					arrow.
						</td>
</tr>
<tr>
<td><i>z1</i></td>
<td>
					The Z coordinate of the staring point of the
					reaction arrow.
						</td>
</tr>
<tr>
<td><i>z2</i></td>
<td>
					The Z coordinate of the endpoint of the reaction
					arrow.
						</td>
</tr>
</table>
</li></ul>
<ul><li>propertyList:
            
						Property list.
					<table></table>
<ul><li>property:
            
						Molecule property type.
					<table>
<tr>
<td><i>dictRef</i></td>
<td>
					Key of the properties related to the document.
					</td>
</tr>
<tr>
<td><i>title</i></td>
<td></td>
</tr>
</table>
<ul><li>scalar:
            
						Scalar type molecular property.
					<table><tr>
<td><i>dataType</i></td>
<td>
							Scalar data types
					</td>
</tr></table>
</li></ul>
<ul><li>array:
            
						Array type molecular property.
					<table><tr>
<td><i>delimiter</i></td>
<td>
							Delimiter string of molecule array properties.
					</td>
</tr></table>
</li></ul>
</li></ul>
</li></ul>
<ul><li>reactantList:
            
				List of reactants in the reaction.
					<table></table>
<ul><li>
<a href="#molecule">molecule</a>:
            
			Element containing the description of a molecule.
				</li></ul>
</li></ul>
<ul><li>agentList:
            
				List of agents in the reaction.
					<table></table>
<ul><li>
<a href="#molecule">molecule</a>:
            
			Element containing the description of a molecule.
				</li></ul>
</li></ul>
<ul><li>productList:
            
				List of products in the reaction.
					<table></table>
<ul><li>
<a href="#molecule">molecule</a>:
            
			Element containing the description of a molecule.
				</li></ul>
</li></ul>
</ul>
<h2>
<a name="escape"></a>Escape characters</h2>
    Special value of an element or attribute is escaped as follows:<ul><table>
<tr>
<td> null </td>
<td> "0"</td>
</tr>
<tr>
<td> 0 </td>
<td> "zero" <i>(character string)</i>
</td>
</tr>
<tr>
<td> no value   </td>
<td> "."</td>
</tr>
<tr>
<td> "."</td>
<td> &amp;#<i>n</i>;, <i>n</i> is the character code  </td>
</tr>
</table></ul>
<h2>
<a name="schema"></a>Schema</h2>
	Mrv files can be validated by using the XSD schema 
	containing the description of the mrv format.
	<h3>
<a name="schemaVersions"></a>Schema Versions </h3>
<ul>
<li><a href="schema/mrvSchema_6_0_0.xsd">mrvSchema-6_0_0.xsd</a></li>
<li><a href="schema/mrvSchema_5_11_0.xsd">mrvSchema-5_11_0.xsd</a></li>
<li><a href="schema/mrvSchema_5_10_0.xsd">mrvSchema-5_10_0.xsd</a></li>
<li><a href="schema/mrvSchema_5_9_0.xsd">mrvSchema-5_9_0.xsd</a></li>
<li><a href="schema/mrvSchema_5_8_2.xsd">mrvSchema-5_8_2.xsd</a></li>
</ul>
<h2>
<a name="old"></a>Old mrv format documentation</h2>
	The old description of mrv format is available <a href="mrv-doc-old.html">here</a>. 
    <h2>
<a name="export"></a>  Export Options</h2>
    The mrv has all the export options that 
    <a href="cml-doc.html#export">cml</a> has, except option ':A' (in mrv the 
    usage of array format for the description of atoms is decided automatically 
    upon the content of the molecule).
    In addition, mrv has a further 
    export option: 
    
    <table cellpadding="0" width="850" cellspacing="0">
<col width="103">
<col width="748">
<tr valign="TOP">
<td width="103" style="border: none; padding: 0cm"><p style="border: none; padding: 0cm">mrv:S</p></td>
<td width="746" style="; border: none; padding: 0cm"><p style="border: none; padding: 0cm">
              Save selection. If set then the mrv output will contain 
              which atoms and graphical objects are
              in selected state.</p></td>
</tr>
</table>
<P>Example of MRV file exported without options: 
	</P>
<PRE STYLE="margin-left: 1cm; margin-right: 1cm">
	&lt;?xml version="1.0"?&gt;&lt;cml&gt;
	&lt;MDocument&gt;&lt;MChemicalStruct&gt;&lt;molecule title="Ethane" molID="m1"&gt;&lt;atomArray atomID="a1 a2" elementType="C C" x2="0.0 0.0" y2="0.0 1.54"&gt;&lt;/atomArray&gt;&lt;bondArray&gt;&lt;bond atomRefs2="a1 a2" order="1"&gt;&lt;/bond&gt;&lt;/bondArray&gt;&lt;/molecule&gt;&lt;/MChemicalStruct&gt;&lt;MRectangle id="o2"&gt;&lt;MPoint x="-6.599999904632568" y="1.7050000429153442"&gt;&lt;/MPoint&gt;&lt;MPoint x="-2.640000104904175" y="1.7050000429153442"&gt;&lt;/MPoint&gt;&lt;MPoint x="-2.640000104904175" y="-0.16500000655651093"&gt;&lt;/MPoint&gt;&lt;MPoint x="-6.599999904632568" y="-0.16500000655651093"&gt;&lt;/MPoint&gt;&lt;/MRectangle&gt;&lt;/MDocument&gt;
	&lt;/cml&gt;
	</PRE>
<P>
	Example of MRV file exported with option "S": 
	</P>
<PRE STYLE="margin-left: 1cm; margin-right: 1cm">
	&lt;?xml version="1.0"?&gt;&lt;cml&gt;
	&lt;MDocument&gt;&lt;MChemicalStruct&gt;&lt;molecule title="Ethane" molID="m1"&gt;&lt;atomArray atomID="a1 a2" elementType="C C" isSelected="true true" x2="0.0 0.0" y2="0.0 1.54"&gt;&lt;/atomArray&gt;&lt;bondArray&gt;&lt;bond atomRefs2="a1 a2" order="1"&gt;&lt;/bond&gt;&lt;/bondArray&gt;&lt;/molecule&gt;&lt;/MChemicalStruct&gt;&lt;MRectangle id="o2" isSelected="true"&gt;&lt;MPoint x="-6.599999904632568" y="1.7050000429153442"&gt;&lt;/MPoint&gt;&lt;MPoint x="-2.640000104904175" y="1.7050000429153442"&gt;&lt;/MPoint&gt;&lt;MPoint x="-2.640000104904175" y="-0.16500000655651093"&gt;&lt;/MPoint&gt;&lt;MPoint x="-6.599999904632568" y="-0.16500000655651093"&gt;&lt;/MPoint&gt;&lt;/MRectangle&gt;&lt;/MDocument&gt;
	&lt;/cml&gt;
	</PRE>
<P>
	Example of MRV file exported with option "P": 
	</P>
<PRE STYLE="margin-left: 1cm; margin-right: 1cm">
	&lt;?xml version="1.0"?&gt;
	&lt;cml&gt;
	&lt;MDocument&gt;
	  &lt;MChemicalStruct&gt;
	    &lt;molecule title="Ethane" molID="m1"&gt;
	      &lt;atomArray atomID="a1 a2" elementType="C C" x2="0.0 0.0" y2="0.0 1.54"/&gt;
	      &lt;bondArray&gt;
	        &lt;bond atomRefs2="a1 a2" order="1"/&gt;
	      &lt;/bondArray&gt;
	    &lt;/molecule&gt;
	  &lt;/MChemicalStruct&gt;
	  &lt;MRectangle id="o2"&gt;
	    &lt;MPoint x="-6.599999904632568" y="1.7050000429153442"/&gt;
	    &lt;MPoint x="-2.640000104904175" y="1.7050000429153442"/&gt;
	    &lt;MPoint x="-2.640000104904175" y="-0.16500000655651093"/&gt;
	    &lt;MPoint x="-6.599999904632568" y="-0.16500000655651093"/&gt;
	  &lt;/MRectangle&gt;
	&lt;/MDocument&gt;
	&lt;/cml&gt;
	</PRE>
<P>
	Example of MRV file exported with options "SP": 
	</P>
<PRE STYLE="margin-left: 1cm; margin-right: 1cm">
	&lt;?xml version="1.0"?&gt;
	&lt;cml&gt;
	&lt;MDocument&gt;
	  &lt;MChemicalStruct&gt;
	    &lt;molecule title="Ethane" molID="m1"&gt;
	      &lt;atomArray atomID="a1 a2" elementType="C C" isSelected="true true" x2="0.0 0.0" y2="0.0 1.54"/&gt;
	      &lt;bondArray&gt;
	        &lt;bond atomRefs2="a1 a2" order="1"/&gt;
	      &lt;/bondArray&gt;
	    &lt;/molecule&gt;
	  &lt;/MChemicalStruct&gt;
	  &lt;MRectangle id="o2" isSelected="true"&gt;
	    &lt;MPoint x="-6.599999904632568" y="1.7050000429153442"/&gt;
	    &lt;MPoint x="-2.640000104904175" y="1.7050000429153442"/&gt;
	    &lt;MPoint x="-2.640000104904175" y="-0.16500000655651093"/&gt;
	    &lt;MPoint x="-6.599999904632568" y="-0.16500000655651093"/&gt;
	  &lt;/MRectangle&gt;
	&lt;/MDocument&gt;
	&lt;/cml&gt;
	</PRE>
</body>
</html>
