<html>
<head>
<meta NAME="description" CONTENT="PDB in Marvin">
<meta NAME="keywords" CONTENT="PDB, Java, Marvin">
<meta NAME="author" CONTENT="Peter <PERSON>">
<link REL ="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css" TITLE="Style">
<title>PDB file format in Marvin</title>
</head>
<body>

<h1>Protein Data Bank format</h1>

<p>
Codename: <strong>pdb</strong>
</p>
<h2>Contents</h2>
<ul>
    <li><a href="#import">PDB import</a></li>
    <li><a href="#export">PDB export</a></li>
    <li><a href="#reference">Reference</a></li>
</ul>

<a name="import"></a><h2>Import from PDB format</h2>
<p>PDB files complying the PDB Contents Guide version 2.3 are processed by <PERSON>, though with some negligible
<a href="#importlimitations">limitations</a>. PDB files produced by various 3rd party applications may not comply the
PDB standard. Most of these files are also properly handled though there might be exceptions.<br>
All covalent bonds in proteins and in nucleic acids are properly assigned, but hydrogen bonds,
sulphur and water bridges,
coordinated bonds are not recognised yet. Covalent bonds in hetero groups are perceived based on geometry,
bond types are guessed with some errors. Hydregon atoms are identified and bonded to the appropriate
heavy atom eiter in chains, in hetero groups as well as in water molecules.<br>
Multiple models are properly processed as well as insertions and modified residues.</p>

<p>
Import options can be specified in the <a href="../applications/molconvert.html#usage">format string</a>.
The format descriptor and the options are separated by a colon. Options listed below are available for PDB import.</P>


<blockquote>
<table CELLSPACING=0 CELLPADDING=5 border="0">
<tr VALIGN="TOP">
    <td><code>H</code> or <code>+H</code></td>	<td>Add explicit hydrogen atoms. Usage: <code>"PDB:H"</code></td>
</tr>
<tr VALIGN="TOP">
    <td><code>-H</code></td>     	<td>Remove explicit hydrogen atoms. Usage: <code>"PDB:-H"</code></td>
</tr>
 <tr VALIGN="TOP">
    <td><code>c</code>&nbsp;&nbsp;&nbsp;&nbsp;
    </td>
    <td>Omit CONECT records for hetero compounds. Bonds are detected by the PDB reader modul based on local geometry
        unless the <code>b</code> option is specified.
        Usage: <code> &quot;pdb:c&quot;</code></td></tr>
    <tr VALIGN="TOP">
       <td><code>b</code>&nbsp;&nbsp;&nbsp;&nbsp;
       </td>
       <td>Do not recognise bond order. All bonds either defined by CONECT records or generated by
           PDB import are represented as ANY bonds. Usage: <code> &quot;pdb:b&quot;</code></td></tr>
</table>
</blockquote>

<a name="importlimitations"></a><h3>Limitations</h3>
<p>Standard record types listed below are not recognised by the current version of PDB import:
<ul>
    <li>Optional:
     <code>OBSLTE</code>, <code>CAVEAT</code>, <code>SPRSDE</code>, <code>JRNL</code>, <code>REMARK</code>,
     <code>SEQADV</code>, <code>FTNOTE</code>, <code>HETSYN</code>, <code>FORMUL</code>, <code>SSBOND</code>,
     <code>LINK</code>, <code>HYDBND</code>,
     <code>SLTBRG</code>, <code>CISPEP</code>, <code>SITE</code>, <code>MTRIX1</code>, <code>MTRIX2</code>,
     <code>MTRIX3</code>, <code>TVECT</code>, <code>SIGATM</code>, <code>ANISOU</code>, <code>SIGUIJ</code></li>
    <li>Mandatory: <code>CRYST1</code>, <code>ORIGX1</code>, <code>ORIGX2</code>, <code>ORIGX3</code>,
                   <code>SCALE1</code>, <code>SCALE2</code>, <code>SCALE3</code>, <code>MASTER</code></li>
</ul>
The recognition and proper processing of these record types will be implemented in forthcoming releases on demand.</p>



<a name="export"></a><h2>Export to PDB format</h2>
<p>Marvin exports simplified PDB files containing record types listed below: 


<ul>
<li>Title section:
    <ul>
    <li><code>HEADER</code> contains the following fields:
	classification=&quot;<code>PROTEIN</code>&quot;
	(or imported value), date,
	idCode=&quot;<code>NONE</code>&quot; (or imported value).</li>
    <li><code>TITLE</code>, <code>SOURCE</code>, <code>KEYWDS</code>,
	<code>EXPDTA</code>: The imported value is exported.
	Default: &quot;<code>NULL</code>&quot;.</li>
    <li><code>COMPND</code>: The imported value is exported. Default:
	&quot;<code>MOLECULE: </code>name&quot;,
	where &quot;name&quot; is the molecule name.</li>
    <li><code>AUTHOR</code>: The imported value is exported. Default:
	&quot;<code>Marvin</code>&quot;.</li>
    <li><code>REVDAT</code>: The following line plus the imported value.<br>
	<code>REVDAT&nbsp;&nbsp;&nbsp;N&nbsp;&nbsp;&nbsp;DD-MMM-YY&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;3</code><br>
	(N is the modification number,
	DD-MMM-YY is the date of the modification.)
	</li>
    </ul>
    </li>
<li>Coordinate section:
    <ul>
    <li><code>ATOM</code> and <code>HETATM</code>:
	The atom name includes the remoteness indicator and the branch
	designator character in case of amino acids. For non-standard residues,
	the atom name and the element symbol field contain the same value.
	The occupancy and the temperature factor are zero.
	The residue field contains one of the
	<a HREF="residues.html">standard residue</a> symbols.</li>
    </ul>
    </li>
<li>Connectivity section:
    <ul>
    <li><code>CONECT</code>: Only the first five fields are used.
	If the number of bonds is greater than four, a second CONECT line
	with the same atom serial number (first field) will be used.</li>
    <li><code>TER</code>: Indicates the end of a chain. Imported but not
	exported in the current version.
	</li>
    </ul>
    </li>
<li>Book keeping section:
    <ul>
    <li><code>MASTER</code></li>
    </ul>
    </li>
</ul></p>

<p>
Export options can be specified in the <a href="../applications/molconvert.html#usage">format string</a>.
The format descriptor and the options are separated by a colon. Options listed below are available for PDB output.</P>


<blockquote>
<table CELLSPACING=0 CELLPADDING=5 border="0">
<tr VALIGN="TOP">
    <td><code>H</code> or <code>+H</code></td>	<td>Add explicit hydrogen atoms. Usage: <code>"PDB:H"</code></td>
</tr>
<tr VALIGN="TOP">
    <td><code>-H</code></td>     	<td>Remove explicit Hydrogen atoms. Usage: <code>"PDB:-H"</code></td>
</tr>
<!-- <tr VALIGN="TOP">
    <td><code>-c</code>&nbsp;&nbsp;&nbsp;&nbsp;
    </td>
    <td>Omit CONECT record for hetero compounds. Usage: <code> &quot;pdb:-c&quot;</code></td></tr>       -->
</table>
</blockquote>


Limitations:
<ul>
<li>The exporter writes the atoms in the molecule object's internal atom order
    which may be different from the order of residues in a chain. Thus export
    is still not reliable for macromolecules with residues.</li>
</ul>
<p>

<h2><a class="anchor" name="reference">Reference</a></h2>
<ul>
<li><a HREF="http://www.wwpdb.org/documentation/format23/v2.3.html">PDB File Format - Contents Guide Version 2.3 (July 09, 1998)</a></li>

</ul>

</body>
</html>
