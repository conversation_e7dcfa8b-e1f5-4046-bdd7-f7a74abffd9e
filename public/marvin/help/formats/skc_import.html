<!-- 
    Document   : SKC_import
    Created on : March 10, 2010
    Author     : <PERSON><PERSON><PERSON>
-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <title>SKC Import options details</title>
    <link REL ="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css" TITLE="Style">
  </head>
  <body>
  <h1>SKC Import options details</h1>
  
 <h2><a name="atoms">Atoms</a></h2>
 <p>The following atom features from the SKC file format are imported to Marvin:</p>
  
  <p>  <table border="1">
<thead>
<tr>
<th>ISIS/Draw</th>
<th>Marvin</th>
</tr>
</thead>
<tbody>
<tr><td colspan="2"><b>General properties</b></tr>
<tr><td>Type</td><td>	Supported</td></tr>
<tr><td>Isotope	</td><td>Supported</td></tr>
<tr><td>Charge</td><td>Supported</td></tr>
<tr><td>Radical</td><td>Supported</td></tr>
<tr><td>Valence</td><td>Supported</td></tr>
<tr><td>Hydrogen display</td><td>not supported</td></tr>
<tr><td>Atom number</td><td>Supported</td></tr>
<tr><td>Number position</td><td>Supported</td></tr>
<tr><td>Value</td><td>Supported</td></tr>
<tr><td>Pseudo</td><td>Not supported</td></tr>
<tr><td>Alias</td><td>Not supported</td></tr>
<tr><td>Beilstein</td><td>Imported as Pseudo atom</td></tr>
<tr><td>Map</td><td>Supported</td></tr>

<tr><td colspan="2"><b>Query properties</b></tr>
<tr><td>No Implicit Hydrogens</td><td>Imported as 'Number of implicit hydrogens=0'</td></tr>
<tr><td>Unsaturated atoms (Unsaturated bond to atom)</td><td>Supported</td></tr>
<tr><td>Exact change[reaction]</td><td>Not supported</td></tr>
<tr><td>Ring bond count</td><td>Supported, <i>r*</i> imported as <i>rb*</i></td>
<tr><td>Reaction stereo</td><td>Supported</td>
<tr><td>Enhanced stereo</td><td>Supported</td>
<tr><td>Prevent hydrogen substituents</td><td>Supported</td>
<tr><td>Allow these atoms</td><td>Supported, imported as Atom List</td>
<tr><td>Prohibit these atoms</td><td>Supported, imported as Atom Not List</td>
<tr><td colspan="2"><b>Font properties</b></tr>
<tr><td>Font, size, bold, italic</td><td>Supported</td>
<tr><td>Underline, formula</td><td>Not supported</td>
<tr><td>Atom color</td><td>Supported</td>
</tbody>
</table>
</p>
 
 <h2><a name="bonds">Bonds</a></h2>
  <p>The following bond features from the SKC file format are imported to Marvin as:</p>
  
  <p>  <table border="1">
<thead>
<tr>
<th>ISIS/Draw</th>
<th>Marvin</th>
</tr>
</thead>
<tbody>
<tr><td colspan="2"><b>Bond type</b></tr>
<tr>
<td>Single</td><td>Supported</td></tr>
<tr>
<td>Double</td><td>Supported</td></tr>
<tr>
<td>Triple</td><td>Supported</td></tr>
<tr>
<td>Up</td><td>Supported (Single Up)</td></tr>
<tr>
<td>Down</td><td>Supported (Single Down)</td></tr>
<tr>
<td>Either</td><td>Supported (Single Up or Down)</td></tr>
<tr>
<td>Dbl Either</td><td>Supported (Double Cis or Trans)</td></tr>
<tr>
<td>Any</td><td>Supported</td></tr>
<tr>
<td>Aromatic</td><td>Supported</td></tr>
<tr>
<td>Single/Double</td><td>Supported (Single or Double)</td></tr>
<tr>
<td>Double/Aromatic</td><td>Supported (Double or Aromatic)</td></tr>
<tr>
<td>Single/Aromatic</td><td>Supported (Single or Aromatic)</td></tr>

<tr><td colspan="2"><b>Reacting center</b></tr>
<tr>
<td>Center</td><td>Supported</td></tr>
<tr>
<td>Make/Break</td><td>Supported</td></tr>
<tr>
<td>Change</td><td>Supported</td></tr>
<tr>
<td>Make/Change</td><td>Supported</td></tr>
<tr>
<td>Not Center</td><td>Supported</td></tr>
<tr>
<td colspan="2"><b>Topology</b></td></tr>
<tr>
<td>Ring</td><td>Supported</td></tr>
<tr>
<td>Chain</td><td>Supported</td></tr>
<tr>
<td colspan="2"><b>Other</b></td></tr>
<tr>
<td>Stereo search</td><td>Supported</td></tr>
<tr>
<td>Thickness</td><td>Supported</td></tr>
<tr><td>Color</td><td>Supported</td></tr>
<tr><td>Crossed bond</td><td>Supported</td></tr>
</tbody>
</table>
</p>

 <h2><a name="reaction">Reaction</a></h2>
<p>The following reaction features from the SKC file format are imported to Marvin as:</p>

<p>
<table border="1">
<thead>
<tr>
<th>ISIS/Draw</th>
<th>Marvin</th>
</tr>
</thead>
<tbody>
<tr><td>Reaction arrow</td><td>Supported, some types of reaction arrows are converted to single reaction arrows</td></tr>
<tr><td>Reactant</td><td>Supported</td></tr>
<tr><td>Product</td><td>Supported</td></tr>
<tr><td>Agent</td><td>Supported</td></tr>
<tr><td>Multiple step reaction</td><td>Not supported, converted to graphic arrows</td></tr>
<tr><td>Reaction and R-group combination</td><td>Supported</td></tr>
<tr><td>Reaction and S-group combination</td><td>Supported</td></tr>
<tr><td>Resonance	</td><td>Resonance arrow type</td></tr>
<tr><td>Equilibrium	</td><td>Equilibrium arrow type</td></tr>
<tr><td>Degree arrows	</td><td>Not yet supported, will be converted to electron flows</td></tr>

</tbody>
</table>
<p>Note that only one reaction arrow per file is imported because Marvin does not
support multistep reactions yet.</p>
 
 
 <h2><a name="groups">Groups</a></h2>
<p>The following groups from the SKC file format are imported to Marvin as:</p>

<p>
<table border="1">
<thead>
<tr>
<th>ISIS/Draw</th>
<th>Marvin</th>
</tr>
</thead>
<tbody>
<tr><td colspan="2"><b>S-groups</b>	</td></tr>
<tr><td>Anypolymer (anyp)	</td><td>Any polymer S-group type</td></tr>
<tr><td>Component (c)	</td><td>Component S-group type</td></tr>
<tr><td>Copolymer (co)	</td><td>Copolymer S-group type</td></tr>
<tr><td>Copolymer, alternating (alt)	</td><td>Copolymer S-group type with alternating polymer S-group subtype</td></tr>
<tr><td>Copolymer, block (blk)	</td><td>Copolymer S-group type with block polymer S-group subtype</td></tr>
<tr><td>Copolymer, random (ran)	</td><td>Copolymer S-group type with random polymer S-group subtype</td></tr>
<tr><td>Crosslink (xl)	</td><td>Crosslink S-group type</td></tr>
<tr><td>Generic ()	</td><td>Generic S-group type</td></tr>
<tr><td>Graft (grf)	</td><td>Graft S-group type</td></tr>
<tr><td>Mer (mer)	</td><td>Mer S-group type</td></tr>
<tr><td>Mixture, ordered (f)	</td><td>Mixture S-group type</td></tr>
<tr><td>Mixture, unordered (mix)	</td><td>Mixture S-group type</td></tr>
<tr><td>Modification (mod)	</td><td>Modification S-group type</td></tr>
<tr><td>Monomer (mon)	</td><td>Monomer S-group type</td></tr>
<tr><td>Multiple Group (#)	</td><td>Multiple group S-group type</td></tr>
<tr><td>SRU (n)	</td><td>Label and bracket type supported, repeat pattern not yet supported</td></tr>
<tr><td>Superatom S-group</td><td>Supported</td></tr>
<tr><td>Repeating unit with repetition ranges(e.g.2,4-6)</td><td>Supported</td></tr>
<tr><td>Attached data</td><td>Supported</td></tr>
<tr><td colspan="2"><b>R-groups</b>	</td></tr>
<tr><td>R-group</td><td>	Supported</td></tr>
<tr><td>Attachment points</td><td>	Supported</td></tr>
<tr><td>R-Logic</td><td>  Supported</td></tr>
</tbody>
</table>

<h2>Graphical objects</h2>
<p>
All graphical objects except for graphic arc and smooth spline are supported. Line style and Polygon Fill Color are not supported. All text box attributes are imported except for Underlined.

 <h2><a name="labels">Generic labels</a></h2>
<p>All generic labels are imported to Marvin:

<ul>
<li>Any atom (<i>A</i>)
<li>Any atom except carbon (<i>Q</i>)
<li>Any Metal (<i>M</i>)
<li>Any halogen (<i>X</i>)
<li>Any Generic
<li>Acyclic
<li>Acyclic hetero
<li>Acyclic alkenyl
<li>Acyclic Carbacyclic
<li>Alkoxy
<li>Alkyl
<li>Alkynyl
<li>Aryl
<li>Cyclic
<li>Carbocyclic
<li>Cycloalkenyl
<li>Cycloalkyl
<li>Heteroaryl
<li>Heterocyclic
<li>Cyclic no carbon
</ul>

 <h2><a name="">Attach data</a></h2>
<p>Import of Data to Atom, Data to Bond, Data to Molecule and Data to Brackets is supported.

<h2><a name="chemistry">Chemistry</a></h2>
<p>Import of Autoname, Link node, Create Chiral Flag and residue is supported.

<h2><a name="chiral">Chirality</a></h2>
<p> Import of CHIRAL and AND attributes is supported.

<h2><a name="chiral">Known Issues</a></h2>
<p> The import of the following features has not been implemented yet:
<ul>
	<li> Multicenter bond, </li>
	<li> Position variation bond, </li>
	<li> Hydrogen bond</li>
	<li> Coodination bond </li>
	<li> Variable Repeat Group</li>
</ul>
<h2><a class="anchor" NAME="ioptions">Import options</a></h2>

<blockquote>
<table cellspacing="5" cellpadding="0">
<tr VALIGN="TOP"><td><a class="text" NAME="ioption_Fsg"><strong>Fsg</strong></a></td>
    <td>Ungroup S-groups with 3 or more attachment points.</td></tr>
<tr VALIGN="TOP">
</table>
</blockquote>

<p><center><a href="skc-doc.html">Back to SKC file format page</a></center></p>

  </body>
</html>
