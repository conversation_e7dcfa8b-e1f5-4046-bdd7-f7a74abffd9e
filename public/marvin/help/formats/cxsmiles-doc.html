<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta NAME="description" CONTENT="Extended SMILES and SMARTS in Marvin">
<meta NAME="keywords" CONTENT="Extended SMILES, SMARTS, Java, Marvin">
<meta NAME="author" CONTENT="Andras Volford">
<link REL ="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css" TITLE="Style">
<title>Extended SMILES in Marvin</title>
</head>
<body>

<h1>Extended SMILES, SMARTS</h1>

<p>
Codename: <strong>cxsmiles</strong>,<strong>cxsmarts</strong>
</p>
<h2>Contents:</h2>
<ul>
<li><a href="#cxsmiles">Extended SMILES, SMARTS format</a></li>
<li><a href="#ioptions">Import options</a></li>
<li><a href="#options">Export options</a></li>
</ul>
<p>
<h2><a class="anchor" name="cxsmiles">Extended SMILES, SMARTS format</a></h2>
ChemAxon Extended SMILES/SMARTS is used for storing special features 
of the molecules after the <a HREF="smiles-doc.html">SMILES</a> string.
Any information can be stored after the SMILES string 
if it is separated by space or tab characters as the SMILES parsers ignore them 
or use them as comment. 
The extended features are stored in the following format:<br>
<code>SMILES_String |&lt;feature1&gt;,&lt;feature2&gt;,...|</code><br>

ChemAxon's extended SMILES/SMARTS does not contain non-ASCII characters,  
they are escaped in the usual form, "&#<i>n</i>;", with their character code, <i>n</i>.
The ASCII characters ',', ';', '|', '{', '}' and ':' in <a href="#datasgroup"> Data Sgroup information</a> are 
also escaped in this way. Moreover, the symbols '$', ';', '|', '{', '}' between dollar signs 
(see <a href="#atomiclabels"> Atom labels / aliases / values </a>) are coded in the above mentioned way as well.

<br>
The extended feature description is economic.
If some feature is missing in the molecule, then the corresponding special 
characters are not written. 
(Eg: If the atoms of the molecule has no alias strings at all, 
no &quot;$&quot; and &quot;;&quot; characters are written.) 
Moreover, if no feature of the molecule to be written, 
the extended feature field is omitted.<br>

Please note that the SMILES string part generated in cxsmiles format is not 
always the same as the one generated by smiles output. Eg: In case of Ferrocene 
the coordinate bonds are not exported to plain SMILES ([Fe].c1cccc1.c1cccc1), 
but they appear in the cxsmiles 
(c12c3c4c5c1[Fe]23451234c5c1c2c3c45 |C:4.5,0.6,1.7,2.8,3.9,7.12,6.10,9.16,10.18,8.14|).

<p>
In extended smiles export the following additional features are exported:
<ul>

<li>All aromatic atom are exported with lowercase letter 
in the SMILES string part.<br>
E.g. aromatic Boron is written with lowercase letter: 
b1ccccc1.
</li>

<li>Molecule absolute stereoconfiguration (For detailed
description see the <a href="http://www.chemaxon.com/jchem/doc/user/query_stereochemistry.html">
Stereochemistry</a> section of the Query guide in JChem Base.)
<p>
The relative stereoconfiguration is stored as &quot;<b>r</b>&quot;. 
If a reaction contains components with absolute and relative stereo the indexes of the fragments with relative configuration is written.  
The absolute stereoconfiguration is the default, which is not marked. 
(Absolute stereoconfiguration known also as "Chiral flag" in MDL molfiles. )
<br>
Example: "r:2,4,5"
</p>
</li>


<li>Enhanced stereochemical representation (For detailed
description see the <a href="http://www.chemaxon.com/jchem/doc/user/query_stereochemistry.html">
Stereochemistry</a> section of the Query guide in JChem Base.)
<p>
The following stereochemical group types are stored:
</p>
    <ul>
	<li>Absolute stereo group type. <br>
	    <b>a:</b>&lt;atomindex&gt;,&lt;atomindex&gt;...
	</li>
	<li>OR stereo group type.<br>
	    <b>o</b>&lt;group&gt;<b>:</b>&lt;atomindex&gt;,&lt;
	    atomindex&gt;...</li>
	<li>AND stereo group type.<br>
	    <b>&</b>&lt;group&gt;<b>:</b>&lt;atomindex&gt;,&lt;
	    atomindex&gt;...</li>
    </ul>
</li>


<li> <a name="atomiclabels"> Atom labels / aliases / values </a>
<p>
Atom labels / aliases are written between &quot;<b>$</b>&quot; characters 
each label is separated by &quot;;&quot; characters.<br>
Atom values are written after &quot;<b>$_AV:</b>&quot; separated by 
semicolon characters and closed with &quot;<b>$</b>&quot; tag. 
Special chacters are <a href="#escaping">escaped.</a>
</p>
</li>

<li>Single &quot;Up or Down&quot; (Wiggly), UP and DOWN bonds
<p>
Atom indexes relating to wiggly bonds are written after &quot;<b>w:</b>&quot; 
followed by a dot character and the wiggly bond index.
The wiggly bonds are separated by commas.<br>
If atomic coordinates are also exported, then UP bonds are written
after &quot;<b>wU:</b>&quot;
DOWN bonds are written after &quot;<b>wD:</b>&quot; in a similar way to
wiggly bond export.
</p>
</li>

<li>CIS, TRANS, UNSPEC bond info for double bonds in rings
<p>
Bond indexes of the double bonds in SSSR are written. <br>
The bond stereo information is generated as the following: 
the double bond has the representation a1-a2=a3-a4, where <br>
</p>
<UL>
<LI> a1 is the smallest atom index of the generated smiles connected to a2
<LI> a2 is the double bond smaller atom index in the generated smiles
<LI> a3 is the double bond larger atom index in the generated smiles
<LI> a4 is the smallest atom index of the generated smiles connected to a3
</UL>
The CIS double bond indexes are written after &quot;<b>c:</b>&quot;, <br>
the TRANS double bond indexes are written after &quot;<b>t:</b>&quot;, <br>
the double bond indexes with UNSPEC flag are written after &quot;<b>ctu:</b>&quot;.
</li>

<li>Fragment level grouping of reactant, agent and product fragments
    Grouped fragment indexes are written after &quot;f:&quot; in the following
    format:<br>
    <UL>
    <LI> Connected groups are separated by &quot;,&quot;.
    <LI> A connected group is a &quot;.&quot; separated list of fragment indices.
    </UL><br>
    Example: &quot;f:0.1,5.6&quot;
</li>

<li>Local parity information
<p>
Atom indexes with local ODD parity are written after &quot;<b>@:</b>&quot;,
while atom indexes with local EVEN parity are written 
after &quot;<b>@@:</b>&quot;
characters separated by commas.
</p>
</li>

<li>Local bicyclo-alkane stereo information (local syn/anti, endo/exo representation)
<p>
For each ligand connected to non-bridgehead atoms of bicyclo-alkanes, if they are 
in a syn/anti or endo/exo position (ligand is not in the plane of the bridge to which 
it is connected), their relative position in the ring system is stored by their 
position in relation to the bridges to which they are not connected. Bridges are 
identified by the indexes of the contained atoms: <i>higher bridge</i> 
is the one with the highest atom index, the other is the <i>lower bridge</i>. The 
ligand's position can be:
<ul>
<li> towards higher bridge (THB), if it lies towards the higher bridge regarding the plane 
of the connected bridge
<li> towards lower bridge (TLB), if it lies towards the lower bridge regarding the plane 
of the connected bridge
<li> towards either bridge (TEB), if its position is not determined, it can lie towards both 
of the bridges (it is connected by a wiggly bond)
</ul>
</p>
The order of the stored information: at first each THB, than each TLB, 
in the end each TEB description is written in the following form:
<ul>
<li> &quot;<b>THB</b>&quot;, followed by &quot;<b>:</b>&quot;, than for each THB ligand as follows:
<ul>
<li> index of the ligand atom for which bicyclo stereo description is stored, followed by
&quot;<b>:</b>&quot; 
<li> index of the ring atom in the bicyclo-alkane to which the ligand is connected (connection 
atom), followed by &quot;<b>:</b>&quot; 
<li> list of indexes in the lower bridge, separated by 
&quot;<b>.</b>&quot; and followed by &quot;<b>:</b>&quot; 
<li> list of indexes in the higher bridge, separated by 
&quot;<b>.</b>&quot; 
<li> stereo description of separate ligands are separated by &quot;<b>,</b>&quot; 
</ul>
<li> the same lists are present for THB and TEB atoms, separated by &quot;<b>,</b>&quot;. 
</ul>
If a ligand has more than one stereo information (e.g. it is connected to more than one bicyclo-alkanes)
then it appears twice in the list with the adequate stereo description.
</p>
<p>
Examples:
<table><tr><td width="40%">[H][C@]12CCC[C@]([H])(CC(C)C1)C2(S)Cl <b>|r,TLB:13:11:2.4.3:7.10.8,THB:12:11:2.4.3:7.10.8,9:8:11:2.4.3|</b></td>
        <td><img src="cxsmiles1.png" alt="cxsmiles1"/></td></tr>
    <tr><td>[H][C@]12COC[C@]([H])(C[C@@H](CCCC)C1)[C@@]2(Cl)C1CCCC1 <b>|r,TLB:15:14:2.4.3:7.13.8,THB:16:14:2.4.3:7.13.8,9:8:14:2.4.3|</b></td>
        <td><img src="cxsmiles2.png" alt="cxsmiles2"/></td></tr>
</table>
</p>
</li>

<li>Radical numbers
<p>
Atom indexes with 
<ul>
<li> divalent radical center are written after &quot;<b>^2:</b>&quot;,
<li> divalent singlet radical center are written after &quot;<b>^3:</b>&quot;,
<li> divalent triplet radical center are written after &quot;<b>^4:</b>&quot;,
<li> trivalent radical center are written after &quot;<b>^5:</b>&quot;,
</ul>
characters separated by commas.
</li>

<li>Lone electron pairs
<p>
The indexes of the atoms having bond connected lone electron pairs are 
written after 
&quot;<b>LP:</b>&quot;.
</p>
<p>
The indexes of the atoms followed by a colon character and the number of
explicit lone electron pairs are written after 
&quot;<b>lp:</b>&quot;.<br>
(See <a href=" http://www.chemaxon.com/marvin/examples/applets/sketch/studentexam/index.html">live example</a>.)
</p>
<p>
Example: &quot;LP:1,lp:0:1,2:2&quot;
</p>
</li>

<li>Multicenter SGroups and coordinate bonds
<p>
The multicenter atom indexes written after &quot;<b>m:</b>&quot; 
followed by a colon character and the indexes of the atoms which forms 
the given SGroup separated by &quot;<b>.</b>&quot;. 
The SGroups are separated by commas.
</p>
<p>
Atom indexes relating to coordinate bond indexes are written after 
&quot;<b>C:</b>&quot; followed by a dot character 
and the coordinate bond index.
The coordinate bonds are separated by commas.<br>
In the smiles part of cxsmiles the atom-to-atom coordinate bonds are 
represented by single bonds, which are corrected according to the 
C information at the extended part.
</p>
Example: &quot;m:0:7.6.5.4.3,2:12.11.10.9.8,C:0.0,2.1&quot;
</li>

<li>Link nodes
<p>
The link node atom indexes are written after &quot;<b>LN:</b>&quot;
followed by a colon character, the minimum repetitions, maximum repetitions,
the node first and second outer atom indexes separated by &quot;<b>.</b>&quot;.
If the link node has only two connections, then the first and second outer atom 
indexes are obvious, so they are omitted.
The link nodes separated by commas.
</p>
Example: &quot;LN:1:*******,6:*******,9:*********&quot;
</li>

<li>Atomic coordinates
<p>
The atomic coordinates are written between parentheses. 
Each atomic coordinate triplet (x, y, z) is separated by semicolon, and the
x y z coordinates are separated by commas. Zero coordinates are omitted.<br>
Note: The CIS/TRANS information is redundant in this case. It is specified 
in the SMILES string and also in the atomic coordinates. The 
atomic coordinates has priority over the SMILES string.
</p>
</li>
<li>
    <a name="datasgroup">Data Sgroup information </a>
    <p>
        Atomic indexes in the data sgroup are written after
        &quot;<b>SgD:</b>&quot; followed by 
        field name, data value, query operator, unit, tag
        and coordinates in parenthesis if necessary, separated by
        colon characters.  The field values with special characters are <a href="#escaping">escaped</a>. If atomic coordinates are exported (with
        <b>c</b> option) (-1) is used in the coordinate field
        for data sgroup attached to the atoms.<br>
            Example: &quot;SgD:3,2,1,0:name:data:like:unit:t:(-1)&quot;
    </p>
</li>
<li>
    R-group attachment point information
    <p>
    The R-group attachment point is written explicitly as ANY atom into the SMILES 
    string. 
    The order of attachment point is written as alias string 
    (see above) after &quot;<b>_AP</b>&quot; separated by semicolon characters.
    Before version 5.4 only two attachment point type was supported, the
    attachment point was not exported to the SMILES string explicitly.
    In the extended part the atomic indexes of the attachment points written 
    after &quot;AP_x:&quot; format was used, where x denoted 
    attachment type 1, 2 or 3 for attachment points 1, 2 or both.<br>
    Example: &quot;C[C@H](N*)C(*)=O |$;;;_AP1;;_AP2;$|&quot;, before version 5.4: &quot;C[C@H]([NH])[C]=O |AP_1:2,AP_2:3|&quot;
    </p>
</li>
<li>
   S-group attachment point information
   <p>
    S-group attachment point informations are not handled by cxsmiles or cxsmarts.
   </p>
</li>
<li>
    Rgroups
    <p>Rgroup information can be exported to extended cxsmiles/cxsmarts. Rgroups in
    the molecule is exported to ANY atom in the SMILES part, they are described in
    the alias part as "_Rn". Rgroup descriptions (molecules) are enumerated also in
    the extended part after &quot;<b>RG</b>&quot; followed by a colon character.
    <ul>
       <li>The Rgroup number is written after &quot;<b>_R</b>&quot;
        followed by &quot;<b>=</b>&quot; (eg. _R1=)</li>
       <li>The Rgroup description is written as cxsmiles/cxsmarts in braces
       &quot;<b>{}</b>&quot;</li>
       <li>Members of the same Rgroup are seperated by commas</li>
       <li>Different Rgroups are seperated by commas.</li>
    </ul>
    Examples
    <ul>
      <li>C1O[*]CO[*]1 |$;;_R2;;;_R1$,RG:_R1={C},{N},_R2={C},{N}|</li>
      <li>Cl[*](Br)I |$;_R1;;$,RG:_R1={*CCCC(C*)CC* |$_AP3;;;;;;_AP2;;;_AP1$|},{*CCCN(C*)CC* |$_AP3;;;;;;_AP2;;;_AP1$|},LO:1:0.2.3|</li>
    </ul></p>
</li>
<li>
     Ligand order
     <p>Ligand order information can be exported to extended cxsmiles/cxsmarts after
     &quot;<b>LO</b>&quot; followed by a colon character.
     <ul>
        <li>First the center atom's index is exported followed by a colon</li>
        <li>After that all atom's indexes connected to the central atom is written
         in ligand order sperated by &quot;<b>.</b>&quot;</li>
        <li>The different ligand order informations are seperated by comma.</li>
        <li>Eg: LO:centerIdx1:idx1.idx2.idx3,centerIdx2:idx1.idx2...</li>
     </ul>
     Example
     <ul><li>Cl[*](Br)I |$;_R1;;$,RG:_R1={*CCCC(C*)CC* |$_AP3;;;;;;_AP2;;;_AP1$|},{*CCCN(C*)CC* |$_AP3;;;;;;_AP2;;;_AP1$|},LO:1:0.2.3|</li></ul></p>
</li>
<li>
     Pseudo Atoms
     <p>
        Pseudo atoms can be exported to extended cxsmiles/cxsmarts. 
        They are described in the alias part as "<code><i>pseudo</i>_p</code>", 
        where <i>pseudo</i> is the value of the pseudo atom. 
     </p>
     <p>
     Example:
     <blockquote>
     <table summary="Examples for pseudo atoms">
       <tr>
         <td> CCCC* |$;;;;pseudo_p$|        </td>
         <td><img src="cxsmiles3.png" alt="cxsmiles3"/> </td>
       </tr>    
     </table>
     </blockquote>
</li>
<li>
     Special Atoms
     <p>
       Special atoms
       <a href="http://www.chemaxon.com/jchem/doc/user/query_features.html#generatom"> 
       AH, QH, M, MH, X, XH</a> and
       <a href="http://www.chemaxon.com/jchem/doc/user/query_features.html#pseudo_atoms">
       Pol</a>,
       are exported to cxsmiles/cxsmarts as pseudo atoms, 
       <i>i.e.</i> AH_p, QH_p, M_p, MH_p, X_p, XH_p, and Pol_p, 
       respecively. Special atoms <i>Q</i> and <i>star </i> 
       are exported as Q_e and star_e, 
       respectively.         
       Special atom A can be handled by SMILES export, therefore it is not
       written to the alias part of the cxsmiles/cxsmarts.
     </p>
     <p>
       Examples:
       <blockquote>
       <table summary="Examples for special atoms">
         <tr>
           <td> *C(*)CC(*)CC(*)* |$;;Pol_p;;;Q_e;;;star_e;M_p$| </td>
           <td><img src="cxsmiles4.png" alt="cxsmiles4"/>       </td>
         </tr>    
         <tr>
           <td> *C(*)CC(*)CC(*)* |$Q_e;;AH_p;;;X_p;;;QH_p;XH_p$|</td>
           <td> <img src="cxsmiles5.png" alt="cxsmiles5"/></td>
         </tr>
        </table> 
       </blockquote>
     </p>
</li>
<li>
	MDL Query features
	<p> Ring bond count (rb), Substitution count (s) and unstaturated atom (u) are exported in the following form:
	<br>
	   rb:atomIndex1:value,atomIndex2:value<br>
	   s:atomIndex1:value,atomIndex2:value<br>
	   u:atomIndex1,atomIndex2,atomIndex3<br>
	   Examples: "rb:1:2,2:*,4:2", "u:3,4,5"
</li>
<li>
	Polymer Sgroups <p>
	
	Each Sgroup exported after &quot;<b>Sg:</b>&quot; in  fields separated by a colon.   Fields are:
	<ol>
	<li>Sgroup type keyword. 
		Valid keywords are:
		<table border="1">
		<tr>
		<td>Keyword</td><td>Sgroup Type</td></tr>
		<tr><td> n</td><td> SRU</td></tr>
		<tr><td>mon</td><td>monomer</td></tr>
		<tr><td>mer</td><td>mer</td></tr>
		<tr><td>co</td><td>copolymer</td></tr>
		<tr><td>xl</td><td>crosslink</td></tr>
		<tr><td>mod</td><td>modification</td></tr>
		<tr><td>mix</td><td>mixture</td></tr>
		<tr><td>f</td><td>formulation</td></tr>
		<tr><td>any</td><td>anypolimer</td></tr>
		<tr><td>gen</td><td>generic</td></tr>
		<tr><td>c</td><td>component</td></tr>
		<tr><td>grf</td><td>graft</td></tr>
		<tr><td>alt</td><td>alternating copolymer</td></tr>
		<tr><td>ran</td><td>random copolymer</td></tr>
		<tr><td>blk</td><td>block copolymer</td></tr>
		</table>
	</li>
	<li>
	Atom indexes separated with commas.
	</li>
	<li>
	Subscript of the Sgroup. If the supscript equals the keyword of the Sgroup this field can be empty. <a href="#escaping">Escaped</a> field.
	</li>
	<li>
	Superscript of the Sgroup. In the superscript only connectivity and flip information is allowed. This field can be empty. <a href="#escaping">Escaped</a> field.
	</li>
	<li>
	Head crossing bond indexes. The indexes of bonds that share a common bracket in case of ladder-type polymers. This field can be empty.
	</li>
	<li>
	Tail crossing bond indexes. The indexes of bonds that share a common bracket in case of ladder-type polymers. This field can be empty.
	</li>
	<li>
	 If the <a href=#option_c>c</a>  export option is present then bracket  orientation,  bracket type followed by the coordinates (4 pair, separated with commas). 
	 Bracket orientation can be s or d (single or double), bracket type can be b,c,r,s for braces, chevrons, round and square, respectively. The brackets are
	  written between parentheses and separated with semicolons.
	</li>
	</ol>
	A colon is needed after the last non-empty field. <br>
	If one needs to retain not only the chemically relevant information, but the whole structure (as drawn), then the <a href=#option_c>c</a>  export option
	should be used. <br>
	 
	Examples: <br>
		CCCC |Sg:gen:0,1,2:|<br>
		CCCC |Sg:n:0,1,2:3-6:eu|<br>
		*CC(*)C(*)N* |$star_e;;;star_e;;star_e;;star_e$,Sg:n:6,1,2,4::hh&amp;#44;f:6,0,:4,2,|<br>
		C1=CC=CC=C1 |c:0,2,4,(-4.62,1.05,;-3.29,.28,;-3.29,-1.27,;-4.62,-2.04,;-5.95,-1.27,;-5.95,.28,),Sg:mon:0,5,4,3,2,1:::::(d,s,-7.03,2.12,-2.21,2.12,-2.21,-3.11,-7.03,-3.11,)|
		
</ul>
<p>
<h4><a class="anchor" name="escaping">Escaping</a></h4>
In some places special characters are escaped to '&#<i>code</i>' where <i>code</i> 
is the ASCII code of the special character. <br>
Not escaped characters in fields of Sgroups and DataSgroups: 'a'-'z', 'A'-'Z', '0'-'9' and '&gt;&lt;\&quot;!@#$%()[]./\\?-+*^_~=' and the space character <br>
Not escaped characters in atom labels and atom values: 'a'-'z', 'A'-'Z', '0'-'9' and '&gt;&lt;\&quot;!@#%()[]./\\?-+*^_~=,:' and the space character
<h3><a class="anchor" NAME="ioptions">Import options</a></h3>
<blockquote>
<table CELLSPACING=0 CELLPADDING=5 border="0">
<tr VALIGN="TOP">
    <td><a class="text" NAME="ioption_s"><strong>s</strong></a><br>
	</td>
    <td> Fix chiral flag from cxsmiles input.<br>
         By default the molecule absolute stereoconfiguration 
         (relative or absolute chirality - chiral flag)
         is specified at the extended part of the cxsmiles string.
         If it is missing it is assumed to be absolute by default 
         (see Molecule absolute stereoconfiguration above).
         Using the 's' option the molecule's absolute stereoconfiguration
         is tried to be figured out.<br>
         Example: molconvert cxsmiles -s 'C[C@H]1CC[C@@H](C)CC1{cxsmiles}' results C[C@H]1CC[C@@H](C)CC1<br>
         But: molconvert cxsmiles -s 'C[C@H]1CC[C@@H](C)CC1{cxsmiles:s}' results C[C@H]1CC[C@@H](C)CC1 |r|
	</td>
    </tr>
</table>
</blockquote>
<p>
    See also <a HREF="smiles-doc.html#ioptions">SMILES import options</a>.

<p>
<h3><a class="anchor" NAME="options">Export options</a></h3>

<p>
Export options can be specified in the format string. The format descriptor
and the options are separated by a colon. 
All options have default values (see below).
Using the &quot;+&quot; or &quot;-&quot; sign the default export values 
can be changed to &quot;true&quot; or &quot;false&quot; respectively. If the option is given without &quot;+&quot; or &quot;-&quot; modifier then the 
default values are not used and only the specific feature is exported.
<br>
Examples: <br>
&quot;cxsmiles:&quot; writes all default features 
(absolute stereoconfiguration, enhanced
stereo features, atom labels, wiggly bond indexes, ring stereo bond info and
reaction fragment level grouping),<br>
&quot;cxsmiles:lc&quot; writes the atom labels and the atomic coordinates only,<br>
&quot;cxsmiles:+c&quot; writes writes all default features and the atomic coordinates,<br>
&quot;cxsmiles:-le&quot; writes absolute stereoconfiguration, enhanced
stereo features, ring stereo bond info and
reaction fragment level grouping but not atom labels and 
wiggly bond indexes.

<blockquote>
<table CELLSPACING=0 CELLPADDING=0 border="0">
<tr VALIGN="TOP">
    <td><a class="text" NAME="option_u"><strong>u</strong></a></td>
    <td>Write unique cxsmiles output. (Includes unique smiles string.)<br>
    Enhanced stereo information are also stored in unique format. <br>
    Default value: <i>false</i>.
    </td></tr>
<tr VALIGN="TOP">
    <td><a class="text" NAME="option_e"><strong>e</strong></a></td>
    <td>Write relative stereo configuration and enhanced stereo features. Default value: <i>true</i>.
    </td></tr>
<tr VALIGN="TOP">
    <td><a class="text" NAME="option_l"><strong>l</strong></a></td>
    <td>Write atom labels / aliases / values. Default value: <i>true</i>.</td></tr>
<tr VALIGN="TOP">
    <td><a class="text" NAME="option_w"><strong>w</strong></a></td>
    <td>Write wiggly and in case of atomic coordinate export also
    UP and DOWN bond indexes. Default value: <i>true</i>.  </td></tr>
<tr VALIGN="TOP">
    <td><a class="text" NAME="option_d"><strong>d</strong></a></td>
    <td>Write CIS, TRANS ring bond indexes. Default value: <i>true</i>.</td></tr>
<tr VALIGN="TOP">
    <td><a class="text" NAME="option_f"><strong>f</strong></a></td>
    <td>Reaction fragment level grouping. Default value: <i>true</i>.</td></tr>
<tr VALIGN="TOP">
    <td><a class="text" NAME="option_p"><strong>p</strong></a></td>
    <td>Write local parities. Default value: <i>true</i>.</td></tr>
<tr VALIGN="TOP">
    <td><a class="text" NAME="option_R"><strong>R</strong></a></td>
    <td>Write radical numbers. Default value: <i>true</i>.</td></tr>
<tr VALIGN="TOP">
    <td><a class="text" NAME="option_LL"><strong>L</strong></a></td>
    <td>Write lone electron pairs. Default value: <i>true</i>.</td></tr>
<tr VALIGN="TOP">
    <td><a class="text" NAME="option_m"><strong>m</strong></a></td>
    <td>Write multicenter SGroups and coordinate bonds. Default value: <i>true</i>.</td></tr>
<tr VALIGN="TOP">
    <td><a class="text" NAME="option_N"><strong>N</strong></a></td>
    <td>Write link nodes. Default value: <i>true</i>.</td></tr>
<tr VALIGN="TOP">
    <td><a class="text" NAME="option_c"><strong>c</strong></a>[p]</td>
    <td>Write atomic coordinates. 
    <i>p</i> can optionally specify the coordinate precision.
    If <i>p</i> is not specified, the default value 2 is used.  
    Default value: <i>false</i>.</td></tr>
<tr VALIGN="TOP">
    <td><a class="text" NAME="option_D"><strong>D</strong></a></td>
    <td>Write Data Sgroup information.
    Default value: <i>true</i>.</td></tr>
<tr VALIGN="TOP">
    <td><a class="text" NAME="option_BOM"><strong>BOM</strong></a></td>
    <td>Write the UTF-8 <i>byte order mark</i> (BOM), if the given or the system's encoding is UTF-8.
    Default value: <i>false</i>.</td></tr>
<tr VALIGN="TOP">
    <td><a class="text" NAME="option_q"><strong>q</strong></a></td>
    <td>Write MDL query features.
    Default value: <i>true</i>.</td></tr>
    <tr VALIGN="TOP">
    <td><a class="text" NAME="option_P"><strong>P</strong></a></td>
    <td>Write polymer Sgroups.
    Default value: <i>true</i>.</td></tr>
</table>
</blockquote>
<p>
    See also <a HREF="smiles-doc.html#options">SMILES export options</a>
    and <a HREF="basic-export-opts.html">basic export options</a>.

<p>
<h2>See also</h2>
<ul>
<li><a HREF="smiles-doc.html">SMILES and SMARTS</a></li>
<li><a HREF="../../examples/applets/sketch/studentexam/index.html">Explicit lone pair live example</a></li>
</ul>

</body>
</html>
