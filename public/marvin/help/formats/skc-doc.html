<!-- 
    Document   : skc-doc
    Created on : March 10, 2010
    Author     : <PERSON><PERSON><PERSON>
-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta NAME="description" CONTENT="SKC file format in Marvin">
    <meta NAME="keywords" CONTENT="SKC, file format, Java, Marvin">
    <link REL ="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css" TITLE="Style">
    <title>SKC format</title>
  </head>
  <body>
    
    <h1>SKC</h1>
    <p>
      Codename: <strong>skc</strong>
    </p>
    
    <p>The SKC format of ISIS/Draw is imported and exported by <PERSON>. Most properties of atoms, bonds, groups (R-group and S-groups), query structures and reactions are supported. 


<h2>Export and Import</h2>

    The main features:
    <ul>
   <li>Chemical structures (atom properties, labels, bond types)</li>
    <li>Formatted text</li>
    <li>R-groups</li>
    <li>S-groups </li>
    <li>Atom and bond query properties </li>
    <li>Graphical objects (rectangle, ellipse, graphical brackets)</li>
    <li>Reactions</li>
    </ul>

<p>Read the detailed <a href="skc_import.html">import</a> and <a href="skc_export.html">export</a> list.

<p>Note: 
<ul><li> Some graphical object or objects properties are not available in Marvin (e.g. graphics arc, line style of graphical objects and underlined text).
<li>Graphical brackets are not supported by ISIS/Draw, thus all brackets are imported as S-groups.
  </body>
</html>
