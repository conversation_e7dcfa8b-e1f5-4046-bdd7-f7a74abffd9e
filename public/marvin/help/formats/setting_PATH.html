<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta NAME="description" CONTENT="Seeting PATH">
<meta NAME="author" CONTENT="Efi Hoffmann">   
<link REL ="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css" TITLE="Style">
<title>Setting PATH
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
</head>
<body>
<h1>Setting PATH</h1>

<h2>Windows 7</h2>
<ol>
   <li><b>Start</b>&gt;<b>Control Panel</b>&gt;<b>System and Security</b>&gt;<b>System</b>.</li>
   <li>Select "Advanced system settings".</li>
   <li>Click on "Advanced" tab.</li>
   <li>Choose "Environment Variables".</li>
   <li>In the "System Variables window", add the location of OSRA to the value for PATH. If you do not have the item PATH, you can add a new variable named as "PATH"  and add the location of OSRA as the value.</li>
</ol>

<h2>Windows Vista</h2>
<ol>
   <li>Right click "My Computer" icon.</li>
   <li>Select "Properties" from context menu.</li>
   <li>Select "Advanced system settings".</li>
   <li>Click on "Advanced" tab.</li>
   <li>In the Edit windows, modify PATH by adding the location of the class to the value for PATH. If you do not have the item PATH, you can add a new variable named as "PATH"  and add the location of OSRA as the value.</li>
 </ol>  
<h2>Windows XP</h2>
<ol>
   <li><b>Start</b>&gt;<b>Control Panel</b>&gt;<b>System</b>&gt;<b>Advanced</b>.</li>
   <li>Click on Environment Variables, under System Variables.</li>
   <li>Click on PATH.</li>
   <li>In the Edit windows, modify PATH by adding the location of OSRA to the value for PATH. If you do not have the item PATH, you can add a new variable named as "PATH"  and add the location of OSRA as the value.</li>
 </ol>

</body>
</html>

   


