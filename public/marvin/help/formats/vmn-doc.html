<html>
<head>
<meta NAME="description" CONTENT="VMN in Marvin">
<meta NAME="keywords" CONTENT="VMN, Markush, DARC, Java, Marvin">
<meta NAME="author" CONTENT="Nora Mate">
<link REL ="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css" TITLE="Style">
<title>VMN file format in Marvin</title>
</head>
<body>

<h1>Markush DARC format</h1>

<p>
Codename: <strong>vmn</strong>
</p>
<h2>Contents</h2>
<ul>
    <li><a href="#import">VMN import</a>
        <ul>
	<li><a href="#features">Interpretation of VMN features</a></li>
	<li><a href="#abbrevgroups">Structure shortcuts (abbreviated groups)</a></li>
	<li><a href="#peptides">Amino acids (peptides)</a></li>
	<li><a href="#homologies">Superatoms (homology pseudo atoms)</a></li>
	<li><a href="#multipleattachments">Multiple R-group attachments</a></li>
	<li><a href="#formatcorrections">VMN format corrections</a></li>
	<li><a href="#importlimitations">Limitations</a></li>
	<li><a href="#ioptions">Import options</a></li>
	</ul>
    </li>
    <li><a href="#export">VMN export</a></li>
    <li><a href="#references">References</a></li>
</ul>

<a name="import"></a><h2>Import from VMN format</h2>
<p>Markush structure files complying the Markush DARC format are processed by Marvin, though with some 
<a href="#importlimitations">limitations</a>. Interpretation of the main VMN features are 
<a href="#features">listed below</a>. 
The AMN file is looked for in the directory of
the VMN file, with the same name and <code>.amn</code> extension 
(e.g. the AMN file of <code>46mrk001.vmn</code> is <code>46mrk001.amn</code>). 
The atom attributes and the AMN text notes are stored in Marvin atom properties, as <a href="#attributes">described below</a>.</p>

<a name="features"></a><h3>Interpretation of VMN features</h3>

<p>
<ul>
<li><p><b>Groups</b>: <code>G0</code> is read in as root structure while <code>G1</code>, <code>G2</code>, ...
are stored in corresponding R-groups <code>R1</code>, <code>R2</code>, ... The representation of
attachments is <a href="#multipleattachments">described below</a>.</p></li>
<li><p><b>Undefined attachment information</b> is stored in form of 
<a href="../sketch/sketch-basic.html#positionvariation">position variation (variable attachment) bonds</a>.</p></li>
<li><p><b>Moieties</b> are represented as 
<a href="../sketch/sketch-basic.html#repeatingunit">repeating units with repetition ranges</a>
with no crossing bonds, but ignored (ungrouped) during search and enumeration.</p></li>
<li><p><b>Multipliers</b> are represented as
<a href="../sketch/sketch-basic.html#repeatingunit">repeating units with repetition ranges</a>
with a single crossing bond, but ignored (ungrouped) during search and enumeration.</p></li>  
<li><p><b>Repeating units</b> other than moieties and multipliers are described using a numerotation (numbering) 
attribute of three digits (e.g. <code>100</code>) on the atoms of the repeating unit, with the appropriate 
repetition range text recorded in the corresponding AMN file (e.g. <code>M100=1-4</code>). 
Note, that there is a <a href="#rangelimitation">limitation</a> on the number of elements in a 
repetition range (at most <code>10</code>) and on the number of crossing bonds that can be processed 
(<code>2</code> or <code>4</code>).</p></li>
<li><p><a href="#abbrevgroups"><b>Structure shortcuts</b></a> are read in as specific built-in
<a href="../sketch/sketch-chem.html#sgroups">abbreviated groups (superatom groups)</a>.</p></li>
<li><p><a href="#peptides"><b>Amino acids</b></a> are read in by <a href="seq-doc.html">peptide import</a>, 
which uses built-in <a href="../sketch/sketch-chem.html#sgroups">abbreviated groups (superatom groups)</a> 
to represent peptides.</p></li>
<li><p><a href="#homologies"><b>Superatoms</b></a> are read in as pseudo-atoms and treated as homology groups.</p></li>
<li><p><b>Atom attributes</b>: we interpret the following VMN atom attributes:
    <ul>
    <li><b>AM - Abnormal mass</b> is stored in the mass number of the atom</li>
    <li><b>AV - Abnormal valence</b> is stored in AV atom property</li>
    <li><b>DL - Peptide attribute</b> is interpreted by <a href="seq-doc.html">peptide import</a></li>
    </ul></p>
</li>

<li><a name="attributes"><b>Homology atom attributes</b></a>: we store the following VMN homology atom attributes in Marvin atom properties:
<p>
<table cellpadding="3" cellspacing="2" border="1">
<tr><th>VMN attribute name</th><th>Marvin atom property name</th><th>property values</th></tr>
<tr><td align="center">DT - Deuterium-Tritium counts</td><td>DTCOUNT</td><td>D[deuterium count]T[tritium count] (e.g. D3T2)</td></tr>
<tr><td rowspan="4" align="center">CR - Carbon ring attributes</td><td>BRANCHING</td><td>BRA, STR</td></tr>
<tr>                                               <td>SIZE</td><td>LO, MID, HI, LO MID, MID HI, LO HI</td></tr>
<tr>                                               <td>SATURATION</td><td>SAT, UNS</td></tr>
<tr>                                               <td>RINGTYPE</td><td>MON, FU</td></tr>
<tr><td align="center">data in AMN</td><td>TEXTNOTES</td><td>AMN text referring to the atom (e.g. N0-4,S0-4)</td></tr>
</table>
</p>
<p>For the interpretation of these attributes, refer to the <a href="../calculations/homologygroups.html#homology_properties">Homology groups in Markush structures</a> manual.</p>
</li>
</ul>
</p>

<a name="abbrevgroups"></a><h3>Structure shortcuts (abbreviated groups)</h3>

<p>The following structure shortcuts (abbreviated groups) are supported:</p>
<p>
    <table cellspacing="10">
        </tr>
	    <td colspan="6">C2, C3, ..., C50</td>
        </tr>
	<tr>
	    <td>ACE</td><td>BU</td><td>CN</td><td>CO1</td><td>CO2</td>
        </tr>
        <tr>
	    <td>COI</td><td>ET</td><td>IBU</td><td>IPR</td><td>MBE</td><td>NBU</td>
        </tr>
        <tr>
	    <td>NO2</td><td>NPR</td><td>OBE</td><td>PBE</td><td>PH</td><td>PO3</td>
        </tr>
        <tr>
	    <td>PO4</td><td>SBU</td><td>SO2</td><td>SO3</td><td>TBU</td>
        </tr>
</table> 
</p>

<a name="peptides"></a><h3>Amino acids (peptides)</h3>

<p>The following standard amino acids (peptide abbreviated groups) are supported:</p>
<p>
    <table cellspacing="10">
	<tr>
	    <td>ALA</td><td>ARG</td><td>ASN</td><td>ASP</td><td>CYS</td><td>GLN</td>
        </tr>
        <tr>
	    <td>GLU</td><td>GLY</td><td>HIS</td><td>ILE</td><td>LEU</td><td>LYS</td>
        </tr>
        <tr>
	    <td>MET</td><td>PHE</td><td>PRO</td><td>SER</td><td>THR</td><td>TRY</td>
        </tr>
        <tr>
	    <td>TYR</td><td>VAL</td>
	</tr>
    </table>
</p>

<p>The following non-standard peptides are also supported:</p>
<p>
    <table cellspacing="10">
	<tr><td>ABU</td><td>aminobutyric acid</td></tr>
	<tr><td>ASU</td><td>aminosuberic acid</td></tr>
	<tr><td>GLP</td><td>pyroglumatic acid</td></tr>
	<tr><td>HCY</td><td>homocysteine</td></tr>
	<tr><td>HSE</td><td>homoserine</td></tr>
	<tr><td>NLE</td><td>norleucine</td></tr>
	<tr><td>NVA</td><td>norvaline</td></tr>
	<tr><td>ORN</td><td>ornithine</td></tr>
	<tr><td>SAR</td><td>sarcosine</td></tr>
	<tr><td>STA</td><td>statine</td></tr>
    </table>
</p>

<p>Note, that peptide connection bonds are not handled currently, 
therefore peptide sequences may not be correct.</p>

<p>For more information on peptide representation refer to the <a href="seq-doc.html">Peptide import</a> documentation.</p>

<a name="homologies"></a><h3>Superatoms (homology pseudo atoms)</h3>

<p>Superatoms representing homology groups are read in as pseudo atoms. The following homologies
are interpreted by enumeration and search:</p>
<p>
    <table cellspacing="10">
	<tr>
	    <td>CHK</td><td>CHE</td><td>CHY</td><td>CYC</td><td>ARY</td><td>HET</td>
        </tr>
        <tr>
	    <td>HEA</td><td>HEF</td><td>UNK</td><td>MX</td><td>AMX</td><td>A35</td>
        </tr>
        <tr>
	    <td>TRM</td><td>LAN</td><td>ACT</td><td>HAL</td><td>ACY</td><td>PRT</td>
	   	</tr>
        <tr>
	    <td>XX</td>
	</tr>
    </table>   
</p>
<p>For a detailed description of the interpretation, refer to the 
<a href="../calculations/homologygroups.html#definition">Homology groups in Markush structures</a>
manual.</p>

<a name="multipleattachments"></a><h3>Multiple R-group attachments</h3>

<p><b>From Marvin 5.4 multiple R-group attachments are fully supported by <a href="../sketch/sketch-basic.html#howto-draw.rgroups">the new R-group attachment representation</a>:</b></p>

<p><img src="vmn_1.png" alt="R-group attachment representation" width="412" height="329"></p>

<a name="formatcorrections"></a><h3>VMN format corrections</h3>
<ul>
<li>C0 is interpreted as CHK.</li>
<li>C1 is interpreted as C.</li>
<li>Deuterium/Tritium: we remove D and T ligands of the R-atoms.</li>
<li>Homology groups being in rings are transformed to XX.</li>
<li>R-group attachment bond type is transformed to parent R-atom bond type if different.</li>
</ul>

<a name="importlimitations"></a><h3>Limitations</h3>

<a name="rangelimitation"></a><h4>Repeating units with repetition ranges</h4>

<p>The number of elements in a repetition range is limited to <code>10</code> (e.g. range <code>M100=2,5- </code> is interpreted as <code>M100=2,5-13</code>). Repeating units with more than <code>4</code> crossing bonds are not processed by search and enumeration.</p>

<a name="missinghomologies"></a><h4>Superatoms (homologies) that are not supported</h4>

<p>The following superatoms (homologies) are not supported by search and enumeration
(but read in and displayed as pseudo-atoms):</p>

<p>
    <table cellspacing="10">
	<tr>
	    <td>POL</td><td>PEG</td><td>DYE</td><td>PRT</td>
        </tr> 
    </table>
</p>

<p>The detailed description of homology interpretation is described in the 
<a href="../calculations/homologygroups.html#definition">Homology groups in Markush structures</a>
manual.</p>


<a name="missingattributes"></a><h4>Atom attributes that are not processed by search and enumeration</h4>

<p>The following atom attributes are not processed by search and enumeration
(but displayed in atom labels):</p>
<p>
    <table cellspacing="5">
	<tr><td>PA</td><td>Polymer indicator</td></tr>
	<tr><td>SP</td><td>Position indicator</td></tr>
    </table>
</p>

<h3><a class="anchor" NAME="ioptions">Import options</a></h3>


<table CELLSPACING=0 CELLPADDING=5 border="0">
<tr VALIGN="TOP">
    <td><a class="text" NAME="ioption_o"><strong>o</strong></a>
	</td>
    <td>Import runs in the original mode. <a href="#formatcorrections">VMN format corrections</a> are not executed.
	</td>
    </tr>
</table>

<a name="export"></a><h2>Export to VMN format</h2>
<p>VMN export is not available yet.</p> 




<h2><a class="anchor" name="references">References</a></h2>
<ol>
<li>The Markush DARC Format, T Ferns, Internal Technical Report, Thomson Scientific, 2002</li>
<li><a href="http://science.thomsonreuters.com/m/pdfs/mgr/Markush_Darc_User_Manual.pdf">Derwent World Patents Index, Markush DARC User Manual, The Thomson Corporation, 1993, 2008</a></li>
</ol>

</body>
</html>
