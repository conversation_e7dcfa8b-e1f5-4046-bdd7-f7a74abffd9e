<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
  <meta name="description"
 content="Gaussian Input/Output format in Marvin">
  <meta name="keywords" content="Gaussian, Cube, Java, Marvin">
  <meta name="author" content="<PERSON><PERSON>, <PERSON>">
  <link rel="stylesheet" type="text/css" href="../marvinmanuals.css"
 title="Style">
  <title>Gaussian Input/Output format in Marvin</title>
</head>
<body bgcolor="#ffffff" text="#333366">
<h1>Gaussian Input/Output</h1>
<p>
Codename: <strong>gjf, gout</strong>
</p>
<h2>Contents</h2>
<ul>
  <li><a href="#gout-format">Gaussian Output (import: gout) format)</a></li>
  <li><a href="#gjf-format">Gaussian Input (export: gjf) format</a></li>
</ul>
<h2><a class="anchor" name="gout-format">Gaussian output (import only)</a></h2>
<PERSON> provides a basic functionality for importing the text output
(lige) files produced by<br>
Gaussian. Marvin imports the molecular structure stored in Gaussian
output files including <br>
the following data:
<blockquote>
  <table style="width: 492px; height: 194px;" border="0" cellpadding="5"
 cellspacing="0">
    <tbody>
      <tr>
        <td>Description</td>
        <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <em>Imported
into property:<br>
        </em></td>
      </tr>
      <tr>
        <td>Title</td>
        <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <em>"Gaussian_titleline"<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
The title line is checked for the existance of a<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        </em><em>SMILES representation </em><em>of the structure<br>
        </em></td>
      </tr>
      <tr>
        <td valign="top">Command line(s)<br>
        </td>
        <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <em>"Gaussian_commandline"<br>
        </em></td>
      </tr>
      <tr>
        <td valign="top">Energy<br>
        </td>
        <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <em>"Energy"
and "Energy_unit" to "Hartree"<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
As Gaussian may report the system's total energy,<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
in case of special jobs the stored value should be<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
validated before further use</em></td>
      </tr>
    </tbody>
  </table>
</blockquote>

<h2><a class="anchor" name="gjf-format">Gaussian input (export only)</a></h2>
Marvin provides a basic functionality to export into Gaussian input
(gjf) format. The general <br>
description of the format is too complicated, therefore the import
functionality is not available yet.<br>
<br>
"Gaussian_titleline" and "Gaussian_commandline" properties can
overwrite the defaults. The<br>
total charge of the molecule is determined by the charged atoms in the
molecule.<br>
<br>
<h2>Reference
</h2>
<ul>
<li><a HREF="http://www.gaussian.com">http://www.gaussian.com</a></li>
</ul>

</body>
</html>
