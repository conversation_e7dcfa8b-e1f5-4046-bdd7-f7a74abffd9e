<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
  <meta name="author" content="Peter C<PERSON>">
  <link rel="stylesheet" type="text/css" href="../marvinmanuals.css"
 title="Style">
  <title>File Formats in Marvin</title>
</head>
<body>
<h1 align="center">File Formats in Marvin</h1>
<ul>
  <li><a href="basic-export-opts.html">Basic Export Options</a></li>
  <li>Document formats:
    <ul>
      <li><a href="mrv-doc.html">Marvin Documents (MRV)</a> <sup>(<strong>text</strong>)</sup></li>
    </ul>
  </li>
  <li>Molecule formats:
    <ul>
      <li><a href="cml-doc.html">CML</a> <sup>(<strong>text</strong>)</sup></li>
      <li><a href="mol-csmol-doc.html">Molfiles and compressed
molfiles</a> <sup>(<strong>text</strong>)</sup>
        <ul>
          <li><a href="mol-csmol-doc.html#mol">MDL molfiles,
RGfiles, SDfiles, Rxnfiles, RDfiles</a></li>
          <li><a href="mol-csmol-doc.html#csmol">Compressed
molfiles</a></li>
          <li><a href="mol-csmol-doc.html#options">Export
options</a></li>
          <li><a href="../developer/molcompress-doc.html">Programming interface</a>
            <ul>
              <li><a href="../developer/molcompress-doc.html#java">in Java</a></li>
              <li><a href="../developer/molcompress-doc.html#js">in JavaScript</a></li>
              <li><a href="../developer/molcompress-doc.html#c">in C and C++</a></li>
            </ul>
          </li>
          <li><a
 href="http://www.chemaxon.com/marvin/doc/dev/molcompress-demo.html">Online
compression</a></li>
        </ul>
      </li>
    
 <li><a href="skc-doc.html">ISIS/Draw sketch file (skc)</a>
 <sup>(<strong>binary</strong>)</sup></li>
 <li><a href="cdx-doc.html">ChemDraw sketch file </a> (cdx <sup>(<strong>binary</strong>)</sup>, cdxml <sup>(<strong>text</strong>)</sup>)</li>       
<li>SMILES, SMARTS and related formats <sup>(<strong>text</strong>)</sup>
        <ul>
          <li><a href="smiles-doc.html">SMILES and SMARTS</a></li>
          <li><a href="cxsmiles-doc.html">ChemAxon Extended
SMILES and SMARTS</a></li>
          <li><a href="abbrevgroup-doc.html">ChemAxon SMILES
Abbreviated Group</a></li>
<ul> <li><a href="../developer/queryprops.html">Query properties in molecule file
      formats</a></li></ul>
        </ul>
      </li>
  <li><a href="inchi-doc.html">IUPAC InChI and InChIKey</a> <sup>(<strong>text</strong>)</sup></li>
      <li><a href="name-doc.html">Name</a> <sup>(<strong>text</strong>)</sup></li>
        <li><a href="seq-doc.html">Sequences(Peptide, DNA, RNA)</a> <sup>(<strong>text</strong>)</sup></li> 
   <li>Tripos <a href="sybyl-doc.html">SYBYL Mol</a> and <a
 href="mol2-doc.html">Mol2</a> <sup>(<strong>text</strong>)</sup></li>
      <li><a href="pdb-doc.html">PDB</a> <sup>(<strong>text</strong>)</sup></li>
      <li><a href="fasta-doc.html">FASTA</a> <sup>(<strong>text</strong>)</sup></li>
      <li><a href="xyz-doc.html">XYZ</a> <sup>(<strong>text</strong>)</sup></li>
      <li><a href="cube-doc.html">Gaussian Cube</a> <sup>(<strong>text</strong>)</sup></li>
      <li><a href="gaussian-doc.html">Gaussian Input/Output</a>
        <sup>(<strong>text</strong>)</sup></li>
    
<li><a href="vmn-doc.html">Markush DARC (vmn)</a>
 <sup>(<strong>binary</strong>)</sup></li>
    </ul>
  </li>
  <li>Graphics formats:
    <ul>
      <li><a href="images-doc.html">Image export</a>
        <ul>
          <li><a href="jpeg-doc.html">JPEG</a> <sup>(<strong>binary</strong>)</sup></li>
          <li><a href="msbmp-doc.html">BMP</a> <sup>(<strong>binary</strong>)</sup></li>
          <li><a href="png-doc.html">PNG</a> <sup>(<strong>binary</strong>)</sup></li>
          <li><a href="ppm-doc.html">PPM</a> <sup>(<strong>binary</strong>)</sup></li>
          <li><a href="emf-doc.html">EMF</a> <sup>(<strong>binary</strong>)</sup></li>
          <li><a href="pdf-doc.html">PDF</a> <sup>(<strong>text</strong>)</sup></li>
          <li><a href="svg-doc.html">SVG</a> <sup>(<strong>text</strong>)</sup></li>
        </ul>
      </li>
      <li><a href="pov-doc.html">Export to POV-Ray</a> <sup>(<strong>text</strong>)</sup></li>
     <li><a href="imageimport.html">Image import via OSRA</a>    </ul>
  </li>
  <li>Compression and encoding:
    <ul>
      <li><a href="gzip-doc.html">GZIP</a> <sup>(<strong>binary</strong>)</sup></li>
      <li><a href="base64-doc.html">Base64</a> <sup>(<strong>text</strong>)</sup></li>
    </ul>
  </li>
  <li><a href="molconvert.html">Molecule file conversion with
the MolConverter program</a></li>
</ul>
</body>
</html>
