<html>
<head>
<meta NAME="description" CONTENT="XYZ format in Marvin">
<meta NAME="keywords" CONTENT="XYZ, Java, Marvin">
<meta NAME="author" CONTENT="Peter <PERSON>">
<link REL ="stylesheet" TYPE="text/css" HREF="../marvinmanuals.css" TITLE="Style">
<title>POV-Ray export in Marvin</title>
</head>
<body>

<h1>POV-Ray</h1>

<p>
Codename: <strong>pov</strong>

<p>
<PERSON> is capable of creating a blob object from the molecule that can be
included in input files for the
Persistence of Vision Ray Tracer.
However, as MarvinSket<PERSON> is still primarily a 2D sketcher, the POV-Ray
output is not as nice as it could be.
<p>
POV-Ray export is an experimental feature that might be improved in the future
as <PERSON> gets more 3D capabilities.
<p>

<h2>Reference</h2>
<ul>
<li><a HREF="http://www.povray.org" TARGET="_top">http://www.povray.org</a></li>
</ul>

</body>
</html>
