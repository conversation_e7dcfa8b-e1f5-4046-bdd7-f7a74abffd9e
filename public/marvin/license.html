<!DOCTYPE HTML>
<HTML>
<HEAD>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<TITLE>EULA</TITLE>
</HEAD>
<BODY>

<H1 align="center">END-USER LICENSE AGREEMENT FOR CHEMAXON'S PRODUCTS</H1>
<P align="right">May 15, 2013</P>
<h3>Table Of Contents</h3>
<ul>
<li>1. GRANT OF LICENCE</li>
<li>2. SUPPORT SERVICES</li>
<li>3. TERMINATION</li>
<li>4. COPYRIGHT</li>
<li>5. LIMITATION OF WARRANTIES AND LIABILITY</li>
<li>6. BUNDLED THIRD PARTY PRODUCTS</li>
</ul>

<P>This End-User License Agreement (&quot;EULA&quot;) for ChemAxon's software
("SOFTWARE PRODUCT") is a legal agreement between you (either an individual or
an entity) and ChemAxon Ltd. and its suppliers. You may install the SOFTWARE
PRODUCT on the single computer or on the nodes of computer networks for which
ChemAxon provided the license, as set forth on the applicable license addendum, purchase order, or other documentation.

<P><B>You agree that your use of the SOFTWARE PRODUCT acknowledges that you have read this license, understand it, and agree to be bound by its terms and conditions.</B></P>

<P>The SOFTWARE PRODUCT is protected by copyright laws and international copyright treaties, as well as other intellectual property laws and treaties. The SOFTWARE PRODUCT is licensed, not sold.</P>

<P>This EULA applies to all products of ChemAxon.

</P>

<a name="grant"></a>
<H3>1.  GRANT OF LICENSE</H3>
<OL TYPE="a">

<LI><I>Free usage of the SOFTWARE PRODUCT for evaluation</I>. Any number of users may access and use the SOFTWARE PRODUCT, for the sole purpose of testing it for a period of one month.</LI>

<LI><I>Free usage of Marvin Applets and Marvin Beans for secondary and high schools</I>. Marvin Applets and Marvin Beans may be accessed free of charge by secondary and high school teachers and students.</LI>

<LI><I>Free usage of the MarvinSketch, MarvinView and MarvinSpace applets for free Internet sites.</I> If any of the following conditions is true, then any number of users may access Marvin Applets 
<ul>
<li>A freely available Internet site uses any of these applets.</li>
<li>A scientific or technical publication (posters or articles) available on the Internet displays structures using the MarvinView applet.</li>
<li>A static web page that displays structures using the MarvinView applet.</li>
In this case, it is also allowed that the MarvinView applet is downloaded from 
http://www.chemaxon.com/marvin/ (So neither the user nor the server administrator needs to install Marvin).</li>
</ul>
</LI>

<LI><I>Free usage of MarvinSketch, MarvinView, MarvinSpace, and MolConverter.</I>
    <br>The usage of the following applications is free of charge if they are
    not used as integral parts of other applications:
    <ol>
    <li>MarvinSketch</li>
    <li>MarvinView</li>
    <li>MarvinSpace</li>
    <li>MolConverter</li>
        </ol>
    MarvinSketch, MarvinView, MarvinSpace and MolConverter applications may be 
    accessed:

    <ul>
    <li>by downloading the Marvin Beans or the JChem package and running the 
    appropriate batch files, shell scripts (<code>msketch</code>, 
    <code>mview</code>, <code>mspace</code>, <code>molconvert</code>) or
    desktop launch. 
    (Other usages of Marvin Beans or JChem are <b>not free</b> of charge.)</li> 
    
    <li>as Java Web Start applications from 
    <a href="http://www.chemaxon.com/marvin/jnlp/index.html" 
    target="_blank">a page at ChemAxon's web site</a>.
    (Only MarvinSketch, MarvinView, and MarvinSpace.)</li>
    </ul>
  
    The license does not include the Calculator Plugins.

<li><i>Free academic packages</i>
<ul>
	<li>For teaching:
<br>ChemAxon offers all of its products free of charge for education.
	</li>

	<li>For research:
<br>ChemAxon offers all of its products free of charge for a period of 2 years from date of license issue to users conducting original academic research on condition that any presentation or publication of data generated using ChemAxon products include a credit to ChemAxon. Additional free of charge periods can be applied if at least one publication, including correct credit, resulting from the research can be demonstrated.  Following the free of charge period(s) the usual academic rate will be applied.
	</li>

</ul>

The Academic package does not allow transfer of licenses or use of ChemAxon products for third parties or charges to be made by license holders for use or deployment of products provided under an Academic package.<br>

The license for JChem Base allows maximum 3 searches per minute.
</li>

<LI><i>FreeWeb Package</i>
<br>For freely accessible (no login) and non commercial websites JChem Base, JChem Cartridge and Standardizer may be used. Calculator Plugins can be used also but only for structural content held by the website. The website may not use Calculator Plugin license keys to enable structure based calculations provided by the Marvin interface or to perform structure based calculations on submitted structures.</LI>



<LI><I>Usage of the SOFTWARE PRODUCT</I>. 
    Payment conditions may limit
    <ul>
    <li>the number of users who may access and use the SOFTWARE PRODUCT
    <li>the number of nodes in a computer network, which may access the SOFTWARE PRODUCT
    <li>the number of servers that are allowed to store copies of the SOFTWARE PRODUCT
    <li>the number of copies of the SOFTWARE PRODUCT, which may be stored on
    the server(s)
    <li>the location of the server(s) storing the SOFTWARE PRODUCT
    </ul>
</LI>
</OL>

<P>REDISTRIBUTABLE COMPONENTS:
<P>In addition to the rights granted above, ChemAxon grants you the right to use and modify the example files provided with the SOFTWARE PRODUCT designated as "Examples" for the sole purposes of designing, developing, and testing your software product(s).</P>


<P>YOU MAY NOT:</P>

<UL>
<LI>use the SOFTWARE PRODUCT or make copies of it except as permitted in this EULA,</LI>
<LI>translate, reverse engineer, decompile or disassemble the SOFTWARE PRODUCT except to the extent the foregoing restriction is expressly prohibited by applicable law notwithstanding this limitation,</LI>
<LI>rent, lease, assign, or transfer the SOFTWARE PRODUCT or its components to a third party without the written permission of ChemAxon,</LI>
<LI>redistribute the SOFTWARE PRODUCT or its components (except for the Examples) or transfer your license to a third party without the written permission of ChemAxon.</LI></UL>

<a name="support_services"></a>
<H3>2. SUPPORT SERVICES</H3>
<P>ChemAxon may provide you with support services related to the SOFTWARE PRODUCT. Support Services include free downloading of upgrades and technical support  by e-mail or phone. Use of Support Services is governed by ChemAxon policies and programs described on 
<a href="http://www.chemaxon.com">ChemAxon' s Internet site</a>
or in other materials provided by ChemAxon. Any supplemental software code provided to you as part of the Support Services shall be considered part of the SOFTWARE PRODUCT and is subject to the terms and conditions of this EULA. </P>

<P>Upgrades can be downloaded from ChemAxon's Internet site (<A HREF="http://www.chemaxon.com/">www.chemaxon.com</A>).</P>

<ul>
<li>Annual licenses:
Support services may be accessed for a period of up to 12 months from the date of the purchase of the annual license.</li>
<li>Perpetual licenses: During the first 12 month period from the date of the initial license
purchase Support Services are included in the license. Thereafter
upgrades can be accessed by subscribing to our Support Services, details of
which are in the invoice relating to the license purchase.</li>
</ul>

When needed
ChemAxon will send you an updated LICENSE KEY for any new upgrades.</P>

<P>You may use the resulting upgraded product only in accordance with the terms of this EULA. If the SOFTWARE PRODUCT is an upgrade of a component of a package of software programs that you licensed as a single product, the SOFTWARE PRODUCT may be used only as part of that single product package and may not be separated for use on more than one computer.</P>

<a name="termination"></a>
<H3>3. TERMINATION</H3>
<P>This license shall remain in effect only for so long as you are in compliance with the terms and conditions of this EULA. This license will terminate if you fail to comply with any of its terms or conditions. You may terminate it at any time by destroying your copies of the SOFTWARE PRODUCT. You agree, upon termination to destroy all copies of the Product. Without prejudice to any other rights, ChemAxon may terminate this EULA if you fail to comply with the terms. The provisions of this EULA that protect the proprietary rights of ChemAxon and the LIMITATIONS OF WARRANTIES will continue to be in force even after any termination. Upon termination, ChemAxon may also enforce any rights provided by law.</P>

<a name="copyright"></a>
<H3>4. COPYRIGHT</H3>
<P>All title and copyrights in and to the SOFTWARE PRODUCT (including but not limited to <I>any images, text, and applets</I> incorporated into the SOFTWARE PRODUCT) are the proprietary products of ChemAxon and are protected by copyright law.  You acquire only the right to use the SOFTWARE PRODUCT and do not acquire any rights of ownership. You acknowledge that the SOFTWARE PRODUCT in source code remains a confidential trade secret of ChemAxon. ChemAxon may have trademarks, copyrights, or other intellectual property rights covering the SOFTWARE PRODUCT. You are not granted any license to these patents, trademarks, copyrights, or other intellectual property rights except as expressly provided herein. ChemAxon reserves all rights not expressly granted.</P>

<a name="limitation"></a>
<H3>5. LIMITATION OF WARRANTIES AND LIABILITY</H3>
</B>
<P><B>The SOFTWARE PRODUCT is provided on an &quot;as is&quot; basis, without any other warranties or conditions, express or implied, including, but not limited to, warranties of merchantable quality, satisfactory quality, merchantability or fitness for a particular purpose, or those arising by law, statute, usage of trade, course of dealing or otherwise.  the entire risk as to the results and performance of the product is assumed by you.  Neither we nor our dealers or suppliers shall have any liability to you or any other person or entity for any indirect, incidental, special, or consequential damages whatsoever, including, but not limited to, loss of revenue or profit, lost or damaged data or other commercial or economic loss, even if we have been advised of the possibility of such damages, or they are foreseeable.  We are also not responsible for claims by a third party.  Our maximum aggregate liability to you and that of our dealers and suppliers shall not exceed the amount paid by you for the product.  The limitations in this section shall apply whether or not the alleged breach or default is a breach of a fundamental condition or term or a fundamental breach.  Some states/countries do not allow the exclusion or limitation of liability for consequential or incidental damages, so the above limitation may not apply to you.</B></P>

<a name="thirdparty"></a>
<H3>6. BUNDLED THIRD PARTY PRODUCTS</H3>
<h4>Marvin uses software developed by:</h4>
<ul>
    <li><em>Aloe</em> Swing Extension Package: <code>cb.aloe.swing.tools</code> having <a href="http://www.gnu.org/licenses/lgpl.html">LGPL</a> license</li>
<li><a href="http://www.jgoodies.com/freeware/forms/">JGoodies Forms</a> is a free library.</li>
<li><a href="http://www.jgoodies.com/freeware/looks/index.html">JGoodies Looks</a> is a free library.</li>
<li><a href="http://www.objectplanet.com/easycharts/">EasyCharts</a>.</li>
<li><a href="http://www.singularsys.com/jep/">Jep - Java Math Expression Parser</a>, see <a href="http://www.singularsys.com/order/license.html">license</a>.</li>
<li><a href="http://sourceforge.net/projects/jacob-project">Jacob Java - Com Bridge</a>.</li>
<li><a href="http://www.ikvm.net">IKVM</a>, see <a href="http://weblog.ikvm.net/story.aspx/license">license</a>. We added some modifications to the original sources, since originally it does/did not support everything we need.</li>
<li>InChI<sup>TM</sup> Material by <a HREF="http://www.chemaxon.com/marvin/help/license/IUPACInChI.license.txt">IUPAC 2005&copy;</a></li>
<li><a HREF="http://jni-inchi.sourceforge.net/">JNI-InChI</a> is Copyright &#169; 2006-2010, Sam Adams. JNI-InChI is free software:
        you can redistribute it and/or modify it under the terms of the GNU Lesser General Public License e as published
        by the Free Software Foundation, either version 3 of the License, or (at your option) any later version.
        (See <a HREF="http://www.gnu.org/licenses/gpl.html">GPL</a>, <a HREF="http://www.gnu.org/licenses/lgpl.html">LGPL</a> licence.)</li>
<li><a href="http://ws.apache.org/xmlrpc/index.html">Apache XML-RPC</a>
 is <a href="http://ws.apache.org/xmlrpc/license.html">licensed</a> under <a href="http://www.apache.org/licenses/">Apache License Version 2.0, January 2004</a>.</li>
<li><a href="http://www.json.org/java/">Json-org (JSON)</a>, see <a href="http://www.json.org/license.html">license</a>.</li>
<li><a href="http://java.sun.com/xml/downloads/saaj.html">SOAP with Attachments API for Java (SAAJ)</a>.</li>
<li><a href="http://sourceforge.net/projects/wsdl4j/">WSDL4J (SOAP)</a> is licensed under
 <a href="http://www.opensource.org/licenses/cpl1.0.php">Common Public License 1.0</a>.</li>
<li><a href="http://jcommander.org/">JCommander</a> is
 <a href="https://github.com/cbeust/jcommander/blob/master/license.txt">licensed</a> under
<a href="http://www.apache.org/licenses/">Apache License Version 2.0, January 2004</a>.</li>
<li><a href="http://www.antlr.org/">antlr</a>, licensed under the <a href="http://www.antlr.org/wiki/display/Mantra/License">BSD Licence</a>.</li>
<li><a href="http://www.brics.dk/automaton/">dk.brics.automaton</a>, licensed under the <a href="http://www.brics.dk/automaton/index.html">BSD License</a>.</li>
<li><a href="http://www.bouncycastle.org/">bcprov</a>, licensed under the <a href="http://www.bouncycastle.org/licence.html">MIT X11 License</a>.</li>
<li><a href="http://commons.apache.org/proper/commons-codec/">commons-codec</a>, licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>.</li>
<li><a href="http://commons.apache.org/proper/commons-compress/">commons-compress</a>, licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>.</li>
<li><a href="http://commons.apache.org/proper/commons-exec/">commons-exec</a>, licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>.</li>
<li><a href="http://commons.apache.org/proper/commons-logging/">commons-logging</a>, licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>.</li>
<li><a href="http://sourceforge.net/projects/fontbox/">fontbox</a>, licensed under the <a href="http://sourceforge.net/projects/fontbox">BSD License</a>.</li>
<li><a href="http://java.freehep.org/">freehep</a>, licensed under the <a href="http://java.freehep.org/license.html">LGPL 2.1</a>.</li>
<li><a href="http://code.google.com/p/guava-libraries">guava</a>, licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>.</li>
<li><a href="http://www.oracle.com/technetwork/java/javase/tech/jai-142803.html">jai</a>, licensed under the <a href="http://download.java.net/media/jai/builds/release/1_1_3/LICENSE-jai.txt">license agreement</a>.</li>
<li><a href="http://sourceforge.net/projects/jcamp-dx">jcampdx</a>, licensed under the <a href="http://www.gnu.org/licenses/lgpl-2.1-standalone.html">GNU LESSER GENERAL PUBLIC LICENSE 2.1</a>.</li>
<li><a href="http://sourceforge.net/projects/jempbox/">jempbox</a>, licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>.</li>
<li><a href="http://code.google.com/p/juniversalchardet/">juniversalchardet</a>, licensed under the <a href="http://www.mozilla.org/MPL/1.1/">MPL 1.1</a>.</li>
<li><a href="http://pdfbox.apache.org/">pfdbox</a>, licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>.</li>
<li><a href="http://jakarta.apache.org/site/legal.html">regexp</a>, licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>.</li>
<li><a href="http://ccil.org/~cowan/XML/tagsoup/">tagsoup</a>, licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>.</li>
<li><a href="http://code.google.com/p/tesseract-ocr/">tesseract</a>, licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>.</li>
<li><a href="http://tika.apache.org/">tika</a>, licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>.</li>
<li><a href="http://ws.apache.org/commons/util">ws-commons-util</a>, licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>.</li>
<li><a href="http://xmlbeans.apache.org/index.html">xmlbeans</a>, licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>.</li>
<li><a href="http://tukaani.org/xz/java.html">xz</a>, public domain.</li>

<!--
Unknown:
chart - ? - 
csjdbc - ?
jextexp -? - 
-->

<li>Image formats:
<ul>
    <li><a HREF="http://xmlgraphics.apache.org/batik/">Apache SVG Toolkit</a>, see <a HREF="http://www.chemaxon.com/marvin/help/license/LICENSE.batik.txt">license</a></li>
    <li>JPEG Encoder by<a HREF="http://www.chemaxon.com/marvin/help/license/JpegEncoder.license.txt">
James R. Weeks and BioElectroMech&copy;</a>,
    <a HREF="http://www.chemaxon.com/marvin/help/license/JpegEncoder.IJGreadme.txt">Independent JPEG Group</a></li>
    <li><a HREF="http://www.chemaxon.com/marvin/help/formats/PngEncoder.java.txt">PNG encoder</a> by J. David Eisenberg is under <a HREF="http://www.gnu.org/licenses/lgpl.html">LGPL</a> license</li>
    <li>PPM encoder by Jef Poskanzer&copy;, see <a HREF="http://www.chemaxon.com/marvin/help/license/PpmEncoder.license.txt">license</a></li>
    <li><a HREF="http://java.freehep.org/vectorgraphics/">VectorGraphics</a> package of <a href="http://java.freehep.org/">FreeHEP Java Library</a> is under <a href="http://www.gnu.org/licenses/lgpl.html">LGPL</a> license.</li>
    <li><a HREF="http://osra.sourceforge.net">OSRA</a> software by <a HREF="http://cactus.nci.nih.gov/osra">CADD Group Chemoinformatics Tools and User Services</a></li>
	<li><a HREF="http://pdfbox.apache.org">Apache PDFBox -Java PDF Library</a></li>
	<li><a href="http://www.lowagie.com/iText/">iText</a>, licensed under the <a href="http://www.gnu.org/copyleft/lesser.html">LGPL</a> license.</li>
</ul>
</li>
    <li>MarvinSpace is based on <a href="https://jogl.dev.java.net/">JOGL</a>, a
Java interface to OpenGL under <a href="http://www.opensource.org/licenses/bsd-license.html">
Berkeley Software Distribution (BSD) License</a></li>
    <li>MarvinSpace uses the DualThumbSlider component of <a href="http://www.gnf.org">
Genomics Institute of the Novartis Research Foundation (GNF)</a></li>
	<li>Name to structure and Structure to name use names from the
    	<a href="http://www.drugbank.ca/">DrugBank</a> database.</li>

    <li><strong>Marvin for JavaScript server side code (war distribution) uses:</strong>
    <ul>
        <li>Jersey is an open source (under dual CDDL+GPL license) production from the GlassFish project. <a href="http://glassfish.java.net/public/CDDL+GPL_1_1.html">http://glassfish.java.net/public/CDDL+GPL_1_1.html</a></li>
    </ul></li>
</ul>

<h4>JChem uses software developed by:</h4>
<ul>
<li><a href="http://commons.apache.org/daemon">Apache Commons Daemon</a> is <a href="http://commons.apache.org/daemon/license.html">licensed</a> under the <a href="http://www.apache.org/licenses/">Apache License Version 2.0, January 2004</a> </li>
<li><a href="http://commons.apache.org/logging/commons-logging-1.1.1/index.html">Apache Commons Logging</a> is licensed under the <a href="http://www.apache.org/licenses/">Apache License Version 2.0, January 2004</a> </li>
<li><a href="http://commons.apache.org/proper/commons-pool/">commons-pool</a>, licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>.</li>
<li><a href="http://www.castor.org">Castor</a>, see <a href="http://www.castor.org/license.html">license</a>.</li>
<li><a href="http://www.dom4j.org">dom4j</a>, see <a href="http://www.dom4j.org/license.html">license</a>.</li>
</ul>

<h4>JChem distribution contains the following JDBC drivers</h4> 
<ul>
<li><a href="http://hsqldb.org">HSQLDB</a>, see <a href="http://hsqldb.org/web/hsqlLicense.html">license</a>.</li>
<li>JDBC driver for <a href="http://www-01.ibm.com/software/data/db2/">DB2:</a>
The db2jcc.jar file included in JChem may not be 1) used for any purpose other than to enable JChem to access DB2 databases, 2) copied (except for backup purposes), 3) further distributed, or 4) reverse assembled, reverse compiled, or otherwise translated.<br/><br/>
JChem <em>CONTAINS<br>
<br>
Runtime Modules of<br>
IBM DB2 Driver for JDBC and SQLJ<br>
<br>
(c) Copyright IBM Corporation 2006<br>
All Rights Reserved</em><br><br></li>
<li><a href="http://msdn.microsoft.com/en-us/sqlserver">Microsoft SQL server</a>, see the <a href="http://msdn.microsoft.com/en-us/library/ms378749.aspx">license</a></li>
<li><a href="http://www.mysql.com/downloads/connector/j/">MySql JDBC driver</a> MySQL open source software is provided under the <a href="http://www.gnu.org/licenses/old-licenses/gpl-2.0.html">GPL License</a>. OEMs, ISVs and VARs can purchase commercial licenses.
<li><a href="http://www.compositesw.com/products/information-server/">Composite database driver</a></li>
<li><a href="http://db.apache.org/derby/">Derby</a>, licensed under the <a href="http://www.apache.org/licenses/">Apache License Version 2.0, January 2004</a>.</li>
<li><a href="http://www.oracle.com/technology/software/tech/java/sqlj_jdbc/index.html">Oracle JDBC drivers</a>, see the <a href="http://www.oracle.com/technetwork/licenses/distribution-license-152002.html">driver</a></li>
<li><a href="http://jdbc.postgresql.org/">PostgreSQL JDBC driver</a>, see <a href="http://jdbc.postgresql.org/license.html">license</a>.</li>
<li><a href="http://jtds.sourceforge.net">jtds</a>, licensed under the <a href="http://www.gnu.org/licenses/lgpl-3.0-standalone.html">GNU LESSER GENERAL PUBLIC LICENSE</a>.</li>
</ul>

<h4>JChem Cartridge also incorporates the following software:</h4>
Apache Commons <a href="http://commons.apache.org/pool/">Pool</a> licensed under the <a href="http://www.apache.org/licenses/">Apache License Version 2.0, January 2004</a>.</li>

<h4>JChem Web Services also incorporates the following software:</h4>
<ul>
<li><a href="http://tomcat.apache.org/index.html">Apache Tomcat </a> is licensed with <a href="http://www.apache.org/licenses/">Apache License, Version 2.0</a></li>
<li><a href="http://jquery.com/">JQuery</a>, see the <a href="http://jquery.org/license/">license</a>
</ul>


<h4>Instant JChem also incorporates the following software:</h4>
<ul>
<li><a href="http://www.acegisecurity.org/">Spring Acegi security</a>, licensed under the <a href="http://www.apache.org/licenses/">Apache License Version 2.0, January 2004</a>.</li>
<li><a href="http://cglib.sourceforge.net/">CGLIB</a>, licensed under the <a href="http://www.apache.org/licenses/">Apache License Version 2.0, January 2004</a>.</li>
<li>Apache Commons
<a href="http://commons.apache.org/beanutils/">BeanUtils</a>, 
<a href="http://commons.apache.org/collections/">Collections</a>,
<a href="http://commons.apache.org/dbcp/">DBCP</a>, 
<a href="http://commons.apache.org/digester/">Digester</a>, 
<a href="http://commons.apache.org/lang/">Lang</a>, 
<a href="http://commons.apache.org/pool/">Pool</a>, 
</a>all licensed under the <a href="http://www.apache.org/licenses/">Apache License Version 2.0, January 2004</a>.</li>
<li><a href="http://www.eclipse.org/jdt/">Eclipse Java development tools (JDT)</a>, licensed under the <a href="http://www.eclipse.org/org/documents/epl-v10.php">Eclipse Public License - v 1.0</a>.</li>
<li><a href="http://www.lowagie.com/iText/">iText</a>, licensed under the <a href="http://www.gnu.org/copyleft/lesser.html">LGPL</a> license.</li>
<li><a href="http://danadler.com/jacob/">JACOB</a>, licensed under the <a href="http://www.gnu.org/copyleft/lesser.html">LGPL</a> license.</li>
<li><a href="http://poi.apache.org/">Apache POI</a>, licensed under the <a href="http://www.apache.org/licenses/">Apache License Version 2.0, January 2004</a>.</li>
<li><a href="http://www.jaspersoft.com/jasperreports">Jasper Reports</a>, licensed under the <a href="http://www.gnu.org/copyleft/lesser.html">LGPL</a> license.</li>
<li><a href="http://db.apache.org/derby/">Derby</a>, licensed under the <a href="http://www.apache.org/licenses/">Apache License Version 2.0, January 2004</a>.</li>
<li><a href="http://microba.sourceforge.net/">Microba</a>, see <a href="http://microba.sourceforge.net/license.txt">license</a>.</li>
<li><a href="http://www.netbeans.org/">Netbeans</a>, see <a href="http://www.netbeans.org/cddl-gplv2.html">license</a>.</li>
<li><a href="http://www.springsource.org/about">Spring Framework</a>, licensed under the <a href="http://www.apache.org/licenses/">Apache License Version 2.0, January 2004</a>.</li>
</ul>

</BODY>
</HTML>
