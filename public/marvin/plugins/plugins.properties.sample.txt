#$<plugin class name>$<plugin JAR URL>$<menu>$<mnemonics>$<group>$<groupmnemonics>$<NOPARAMS>

#first char is separator, you can choose any ASCII character that is not contained in the fields

plugin_01=$chemaxon.marvin.calculations.ElementalAnalyserPlugin$ElementalAnalyserPlugin.jar$Elemental Analysis$EA$$
plugin_02=$chemaxon.marvin.calculations.IUPACNamingPlugin$IUPACNamingPlugin.jar$Naming$N$$

plugin_11=$chemaxon.marvin.calculations.pKaPlugin$pKaPlugin.jar$pKa$pK$Protonation$P
plugin_12=$chemaxon.marvin.calculations.MajorMicrospeciesPlugin$MajorMicrospeciesPlugin.jar$Major Microspecies$M$Protonation$P
plugin_13=$chemaxon.marvin.calculations.IsoelectricPointPlugin$IsoelectricPointPlugin.jar$Isoelectric Point$IP$Protonation$P

plugin_21=$chemaxon.marvin.calculations.logPPlugin$logPPlugin.jar$logP$Pl$Partitioning$a
plugin_22=$chemaxon.marvin.calculations.logDPlugin$logDPlugin.jar$logD$Dl$Partitioning$a

plugin_41=$chemaxon.marvin.calculations.ChargePlugin$ChargePlugin.jar$Charge$Ch$Charge$C
plugin_42=$chemaxon.marvin.calculations.PolarizabilityPlugin$PolarizabilityPlugin.jar$Polarizability$Pz$Charge$C
plugin_43=$chemaxon.marvin.calculations.OrbitalElectronegativityPlugin$ChargePlugin.jar$Orbital Electronegativity$ORb$Charge$C

plugin_51=$chemaxon.marvin.calculations.TautomerizationPlugin$MultiformPlugin.jar$Tautomers$Tautomers$Isomers$I
plugin_53=$chemaxon.marvin.calculations.StereoisomerPlugin$StereoisomerPlugin.jar$Stereoisomers$Stereoisomers$Isomers$I

plugin_61=$chemaxon.marvin.calculations.ConformerPlugin$ConformerPlugin.jar$Conformers$Conformers$Conformation$f
plugin_62=$chemaxon.marvin.calculations.MolecularDynamicsPlugin$MolecularDynamicsPlugin.jar$Molecular Dynamics$Moldyn$Conformation$f
plugin_63=$chemaxon.marvin.calculations.AlignmentPlugin$Alignment.jar$3D Alignment$a$Conformation$f

plugin_71=$chemaxon.marvin.calculations.TopologyAnalyserPlugin$TopologyAnalyserPlugin.jar$Topology Analysis$TA$Geometry$G
plugin_72=$chemaxon.marvin.calculations.GeometryPlugin$GeometryPlugin.jar$Geometry$Geometry$Geometry$G
plugin_73=$chemaxon.marvin.calculations.TPSAPlugin$TPSAPlugin.jar$Polar Surface Area (2D)$PSA$Geometry$G
plugin_74=$chemaxon.marvin.calculations.MSAPlugin$MSAPlugin.jar$Molecular Surface Area (3D)$MSA$Geometry$G

plugin_81=$chemaxon.marvin.calculations.MarkushEnumerationPlugin$MarkushEnumerationPlugin.jar$Markush Enumeration$ME$$

plugin_91=$chemaxon.marvin.calculations.PredictorPlugin$PredictorPlugin.jar$Predictor$r$$

plugin_A1=$chemaxon.marvin.calculations.HBDAPlugin$HBDAPlugin.jar$H Bond Donor/Acceptor$HBDA$Other$O
plugin_A2=$chemaxon.marvin.calculations.HuckelAnalysisPlugin$HuckelAnalysisPlugin.jar$Huckel Analysis$u$Other$O
plugin_A3=$chemaxon.marvin.calculations.RefractivityPlugin$RefractivityPlugin.jar$Refractivity$Rfc$Other$O
plugin_A4=$chemaxon.marvin.calculations.ResonancePlugin$MultiformPlugin.jar$Resonance$e$Other$O
plugin_A5=$chemaxon.marvin.calculations.StructuralFrameworksPlugin$StructuralFrameworksPlugin.jar$Structural frameworks$f$Other$O
