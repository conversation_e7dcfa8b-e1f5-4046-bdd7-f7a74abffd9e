<?xml version="1.0" encoding="UTF-8"?>

<ParameterPanel>
	<Group Title="Display options">
	    <RadioButtonGroup Key="displayoptions">
			<Choice Value="conformers" Text="Display conformers" Tooltip="Conformers are displayed in a MarivnView window." Selected="true"/>
			<Choice Value="storeinproperty" Text="Store conformer information in property field"
            Tooltip="The conformer data are calculated and stored with the structures. This option provides
            the calculations needed to select a specific conformer when using 3D cleaning
            (menu item Structure > Clean 3D > Display Stored Conformers).
            The conformers will only be stored if you select one result and click on 'Select'."/>            
	    </RadioButtonGroup>
	</Group>
	<SingleSelection Key="forcefield" Label="Force Field" Toooltip="Type of force field used for calculation." >
	<Choice Value="dreiding" Text="Dreiding" Selected="true"/>
	<Choice Value="mmff94" Text="MMFF94" />
    </SingleSelection>
    <SingleSelection Key="energyunit" Label="Energy unit" Tooltip="Gives results in kcal/mol or kJ/mol.">
    	<Choice Value="kcal/mol" Text="kcal/mol" Selected="true" />
    	<Choice Value="kJ/mol" Text="kJ/mol" />
    </SingleSelection>
    <SingleSelection Key="optimization" Label="Optimization limit"
        Tooltip="Sets the optimization to loose, normal, strict very strict (in this order increasing calculation times and precisity).">
		<Choice Value="0" Text="Very loose"/>
		<Choice Value="1" Text="Normal" Selected="true"/>
		<Choice Value="2" Text="Strict"/>
		<Choice Value="3" Text="Very strict"/>
    </SingleSelection>
    <Boolean Key="leconformercalculation" Label="Calculate lowest energy conformer"
        Tooltip="Calculates and displays only the lowest energy conformer structure. When
        checking this option, max. number if conformers and diversity limit are disabled." Value="false"/>
    <Number Key="maxconformers" Label="Maximum number of conformers" Tooltip="Limits the number of calculated structures." Range="[-1,1000000000]" Value="10">
		<Dependencies>
		    <Item Key="leconformercalculation" Range="false"/>
		</Dependencies>
    </Number>
    <Number Key="diversity" Label="Diversity limit" 
        Tooltip="Conformers within diversity limit will be considered the same and doubles removed."
        Range="[0.1,1000|" Value="0.1">
		<Dependencies>
		    <Item Key="leconformercalculation" Range="false"/>
		</Dependencies>
    </Number>
    <Number Key="timelimit" Label="Timelimit (s)" 
        Tooltip="No conformers will be displayed if the calculation is stopped at the time limit set." Range="[0,1000000000]" Value="900">
	</Number>
    <Boolean Key="prehydrogenize" Label="Prehydrogenize"
        Tooltip="If checked, converts all implicit hydrogens to explicit hydrogens without removing them after the calculation. If unchecked, no explicit hydrogens will be added."
        Value="true"/>
    <Boolean Key="hyperfine" Label="Hyperfine" Tooltip="Inserts more iteration steps in the calculations, gives more precision in results but the needed time becomes longer." Value="false">
	</Boolean>
    <Boolean Key="multifrag" Label="Multi-fragment optimization"
        Tooltip="Multi-fragment optimization with MMFF94." Value="false">
        <Dependencies>
        	<Item Key="leconformercalculation" Range="true"/>
		    <Item Key="forcefield" Range="mmff94"/>
		</Dependencies> 
    </Boolean>        
    <Boolean Key="hbonds" Label="Visualize H bonds" Tooltip="Marks intramolecular hydrogen bonds in the conformer where it is likely to occur." Value="false">
	    <Dependencies>
		    <Item Key="displayoptions" Range="conformers"/>
		    <Item Key="prehydrogenize" Range="true"/>
		</Dependencies> 
	</Boolean>
</ParameterPanel>
