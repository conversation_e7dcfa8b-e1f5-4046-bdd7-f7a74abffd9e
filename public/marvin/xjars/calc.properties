# <plugin name>=<plugin class name>$<plugin JAR URL>$<plugin group name>$<parameters>$<default tag name>$<description>$<helptext>$<exampletext>
# important: plugins cannot have -c options because it is in conflict with the
# general option -c --config.
# separate parameter description items by ';' (will be separated by newline
# characters) in <helptext>
# do not use $ characters except for field separation
# <exampletext> is optional

# alias for elemanal
elementalanalysistable=$chemaxon.marvin.calculations.ElementalAnalyserPlugin\
	$ElementalAnalyserPlugin.jar\
	$-\
	$t=type:formula,isotopeformula,dotdisconnectedformula,dotdisconnectedisotopeformula,mass,exactmass,composition,isotopecomposition,atomcount;s=single:false\
	$ELEMANAL\
	$Molecule data calculation: formula, isotopeformula, dotdisconnectedformula,\ndotdisconnectedisotopeformula, mass, exactmass, composition, isotopecomposition,\natomcount.\
	$-t, --type=[formula|isotopeformula|dotdisconnectedformula|\ndotdisconnectedisotopeformula|mass|exactmass|composition|\nisotopecomposition|atomcount] (default: all);-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc elementalanalysistable -t "mass,composition,formula" test.mol

elemanal=$chemaxon.marvin.calculations.ElementalAnalyserPlugin\
	$ElementalAnalyserPlugin.jar\
	$-\
	$t=type:formula,isotopeformula,dotdisconnectedformula,dotdisconnectedisotopeformula,mass,exactmass,composition,isotopecomposition,atomcount;s=single:false\
	$ELEMANAL\
	$Molecule data calculation: formula, isotopeformula, dotdisconnectedformula,\ndotdisconnectedisotopeformula, mass, exactmass, composition, isotopecomposition,\natomcount.\
	$-t, --type=[formula|isotopeformula|dotdisconnectedformula|\ndotdisconnectedisotopeformula|mass|exactmass|composition|\nisotopecomposition|atomcount] (default: all);-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc elemanal -t "mass,composition,formula" test.mol

mass=$chemaxon.marvin.calculations.ElementalAnalyserPlugin\
	$ElementalAnalyserPlugin.jar\
	$-\
	$p=precision:-1;t=type:mass;s=single:false\
	$MASS\
	$Molecule mass calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf>\n(default: precision of the least precise atomic mass);-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc mass test.mol

exactmass=$chemaxon.marvin.calculations.ElementalAnalyserPlugin\
	$ElementalAnalyserPlugin.jar\
	$-\
	$p=precision:-1;t=type:exactmass;s=single:false\
	$EXACTMASS\
	$Exact molecule mass calculation based on the most frequent\nnatural isotopes of the elements.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf>\n(default: precision of the least precise atomic mass);-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc exactmass test.mol

formula=$chemaxon.marvin.calculations.ElementalAnalyserPlugin\
	$ElementalAnalyserPlugin.jar\
	$-\
	$t=type:formula;s=single:false\
	$FORMULA\
	$Molecular formula calculation.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc formula -s true test.mol

sortableformula=$chemaxon.marvin.calculations.ElementalAnalyserPlugin\
	$ElementalAnalyserPlugin.jar\
	$-\
	$t=type:formula;d=digits:5;s=single:false\
	$FORMULA\
	$Calculates a fixed digit sortable molecular formula.\
	$-d, --digits=<minimum number of digits in proportionate\nnumber of atoms> (default: 5);-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc sortableformula -d 4 test.mol

# alias for iformula
isotopeformula=$chemaxon.marvin.calculations.ElementalAnalyserPlugin\
	$ElementalAnalyserPlugin.jar\
	$-\
	$t=type:isotopeformula;D=symbolD:true;s=single:false\
	$ISOTOPEFORMULA\
	$Molecular formula calculation, isotopes included.\
	$-D, --symbolD=[true|false] use D / T symbols for Deuterium / Tritium\n(default: true);-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc isotopeformula -s true test.mol

iformula=$chemaxon.marvin.calculations.ElementalAnalyserPlugin\
	$ElementalAnalyserPlugin.jar\
	$-\
	$t=type:isotopeformula;D=symbolD:true;s=single:false\
	$ISOTOPEFORMULA\
	$Molecular formula calculation, isotopes included.\
	$-D, --symbolD=[true|false] use D / T symbols for Deuterium / Tritium\n(default: true);-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc iformula -s true test.mol

dotdisconnectedformula=$chemaxon.marvin.calculations.ElementalAnalyserPlugin\
	$ElementalAnalyserPlugin.jar\
	$-\
	$t=type:dotdisconnectedformula;s=single:false\
	$DOTDISCONNECTEDFORMULA\
	$Dot-disconnected molecular formula calculation.\
	$-\
	$cxcalc dotdisconnectedformula test.mol

dotdisconnectedisotopeformula=$chemaxon.marvin.calculations.ElementalAnalyserPlugin\
	$ElementalAnalyserPlugin.jar\
	$-\
	$t=type:dotdisconnectedisotopeformula;D=symbolD:true;s=single:false\
	$DOTDISCONNECTEDISOTOPEFORMULA\
	$Dot-disconnected molecular formula calculation, isotopes included.\
	$-D, --symbolD=[true|false] use D / T symbols for Deuterium / Tritium\n(default: true)\
	$cxcalc dotdisconnectedisotopeformula test.mol

composition=$chemaxon.marvin.calculations.ElementalAnalyserPlugin\
	$ElementalAnalyserPlugin.jar\
	$-\
	$p=precision:2;t=type:composition;s=single:false\
	$COMPOSITION\
	$Elemental composition calculation (w/w%).\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc composition -s true test.mol

# alias for icomposition
isotopecomposition=$chemaxon.marvin.calculations.ElementalAnalyserPlugin\
	$ElementalAnalyserPlugin.jar\
	$-\
	$p=precision:2;t=type:isotopecomposition;D=symbolD:true;s=single:false\
	$ISOTOPECOMPOSITION\
	$Elemental composition calculation, isotopes included (w/w%).\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-D, --symbolD=[true|false] use D / T symbols for Deuterium / Tritium\n(default: true);-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc isotopecomposition -s true test.mol

icomposition=$chemaxon.marvin.calculations.ElementalAnalyserPlugin\
	$ElementalAnalyserPlugin.jar\
	$-\
	$p=precision:2;t=type:isotopecomposition;D=symbolD:true;s=single:false\
	$ISOTOPECOMPOSITION\
	$Elemental composition calculation, isotopes included (w/w%).\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-D, --symbolD=[true|false] use D / T symbols for Deuterium / Tritium\n(default: true);-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc icomposition -s true test.mol

atomcount=$chemaxon.marvin.calculations.ElementalAnalyserPlugin\
	$ElementalAnalyserPlugin.jar\
	$-\
	$z=atno;m=massno;t=type:atomcount;s=single:false\
	$ATOMCOUNT\
	$Number of atoms in the molecule: \n\
	no atno: counts all atoms in the molecule;\n\
	atno, but no massno: counts atoms of the given type in the molecule;\n\
	atno, massno: counts atoms of the given isotope type in the molecule;\n\
	atno, massno=0: counts atoms of the given type in the molecule,\n                but excludes its isotopes.\
	$-z, --atno=<atomic number>;-m, --massno=<mass number>;-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc atomcount -z 7 test.mol

# Protonation

# alias for majorms
majormicrospecies=$chemaxon.marvin.calculations.MajorMicrospeciesPlugin\
	$MajorMicrospeciesPlugin.jar\
	$Protonation\
	$H=pH;f=format;M=majortautomer:false;K=keephydrogens:false\
	$MAJORMS\
	$Major microspecies at given pH.\
	$-H, --pH=<pH value> gets major microspecies at this pH\n(default: no pH, all microspecies);-f, --format=<output format> (default: smiles);-M, --majortautomer=[true|false] take major tautomeric form\n(default: false);-K, --keephydrogens=[true|false]\nkeep explicit hydrogen on result molecule\n(default: false)\
	$cxcalc majormicrospecies -H 3.5 -f mol test.mol

majorms=$chemaxon.marvin.calculations.MajorMicrospeciesPlugin\
	$MajorMicrospeciesPlugin.jar\
	$Protonation\
	$H=pH;f=format;M=majortautomer:false;K=keephydrogens:false\
	$MAJORMS\
	$Major microspecies at given pH.\
	$-H, --pH=<pH value> gets major microspecies at this pH\n(default: no pH, all microspecies);-f, --format=<output format> (default: smiles);-M, --majortautomer=[true|false] take major tautomeric form\n(default: false);-K, --keephydrogens=[true|false]\nkeep explicit hydrogen on result molecule\n(default: false)\
	$cxcalc majorms -H 3.5 -f mol test.mol

# alias for msdistr
microspeciesdistribution=$chemaxon.marvin.calculations.MajorMicrospeciesPlugin\
	$MajorMicrospeciesPlugin.jar\
	$Protonation\
	$H=pH:7.4;f=format:sdf:-a;t=tag;msdistr=msdistr:true;M=majortautomer:false;K=keephydrogens:false\
	$MSDISTR\
	$Microspecies list with distributions at given pH.\
	$-H, --pH=<pH value> gets major microspecies at this pH\n(default: 7.4);-f, --format=<output format> (default: sdf:-a);-t, --tag=<SDF/MRV tag to store the distribution value>\n(default: MSDISTR[pH=...]);-M, --majortautomer=[true|false] take major tautomeric form\n(default: false);-K, --keephydrogens=[true|false]\nkeep explicit hydrogen on result molecule\n(default: false)\
	$cxcalc microspeciesdistribution -H 3.5 test.mol

msdistr=$chemaxon.marvin.calculations.MajorMicrospeciesPlugin\
	$MajorMicrospeciesPlugin.jar\
	$Protonation\
	$H=pH:7.4;f=format:sdf:-a;t=tag;msdistr=msdistr:true;M=majortautomer:false;K=keephydrogens:false\
	$MSDISTR\
	$Microspecies list with distributions at given pH.\
	$-H, --pH=<pH value> gets major microspecies at this pH\n(default: 7.4);-f, --format=<output format> (default: sdf:-a);-t, --tag=<SDF/MRV tag to store the distribution value>\n(default: MSDISTR[pH=...]);-M, --majortautomer=[true|false] take major tautomeric form\n(default: false);-K, --keephydrogens=[true|false]\nkeep explicit hydrogen on result molecule\n(default: false)\
	$cxcalc msdistr -H 3.5 test.mol

#msdistr2=$chemaxon.marvin.calculations.MajorMicrospeciesPlugin\
#	$MajorMicrospeciesPlugin.jar\
#	$Protonation\
#	$H=pH:7.4;l=lower:0;u=upper:14;s=step:1;f=format:sdf:-a;t=tag;msdistr=msdistr:true;M=majortautomer:false\
#	$MSDISTR\
#	$Microspecies list with distributions at given pH.\
#	$-H, --pH=<pH value> gets major microspecies at this pH\n(default: 7.4);-f, --format=<output format> (default: sdf:-a);-t, --tag=<SDF/MRV tag to store the distribution value>\n(default: MSDISTR[pH=...]);-M, --majortautomer=[true|false] take major tautomeric form\n(default: false)\
#	$cxcalc msdistr -H 3.5 test.mol

	
#msdistr3=$chemaxon.marvin.calculations.pKaPlugin\
#	$pKaPlugin.jar\
#	$Protonation\
#	$t=type:msdistr;H=pH;l=lower:0;u=upper:14;s=step:1;f=format:sdf:-a;t=tag;msdistr=msdistr:true;M=majortautomer:false\
#	$MSDISTR\
#	$Microspecies list with distributions at given pH.\
#	$-H, --pH=<pH value> gets major microspecies at this pH\n(default: 7.4);-f, --format=<output format> (default: sdf:-a);-t, --tag=<SDF/MRV tag to store the distribution value>\n(default: MSDISTR[pH=...]);-M, --majortautomer=[true|false] take major tautomeric form\n(default: false)\
#	$cxcalc msdistr -H 3.5 test.mol

pka=$chemaxon.marvin.calculations.pKaPlugin\
	$pKaPlugin.jar\
	$Protonation\
	$p=precision:2;t=type:pKa;m=mode:macro;P=prefix:static;i=min:-10;x=max:20;T=temperature:298;n=ions:8;d=model:small;a=na:2;b=nb:2;C=considertautomerization:false;M=majortautomer:false;L=correctionlibrary:;P=correctionlibrarypath:;c=usecorrectionlibrary:false;K=keephydrogens:false\
	$PKA\
	$pKa calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-t, --type=[pKa|acidic|basic] (default: pKa);-m, --mode=[macro|micro] (default: macro);-P, --prefix=[static|dynamic] (default: static);-d, --model=[small|large] calculation model\nsmall: optimized for at most 8 ionizable atoms\nlarge: optimized for a large number of\nionizable atoms (default: small);-i, --min=<min basic pKa> (default: -10);-x, --max=<max acidic pKa> (default: 20);-T, --temperature=<temperature in Kelvin> (default: 298 K);-a, --na=<number of acidic pKa values displayed>\n(default: 2);-b, --nb=<number of basic pKa values displayed>\n(default: 2);    --considertautomerization=[true|false] consider tautomerization \nand resonance (default: false);-L, --correctionlibrary=<correction library ID>;-P, --correctionlibrarypath=<path of the correction library>\nuse this parameter when the correction library\nnot stored on the default location\
	$cxcalc pka -i -15 -x 25 -a 3 -b 3 -d large test.mol

# alias for pi
isoelectricpoint=$chemaxon.marvin.calculations.IsoelectricPointPlugin\
	$IsoelectricPointPlugin.jar\
	$Protonation\
	$p=precision:2;t=type:pI\
	$PI\
	$Isoelectric point calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2)\
	$cxcalc isoelectricpoint test.mol

pi=$chemaxon.marvin.calculations.IsoelectricPointPlugin\
	$IsoelectricPointPlugin.jar\
	$Protonation\
	$p=precision:2;t=type:pI\
	$PI\
	$Isoelectric point calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2)\
	$cxcalc pI test.mol

chargedistribution=$chemaxon.marvin.calculations.IsoelectricPointPlugin\
	$IsoelectricPointPlugin.jar\
	$Protonation\
	$p=precision:2;t=type:chargedistr;H=pH:;l=lower:0;u=upper:14;s=step:1\
	$CHARGE_DISTRIBUTION\
	$Charge distribution calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> calculates average charge at this pH\n(default: no single pH, takes pH values in\ninterval [lower, upper] by given step size);-l, --lower=<pH lower limit> (default: 0);-u, --upper=<pH upper limit> (default: 14);-s, --step=<pH step size> (default: 1)\
	$cxcalc chargedistribution test.mol

averagemicrospeciescharge=$chemaxon.marvin.calculations.IsoelectricPointPlugin\
	$IsoelectricPointPlugin.jar\
	$Protonation\
	$p=precision:2;t=type:chargedistr;H=pH:7.4\
	$AVERAGE_MICROSPECIES_CHARGE\
	$Average microspecies charge calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> calculates average charge at this pH\n(default: 7.4)\
	$cxcalc averagemicrospeciescharge test.mol

# Partitioning

logp=$chemaxon.marvin.calculations.logPPlugin\
	$logPPlugin.jar\
	$Partitioning\
	$p=precision:2;m=method:weighted;T=trainingid:;w=weights:;a=anion:0.1;k=kation:0.1;t=type:logPTrue;i=increments:false;C=considertautomerization:false;M=majortautomer:false;H=pH;E=error:false\
	$LOGP\
	$logP calculation:\nfor type logPTrue: logP of uncharged species, or,\nin the case of zwitterions, logD at pI;\nfor type logPMicro: logP of the input species.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-m, --method=[vg|klop|phys|user|weighted]\n(default: weighted);    --trainingid=<training id>\n;-w, --weights=<wVG:wKLOP:wPHYS:wUSER> method weights\n(default: 1:1:1:0)\nwVG: weight of the VG method\nwKLOP: weight of the KLOP method\nwPHYS: weight of the PHYS method\nwUSER: weight of the user defined method;-a, --anion=<Cl- concentration>\n(default: 0.1, range: [0.0, 0.25]);-k, --kation=<Na+ K+ concentration>\n(default: 0.1, range: [0.0, 0.25]);-t, --type=[increments|logPMicro|logPTrue]\n(default: logPTrue);-i, --increments=[true|false] show atomic increments\n(default: false);    --considertautomerization=[true|false] consider tautomerization\nand resonance (default: false);-H, --pH=<pH value> gets logp of the major\nmicrospecies at this pH (default:\nno pH, use given protonation state)\
	$cxcalc -S -t myLOGP logp -a 0.15 -k 0.05 test.mol

logd=$chemaxon.marvin.calculations.logDPlugin\
	$logDPlugin.jar\
	$Partitioning\
	$p=precision:2;m=method:weighted;T=logptrainingid:;w=weights:;a=anion:0.1;k=kation:0.1;H=pH:;l=lower:0;u=upper:14;s=step:1;1=ref1:;2=ref2:;3=ref3:;4=ref4:;c=considertautomerization:false;L=pkacorrectionlibrary:;\
	$LOGD\
	$logD calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-m, --method=[vg|klop|phys|user|weighted]\n(default: weighted);    --logptrainingid=<logP training id>\n;-w, --weights=<wVG:wKLOP:wPHYS:wUSER> method weights\n(default: 1:1:1:0)\nwVG: weight of the VG method\nwKLOP: weight of the KLOP method\nwPHYS: weight of the PHYS method\nwUSER: weight of the user defined method;-a, --anion=<Cl- concentration>\n(default: 0.1, range: [0.0, 0.25]);-k, --kation=<Na+ K+ concentration>\n(default: 0.1, range: [0.0, 0.25]);-H, --pH=<pH value> takes logD at this pH\n(default: no single pH, takes pH values in\ninterval [lower, upper] by given step size);-l, --lower=<pH lower limit> (default: 0);-u, --upper=<pH upper limit> (default: 14);-s, --step=<pH step size> (default: 1);-1, --ref1=<pH reference 1> (default: none);-2, --ref2=<pH reference 2> (default: none);-3, --ref3=<pH reference 3> (default: none);-4, --ref4=<pH reference 4> (default: none);    --considertautomerization=[true|false] consider tautomerization\nand resonance(default: false);;    --pkacorrectionlibrary=<pKa correction library ID>\
	$cxcalc -i ID logd -l 2 -u 3 -s 0.5 test.sdf

# Charge

charge=$chemaxon.marvin.calculations.ChargePlugin\
	$ChargePlugin.jar\
	$Charge\
	$p=precision:2;t=type:total;i=implh:false;r=resonance:false;H=pH\
	$CHARGE\
	$Partial charge calculation.\nTypes aromaticsystem / aromaticring calculate the sum of charges\nin the aromatic system / aromatic ring containing the atom.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-t, --type=[sigma|pi|total|implh|\naromaticsystem|aromaticsystemsigma|aromaticsystempi|\naromaticring|aromaticringsigma|aromaticringpi]\n(default: total);-i, --implh=[true|false] implicit H charge sum shown in brackets\n(for sigma and total charge only) (default: false);-r, --resonance=[true|false]\ntrue: take resonant structures (default: false);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc -S -o result.sdf -t myCHARGE charge -t "pi,total" -p 3 test.mol

formalcharge=$chemaxon.marvin.calculations.ChargePlugin\
	$ChargePlugin.jar\
	$Charge\
	$t=type:formalcharge;H=pH\
	$FORMAL_CHARGE\
	$Formal charge calculation.\
	$-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc formalcharge test.mol

orbitalelectronegativity=$chemaxon.marvin.calculations.OrbitalElectronegativityPlugin\
	$ChargePlugin.jar\
	$Charge\
	$p=precision:2;t=type:sigma,pi;r=resonance:false;H=pH\
	$ORBITAL_ELECTRONEGATIVITY\
	$Orbital electronegativity calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-t, --type=[sigma|pi]\nsigma: sigma orbital electronegativity\npioen: pi orbital electronegativity\n(default: sigma,pi);-r, --resonance=[true|false]\ntrue: take resonant structures (default: false);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc orbitalelectronegativity -p 3 test.mol

# alias for orbitalelectronegativity
oen=$chemaxon.marvin.calculations.OrbitalElectronegativityPlugin\
	$ChargePlugin.jar\
	$Charge\
	$p=precision:2;t=type:sigma,pi;r=resonance:false;H=pH\
	$ORBITAL_ELECTRONEGATIVITY\
	$Orbital electronegativity calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-t, --type=[sigma|pi]\nsigma: sigma orbital electronegativity\npi: pi orbital electronegativity\n(default: sigma,pi);-r, --resonance=[true|false]\ntrue: take resonant structures (default: false);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc oen -t sigma test.mol

polarizability=$chemaxon.marvin.calculations.PolarizabilityPlugin\
	$PolarizabilityPlugin.jar\
	$Charge\
	$p=precision:2;t=type:molecular,atomic;H=pH\
	$POLARIZABILITY\
	$Atomic and molecular polarizability calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-t, --type=[molecular|atomic] (default: both);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc polarizability -p 3 test.mol

# alias for polarizability
pol=$chemaxon.marvin.calculations.PolarizabilityPlugin\
	$PolarizabilityPlugin.jar\
	$Charge\
	$p=precision:2;t=type:molecular,atomic;H=pH\
	$POLARIZABILITY\
	$Atomic and molecular polarizability calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-t, --type=[molecular|atomic] (default: both);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc pol -p 3 test.mol

# alias for atompol
atomicpolarizability=$chemaxon.marvin.calculations.PolarizabilityPlugin\
	$PolarizabilityPlugin.jar\
	$Charge\
	$p=precision:2;t=type:atomic;H=pH\
	$ATOM_POLARIZABILITY\
	$Atomic polarizability calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc atomicpolarizability test.mol

atompol=$chemaxon.marvin.calculations.PolarizabilityPlugin\
	$PolarizabilityPlugin.jar\
	$Charge\
	$p=precision:2;t=type:atomic;H=pH\
	$ATOM_POLARIZABILITY\
	$Atomic polarizability calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc atompol test.mol

# alias for molpol
molecularpolarizability=$chemaxon.marvin.calculations.PolarizabilityPlugin\
	$PolarizabilityPlugin.jar\
	$Charge\
	$p=precision:2;t=type:molecular;H=pH\
	$MOL_POLARIZABILITY\
	$Molecular polarizability calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc molecularpolarizability test.mol

molpol=$chemaxon.marvin.calculations.PolarizabilityPlugin\
	$PolarizabilityPlugin.jar\
	$Charge\
	$p=precision:2;t=type:molecular;H=pH\
	$MOL_POLARIZABILITY\
	$Molecular polarizability calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc molpol test.mol

# alias for tpol
tholepolarizability=$chemaxon.marvin.calculations.PolarizabilityPlugin\
	$PolarizabilityPlugin.jar\
	$Charge\
	$p=precision:2;t=type:molecular,a(xx),a(yy),a(zz);D=geom3D:true;H=pH\
	$THOLE_POLARIZABILITY\
	$Calculation of average molecular polarizability and\nprincipal components of polarizability tensor (axx, ayy, azz).\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc tholepolarizability test.mol

tpol=$chemaxon.marvin.calculations.PolarizabilityPlugin\
	$PolarizabilityPlugin.jar\
	$Charge\
	$p=precision:2;t=type:molecular,a(xx),a(yy),a(zz);D=geom3D:true;H=pH\
	$THOLE_POLARIZABILITY\
	$Calculation of average molecular polarizability and\nprincipal components of polarizability tensor (axx, ayy, azz).\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc tpol test.mol

#DEL
# alias for tpol
tpolarizability=$chemaxon.marvin.calculations.PolarizabilityPlugin\
	$PolarizabilityPlugin.jar\
	$Charge\
	$p=precision:2;t=type:molecular,a(xx),a(yy),a(zz);D=geom3D:true;H=pH\
	$THOLE_POLARIZABILITY\
	$Calculation of average molecular polarizability and\nprincipal components of polarizability tensor (axx, ayy, azz).\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc tpolarizability test.mol

# alias for avgpol
averagemolecularpolarizability=$chemaxon.marvin.calculations.PolarizabilityPlugin\
	$PolarizabilityPlugin.jar\
	$Charge\
	$p=precision:2;t=type:molecular;D=geom3D:true;H=pH\
	$AVERAGE_MOL_POLARIZABILITY\
	$Average molecular polarizability calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc averagemolecularpolarizability test.mol

avgpol=$chemaxon.marvin.calculations.PolarizabilityPlugin\
	$PolarizabilityPlugin.jar\
	$Charge\
	$p=precision:2;t=type:molecular;D=geom3D:true;H=pH\
	$AVERAGE_MOL_POLARIZABILITY\
	$Average molecular polarizability calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc avgpol test.mol

#DEL
# alias for avgpol
averagepol=$chemaxon.marvin.calculations.PolarizabilityPlugin\
	$PolarizabilityPlugin.jar\
	$Charge\
	$p=precision:2;t=type:molecular;D=geom3D:true;H=pH\
	$AVERAGE_MOL_POLARIZABILITY\
	$Average molecular polarizability calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc averagepol test.mol

#?
axxpol=$chemaxon.marvin.calculations.PolarizabilityPlugin\
	$PolarizabilityPlugin.jar\
	$Charge\
	$p=precision:2;t=type:a(xx);D=geom3D:true;H=pH\
	$AXX_POLARIZABILITY\
	$Calculation of principal component of polarizability tensor axx.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc axxpol test.mol

#?
ayypol=$chemaxon.marvin.calculations.PolarizabilityPlugin\
	$PolarizabilityPlugin.jar\
	$Charge\
	$p=precision:2;t=type:a(yy);D=geom3D:true;H=pH\
	$AYY_POLARIZABILITY\
	$Calculation of principal component of polarizability tensor ayy.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc ayypol test.mol

#?
azzpol=$chemaxon.marvin.calculations.PolarizabilityPlugin\
	$PolarizabilityPlugin.jar\
	$Charge\
	$p=precision:2;t=type:a(zz);D=geom3D:true;H=pH\
	$AZZ_POLARIZABILITY\
	$Calculation of principal component of polarizability tensor azz.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc azzpol test.mol

ioncharge=$chemaxon.marvin.calculations.IonChargePlugin\
	$IonChargePlugin.jar\
	$Charge\
	$p=precision:2;H=pH:7;n=max-ions:9;m=min-percent;t=charge-type\
	$IONCHARGE\
	$Partial charge(s):\n  A) on the ionic forms with distribution percentage not less than\n     the minimum percentage specified in the min-percent parameter,\nor else\n  B) on the ionic form with maximal distribution\n     if the min-percent parameter is omitted.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> (default: 7);-n, --max-ions=max number of ionizable atoms\nto be considered (default: 9);-m, --min-percent=<min occurrence percentage of ionic form to be considered>\n(optional, if omitted then only the ionic form\nwith max percentage is considered);-t, --charge-type=[single|accumulated] charge type,\naccumulated means that charges of attached H atoms\nshould be added (default: single)\
	$cxcalc ioncharge -n 6 -H 8 -m 1 -t accumulated test.mol

# Isomers

tautomers=$chemaxon.marvin.calculations.TautomerizationPlugin\
	$MultiformPlugin.jar\
	$Isomers\
	$p=precision:0;c=canonical:false;g=generic:false;M=major:false;M=moststable:false;d=dominants:true;R=rational:false;D=distribution:false;m=max:200;l=pathlength:4;H=pH;a=protectaromaticity:true;C=protectcharge:true;e=excludeantiaroma:true;P=protectdoublebondstereo:false;T=protectalltetrahedralcenters:false;L=protectlabeledtetrahedralcenters:false;E=protectestergroups:true;s=symfilter:true;f=format;t=tag:TAUTOMER_DISTRIBUTION;r=ring:false\
	$TAUTOMERS\
	$Tautomers.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf>\n(default: 0);-c, --canonical=[true|false]\ntrue: take canonical tautomer\n(default: false);-R,  --rational=[true|false]\ntrue: generates only rational\ntautomers (default: false);-g, --generic=[true|false]\ntrue: take generic tautomer\n(default: false);-M, --major=[true|false]\ntrue: take major tautomer\n(default: false);-d, --dominants=[true|false]\ntrue: take dominant tautomers\n(default: true);-D, --distribution=[true|false]\ntrue: calculate dominant tautomer\ndistribution (default: false);-m, --max=<count> maximum number of structures\nto be generated (default: 200);-l, --pathlength=<length> maximum allowed length of the\ntautomerization path in chemical bonds\n(default: 4);-H, --pH=<pH value> considers pH effect at this\npH. Only has effect when dominant\ntautomers are generated.\n(default: do not consider pH effect);-a, --protectaromaticity=[true|false]\ntrue: protect aromaticity\n(default: true);-C, --protectcharge=[true|false]\ntrue: protect charge (default: true);-e, --excludeantiaroma=[true|false]\ntrue: exclude antiaromatic compounds\n(default: true);-P, --protectdoublebondstereo=[true|false]\ntrue: protect double bond stereo\n(default: false);-T, --protectalltetrahedralcenters=[true|false]\ntrue: protect all tetrahedral stereo\ncenters (default: false);-L, --protectlabeledtetrahedralcenters=[true|false]\ntrue: protect labeled tetrahedral\nstereo centers (default: false);-E, --protectestergroups=[true|false]\ntrue: protect ester groups\n(default: true);-s, --symfilter=[true|false]\ntrue: filter out symmetrical\nstructures false: allow duplicates\n(default: true);-f, --format=<output format>\n(default: fused smiles,\nmultiple molecule output if specified);-t, --tag=<SDF/MRV tag to store the\ndistribution value>\n(default: TAUTOMER_DISTRIBUTION);-r, --ring=[true|false]\nEnable/disable ring tautomers.\nDefault false.\
	$cxcalc tautomers -f sdf test.mol\n  cxcalc tautomers --dominants false --rational true test.mol --format smiles

canonicaltautomer=$chemaxon.marvin.calculations.TautomerizationPlugin\
	$MultiformPlugin.jar\
	$Isomers\
	$f=format;c=canonical:true;R=rational:false;a=protectaromaticity:true;C=protectcharge:true;e=excludeantiaroma:true;P=protectdoublebondstereo:false;T=protectalltetrahedralcenters:false;L=protectlabeledtetrahedralcenters:false;E=protectestergroups:true\
	$CANONICAL_TAUTOMER\
	$Canonical tautomer.\
	$-f, --format=<output format>\n(default: smiles table, multiple\nmolecule output if specified);-R,  --rational=[true|false]\ntrue: generates only rational\ntautomers (default: false);-a, --protectaromaticity=[true|false]\ntrue: protect aromaticity\n(default: true);-C, --protectcharge=[true|false]\ntrue: protect charge (default: true);-e, --excludeantiaroma=[true|false]\ntrue: exclude antiaromatic compounds\n(default: true);-P, --protectdoublebondstereo=[true|false]\ntrue: protect double bond stereo\n(default: false);-T, --protectalltetrahedralcenters=[true|false]\ntrue: protect all tetrahedral stereo\ncenters (default: false);-L, --protectlabeledtetrahedralcenters=[true|false]\ntrue: protect labeled tetrahedral\nstereo centers (default: false);-E, --protectestergroups=[true|false]\ntrue: protect ester groups\n(default: true)\
	$cxcalc canonicaltautomer -f sdf test.mol

#deprecated
moststabletautomer=$chemaxon.marvin.calculations.TautomerizationPlugin\
	$MultiformPlugin.jar\
	$Isomers\
	$f=format;M=moststable:true;l=pathlength:4;a=protectaromaticity:true;C=protectcharge:true;e=excludeantiaroma:true;P=protectdoublebondstereo:false;T=protectalltetrahedralcenters:false;L=protectlabeledtetrahedralcenters:false;E=protectestergroups:true\
	$MOST_STABLE_TAUTOMER\
	$Most stable tautomer. Depreacated, use "majortautomer" instead.\
	$-f, --format=<output format>\n(default: smiles table, multiple\nmolecule output if specified);-l, --pathlength=<length> maximum allowed length of\nthe tautomerization path in chemical\nbonds (default: 4);-a, --protectaromaticity=[true|false]\ntrue: protect aromaticity\n(default: true);-C, --protectcharge=[true|false]\ntrue: protect charge (default: true);-e, --excludeantiaroma=[true|false]\ntrue: exclude antiaromatic compounds\n(default: true);-P, --protectdoublebondstereo=[true|false]\ntrue: protect double bond stereo\n(default: false);-T, --protectalltetrahedralcenters=[true|false]\ntrue: protect all tetrahedral stereo\ncenters (default: false);-L, --protectlabeledtetrahedralcenters=[true|false]\ntrue: protect labeled tetrahedral\nstereo centers (default: false);-E, --protectestergroups=[true|false]\ntrue: protect ester groups\n(default: true)\
	$cxcalc moststabletautomer -f sdf test.mol

generictautomer=$chemaxon.marvin.calculations.TautomerizationPlugin\
	$MultiformPlugin.jar\
	$Isomers\
	$f=format;g=generic:true;l=pathlength:4;a=protectaromaticity:true;C=protectcharge:true;e=excludeantiaroma:true;P=protectdoublebondstereo:false;T=protectalltetrahedralcenters:false;L=protectlabeledtetrahedralcenters:false;E=protectestergroups:true\
	$GENERIC_TAUTOMER\
	$Generic tautomer.\
	$-f, --format=<output format>\n(default: smiles table, multiple\nmolecule output if specified);-l, --pathlength=<length> maximum allowed length of\nthe tautomerization path in chemical\nbonds (default: 4);-a, --protectaromaticity=[true|false]\ntrue: protect aromaticity\n(default: true);-C, --protectcharge=[true|false]\ntrue: protect charge (default: true);-e, --excludeantiaroma=[true|false]\ntrue: exclude antiaromatic compounds\n(default: true);-P, --protectdoublebondstereo=[true|false]\ntrue: protect double bond stereo\n(default: false);-T, --protectalltetrahedralcenters=[true|false]\ntrue: protect all tetrahedral stereo\ncenters (default: false);-L, --protectlabeledtetrahedralcenters=[true|false]\ntrue: protect labeled tetrahedral\nstereo centers (default: false);-E, --protectestergroups=[true|false]\ntrue: protect ester groups\n(default: true)\
	$cxcalc generictautomer -f sdf test.mol

majortautomer=$chemaxon.marvin.calculations.TautomerizationPlugin\
	$MultiformPlugin.jar\
	$Isomers\
	$f=format;M=major:true;l=pathlength:4;H=pH;a=protectaromaticity:true;C=protectcharge:true;e=excludeantiaroma:true;P=protectdoublebondstereo:false;T=protectalltetrahedralcenters:false;L=protectlabeledtetrahedralcenters:false;E=protectestergroups:true\
	$MAJOR_TAUTOMER\
	$Major tautomer.\
	$-f, --format=<output format>\n(default: smiles table, multiple\nmolecule output if specified);-l, --pathlength=<length> maximum allowed length of\nthe tautomerization path in chemical\nbonds (default: 4);-H, --pH=<pH value> considers pH effect at\nthis pH. (default: do not consider\npH effect);-a, --protectaromaticity=[true|false]\ntrue: protect aromaticity\n(default: true);-C, --protectcharge=[true|false]\ntrue: protect charge (default: true);-e, --excludeantiaroma=[true|false]\ntrue: exclude antiaromatic compounds\n(default: true);-P, --protectdoublebondstereo=[true|false]\ntrue: protect double bond stereo\n(default: false);-T, --protectalltetrahedralcenters=[true|false]\ntrue: protect all tetrahedral stereo\ncenters (default: false);-L, --protectlabeledtetrahedralcenters=[true|false]\ntrue: protect labeled tetrahedral\nstereo centers (default: false);-E, --protectestergroups=[true|false]\ntrue: protect ester groups\n(default: true);\
	$cxcalc majortautomer -H 7.4 -f sdf test.mol

dominanttautomerdistribution=$chemaxon.marvin.calculations.TautomerizationPlugin\
	$MultiformPlugin.jar\
	$Isomers\
	$d=dominants:true;D=distribution:true;p=precision:0;l=pathlength:4;H=pH;a=protectaromaticity:true;C=protectcharge:true;e=excludeantiaroma:true;P=protectdoublebondstereo:false;T=protectalltetrahedralcenters:false;L=protectlabeledtetrahedralcenters:false;E=protectestergroups:true;f=format:sdf:-a;t=tag:TAUTOMER_DISTRIBUTION\
	$DOMINANT_TAUTOMER_DISTRIBUTION\
	$Dominant tautomer distribution.\
	$-p, --precision=<floating point precision as number\nof fractional digits: 0-8 or inf>\n (default: 0);-l, --pathlength=<length> maximum allowed length of the\ntautomerization path in chemical bonds\n(default: 4);-H, --pH=<pH value> considers pH effect at\nthis pH. (default: do not consider\npH effect);-a, --protectaromaticity=[true|false]\ntrue: protect aromaticity\n(default: true);-C, --protectcharge=[true|false]\ntrue: protect charge (default: true);-e, --excludeantiaroma=[true|false]\ntrue: exclude antiaromatic compounds\n(default: true);-P, --protectdoublebondstereo=[true|false]\ntrue: protect double bond stereo\n(default: false);-T, --protectalltetrahedralcenters=[true|false]\ntrue: protect all tetrahedral stereo\ncenters (default: false);-L, --protectlabeledtetrahedralcenters=[true|false]\ntrue: protect labeled tetrahedral\nstereo centers (default: false);-E, --protectestergroups=[true|false]\ntrue: protect ester groups\n(default: true);-f, --format=<output format> (default: sdf:-a);-t, --tag=<SDF/MRV tag to store\nthe distribution value>\n(default: TAUTOMER_DISTRIBUTION)\
	$cxcalc dominanttautomerdistribution test.mol

tautomercount=$chemaxon.marvin.calculations.TautomerizationPlugin\
	$MultiformPlugin.jar\
	$Isomers\
	$d=dominants:true;R=rational:false;m=max:200;l=pathlength:4;H=pH;a=protectaromaticity:true;C=protectcharge:true;e=excludeantiaroma:true;P=protectdoublebondstereo:false;T=protectalltetrahedralcenters:false;L=protectlabeledtetrahedralcenters:false;E=protectestergroups:true;t=type:count;s=symfilter:true\
	$TAUTOMER_COUNT\
	$The number of tautomers.\
	$-d, --dominants=[true|false]\ntrue: take dominant tautomers\n(default: true);-R,  --rational=[true|false]\ntrue: takes only rational tautomers\n(default: false);-m, --max=<count> max. number of structures to\nbe generated (default: 200);-l, --pathlength=<length> maximum allowed length of the\ntautomerization path in chemical bonds;-H, --pH=<pH value> considers pH effect at this\npH. Only has effect when dominant\ntautomers are generated.\n(default: do not consider pH effect);-a, --protectaromaticity=[true|false]\ntrue: protect aromaticity\n(default: true);-C, --protectcharge=[true|false]\ntrue: protect charge (default: true);-e, --excludeantiaroma=[true|false]\ntrue: exclude antiaromatic compounds\n(default: true);-s, --symfilter=[true|false]\ntrue: filter out symmetrical\nstructures\nfalse: allow duplicates\n(default: true);-P, --protectdoublebondstereo=[true|false]\ntrue: protect double bond stereo\n(default: false);-T, --protectalltetrahedralcenters=[true|false]\ntrue: protect all tetrahedral stereo\ncenters (default: false);-L, --protectlabeledtetrahedralcenters=[true|false]\ntrue: protect labeled tetrahedral\nstereo centers (default: false);-E, --protectestergroups=[true|false]\ntrue: protect ester groups\n(default: true)\
	$cxcalc tautomerCount -s false test.mol

stereoisomers=$chemaxon.marvin.calculations.StereoisomerPlugin\
	$StereoisomerPlugin.jar\
	$Isomers\
	$t=type:structures;s=stereoisomerism:both;m=maxstereoisomers:1000;D=protectdoublebondstereo:false;T=protecttetrahedralstereo:false;v=verify3d:false;3=in3d:false;f=format:sdf\
	$STEREOISOMERS\
	$Generates stereoisomers of the molecule.\
	$-f, --format=<output format> (default: sdf);-m, --maxstereoisomers=<maximum number of stereoisomers to be\ngenerated> (default: 1000);-D, --protectdoublebondstereo=[true|false]\ntrue: protect double bond stereo\n(default: false);-T, --protecttetrahedralstereo=[true|false]\ntrue: protect tetrahedral stereo centers\n(default: false);-v, --verify3d=[true|false] if true invalid 3D structures of\ngenereated stereoisomers are filtered;-3, --in3d=[true|false] if true 3D structures are\ngenerated (invalid 3D structures are filtered)\
	$cxcalc stereoisomers -v true test.sdf

stereoisomercount=$chemaxon.marvin.calculations.StereoisomerPlugin\
	$StereoisomerPlugin.jar\
	$Isomers\
	$t=type:count;s=stereoisomerism:both;m=maxstereoisomers:1000;D=protectdoublebondstereo:false;T=protecttetrahedralstereo:false;v=verify3d:false;3=in3d:false\
	$STEREOISOMER_COUNT\
	$The number of stereoisomers of the molecule.\
	$-m, --maxstereoisomers=<maximum number of double bond stereoisomers\nto be generated> (default: 1000);-D, --protectdoublebondstereo=[true|false]\ntrue: protect double bond stereo\n(default: false);-T, --protecttetrahedralstereo=[true|false]\ntrue: protect tetrahedral stereo centers\n(default: false)\
	$cxcalc stereoisomercount test.sdf

tetrahedralstereoisomers=$chemaxon.marvin.calculations.StereoisomerPlugin\
	$StereoisomerPlugin.jar\
	$Isomers\
	$t=type:structures;s=stereoisomerism:tetrahedral;m=maxstereoisomers:1000;T=protecttetrahedralstereo:false;v=verify3d:false;3=in3d:false;f=format:sdf\
	$TETRAHEDRAL_STEREOISOMERS\
	$Generates tetrahedral stereoisomers of the molecule.\
	$-f, --format=<output format> (default: sdf);-m, --maxstereoisomers=<maximum number of tetrahedral stereoisomers\nto be generated> (default: 1000);-T, --protecttetrahedralstereo=[true|false]\ntrue: protect tetrahedral stereo centers\n(default: false);-v, --verify3d=[true|false] if true invalid 3D structures of\ngenereated stereoisomers are filtered;-3, --in3d=[true|false] if true 3D structures are\ngenerated (invalid 3D structures are filtered)\
	$cxcalc tetrahedralstereoisomers -3 true test.sdf

tetrahedralstereoisomercount=$chemaxon.marvin.calculations.StereoisomerPlugin\
	$StereoisomerPlugin.jar\
	$Isomers\
	$t=type:count;s=stereoisomerism:tetrahedral;m=maxstereoisomers:1000;T=protecttetrahedralstereo:false;v=verify3d:false;3=in3d:false\
	$TETRAHEDRAL_STEREOISOMER_COUNT\
	$The number of tetrahedral stereoisomers of the molecule.\
	$-m, --maxstereoisomers=<maximum number of double bond stereoisomers\nto be generated> (default: 1000);-T, --protecttetrahedralstereo=[true|false]\ntrue: protect tetrahedral stereo centers\n(default: false)\
	$cxcalc tetrahedralstereoisomercount test.sdf

doublebondstereoisomers=$chemaxon.marvin.calculations.StereoisomerPlugin\
	$StereoisomerPlugin.jar\
	$Isomers\
	$t=type:structures;s=stereoisomerism:doublebond;m=maxstereoisomers:1000;D=protectdoublebondstereo:false;v=verify3d:false;3=in3d:false;f=format:sdf\
	$DOUBLEBOND_STEREOISOMERS\
	$Generates double-bond stereoisomers of the molecule.\
	$-f, --format=<output format> (default: sdf);-m, --maxstereoisomers=<maximum number of double bond stereoisomers\nto be generated> (default: 1000);-D, --protectdoublebondstereo=[true|false]\ntrue: protect double bond stereo\n(default: false);-v, --verify3d=[true|false] if true invalid 3D structures of\ngenereated stereoisomers are filtered;-3, --in3d=[true|false] if true 3D structures are\ngenerated (invalid 3D structures are filtered)\
	$cxcalc doublebondstereoisomers -f mrv test.sdf

doublebondstereoisomercount=$chemaxon.marvin.calculations.StereoisomerPlugin\
	$StereoisomerPlugin.jar\
	$Isomers\
	$t=type:count;s=stereoisomerism:doublebond;m=maxstereoisomers:1000;D=protectdoublebondstereo:false;v=verify3d:false;3=in3d:false\
	$DOUBLEBOND_STEREOISOMER_COUNT\
	$The number of double-bond stereoisomers of the molecule.\
	$-m, --maxstereoisomers=<maximum number of double bond stereoisomers\nto be generated> (default: 1000);-D, --protectdoublebondstereo=[true|false]\ntrue: protect double bond stereo\n(default: false)\
	$cxcalc doublebondstereoisomercount test.sdf

# Conformation

conformers=$chemaxon.marvin.calculations.ConformerPlugin\
	$ConformerPlugin.jar\
	$Conformation\
	$t=type:structures;x=forcefield:dreiding;m=maxconformers:100;d=diversity:0.1;s=saveconfdesc:false;e=hyperfine:false;y=prehydrogenize:true;l=timelimit:900;O=optimization:1;f=format:sdf\
	$CONFORMERS\
	$Calculates the conformers of the molecule.\
	$-f, --format=<output format> should be a 3D format (default: sdf);-x, --forcefield=[dreiding|mmff94] forcefield used for calculation\n(default: dreiding);-m, --maxconformers=<maximum number of conformers to be generated>\n(default: 100);-d, --diversity=<diversity limit> (default: 0.1);-s, --saveconfdesc=[true|false] if true a single conformer is saved\nwith a property containing conformer information\n(default: false);-e, --hyperfine=[true|false] if true hyperfine option is set\n(default: false);-y, --prehydrogenize=[true|false] if true prehydrogenize is done before\ncalculation, if false calculation is done without\nhydrogens (default: true);-l, --timelimit=<timelimit for calculation in sec> (default: 900);-O, --optimization=[0|1|2|3] conformer generation optimiztaion limit\nfor different enviroments\n{0}: very loose  (limit=0.01)\n{1}: normal      (limit=0.0010)\n{2}: strict      (limit=1.0E-4)\n{3}: very strict (limit=1.0E-5)\n(default: 1)\
	$cxcalc conformers -m 250 -s true test.sdf

# alias for leconformer
lowestenergyconformer=$chemaxon.marvin.calculations.ConformerPlugin\
	$ConformerPlugin.jar\
	$Conformation\
	$t=type:structure;x=forcefield:dreiding;L=leconformercalculation:true;e=hyperfine:false;y=prehydrogenize:true;l=timelimit:900;O=optimization:1;m=multifrag:false;f=format:sdf\
	$LECONFORMER\
	$Calculates the lowest energy conformer of the molecule.\
	$-f, --format=<output format> should be a 3D format (default: sdf);-x, --forcefield=[dreiding|mmff94] forcefield used for calculation\n(default: dreiding);-e, --hyperfine=[true|false] if true hyperfine option is set\n(default: false);-y, --prehydrogenize=[true|false] if true prehydrogenize is done before\ncalculation, if false calculation is done without\nhydrogens (default: true);-l, --timelimit=<timelimit for calculation in sec> (default: 900);-O, --optimization=[0|1|2|3] conformer generation optimiztaion limit for\ndifferent enviroments\n{0}: very loose  (limit=0.01)\n{1}: normal      (limit=0.0010)\n{2}: strict      (limit=1.0E-4)\n{3}: very strict (limit=1.0E-5)\n(default: 1);-m, --multifrag=[true|false] in case of multi-fragment molecules and\nif mmff94 forcefield selected:\ntakes largest fragment if false,\ntakes whole molecule if true (default: false)\
	$cxcalc lowestenergyconformer -f mrv test.sdf

leconformer=$chemaxon.marvin.calculations.ConformerPlugin\
	$ConformerPlugin.jar\
	$Conformation\
	$t=type:structure;x=forcefield:dreiding;L=leconformercalculation:true;e=hyperfine:false;y=prehydrogenize:true;l=timelimit:900;O=optimization:1;m=multifrag:false;f=format:sdf\
	$LECONFORMER\
	$Calculates the lowest energy conformer of the molecule.\
	$-f, --format=<output format> should be a 3D format (default: sdf);-x, --forcefield=[dreiding|mmff94] forcefield used for calculation\n(default: dreiding);-e, --hyperfine=[true|false] if true hyperfine option is set\n(default: false);-y, --prehydrogenize=[true|false] if true prehydrogenize is done before\ncalculation, if false calculation is done without\nhydrogens (default: true);-l, --timelimit=<timelimit for calculation in sec> (default: 900);-O, --optimization=[0|1|2|3] conformer generation optimiztaion limit for\ndifferent enviroments\n{0}: very loose  (limit=0.01)\n{1}: normal      (limit=0.0010)\n{2}: strict      (limit=1.0E-4)\n{3}: very strict (limit=1.0E-5)\n(default: 1);-m, --multifrag=[true|false]  in case of multi-fragment molecules and\nif mmff94 forcefield selected:\ntakes largest fragment if false,\ntakes whole molecule if true (default: false)\
	$cxcalc leconformer -f mrv test.sdf

#mmff94optimizedstructure=$chemaxon.marvin.calculations.ConformerPlugin\
#	$ConformerPlugin.jar\
#	$Conformation\
#	$t=type:structure;L=leconformercalculation:true;e=hyperfine:false;y=prehydrogenize:true;M=mmff94optimization:true;O=optimization:1;f=format:sdf\
#	$MMFF94_OPTIMIZED_STRUCTURE\
#	$Calculates the MMFF94 optimized lowest energy conformer.\
#	$-f, --format=<output format> should be a 3D format (default: sdf);-y, --prehydrogenize=[true|false] if true prehydrogenize is done before\ncalculation, if false calculation is done without\nhydrogens (default: true);\
#	$cxcalc mmff94optimizedstructure -f mrv test.sdf

hasvalidconformer=$chemaxon.marvin.calculations.ConformerPlugin\
	$ConformerPlugin.jar\
	$Conformation\
	$t=type:hasvalidconformer\
	$HAS_VALID_CONFORMER\
	$Calculates if the molecule has a conformer.\
	$-\
	$cxcalc hasvalidconformer test.sdf

# alias for moldyn
moleculardynamics=$chemaxon.marvin.calculations.MolecularDynamicsPlugin\
	$MolecularDynamicsPlugin.jar\
	$Conformation\
	$x=forcefield:dreiding;i=integrator:velocityverlet;n=stepno:1000;m=steptime:0.1;T=temperature:300;s=samplinginterval:10;f=format:sdf\
	$MOLDYN\
	$Molecular Dynamics.\
	$-f, --format=<output format> should be a 3D format (default: sdf);-x, --forcefield=[dreiding|mmff94] forcefield used for calculation\n(default: dreiding);-i, --integrator=[positionverlet|velocityverlet|leapfrog]\nintegrator type used for calculation\n(default: velocityverlet);-n, --stepno=<number of simulation steps> (default: 1000);-m, --steptime=<time between steps in femtoseconds> (default: 0.1);-T, --temperature=<temperature in Kelvin> (default: 300 K);-s, --samplinginterval=<sampling interval in femtoseconds> (default: 10)\
	$cxcalc moleculardynamics test.mol

moldyn=$chemaxon.marvin.calculations.MolecularDynamicsPlugin\
	$MolecularDynamicsPlugin.jar\
	$Conformation\
	$x=forcefield:dreiding;i=integrator:velocityverlet;n=stepno:1000;m=steptime:0.1;T=temperature:300;s=samplinginterval:10;f=format:sdf\
	$MOLDYN\
	$Molecular Dynamics.\
	$-f, --format=<output format> should be a 3D format (default: sdf);-x, --forcefield=[dreiding|mmff94] forcefield used for calculation\n(default: dreiding);-i, --integrator=[positionverlet|velocityverlet|leapfrog]\nintegrator type used for calculation\n(default: velocityverlet);-n, --stepno=<number of simulation steps> (default: 1000);-m, --steptime=<time between steps in femtoseconds> (default: 0.1);-T, --temperature=<temperature in Kelvin> (default: 300 K);-s, --samplinginterval=<sampling interval in femtoseconds> (default: 10)\
	$cxcalc moldyn test.mol

# Geometry

# alias for topanal
topologyanalysistable=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$a=arom:general;t=type:atomcount,aliphaticatomcount,aromaticatomcount,bondcount,aliphaticbondcount,aromaticbondcount,rotatablebondcount,ringcount,aliphaticringcount,aromaticringcount,heteroringcount,heteroaliphaticringcount,heteroaromaticringcount,ringatomcount,ringbondcount,chainatomcount,chainbondcount,smallestringsize,largestringsize;s=single:false\
	$TOPANAL\
	$Molecule topology data calculation: atomcount,aliphaticatomcount,\naromaticatomcount,bondcount,aliphaticbondcount,aromaticbondcount,\nrotatablebondcount,ringcount,aliphaticringcount,aromaticringcount,\nheteroringcount,heteroaliphaticringcount,heteroaromaticringcount,\nringatomcount,ringbondcount,chainatomcount,chainbondcount,\nsmallestringsize,largestringsize.\
	$-a --arom=[general|basic|loose] sets aromatization method;-t, --type=[atomcount|aliphaticatomcount|aromaticatomcount|\nbondcount|aliphaticbondcount|aromaticbondcount|\nrotatablebondcount|ringcount|aliphaticringcount|\naromaticringcount|heteroringcount|heteroaliphaticringcount|\nheteroaromaticringcount|ringatomcount|ringbondcount|\nchainatomcount|chainbondcount|\nsmallestringsize|largestringsize] (default: all);-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc topologyanalysistable test.mol

topanal=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$a=arom:general;t=type:atomcount,aliphaticatomcount,aromaticatomcount,bondcount,aliphaticbondcount,aromaticbondcount,rotatablebondcount,ringcount,aliphaticringcount,aromaticringcount,heteroringcount,heteroaliphaticringcount,heteroaromaticringcount,ringatomcount,ringbondcount,chainatomcount,chainbondcount,smallestringsize,largestringsize;s=single:false\
	$TOPANAL\
	$Molecule topology data calculation: atomcount,aliphaticatomcount,\naromaticatomcount,bondcount,aliphaticbondcount,aromaticbondcount,\nrotatablebondcount,ringcount,aliphaticringcount,aromaticringcount,\nheteroringcount,heteroaliphaticringcount,heteroaromaticringcount,\nringatomcount,ringbondcount,chainatomcount,chainbondcount,\nsmallestringsize,largestringsize.\
	$-a --arom=[general|basic|loose] sets aromatization method;-t, --type=[atomcount|aliphaticatomcount|aromaticatomcount|\nbondcount|aliphaticbondcount|aromaticbondcount|\nrotatablebondcount|ringcount|aliphaticringcount|\naromaticringcount|heteroringcount|heteroaliphaticringcount|\nheteroaromaticringcount|ringatomcount|ringbondcount|\nchainatomcount|chainbondcount|\nsmallestringsize|largestringsize] (default: all);-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc topanal test.mol

aliphaticatomcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$a=arom:general;t=type:aliphaticatomcount;s=single:false\
	$ALIPHATIC_ATOMCOUNT\
	$Aliphatic atom count.\
	$-a --arom=[general|basic|loose] sets aromatization method;-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc aliphaticatomcount test.mol

aromaticatomcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$a=arom:general;t=type:aromaticatomcount;s=single:false\
	$AROMATIC_ATOMCOUNT\
	$Aromatic atom count.\
	$-a --arom=[general|basic|loose] sets aromatization method;-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc aromaticatomcount test.mol

bondcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:bondcount;s=single:false\
	$BONDCOUNT\
	$Bond count.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc bondcount test.mol

aliphaticbondcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$a=arom:general;t=type:aliphaticbondcount;s=single:false\
	$ALIPHATIC_BONDCOUNT\
	$Aliphatic bond count.\
	$-a --arom=[general|basic|loose] sets aromatization method;-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc aliphaticbondcount test.mol

aromaticbondcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$a=arom:general;t=type:aromaticbondcount;s=single:false\
	$AROMATIC_BONDCOUNT\
	$Aromatic bond count.\
	$-a --arom=[general|basic|loose] sets aromatization method;-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc aromaticbondcount test.mol

rotatablebondcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:rotatablebondcount;s=single:false\
	$ROTATABLE_BONDCOUNT\
	$Rotatable bond count.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc rotatablebondcount test.mol

fragmentcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:fragmentcount;s=single:false\
	$FRAGMENTCOUNT\
	$Fragment count.\
	$-\
	$cxcalc fragmentcount test.mol

ringcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:ringcount;s=single:false\
	$RINGCOUNT\
	$Ring count.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc ringcount test.mol

ringcountofsize=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$a=arom:general;t=type:ringcountofsize;s=single:false;z=size\
	$RINGCOUNT_OF_SIZE\
	$Ring count of size.\
	$-a --arom=[general|basic|loose] sets aromatization method;-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false);-z, --size=<ring size> size of rings to count\
	$cxcalc ringcountofsize -z 5 test.mol

aliphaticringcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$a=arom:general;t=type:aliphaticringcount;s=single:false\
	$ALIPHATIC_RINGCOUNT\
	$Aliphatic ring count.\
	$-a --arom=[general|basic|loose] sets aromatization method;-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc aliphaticringcount test.mol

aliphaticringcountofsize=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$a=arom:general;t=type:aliphaticringcountofsize;s=single:false;z=size\
	$ALIPHATIC_RINGCOUNT_OF_SIZE\
	$Aliphatic ring count of size.\
	$-a --arom=[general|basic|loose] sets aromatization method;-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false);-z, --size=<ring size> size of rings to count\
	$cxcalc aliphaticringcountofsize -z 5 test.mol

aromaticringcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$a=arom:general;t=type:aromaticringcount;s=single:false\
	$AROMATIC_RINGCOUNT\
	$Aromatic ring count.\
	$-a --arom=[general|basic|loose] sets aromatization method;-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc aromaticringcount test.mol

aromaticringcountofsize=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$a=arom:general;t=type:aromaticringcountofsize;s=single:false;z=size\
	$AROMATIC_RINGCOUNT_OF_SIZE\
	$Aromatic ring count of size.\
	$-a --arom=[general|basic|loose] sets aromatization method;-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false);-z, --size=<ring size> size of rings to count\
	$cxcalc aromaticringcountofsize -z 6 test.mol

heteroringcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:heteroringcount;s=single:false\
	$HETERO_RINGCOUNT\
	$Hetero ring count.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc heteroringcount test.mol

heteroaliphaticringcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$a=arom:general;t=type:heteroaliphaticringcount;s=single:false\
	$HETEROALIPHATIC_RINGCOUNT\
	$Heteroaliphatic ring count.\
	$-a --arom=[general|basic|loose] sets aromatization method;-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc heteroaliphaticringcount test.mol	
	
heteroaromaticringcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$a=arom:general;t=type:heteroaromaticringcount;s=single:false\
	$HETEROAROMATIC_RINGCOUNT\
	$Heteroaromatic ring count.\
	$-a --arom=[general|basic|loose] sets aromatization method;-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc heteroaromaticringcount test.mol

carboringcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:carboringcount;s=single:false\
	$CARBO_RINGCOUNT\
	$Carbo ring count.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc carboringcount test.mol

carboaliphaticringcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$a=arom:general;t=type:carboaliphaticringcount;s=single:false\
	$CARBOALIPHATIC_RINGCOUNT\
	$Carboaliphatic ring count.\
	$-a --arom=[general|basic|loose] sets aromatization method;-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc carboaliphaticringcount test.mol

carboaromaticringcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$a=arom:general;t=type:carboaromaticringcount;s=single:false\
	$CARBOAROMATIC_RINGCOUNT\
	$Carboaromatic ring count.\
	$-a --arom=[general|basic|loose] sets aromatization method;-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc carboaromaticringcount test.mol

ringatomcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:ringatomcount;s=single:false\
	$RINGATOMCOUNT\
	$Ring atom count.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc ringatomcount test.mol

ringbondcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:ringbondcount;s=single:false\
	$RINGBONDCOUNT\
	$Ring bond count.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc ringbondcount test.mol

chainatomcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:chainatomcount;s=single:false\
	$CHAINATOMCOUNT\
	$Chain atom count.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc chainatomcount test.mol

chainbondcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:chainbondcount;s=single:false\
	$CHAINBONDCOUNT\
	$Chain bond count.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc chainbondcount test.mol

smallestringsize=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:smallestringsize;s=single:false\
	$SMALLEST_RINGSIZE\
	$Smallest ring size.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc smallestringsize test.mol

largestringsize=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:largestringsize;s=single:false\
	$LARGEST_RINGSIZE\
	$Largest ring size.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc largestringsize test.mol

fusedringcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:fusedringcount;s=single:false\
	$FUSED_RINGCOUNT\
	$The number of fused rings\n(SSSR smallest set of smallest rings).\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc fusedringcount test.mol

fusedaliphaticringcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$a=arom:general;t=type:fusedaliphaticringcount;s=single:false\
	$FUSEDALIPHATIC_RINGCOUNT\
	$The number of fused aliphatic rings\n(SSSR smallest set of smallest aliphatic rings).\
	$-a --arom=[general|basic|loose] sets aromatization method;-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc fusedaliphaticringcount test.mol

fusedaromaticringcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$a=arom:general;t=type:fusedaromaticringcount;s=single:false\
	$FUSEDAROMATIC_RINGCOUNT\
	$The number of fused aromatic rings\n(SSSR smallest set of smallest aromatic rings).\
	$-a --arom=[general|basic|loose] sets aromatization method;-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc fusedaromaticringcount test.mol

ringsystemcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:ringsystemcount;s=single:false\
	$RING_SYSTEM_COUNT\
	$The number of ring systems.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc ringsystemcount test.mol

ringsystemcountofsize=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:ringsystemcountofsize;s=single:false;z=size\
	$RING_SYSTEM_COUNT_OF_SIZE\
	$Ring system count of size.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false);-z, --size=<size> size of ring systems to count\
	$cxcalc ringsystemcountofsize -z 3 test.mol

largestringsystemsize=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:largestringsystemsize;s=single:false\
	$LARGEST_RING_SYSTEM_SIZE\
	$Largest ring system size.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc largestringsystemsize test.mol

smallestringsystemsize=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:smallestringsystemsize;s=single:false\
	$SMALLEST_RING_SYSTEM_SIZE\
	$Smallest ring system size.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes smallest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc smallestringsystemsize test.mol

asymmetricatomcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:asymmetricatomcount;s=single:false\
	$ASYMMETRIC_ATOMCOUNT\
	$The number of asymmetric atoms.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc asymmetricatomcount test.mol

asymmetricatoms=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:asymmetricatoms;s=single:false\
	$ASYMMETRIC_ATOMS\
	$The asymmetric atoms.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc asymmetricatoms test.mol

chiralcentercount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:chiralcentercount;s=single:false\
	$CHIRALCENTER_COUNT\
	$The number of tetrahedral stereogenic center atoms.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc chiralcentercount test.mol

chiralcenters=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:chiralcenters;s=single:false\
	$CHIRALCENTER_COUNT\
	$The the chiral center atoms.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc chiralcenters test.mol

stereodoublebondcount=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:stereodoublebondcount;s=single:false\
	$STEREO_DOUBLE_BOND_COUNT\
	$The number of stereo double bonds.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc stereodoublebondcount test.mol

cyclomaticnumber=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:cyclomaticnumber;s=single:false\
	$CYCLOMATIC_NUMBER\
	$The cyclomatic number.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc cyclomaticnumber test.mol

plattindex=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:plattindex;s=single:false\
	$PLATT_INDEX\
	$The Platt index.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc plattindex test.mol

randicindex=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:randicindex;p=precision:2;s=single:false\
	$RANDIC_INDEX\
	$The Randic index.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc randicindex test.mol

balabanindex=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:balabanindex;p=precision:2;s=single:false\
	$BALABAN_INDEX\
	$The Balaban index.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc balabanindex test.mol

distancedegree=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:distancedegree;s=single:false\
	$DISTANCE_DEGREE\
	$Distance degree of atom.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc distancedegree test.mol

eccentricity=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:eccentricity;s=single:false\
	$ECCENTRICITY\
	$Eccentricity of atom.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc eccentricity test.mol

hararyindex=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:hararyindex;p=precision:2;s=single:false\
	$HARARY_INDEX\
	$Harary index.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc hararyindex test.mol

hyperwienerindex=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:hyperwienerindex;s=single:false\
	$HYPERWIENER_INDEX\
	$Hyper Wiener index.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc hyperwienerindex test.mol

szegedindex=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:szegedindex;s=single:false\
	$SZEGED_INDEX\
	$Szeged index.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc szegedindex test.mol

wienerindex=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:wienerindex;s=single:false\
	$WIENER_INDEX\
	$Wiener index.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc wienerindex test.mol

wienerpolarity=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:wienerpolarity;s=single:false\
	$WIENER_POLARITY\
	$Wiener polarity.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc wienerpolarity test.mol

stericeffectindex=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:stericeffectindex;p=precision:2;s=single:false\
	$STERICEFFECT_INDEX\
	$Steric effect index.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc stericeffectindex test.mol

aromaticatom=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$a=arom:general;t=type:aromaticatom;s=single:false\
	$AROMATIC_ATOM\
	$Checks if a specified atom is aromatic.\
	$-a --arom=[general|basic|loose] sets aromatization method;-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc aromaticatom test.mol

aliphaticatom=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$a=arom:general;t=type:aliphaticatom;s=single:false\
	$ALIPHATIC_ATOM\
	$Checks if a specified atom is aliphatic.\
	$-a --arom=[general|basic|loose] sets aromatization method;-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc aliphaticatom test.mol

chainatom=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:chainatom;s=single:false\
	$CHAIN_ATOM\
	$Checks if a specified atom is a chain atom.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc chainatom test.mol

ringatom=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:ringatom;s=single:false\
	$RING_ATOM\
	$Checks if a specified atom is a ring atom.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc ringatom test.mol

asymmetricatom=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:asymmetricatom;s=single:false\
	$ASYMMETRIC_ATOM\
	$Checks if a specified atom is an asymmetric atom.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc asymmetricatom test.mol

chiralcenter=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:chiralcenter;s=single:false\
	$CHIRALCENTER\
	$Checks if a specified atom is a tetrahedral stereogenic center.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc chiralcenter test.mol

smallestatomringsize=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:smallestatomringsize;s=single:false\
	$SMALLEST_ATOM_RINGSIZE\
	$Size of smallest ring containing a specified atom.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc smallestatomringsize test.mol

largestatomringsize=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:largestatomringsize;s=single:false\
	$LARGEST_ATOM_RINGSIZE\
	$Size of largest ring containing a specified atom.\
	$-s, --single=[true|false] in case of multi-fragment molecules:\ntakes largest fragment if true,\ntakes whole molecule if false (default: false)\
	$cxcalc largestatomringsize test.mol

shortestpath=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:shortestpath;a=atoms;s=single:false\
	$SHORTEST_PATH\
	$Length of shortest path between two atoms.\
	$-a, --atoms=[<atom1>-<atom2>] (1-based) atom indexes of the atom pair\
	$cxcalc shortestpath -a 2-3 test.mol

connected=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:connected;a=atoms;s=single:false\
	$CONNECTED\
	$Checks if two atoms are in the same connected component.\
	$-a, --atoms=[<atom1>-<atom2>] (1-based) atom indexes of the atom pair\
	$cxcalc connected -a 2-3 test.mol

connectedgraph=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:connectedgraph;a=atoms;s=single:false\
	$CONNECTED_GRAPH\
	$Checks if the molecule graph is connected.\
	$-\
	$cxcalc connectedgraph test.mol

bondtype=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$a=arom:general;t=type:bondtype;a=atoms;s=single:false\
	$BOND_TYPE\
	$The bond type between two atoms.\
	$-a --arom=[general|basic|loose] sets aromatization method;-a, --atoms=[<atom1>-<atom2>] (1-based) atom indexes of the bond atoms\
	$cxcalc bondtype -a 2-3 test.mol

chainbond=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:chainbond;a=atoms;s=single:false\
	$CHAIN_BOND\
	$Checks if the bond is a chain bond.\
	$-a, --atoms=[<atom1>-<atom2>] (1-based) atom indexes of the bond atoms\
	$cxcalc chainbond -a 2-3 test.mol

ringbond=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:ringbond;a=atoms;s=single:false\
	$RING_BOND\
	$Checks if the bond is a ring bond.\
	$-a, --atoms=[<atom1>-<atom2>] (1-based) atom indexes of the bond atoms\
	$cxcalc ringbond -a 2-3 test.mol

rotatablebond=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:rotatablebond;a=atoms;s=single:false\
	$ROTATABLE_BOND\
	$Checks if the bond is a rotatable bond.\
	$-a, --atoms=[<atom1>-<atom2>] (1-based) atom indexes of the bond atoms\
	$cxcalc rotatablebond -a 2-3 test.mol

ringcountofatom=$chemaxon.marvin.calculations.TopologyAnalyserPlugin\
	$TopologyAnalyserPlugin.jar\
	$Geometry\
	$t=type:ringcountofatom;s=single:false\
	$RINGCOUNT_OF_ATOM\
	$Ring counts of atoms.\
	$-\
	$cxcalc ringcountofatom test.mol

#hasvalidconformer=$chemaxon.marvin.calculations.GeometryPlugin\
#	$GeometryPlugin.jar\
#	$Geometry\
#	$t=type:hasvalidconformer;l=calcforleconformer:always;o=optimization:1\
#	$HAS_VALID_CONFORMER\
#	$Calculates if the molecule has a conformer.\
#	$-o, --optimization=[0|1|2|3] conformer generation optimiztaion limit for\ndifferent enviroments\n{0}: very loose  (limit=0.01)\n{1}: normal      (limit=0.0010)\n{2}: strict      (limit=1.0E-4)\n{3}: very strict (limit=1.0E-5)\n(default: 1)\
#	$cxcalc hasvalidconformer -o 3 test.sdf

dreidingenergy=$chemaxon.marvin.calculations.GeometryPlugin\
	$GeometryPlugin.jar\
	$Geometry\
	$p=precision:2;t=type:dreidingenergy;l=calcforleconformer:if2D;o=optimization:1\
	$DREIDING_ENERGY\
	$Calculates the dreiding energy of a conformer of the molecule in kcal/mol.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-o, --optimization=[0|1|2|3] conformer generation optimiztaion limit for\ndifferent enviroments\n{0}: very loose  (limit=0.01)\n{1}: normal      (limit=0.0010)\n{2}: strict      (limit=1.0E-4)\n{3}: very strict (limit=1.0E-5)\n(default: 1);-l, --calcforleconformer=[if2D|never|always] (default: if2D)\
	$cxcalc dreidingenergy -p 1 -l always test.sdf

mmff94energy=$chemaxon.marvin.calculations.GeometryPlugin\
	$GeometryPlugin.jar\
	$Geometry\
	$p=precision:2;t=type:mmff94energy;M=mmff94optimization:false;l=calcforleconformer:if2D;o=optimization:1\
	$MMFF94_ENERGY\
	$Calculates the MMFF94 energy of the molecule in kcal/mol.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-l, --calcforleconformer=[if2D|never|always] (default: if2D);    --mmff94optimization=[true|false] sets MFF94 optimization\n(default: false) \
	$cxcalc mmff94energy --mmff94optimization test.sdf

minimalprojectionarea=$chemaxon.marvin.calculations.GeometryPlugin\
	$GeometryPlugin.jar\
	$Geometry\
	$p=precision:2;t=type:minimalprojectionarea;s=scalefactor:1;l=calcforleconformer:if2D;o=optimization:1;O=optimizeprojection:false\
	$MINIMAL_PROJECTION_AREA\
	$Calculates the minimal projection area.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-s, --scalefactor=<radius scale factor>;-o, --optimization=[0|1|2|3] conformer generation optimiztaion limit\nfor different enviroments\n{0}: very loose  (limit=0.01)\n{1}: normal      (limit=0.0010)\n{2}: strict      (limit=1.0E-4)\n{3}: very strict (limit=1.0E-5)\n(default: 1);-O, --optimizeprojection=[true|false] sets projection optimization\n(default: false);-l, --calcforleconformer=[if2D|never|always] (default: if2D)\
	$cxcalc minimalprojectionarea test.sdf

maximalprojectionarea=$chemaxon.marvin.calculations.GeometryPlugin\
	$GeometryPlugin.jar\
	$Geometry\
	$p=precision:2;t=type:maximalprojectionarea;s=scalefactor:1;l=calcforleconformer:if2D;o=optimization:1;O=optimizeprojection:false\
	$MAXIMAL_PROJECTION_AREA\
	$Calculates the maximal projection area.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-s, --scalefactor=<radius scale factor>;-o, --optimization=[0|1|2|3] conformer generation optimiztaion limit\nfor different enviroments\n{0}: very loose  (limit=0.01)\n{1}: normal      (limit=0.0010)\n{2}: strict      (limit=1.0E-4)\n{3}: very strict (limit=1.0E-5)\n(default: 1);-O, --optimizeprojection=[true|false] sets projection optimization\n(default: false);-l, --calcforleconformer=[if2D|never|always] (default: if2D)\
	$cxcalc maximalprojectionarea test.sdf

minimalprojectionradius=$chemaxon.marvin.calculations.GeometryPlugin\
	$GeometryPlugin.jar\
	$Geometry\
	$p=precision:2;t=type:minimalprojectionradius;s=scalefactor:1;l=calcforleconformer:if2D;o=optimization:1;O=optimizeprojection:false\
	$MINIMAL_PROJECTION_RADIUS\
	$Calculates the minimal projection radius.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-s, --scalefactor=<radius scale factor>;-o, --optimization=[0|1|2|3] conformer generation optimiztaion limit\nfor different enviroments\n{0}: very loose  (limit=0.01)\n{1}: normal      (limit=0.0010)\n{2}: strict      (limit=1.0E-4)\n{3}: very strict (limit=1.0E-5)\n(default: 1);-O, --optimizeprojection=[true|false] sets projection optimization\n(default: false);-l, --calcforleconformer=[if2D|never|always] (default: if2D)\
	$cxcalc minimalprojectionradius test.sdf

maximalprojectionradius=$chemaxon.marvin.calculations.GeometryPlugin\
	$GeometryPlugin.jar\
	$Geometry\
	$p=precision:2;t=type:maximalprojectionradius;s=scalefactor:1;l=calcforleconformer:if2D;o=optimization:1;O=optimizeprojection:false\
	$MAXIMAL_PROJECTION_RADIUS\
	$Calculates the maximal projection radius.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-s, --scalefactor=<radius scale factor>;-o, --optimization=[0|1|2|3] conformer generation optimiztaion limit\nfor different enviroments\n{0}: very loose  (limit=0.01)\n{1}: normal      (limit=0.0010)\n{2}: strict      (limit=1.0E-4)\n{3}: very strict (limit=1.0E-5)\n(default: 1);-O, --optimizeprojection=[true|false] sets projection optimization\n(default: false);-l, --calcforleconformer=[if2D|never|always] (default: if2D)\
	$cxcalc maximalprojectionradius test.sdf

maximalprojectionsize=$chemaxon.marvin.calculations.GeometryPlugin\
	$GeometryPlugin.jar\
	$Geometry\
	$p=precision:2;t=type:maxz;l=calcforleconformer:if2D;o=optimization:1;O=optimizeprojection:false\
	$MAXIMAL_PROJECTION_P_SIZE\
	$Calculates the size of the molecule perpendicular to the maximal projection area surface.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-o, --optimization=[0|1|2|3] conformer generation optimiztaion limit\nfor different enviroments\n{0}: very loose  (limit=0.01)\n{1}: normal      (limit=0.0010)\n{2}: strict      (limit=1.0E-4)\n{3}: very strict (limit=1.0E-5)\n(default: 1);-O, --optimizeprojection=[true|false] sets projection optimization\n(default: false);-l, --calcforleconformer=[if2D|never|always] (default: if2D)\
	$cxcalc maximalprojectionsize test.sdf

minimalprojectionsize=$chemaxon.marvin.calculations.GeometryPlugin\
	$GeometryPlugin.jar\
	$Geometry\
	$p=precision:2;t=type:minz;l=calcforleconformer:if2D;o=optimization:1;O=optimizeprojection:false\
	$MINIMAL_PROJECTION_P_SIZE\
	$Calculates the size of the molecule perpendicular to the minimal projection area surface.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-o, --optimization=[0|1|2|3] conformer generation optimiztaion limit\nfor different enviroments\n{0}: very loose  (limit=0.01)\n{1}: normal      (limit=0.0010)\n{2}: strict      (limit=1.0E-4)\n{3}: very strict (limit=1.0E-5)\n(default: 1);-O, --optimizeprojection=[true|false] sets projection optimization\n(default: false);-l, --calcforleconformer=[if2D|never|always] (default: if2D)\
	$cxcalc minimalprojectionsize test.sdf

volume=$chemaxon.marvin.calculations.GeometryPlugin\
	$GeometryPlugin.jar\
	$Geometry\
	$p=precision:2;t=type:volume;l=calcforleconformer:if2D;o=optimization:1;O=optimizeprojection:false\
	$Volume\
	$Calculates the van der Waals volume of the molecule.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-o, --optimization=[0|1|2|3] conformer generation optimiztaion limit\nfor different enviroments\n{0}: very loose  (limit=0.01)\n{1}: normal      (limit=0.0010)\n{2}: strict      (limit=1.0E-4)\n{3}: very strict (limit=1.0E-5)\n(default: 1);-l, --calcforleconformer=[if2D|never|always] (default: if2D)\
	$cxcalc volume test.sdf

distance=$chemaxon.marvin.calculations.GeometryPlugin\
	$GeometryPlugin.jar\
	$Geometry\
	$t=type:distance;a=atoms;p=precision:2;l=calcforleconformer:if2D;o=optimization:1\
	$DISTANCE\
	$Distance between two atoms.\
	$-a, --atoms=[<atom1>-<atom2>] (1-based) atom indexes of the atom pair;-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-o, --optimization=[0|1|2|3] conformer generation optimiztaion limit for\ndifferent enviroments\n{0}: very loose  (limit=0.01)\n{1}: normal      (limit=0.0010)\n{2}: strict      (limit=1.0E-4)\n{3}: very strict (limit=1.0E-5)\n(default: 1);-l, --calcforleconformer=[if2D|never|always] (default: if2D)\
	$cxcalc distance -a 2-4 test.mol

angle=$chemaxon.marvin.calculations.GeometryPlugin\
	$GeometryPlugin.jar\
	$Geometry\
	$t=type:angle;a=atoms;p=precision:2;l=calcforleconformer:if2D;o=optimization:1\
	$ANGLE\
	$Angle of three atoms.\
	$-a, --atoms=[<atom1>-<atom2>-<atom3>] (1-based) atom indexes\nof the atom pair;-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-o, --optimization=[0|1|2|3] conformer generation optimiztaion limit for\ndifferent enviroments\n{0}: very loose  (limit=0.01)\n{1}: normal      (limit=0.0010)\n{2}: strict      (limit=1.0E-4)\n{3}: very strict (limit=1.0E-5)\n(default: 1);-l, --calcforleconformer=[if2D|never|always] (default: if2D)\
	$cxcalc angle -a 2-4-6 test.mol

dihedral=$chemaxon.marvin.calculations.GeometryPlugin\
	$GeometryPlugin.jar\
	$Geometry\
	$t=type:dihedral;a=atoms;p=precision:2;l=calcforleconformer:if2D;o=optimization:1\
	$DIHEDRAL\
	$Dihedral of four atoms.\
	$-a, --atoms=[<atom1>-<atom2>-<atom3>-<atom4>] (1-based) atom\nindexes of the atom pair;-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-o, --optimization=[0|1|2|3] conformer generation optimiztaion limit for\ndifferent enviroments\n{0}: very loose  (limit=0.01)\n{1}: normal      (limit=0.0010)\n{2}: strict      (limit=1.0E-4)\n{3}: very strict (limit=1.0E-5)\n(default: 1);-l, --calcforleconformer=[if2D|never|always] (default: if2D)\
	$cxcalc dihedral -a 1-2-4-6 test.mol

# alias for sterichindrance
hindrance=$chemaxon.marvin.calculations.GeometryPlugin\
	$GeometryPlugin.jar\
	$Geometry\
	$t=type:hindrance;p=precision:2;l=calcforleconformer:if2D;o=optimization:1\
	$HINDRANCE\
	$Steric hindrance.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-o, --optimization=[0|1|2|3] conformer generation optimiztaion limit for\ndifferent enviroments\n{0}: very loose  (limit=0.01)\n{1}: normal      (limit=0.0010)\n{2}: strict      (limit=1.0E-4)\n{3}: very strict (limit=1.0E-5)\n(default: 1);-l, --calcforleconformer=[if2D|never|always] (default: if2D)\
	$cxcalc hindrance test.mol

sterichindrance=$chemaxon.marvin.calculations.GeometryPlugin\
	$GeometryPlugin.jar\
	$Geometry\
	$t=type:hindrance;p=precision:2;l=calcforleconformer:if2D;o=optimization:1\
	$STERIC_HINDRANCE\
	$Steric hindrance.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-o, --optimization=[0|1|2|3] conformer generation optimiztaion limit for\ndifferent enviroments\n{0}: very loose  (limit=0.01)\n{1}: normal      (limit=0.0010)\n{2}: strict      (limit=1.0E-4)\n{3}: very strict (limit=1.0E-5)\n(default: 1);-l, --calcforleconformer=[if2D|never|always] (default: if2D)\
	$cxcalc sterichindrance test.mol

# alias for psa
polarsurfacearea=$chemaxon.marvin.calculations.TPSAPlugin\
	$TPSAPlugin.jar\
	$Geometry\
	$p=precision:2;H=pH;S=excludesulfur:true;P=excludephosphorus:true\
	$PSA\
	$Topological Polar Surface Area calculation (2D).\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule);-S, --excludesulfur=[true|false] exclude sulfur atom from calculation\n(default: true);-P, --excludephosphorus=[true|false] exclude phosphorus atom from calculation\n(default: true)\
	$cxcalc -S -t myPSA polarsurfacearea test.mol

psa=$chemaxon.marvin.calculations.TPSAPlugin\
	$TPSAPlugin.jar\
	$Geometry\
	$p=precision:2;H=pH;S=excludesulfur:true;P=excludephosphorus:true\
	$PSA\
	$Topological Polar Surface Area calculation (2D).\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule);-S, --excludesulfur=[true|false] exclude sulfur atom from calculation\n(default: true);-P, --excludephosphorus=[true|false] exclude phosphorus atom from calculation\n(default: true)\
	$cxcalc -S -t myPSA psa test.mol

# alias for msa
molecularsurfacearea=$chemaxon.marvin.calculations.MSAPlugin\
	$MSAPlugin.jar\
	$Geometry\
	$p=precision:2;H=pH;t=type:vanderwaals;i=increments:false\
	$MSA\
	$Molecular Surface Area calculation (3D).\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule);-t, --type=[vanderwaals|ASA|ASA+|ASA-|ASA_H|ASA_P]\n(default: vanderwaals);-i, --increments=[true|false] show incremental surface area on atoms\n(default: false)\
	$cxcalc molecularsurfacearea -t ASA+ -i true -H 7.4 test.mol

msa=$chemaxon.marvin.calculations.MSAPlugin\
	$MSAPlugin.jar\
	$Geometry\
	$p=precision:2;H=pH;t=type:vanderwaals;i=increments:false\
	$MSA\
	$Molecular Surface Area calculation (3D).\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule);-t, --type=[vanderwaals|ASA|ASA+|ASA-|ASA_H|ASA_P]\n(default: vanderwaals);-i, --increments=[true|false] show incremental surface area on atoms\n(default: false)\
	$cxcalc msa -t ASA+ -i true -H 7.4 test.mol

vdwsa=$chemaxon.marvin.calculations.MSAPlugin\
	$MSAPlugin.jar\
	$Geometry\
	$p=precision:2;H=pH;t=type:vanderwaals;i=increments:false\
	$VDWSA\
	$Van der Waals Surface Area calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule);-i, --increments=[true|false] show incremental surface area on atoms\n(default: false)\
	$cxcalc vdwsa -H 7.4 test.mol

# alias for asa
wateraccessiblesurfacearea=$chemaxon.marvin.calculations.MSAPlugin\
	$MSAPlugin.jar\
	$Geometry\
	$p=precision:2;r=solventradius:1.4;H=pH;t=type:ASA,ASA+,ASA-,ASA_H,ASA_P;i=increments:false\
	$ASA\
	$Water Accessible Surface Area calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-r, --solventradius=<solvent radius: 0.0-5.0> (default: 1.4);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule);-i, --increments=[true|false] show incremental surface area on atoms\n(default: false)\
	$cxcalc wateraccessiblesurfacearea test.mol

asa=$chemaxon.marvin.calculations.MSAPlugin\
	$MSAPlugin.jar\
	$Geometry\
	$p=precision:2;r=solventradius:1.4;H=pH;t=type:ASA,ASA+,ASA-,ASA_H,ASA_P;i=increments:false\
	$ASA\
	$Water Accessible Surface Area calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-r, --solventradius=<solvent radius: 0.0-5.0> (default: 1.4);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule);-i, --increments=[true|false] show incremental surface area on atoms\n(default: false)\
	$cxcalc asa test.mol

# Name

name=$chemaxon.marvin.calculations.IUPACNamingPlugin\
	$IUPACNamingPlugin.jar\
	$Name\
	$t=type:preferred\
	$NAME\
	$Generates the IUPAC name of the molecule.\
	$-t, --type=[preferred|traditional] (default: preferred)\npreferred: Preferred IUPAC Name\ntraditional: traditional name\
	$cxcalc name test.sdf

# Markush Enumeration

markushenumerations=$chemaxon.marvin.calculations.MarkushEnumerationPlugin\
	$MarkushEnumerationPlugin.jar\
	$Markush Enumerations\
	$t=type:structures;m=max;v=valencecheck:false;a=atoms;s=alignscaffold:false;c=coloring:none;r=random:false;g=enumhomology:false;o=code:false;i=structureid;f=format;C=clean:0\
	$ENUMERATED STRUCTURES\
	$Markush enumerated structures.\
	$-m, --max=<count> max. number of structures to be generated\n(default: all);-v, --valencecheck=[true|false] valence filter is on if true\n(default: false);-a, --atoms=[atom1,atom2,atom3,...]\n(1-based) atom indexes of the atoms\nto be enumerated (default: all);-s, --alignscaffold=[true|false] align scaffold (default: false);-c, --coloring=[none|all|scaffold|rgroups] structure coloring\n(default: none);-r, --random=[true|false] random enumeration\n(default: false);-g, --enumhomology=[true|false] enumerate homology groups (default: false);-o, --code=[true|false] generate Markush code (default: false);-i, --structureid=[id or tag name]\nstructure ID or SDF/MRV tag name storing the ID\n(default: no structure ID);-f, --format=<output format> (default: concatenated smiles);-C, --clean=<dim[:opts]> clean dimension with options\n(default: no clean)\
	$cxcalc markushenumerations -f sdf -C 2:t3000 -a 2,3,5 test.mol

# alias for markushenumerations
enumerations=$chemaxon.marvin.calculations.MarkushEnumerationPlugin\
	$MarkushEnumerationPlugin.jar\
	$Markush Enumerations\
	$t=type:structures;m=max;v=valencecheck:false;a=atoms;s=alignscaffold:false;c=coloring:none;r=random:false;g=enumhomology:false;o=code:false;i=structureid;f=format;C=clean:0\
	$ENUMERATED STRUCTURES\
	$Markush enumerated structures.\
	$-m, --max=<count> max. number of structures to be generated\n(default: all);-v, --valencecheck=[true|false] valence filter is on if true\n(default: false);-a, --atoms=[atom1,atom2,atom3,...]\n(1-based) atom indexes of the atoms\nto be enumerated (default: all);-s, --alignscaffold=[true|false] align scaffold (default: false);-c, --coloring=[none|all|scaffold|rgroups] structure coloring\n(default: none);-r, --random=[true|false] random enumeration\n(default: false);-g, --enumhomology=[true|false] enumerate homology groups (default: false);-o, --code=[true|false] generate Markush code (default: false);-i, --structureid=[id or tag name]\nstructure ID or SDF/MRV tag name storing the ID\n(default: no structure ID);-f, --format=<output format> (default: concatenated smiles);-C, --clean=<dim[:opts]> clean dimension with options\n(default: no clean)\
	$cxcalc enumerations -f sdf -C 2:t3000 -a 2,3,5 test.mol

randommarkushenumerations=$chemaxon.marvin.calculations.MarkushEnumerationPlugin\
	$MarkushEnumerationPlugin.jar\
	$Markush Enumerations\
	$r=random:true;t=type:structures;m=max;v=valencecheck:false;a=atoms;s=alignscaffold:false;c=coloring:none;g=enumhomology:false;o=code:false;i=structureid;f=format;C=clean:0\
	$RANDOM ENUMERATED STRUCTURES\
	$Randomly constructed Markush enumerated structures.\
	$-m, --max=<count> max. number of structures to be generated\n(default: all);-v, --valencecheck=[true|false] valence filter is on if true\n(default: false);-a, --atoms=[atom1,atom2,atom3,...]\n(1-based) atom indexes of the atoms\nto be enumerated (default: all);-s, --alignscaffold=[true|false] align scaffold (default: false);-c, --coloring=[none|all|scaffold|rgroups] structure coloring\n(default: none);-g, --enumhomology=[true|false] enumerate homology groups (default: false);-o, --code=[true|false] generate Markush code (default: false);-i, --structureid=[id or tag name]\nstructure ID or SDF/MRV tag name storing the ID\n(default: no structure ID);-f, --format=<output format> (default: concatenated smiles);-C, --clean=<dim[:opts]> clean dimension with options\n(default: no clean)\
	$cxcalc randommarkushenumerations -f sdf -C 2:t5000 test.mol

markushenumerationcount=$chemaxon.marvin.calculations.MarkushEnumerationPlugin\
	$MarkushEnumerationPlugin.jar\
	$Markush Enumerations\
	$t=type:count;c=calctype:countonly;d=mode:large;m=magnitude:false;g=enumhomology:false;a=atoms\
	$ENUMERATED STRUCTURE COUNT\
	$Number of Markush enumerated structures.\
	$-a, --atoms=[atom1,atom2,atom3,...]\n(1-based) atom indexes of the atoms\nto be enumerated (default: all);-m, --magnitude=[true|false] display magnitude if >= 100 000\n(default: false);-g, --enumhomology=[true|false] enumerate homology groups (default: false)\
	$cxcalc markushenumerationcount -m true test.mol

# alias for markushenumerationcount
enumerationcount=$chemaxon.marvin.calculations.MarkushEnumerationPlugin\
	$MarkushEnumerationPlugin.jar\
	$Markush Enumerations\
	$t=type:count;c=calctype:countonly;d=mode:large;m=magnitude:false;g=enumhomology:false;a=atoms\
	$ENUMERATED STRUCTURE COUNT\
	$Number of Markush enumerated structures.\
	$-a, --atoms=[atom1,atom2,atom3,...]\n(1-based) atom indexes of the atoms\nto be enumerated (default: all);-m, --magnitude=[true|false] display magnitude if >= 100 000\n(default: false);-g, --enumhomology=[true|false] enumerate homology groups (default: false)\
	$cxcalc enumerationcount -m true test.mol

# Other

# alias for hbda
hbonddonoracceptor=$chemaxon.marvin.calculations.HBDAPlugin\
	$HBDAPlugin.jar\
	$>Other\
	$p=precision:2;t=type:acceptorcount,donorcount,accsitecount,donsitecount;e=excludesulfur:true;x=excludehalogens:true;l=lower:0;u=upper:14;s=step:1;H=pH\
	$HBDA\
	$Hydrogen bond acceptor-donor calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-t, --type=[acc|don|accsitecount|donsitecount|\nacceptorcount|donorcount|msacc|msdon]\n(default: acceptorcount,donorcount,accsitecount,\ndonsitecount)\nacc: acceptor multiplicity on atoms\ndon: donor multiplicity on atoms\naccsitecount: acceptor multiplicity in molecule\ndonsitecount: donor multiplicity in molecule\nacceptorcount: number of acceptor atoms in molecule\ndonorcount: number of donor atoms in molecule\nmsacc: average acceptor multiplicity\n    over microspecies by pH\nmsdon: average donor multiplicity\n    over microspecies by pH;-l, --lower=<pH lower limit> (default: 0);-u, --upper=<pH upper limit> (default: 14);-s, --step=<pH step size> (default: 1);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule));-e, --excludesulfur=[true|false] exclude sulfur atom from acceptors\n(default: true);-x, --excludehalogens=[true|false] exclude halogens from acceptors\n(default: true)\
	$cxcalc hbonddonoracceptor -t "msacc,msdon" -l 2 -u 12 -s 0.5 test.sdf

hbda=$chemaxon.marvin.calculations.HBDAPlugin\
	$HBDAPlugin.jar\
	$>Other\
	$p=precision:2;t=type:acceptorcount,donorcount,accsitecount,donsitecount;e=excludesulfur:true;x=excludehalogens:true;l=lower:0;u=upper:14;s=step:1;H=pH\
	$HBDA\
	$Hydrogen bond acceptor-donor calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-t, --type=[acc|don|accsitecount|donsitecount|\nacceptorcount|donorcount|msacc|msdon]\n(default: acceptorcount,donorcount,accsitecount,\ndonsitecount)\nacc: acceptor multiplicity on atoms\ndon: donor multiplicity on atoms\naccsitecount: acceptor multiplicity in molecule\ndonsitecount: donor multiplicity in molecule\nacceptorcount: number of acceptor atoms in molecule\ndonorcount: number of donor atoms in molecule\nmsacc: average acceptor multiplicity\n    over microspecies by pH\nmsdon: average donor multiplicity\n    over microspecies by pH;-l, --lower=<pH lower limit> (default: 0);-u, --upper=<pH upper limit> (default: 14);-s, --step=<pH step size> (default: 1);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule));-e, --excludesulfur=[true|false] exclude sulfur atom from acceptors\n(default: true);-x, --excludehalogens=[true|false] exclude halogens from acceptors\n(default: true)\
	$cxcalc hbda -t "msacc,msdon" -l 2 -u 12 -s 0.5 test.sdf

# alias for acceptor
acceptortable=$chemaxon.marvin.calculations.HBDAPlugin\
	$HBDAPlugin.jar\
	$>Other\
	$t=type:acceptorcount,accsitecount;H=pH;e=excludesulfur:true;x=excludehalogens:true\
	$ACCEPTOR\
	$Hydrogen bond acceptor calculation.\
	$-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule);-e, --excludesulfur=[true|false] exclude sulfur atom from acceptors\n(default: true);-x, --excludehalogens=[true|false] exclude halogens from acceptors\n(default: true)\
	$cxcalc acceptortable test.sdf

acceptor=$chemaxon.marvin.calculations.HBDAPlugin\
	$HBDAPlugin.jar\
	$>Other\
	$t=type:acceptorcount,accsitecount;H=pH;e=excludesulfur:true;x=excludehalogens:true\
	$ACCEPTOR\
	$Hydrogen bond acceptor calculation.\
	$-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule);-e, --excludesulfur=[true|false] exclude sulfur atom from acceptors\n(default: true);-x, --excludehalogens=[true|false] exclude halogens from acceptors\n(default: true)\
	$cxcalc acceptor test.sdf

# alias for donor
donortable=$chemaxon.marvin.calculations.HBDAPlugin\
	$HBDAPlugin.jar\
	$>Other\
	$t=type:donorcount,donsitecount;H=pH\
	$DONOR\
	$Hydrogen bond donor calculation.\
	$-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc donortable test.sdf

donor=$chemaxon.marvin.calculations.HBDAPlugin\
	$HBDAPlugin.jar\
	$>Other\
	$t=type:donorcount,donsitecount;H=pH\
	$DONOR\
	$Hydrogen bond donor calculation.\
	$-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc donor test.sdf

acceptormultiplicity=$chemaxon.marvin.calculations.HBDAPlugin\
	$HBDAPlugin.jar\
	$>Other\
	$t=type:acc;H=pH;e=excludesulfur:true;x=excludehalogens:true\
	$ACC\
	$Hydrogen bond acceptor multiplicity calculation on atoms.\
	$-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule);-e, --excludesulfur=[true|false] exclude sulfur atom from acceptors\n(default: true);-x, --excludehalogens=[true|false] exclude halogens from acceptors\n(default: true)\
	$cxcalc acceptormultiplicity test.sdf

# alias for acceptormultiplicity (compatibility)
acc=$chemaxon.marvin.calculations.HBDAPlugin\
	$HBDAPlugin.jar\
	$>Other\
	$t=type:acc;H=pH;e=excludesulfur:true;x=excludehalogens:true\
	$ACC\
	$Hydrogen bond acceptor multiplicity calculation on atoms.\
	$-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule;-e, --excludesulfur=[true|false] exclude sulfur atom from acceptors\n(default: true);-x, --excludehalogens=[true|false] exclude halogens from acceptors\n(default: true)\
	$cxcalc acc test.sdf

#(donor)
donormultiplicity=$chemaxon.marvin.calculations.HBDAPlugin\
	$HBDAPlugin.jar\
	$>Other\
	$t=type:don;H=pH\
	$DON\
	$Hydrogen bond donor multiplicity calculation on atoms.\
	$-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc don test.sdf

# alias for donormultiplicity (compatibility)
don=$chemaxon.marvin.calculations.HBDAPlugin\
	$HBDAPlugin.jar\
	$>Other\
	$t=type:don;H=pH\
	$DON\
	$Hydrogen bond donor multiplicity calculation on atoms.\
	$-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc don test.sdf

#alias for accsitecount
acceptorsitecount=$chemaxon.marvin.calculations.HBDAPlugin\
	$HBDAPlugin.jar\
	$>Other\
	$t=type:accsitecount;H=pH;e=excludesulfur:true;x=excludehalogens:true\
	$ACCEPTOR_SITE_COUNT\
	$Hydrogen bond acceptor multiplicity in molecule.\
	$-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule);-e, --excludesulfur=[true|false] exclude sulfur atom from acceptors\n(default: true);-x, --excludehalogens=[true|false] exclude halogens from acceptors\n(default: true)\
	$cxcalc acceptorsitecount test.sdf

accsitecount=$chemaxon.marvin.calculations.HBDAPlugin\
	$HBDAPlugin.jar\
	$>Other\
	$t=type:accsitecount;H=pH;e=excludesulfur:true;x=excludehalogens:true\
	$ACCEPTOR_SITE_COUNT\
	$Hydrogen bond acceptor multiplicity in molecule.\
	$-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule);-e, --excludesulfur=[true|false] exclude sulfur atom from acceptors\n(default: true);-x, --excludehalogens=[true|false] exclude halogens from acceptors\n(default: true)\
	$cxcalc accsitecount test.sdf

# alias for donsitecount
donorsitecount=$chemaxon.marvin.calculations.HBDAPlugin\
	$HBDAPlugin.jar\
	$>Other\
	$t=type:donsitecount;H=pH\
	$DONOR_SITE_COUNT\
	$Hydrogen bond donor multiplicity in molecule.\
	$-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc donorsitecount test.sdf

donsitecount=$chemaxon.marvin.calculations.HBDAPlugin\
	$HBDAPlugin.jar\
	$>Other\
	$t=type:donsitecount;H=pH\
	$DONOR_SITE_COUNT\
	$Hydrogen bond donor multiplicity in molecule.\
	$-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc donsitecount test.sdf

acceptorcount=$chemaxon.marvin.calculations.HBDAPlugin\
	$HBDAPlugin.jar\
	$>Other\
	$t=type:acceptorcount;H=pH;e=excludesulfur:true;x=excludehalogens:true\
	$ACCEPTOR_COUNT\
	$Hydrogen bond acceptor atom count in molecule.\
	$-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule);-e, --excludesulfur=[true|false] exclude sulfur atom from acceptors\n(default: true);-x, --excludehalogens=[true|false] exclude halogens from acceptors\n(default: true)\
	$cxcalc acceptorcount -H 7.4 test.sdf

donorcount=$chemaxon.marvin.calculations.HBDAPlugin\
	$HBDAPlugin.jar\
	$>Other\
	$t=type:donorcount;H=pH\
	$DONOR_COUNT\
	$Hydrogen bond donor atom count in molecule.\
	$-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc donorcount -H 7.4 test.sdf

#?
msacc=$chemaxon.marvin.calculations.HBDAPlugin\
	$HBDAPlugin.jar\
	$>Other\
	$t=type:msacc;p=precision:2;l=lower:0;u=upper:14;s=step:1;e=excludesulfur:true;x=excludehalogens:true\
	$MSACC\
	$Hydrogen bond acceptor average multiplicity over microspecies by pH.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-l, --lower=<pH lower limit> (default: 0);-u, --upper=<pH upper limit> (default: 14);-s, --step=<pH step size> (default: 1);-e, --excludesulfur=[true|false] exclude sulfur atom from acceptors\n(default: true);-x, --excludehalogens=[true|false] exclude halogens from acceptors\n(default: true)\
	$cxcalc msacc test.sdf

#?
msdon=$chemaxon.marvin.calculations.HBDAPlugin\
	$HBDAPlugin.jar\
	$>Other\
	$t=type:msdon;p=precision:2;l=lower:0;u=upper:14;s=step:1\
	$MSDON\
	$Hydrogen bond donor average multiplicity over microspecies by pH.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-l, --lower=<pH lower limit> (default: 0);-u, --upper=<pH upper limit> (default: 14);-s, --step=<pH step size> (default: 1)\
	$cxcalc msdon test.sdf

# alias for huckel
huckeltable=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:order,localizationenergy,pienergy,electrondensity,chargedensity;H=pH\
	$HUCKEL\
	$Huckel analysis parameters.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-t, --type=[order|order:e|order:n|\nlocalizationenergy|\nlocalizationenergy:e|localizationenergy:n|\npienergy|electrondensity|chargedensity]\n(default: order,localizationenergy,\npienergy,electrondensity,chargedensity);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc -S -o result.sdf huckeltable -H 7.4 -p 3 test.mol

hmohuckeltable=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:hmoorder,hmolocalizationenergy,hmopienergy,hmoelectrondensity,hmochargedensity;H=pH\
	$HMO_HUCKEL\
	$HMO Huckel analysis parameters.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-t, --type=[hmoorder|hmoorder:e|hmoorder:n|\nhmolocalizationenergy|\nhmolocalizationenergy:e|hmolocalizationenergy:n|\nhmopienergy|hmoelectrondensity|hmochargedensity]\n(default: hmoorder,hmolocalizationenergy,\nhmopienergy,hmoelectrondensity,hmochargedensity);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc -S -o result.sdf hmohuckeltable -H 7.4 -p 3 test.mol

huckel=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:order,localizationenergy,pienergy,electrondensity,chargedensity;H=pH\
	$HUCKEL\
	$Huckel analysis parameters.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-t, --type=[order|order:e|order:n|\nlocalizationenergy|\nlocalizationenergy:e|localizationenergy:n|\npienergy|electrondensity|chargedensity]\n(default: order,localizationenergy,\npienergy,electrondensity,chargedensity);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc -S -o result.sdf huckel -H 7.4 -p 3 test.mol

hmohuckel=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:hmoorder,hmolocalizationenergy,hmopienergy,hmoelectrondensity,hmochargedensity;H=pH\
	$HMO_HUCKEL\
	$HMO Huckel analysis parameters.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-t, --type=[hmoorder|hmoorder:e|hmoorder:n|\nhmolocalizationenergy|\nhmolocalizationenergy:e|hmolocalizationenergy:n|\nhmopienergy|hmoelectrondensity|hmochargedensity]\n(default: hmoorder,hmolocalizationenergy,\nhmopienergy,hmoelectrondensity,hmochargedensity);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc -S -o result.sdf hmohuckel -H 7.4 -p 3 test.mol

#deprecated
electrophilicityorder=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$t=type:order;s=subtype:e;H=pH\
	$ELECTROPHILICITY_ORDER\
	$Order in E(+) attack. Deprecated.\
	$-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc electrophilicityorder -H 7.4 test.mol
	
hmoelectrophilicityorder=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$t=type:hmoorder;s=subtype:e;H=pH\
	$HMO_ELECTROPHILICITY_ORDER\
	$Order in E(+) attack (HMO).\
	$-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc hmoelectrophilicityorder -H 7.4 test.mol

#deprecared
aromaticelectrophilicityorder=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$t=type:order;s=subtype:e;H=pH\
	$AROMATIC_ELECTROPHILICITY_ORDER\
	$Order in E(+) attack. Deprecated.\
	$-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc aromaticelectrophilicityorder -H 7.4 test.mol

#deprecated
nucleophilicityorder=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$t=type:order;s=subtype:n;H=pH\
	$NUCLEOPHILICITY_ORDER\
	$Order in Nu(-) attack. Deprecated.\
	$-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc nucleophilicityorder -H 7.4 test.mol

hmonucleophilicityorder=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$t=type:hmoorder;s=subtype:n;H=pH\
	$HMO_NUCLEOPHILICITY_ORDER\
	$Order in Nu(-) attack (HMO).\
	$-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc hmonucleophilicityorder -H 7.4 test.mol

#deprecated
aromaticnucleophilicityorder=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$t=type:order;s=subtype:n;H=pH\
	$AROMATIC_NUCLEOPHILICITY_ORDER\
	$Order in Nu(-) attack. Deprecated.\
	$-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc aromaticnucleophilicityorder -H 7.4 test.mol

localizationenergy=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:localizationenergy;s=subtype:e,n;H=pH\
	$LOCALIZATION_ENERGY\
	$Localization energy L(+)/L(-).\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-s, --subtype=[e|n|en] e: electrophilic, n: nucleophilic, en: both (default: en);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc localizationenergy test.mol

hmolocalizationenergy=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:hmolocalizationenergy;s=subtype:e,n;H=pH\
	$HMO_LOCALIZATION_ENERGY\
	$HMO Localization energy L(+)/L(-).\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-s, --subtype=[e|n|en] e: electrophilic, n: nucleophilic, en: both (default: en);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc hmolocalizationenergy test.mol

electrophiliclocalizationenergy=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:localizationenergy;s=subtype:e;H=pH\
	$ELECTROPHILIC_LOCALIZATION_ENERGY\
	$Electrophilic localization energy L(+).\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc electrophiliclocalizationenergy test.mol

hmoelectrophiliclocalizationenergy=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:hmolocalizationenergy;s=subtype:e;H=pH\
	$HMO_ELECTROPHILIC_LOCALIZATION_ENERGY\
	$HMO Electrophilic localization energy L(+).\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc hmoelectrophiliclocalizationenergy test.mol

nucleophiliclocalizationenergy=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:localizationenergy;s=subtype:n;H=pH\
	$NUCLEOPHILIC_LOCALIZATION_ENERGY\
	$Nucleophilic localization energy L(-).\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc nucleophiliclocalizationenergy test.mol

hmonucleophiliclocalizationenergy=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:hmolocalizationenergy;s=subtype:n;H=pH\
	$HMO_NUCLEOPHILIC_LOCALIZATION_ENERGY\
	$HMO Nucleophilic localization energy L(-).\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc hmonucleophiliclocalizationenergy test.mol

#deprecated
pienergy=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:pienergy;H=pH\
	$PI_ENERGY\
	$Pi energy. Deprecated.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc pienergy test.mol

hmopienergy=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:hmopienergy;H=pH\
	$HMO_PI_ENERGY\
	$HMO Pi energy.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc hmopienergy test.mol

#deprecated
huckeleigenvalue=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:eigenvalue;H=pH\
	$HUCKEL_EIGENVALUE\
	$Huckel eigenvalue. Deprecated.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc huckeleigenvalue test.mol

hmohuckeleigenvalue=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:hmoeigenvalue;H=pH\
	$HMO_HUCKEL_EIGENVALUE\
	$HMO Huckel eigenvalue.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc hmohuckeleigenvalue test.mol

#deprecated	
huckeleigenvector=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:eigenvector;H=pH\
	$HUCKEL_EIGENVECTOR\
	$Huckel eigenvector. Deprecated.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc huckeleigenvector test.mol

hmohuckeleigenvector=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:hmoeigenvector;H=pH\
	$HMO_HUCKEL_EIGENVECTOR\
	$HMO Huckel eigenvector.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc hmohuckeleigenvector test.mol

#deprecated
huckelorbitals=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:orbitals;H=pH\
	$HUCKEL_ORBITAL_COEFFICIENTS\
	$Huckel orbital coefficients. Deprecated.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc huckelorbitals test.mol

hmohuckelorbitals=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:hmoorbitals;H=pH\
	$HUCKEL_ORBITAL_COEFFICIENTS\
	$HMO Huckel orbital coefficients.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc hmohuckelorbitals test.mol

#deprecated, use electrondensity
pichargedensity=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:pichargedensity;H=pH\
	$PICHARGE_DENSITY\
	$Pi charge density. Deprecated, use "electrondensity" calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc pichargedensity -p 4 -H 6.5 test.mol

electrondensity=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:electrondensity;H=pH\
	$ELECTRON_DENSITY\
	$Electron density.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc electrondensity -p 4 -H 6.5 test.mol

hmoelectrondensity=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:hmoelectrondensity;H=pH\
	$HMO_ELECTRON_DENSITY\
	$HMO Electron density.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc hmoelectrondensity -p 4 -H 6.5 test.mol

#deprecated, use chargedensity
totalchargedensity=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:totalchargedensity;H=pH\
	$TOTALCHARGE_DENSITY\
	$Total charge density. Deprecated, use "chargedensity" calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc totalchargedensity -p 4 -H 6.5 test.mol

chargedensity=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:chargedensity;H=pH\
	$CHARGE_DENSITY\
	$Charge density.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc chargedensity -p 4 -H 6.5 test.mol

hmochargedensity=$chemaxon.marvin.calculations.HuckelAnalysisPlugin\
	$HuckelAnalysisPlugin.jar\
	$>Other\
	$p=precision:2;t=type:hmochargedensity;H=pH\
	$HMO_CHARGE_DENSITY\
	$HMO Charge density.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-H, --pH=<pH value> takes major microspecies at this pH\n(default: no pH, takes the input molecule)\
	$cxcalc hmochargedensity -p 4 -H 6.5 test.mol

refractivity=$chemaxon.marvin.calculations.RefractivityPlugin\
	$RefractivityPlugin.jar\
	$>Other\
	$p=precision:2;t=type:refractivity;i=inch:false\
	$REFRACTIVITY\
	$Refractivity calculation.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-t, --type=[increments|inch|refractivity] (default: refractivity);-i, --inch=[true|false] refractivity on H atoms shown in brackets\n(for incremental refractivity only) (default: false)\
	$cxcalc refractivity -p 3 -t refractivity,increments test.mol

resonants=$chemaxon.marvin.calculations.ResonancePlugin\
	$MultiformPlugin.jar\
	$>Other\
	$c=canonical:false;r=mcontrib:true;m=max:200;f=format;s=symfilter:true\
	$RESONANTS\
	$Resonant structures.\
	$-c, --canonical=[true|false]\ntrue: take canonical resonant form (default: false);-r, --mcontrib=[true|false]\ntrue: take major contributors\n(default: true);-m, --max=<count> max. number of structures to be generated\n(default: 200);-f, --format=<output format> (default: fused smiles,\nmultiple molecule output if specified);-s, --symfilter=[true|false]\ntrue: filter out symmetrical structures\nfalse: allow duplicates\n(default: true)\
	$cxcalc resonants -f sdf test.mol

canonicalresonant=$chemaxon.marvin.calculations.ResonancePlugin\
	$MultiformPlugin.jar\
	$>Other\
	$f=format;c=canonical:true\
	$CANONICAL_RESONANT\
	$Canonical resonant structure.\
	$-f, --format=<output format> (default: smiles)\
	$cxcalc canonicalResonant -f sdf test.mol

resonantcount=$chemaxon.marvin.calculations.ResonancePlugin\
	$MultiformPlugin.jar\
	$>Other\
	$r=mcontrib:true;m=max:200;t=type:count;s=symfilter:true\
	$RESONANT_COUNT\
	$The number of resonant structures.\
	$-r, --mcontrib=[true|false]\ntrue: take major contributors\n(default: true);-m, --max=<count> max. number of structures to be generated\n(default: 200);-s, --symfilter=[true|false]\ntrue: filter out symmetrical structures\nfalse: allow duplicates\n(default: true)\
	$cxcalc resonantCount test.mol

frameworks=$chemaxon.marvin.calculations.StructuralFrameworksPlugin\
	$StructuralFrameworksPlugin.jar\
	$>Other\
    $t=type:bmf;\
    i=lfin:false;\
    p=prunein:false;\
    r=pruneout:false;\
    o=lfout:false;\
    q=oeqcheck:false;\
    s=keepsingleatom:true;\
    f=format:sdf;\
    y=hydrogenize:false;\
    d=dehydrogenize:false\
	$FRAMEWORKS\
	$Calculates different structural frameworks (Bemis-Murcko, MCS, etc) of\n\
    the molecule\
	$-t, --type=[bmf|bmfl|mcs|largestring|allringsystems|\n\
    largestringsystem|sssr|cssr|keep] Framework type\n\
    to calculate;\
    -i, --lfin=[true|false] Process only the largest fragment\n\of input structure (default: false);\
    -p, --prunein=[true|false] Prune input: generalize input atom\n\and bond types (default: false);\
    -h, --hydrogenize=[true|fase] Add explicit hydrogens to the input\n\
    structure (default: false);\
    -d, --dehydrogenize=[true|false] Remove explicit hydrogens from the\n\
    input structure (default: false);\
    -r, --pruneout=[true|false] Prune results: generalize result\n\atom and bond types (default: false);\
    -o, --lfout=[true|false] Return only the largest fragment of\nthe result (default: false);\
    -q, --oeqcheck=[true|false] Remove topologically equivalent\n\
    output fragments (default: false);\
    -s, --keepsingleatom=[true|false] Return a single atom for non-empty\n\
    acyclic input structures (default: true);\
    -f, --format=<output format> (default: sdf)\
    $cxcalc frameworks -t bmf -s true test.mol

# Predictor

predictor=$chemaxon.marvin.calculations.PredictorPlugin\
	$PredictorPlugin.jar\
	$Predictor\
	$p=precision:2;I=trainingid;E=error:false\
	$PREDICTOR\
	$Predicts molecular properties.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-I, --trainingid=<training id> sets the training\
	$cxcalc predictor --trainingid pampa test.mol

predict=$chemaxon.marvin.calculations.PredictorPlugin\
	$PredictorPlugin.jar\
	$Predictor\
	$p=precision:2;I=trainingid;E=error:false\
	$PREDICTOR\
	$Predicts molecular properties.\
	$-p, --precision=<floating point precision as number of\nfractional digits: 0-8 or inf> (default: 2);-I, --trainingid=<training id> sets the training\
	$cxcalc predict --trainingid pampa test.mol

