#!/usr/bin/env ruby

# Test script for Morgan fingerprint generation
require_relative 'config/environment'

puts "Testing Morgan fingerprint generation..."

# Test the helper method from the rake task
def generate_morgan_fingerprint(structure)
  script = <<~PYTHON
    import sys
    import json
    from rdkit import Chem
    from rdkit.Chem import rdMolDescriptors
    
    def parse_structure(structure):
        try:
            # Try parsing as SMILES first
            mol = Chem.MolFromSmiles(structure)
            if mol is None:
                # Try parsing as MOL block
                mol = Chem.MolFromMolBlock(structure)
            if mol is None:
                # Try parsing as InChI
                mol = Chem.MolFromInchi(structure)
            return mol
        except Exception as e:
            print(f"Error parsing structure: {e}", file=sys.stderr)
            return None
    
    # Read structure from input file
    with open(sys.argv[1], 'r') as f:
        structure = f.read().strip()
    
    # Parse the structure
    mol = parse_structure(structure)
    
    if mol is not None:
        try:
            # Generate Morgan fingerprint as bit vector
            fp = rdMolDescriptors.GetMorganFingerprintAsBitVect(mol, 2, nBits=2048)
            
            # Convert to hex string for storage
            fp_hex = fp.ToBitString()
            
            with open(sys.argv[2], 'w') as f:
                f.write(fp_hex)
        except Exception as e:
            print(f"Error generating fingerprint: {e}", file=sys.stderr)
            with open(sys.argv[2], 'w') as f:
                f.write("")
    else:
        with open(sys.argv[2], 'w') as f:
            f.write("")
  PYTHON
  
  begin
    result = Jchem.run_rdkit(script, structure)
    return result.strip.empty? ? nil : result.strip
  rescue => e
    Rails.logger.error "Morgan fingerprint generation error: #{e.message}"
    return nil
  end
end

# Test with simple SMILES
puts "\n1. Testing with simple SMILES (ethanol):"
test_smiles = "CCO"
fingerprint = generate_morgan_fingerprint(test_smiles)
if fingerprint
  puts "  ✓ Generated fingerprint: #{fingerprint[0..50]}... (length: #{fingerprint.length})"
else
  puts "  ✗ Failed to generate fingerprint"
end

# Test with complex structure from user's example
puts "\n2. Testing with complex structure:"
complex_structure = "[H]C1=C([H])C([H])=C2C(=C1[H])N1C(=O)C([H])([H])[C@]3([H])OC([H])([H])C([H])=C4C([H])([H])N5C([H])([H])C([H])([H])[C@]22[C@]5([H])C([H])([H])[C@]4([H])[C@]3([H])[C@]12[H]"
fingerprint = generate_morgan_fingerprint(complex_structure)
if fingerprint
  puts "  ✓ Generated fingerprint: #{fingerprint[0..50]}... (length: #{fingerprint.length})"
else
  puts "  ✗ Failed to generate fingerprint"
end

# Test with a structure from the database
puts "\n3. Testing with database structure:"
begin
  structure = Structure.first
  if structure
    puts "  Testing structure ID #{structure.id} (#{structure.inchikey})"
    fingerprint = generate_morgan_fingerprint(structure.original_structure)
    if fingerprint
      puts "  ✓ Generated fingerprint: #{fingerprint[0..50]}... (length: #{fingerprint.length})"
      puts "  Current stored fingerprint: #{structure.morgan_fingerprint ? structure.morgan_fingerprint[0..50] + '...' : 'nil'}"
    else
      puts "  ✗ Failed to generate fingerprint"
    end
  else
    puts "  No structures found in database"
  end
rescue => e
  puts "  Error accessing database: #{e.message}"
end

# Check RDKit availability
puts "\n4. Checking RDKit availability:"
begin
  script = "from rdkit import Chem; print('RDKit available')"
  result = Jchem.run_rdkit(script, "")
  puts "  ✓ RDKit is available: #{result.strip}"
rescue => e
  puts "  ✗ RDKit not available: #{e.message}"
end

puts "\nTest complete!"
