#!/usr/bin/env ruby

# Debug script for the specific search issue
puts "Loading Rails environment..."

begin
  require_relative 'config/environment'
  puts "Rails environment loaded successfully"
rescue => e
  puts "Error loading Rails environment: #{e.message}"
  exit 1
end

# Test structure from the user's request
test_structure = "[H]C1=C([H])C([H])=C2C(=C1[H])N1C(=O)C([H])([H])[C@]3([H])OC([H])([H])C([H])=C4C([H])([H])N5C([H])([H])C([H])([H])[C@]22[C@]5([H])C([H])([H])[C@]4([H])[C@]3([H])[C@]12[H]"

# Test parameters from the user's request
test_params = {
  'search_type' => 'similarity',
  'similarity' => '0.7',
  'resource' => 'natural_products',
  'max_results' => '100'
}

puts "\n=== Debug Information ==="
puts "Test structure: #{test_structure[0..50]}..."
puts "Search type: #{test_params['search_type']}"
puts "Similarity threshold: #{test_params['similarity']}"
puts "Resource: #{test_params['resource']}"

# Check if the source exists
source = Source.find_by(name: test_params['resource'])
puts "Source found: #{source ? "Yes (ID: #{source.id})" : "No"}"

if source.nil?
  puts "Available sources:"
  Source.all.each { |s| puts "  - #{s.name} (ID: #{s.id})" }
end

# Check structure count
total_structures = Structure.count
puts "Total structures in database: #{total_structures}"

if source
  source_structures = Structure.joins(:database_registrations).where(database_registrations: { source_id: source.id }).count
  puts "Structures for '#{source.name}': #{source_structures}"
end

# Test the search
puts "\n=== Testing ChemSearch ==="
begin
  result = ChemSearch.search(test_structure, source&.id, test_params)
  puts "Search completed successfully!"
  puts "Total hits: #{result.total_count}"
  puts "Returned hits: #{result.hits.length}"
  
  if result.hits.any?
    puts "\nFirst hit details:"
    hit = result.hits.first
    puts "  cd_id: #{hit[:cd_id]}"
    puts "  cd_formula: #{hit[:cd_formula]}"
    puts "  cd_molweight: #{hit[:cd_molweight]}"
    puts "  similarity: #{hit[:similarity]}"
  end
rescue => e
  puts "Error during search: #{e.message}"
  puts "Backtrace:"
  puts e.backtrace.first(10).map { |line| "  #{line}" }.join("\n")
end

# Test RDKit directly
puts "\n=== Testing RDKit Directly ==="
begin
  script = <<~PYTHON
    import sys
    import json
    from rdkit import Chem
    from rdkit.Chem import rdMolDescriptors
    
    structure = "#{test_structure}"
    print(f"Testing structure: {structure[:50]}...")
    
    # Try to parse the structure
    mol = Chem.MolFromSmiles(structure)
    if mol is None:
        print("Failed to parse as SMILES")
        mol = Chem.MolFromMolBlock(structure)
        if mol is None:
            print("Failed to parse as MOL block")
        else:
            print("Successfully parsed as MOL block")
    else:
        print("Successfully parsed as SMILES")
        canonical = Chem.MolToSmiles(mol, canonical=True)
        print(f"Canonical SMILES: {canonical}")
    
    if mol is not None:
        fp = rdMolDescriptors.GetMorganFingerprintAsBitVect(mol, 2, nBits=2048)
        print(f"Generated fingerprint with {fp.GetNumBits()} bits")
    else:
        print("Could not generate fingerprint - molecule parsing failed")
  PYTHON
  
  result = Jchem.run_rdkit(script, "")
  puts "RDKit test result:"
  puts result
rescue => e
  puts "RDKit test error: #{e.message}"
end

puts "\n=== Debug Complete ==="
