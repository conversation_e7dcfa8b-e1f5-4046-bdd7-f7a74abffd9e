#!/usr/bin/env ruby

# Test optimized similarity search using precalculated fingerprints
require_relative 'config/environment'

puts "Testing optimized similarity search with precalculated fingerprints..."

# Find a structure with a fingerprint to use as query
test_structure = Structure.where.not(morgan_fingerprint: [nil, ""]).first

if test_structure.nil?
  puts "❌ No structures with precalculated fingerprints found!"
  puts "Run: bundle exec rake structures:create_fingerprints"
  exit 1
end

puts "\n📋 Test Setup:"
puts "  Query structure ID: #{test_structure.id}"
puts "  Query InChI Key: #{test_structure.inchikey}"
puts "  Query fingerprint: #{test_structure.morgan_fingerprint[0..50]}... (#{test_structure.morgan_fingerprint.length} bits)"

# Count structures with fingerprints
fingerprint_count = Structure.where.not(morgan_fingerprint: [nil, ""]).count
total_count = Structure.count

puts "  Structures with fingerprints: #{fingerprint_count}/#{total_count} (#{(fingerprint_count.to_f/total_count*100).round(2)}%)"

# Test similarity search
puts "\n🔍 Testing Similarity Search:"
puts "  Using query structure's original_structure as input..."

begin
  # Use the structure's original_structure as the query
  query_structure = test_structure.original_structure
  
  # Test with different similarity thresholds
  [0.9, 0.7, 0.5].each do |threshold|
    puts "\n  Threshold: #{threshold}"
    
    start_time = Time.now
    results = ChemSearch.search(
      structure: query_structure,
      search_type: 'similarity',
      similarity_threshold: threshold,
      max_results: 10,
      source: nil
    )
    end_time = Time.now
    
    puts "    Results: #{results.length} structures found"
    puts "    Time: #{(end_time - start_time).round(3)}s"
    
    if results.any?
      puts "    Top results:"
      results.first(3).each_with_index do |result, i|
        puts "      #{i+1}. ID #{result[:cd_id]} - Similarity: #{result[:similarity]&.round(4)} - #{result[:cd_formula]}"
      end
    end
  end

rescue => e
  puts "❌ Error during similarity search: #{e.message}"
  puts e.backtrace.first(5)
end

# Test performance comparison info
puts "\n📊 Performance Notes:"
puts "  - Using precalculated fingerprints should be much faster"
puts "  - Only structures with fingerprints are searched for optimal performance"
puts "  - Query structure fingerprint is calculated on-the-fly (only once)"

# Show some statistics about fingerprint coverage
puts "\n📈 Fingerprint Coverage by Recent Updates:"
recent_with_fp = Structure.where.not(morgan_fingerprint: [nil, ""]).order(updated_at: :desc).limit(5)
recent_with_fp.each do |s|
  puts "  Structure #{s.id}: #{s.morgan_fingerprint[0..30]}... (updated: #{s.updated_at.strftime('%Y-%m-%d %H:%M')})"
end

puts "\n✅ Test complete!"
