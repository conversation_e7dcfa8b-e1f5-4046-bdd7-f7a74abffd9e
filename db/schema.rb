# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# Note that this schema.rb definition is the authoritative source for your
# database schema. If you need to create the application database on another
# system, you should be using db:schema:load, not running all the migrations
# from scratch. The latter is a flawed and unsustainable approach (the more migrations
# you'll amass, the slower it'll run and the greater likelihood for issues).
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 2022_05_04_200614) do

  create_table "active_admin_comments", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.string "namespace"
    t.text "body"
    t.string "resource_id", null: false
    t.string "resource_type", null: false
    t.integer "author_id"
    t.string "author_type"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["author_type", "author_id"], name: "index_active_admin_comments_on_author_type_and_author_id"
    t.index ["namespace"], name: "index_active_admin_comments_on_namespace"
    t.index ["resource_type", "resource_id"], name: "index_active_admin_comments_on_resource_type_and_resource_id"
  end

  create_table "adducts", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.string "adduct", default: "", null: false
    t.string "adduct_type", limit: 1
    t.decimal "adduct_mass", precision: 14, scale: 6, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "structure_id"
    t.index ["adduct_mass"], name: "index_adducts_on_adduct_mass"
    t.index ["adduct_type", "adduct", "adduct_mass"], name: "index_adducts_on_adduct_type_and_adduct_and_adduct_mass"
    t.index ["adduct_type", "adduct_mass"], name: "index_adducts_on_adduct_type_and_mass"
    t.index ["structure_id", "adduct"], name: "index_adducts_on_structure_id_and_adduct", unique: true
    t.index ["structure_id", "adduct_type", "adduct", "adduct_mass"], name: "index_on_all", unique: true
  end

  create_table "admin_users", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email"], name: "index_admin_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_admin_users_on_reset_password_token", unique: true
  end

  create_table "atom_groups", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.integer "molecule_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["molecule_id"], name: "index_atom_groups_on_molecule_id"
  end

  create_table "atoms", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.integer "molecule_id"
    t.integer "atom_group_id"
    t.integer "atom_number"
    t.float "x_coordinate"
    t.float "y_coordinate"
    t.float "z_coordinate"
    t.string "symbol"
    t.integer "mass_difference"
    t.integer "charge"
    t.integer "stereo_parity"
    t.integer "hydrogen_count"
    t.integer "stereo_care_box"
    t.integer "valence"
    t.integer "ho_designator"
    t.integer "mapping_number"
    t.integer "inversion_retention_flag"
    t.integer "exact_change_flag"
    t.integer "isotope_number"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["molecule_id"], name: "index_atoms_on_molecule_id"
  end

  create_table "bmrb_records", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.string "inchikey"
    t.integer "pubchem_compound_id"
    t.string "bmrb_compound_name"
    t.string "bmrb_record_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["inchikey"], name: "index_bmrb_records_on_inchikey"
  end

  create_table "bonds", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.integer "molecule_id"
    t.integer "atom_one"
    t.integer "atom_two"
    t.string "bond_type"
    t.string "stereo"
    t.string "topology"
    t.string "reacting_center_status"
    t.text "details"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["molecule_id"], name: "index_bonds_on_molecule_id"
  end

  create_table "c_ms", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.text "notes"
    t.decimal "sample_concentration", precision: 15, scale: 10
    t.string "solvent"
    t.decimal "sample_mass", precision: 15, scale: 10
    t.text "sample_assessment"
    t.text "spectra_assessment"
    t.text "sample_source"
    t.date "collection_date"
    t.string "instrument_type"
    t.integer "peak_counter"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "chromatography_type"
    t.decimal "retention_index", precision: 15, scale: 5
    t.decimal "retention_time", precision: 15, scale: 5
    t.string "sample_concentration_units"
    t.string "sample_mass_units"
    t.string "ionization_mode"
    t.string "column_type"
    t.integer "base_peak"
    t.string "derivative_type"
    t.string "ri_type"
    t.string "derivative_formula"
    t.decimal "derivative_mw", precision: 15, scale: 10
    t.integer "structure_id"
    t.string "splash_key"
    t.boolean "predicted", default: false, null: false
    t.text "derivative_smiles"
    t.float "derivative_exact_mass"
    t.index ["predicted"], name: "index_c_ms_on_predicted"
    t.index ["splash_key"], name: "index_c_ms_on_splash_key"
    t.index ["structure_id"], name: "fk_rails_aea4a9ee3e"
  end

  create_table "c_ms_peak_assignments", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.string "fragment_type"
    t.text "smiles"
    t.integer "c_ms_peak_id"
    t.integer "structure_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "assignment_view_file_name"
    t.string "assignment_view_content_type"
    t.integer "assignment_view_file_size"
    t.datetime "assignment_view_updated_at"
    t.string "formula"
    t.index ["c_ms_peak_id"], name: "index_c_ms_peak_assignments_on_c_ms_peak_id"
    t.index ["structure_id"], name: "index_c_ms_peak_assignments_on_structure_id"
  end

  create_table "c_ms_peaks", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.integer "c_ms_id"
    t.decimal "mass_charge", precision: 15, scale: 10
    t.decimal "intensity", precision: 15, scale: 10
    t.index ["c_ms_id"], name: "fk_rails_43644250c2"
  end

  create_table "curations", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.integer "structure_id", null: false
    t.text "result", limit: 4294967295, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["structure_id"], name: "index_curations_on_structure_id", unique: true
  end

  create_table "database_registrations", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.integer "structure_id", null: false
    t.integer "source_id", null: false
    t.string "database_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "exported", default: true, null: false
    t.index ["database_id", "exported"], name: "index_database_registrations_on_database_id_and_exported"
    t.index ["database_id"], name: "index_database_registrations_on_database_id", unique: true
    t.index ["exported"], name: "index_database_registrations_on_exported"
    t.index ["source_id"], name: "index_database_registrations_on_source_id"
    t.index ["structure_id"], name: "index_database_registrations_on_structure_id"
  end

  create_table "documents", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.integer "spectra_id"
    t.string "spectra_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "file_file_name"
    t.string "file_content_type"
    t.integer "file_file_size"
    t.datetime "file_updated_at"
    t.text "description"
    t.index ["spectra_type", "spectra_id"], name: "index_documents_on_spectra_type_and_spectra_id"
  end

  create_table "ei_ms", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.text "notes"
    t.decimal "sample_concentration", precision: 15, scale: 10
    t.string "sample_concentration_units"
    t.string "solvent"
    t.decimal "sample_mass", precision: 15, scale: 10
    t.string "sample_mass_units"
    t.text "sample_assessment"
    t.text "spectra_assessment"
    t.text "sample_source"
    t.date "collection_date"
    t.string "instrument_type"
    t.integer "peak_counter"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "structure_id"
    t.string "splash_key"
    t.boolean "predicted", default: false, null: false
    t.index ["predicted"], name: "index_ei_ms_on_predicted"
    t.index ["splash_key"], name: "index_ei_ms_on_splash_key"
    t.index ["structure_id"], name: "fk_rails_366d99f5fc"
  end

  create_table "ei_ms_peaks", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.integer "ei_ms_id"
    t.decimal "mass_charge", precision: 15, scale: 10
    t.decimal "intensity", precision: 15, scale: 10
    t.index ["ei_ms_id"], name: "index_ei_ms_peaks_on_ei_ms_id"
  end

  create_table "formats", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.integer "structure_id", null: false
    t.string "name", null: false
    t.text "value", limit: 16777215, null: false
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["structure_id", "name"], name: "index_formats_on_structure_id_and_name", unique: true
    t.index ["structure_id"], name: "index_formats_on_structure_id"
  end

  create_table "identifiers", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3", force: :cascade do |t|
    t.datetime "created_at"
    t.datetime "updated_at"
    t.integer "structure_id"
    t.string "kegg_id"
    t.string "chemspider_id"
    t.text "name"
    t.string "kegg_drug_id"
    t.string "chembl_id"
    t.string "drugbank_id"
    t.string "iuphar_id"
    t.string "chebi_id"
    t.string "emolecules_id"
    t.string "ibm_patent_id"
    t.string "patent_id"
    t.string "surechem_id"
    t.string "pubchem_thomson_id"
    t.string "pubchem_id"
    t.string "molport_id"
    t.string "nikkaji_id"
    t.string "ymdb_id"
    t.string "pdbe_id"
    t.string "fda_srs_id"
    t.string "hmdb_id"
    t.string "actor_id"
    t.string "bindingdb_id"
    t.string "zinc_id"
    t.string "mcule_id"
    t.string "recon_id"
    t.string "t3db_id"
    t.string "pharmkgb_id"
    t.string "nmrshiftdb_id"
    t.string "foodb_id"
    t.string "ecmdb_id"
    t.string "cas"
    t.string "selleck_id"
    t.string "lincs_id"
    t.string "pubchem_dotf_id"
    t.string "atlas_id"
    t.string "nih_id"
    t.string "ligand_expo_id"
    t.string "phenol_id"
    t.string "meta_cyc_id"
    t.string "wikipedia_id"
    t.string "knapsack_id"
    t.string "bigg_id"
    t.string "nugowiki_id"
    t.string "metagene_id"
    t.string "metlin_id"
    t.text "iupac_name"
    t.index ["structure_id"], name: "index_identifiers_on_structure_id"
  end

  create_table "mass_bank_records", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.string "accession"
    t.string "inchi_key"
    t.text "record"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["inchi_key"], name: "index_mass_bank_records_on_inchi_key"
  end

  create_table "ms_ms", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.text "notes"
    t.decimal "sample_concentration", precision: 15, scale: 10
    t.string "solvent"
    t.decimal "sample_mass", precision: 15, scale: 10
    t.text "sample_assessment"
    t.text "spectra_assessment"
    t.text "sample_source"
    t.date "collection_date"
    t.string "instrument_type"
    t.integer "peak_counter"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.decimal "mono_mass", precision: 15, scale: 10
    t.string "energy_field"
    t.string "collision_energy_level"
    t.integer "collision_energy_voltage"
    t.string "ionization_mode"
    t.string "sample_concentration_units"
    t.string "sample_mass_units"
    t.boolean "predicted", default: false, null: false
    t.integer "structure_id"
    t.string "splash_key"
    t.index ["predicted"], name: "index_ms_ms_on_predicted"
    t.index ["splash_key"], name: "index_ms_ms_on_splash_key"
    t.index ["structure_id", "predicted", "ionization_mode"], name: "index_ms_ms_on_structure_id_and_predicted_and_ionization_mode"
    t.index ["structure_id"], name: "fk_rails_2c331664d1"
  end

  create_table "ms_ms_peak_assignments", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.integer "ms_ms_peak_id"
    t.string "fragment_type"
    t.text "smiles"
    t.string "assignment_view_file_name"
    t.string "assignment_view_content_type"
    t.integer "assignment_view_file_size"
    t.datetime "assignment_view_updated_at"
    t.integer "structure_id"
    t.string "formula"
    t.index ["ms_ms_peak_id"], name: "index_ms_ms_peak_assignments_on_ms_ms_peak_id"
    t.index ["structure_id"], name: "index_ms_ms_peak_assignments_on_structure_id"
  end

  create_table "ms_ms_peaks", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.integer "ms_ms_id"
    t.decimal "mass_charge", precision: 15, scale: 10
    t.decimal "intensity", precision: 15, scale: 10
    t.integer "molecule_id"
    t.boolean "export", default: true
    t.index ["molecule_id"], name: "index_ms_ms_peaks_on_molecule_id"
    t.index ["ms_ms_id"], name: "index_ms_ms_peaks_on_ms_ms_id"
  end

  create_table "nmr_one_d", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.text "notes"
    t.string "sample_concentration"
    t.string "solvent"
    t.decimal "sample_mass", precision: 15, scale: 10
    t.text "spectra_assessment"
    t.text "sample_assessment"
    t.text "sample_source"
    t.date "collection_date"
    t.string "instrument_type"
    t.integer "peak_counter"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "nucleus"
    t.string "frequency"
    t.string "sample_ph"
    t.decimal "sample_temperature", precision: 15, scale: 10
    t.string "chemical_shift_reference"
    t.boolean "searchable", default: false
    t.string "sample_concentration_units"
    t.string "sample_mass_units"
    t.string "sample_temperature_units"
    t.integer "structure_id"
    t.string "genus"
    t.string "species"
    t.string "data_source_type"
    t.boolean "predicted", default: false, null: false
    t.decimal "spectrometer_frequency_MHz", precision: 21, scale: 16
    t.integer "fid_complex_points"
    t.integer "spectrum_real_points"
    t.integer "linewidth"
    t.decimal "fid_first_point_multiplier", precision: 3, scale: 1
    t.integer "center_frequency_ppm"
    t.integer "sweep_width_ppm", unsigned: true
    t.boolean "exported", default: true, null: false
    t.boolean "simulated"
    t.integer "distinct_peaks"
    t.index ["exported"], name: "index_nmr_one_d_on_exported"
    t.index ["nucleus"], name: "index_nmr_one_d_on_nucleus"
    t.index ["peak_counter"], name: "index_nmr_one_d_on_peak_counter"
    t.index ["structure_id"], name: "fk_rails_6182d8dcff"
  end

  create_table "nmr_one_d_assignments", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3", force: :cascade do |t|
    t.integer "spectra_id"
    t.integer "atom_id"
    t.string "atom_symbol"
    t.decimal "chemical_shift_true", precision: 15, scale: 10
    t.decimal "chemical_shift_pred", precision: 15, scale: 10
    t.decimal "chemical_shift_error", precision: 15, scale: 10
    t.decimal "intensity_true", precision: 21, scale: 10
    t.decimal "intensity_pred", precision: 21, scale: 10
    t.decimal "intensity_error", precision: 21, scale: 10
    t.string "multiplet_true"
    t.string "multiplet_pred"
    t.string "j_coupling_true"
    t.string "j_coupling_pred"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "spectra_type"
    t.boolean "experimental"
    t.boolean "stimulated"
    t.boolean "predicted"
    t.index ["spectra_id"], name: "fk_rails_b1ee238e0b"
  end

  create_table "nmr_one_d_peak_assignment_links", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.integer "nmr_one_d_peak_assignment_id"
    t.integer "nmr_one_d_peak_id"
    t.index ["nmr_one_d_peak_assignment_id", "nmr_one_d_peak_id"], name: "index_nmronedlinks"
  end

  create_table "nmr_one_d_peak_assignments", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.string "multiplicity"
    t.integer "atom_id"
    t.integer "atom_group_id"
    t.boolean "ambiguous"
    t.string "assignment_view_file_name"
    t.string "assignment_view_content_type"
    t.integer "assignment_view_file_size"
    t.datetime "assignment_view_updated_at"
    t.boolean "export", default: true
  end

  create_table "nmr_one_d_peak_group_links", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.integer "nmr_one_d_peak_group_id"
    t.integer "nmr_one_d_peak_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "nmr_one_d_peak_groups", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.string "multiplicity"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "nmr_one_d_peaks", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.integer "nmr_one_d_id"
    t.decimal "chemical_shift", precision: 15, scale: 10
    t.decimal "intensity", precision: 21, scale: 10
    t.integer "unique_0"
    t.integer "unique_1"
    t.integer "unique_2"
    t.integer "unique_3"
    t.integer "unique_4", unsigned: true
    t.decimal "chemical_shift_error", precision: 15, scale: 10
    t.decimal "intensity_error", precision: 15, scale: 10
    t.integer "atom_id"
    t.string "atom_symbol"
    t.decimal "chemical_shift_pred", precision: 15, scale: 10
    t.decimal "chemical_shift_true", precision: 15, scale: 10
    t.string "multiplet_true"
    t.string "multiplet_pred"
    t.string "j_coupling_true"
    t.string "j_coupling_pred"
    t.decimal "peak_position_ppm", precision: 20, scale: 15
    t.decimal "intensity_pred", precision: 23, scale: 19
    t.integer "structure_id"
    t.string "nucleus"
    t.index ["nmr_one_d_id"], name: "index_nmr_one_d_peaks_on_nmr_one_d_id"
    t.index ["structure_id"], name: "fk_rails_efa8e6ff6b"
  end

  create_table "nmr_two_d", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.text "notes"
    t.string "sample_concentration"
    t.string "solvent"
    t.decimal "sample_mass", precision: 15, scale: 10
    t.text "sample_assessment"
    t.text "spectra_assessment"
    t.text "sample_source"
    t.date "collection_date"
    t.string "instrument_type"
    t.integer "peak_counter"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "nucleus_x"
    t.string "nucleus_y"
    t.string "frequency"
    t.string "sample_ph"
    t.decimal "sample_temperature", precision: 15, scale: 10
    t.string "chemical_shift_reference"
    t.string "sample_concentration_units"
    t.string "sample_mass_units"
    t.string "sample_temperature_units"
    t.boolean "searchable"
    t.integer "structure_id"
    t.boolean "predicted"
    t.boolean "exported"
    t.index ["structure_id"], name: "fk_rails_030e3ddbde"
  end

  create_table "nmr_two_d_peaks", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.integer "nmr_two_d_id"
    t.decimal "chemical_shift_x", precision: 15, scale: 10
    t.decimal "chemical_shift_y", precision: 15, scale: 10
    t.decimal "intensity", precision: 15, scale: 10
    t.integer "unique_0"
    t.integer "unique_1"
    t.integer "unique_2"
    t.integer "unique_3"
    t.integer "unique_4"
    t.decimal "intensity_height", precision: 25, scale: 10
    t.decimal "intensity_volume", precision: 25, scale: 10
    t.string "coupling_pattern_x"
    t.string "coupling_pattern_y"
    t.index ["nmr_two_d_id"], name: "index_nmr_two_d_peaks_on_nmr_two_d_id"
  end

  create_table "properties", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3", force: :cascade do |t|
    t.integer "structure_id", null: false
    t.string "name", null: false
    t.string "value", limit: 5000, null: false
    t.string "source", null: false
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["name"], name: "index_properties_on_name"
    t.index ["structure_id", "name", "source"], name: "index_properties_on_structure_id_and_name_and_source", unique: true
    t.index ["structure_id", "name"], name: "index_properties_on_structure_id_and_name"
    t.index ["structure_id"], name: "index_properties_on_structure_id"
  end

  create_table "references", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.integer "spectra_id"
    t.string "spectra_type"
    t.string "pubmed_id"
    t.text "ref_text"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "database"
    t.string "database_id"
    t.index ["database", "database_id"], name: "index_references_on_database_and_database_id"
    t.index ["spectra_type", "spectra_id"], name: "index_references_on_spectra_type_and_spectra_id"
  end

  create_table "registration_histories", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.integer "structure_id"
    t.integer "database_registration_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "exported", default: true
    t.index ["database_registration_id"], name: "index_registration_histories_on_database_registration_id"
    t.index ["structure_id"], name: "index_registration_histories_on_structure_id"
  end

  create_table "sops", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.string "name"
    t.string "file_file_name"
    t.string "file_content_type"
    t.integer "file_file_size"
    t.datetime "file_updated_at"
  end

  create_table "sources", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.string "name", null: false
    t.boolean "active", default: false, null: false
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "human_name"
  end

  create_table "spectra_sops", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.integer "spectra_id"
    t.string "spectra_type"
    t.integer "sop_id"
    t.index ["spectra_id"], name: "index_spectra_sops_on_spectra_id"
  end

  create_table "structure_subsets", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.integer "structure_id"
    t.integer "subset_id"
    t.index ["structure_id"], name: "index_structure_subsets_on_structure_id"
    t.index ["subset_id"], name: "index_structure_subsets_on_subset_id"
  end

  create_table "structures", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.integer "jchem_id", null: false
    t.text "original_structure", limit: 16777215, null: false
    t.string "inchikey", limit: 27, null: false
    t.string "formula", limit: 100
    t.decimal "molecular_weight", precision: 15, scale: 4
    t.decimal "exact_mass", precision: 20, scale: 9
    t.datetime "properties_updated_at"
    t.datetime "wrangled_on"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["exact_mass"], name: "index_structures_on_exact_mass"
    t.index ["inchikey"], name: "index_structures_on_inchikey", unique: true
    t.index ["jchem_id"], name: "index_structures_on_jchem_id", unique: true
  end

  create_table "subsets", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.string "name"
  end

  create_table "synonym_sources", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.integer "synonym_id"
    t.integer "source_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["source_id"], name: "index_synonym_sources_on_source_id"
    t.index ["synonym_id"], name: "index_synonym_sources_on_synonym_id"
  end

  create_table "synonyms", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.integer "structure_id", null: false
    t.string "name", limit: 1000, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "exclude", default: false
    t.index ["name"], name: "index_synonyms_on_name", length: 255
    t.index ["structure_id"], name: "index_synonyms_on_structure_id"
  end

  create_table "taggings", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3", force: :cascade do |t|
    t.integer "tag_id"
    t.integer "taggable_id"
    t.string "taggable_type"
    t.integer "tagger_id"
    t.string "tagger_type"
    t.string "context", limit: 128
    t.datetime "created_at"
    t.index ["tag_id", "taggable_id", "taggable_type", "context", "tagger_id", "tagger_type"], name: "taggings_idx", unique: true
    t.index ["taggable_id", "taggable_type", "context"], name: "index_taggings_on_taggable_id_and_taggable_type_and_context"
  end

  create_table "tags", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3", force: :cascade do |t|
    t.string "name"
    t.integer "taggings_count", default: 0
    t.index ["name"], name: "index_tags_on_name", unique: true
  end

  create_table "wrangler_curations", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.integer "wrangler_id"
    t.integer "curation_id"
    t.string "query"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["curation_id"], name: "index_wrangler_curations_on_curation_id"
    t.index ["query"], name: "index_wrangler_curations_on_query"
    t.index ["wrangler_id"], name: "index_wrangler_curations_on_wrangler_id"
  end

  create_table "wranglers", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci", force: :cascade do |t|
    t.string "name", null: false
    t.string "runner", null: false
    t.text "query", limit: 4294967295, null: false
    t.datetime "started_at"
    t.datetime "finished_at"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_foreign_key "adducts", "structures"
  add_foreign_key "c_ms", "structures"
  add_foreign_key "c_ms_peak_assignments", "c_ms_peaks"
  add_foreign_key "c_ms_peak_assignments", "structures"
  add_foreign_key "c_ms_peaks", "c_ms", on_delete: :cascade
  add_foreign_key "curations", "structures", on_delete: :cascade
  add_foreign_key "database_registrations", "sources"
  add_foreign_key "database_registrations", "structures"
  add_foreign_key "ei_ms", "structures"
  add_foreign_key "formats", "structures", on_delete: :cascade
  add_foreign_key "ms_ms", "structures"
  add_foreign_key "ms_ms_peak_assignments", "structures"
  add_foreign_key "nmr_one_d", "structures"
  add_foreign_key "nmr_one_d_peaks", "structures"
  add_foreign_key "nmr_two_d", "structures"
  add_foreign_key "properties", "structures", on_delete: :cascade
  add_foreign_key "registration_histories", "database_registrations"
  add_foreign_key "registration_histories", "structures"
  add_foreign_key "structure_subsets", "structures"
  add_foreign_key "structure_subsets", "subsets"
  add_foreign_key "synonym_sources", "sources"
  add_foreign_key "synonym_sources", "synonyms"
  add_foreign_key "wrangler_curations", "curations"
  add_foreign_key "wrangler_curations", "wranglers"
end
