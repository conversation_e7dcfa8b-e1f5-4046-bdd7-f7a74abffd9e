class ChangeColumnNullFalse < ActiveRecord::Migration[4.2]
  def up
    change_column_null(:molecules, :structure_id, false)
    change_column_null(:molecules, :source_id, false)
    change_column_null(:molecules, :database_id, false)
    change_column_null(:molecules, :original_structure, false)

    change_column_null(:properties, :molecule_id, false)
    change_column_null(:properties, :name, false)
    change_column_null(:properties, :value, false)
    change_column_null(:properties, :source, false)

    change_column_null(:formats, :molecule_id, false)
    change_column_null(:formats, :name, false)
    change_column_null(:formats, :value, false)

    change_column_null(:sources, :name, false)
    change_column_null(:sources, :active, false)
  end

  def down
  end
end
