class AddMoreFieldsToNmrOneD < ActiveRecord::Migration[5.2]
  def change
    add_column :nmr_one_d, :predicted, :boolean, null: false, default: false
    add_column :nmr_one_d, :spectrometer_frequency_MHz, :decimal, precision: 21, scale: 16
    add_column :nmr_one_d, :fid_complex_points, :integer
    add_column :nmr_one_d, :spectrum_real_points, :integer
    add_column :nmr_one_d, :linewidth, :integer
    add_column :nmr_one_d, :fid_first_point_multiplier, :decimal, precision: 3, scale: 1
    add_column :nmr_one_d, :center_frequency_ppm, :integer
    add_column :nmr_one_d, :sweep_width_ppm, :integer
    add_column :nmr_one_d, :exported, :boolean, null: false, default: true
  end
end
