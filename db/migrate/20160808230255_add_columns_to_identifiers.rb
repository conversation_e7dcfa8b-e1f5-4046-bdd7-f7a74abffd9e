class AddColumnsToIdentifiers < ActiveRecord::Migration[4.2]
  def change
    add_column :identifiers, :kegg_id, :string
    add_column :identifiers, :chemspider_id, :string
    add_column :identifiers, :name, :string
    add_column :identifiers, :kegg_drug_id, :string
    add_column :identifiers, :chembl_id, :string
    add_column :identifiers, :drugbank_id, :string
    add_column :identifiers, :iuphar_id, :string
    add_column :identifiers, :chebi_id, :string
    add_column :identifiers, :emolecules_id, :string
    add_column :identifiers, :ibm_patent_id, :string
    add_column :identifiers, :patent_id, :string
    add_column :identifiers, :surechem_id, :string
    add_column :identifiers, :pubchem_thomson_id, :string
    add_column :identifiers, :pubchem_id, :string
    add_column :identifiers, :molport_id, :string
    add_column :identifiers, :nikkaji_id, :string
    add_column :identifiers, :ymdb_id, :string
    add_column :identifiers, :pdbe_id, :string
    add_column :identifiers, :fda_srs_id, :string
    add_column :identifiers, :hmdb_id, :string
    add_column :identifiers, :actor_id, :string
    add_column :identifiers, :bindingdb_id, :string
    add_column :identifiers, :zinc_id, :string
    add_column :identifiers, :mcule_id, :string
    add_column :identifiers, :recon_id, :string
    add_column :identifiers, :t3db_id, :string
    add_column :identifiers, :pharmkgb_id, :string
    add_column :identifiers, :nmrshiftdb_id, :string
    add_column :identifiers, :foodb_id, :string
    add_column :identifiers, :ecmdb_id, :string
    add_column :identifiers, :cas, :string
    add_column :identifiers, :selleck_id, :string
    add_column :identifiers, :lincs_id, :string
    add_column :identifiers, :pubchem_dotf_id, :string
    add_column :identifiers, :atlas_id, :string
    add_column :identifiers, :nih_id, :string
    add_column :identifiers, :ligand_expo_id, :string
    add_column :identifiers, :phenol_id, :string
    add_column :identifiers, :meta_cyc_id, :string
    add_column :identifiers, :wikipedia_id, :string
    add_column :identifiers, :knapsack_id, :string
    add_column :identifiers, :bigg_id, :string
    add_column :identifiers, :nugowiki_id, :string
    add_column :identifiers, :metagene_id, :string
    add_column :identifiers, :metlin_id, :string
    add_column :identifiers, :iupac_name, :string
  end
end
