class MoveMoleculeToStructure < ActiveRecord::Migration[4.2]
  def change
    rename_table(:molecules, :structures)
    rename_column(:structures, :structure_id, :jchem_id)
    rename_column(:database_registrations, :molecule_id, :structure_id)
    rename_column(:formats, :molecule_id, :structure_id)
    rename_column(:properties, :molecule_id, :structure_id)
    rename_column(:identifiers, :molecules_id, :structure_id)
  end
end
