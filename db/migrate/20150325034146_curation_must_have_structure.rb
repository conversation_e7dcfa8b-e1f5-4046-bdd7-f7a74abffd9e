class CurationMustHaveStructure < ActiveRecord::Migration[4.2]
  def change
    change_column_null(:curations, :structure_id, false)
    change_column_null(:curations, :result, false)
    remove_foreign_key(:curations, :structures)
    remove_index(:curations, :structure_id)
    add_index(:curations, :structure_id, unique: true)
    add_foreign_key(:curations, :structures, on_delete: :cascade)
  end
end
