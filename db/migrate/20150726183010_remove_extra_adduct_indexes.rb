class RemoveExtraAdductIndexes < ActiveRecord::Migration[4.2]
  def change
    remove_index(:adducts, name: 'inchi_key')
    remove_index(:adducts, name: 'index_adducts_on_structure_id')
    rename_index(:adducts, 'index_adducts_on_inchi_key_and_adduct_mass',
                           'index_adducts_on_adduct_mass')
    rename_index(:adducts, 'adduct_type',
                           'index_adducts_on_adduct_type_and_mass')
    add_index(:adducts, [:structure_id, :adduct_type, :adduct, :adduct_mass],
              name: 'index_on_all', unique: true)
  end
end
