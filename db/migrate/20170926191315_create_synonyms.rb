class CreateSynonyms < ActiveRecord::Migration[4.2]
  def change
    drop_table 'synonyms' if ApplicationRecord.connection.table_exists? 'synonyms'

    create_table :synonyms do |t|
      t.belongs_to :structure, index: true, null: false
      t.string :name, null: false, :limit => 1000
      t.string :source

      t.timestamps null: false
    end
    add_index :synonyms, :name, :length => {:name => 255}
  end
end
