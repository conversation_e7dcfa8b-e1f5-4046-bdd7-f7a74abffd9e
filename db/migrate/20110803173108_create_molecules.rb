class CreateMolecules < ActiveRecord::Migration[4.2]
  def change
    create_table :molecules do |t|
      t.integer :structure_id
      t.integer :source_id
      t.string :database_id
      t.text :original_structure, :length => 2.megabytes

      t.timestamps
    end
    
    add_index :molecules, :structure_id, :unique => true
    add_index :molecules, :source_id
    add_index :molecules, :database_id, :unique => true
  end
end
