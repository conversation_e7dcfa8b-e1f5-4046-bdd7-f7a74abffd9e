class CreateNmrOneDAssignments < ActiveRecord::Migration[5.2]
  def change
    create_table :nmr_one_d_assignments do |t|
      t.integer :nmr_one_d_id
      t.integer :atom_id
      t.string :atom_symbol
      t.decimal :chemical_shift_true, precision: 15, scale: 10
      t.decimal :chemical_shift_pred, precision: 15, scale: 10
      t.decimal :chemical_shift_error, precision: 15, scale: 10
      t.decimal :intensity_true, precision: 21, scale: 10
      t.decimal :intensity_pred, precision: 21, scale: 10
      t.decimal :intensity_error, precision: 21, scale: 10
      t.string :multiplet_true
      t.string :multiplet_pred
      t.string :j_coupling_true
      t.string :j_coupling_pred

      t.timestamps
    end

    add_foreign_key :nmr_one_d_assignments, :nmr_one_d
    
  end
end
