class CreateWranglerAnnotations < ActiveRecord::Migration[4.2]
  def change
    create_table :wrangler_annotations do |t|
      t.belongs_to :structure, index: { unique: true }
      t.text :result, limit: 50.megabytes

      t.timestamps null: false
    end
    add_foreign_key :wrangler_annotations, :structures, on_delete: :cascade

    create_join_table(:wranglers, :wrangler_annotations)
    add_index :wrangler_annotations_wranglers, 
      [:wrangler_id, :wrangler_annotation_id], 
      name: 'index_on_keys', unique: true
  end
end
