class CreateProperties < ActiveRecord::Migration[4.2]
  def change
    create_table :properties do |t|
      t.references :molecule
      t.string :name
      t.string :value
      t.string :source

      t.timestamps
    end
    
    add_index :properties, :molecule_id
    add_index :properties, [ :molecule_id, :name ]
    add_index :properties, [ :molecule_id, :name, :source ], :unique => true
  end
end
