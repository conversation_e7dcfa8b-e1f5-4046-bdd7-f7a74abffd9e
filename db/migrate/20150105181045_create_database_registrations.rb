class CreateDatabaseRegistrations < ActiveRecord::Migration[4.2]
  def change
    create_table :database_registrations do |t|
      t.references :molecule, index: true, null: false
      t.references :source, index: true, null: false
      t.string :database_id, null: false, index: { unique: true }

      t.timestamps null: false
    end
    add_foreign_key :database_registrations, :molecules, on_delete: :restrict
    add_foreign_key :database_registrations, :sources, on_delete: :restrict
  end
end
