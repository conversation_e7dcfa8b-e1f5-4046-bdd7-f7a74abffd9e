class AddFieldsToNmrOneDPeaks < ActiveRecord::Migration[5.2]
  def change
    add_column(:nmr_one_d_peaks, :atom_id, :integer)
    add_column(:nmr_one_d_peaks, :atom_symbol, :string)
    add_column(:nmr_one_d_peaks, :chemical_shift_pred, :decimal, precision: 15, scale: 10)
    add_column(:nmr_one_d_peaks, :chemical_shift_true, :decimal, precision: 15, scale: 10)
    add_column(:nmr_one_d_peaks, :multiplet_true, :string)
    add_column(:nmr_one_d_peaks, :multiplet_pred, :string)
    add_column(:nmr_one_d_peaks, :j_coupling_true, :string)
    add_column(:nmr_one_d_peaks, :j_coupling_pred, :string)
  end
end
