#!/usr/bin/env ruby

# Test MOL block parsing for fingerprint generation
require_relative 'config/environment'

puts "Testing MOL block parsing for fingerprint generation..."

# Test the helper method from the rake task with MOL block priority
def generate_morgan_fingerprint(structure)
  script = <<~PYTHON
    import sys
    import json
    from rdkit import Chem
    from rdkit.Chem import rdMolDescriptors

    def parse_structure(structure):
        try:
            # Debug: Show structure format info
            print(f"Structure length: {len(structure)}", file=sys.stderr)
            print(f"First 100 chars: {repr(structure[:100])}", file=sys.stderr)

            # Check if it looks like a MOL block
            is_mol_like = any(marker in structure for marker in ['V2000', 'V3000', 'M  END'])
            print(f"Appears to be MOL block: {is_mol_like}", file=sys.stderr)

            # Try parsing as MOL block first (most common format in original_structure)
            if is_mol_like or len(structure) > 50:  # MOL blocks are typically longer
                mol = Chem.MolFromMolBlock(structure, sanitize=False)
                if mol is not None:
                    try:
                        Chem.SanitizeMol(mol)
                        print(f"Parsed as MOL block: {mol.GetNumAtoms()} atoms", file=sys.stderr)
                        return mol
                    except Exception as sanitize_error:
                        print(f"MOL block parsed but sanitization failed: {sanitize_error}", file=sys.stderr)
                        # Try without sanitization
                        mol = Chem.MolFromMolBlock(structure, sanitize=False)
                        if mol is not None and mol.GetNumAtoms() > 0:
                            print(f"Using unsanitized MOL: {mol.GetNumAtoms()} atoms", file=sys.stderr)
                            return mol

            # Try parsing as SMILES (strip whitespace for SMILES)
            smiles_candidate = structure.strip()
            if len(smiles_candidate) < 200 and '\\n' not in smiles_candidate:  # SMILES are typically short and single-line
                mol = Chem.MolFromSmiles(smiles_candidate)
                if mol is not None:
                    print(f"Parsed as SMILES: {mol.GetNumAtoms()} atoms", file=sys.stderr)
                    return mol

            # Try parsing as InChI
            if structure.strip().startswith('InChI='):
                mol = Chem.MolFromInchi(structure.strip())
                if mol is not None:
                    print(f"Parsed as InChI: {mol.GetNumAtoms()} atoms", file=sys.stderr)
                    return mol

            print("Failed to parse structure with any format", file=sys.stderr)
            return None
        except Exception as e:
            print(f"Error parsing structure: {e}", file=sys.stderr)
            return None

    # Read structure from input file
    with open(sys.argv[1], 'r') as f:
        structure = f.read()  # Don't strip - MOL blocks need exact formatting

    # Parse the structure
    mol = parse_structure(structure)

    if mol is not None:
        try:
            print(f"Successfully parsed molecule with {mol.GetNumAtoms()} atoms", file=sys.stderr)

            # Generate Morgan fingerprint as bit vector
            fp = rdMolDescriptors.GetMorganFingerprintAsBitVect(mol, 2, nBits=2048)

            # Convert to bit string for storage
            fp_hex = fp.ToBitString()

            print(f"Generated fingerprint with {len(fp_hex)} bits", file=sys.stderr)

            with open(sys.argv[2], 'w') as f:
                f.write(fp_hex)
        except Exception as e:
            print(f"Error generating fingerprint: {e}", file=sys.stderr)
            with open(sys.argv[2], 'w') as f:
                f.write("")
    else:
        print("Failed to parse structure", file=sys.stderr)
        with open(sys.argv[2], 'w') as f:
            f.write("")
  PYTHON

  begin
    result = Jchem.run_rdkit(script, structure)
    return result.strip.empty? ? nil : result.strip
  rescue => e
    puts "Error: #{e.message}"
    return nil
  end
end

# Test with a sample structure from the database
puts "\n1. Testing with database structure:"
begin
  structure = Structure.first
  if structure
    puts "  Testing structure ID #{structure.id} (#{structure.inchikey})"
    puts "  Original structure length: #{structure.original_structure.length}"
    puts "  Structure starts with: #{structure.original_structure[0..100]}..."
    
    # Check if it looks like a MOL block
    is_mol_block = structure.original_structure.include?("M  END") || 
                   structure.original_structure.include?("V2000") ||
                   structure.original_structure.include?("V3000")
    puts "  Appears to be MOL block: #{is_mol_block}"
    
    fingerprint = generate_morgan_fingerprint(structure.original_structure)
    if fingerprint
      puts "  ✓ Generated fingerprint: #{fingerprint[0..50]}... (length: #{fingerprint.length})"
      puts "  Current stored fingerprint: #{structure.morgan_fingerprint ? structure.morgan_fingerprint[0..50] + '...' : 'nil'}"
    else
      puts "  ✗ Failed to generate fingerprint"
    end
  else
    puts "  No structures found in database"
  end
rescue => e
  puts "  Error accessing database: #{e.message}"
end

# Test with the user's example MOL block
puts "\n2. Testing with user's example MOL block:"
user_mol = <<~MOL

  MJ161017

 11 11  0  0  0  0  0  0  0  0999 V2000
 9999.978410000.2014    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
10000.6930 9999.7891    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
10001.409010000.2014    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
10002.1251 9999.7891    0.0000 O   0  0  0  0  0  0  0  0  0  0  0  0
10001.409010001.0268    0.0000 O   0  0  0  0  0  0  0  0  0  0  0  0
10000.6930 9998.9631    0.0000 N   0  0  0  0  0  0  0  0  0  0  0  0
 9998.590810000.2834    0.0000 N   0  0  0  0  0  0  0  0  0  0  0  0
 9997.9234 9999.7984    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
 9998.1783 9999.0138    0.0000 N   0  0  0  0  0  0  0  0  0  0  0  0
 9999.0033 9999.0138    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
 9999.2582 9999.7984    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
  1  2  1  0  0  0  0
  2  3  1  0  0  0  0
  2  6  1  1  0  0  0
  3  4  1  0  0  0  0
  3  5  2  0  0  0  0
 10 11  2  0  0  0  0
  7  8  2  0  0  0  0
  7 11  1  0  0  0  0
  8  9  1  0  0  0  0
  9 10  1  0  0  0  0
  1 11  1  0  0  0  0
M  END
MOL

puts "  MOL block length: #{user_mol.length}"
puts "  Contains V2000: #{user_mol.include?('V2000')}"
puts "  Contains M  END: #{user_mol.include?('M  END')}"

fingerprint = generate_morgan_fingerprint(user_mol)
if fingerprint
  puts "  ✓ Generated fingerprint: #{fingerprint[0..50]}... (length: #{fingerprint.length})"
else
  puts "  ✗ Failed to generate fingerprint"
end

# Test with a simple MOL block
puts "\n3. Testing with simple MOL block (methane):"
simple_mol = <<~MOL

  Mrv2014 01010100002D

  1  0  0  0  0  0            999 V2000
   -0.0000    0.0000    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
M  END
MOL

fingerprint = generate_morgan_fingerprint(simple_mol)
if fingerprint
  puts "  ✓ Generated fingerprint: #{fingerprint[0..50]}... (length: #{fingerprint.length})"
else
  puts "  ✗ Failed to generate fingerprint"
end

# Test with SMILES for comparison
puts "\n4. Testing with SMILES (methane):"
smiles = "C"
fingerprint = generate_morgan_fingerprint(smiles)
if fingerprint
  puts "  ✓ Generated fingerprint: #{fingerprint[0..50]}... (length: #{fingerprint.length})"
else
  puts "  ✗ Failed to generate fingerprint"
end

puts "\nTest complete!"
