#!/usr/bin/env ruby

# Test MOL block parsing for fingerprint generation
require_relative 'config/environment'

puts "Testing MOL block parsing for fingerprint generation..."

# Test the helper method from the rake task with MOL block priority
def generate_morgan_fingerprint(structure)
  script = <<~PYTHON
    import sys
    import json
    from rdkit import Chem
    from rdkit.Chem import rdMolDescriptors
    
    def parse_structure(structure):
        try:
            # Try parsing as MOL block first (most common format in original_structure)
            mol = Chem.MolFromMolBlock(structure)
            if mol is None:
                # Try parsing as SMILES
                mol = Chem.MolFromSmiles(structure)
            if mol is None:
                # Try parsing as InChI
                mol = Chem.MolFromInchi(structure)
            return mol
        except Exception as e:
            print(f"Error parsing structure: {e}", file=sys.stderr)
            return None
    
    # Read structure from input file
    with open(sys.argv[1], 'r') as f:
        structure = f.read().strip()
    
    print(f"Structure length: {len(structure)}", file=sys.stderr)
    print(f"Structure starts with: {structure[:50]}...", file=sys.stderr)
    
    # Parse the structure
    mol = parse_structure(structure)
    
    if mol is not None:
        try:
            print(f"Successfully parsed molecule with {mol.GetNumAtoms()} atoms", file=sys.stderr)
            
            # Generate Morgan fingerprint as bit vector
            fp = rdMolDescriptors.GetMorganFingerprintAsBitVect(mol, 2, nBits=2048)
            
            # Convert to bit string for storage
            fp_hex = fp.ToBitString()
            
            print(f"Generated fingerprint with {len(fp_hex)} bits", file=sys.stderr)
            
            with open(sys.argv[2], 'w') as f:
                f.write(fp_hex)
        except Exception as e:
            print(f"Error generating fingerprint: {e}", file=sys.stderr)
            with open(sys.argv[2], 'w') as f:
                f.write("")
    else:
        print("Failed to parse structure", file=sys.stderr)
        with open(sys.argv[2], 'w') as f:
            f.write("")
  PYTHON
  
  begin
    result = Jchem.run_rdkit(script, structure)
    return result.strip.empty? ? nil : result.strip
  rescue => e
    puts "Error: #{e.message}"
    return nil
  end
end

# Test with a sample structure from the database
puts "\n1. Testing with database structure:"
begin
  structure = Structure.first
  if structure
    puts "  Testing structure ID #{structure.id} (#{structure.inchikey})"
    puts "  Original structure length: #{structure.original_structure.length}"
    puts "  Structure starts with: #{structure.original_structure[0..100]}..."
    
    # Check if it looks like a MOL block
    is_mol_block = structure.original_structure.include?("M  END") || 
                   structure.original_structure.include?("V2000") ||
                   structure.original_structure.include?("V3000")
    puts "  Appears to be MOL block: #{is_mol_block}"
    
    fingerprint = generate_morgan_fingerprint(structure.original_structure)
    if fingerprint
      puts "  ✓ Generated fingerprint: #{fingerprint[0..50]}... (length: #{fingerprint.length})"
      puts "  Current stored fingerprint: #{structure.morgan_fingerprint ? structure.morgan_fingerprint[0..50] + '...' : 'nil'}"
    else
      puts "  ✗ Failed to generate fingerprint"
    end
  else
    puts "  No structures found in database"
  end
rescue => e
  puts "  Error accessing database: #{e.message}"
end

# Test with a simple MOL block
puts "\n2. Testing with simple MOL block (methane):"
simple_mol = <<~MOL

  Mrv2014 01010100002D          

  1  0  0  0  0  0            999 V2000
   -0.0000    0.0000    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
M  END
MOL

fingerprint = generate_morgan_fingerprint(simple_mol)
if fingerprint
  puts "  ✓ Generated fingerprint: #{fingerprint[0..50]}... (length: #{fingerprint.length})"
else
  puts "  ✗ Failed to generate fingerprint"
end

# Test with SMILES for comparison
puts "\n3. Testing with SMILES (methane):"
smiles = "C"
fingerprint = generate_morgan_fingerprint(smiles)
if fingerprint
  puts "  ✓ Generated fingerprint: #{fingerprint[0..50]}... (length: #{fingerprint.length})"
else
  puts "  ✗ Failed to generate fingerprint"
end

puts "\nTest complete!"
