source 'https://rubygems.org'
ruby '2.7.1'
gem 'actionview', '5.2.3'
gem 'activesupport', '5.2.3'
gem 'activemodel', '5.2.3'
gem 'rails', '5.2.3'
gem 'bootsnap', require: false
gem 'actionpack', '5.2.3'

gem 'rack-cors'
# gem 'mysql2'
# for some reason new mysql2 doesn't work with rails 4.x.x (a bug)
gem 'mysql2'
#gem 'mini_racer'
gem 'sass-rails', '~> 5.0'
gem 'coffee-rails', '~> 5.0.0'
gem 'uglifier', '>= 1.3.0'
gem 'jquery-rails'
gem 'jbuilder', '~> 2.0'
gem 'ransack', github: 'activerecord-hackery/ransack'
gem 'jbuilder_cache_multi'
gem 'sidekiq', '< 6.0.0'
gem 'sinatra', require: false # Required by sidekiq/web
gem 'savon', '~>2.5.1'
gem 'soap4r'
gem 'kaminari'
gem 'actionpack-action_caching'
gem 'acts-as-taggable-on'
gem 'rest-client'
gem 'simple_form'
gem 'responders', '~> 3.0'
gem 'paperclip'
gem 'whenever', require: false
gem 'typhoeus'
gem 'zip-zip', require: false
gem 'http'
gem 'activeadmin'
gem 'devise'
gem 'spectrum_hash', git: "*****************:wishartlab/spectrum_hash.git"
gem 'redis', "~> 4.0"
gem 'crack'
gem 'optimist'
gem 'oink'
gem 'rake', '~>13.0.1'
#gem 'backport_new_renderer'
gem 'deep_cloneable'
gem 'gsl'
gem 'similarity-NS', git: "*****************:wishartlab/similarity-ns.git"
gem 'kmeans-clusterer'
gem 'scalpel'
gem 'pragmatic_segmenter'
gem 'memory_profiler'
gem 'parallel'
gem 'rails-controller-testing'
# This is a requirement in ChemoSummarizer for some reason:
gem 'sqlite3', '~> 1.3', '>= 1.3.11'
#gem 'nokogiri', '~> 1.6.1'
# Locking to 2.x for now - don't have time to fix for version 3.
# http://stackoverflow.com/questions/34344094/after-gem-update-test-fail-with-asset-was-not-declared-to-be-precompiled-in-pr
gem 'sprockets-rails', '~> 2.3.3'
#gem 'similarity'
# Wishart gems:
gem 'wishart', git: '*****************:wishartlab/wishart', :branch => 'rails5.2'
gem 'data-wrangler', git: '*****************:wishartlab/datawrangler.git'
# gem 'data-wrangler', git: '*****************:wishartlab/datawrangler.git', :branch => 'no_classyfire'

gem 'chemoSummarizer',git: '*****************:wishartlab/chemosummarizer.git'
gem 'metbuilder' , git: '*****************:wishartlab/metbuilder.git', :branch => 'master'


gem 'synonym_cleaner', git: '*****************:wishartlab/synonym-cleaner.git',require: false
gem 'moldbi', git: '*****************:wishartlab/moldbi', :branch => "rails_5.2.3"
gem 'admin_mailer', git: '*****************:wishartlab/admin-mailer.git'

gem 'appsignal'

group :production do
  #gem 'newrelic_rpm'
  #gem 'appsignal'
  gem 'execjs'
  gem 'puma'
  gem 'puma_worker_killer'

end

group :development, :test do
  # Call 'byebug' anywhere in the code to stop execution and
  # get a debugger console
  gem 'byebug'
  # Access an IRB console on exception pages or by using
  # <%= console %> in views
  gem 'web-console', '~> 2.0'
  gem 'minitest-rails'
  gem 'thin'
end

group :development do
  gem 'awesome_print'
  gem "capistrano"
  gem 'capistrano-rails', require: false
  gem 'capistrano-bundler', require: false
  gem 'capistrano-sidekiq', require: false
  gem 'capistrano-rbenv', '~> 2.1'
  gem 'capistrano3-puma'
  gem 'better_errors'
  gem 'binding_of_caller'
  gem 'guard-minitest'
  gem 'guard-bundler', require: false
  gem 'guard-rails'
  gem 'rb-fchange', require: false
  gem 'rb-fsevent', require: false
  gem 'rb-inotify', require: false
  gem 'terminal-notifier-guard', require: false
  gem 'terminal-notifier', require: false
  gem 'syncfast', git: '*****************:wishartlab/syncfast'
  gem 'derailed_benchmarks'
  gem 'stackprof' # used by derailed_benchmarks
end
