# Load DSL and Setup Up Stages
require 'capistrano/setup'

# Includes default deployment tasks
require 'capistrano/deploy'
require 'capistrano/bundler'
require 'capistrano/rails'
require 'capistrano/rbenv'
require 'capistrano/sidekiq'

require 'whenever/capistrano'

# Use git as the default
require "capistrano/scm/git"
install_plugin Capistrano::SCM::Git

#Use capistrano puma plugin
require 'capistrano/puma'
install_plugin Capistrano::Puma

# Loads custom tasks from `lib/capistrano/tasks' if you have any defined.
Dir.glob('lib/capistrano/tasks/*.cap').each { |r| import r }


#require 'appsignal/capistrano'
