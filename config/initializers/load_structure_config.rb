# Load the Molecule JChem config file
raw_config = File.read("#{Rails.root}/config/structure.yml")
STRUCTURE_CONFIG = YAML.load(raw_config).symbolize_keys

# Load YAML file into variables for quick access
STRUCTURE_INCHIKEY    = STRUCTURE_CONFIG[:inchikey]
STRUCTURE_STANDARDIZE = STRUCTURE_CONFIG[:standardized]
STRUCTURE_FORMATS     = STRUCTURE_CONFIG[:formats].symbolize_keys
STRUCTURE_PROPERTIES  = STRUCTURE_CONFIG[:properties].symbolize_keys
STRUCTURE_IMAGES      = STRUCTURE_CONFIG[:images].symbolize_keys
