class String
  def to_star
    if self.match(/^\s*$/)
      '?'
    elsif self.match(/\n/)
      ";\n#{self}\n;"
    elsif self.match(/\s/)
      if self.match(/"/)
        "'#{self}'"
      else
        "\"#{self}\""
      end
    else
      self
    end
  end
end

class Date
  def to_star
    self.strftime('%Y-%m-%d')
  end
end

class Time
  def to_star
    self.strftime('%Y-%m-%d')
  end
end

class NilClass
  def to_star
    '?'
  end
end

class Integer
  def to_star
    self.to_s
  end
end

class Float
  def to_star
    self.to_s
  end
end
