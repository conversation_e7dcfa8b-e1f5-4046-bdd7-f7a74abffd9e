# Be sure to restart your server when you modify this file.

# Add new inflection rules using the following format. Inflections
# are locale specific, and you may define rules for as many different
# locales as you wish. All of these examples are active by default:
# ActiveSupport::Inflector.inflections(:en) do |inflect|
#   inflect.plural /^(ox)$/i, '\1en'
#   inflect.singular /^(ox)en/i, '\1'
#   inflect.irregular 'person', 'people'
#   inflect.uncountable %w( fish sheep )
# end

# These inflection rules are supported but not enabled by default:
# ActiveSupport::Inflector.inflections(:en) do |inflect|
#   inflect.acronym 'RESTful'
# end
ActiveSupport::Inflector.inflections do |inflect|
  inflect.irregular 'ms_ms', 'ms_ms'
  inflect.irregular 'nmr_one_d', 'nmr_one_d'
  inflect.irregular 'nmr_two_d', 'nmr_two_d'
  inflect.irregular 'c_ms', 'c_ms'
  inflect.irregular 'ei_ms', 'ei_ms'
  inflect.irregular 'MsMs', 'MsMs'
  inflect.irregular 'NmrOneD', 'NmrOneD'
  inflect.irregular 'NmrTwoD', 'NmrTwoD'
  inflect.irregular 'CMs', 'CMs'
  inflect.irregular 'EiMs', 'EiMs'
end
