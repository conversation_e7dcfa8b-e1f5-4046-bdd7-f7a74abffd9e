require 'sidekiq/web'

Rails.application.routes.draw do


  devise_for :admin_users, ActiveAdmin::Devise.config
  ActiveAdmin.routes(self)

  mount Wishart::Engine => "/w" , as: 'wishart'

  # Resource routes
  resources :molecules, controller: :database_registrations, except: [:new, :edit] do
    resources :properties, only: [:index]
    resource :taggings
    resource :curation, only: :show
    resource :adducts, only: :show
    collection do
      post :batch
    end
    member do
      get :thumb, to: 'images#thumb'
      get :image, to: 'images#image'
      get '3d', to: 'database_registrations#three_d'
      get :refresh
    end
  end

  resources :structures do
    member do
      get :thumb, to: 'images#thumb'
      get :image, to: 'images#image'
      # get :peak, to: 'images#peak'
      get :refresh
      # get '/:type/:id/peak', to: 'images#peak'
    end
  end
  get 'structures/:type/:id/peak', to: 'images#peak'
  resources :tms_derivatives, only: :index

  resources :sources, only: :index
  resources :wranglers, except: [:edit, :update, :delete]

  # Search routes
  match 'search/simple', as: :simple_search, via: [:post, :get]
  post 'search/detailed', as: :detailed_search

  # Spectra routes
  resources :spectra, only: :index, controller: :spectra_collection

  get 'ms_ms/search' => 'ms_ms#search'
  resources :ms_ms do
    resources :peaks, only: :index, controller: :ms_ms_peaks do
      resources :assignments, only: :index, controller: :ms_ms_peak_assignments
    end
  end

  get 'c_ms/search' => 'c_ms#search'
  resources :c_ms do
    resources :peaks, only: :index, controller: :c_ms_peaks
  end

  resources :ei_ms do
    resources :peaks, only: :index, controller: :ei_ms_peaks
  end

  namespace :nmr_one_d do
    resources :search, controller: :search do
      resources :results, only: :index
    end
  end

  resources :nmr_one_d do
    resources :peaks, only: :index, controller: :nmr_one_d_peaks do
      resources :assignments, only: :index, controller: :nmr_one_d_peak_assignments
    end
  end

  get 'nmr_two_d/search' => 'nmr_two_d#search'
  resources :nmr_two_d do
    resources :peaks, only: :index, controller: :nmr_two_d_peaks
  end

  resources :statistics, only: [:show, :index]
  post 'registrations/bulk' => 'registrations#bulk_registration'

  get 'ms/search' => 'ms#search'
  post 'ms/search' => 'ms#search'

  resources :modes, only: :index

  root 'database_registrations#index'

  # Sidekiq status interface - let's you monitor jobs at /sidekiq
  #Sidekiq::Web.use Rack::Auth::Basic do |username, password|
   # username == ACCESS_CONFIG[:username] && password == ACCESS_CONFIG[:password]
  #end if Rails.env.production?
  mount Sidekiq::Web, at: "/sidekiq"
end