require_relative 'boot'

require 'rails/all'

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module Moldb
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 5.2

    config.autoload_paths += %W(#{config.root}/lib)

    config.autoload_paths += %W(#{config.root}/app/models/nmrs)
    config.autoload_paths += %W(#{config.root}/app/models/nmrs/one_d)
    config.autoload_paths += %W(#{config.root}/app/models/nmrs/two_d)
    config.autoload_paths += %W(#{config.root}/app/models/molecules)
    config.autoload_paths += %W(#{config.root}/app/models/ms_mss)
    config.autoload_paths += %W(#{config.root}/app/models/ei_mss)
    config.autoload_paths += %W(#{config.root}/app/models/c_mss)
    config.autoload_paths += %W(#{config.root}/app/models/ms)
    config.autoload_paths += %W(#{config.root}/app/builders)
    config.autoload_paths += %W(#{config.root}/lib/chem_convert)
    config.autoload_paths += %W(#{config.root}/lib/chem_client)
    config.autoload_paths += %W(#{config.root}/lib/chem_calculator)
    config.autoload_paths += %W(#{config.root}/lib/chem_search)
    config.autoload_paths += %W(#{config.root}/lib/chem_standardize)
    config.autoload_paths += %W(#{config.root}/lib/indexers)
    config.autoload_paths += %W(#{config.root}/lib/util)
    config.autoload_paths += %W(#{config.root}/lib/sdf)
    config.autoload_paths += %W(#{config.root}/lib/ms_csv)
    config.autoload_paths += %W(#{config.root}/lib/jchem)
    config.eager_load_paths << Rails.root.join('lib')
    # Settings in config/environments/* take precedence over those specified here.
    # Application configuration can go into files in config/initializers
    # -- all .rb files in that directory are automatically loaded after loading
    # the framework and any gems in your application.

    # Should we???
    config.action_controller.permit_all_parameters = true


    # Oink (for memory profiling)
    config.middleware.use Oink::Middleware
  end
end
