default: &defaults
  # Your push api key, it is possible to set this dynamically using ERB:
  # push_api_key: "<%= ENV['APPSIGNAL_PUSH_API_KEY'] %>"
  push_api_key: "248b34ae-e2ee-4fe7-9ff7-18574edb0b03"

  # Your app's name
  name: "MolDB (NP-MRD)"

  # Actions that should not be monitored by AppSignal
  # ignore_actions:
  #   - ApplicationController#isup

  # Errors that should not be recorded by AppSignal
  # For more information see our docs:
  # https://docs.appsignal.com/ruby/configuration/ignore-errors.html
  # ignore_errors:
  #   - Exception
  #   - NoMemoryError
  #   - ScriptError
  #   - LoadError
  #   - NotImplementedError
  #   - SyntaxError
  #   - SecurityError
  #   - SignalException
  #   - Interrupt
  #   - SystemExit
  #   - SystemStackError

  # See http://docs.appsignal.com/ruby/configuration/options.html for
  # all configuration options.

# Configuration per environment, leave out an environment or set active
# to false to not push metrics for that environment.
development:
  <<: *defaults
  active: true

production:
  <<: *defaults
  active: true
