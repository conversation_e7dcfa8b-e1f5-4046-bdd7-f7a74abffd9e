set :application, 'moldb'
set :repo_url,  "*****************:wishartlab/moldb.git"
set :deploy_to, '/apps/moldb/project'
set :use_sudo, false
set :linked_files, ['config/database.yml', 'config/jchem.yml', 'config/access.yml']
set :linked_dirs, %w{log public/system}
set :linked_dirs, %w{log public/system public/import public/downloads tmp/pids tmp/sockets bundle}
set :keep_releases, 3
set :sidekiq_role, :app
set :sidekiq_processes, 2
set :sidekiq_options_per_process,
  ["--queue critical --concurrency 4", "--queue default --concurrency 2"]
set :branch, ENV['BRANCH'] if ENV['BRANCH']
set :bundle_path, nil
set :bundle_without, nil
set :bundle_flags, nil
set :rbenv_map_bins, %w{rake gem bundle ruby rails sidekiq sidekiqctl}
set :sidekiq_flags, nil

namespace :deploy do
  desc 'Start application'
  task :start do
    on roles(:web) do
      invoke('puma:start')
    end
  end

  desc 'Stop application'
  task :stop do
    on roles(:web) do
      invoke('puma:stop')
    end
  end

  desc 'Restart application'
  task :restart do
    on roles(:web) do
      invoke('puma:phased-restart')
    end
  end

  desc 'Hard-restart application'
  task :hard_restart do
    on roles(:web) do
      invoke('puma:restart')
    end
  end
end
