# Use this file to easily define all of your cron jobs.
#
# It's helpful, but not entirely necessary to understand cron before proceeding.
# http://en.wikipedia.org/wiki/Cron

# Example:
#
# set :output, "/path/to/my/cron_log.log"
#
# every 2.hours do
#   command "/usr/bin/some_great_command"
#   runner "MyModel.some_method"
#   rake "some:great:rake:task"
# end
#
# every 4.days do
#   runner "AnotherModel.prune_old_records"
# end

# Learn more: http://github.com/javan/whenever

set :output, { error: '/apps/moldb/project/shared/log/cron-error.log', standard: '/apps/moldb/project/shared/log/cron.log' }

every 1.month, at: '7pm', roles: [:app] do
  rake "export:all_for[HMDB]"
end

# every :saturday, at: '12am', roles: [:db] do
#   rake "adducts:update"
# end

every :saturday, at: '6pm', roles: [:app] do
  rake "sync_with_moldb:exported_ids_for[all]" # you can use i.e. HMDB, instead of all
  rake "subset:sync_all_biofluids"
end