require "test_helper"

describe WranglersController do
  let(:wrangler) { wranglers :one }

  describe 'when user logged out' do
    it "redirects to login path" do
      get :index
      assert_response :unauthorized
    end
  end

  describe 'when user logged in' do
    before(:each) { http_login }

    describe "GET 'index'" do
      it "populates an array of reports" do
        get :index
        assigns(:wranglers).length.must_equal(2)
        assert_response :success
      end

      it "renders the :index view" do
        get :index
        assert_template :index
      end
    end

    describe "GET 'new'" do
      it 'should be successful' do
        get :new
        assert_response :success
        assert_not_nil assigns(:wrangler)
      end
    end

    describe "POST 'create'" do
      it 'created a new wrangler' do
        assert_difference('Wrangler.count') do
          post :create, wrangler: { name: wrangler.name, query: wrangler.query, runner: wrangler.runner }
        end

        assert_redirected_to wrangler_path(assigns(:wrangler))
      end
    end

    describe "GET 'show'" do
      it 'should be successful' do
        get :show, id: wrangler
        assert_response :success
      end
    end

    describe "DELETE 'destroy'" do
      it 'should be successful' do
        assert_difference('Wrangler.count', -1) do
          delete :destroy, id: wrangler
        end
      end
    end
  end
end