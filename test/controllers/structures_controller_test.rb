require "test_helper"

class StructuresControllerTest < ActionController::TestCase

  def structure
    @structure ||= structures :one
  end

  def test_index
    get :index
    assert_response :success
    assert_not_nil assigns(:structures)
  end

  def test_new
    get :new
    assert_response :success
  end

  def test_create
    assert_difference('Structure.count') do
      post :create, structure: {  }
    end

    assert_redirected_to structure_path(assigns(:structure))
  end

  def test_show
    get :show, id: structure
    assert_response :success
  end

  def test_edit
    get :edit, id: structure
    assert_response :success
  end

  def test_update
    put :update, id: structure, structure: {  }
    assert_redirected_to structure_path(assigns(:structure))
  end

  def test_destroy
    assert_difference('Structure.count', -1) do
      delete :destroy, id: structure
    end

    assert_redirected_to structures_path
  end
end
