require "test_helper"

class CurationsControllerTest < ActionController::TestCase

  def curation
    @curation ||= curations :one
  end

  def test_index
    get :index
    assert_response :success
    assert_not_nil assigns(:curations)
  end

  def test_new
    get :new
    assert_response :success
  end

  def test_create
    assert_difference('Curation.count') do
      post :create, curation: {  }
    end

    assert_redirected_to curation_path(assigns(:curation))
  end

  def test_show
    get :show, id: curation
    assert_response :success
  end

  def test_edit
    get :edit, id: curation
    assert_response :success
  end

  def test_update
    put :update, id: curation, curation: {  }
    assert_redirected_to curation_path(assigns(:curation))
  end

  def test_destroy
    assert_difference('Curation.count', -1) do
      delete :destroy, id: curation
    end

    assert_redirected_to curations_path
  end
end
