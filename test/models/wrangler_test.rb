require "test_helper"

class WranglerTest < ActiveSupport::TestCase

  def wrangler
    @wrangler ||= Wrangler.new(name: 'Wrangler<PERSON>', runner: '<PERSON><PERSON>', query: 'ha')
  end

  def test_valid
    assert wrangler.valid?
  end

  def test_presence_of_name
    wrangler.name = nil
    assert wrangler.invalid?
  end

  def test_length_of_name
    wrangler.name = "A"*256
    assert wrangler.invalid?
  end  

  def test_presence_of_runner
    wrangler.runner = nil
    assert wrangler.invalid?
  end

  def test_length_of_runner
    wrangler.runner = "A"*256
    assert wrangler.invalid?
  end  

  def test_presence_of_query
    wrangler.query = nil
    assert wrangler.invalid?
  end  
end
