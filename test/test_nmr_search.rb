require "benchmark"
require 'set'

#load 'nmr_1d_search.rb'
#load 'nmr_1d_search_lib.rb'

tolerance = 0.01
query_spec_1 = File.open("spec1.txt").readlines.map(&:to_f)
query_spec_2 = File.open("spec2.txt").readlines.map(&:to_f)
query_spec_3 = File.open("spec3.txt").readlines.map(&:to_f)
query_spec_4 = File.open("spec4.txt").readlines.map(&:to_f)

scores_1 = []
scores_2 = []
scores_3 = []
scores_4 = []

NmrOneDSearchLib.clear_cache!
Benchmark.bmbm do |x|
  x.report("build lib"){  NmrOneDSearchLib.rebuild! }
  x.report("load lib"){  NmrOneDSearch.new(nucleus:"1H").lib }
  x.report("search spec 1"){ scores_1 = NmrOneDSearch.new(query_spec:query_spec_1,tolerance:tolerance).rebuild_results! }
  x.report("search spec 2"){ scores_2 = NmrOneDSearch.new(query_spec:query_spec_2,tolerance:tolerance).rebuild_results! }
  x.report("search spec 3"){ scores_3 = NmrOneDSearch.new(query_spec:query_spec_3,tolerance:tolerance).rebuild_results! }
  x.report("search spec 4"){ scores_4 = NmrOneDSearch.new(query_spec:query_spec_4,tolerance:tolerance).rebuild_results! }
  x.report("fetch search spec 1"){ scores_1 = NmrOneDSearch.new(query_spec:query_spec_1,tolerance:tolerance).results }
  x.report("fetch search spec 2"){ scores_2 = NmrOneDSearch.new(query_spec:query_spec_2,tolerance:tolerance).results }
  x.report("fetch search spec 3"){ scores_3 = NmrOneDSearch.new(query_spec:query_spec_3,tolerance:tolerance).results }
  x.report("fetch search spec 4"){ scores_4 = NmrOneDSearch.new(query_spec:query_spec_4,tolerance:tolerance).results }
  #x.report("fetch results with cache id"){ NmrOneDSearch.fetch_from_cache("5d4d5a0c2f984615718c938188a57a3d") }
  x.report("compare a spectrum"){ NmrOneDSearch.new(query_spec:query_spec_1,tolerance:tolerance).compare_to_peak_list(query_spec_2) }
end

puts scores_1[0..0].map{|i| "#{i[0]}\t#{i[1]}" }
puts scores_2[0..0].map{|i| "#{i[0]}\t#{i[1]}" }
puts scores_3[0..0].map{|i| "#{i[0]}\t#{i[1]}" }
puts scores_4[0..0].map{|i| "#{i[0]}\t#{i[1]}" }

puts "***"
puts NmrOneDSearch.new(query_spec:query_spec_1,tolerance:tolerance).compare_to_peak_list(query_spec_1)
puts query_spec_1.join(", ")


puts "\n\n\n*** caching"

puts NmrOneDSearch.new(query_spec:query_spec_1,tolerance:0.01).cache_key
puts NmrOneDSearch.new(query_spec:query_spec_1,tolerance:0.02).cache_key
puts NmrOneDSearch.new(query_spec:query_spec_1,tolerance:0.03).cache_key

#puts NmrOneDSearch.new(query_spec_1,0.2).cache_key
#puts NmrOneDSearch.new(query_spec_1,0.3).cache_key









