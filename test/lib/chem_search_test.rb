require "test_helper"

class ChemSearchTest < ActiveSupport::TestCase
  
  def setup
    # Create test structures with known SMILES
    @structure1 = Structure.create!(
      jchem_id: 1001,
      original_structure: "CCO", # Ethanol
      inchikey: "LFQSCWFLJHTTHZ-UHFFFAOYSA-N",
      formula: "C2H6O",
      molecular_weight: 46.07,
      exact_mass: 46.0419
    )
    
    @structure2 = Structure.create!(
      jchem_id: 1002,
      original_structure: "CC(C)O", # Isopropanol
      inchikey: "KFZMGEQAYNKOFK-UHFFFAOYSA-N",
      formula: "C3H8O",
      molecular_weight: 60.10,
      exact_mass: 60.0575
    )
    
    @structure3 = Structure.create!(
      jchem_id: 1003,
      original_structure: "c1ccccc1", # <PERSON>ene
      inchikey: "UHOVQNZJYSORNB-UHFFFAOYSA-N",
      formula: "C6H6",
      molecular_weight: 78.11,
      exact_mass: 78.0469
    )
  end
  
  def teardown
    Structure.where(jchem_id: [1001, 1002, 1003]).destroy_all
  end

  test "search returns empty result for blank structure" do
    result = ChemSearch.search("", nil, {})
    assert_equal 0, result.total_count
    assert_equal [], result.hits
  end

  test "search returns empty result for nil structure" do
    result = ChemSearch.search(nil, nil, {})
    assert_equal 0, result.total_count
    assert_equal [], result.hits
  end

  test "exact search finds matching structure" do
    # Skip this test if RDKit is not available
    skip "RDKit not available" unless rdkit_available?
    
    result = ChemSearch.search("CCO", nil, { search_type: :exact })
    
    assert result.total_count > 0, "Should find at least one exact match"
    
    # Check that the result has the expected format
    hit = result.hits.first
    assert_not_nil hit[:cd_id], "Hit should have cd_id"
    assert_not_nil hit[:cd_formula], "Hit should have cd_formula"
    assert_not_nil hit[:cd_molweight], "Hit should have cd_molweight"
    assert_not_nil hit[:cd_structure], "Hit should have cd_structure"
    assert_equal 1.0, hit[:similarity], "Exact match should have similarity of 1.0"
  end

  test "similarity search with high threshold" do
    # Skip this test if RDKit is not available
    skip "RDKit not available" unless rdkit_available?
    
    result = ChemSearch.search("CCO", nil, { 
      search_type: :similarity, 
      similarity: 0.8 
    })
    
    # Should find some similar structures
    assert result.total_count >= 0, "Similarity search should complete"
    
    # Check similarity scores are within expected range
    result.hits.each do |hit|
      assert hit[:similarity] >= 0.8, "All hits should meet similarity threshold"
      assert hit[:similarity] <= 1.0, "Similarity should not exceed 1.0"
    end
  end

  test "substructure search finds containing structures" do
    # Skip this test if RDKit is not available
    skip "RDKit not available" unless rdkit_available?
    
    # Search for structures containing a simple carbon chain
    result = ChemSearch.search("CC", nil, { search_type: :substructure })
    
    assert result.total_count >= 0, "Substructure search should complete"
    
    # All hits should have similarity of 1.0 for substructure matches
    result.hits.each do |hit|
      assert_equal 1.0, hit[:similarity], "Substructure matches should have similarity of 1.0"
    end
  end

  test "molecular weight filtering works" do
    # Skip this test if RDKit is not available
    skip "RDKit not available" unless rdkit_available?
    
    result = ChemSearch.search("CCO", nil, { 
      search_type: :exact,
      min_weight: 40,
      max_weight: 50
    })
    
    # Should only find structures within the weight range
    result.hits.each do |hit|
      weight = hit[:cd_molweight].to_f
      assert weight >= 40, "Molecular weight should be >= 40"
      assert weight <= 50, "Molecular weight should be <= 50"
    end
  end

  test "pagination works correctly" do
    # Skip this test if RDKit is not available
    skip "RDKit not available" unless rdkit_available?
    
    # Get all results first
    all_results = ChemSearch.search("C", nil, { search_type: :substructure })
    
    if all_results.total_count > 2
      # Test pagination
      page1 = ChemSearch.search("C", nil, { 
        search_type: :substructure,
        page: 1,
        per_page: 2
      })
      
      page2 = ChemSearch.search("C", nil, { 
        search_type: :substructure,
        page: 2,
        per_page: 2
      })
      
      assert_equal 2, page1.hits.length, "First page should have 2 results"
      assert page1.total_count >= 4, "Total count should be preserved"
      
      # Results should be different between pages
      page1_ids = page1.hits.map { |h| h[:cd_id] }
      page2_ids = page2.hits.map { |h| h[:cd_id] }
      assert_empty (page1_ids & page2_ids), "Pages should have different results"
    end
  end

  private

  def rdkit_available?
    # Simple check to see if RDKit is available
    begin
      script = <<~PYTHON
        import sys
        from rdkit import Chem
        print("RDKit available")
      PYTHON
      
      result = Jchem.run_rdkit(script, "")
      result.include?("RDKit available")
    rescue
      false
    end
  end
end
