ENV["RAILS_ENV"] = "test"
require File.expand_path("../../config/environment", __FILE__)
require "rails/test_help"
require 'minitest/rails'

# Uncomment for awesome colorful output
require "minitest/pride"

class ActiveSupport::TestCase
  ActiveRecord::Migration.check_pending!

  # Setup all fixtures in test/fixtures/*.(yml|csv) for all tests in alphabetical order.
  #
  # Note: You'll currently still have to declare fixtures explicitly in integration tests
  # -- they do not yet inherit this setting
  fixtures :all

  def encoded_auth_credentials
    username = ACCESS_CONFIG[:username]
    password = ACCESS_CONFIG[:password]

    ActionController::HttpAuthentication::Basic
      .encode_credentials(username, password)
  end

  def http_login
    request.env['HTTP_AUTHORIZATION'] = encoded_auth_credentials
  end
end
