#!/usr/bin/env python3

# Simple test to check RDKit MOL block parsing
import sys

try:
    from rdkit import Chem
    from rdkit.Chem import rdMolDescriptors
    print("✓ RDKit imported successfully")
except ImportError as e:
    print(f"✗ RDKit import failed: {e}")
    sys.exit(1)

# Test MOL block from user
user_mol = """
  MJ161017                      

 11 11  0  0  0  0  0  0  0  0999 V2000
 9999.978410000.2014    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
10000.6930 9999.7891    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
10001.409010000.2014    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
10002.1251 9999.7891    0.0000 O   0  0  0  0  0  0  0  0  0  0  0  0
10001.409010001.0268    0.0000 O   0  0  0  0  0  0  0  0  0  0  0  0
10000.6930 9998.9631    0.0000 N   0  0  0  0  0  0  0  0  0  0  0  0
 9998.590810000.2834    0.0000 N   0  0  0  0  0  0  0  0  0  0  0  0
 9997.9234 9999.7984    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
 9998.1783 9999.0138    0.0000 N   0  0  0  0  0  0  0  0  0  0  0  0
 9999.0033 9999.0138    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
 9999.2582 9999.7984    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0
  1  2  1  0  0  0  0
  2  3  1  0  0  0  0
  2  6  1  1  0  0  0
  3  4  1  0  0  0  0
  3  5  2  0  0  0  0
 10 11  2  0  0  0  0
  7  8  2  0  0  0  0
  7 11  1  0  0  0  0
  8  9  1  0  0  0  0
  9 10  1  0  0  0  0
  1 11  1  0  0  0  0
M  END
"""

print(f"MOL block length: {len(user_mol)}")
print(f"Contains V2000: {'V2000' in user_mol}")
print(f"Contains M  END: {'M  END' in user_mol}")
print(f"First 100 chars: {repr(user_mol[:100])}")

# Try parsing with sanitization
print("\n1. Trying with sanitization:")
try:
    mol = Chem.MolFromMolBlock(user_mol, sanitize=True)
    if mol:
        print(f"✓ Parsed successfully: {mol.GetNumAtoms()} atoms")
        
        # Try generating fingerprint
        fp = rdMolDescriptors.GetMorganFingerprintAsBitVect(mol, 2, nBits=2048)
        fp_bits = fp.ToBitString()
        print(f"✓ Generated fingerprint: {len(fp_bits)} bits")
        print(f"  First 50 bits: {fp_bits[:50]}")
    else:
        print("✗ Failed to parse MOL block")
except Exception as e:
    print(f"✗ Error with sanitization: {e}")

# Try parsing without sanitization
print("\n2. Trying without sanitization:")
try:
    mol = Chem.MolFromMolBlock(user_mol, sanitize=False)
    if mol:
        print(f"✓ Parsed successfully: {mol.GetNumAtoms()} atoms")
        
        # Try sanitizing manually
        try:
            Chem.SanitizeMol(mol)
            print("✓ Manual sanitization successful")
        except Exception as sanitize_error:
            print(f"✗ Manual sanitization failed: {sanitize_error}")
        
        # Try generating fingerprint anyway
        try:
            fp = rdMolDescriptors.GetMorganFingerprintAsBitVect(mol, 2, nBits=2048)
            fp_bits = fp.ToBitString()
            print(f"✓ Generated fingerprint: {len(fp_bits)} bits")
            print(f"  First 50 bits: {fp_bits[:50]}")
        except Exception as fp_error:
            print(f"✗ Fingerprint generation failed: {fp_error}")
    else:
        print("✗ Failed to parse MOL block")
except Exception as e:
    print(f"✗ Error without sanitization: {e}")

# Try with removeHs=False
print("\n3. Trying with removeHs=False:")
try:
    mol = Chem.MolFromMolBlock(user_mol, sanitize=True, removeHs=False)
    if mol:
        print(f"✓ Parsed successfully: {mol.GetNumAtoms()} atoms")
        
        # Try generating fingerprint
        fp = rdMolDescriptors.GetMorganFingerprintAsBitVect(mol, 2, nBits=2048)
        fp_bits = fp.ToBitString()
        print(f"✓ Generated fingerprint: {len(fp_bits)} bits")
        print(f"  First 50 bits: {fp_bits[:50]}")
    else:
        print("✗ Failed to parse MOL block")
except Exception as e:
    print(f"✗ Error with removeHs=False: {e}")

print("\nTest complete!")
