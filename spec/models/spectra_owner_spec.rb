require 'spec_helper'

describe "SpectraOwner" do
  it "should be valid for valid attributes" do
    SpectraOwner.make.should be_valid
  end
  
  it "shoud not be valid without reference database" do
    SpectraOwner.make(:database => nil).should_not be_valid
  end
  
  it "should not be valid withouy refernce database identifier" do
    SpectraOwner.make(:database_id => nil).should_not be_valid
  end
  
  it "should not be valid without a spectra" do
    SpectraOwner.make(:spectra => nil).should_not be_valid
  end
end