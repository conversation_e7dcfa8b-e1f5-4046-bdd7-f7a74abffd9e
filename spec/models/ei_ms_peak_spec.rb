require 'spec_helper'

describe EiMsPeak do
  it "is valid for valid attributes" do
    EiMsPeak.make.should be_valid
  end
  
  it "should only accept a number for charge mass" do
    EiMsPeak.make(:mass_charge => "test").should_not be_valid
  end
  
  it "should not be valid witout a charge mass" do
    EiMsPeak.make(:mass_charge => nil).should_not be_valid
  end
  
  it "should only be valid with a number for intensity" do
    EiMsPeak.make(:intensity => 1.2).should be_valid
    EiMsPeak.make(:intensity => "test").should_not be_valid
  end
  
end
