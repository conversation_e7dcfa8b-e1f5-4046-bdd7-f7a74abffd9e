require 'spec_helper'

describe "Atom" do
  it "should be valid for valid attributes" do
    Atom.make.should be_valid
  end
  
  it "should be invalid without a molecule" do
    Atom.make(:molecule => nil).should_not be_valid
  end
  
  it "should be valid only for valid atom symbols" do
    Atom.make(:symbol => "H").should be_valid
    Atom.make(:symbol => "C").should be_valid
    Atom.make(:symbol => "O").should be_valid
    Atom.make(:symbol => "N").should be_valid
    Atom.make(:symbol => "Z").should_not be_valid
    Atom.make(:symbol => "Zee").should_not be_valid
    pending "add more examples of valid atom symbols"
  end
  
  it "should be invalid without interger atom_number" do
    Atom.make(:atom_number => "H").should_not be_valid
    Atom.make(:atom_number => 1).should be_valid
    Atom.make(:atom_number => nil).should_not be_valid
  end
  
  
end