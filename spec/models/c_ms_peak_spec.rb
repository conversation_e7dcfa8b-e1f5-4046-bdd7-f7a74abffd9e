require 'spec_helper'

describe CMsPeak do
  it "is valid for valid attributes" do
    CMsPeak.make.should be_valid
  end
  
  it "should only accept a number for charge mass" do
    CMsPeak.make(:mass_charge => "test").should_not be_valid
  end
  
  it "should not be valid witout a charge mass" do
    CMsPeak.make(:mass_charge => nil).should_not be_valid
  end
  
  it "should only be valid with a number for intensity" do
    CMsPeak.make(:intensity => 1.2).should be_valid
    CMsPeak.make(:intensity => "test").should_not be_valid
  end
  
end