require "spec_helper"

describe NmrOneD do
  it "should be valid for valid attributes" do
    NmrOneD.make.should be_valid
  end
  
  it "should have at least one peak" do
    NmrOneD.make(:peaks => []).should_not be_valid
  end
  
  it "should have both sample temperature and units or neither" do
    NmrOneD.make(:sample_temperature => 1, :sample_temperature_units => nil).should_not be_valid
    NmrOneD.make(:sample_temperature => nil, :sample_temperature_units => "Celcius").should_not be_valid
    NmrOneD.make(:sample_temperature => 1, :sample_temperature_units => "Celcius").should be_valid
    NmrOneD.make(:sample_temperature => nil, :sample_temperature_units => nil).should be_valid
  end
  
  it "should have both sample mass and units or neither" do
    NmrOneD.make(:sample_mass => 1, :sample_mass_units => nil).should_not be_valid
    NmrOneD.make(:sample_mass => nil, :sample_mass_units => "grams").should_not be_valid
    NmrOneD.make(:sample_mass => 1, :sample_mass_units => "grams").should be_valid
    NmrOneD.make(:sample_mass => nil, :sample_mass_units => nil).should be_valid
    
  end
  
  it "should have both sample concentration and units or neither" do
    NmrOneD.make(:sample_concentration => 1, :sample_concentration_units => nil).should_not be_valid
    NmrOneD.make(:sample_concentration => nil, :sample_concentration_units => "Molar").should_not be_valid
    NmrOneD.make(:sample_concentration => 1, :sample_concentration_units => "Molar").should be_valid
    NmrOneD.make(:sample_concentration => nil, :sample_concentration_units => nil).should be_valid
    
  end
  
  it "should not be valid without a detection nucleus" do
    NmrOneD.make(:nucleus => nil).should_not be_valid
  end
  
  it "should be valid with valid detection nucluei" do
    NmrOneD.make(:nucleus => "1H").should be_valid
    NmrOneD.make(:nucleus => "13C").should be_valid
    NmrOneD.make(:nucleus => "QQ").should_not be_valid
  end
  it "should output valid cml" do
    NmrOneD.make.to_cml.should_not be_empty
    pending "test cml output"
  end
  
  it "should output valid nmr star" do
    NmrOneD.make.to_star.should_not be_empty
    pending "test star output"
  end
  
  it "should output valid nmrML" do
    NmrOneD.make.to_nmr_ml.should_not be_empty
    pending "test nmMl output"
  end
  
  describe "Search" do
    before(:each) do
      NmrOneD.make!(:search)
    end
    it "should find a result" do
      NmrOneD.search("1H", ["3.03","3.06", "3.09"]).should_not be_empty
    end
    
    it "should not find any result" do
      NmrOneD.search("1H", ["50","100", "90"]).should be_empty
    end
  end
end