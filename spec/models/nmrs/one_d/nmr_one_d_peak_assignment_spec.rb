require 'spec_helper'

describe NmrOneDPeakAssignment do
  it "be valid for valid attributes" do
    NmrOneDPeakAssignment.make.should be_valid
  end
  
  it "be valid for valid multiplicity" do
    NmrOneDPeakAssignment.make(:multiplicity => "Singlet").should be_valid
    NmrOneDPeakAssignment.make(:multiplicity => "Doublet").should be_valid
    NmrOneDPeakAssignment.make(:multiplicity => "Triblet").should be_valid
    NmrOneDPeakAssignment.make(:multiplicity => "Quartet").should be_valid
    NmrOneDPeakAssignment.make(:multiplicity => "Doublet of doublets").should be_valid
    NmrOneDPeakAssignment.make(:multiplicity => "Quintet").should be_valid
    NmrOneDPeakAssignment.make(:multiplicity => "Sextet").should be_valid
    NmrOneDPeakAssignment.make(:multiplicity => "Septet").should be_valid
    NmrOneDPeakAssignment.make(:multiplicity => "Not a valid multipicity").should_not be_valid
  end

  it "should not be valid without a peak" do
    assignment = NmrOneDPeakAssignment.make
    assignment.peaks = []
    assignment.should_not be_valid
  end
  
  it "should not be valid without any assigned atom" do
    NmrOneDPeakAssignment.make(:atom => nil, :atom_group => nil).should_not be_valid
  end
  
  it "should not be valid with both atom and atom_group assigned" do
    NmrOneDPeakAssignment.make(:atom => Atom.make, :atom_group => AtomGroup.make).should_not be_valid
  end
  context "with many atoms" do    
    it "should have many atoms" do
      nmr = NmrOneDPeakAssignment.make(:atom_group => AtomGroup.make, :atom => nil)
      nmr.should be_valid
      nmr.atoms.present?.should be_true
    end
    
  end
end