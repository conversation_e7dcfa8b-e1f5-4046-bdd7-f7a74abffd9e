require 'spec_helper'

describe NmrOneDPeak do
  it "should be valid for valid attributes" do
    NmrOneDPeak.make.should be_valid
  end
  
  it "should be valid for numerical chemical shift" do
    NmrOneDPeak.make(:chemical_shift => 1.2).should be_valid
    NmrOneDPeak.make(:chemical_shift => "cs").should_not be_valid
  end
  
  it "should be invalid without chemical shift" do
    NmrOneDPeak.make(:chemical_shift => nil).should_not be_valid
  end
  
  it "should be invalid with non number intensity" do
    NmrOneDPeak.make(:intensity => "i").should_not be_valid
  end
  
  it "should have numberical intensity" do
    NmrOneDPeak.make(:intensity => 1.2).should be_valid
  end
  
  it "should be valid with numerical chemical shift error" do
    NmrOneDPeak.make(:chemical_shift_error => 1.2).should be_valid
  end
  
  it "should be invalid with non numerical chemical shift error" do
    NmrOneDPeak.make(:chemical_shift_error => "e").should_not be_valid
  end

  it "should be valid with numerical intensity error" do
    NmrOneDPeak.make(:intensity_error => 1.2).should be_valid
  end
  
  it "should be invalid with non numerical intensity error" do
    NmrOneDPeak.make(:intensity_error => "e").should_not be_valid
  end
  
end