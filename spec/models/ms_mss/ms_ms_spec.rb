require 'spec_helper'

describe "MsMs" do
  it "should be valid for valid attributes" do
    MsMs.make.should be_valid
  end
  
  it "should have at least one peak" do
    MsMs.make(:peaks=>[]).should_not be_valid
  end
  
  it "should have a molecule" do
    MsMs.make(:molecule=>nil).should_not be_valid
  end
  
  it "should have an inchi key" do
    MsMs.make(:inchi_key=>nil).should_not be_valid
  end
  describe "Search" do
    before(:each) do
      MsMs.make!(:search)
    end
    it "should find results with valid query" do
      MsMs.search(100,[[125,100],[126,60],[74,10]]).should_not be_empty
    end
    
    it "should find no results with invalid query" do
      MsMs.search(1000,[[1000,100],[700,60]]).should be_empty
    end
  end
end