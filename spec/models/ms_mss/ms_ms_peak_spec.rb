require 'spec_helper'

describe MsMsPeak do
  it "should be valid for valid attributes" do
    MsMsPeak.make.should be_valid
  end
  
  it "should  always have a numberical charge_mass" do
    MsMsPeak.make(:mass_charge => nil).should_not be_valid
    MsMsPeak.make(:mass_charge => "e").should_not be_valid
    MsMsPeak.make(:mass_charge => 1.2).should be_valid
  end
  
  it "should always have a  numerical intensity" do
    MsMsPeak.make(:intensity => nil).should_not be_valid
    MsMsPeak.make(:intensity => "e").should_not be_valid
    MsMsPeak.make(:intensity => 1.2).should be_valid
  end
  
  
end