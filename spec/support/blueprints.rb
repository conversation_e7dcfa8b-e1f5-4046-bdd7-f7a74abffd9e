require 'machinist/active_record'

# Add your blueprints here.
#
# e.g.
#   Post.blueprint do
#     title { "Post #{sn}" }
#     body  { "Lorem ipsum..." }
#   end

SpectraOwner.blueprint do
  database {"HMDB"}
  database_id {"HMDB00001"}
  spectra {MsMs.make}
end

Reference.blueprint do
  
end

CMs.blueprint() do
  molecule {Molecule.make}
  chromatography_type {"Gas"}
  peaks {[CMsPeak.make]}
end

CMsPeak.blueprint do
  mass_charge { 1.0 }
end

Molecule.blueprint do
  
end
   
Atom.blueprint do
  molecule {Molecule.make}
  symbol {"C"}
  atom_number {sn}
end

AtomGroup.blueprint do
  atoms {[Atom.make,Atom.make]}
  molecule {Molecule.make}
end

NmrOneD.blueprint do
  inchi_key {"InChIKey=BRMWTNUJHUMWMS-LURJTMIESA-N"}
  peaks {[NmrOneDPeak.make]}
  nucleus {"1H"}
  molecule {Molecule.make}
end

NmrOneDPeak.blueprint do
  chemical_shift {1.2}
end

NmrOneDPeakAssignment.blueprint do
  atom {Atom.make}
  peaks {[NmrOneDPeak.make]}
  multiplicity {"Singlet (s)"}
end

NmrOneD.blueprint(:search) do
  searchable {true}
  
  peaks {   [NmrOneDPeak.make(:chemical_shift => 3.03, :unique_0 => 69, :unique_1 => 100, :unique_2 => 179, :unique_3 => 220, :unique_4 => 254),
            NmrOneDPeak.make(:chemical_shift => 3.06, :unique_0 => 42, :unique_1 => 74, :unique_2 => 165, :unique_3 => 201, :unique_4 => 295),
            NmrOneDPeak.make(:chemical_shift => 3.09, :unique_0 => 58, :unique_1 => 92, :unique_2 => 164, :unique_3 => 205, :unique_4 => 306)
    ]}
end

MsMsPeakAssignment.blueprint do
  smiles {"CN1C=NC(C[C@H](N)C(O)=O)=C1"}
  peak {MsMsPeak.make}
end

MsMsPeak.blueprint do
  mass_charge {1.2}
  intensity {100}
end

MsMs.blueprint do
  inchi_key {"InChIKey=BRMWTNUJHUMWMS-LURJTMIESA-N"}
  peaks {[MsMsPeak.make]}
  molecule {Molecule.make}
end

MsMs.blueprint(:search) do
  peaks {[MsMsPeak.make(:mass_charge => 125, :intensity => 100),MsMsPeak.make(:mass_charge => 126, :intensity => 50),MsMsPeak.make(:mass_charge => 70, :intensity => 10)]}
  molecule {Molecule.make(:moldb_mono_mass => 100)}
end