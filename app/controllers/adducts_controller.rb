class AdductsController < ApplicationController
  #include Concerns::CurationConcerns::Cacher

  before_action :find_structure, only: :show

  # GET /molecules/BLAH/curation.json
  def show
    @adducts_set = @structure.adducts || raise(ActiveRecord::RecordNotFound)
  end

  private

  def find_structure
    @database_registration = DatabaseRegistration.from_param!(params[:molecule_id])
    @structure = @database_registration.structure
  end
end
