class SearchController < ApplicationController
  skip_before_action :verify_authenticity_token

  def simple
    source_id = Source.find_by(name: params[:database]).try(:id)

    # Structure can come from raw structure or structure ID
    structure = if params[:database_id].present?
      DatabaseRegistration.exported.from_param!(params[:database_id]).structure.mol
    else
      params[:structure]
    end

    result = ChemSearch.search(structure, source_id, params)
    @hits = result.hits.inject({}) { |hash, hit| hash[hit[:cd_id]] = hit; hash }
    @total_count = result.total_count.to_s

    @database_registrations =
      DatabaseRegistration.exported.
                           joins(:structure).
                           where(structures: { jchem_id: @hits.keys }).
                           includes(:source).
                           order("FIND_IN_SET(structures.jchem_id,
                                 '#{@hits.keys.join(",")}')")
    if source_id.present?
      @database_registrations = @database_registrations.where(source_id: source_id)
    end

    respond_to do |format|
      format.html do
        @database_registrations = @database_registrations.page(params[:page])
      end
      format.json
    end
  end

  def detailed
    simple
  end
end
