class MsController < ApplicationController
  def search
    sort_order = MsSearch::ALLOWED_DIRECTIONS.include?(params[:d]) ? params[:d] : nil
    sort_col   = MsSearch::ALLOWED_COLUMNS.include?(params[:c]) ? params[:c] : nil

    query_masses = params[:query_masses].to_s.strip.split(/\s+/)
    adduct_types = params[:adduct_type].kind_of?(Array) ?
                     params[:adduct_type] :
                     params[:adduct_type].to_s.strip.split(/\s+/)

    @hits = \
      query_masses.map do |query_mass|
        MsSearch.search(query_mass,
                        params[:mode],
                        params[:tolerance],
                        params[:tolerance_units],
                        sort_order: sort_order,
                        sort_col: sort_col,
                        database: params[:database],
                        adduct_type: adduct_types)
      end

    respond_to do |format|
      format.json
      format.csv { send_data MsCsv.new(@hits).build }
    end
  end
end
