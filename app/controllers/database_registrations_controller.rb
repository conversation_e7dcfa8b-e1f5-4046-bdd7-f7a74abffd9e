class DatabaseRegistrationsController < ApplicationController
  DEFAULT_LIMIT = 100
  MAX_LIMIT = 1000

  include Concerns::DatabaseRegistrationConcerns::Cacher

  before_action :authenticate_api,
    only: [:update, :create, :destroy]
  skip_before_action :verify_authenticity_token,
    only: [:update, :create, :destroy]
  before_action :find_database_registration,
    only: [:show, :edit, :update, :destroy, :three_d]

  def index
    # If InChI<PERSON>ey given, just grab the registrations for that key
    if params[:key].to_s.inchikey?
      @database_registrations =
        Structure.find_by!(inchikey: params[:key]).
                  database_registrations.
                  exported.
                  page(1).
                  per(10.megabytes) # Unlimited
      render 'index' and return
    end

    respond_to do |format|
      format.html do
        @database_registrations =
          DatabaseRegistration.exported.includes(:structure, :source).page(params[:page])
      end
      format.json do
        limit = params[:limit].to_i
        limit = DEFAULT_LIMIT if limit <= 0
        limit = MAX_LIMIT if limit > MAX_LIMIT
        offset = params[:offset].to_i

        @database_registrations =
          if params[:database].present?
            Source.find_by!(name: params[:database]).database_registrations.exported
          else
            DatabaseRegistration.exported
          end

        @database_registrations = @database_registrations.limit(limit).offset(offset)
        @database_registrations = @database_registrations.includes(:source, structure: [:mol_format, :formats, :properties, :tags])
        @database_registrations = @database_registrations.order(:database_id)

        # Grab a batch of ids specified by a user
        if params[:ids].present?
          ids = params[:ids].split(",")
          @database_registrations = @database_registrations.where(database_id: ids)
        end
      end
    end
  end

  def batch
    ids = params[:ids].to_s.split(",")
    @structures = Structure.where(database_id: ids).includes(:formats, :properties)
    respond_to do |format|
      format.json { render :index }
    end
  end

  def show
    respond_to do |format|
      format.html
      format.json
      format.sdf do
        send_data @database_registration.to_sdf,
                  type: 'text/plain; charset=utf-8',
                  disposition: 'inline'
      end
      format.mol do
        send_data(@structure.mol, type: :mol, disposition: 'inline')
      end
      format.mol_3d do
        send_data(@structure.mol_3d, type: :mol_3d, disposition: 'inline')
      end
      format.pdb do
        send_data(@structure.pdb, type: :pdb, disposition: 'inline')
      end
    end
  end

  def three_d
    respond_to do |format|
      format.sdf do
        if @structure.try!(:curation).try!(:sdf_3d).present?
          send_data @structure.curation.sdf_3d,
                  type: 'text/plain; charset=utf-8',
                  disposition: 'inline'
        else
          raise ActiveRecord::RecordNotFound
        end
      end
    end
  end

  def create
    source = Source.find_or_create_by!(name: structure_params[:resource])

    # Grab the InChI Key to check if the structure already exists
    inchikey = ChemConvert.inchikey(structure_params[:structure])
    @structure = Structure.find_or_initialize_by(inchikey: inchikey)
    
    # Add original structure if it doesn't already exist
    if @structure.id == nil
      @structure.original_structure = structure_params[:structure]
      @structure.save!
    end

    @database_registration = \
      @structure.database_registrations.
                 build(database_id: structure_params[:database_id],
                       source_id: source.id)
    @database_registration.exported = structure_params[:exported]
    @database_registration.exported = true if structure_params[:exported].nil?

    respond_to do |format|
      if @database_registration.save
        format.json { render :show, status: :created,
                      location: molecule_path(@database_registration) }
      else
        format.json { render json: @database_registration.errors,
                      status: :unprocessable_entity }
      end
    end
  end

  def update
    # Grab the InChI Key to check if the structure already exists
    inchikey = ChemConvert.inchikey(structure_params[:structure])
    @structure = Structure.find_or_initialize_by(inchikey: inchikey)

    # Add original structure if it doesn't already exist
    if @structure.id == nil
      @structure.original_structure = structure_params[:structure]
      @structure.save!
    end

    @database_registration.structure = @structure
    @database_registration.exported = structure_params[:exported]
    @database_registration.exported = true if structure_params[:exported].nil?

    respond_to do |format|
      if @database_registration.save
        format.json { render :show, status: :ok,
                      location: molecule_path(@database_registration) }
      else
        format.json { render json: @database_registration.errors,
                      status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @database_registration.destroy
    respond_to do |format|
      format.json { head :ok }
    end
  end

  private

  def find_database_registration
    @database_registration = DatabaseRegistration.exported.from_param!(params[:id])
    @structure = @database_registration.structure
  end

  def structure_params
    params.permit(:structure, :database_id, :resource, :exported)
  end
end
