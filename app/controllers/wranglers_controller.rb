class WranglersController < ApplicationController
  before_action :authenticate_api, only: [:destroy]
  before_action :set_wrangler, only: [:show, :destroy]

  # GET /wranglers
  # GET /wranglers.json
  def index
    @wranglers = Wrangler.order("created_at DESC").page(params[:page])
  end

  # GET /wranglers/1
  # GET /wranglers/1.json
  def show
  end

  # GET /wranglers/new
  def new
    @wrangler = Wrangler.new
  end

  # POST /wranglers
  # POST /wranglers.json
  def create
    @wrangler = Wrangler.new(wrangler_params)

    respond_to do |format|
      if @wrangler.save && @wrangler.wrangle!
        format.html { redirect_to @wrangler, notice: 'Wrangler was successfully created.' }
        format.json { render :show, status: :created, location: @wrangler }
      else
        format.html { render :new }
        format.json { render json: @wrangler.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /wranglers/1
  # DELETE /wranglers/1.json
  def destroy
    @wrangler.destroy
    respond_to do |format|
      format.html { redirect_to wranglers_url, notice: 'Wrangler was successfully destroyed.' }
      format.json { head :no_content }
    end
  end

  private

  def set_wrangler
    @wrangler = Wrangler.find(params[:id])
  end

  # Never trust parameters from the scary internet, only allow the white list through.
  def wrangler_params
    params.require(:wrangler).permit(:name, :runner, :query)
  end
end
