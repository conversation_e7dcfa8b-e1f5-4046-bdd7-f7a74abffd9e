module Concerns
  module StructureConcerns
    module <PERSON><PERSON>
      extend ActiveSupport::Concern

      included do
        after_action :bust_cache, only: [:create, :update, :destroy, :refresh]
      end

      # This is used to force-clear the cache for this structure and all associated
      # database registrations.
      def refresh
        set_structure

        respond_to do |format|
          format.html { redirect_to @structure, notice: "<PERSON><PERSON> successfully cleared" }
          format.json { render :show, status: :ok, location: @structure  }
        end
      end

      private

      def bust_cache
        @structure.bust_cache
      end
    end
  end
end
