module Concerns
  module DatabaseRegistrationConcerns
    module <PERSON><PERSON>
      extend ActiveSupport::Concern

      CACHED_FORMATS = %i(json sdf mol pdb).freeze

      included do
        caches_action :show, if: -> { request.format.to_sym.in? self.class::CACHED_FORMATS }
        after_action :bust_cache, only: [:create, :update, :destroy, :refresh]
      end

      # This is used to force-clear the cache for this structure and all associated
      # database registrations.
      def refresh
        find_database_registration

        respond_to do |format|
          format.html { redirect_to molecule_path(@database_registration), notice: "Cache successfully cleared" }
          format.json { render :show, status: :ok, location: molecule_path(@database_registration) }
        end
      end

      private

      def bust_cache
        Rails.cache.delete_matched("*#{@database_registration.database_id}*")
      end
    end
  end
end
