class CurationsController < ApplicationController
  include Concerns::CurationConcerns::Cacher

  before_action :find_structure, only: :show

  # GET /molecules/BLAH/curation.json
  def show
    # @database_registration = DatabaseRegistration.from_param!(params[:molecule_id])
    # @structure = @database_registration.structure
    @curation = @structure.curation || raise(ActiveRecord::RecordNotFound)
    @combined_synonyms = @structure.get_combined_synonyms(JSON.parse(@structure.curation.result)['synonyms'])
      # Synonyms that combine those in the curations table and the synonyms table in MolDB.
    species = params[:species]
    @result =JSON.parse(@structure.curation.result)
    if @result["cs_descriptions"].present?
      @result["cs_description"] = @result["cs_descriptions"][species]
    elsif @result["cs_description"].present?
      @result["cs_description"] = @result["cs_description"]["name"]
    else
      @result["cs_description"] = "Description coming very soon!"
    end
    
    @result["structures"]["inchikey"].gsub!("InChIKey=",'')
    @result["structures"]["inchi"].gsub!("InChI=",'')
  end

  private

  def find_structure
    @database_registration = DatabaseRegistration.from_param!(params[:molecule_id])
    @structure = @database_registration.structure
  end
end
