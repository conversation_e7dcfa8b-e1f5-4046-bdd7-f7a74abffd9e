class StatisticsController < ApplicationController
  def index
    spectra_types = Spectrum.all_spectra_types
    database_name = params[:database]

    render json: [{}].to_json and return if database_name.blank?

    @stats = Rails.cache.fetch("statistics/#{params[:database]}", expires_in: 1.day) do
      db_stats = {}

      # Total # of spectra
      db_stats[:total_spectra] = spectra_types.map do |spectra|
        spectra.for_database(database_name).count
      end.reduce(0) { |sum, count| sum + count }

      # Total # of compounds with spectra
      db_stats[:total_compounds_with_spectra] =
        compounds_per_spectra_type(database_name, [:ms_ms,:c_ms,:ei_ms,:nmr_one_d,:nmr_two_d])

      db_stats[:total_compounds_with_nmr_spectra] =
        compounds_per_spectra_type(database_name, [:nmr_one_d,:nmr_two_d])

      db_stats[:total_compounds_with_one_d_nmr_spectra] =
        compounds_per_spectra_type(database_name, [:nmr_one_d])

      db_stats[:total_compounds_with_two_d_nmr_spectra] =
        compounds_per_spectra_type(database_name, [:nmr_two_d])

      db_stats[:total_compounds_with_ms_spectra] =
        compounds_per_spectra_type(database_name, [:ms_ms,:c_ms,:ei_ms])

      db_stats[:total_nmr_spectra] =
        total_spectra_of_type(database_name, [NmrOneD,NmrTwoD])

      db_stats[:total_ms_spectra] =
        total_spectra_of_type(database_name, [MsMs,CMs,EiMs])

      db_stats[:total_predicted_ms_ms_spectra] =
        total_spectra_of_type(database_name, [MsMs.predicted])

      db_stats[:total_experimental_ms_ms_spectra] =
        total_spectra_of_type(database_name, [MsMs.experimental])

      db_stats[:total_spectra_per_type] = spectra_types.map do |spectra|
        spectra.stats(database: database_name)
      end.flatten

      db_stats
    end

    render json: [@stats].to_json
  end

  def show
    @stats = []

    Spectrum.all_spectra_types.each do |spectrum_class|
      @stats.push(spectrum_class.stats(database: params[:id]))
    end
    @stats.flatten!

    render json: @stats
  end

  private

  def compounds_per_spectra_type(database_name, types)
    types.map do |type|
      Source.where(name: database_name).
        joins(database_registrations: { structure: [type] }).
        where("database_registrations.exported = 1").
        pluck(:database_id)
    end.flatten.uniq.count
  end

  def total_spectra_of_type(database_name, types)
    types.map do |klass|
      klass.for_database(database_name).count
    end.reduce(0, &:+)
  end
end
