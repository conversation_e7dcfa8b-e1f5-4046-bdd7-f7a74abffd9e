class NmrTwoDController < SpectraController
  def search
    query = params[:peaks].to_s.split("\n").map do |peak|
      peak.strip.split(/\s+/).map(&:to_f)
    end

    y_molecule =
      case params[:library].try(:downcase)
      when 'tocsy' then "1H"
      when 'hsqc' then "13C"
      else nil
      end

    # NOTE x_molecule is currently hard coded
    @hits = NmrTwoD.search("1H", y_molecule, query, params)
  end
end
