class MsMsController < SpectraController
  def search
    if params[:commit].present?
      query = []
      params[:peaks].to_s.split("\n").each do |peak|
        values = peak.to_s.strip.split(/\s+/)
        if values.length == 2
          query.push([BigDecimal.new(values[0]), BigDecimal.new(values[1])])
        end
      end
      @hits = MsMs.search(params[:parent_ion_mass], query, params)
    end
  end
end
