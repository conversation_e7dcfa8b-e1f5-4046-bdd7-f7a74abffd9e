class SpectraController < ApplicationController
  before_action :load_spectrum_type

  def index
    @spectra =
      if params.has_key? :inchikey
        Structure.from_inchikey!(params[:inchikey]).send(@spectrum_name)
      elsif params.has_key? :database_id
        @database_id = params[:database_id]
        @database_registration = DatabaseRegistration.exported.
          find_by(database_id: params[:database_id])
        raise(ActiveRecord::RecordNotFound) if @database_registration.nil?
        @database_registration.structure.send(@spectrum_name)
      elsif params.has_key? :id
        structure = Structure.from_param(params[:id])
        raise(ActiveRecord::RecordNotFound) if structure.nil?
        structure.send(@spectrum_name)
      else
        @spectrum_class
      end
    # @spectra = @spectra.includes(:references, :sops, :documents, :bmrb_records).
    #                     page(params[:page]).
    #                     per(params[:per_page])
    #
    @spectra = @spectra.includes(:references, :sops, :documents).
      page(params[:page]).
      per(params[:per_page]) unless params[:format] == 'json' # Added this because this was interfering with the HER model interface!

    respond_to do |format|
      format.html
      format.json
    end
  end

  def show
    @spectrum = @spectrum_class.find(params[:id])

    respond_to do |format|
      format.html
      format.json
    end
  end

  private

  def load_spectrum_type
    @spectrum_class = controller_name.classify.constantize
    @spectrum_name  = controller_name.classify.underscore
  end
end
