class ImagesController < ApplicationController
  Mime::Type.register "image/png", :png
  Mime::Type.register "image/svg+xml", :svg
  caches_action :thumb, :image, :peak
  include ChemConvert

  # GET /structures/1/thumb.png
  def thumb
    find_structure # Not before action so action caching doesn't execute
    respond_to do |format|
      format.png do
        if @structure.present?
          structure = check_3d(@structure.structure)
          image = ChemConvert.convert(structure, STRUCTURE_IMAGES[:structure_thumb])
          image_decoded = Base64.decode64(image)
          send_data(image_decoded, 
                type: 'image/png', 
                disposition: 'inline')
          #render content_type: 'image/png', text: image_decoded
        else
          file = File.open("#{Rails.root}/app/assets/images/no_structure_small.png")
          puts file
          #render text: file.read, content_type: 'image/png'
          send_data(file.read, 
                type: 'image/png', 
                disposition: 'inline')
          file.close
        end
      end
      format.svg do
        if @structure.present?
          structure = check_3d(@structure.structure)
          image = ChemConvert.convert(structure, STRUCTURE_IMAGES[:svg_thumb])
          send_data(Base64.decode64(image), 
                type: 'image/svg+xml', 
                disposition: 'inline')
          #render content_type: "image/svg+xml", text: Base64.decode64(image)
        else
          send_file("#{Rails.root}/app/assets/images/no_structure_small.svg", disposition: 'inline', type: "image/svg+xml")
        end
      end
    end
  end

  # GET /structures/1/image.png
  # GET /structures/1/image.svg
  def image
    find_structure # Not before action so action caching doesn't execute
    respond_to do |format|
      format.png do
        if @structure.present?
          structure = check_3d(@structure.structure)
          image = ChemConvert.convert(structure, STRUCTURE_IMAGES[:structure_full])
          send_data(Base64.decode64(image), 
          type: 'image/png', 
          disposition: 'inline') 
          #render content_type: 'image/png', text: Base64.decode64(image)
        else
          file = File.open("#{Rails.root}/app/assets/images/no_structure_large.png")
          send_data(file.read, 
            type: 'image/png', 
            disposition: 'inline')
         # render text: file.read, content_type: 'image/png'
          file.close
        end
      end
      format.svg do
        if @structure.present?
          structure = check_3d(@structure.structure)
          image = ChemConvert.convert(structure, STRUCTURE_IMAGES[:svg_full])
          send_data(Base64.decode64(image), 
            type: 'image/svg+xml', 
            disposition: 'inline') 
          #render content_type: "image/svg+xml", text: Base64.decode64(image)
        else
          send_file("#{Rails.root}/app/assets/images/no_structure_large.svg", disposition: 'inline', type: "image/svg+xml")
        end
      end
    end
  end

  def peak
    find_structure_from_assignment
    respond_to do |format|
      format.png do
        if @peak_assignment.present? && @peak_assignment.smiles.present?
          image = ChemConvert.convert(@peak_assignment.smiles, STRUCTURE_IMAGES[:structure_full])
          render content_type: 'image/png', text: Base64.decode64(image)
        else
          file = File.open("#{Rails.root}/app/assets/images/no_structure_large.png")
          render text: file.read, content_type: 'image/png'
          file.close
        end
      end
      format.svg do
        if @peak_assignment.present? && @peak_assignment.smiles.present?
          image = ChemConvert.convert(@peak_assignment.smiles, STRUCTURE_IMAGES[:svg_full])
          render content_type: "image/svg+xml", text: Base64.decode64(image)
        else
          send_file("#{Rails.root}/app/assets/images/no_structure_large.svg", disposition: 'inline', type: "image/svg+xml")
        end
      end
    end
  end

  private

  def find_structure
    @structure = Structure.from_param(params[:id])
  end

  def find_structure_from_assignment
    spectra_class = (params[:type]+"_peak_assignment")
    @peak_assignment = spectra_class.classify.constantize.from_param(params[:id])
    puts @peak_assignment.image_url
  end

  # Checks if structure is 3D and if it is, returns smiles string instead
  # @param [String] structure the original_structure, usually molfile
  # @return [String] smiles or 2d molfile
  def check_3d(structure)
    unless structure.include?('0.0000')
      return ChemConvert.inchi(structure)
    end
    structure
  end
end
