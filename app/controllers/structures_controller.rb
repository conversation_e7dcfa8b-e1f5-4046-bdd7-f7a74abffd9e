class StructuresController < ApplicationController
  include Concerns::StructureConcerns::Cacher

  before_action :authenticate_api,
    only: [:new, :edit, :create, :update, :destroy]
  skip_before_action :verify_authenticity_token,
    only: [:create, :update, :destroy]
  before_action :set_structure,
    only: [:show, :edit, :update, :destroy]

  # GET /structures
  # GET /structures.json
  def index
    @structures = Structure.page(params[:page])
  end

  # GET /structures/1
  # GET /structures/1.json
  def show
    respond_to do |format|
      format.html
      format.json
      format.sdf do
        send_data(@structure.sdf, type: :sdf, disposition: 'inline')
      end
      format.mol do
        send_data(@structure.mol, type: :mol, disposition: 'inline')
      end
      format.pdb do
        send_data(@structure.pdb, type: :pdb, disposition: 'inline')
      end
    end
  end

  # GET /structures/new
  def new
    @structure = Structure.new
  end

  # GET /structures/1/edit
  def edit
  end

  # POST /structures
  # POST /structures.json
  def create
    @structure = Structure.new(structure_params)

    respond_to do |format|
      if @structure.save
        format.html { redirect_to @structure, notice: 'Structure was successfully created.' }
        format.json { render :show, status: :created, location: @structure }
      else
        format.html { render :new }
        format.json { render json: @structure.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /structures/1
  # PATCH/PUT /structures/1.json
  def update
    respond_to do |format|
      if @structure.update(structure_params)
        format.html { redirect_to @structure, notice: 'Structure was successfully updated.' }
        format.json { render :show, status: :ok, location: @structure }
      else
        format.html { render :edit }
        format.json { render json: @structure.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /structures/1
  # DELETE /structures/1.json
  def destroy
    @structure.destroy
    respond_to do |format|
      format.html { redirect_to structures_url, notice: 'Structure was successfully destroyed.' }
      format.json { head :no_content }
    end
  end

  private

  def set_structure
    @structure = Structure.from_inchikey!(params[:id])
  end

  def structure_params
    params.permit(:original_structure)
  end
end
