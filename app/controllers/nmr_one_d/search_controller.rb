class NmrOneD::SearchController < ApplicationController
  skip_before_action :verify_authenticity_token, only: [:create]

  def index
    redirect_to action: :new
  end

  def new
    @search = NmrOneDSearch.new
  end

  def create
    query_spec= process_query_spec(params[:peaks])
    intensities = (params[:search_type] == 'pure_compound') ? process_intensities(query_spec, params[:intensities]) : []

    @search = NmrOneDSearch.new(query_spec: query_spec,
                                intensities: intensities,
                                search_type: params[:search_type],
                                tolerance: params[:cs_tolerance],
                                frequency: params[:frequency],
                                nucleus: params[:nucleus],
                                no_of_peaks: query_spec.length,
                                database: params[:database])

    @search.results

    respond_to do |format|
      format.html { redirect_to action: :show, id: @search.to_param }
      format.json { render json: {id: @search.to_param} }
    end
  end

  def show
    unless /^[a-zA-Z0-9]{32}$/.match(params[:id])
      raise ActiveRecord::RecordNotFound
    end

    @search = NmrOneDSearch.fetch_from_cache(params[:id])
    if @search.nil?
      raise ActiveRecord::RecordNotFound
    else
      respond_to do |format|
        format.html
        format.json { render json: as_json(@search) }
        format.csv  do
          headers["Content-Disposition"] = "attachment; filename=\"search_results.csv\""
          render text: results_as_csv(@search.results)
        end
      end
    end
  end

  private

  def as_json(search)
    {
      id: search.to_param,
      query_spec: search.query_spec,
      intensities: search.intensities,
      tolerance: search.tolerance,
      frequency: search.frequency.to_i,
      nucleus: search.nucleus,
      search_type: search.search_type,
      total_count: search.results.count
    }
  end

  def process_query_spec(query_spec)
    query_spec.split(/\n/).map(&:strip).map(&:to_f)
  end

  def process_intensities(query_spec, intensities)
    return [].fill(1.0, 0..(query_spec.length-1)) unless intensities.present?
    # split and convert intensities to a list of floats
    intensities = intensities.split(/\n/).map(&:strip).map(&:to_f)
    # make sure intensities are the right length (based on number of peaks)
    intensities.fill(1.0, intensities.length..(query_spec.length-1)).first(query_spec.length)
  end


  def results_as_csv(results)
    headers = %w[nmr_one_d_id match_ratio jaccard_index]
    CSV.generate do |csv|
      csv << headers
      results.select{|id,r| r[:jaccard_index] > 0}.each do |nmr_one_d_id,r|
        csv << [nmr_one_d_id, "#{r[:intersect_count]}/#{r[:union_count]}", r[:jaccard_index].round(3)]
      end
    end
  end
end

