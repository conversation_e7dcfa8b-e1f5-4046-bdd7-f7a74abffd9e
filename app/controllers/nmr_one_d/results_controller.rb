class NmrOneD::ResultsController < ApplicationController
  def index
    unless /^[a-zA-Z0-9]{32}$/.match(params[:search_id])
      raise ActiveRecord::RecordNotFound
    end

    @search = NmrOneDSearch.fetch_from_cache(params[:search_id])
    #
    unless @search.nil?
      @results = paginate(@search.results)
      respond_to do |format|
        format.json { render json: results_as_json(@results) }
      end
    else
      raise ActiveRecord::RecordNotFound
    end
  end

  private

  def paginate(results)
    unless params.has_key? :page
      results
    else
      Kaminari.paginate_array(results).
        page(params[:page]).per(params[:per_page])
    end
  end

  def process_query_spec(text)
    text.split(/\n/).map(&:strip).map(&:to_f)
  end

  def results_as_json(results)
    results.select{|id,r| (r[:dot_score] || r[:jaccard_index]) > 0}.map do |nmr_one_d_id,r|
      data = r.dup
      # data.delete(:intersect_count)
      # data.delete(:union_count)
      data[:nmr_one_d_id] = nmr_one_d_id
      if r[:dot_score].present?
        data[:dot_score] = r[:dot_score].round(3)
      elsif r[:jaccard_index].present?
        data[:jaccard_index] = r[:jaccard_index].round(3)
        data[:match_ratio] = "#{r[:intersect_count]}/#{r[:union_count]}"
      end
      data
    end
  end

  #def results_as_csv(results)
    #headers = %w[nmr_one_d_id match_ratio jaccard_index]
    #CSV.generate do |csv|
      #csv << headers
      #results.select{|id,r| r[:jaccard_index] > 0}.each do |nmr_one_d_id,r|
        #csv << [nmr_one_d_id, "#{r[:intersect_count]}/#{r[:union_count]}", r[:jaccard_index].round(3)]
      #end
    #end
  #end
end

