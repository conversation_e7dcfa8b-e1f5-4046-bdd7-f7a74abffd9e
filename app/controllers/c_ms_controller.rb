class CMsController < SpectraController
  def search
    if params[:commit].present?
      query_peaks = [] # tuples of m/z and intensity
      params[:peaks].to_s.each_line do |line|
        if line =~ /^\s*(\d+(?:\.\d+)?)\s+(\d+(?:\.\d+)?)/
          mz = $1
          i = $2
        elsif line =~ /^\s*(\d+(?:\.\d+)?)/
          mz = $1
          i = 100.0
            # Default if user put no intensity value. Should correspond to what we tell
            # them the default is on the GC-MS search page.
        end
        query_peaks.push([mz.to_f, i.to_f])
      end
      @hits = CMs.search(params[:retention], params[:retention_type], query_peaks, params)
    end
  end
end
