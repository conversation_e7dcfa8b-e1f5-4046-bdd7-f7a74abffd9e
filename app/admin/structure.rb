ActiveAdmin.register Structure do
  filter :inchikey

  index do
    selectable_column
    column :inchikey
    column :created_at
    column :updated_at
    column :wrangled_on
    column("Structure") do |structure|
      image_tag(thumb_molecule_path(structure, format: :png), class: 'thumbnail')
    end
    actions
  end


# See permitted parameters documentation:
# https://github.com/activeadmin/activeadmin/blob/master/docs/2-resource-customization.md#setting-up-strong-parameters
#
# permit_params :list, :of, :attributes, :on, :model
#
# or
#
# permit_params do
#   permitted = [:permitted, :attributes]
#   permitted << :other if resource.something?
#   permitted
# end


end
