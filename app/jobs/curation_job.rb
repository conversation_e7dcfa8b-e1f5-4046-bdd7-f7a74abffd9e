class CurationJob < ApplicationJob
  queue_as :default

  def perform(input, force=false)
    #return if (structure.curation.present? && !force) or !rerun
    #curation =
     # if structure.curation.present?
      #  structure.curation
      #else
      #  Curation.new(structure: structure)
      #end
      puts input
      if (input.is_a?(String) or input.is_a?(Integer)) 
        structure_id = input
        structure = Structure.find_by(id: structure_id)
        curation =
          if structure.curation.present?
            structure.curation
          else
            Curation.new(structure: structure)
          end
          wrangler = DataWrangler::Annotate.compound(inchikey: structure.inchikey)
          wrangler.species.delete_if{|species| wrangler.cs_descriptions[species.taxonomy_id].nil?}
          wrangler.identifiers.iupac_name =  wrangler.identifiers.iupac_name.encode!(Encoding.find('UTF-8'), {invalid: :replace, undef: :replace, replace: ''}) if wrangler.identifiers.iupac_name.present?
          wrangler.identifiers.name.encode!(Encoding.find('UTF-8'), {invalid: :replace, undef: :replace, replace: ''})
          puts wrangler.identifiers.name
          curation.result = wrangler.to_json
          curation.save!
          curation.bust_cache
          structure.touch(:wrangled_on)
      else
        Parallel.each(input, in_threads: input.length) { |structure|
          begin
            thread_curation =
              if structure.curation.present?
               structure.curation
              else
                Curation.new(structure: structure)
              end
           # puts curation.id
            compound = DataWrangler::Annotate.compound(inchikey: structure.inchikey)
            compound.species.delete_if{|species| compound.cs_descriptions[species.taxonomy_id].nil?}
            #throw Exception if wrangler.to_json.nil?
            #puts "Wrangler ByteSize (KB): #{(wrangler.to_s.bytesize / 1000).round(3)}"
            compound.identifiers.iupac_name.encode!(Encoding.find('UTF-8'), {invalid: :replace, undef: :replace, replace: ''}) if compound.identifiers.iupac_name.present?
            compound.identifiers.name.encode!(Encoding.find('UTF-8'), {invalid: :replace, undef: :replace, replace: ''})
            compound.cs_descriptions.each do |key,desc|
              compound.cs_descriptions[key] = desc.encode(Encoding.find('UTF-8'), {invalid: :replace, undef: :replace, replace: ''})
            end
            thread_curation.result = compound.to_json
            saved = false
            tries = 0
            while (!saved && tries < 5)
              begin
                thread_curation.result = compound.to_json
                thread_curation.save!
                thread_curation.bust_cache
                structure.touch(:wrangled_on)
                saved = true
              rescue
                tries += 1
                sleep(5)
              end
            end
            #ApplicationRecord.clear_active_connections!
            raise Exception.new("NAME: #{compound.identifiers.name} NOT SAVED") if !saved
            $stdout.puts "NAME: #{compound.identifiers.name}"
            $stdout.puts "ID: #{structure.id}"
            $stdout.puts "Human Description: #{compound.cs_descriptions["1"]}"
            $stdout.puts "NO ERROR *#{structure.inchikey}*"
          rescue => e
                  $stdout.puts "ERROR *#{structure.inchikey}*"
                  puts e
                  puts e.message
                  #puts e.backtrace
                  $stderr.puts "WARNING Datawrangler did not work on this compound #{structure.inchikey}"
          end
          thread_curation = nil
          compound = nil
        }
    end
  end

end