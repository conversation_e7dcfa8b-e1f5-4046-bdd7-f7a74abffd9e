class StructureJob < ApplicationJob
  queue_as :default

  def perform(structure, force=false)
    curation = nil

    if structure.curation.present?
      structure.curation.json_to_class
      return if !structure.curation.structures.empty? && !force

      curation = structure.curation

      wrangler = DataWrangler::Annotate::Compound.by_inchikey(structure.inchikey)
      curation.structures = JSON.parse(wrangler.structures.to_json)
    else
      curation = Curation.new(structure: structure)
      wrangler = DataWrangler::Annotate.compound(inchikey: structure.inchikey)
      curation.result = wrangler.to_json
    end
    # we also need to update adducts for updated structures (it will update adducts only if mass has changed)
    Adduct.create_or_update_for_structure(structure)
    curation.save!
  end
end