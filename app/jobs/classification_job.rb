class ClassificationJob < ApplicationJob
  queue_as :default
  def perform(structure, force=false)
    curation = nil
    if structure.curation.present?
      structure.curation.json_to_class
      return if !structure.curation.classifications.empty? && !force
      curation = structure.curation
      wrangler = DataWrangler::Annotate::Compound.only_classify(structure.smiles)
      curation.classifications = JSON.parse(wrangler.classifications.to_json)
    else
      curation = Curation.new(structure: structure)
      wrangler = DataWrangler::Annotate.compound(inchikey: structure.inchikey)
      curation.result = wrangler.to_json
    end
    curation.save!
    structure.bust_cache
  end
end