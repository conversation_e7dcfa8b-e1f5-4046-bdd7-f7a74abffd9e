class UpdateCsDescriptionJob < ApplicationJob
  queue_as :default

  def perform(structure)
    return if !structure.curation.present?
    structure.curation.json_to_class
    curation = structure.curation if structure.curation.present? 
    wrangler = DataWrangler::Annotate::Compound.by_inchikey(structure.inchikey)
    curation.cs_descriptions = wrangler.cs_descriptions
    return if curation.cs_descriptions.nil?
    curation.save!
    # puts curation.cs_description
  end
end