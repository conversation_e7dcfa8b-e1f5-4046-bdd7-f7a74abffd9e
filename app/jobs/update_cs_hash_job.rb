class UpdateCsHashJob < ApplicationJob
  queue_as :default

  def perform(structure)
    return if !structure.curation.present?
    structure.curation.json_to_class
    curation = structure.curation if structure.curation.present? 
    wrangler = DataWrangler::Annotate::Compound.by_inchikey(structure.inchikey)
    curation.cs_hash = wrangler.cs_hash
    return if curation.cs_description.nil?
    curation.save!
    # puts curation.cs_description
  end
end