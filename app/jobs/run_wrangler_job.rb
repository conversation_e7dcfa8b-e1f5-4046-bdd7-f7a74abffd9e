class RunWranglerJob < ApplicationJob
  queue_as :default

  def perform(wrangler_curation)
    curation =
      if wc = WranglerCuration.curated.find_by(query: wrangler_curation.query)
        wc.curation # Already searched, no need to curate!
      elsif wrangler_curation.query =~ /\t/ # Includes key and name
        inchikey, name = wrangler_curation.query.split(/\t/)
        curate_by_inchikey_and_name(inchikey.strip, name.strip)
      elsif wrangler_curation.query.sub(/InChIKey=/, '').inchikey?
        curate_by_inchikey(wrangler_curation.query)
      else
        curate_by_name(wrangler_curation.query)
      end

    wrangler_curation.curation = curation
    wrangler_curation.save
  end

  private

  def curate_by_inchikey_and_name(inchikey, name)
    if structure = Structure.from_inchikey(inchikey) and structure.curation.present?
      return structure.curation
    end
 
    wrangler = DataWrangler::Annotate.compound(inchikey: inchikey, name: name)
    curation = Curation.build_from_wrangler(wrangler) and curation.save!
    curation
  end

  def curate_by_inchikey(inchikey)
    if structure = Structure.from_inchikey(inchikey) and structure.curation.present?
      return structure.curation
    end
    wrangler = DataWrangler::Annotate.compound(inchikey: inchikey)
    curation = Curation.build_from_wrangler(wrangler) and curation.save!
    curation
  end

  def curate_by_name(name)
    wrangler = DataWrangler::Annotate.compound(name: name)
    if structure = Structure.from_inchikey(wrangler.structures.try(:inchikey)) and
        structure.curation.present?
      return structure.curation
    end
    curation = Curation.build_from_wrangler(wrangler) and curation.save!
    curation
  end
end
