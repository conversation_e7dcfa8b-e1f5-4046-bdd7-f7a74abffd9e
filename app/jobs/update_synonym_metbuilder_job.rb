class UpdateSynonymMetbuilderJob < ApplicationJob
  queue_as :default

  def perform(structure)
    return if !structure.curation.present?
    structure.curation.json_to_class

    curation = structure.curation 
    wrangler = DataWrangler::Annotate::Compound.by_inchikey(structure.inchikey)
    return if !wrangler.synonyms.present?
    synonyms = wrangler.synonyms
    if curation.synonyms.nil?
      curation.synonyms = wrangler.synonyms
      curation.save!
      puts "updated #{structure.inchikey}"
    else
      for i in 0..((synonyms.length)-1)
        if synonyms[i].source == "MetBuilder"
          curation.synonyms = curation.synonyms.push("name" => synonyms[i].name, "source" => synonyms[i].source, "kind" => synonyms[i].kind)
          curation.save!
        end
      end
    puts "updated #{structure.inchikey}"
    end
  end



end