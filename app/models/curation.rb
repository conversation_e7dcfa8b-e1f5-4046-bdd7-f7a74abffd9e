class Curation < ApplicationRecord
  belongs_to :structure, optional: true

  has_many :wrangler_curations, dependent: :delete_all
  has_many :wranglers, through: :wrangler_curations

  validates :structure_id,
    presence: true,
    uniqueness: true

  after_save :save_synonyms
  after_save :bust_cache

  def as_json
   JSON.parse(self.result)
  end

  def bust_cache
    database_ids = self.structure.database_registrations.pluck(:database_id)
    database_ids.each do |db_id|
      Rails.cache.delete_matched("#{db_id}/curation.json")
    end
  end

  def self.build_from_wrangler(wrangler)
    curation = self.new(result: wrangler.to_json)
    curation.structure =
      if structure = Structure.from_inchikey(wrangler.structures.try(:inchikey))
        structure
      elsif wrangler.structures.inchi.present?
        Structure.create!(structure: wrangler.structures.inchi)
      end
    curation
  end

  def json_to_class
    self.as_json.keys.each do |key|
      self.class.send(:define_method, key) { return self.as_json[key] }
      self.class.send(:define_method, "#{key}=") do |value|
        result = self.as_json
        result[key] = value
        self.result = result.to_json
      end
    end
  end

  def sdf_3d
    self.json_to_class
    self.structures['sdf_3d']
  end

  def save_synonyms
    result_synonyms = self.as_json["synonyms"]
    return if result_synonyms.nil?
    result_synonyms.each do |result_syn|
      synonym = Synonym.find_or_create_by(name: result_syn["name"], structure_id: self.structure_id)
      if !source = Source.find_by(human_name: result_syn["source"])
        source = Source.new
        source.name = result_syn["source"]
        source.human_name = result_syn["source"]
        source.save!
      end
      synonym_source = SynonymSource.find_or_create_by(synonym_id: synonym.id, source_id: source.id)
    
      # puts synonym.name
      # puts source.human_name
    end
  end

end