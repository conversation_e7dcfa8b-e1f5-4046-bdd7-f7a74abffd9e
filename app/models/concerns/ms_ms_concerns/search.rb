module MsMsConcerns
  module Search
    extend ActiveSupport::Concern

    class_methods do
      # query_peaks = [[mass_charge,intensity],...]
      def search(parent_ion_mass, query_peaks = [], options = {})
        return [] if query_peaks.blank? || options[:database].blank?

        parent_tolerance       = BigDecimal.new((options[:parent_ion_mass_tolerance].to_s or 0.1))
        mz_tolerance           = BigDecimal.new((options[:mass_charge_tolerance].to_s or 0.5))
        parent_tolerance_units = options[:parent_ion_mass_tolerance_units]
        mz_tolerance_units     = options[:mass_charge_tolerance_units]

        # I'm not worrying too much about making this nice right now, since I know
        # <PERSON> is planning on re-writing this. -- Craigs
        parent_tolerance_units = 'Da' unless ['Da', 'ppm'].include?(parent_tolerance_units)
        mz_tolerance_units = 'Da' unless ['Da', 'ppm'].include?(mz_tolerance_units)

        parent_diff = self.tolerance_difference(parent_tolerance,
                                                parent_tolerance_units,
                                                parent_ion_mass)
        min = BigDecimal.new(parent_ion_mass) - parent_diff
        max = BigDecimal.new(parent_ion_mass) + parent_diff

        spectra = MsMs.for_database(options[:database]).
          select('ms_ms.*').
          where("structures.exact_mass BETWEEN ? AND ?", min, max).
          includes(:structure, :exported_peaks).
          references(:structures)

        if options[:collision_energy_level].present?
          spectra = spectra.where(collision_energy_level: options[:collision_energy_level])
        end
        if options[:collision_energy_voltage].present?
          spectra = spectra.where(collision_energy_voltage: options[:collision_energy_voltage])
        end
        if options[:ionization_mode].present?
          spectra = spectra.where(ionization_mode: options[:ionization_mode])
        end

        # By default don't include predicted spectra
        if !options[:predicted].present? || options[:predicted] == 0
          spectra = spectra.where(predicted: false)
        end

        result = spectra.map { |x| [x] }

        if query_peaks.empty?
          result.each do |r|
            r.push("N/A")
            r.push("N/A")
            r.push("N/A")
            r.push(0)
          end
        else
          result.each do |r|
            r.push(self.fit(query_peaks, r[0].peaks_array, mz_tolerance, mz_tolerance_units))
            r.push(self.r_fit(query_peaks, r[0].peaks_array, mz_tolerance, mz_tolerance_units))
            r.push(self.purity(query_peaks, r[0].peaks_array, mz_tolerance, mz_tolerance_units))
            r.push((r[1]+r[2]+r[3])*100)
          end
        end

        # Transform the result into objects
        result.map do |r|
          MsMsResult.new(*r)
        end.sort_by { |r| 
          # Sort by average score of fit, r_fit, and purity
          fit = r.fit.to_f.nan? ? 0.0 : r.fit.to_f
          r_fit = r.r_fit.to_f.nan? ? 0.0 : r.r_fit.to_f
          purity = r.purity.to_f.nan? ? 0.0 : r.purity.to_f
          (fit + r_fit + purity) / 3
        }.reverse
      end
    end
  end
end
