module Spectrum
  extend ActiveSupport::Concern

  included do

    belongs_to :structure
    has_many :references, as: :spectra, dependent: :destroy
    has_many :documents, dependent: :destroy, as: :spectra
    has_many :spectra_sops, dependent: :destroy, as: :spectra
    has_many :sops, through: :spectra_sops
    has_many :database_registrations, through: :structure
    has_many :sources, through: :database_registrations
    #has_many :bmrb_records, through: :structure

    validates :peaks, length: { minimum: 1 }

    delegate :inchikey, to: :structure
  end

  # scope :exported, joins(:database_registrations).where(exported: true)

  def inchi_key
    self.inchikey
  end

  def all_documents
    #documents + sops + bmrb_records
    documents + sops
  end

  def to_export_xml
    options = {
      skip_types: true,
      include: {
        references: {
          skip_types: true,
          except: [:updated_at, :created_at]
        }
      }
    }
    attributes.to_xml(options) do |xml|
      yield xml
    end
  end

  module ClassMethods
    # Scopes the spectra to only spectra registered
    # for the given database. If the database parameter
    # is nil, then it will use all spectra
    def for_database(database)
      if database.present?
        source = Source.find_by!(name: database)
        self.joins(:database_registrations).
          merge(DatabaseRegistration.exported.where(source_id: source.id)).
          distinct
      else
        self.joins(:database_registrations).
          merge(DatabaseRegistration.exported)
      end
    end
  end

  # Module methods
  def self.all_spectra_types
    [EiMs, CMs, MsMs, NmrOneD, NmrTwoD]
  end

  def self.eims_spectra
    [EiMs]
  end

  def self.msms_spectra
    [MsMs]
  end

  def self.cms_spectra
    [CMs]
  end

  def self.nmr_spectra
    [NmrOneD, NmrTwoD]
  end
end
