module Structures
  module Properties
    extend ActiveSupport::Concern
    include ChemClient
    include AlogpsGrabber
    # Update the complete external calculated properties if
    # the structure has changed
    def update_properties!
      fields = STRUCTURE_PROPERTIES.keys + %w(solubility logp logs)
      self.properties.where(name: fields).delete_all
      self.update_column(:properties_updated_at, nil)

      begin
        if result = AlogpsGrabber.prediction_from_smiles(self.smiles)
          self.properties.create!(name: 'solubility', source: 'alogps', value: "#{result.solubility} #{result.solubility_units}") if result.solubility.present?
          self.properties.create!(name: 'logp', source: 'alogps', value: result.logp) if result.logp.present?
          self.properties.create!(name: 'logs', source: 'alogps', value: result.logs) if result.logs.present?
        end
      rescue Exception => e
        # TODO: Flag this entry to recalculate ALOGPS fields
        logger.error "Could not grab ALOGPS web service results, service may be down"
      end

      structure = ChemClient.get_structure(self.jchem_id,
                                           additional_fields: STRUCTURE_PROPERTIES)
      STRUCTURE_PROPERTIES.each do |name, term|
        val = structure[name.to_sym]
        if term =~ /(<=|>=|\&\&)/ # JChem returns float for boolean terms
          val = val.to_i unless val.nil?
        end
        self.properties.create!(name: name, source: 'jchem', value: val) if val.present?
      end

      self.touch(:properties_updated_at)
    end
  end
end
