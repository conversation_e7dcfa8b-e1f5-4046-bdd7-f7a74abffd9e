module Structures
  module Formats
    extend ActiveSupport::Concern

    # Update the external converted formats if the structure has changed
    def update_formats!
      self.formats.clear

      STRUCTURE_FORMATS.each do |name, format|
        begin
          value = ChemConvert.convert(self.structure, format)
          self.formats.create!(name: name, value: value) if value.present?
        rescue Exception => e
          logger.error "Could not grab chemical format"
        end
      end
    end
  end
end
