class CMsPeak < ApplicationRecord
  belongs_to :c_ms, counter_cache: :peak_counter, optional: true
  has_many :peak_assignments, class_name: "CMsPeakAssignment", inverse_of: :peak, dependent: :destroy

  validates :mass_charge, presence: true,  numericality: true
  validates :intensity, numericality: true, allow_nil: true

  # scope :exported, -> { where(export: true) }
  scope :annotated, -> { joins(:peak_assignments).where('c_ms_peak_assignments.c_ms_peak_id is not NULL') }
end