class CMs < ApplicationRecord
  include Spectrum

  has_many :peaks,
    dependent: :destroy,
    class_name: "CMsPeak",
    source: :c_ms_peaks

  validates :retention_index, numericality: true, allow_nil: true
  validates :retention_time, numericality: true, allow_nil: true
  validates :chromatography_type, presence: true

  scope :predicted, -> { where(predicted: true) }
  scope :experimental, -> { where(predicted: false) }

  def self.stats(options = {})
    counts = CMs.for_database(options[:database]).
      group(:instrument_type, :chromatography_type).
      count

    counts.map do |(instrument, chromatography), value|
      name = "#{chromatography}-MS Spectrum"
      name = "#{instrument} #{name}" if instrument.present?
      Statistic.new(name: name, value: value)
    end
  end

  def splash
    return if splash_key.nil?
    SpectrumHash.from_splash_string splash_key
  end

  def regenerate_splash_key
    peaks = self.peaks.map do |p|
      [p.mass_charge.to_f,p.intensity.to_f]
    end
    self.splash_key = SpectrumHash.from_peaks(peaks)
  end

  def to_export_xml
    super do |xml|
      # Only add the exported peaks to the xml
      peaks.to_xml(builder: xml, skip_types: true, skip_instruct: true)
    end
  end

  # query_peaks: array of tuples of m/z and intensity
  def self.search(retention, type, query_peaks = [], options = {})
    return [] if query_peaks.blank? || options[:database].blank?

    options[:mass_charge_tolerence] ||= 0.1
    options[:retention_tolerance] ||= 1

    spectra = CMs.for_database(options[:database])

    if options[:predicted].blank? || options[:predicted] == 0
      spectra = spectra.where(predicted: false)
    end
    library_peaks = CMsPeak.where(c_ms_id: spectra.ids).
      pluck(:c_ms_id, :mass_charge, :intensity).
      group_by { |a| a[0] }

    # Filter out peaks < 5% of the intensity of the highest peak.
    max_query_peak_height = query_peaks.transpose.first.max
    max_query_peak_height = 0.0 if max_query_peak_height < 0.0

    query_threshold = 0.05 * max_query_peak_height

    query_peaks_filtered = query_peaks.select do |query_peak|
      query_peak[0].to_f >= query_threshold
    end

    cms_results = spectra.includes(:structure).map do |spectrum|
      num_lib_peaks_after_filtering, peak_matches, correl = \
        spectrum.peak_matches_and_pearson_correlation(query_peaks_filtered,
                                                      library_peaks[spectrum.id],
                                                      options[:mass_charge_tolerence])
      next nil if peak_matches == 0
      query_matches = peak_matches.to_f/query_peaks_filtered.length
      library_matches = peak_matches.to_f/num_lib_peaks_after_filtering.to_f
      next nil if query_matches + library_matches < 0.6
      correl = 0.0 if correl.nan?

      CMsResult.new(spectrum, query_matches, library_matches, correl)
    end

    cms_results.compact.sort.reverse
  end

  # Compare query peaks to library spectrum peaks.
  # Calculate and return
  #   (1) the number of matching peaks based on m/z values;
  #   (2) the Pearson correlation coefficient between the two spectra.
  #
  # To do (2):
  # Let Q be the set of query peaks, L be the set of peaks in one library spectrum.
  # 1. Match peaks based on m/z.
  # 2. For all unmatched peaks in Q and L, add pseudo-peaks to Q and L with 0 intensity so
  #    that all peaks in each set have a paired 'peak'.
  # 3. Calculate the Pearson correlation coefficient between the intensity values of Q and L.
  #
  # query_peaks: array of tuples of m/z and intensity.
  # tolerance: the "Peak Tolerance" value for m/z based peak matching.
  def peak_matches_and_pearson_correlation(query_peaks, library_peaks, tolerance)
    query_peaks_mod = []
    lib_peaks_mod = []

    # initialize list keeping track of which query peaks have match found
    query_peaks_matched = []
    for i in 0..(query_peaks.length - 1) do
      query_peaks_matched[i] = false
    end

    # Filter out peaks < 5% of the intensity of the highest peak.
    max_lib_peak_height = library_peaks.map { |qp| qp[2].to_f }.max
    max_lib_peak_height = 0.0 if max_lib_peak_height < 0.0
    lib_threshold = 0.05 * max_lib_peak_height

    # Find matching peaks based on m/z values within the peak tolerance range.
    num_lib_peaks_after_filtering = 0
    num_matches = 0
    library_peaks.each do |lib_peak|
      lib_intensity = lib_peak[2].to_f
      lib_mass_charge = lib_peak[1].to_f

      next if lib_intensity < lib_threshold
      num_lib_peaks_after_filtering += 1

      match = false
      query_peaks.each_with_index do |query_peak,index|
        next if query_peaks_matched[index] # do not match a query peak with more than one library peak
        x = lib_mass_charge - query_peak[0]
        if x.abs < tolerance.to_f
          num_matches += 1
          query_peaks_matched[index] = true
          query_peaks_mod.push(query_peak)
          lib_peaks_mod.push([lib_mass_charge, lib_intensity])
          match = true
          break
        end
      end

      if !match
        # Library peak lacks matching query peak.
        # Add pseudo-peak to query set to pair with the unmatched library peak.
        psuedo_peak = [lib_mass_charge, 0.0]
        query_peaks_mod.push(psuedo_peak)
        lib_peaks_mod.push([lib_mass_charge, lib_intensity])
      end
    end

    if num_matches == 0
      return num_matches, 0.0
    end

    # Add pseudo-peaks to library set for each unmatched query peak.
    query_peaks.each_with_index do |query_peak,index|
      if !query_peaks_matched[index]
        psuedo_peak = [query_peak[0].to_f, 0.0]
        query_peaks_mod.push(query_peak)
        lib_peaks_mod.push(psuedo_peak)
      end
    end

    # Calculate sample correlation coefficient.
    # This algorithm is adapted from the algorithm that used to be here in Nov 2006:
    # http://en.wikipedia.org/wiki/Correlation_coefficient#Computing_correlation_accurately_in_a_single_pass
    # (One can probably can find it in the Wikipedia page history).
    # This algorithm is supposed to have good numerical stability according to the Wikipedia page.
    # -- David Arndt, Oct 2017
    x = query_peaks_mod
    y = lib_peaks_mod

    sum_sq_x = 0.0
    sum_sq_y = 0.0
    sum_coproduct = 0.0
    mean_x = x[0][1]
    mean_y = y[0][1]
    n = x.length
    for i in 1..(n-1) do
      i_float = i.to_f
      sweep = (i_float - 1.0) / i_float
      delta_x = x[i][1] - mean_x
      delta_y = y[i][1] - mean_y
      sum_sq_x += delta_x * delta_x * sweep
      sum_sq_y += delta_y * delta_y * sweep
      sum_coproduct += delta_x * delta_y * sweep
      mean_x += delta_x / i_float
      mean_y += delta_y / i_float
    end
    n_float = n.to_f
    pop_sd_x = Math.sqrt( sum_sq_x / n_float )
    pop_sd_y = Math.sqrt( sum_sq_y / n_float )
    cov_x_y = sum_coproduct / n_float
    cc = cov_x_y / (pop_sd_x * pop_sd_y)

    return num_lib_peaks_after_filtering, num_matches, cc
  end

end