class CMsResult
  attr_accessor :spectrum, :query_matches, :library_matches, :correl
    # :query_matches: proportion of query peaks with matching peaks in a library spectrum.
    # :library_matches: proportion of library spectrum peaks matching those in the list of query peaks.
    # :correl: Pearson correlation between query and library spectra (see CMs model for algorithm).

  delegate :id, :inchikey, :notes, :derivative_type, :derivative_formula, :derivative_mw, to: :spectrum

  def initialize(spectrum, query_matches, library_matches, correl)
    @spectrum, @query_matches, @library_matches, @correl = spectrum, query_matches, library_matches, correl
  end

  def <=>(b)
    (self.query_matches + self.library_matches + self.correl) <=> (b.query_matches + b.library_matches + b.correl)
  end

  def type
    spectrum.predicted? ? "Predicted" : "Experimental"
  end

end