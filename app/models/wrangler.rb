class Wrangler < ApplicationRecord
  belongs_to :structure, optional: true
  has_many :wrangler_curations, dependent: :delete_all
  has_many :curations, through: :wrangler_curations

  validates :name, presence: true, length: { maximum: 255 }
  validates :runner, presence: true, length: { maximum: 255 }
  validates :query, presence: true

  def complete?
    self.queries.length == self.curations.length
  end

  def finished_at
    if self.complete?
      self.wrangler_curations.order(:updated_at).last.try(:updated_at)
    else
      nil
    end
  end

  def started_at
    self.wrangler_curations.order(:created_at).first.try(:created_at)
  end  

  def queries
    self.query.split(/\n|\r/).reject(&:blank?)
  end

  # Creates an active job for each query and adds them to the queue to
  # perform later.
  def wrangle!
    self.queries.each do |line|
      RunWranglerJob.perform_later self.wrangler_curations.create!(query: line)
    end
    true
  end
end
