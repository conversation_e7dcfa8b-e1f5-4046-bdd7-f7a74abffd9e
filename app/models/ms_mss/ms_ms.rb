require 'base64'
class MsMs < ApplicationRecord
  include Spectrum
  include MsMsConcerns::MzMl
  include MsMsConcerns::Search

  has_many :peaks, class_name: "MsMsPeak", source: :ms_ms_peak, dependent: :destroy do
    def exported
      where export: true
    end
  end
  has_many :exported_peaks, -> { where(export: true) },
    class_name: "MsMsPeak",
    source: :ms_ms_peak

  validate :valid_sample_mass?
  validate :valid_sample_concentration?

  scope :predicted, -> { where(predicted: true) }
  scope :experimental, -> { where(predicted: false) }

  def self.stats(options = {})
    counts = MsMs.for_database(options[:database]).
      group(:instrument_type, :collision_energy_level, :collision_energy_voltage, :predicted).
      count
    counts.map do |(instrument, energy_level, energy_voltage, predicted), value|
      name = "MS/MS Spectrum"
      name = "#{instrument} #{name}" if instrument.present?
      name = "#{energy_level.capitalize} Energy #{name}" if energy_level.present?
      name = "Predicted #{name}" if predicted
      Statistic.new(name: name, value: value)
    end
  end

  def splash
    return if splash_key.nil?
    SpectrumHash.from_splash_string splash_key
  end

  def regenerate_splash_key
    peaks = self.peaks.exported.map do |p|
      [p.mass_charge.to_f,p.intensity.to_f]
    end
    # self.update_attributes!(splash_key: SpectrumHash.from_peaks(peaks))
    splash_key = SpectrumHash.from_peaks(peaks).splash
    self.splash_key = splash_key if splash_key
  end

  def to_export_xml
    super do |xml|
      # Only add the exported peaks to the xml
      peaks.where(export: true).
        to_xml(builder: xml, skip_instruct: true, skip_types: true, except: [:export])
    end
  end

  def spectrum_type
    if instrument_type =~ /^LC/
      "#{predicted? ? "Predicted " : "" }LC-MS Spectrum - #{instrument_type} #{collision_energy_voltage.present? ? collision_energy_voltage.to_s + "V" : collision_energy_level}, #{ionization_mode}"
    else
      "#{predicted? ? "Predicted " : "" }MS/MS Spectrum - #{instrument_type} #{collision_energy_voltage.present? ? collision_energy_voltage.to_s + "V" : collision_energy_level}, #{ionization_mode}"
    end
  end

  def annotated?
    @annotated ||= self.peaks.annotated.first.present?
  end

  def clean
    #remove all peaks with relative intensity less then 0.5
    peaks.each do |peak|
      if peak.intensity <= 0.5
        peak.export = false
        peak.save!
        next
      end
    end

    # Combine fractional peaks into single peaks
    # e.g. if there are multiple peaks that round to
    # the same m/z value, take only the one with
    # the highest intensity.
    peak_filter = Hash.new
    peak_max_intensity = Hash.new

    exported_peaks.each do |peak|
      if peak_filter[peak.mass_charge.round].nil?
        peak_filter[peak.mass_charge.round] = [peak]
        peak_max_intensity[peak.mass_charge.round] = peak.intensity
      else
        peak_filter[peak.mass_charge.round].push(peak)
        peak_max_intensity[peak.mass_charge.round] = peak.intensity if peak.intensity > peak_max_intensity[peak.mass_charge.round]
      end
    end

    peak_filter.each do |mz, peak_array|
      peak_array.each do |peak|
        if peak.intensity < peak_max_intensity[mz]
          peak.export = false
          peak.save!
        end
      end
    end

    #Calculate the average across the range of peaks, assuming 0 for non-present peaks
    #Remove all peaks that aren't at least NOISE_THRESH=2
    #times the background energy level

    total_intensity = 0
    max_mz = 0
    min_mz = 0
    exported_peaks.each do |peak|
      total_intensity += peak.intensity
      max_mz = peak.mass_charge if peak.mass_charge > max_mz
    end

    average_intensity = total_intensity / (max_mz.round - min_mz.round)
    threshold = average_intensity * 2

    exported_peaks.each do |peak|
      if peak.intensity < threshold
        peak.export = false
        peak.save!
      end
    end
    self.save!
  end

  def peaks_array
    exported_peaks.map { |peak| [peak.mass_charge, peak.intensity] }
  end

  def binary_mz
    a = self.exported_peaks.map(&:mass_charge)
    Base64.encode64(a.pack("E"*self.exported_peaks.count))
  end

  def binary_intensity
    a = self.exported_peaks.map(&:intensity)
    Base64.encode64(a.pack("E"*self.exported_peaks.count))
  end

  private

  def valid_sample_mass?
    if self.sample_mass.present? && self.sample_mass_units.blank?
      self.errors.add(:sample_mass_units_required, "missing sample mass units")
    elsif self.sample_mass.blank? && self.sample_mass_units.present?
      self.errors.add(:sample_mass_required, "missing sample mass")
    end
  end

  def valid_sample_concentration?
    if self.sample_concentration.present? && self.sample_concentration_units.blank?
      self.errors.add(:sample_concentration_units_required, "missing sample concentration units")
    elsif self.sample_concentration.blank? && self.sample_concentration_units.present?
      self.errors.add(:sample_concentration_required, "missing sample concentration")
    end
  end

  def self.tolerance_difference(tolerance, units, query_mass)
    # Formula for mass difference from PPM is ppm*mass/10^6
    if units == 'Da'
      BigDecimal.new(tolerance)
    else
      self.ppm_to_dalton(tolerance, query_mass)
    end
  end

  def self.ppm_to_dalton(ppm, mass)
    BigDecimal.new(mass) * BigDecimal.new(ppm) / 1_000_000
  end

  def self.fit(query_peaks, lib, tolerance, tolerance_units)
    x0 = get_x0(lib)
    y0 = get_lib_y(x0, lib, tolerance, tolerance_units)
    calc(lib, x0, y0, query_peaks, lib, tolerance, tolerance_units)
  end

  def self.r_fit(query_peaks,lib, tolerance, tolerance_units)
    x0 = get_x0(query_peaks)
    y0 = get_lib_y(x0, query_peaks, tolerance, tolerance_units)
    calc(query_peaks, x0, y0, query_peaks, lib, tolerance, tolerance_units)
  end

  def self.purity(query_peaks, lib, tolerance, tolerance_units)
    mixed = mix(query_peaks, lib)
    x0 = get_x0(mixed)
    y0 = get_lib_y(x0, mixed, tolerance, tolerance_units)
    calc(mixed, x0, y0, query_peaks, lib, tolerance, tolerance_units)
  end

  # Caculate SUM(w(i)*COS_theta(i))
  # x0 = (xmin+xmax)/2 where x is the set of m/z in target_array
  # y0 is intensity at x0
  # tol is m/z tolerance used for look up of library intensity
  def self.calc(target_array, x0, y0, query_peaks, lib, tolerance, tolerance_units)
    result = 0.0
    target_array.each_index do |i|
      yd = get_lib_y(BigDecimal.new(target_array[i][0].to_s), lib, tolerance, tolerance_units)
      ya = get_lib_y(BigDecimal.new(target_array[i][0].to_s), query_peaks, tolerance, tolerance_units)
      wi = target_array[i][1]/get_sum_column(target_array, 1)
      cos = cos_theta(target_array[i][0], ya, x0, y0, yd)
      result += (wi * cos)
    end
    result
  end

  # xi Target m/z
  # ya Target intensity
  # yd is library intensity at target m/z (xi)
  # x0 = (xmin+xmax)/2
  # y0 is intensity at x0
  def self.cos_theta(xi, ya, x0, y0, yd)
    ((xi-x0)**2 + (ya-y0)*(yd-y0))/(((xi-x0)**2 + (ya-y0)**2)**0.5 * ((xi-x0)**2 + (yd-y0)**2)**0.5)
  end

  # Get intensity within tolerance from libary spectra for a particular m/z
  def self.get_lib_y(xi, lib, tolerance, tolerance_units)
    diff = self.tolerance_difference(tolerance, tolerance_units, xi)
    min = xi - diff
    max = xi + diff
    yval = 0.0
    lib.each_index do |i|
      xval = lib[i][0]
      if(xval <= max  && xval >= min)
        yval = lib[i][1] if (yval < lib[i][1])
      end
    end
    yval
  end

  def self.get_sum_column(array, col)
    result = 0.0
    array.each do |v|
      result += v[col]
    end
    result
  end

  def self.get_x0(target_array)
    min = nil
    max = nil
    target_array.each_index do |i|
      if min.nil? && max.nil?
        min = target_array[i][0]
        max = target_array[i][0]
      elsif target_array[i][0] < min
        min = target_array[i][0]
      elsif target_array[i][0] > max
        max = target_array[i][0]
      end
    end
    if min.nil? || max.nil?
      raise
    end
    return (min+max)/2
  end

  #combine two libraries
  def self.mix(query_peaks, lib)
    mix = []
    query_peaks.each do |e|
      mix.push(e)
    end
    lib.each do |e|
      mix.push(e)
    end

    mix
  end
end
