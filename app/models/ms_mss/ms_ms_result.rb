class MsMsResult
  attr_accessor :spectrum, :fit, :r_fit, :purity, :score

  delegate :id, :inchikey, :notes, to: :spectrum

  def initialize(spectrum, fit, r_fit, purity, score)
    @spectrum, @fit, @r_fit, @purity, @score = spectrum, fit, r_fit, purity, score

    # TODO fix this terrible hack, there should never be a NaN
    # in the actual values !!!
    [@fit, @r_fit, @purity, @score].each do |variable|
      variable = 0.0 if variable.to_s == 'NaN'
    end
  end

  def type
    spectrum.predicted? ? "Predicted" : "Experimental"
  end
end
