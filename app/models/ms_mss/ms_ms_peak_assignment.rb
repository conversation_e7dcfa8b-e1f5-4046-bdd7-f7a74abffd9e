class MsMsPeakAssignment < ApplicationRecord
  has_attached_file :assignment_view
  validates_attachment_content_type :assignment_view,
    content_type: ["image/jpg", "image/jpeg", "image/png", "image/gif", "image/svg+xml"]
  belongs_to :peak,
    class_name: "MsMsPeak",
    foreign_key: :ms_ms_peak_id,
    inverse_of: :peak_assignments, optional: true

  belongs_to :structure, optional: true

  validates :smiles, presence: true
  validates :peak, presence: true

  def self.from_param(id)
    self.find(id)
  end

  def image_url
    if self.structure
      return "http://moldb.np-mrd.org/structures/#{self.structure.inchikey}/image.png"
    else
      return "http://moldb.np-mrd.org/structures/ms_ms/#{self.id}/peak.svg"
    end
  end

end
