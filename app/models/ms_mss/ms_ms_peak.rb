class MsMsPeak < ApplicationRecord
  belongs_to :ms_ms, counter_cache: :peak_counter, inverse_of: :peaks, optional: true
  has_many :peak_assignments, class_name: "MsMsPeakAssignment", inverse_of: :peak, dependent: :destroy

  validates :mass_charge, presence: true, numericality: true
  validates :intensity, presence: true, numericality: true

  scope :exported, -> { where(export: true) }
  scope :annotated, -> { joins(:peak_assignments).where('ms_ms_peak_assignments.ms_ms_peak_id is not NULL') }
end
