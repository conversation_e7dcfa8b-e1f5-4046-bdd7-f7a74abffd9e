class NmrOneD < ApplicationRecord
  # include Spectrum

  SEARCH_DEFAULTS = {
    # Default tolerance for the peak matches
    chemical_shift_tolerance: 0.01,
  }

  belongs_to :structure, optional: true

  has_many :peaks,
    class_name: "NmrOneDPeak",
    dependent: :destroy,
    source: :nmr_one_d_peaks

  before_save :set_distinct_peaks

  #has_many :nmr_one_d_assignments, dependent: :destroy
  include Spectrum
  validates :nucleus, presence: true, inclusion: { in: %w{1H 13C} }
  validates :sample_mass, numericality: true, allow_nil: true
  validates :sample_temperature, numericality: true, allow_nil: true
  #validates :sample_concentration, numericality: true, allow_nil: true
  validate :valid_sample_mass?
  validate :valid_sample_temperature?
  validate :valid_sample_concentration?

  def self.stats(options = {})
    counts = NmrOneD.for_database(options[:database]).group(:nucleus).count
    counts.map do |key, value|
      name = key.present? ? "#{key.upcase} NMR Spectrum" : "NMR Spectrum"
      Statistic.new(name: name, value: value)
    end
  end

  def to_export_xml
    super do |xml|
      # Only add the exported peaks to the xml
      peaks.to_xml(
        builder: xml,
        skip_instruct: true,
        skip_types: true,
        except: [ :unique_0, :unique_1, :unique_2, :unique_3,
          :unique_4, :chemical_shift_error, :intensity_error]
      )
    end
  end

  # Potential alternative to peak_counter
  def set_distinct_peaks
    if distinct_peaks.nil?
      self.distinct_peaks = count_distinct_peaks
      self.save
    end
  end

  private

  def count_distinct_peaks
    return peaks.count if peaks.first&.atom_id.nil?
    peaks.pluck(:atom_id).uniq.count
  end

  def valid_sample_mass?
    if self.sample_mass.present? && self.sample_mass_units.blank?
      self.errors.add(:sample_mass_units_required, "missing sample mass units")
    elsif self.sample_mass.blank? && self.sample_mass_units.present?
      self.errors.add(:sample_mass_required, "missing sample mass")
    end
  end

  def valid_sample_temperature?
    if self.sample_temperature.present? && self.sample_temperature_units.blank?
      self.errors.add(:sample_temperature_units_required, "missing sample temperature units")
    elsif self.sample_temperature.blank? && self.sample_temperature_units.present?
      self.errors.add(:sample_temperature_required, "missing sample temperature")
    end
  end

  def valid_sample_concentration?
    if self.sample_concentration.present? && self.sample_concentration_units.blank?
      self.errors.add(:sample_concentration_units_required, "missing sample concentration units")
    elsif self.sample_concentration.blank? && self.sample_concentration_units.present?
      self.errors.add(:sample_concentration_required, "missing sample concentration")
    end
  end

end
