require 'set'
require 'jaccard_methods'
require 'digest/md5'

class NmrOneDSearchLib
  ALLOWED_NUCLEI = %w[1H 13C].freeze

  attr_reader :nucleus, :no_of_peaks, :query_spec, :query_spec, :database,
              :database_id, :ids, :frequency, :search_type

  def self.clear_cache!
    Rails.cache.delete_matched(/nmr_one_d_search_lib\/\w+/)
  end

  def cache_key
    @cache_key ||=
      begin
        # Stringifying the params to create uniq cache key
        md5 =
          Digest::MD5.hexdigest(
            "#{frequency}|#{nucleus}|#{query_spec.join(',')}|#{database}|#{database_id}|#{ids}"
          )
        "nmr_one_d_search_lib/#{md5}"
      end
  end

  # Clears the library cache and builds the lib
  def self.rebuild!
    clear_cache!
    ALLOWED_NUCLEI.each do |nucleus|
      new(nucleus: nucleus).lib
    end
    true
  end

  def initialize(options = {})
    raise "nucleus must be one of #{ALLOWED_NUCLEI.join(', ')}" unless ALLOWED_NUCLEI.include? options[:nucleus].to_s

    @nucleus = options[:nucleus]
    @no_of_peaks = options[:no_of_peaks]
    @query_spec = options[:query_spec]
    @database = options[:database]
    @frequency = options[:frequency]
    source = @database.nil? ? nil : Source.find_by(name: @database)
    @database_id = source.nil? ? nil : source.id
    @ids = options[:ids]
    @search_type = options[:search_type]
  end

  def lib
    @lib ||= fetch
  end

  def self.meta_data(options = {})
    NmrOneDSearchLib
      .new(nucleus: options[:nucleus], no_of_peaks: options[:no_of_peaks],
           query_spec: options[:query_spec], database: options[:database], ids: options[:ids])
      .meta_data
  end

  def meta_data
    @meta_data ||= fetch_meta_data
  end

  def fetch_pure
    lib = {}
    spectra = NmrOneDPeak
                .select('id', 'nmr_one_d_id', 'peak_position_ppm', 'intensity', 'source_id')
                .joins(nmr_one_d: %i[structure database_registrations])
                .where(nmr_one_d: { nucleus: nucleus, exported: 1 })
    spectra = spectra.where('nmr_one_d.frequency LIKE ?', "%#{frequency}%") if frequency.present?

    # Prefilter the results
    peak_tolerance = 2 # adjust this to control the sensitivity of the prefilter step
    lower_peak = [no_of_peaks - peak_tolerance, 0].max
    upper_peak = no_of_peaks + peak_tolerance
    spectra = spectra.where(nmr_one_d: { peak_counter: lower_peak..upper_peak }) # filter out spectra with too large a difference in the number of peaks

    spectra = spectra.where(database_registrations: { source_id: database_id }) unless database_id.nil?
    spectra = spectra.uniq # ensure we don't include duplicates from the join
    spectra = spectra.group_by(&:nmr_one_d_id)
    spectra.uniq.each do |id, lib_spec|
      unless lib_spec.any? { |peak| peak.peak_position_ppm.nil? } # Some spectra are filled out improperly in nmr_one_d_peaks table, we won't use those in the search
        lib[id] = ScoringMethods.dot_optimize_spectrum(lib_spec)
      end
    end
    lib
  end

  def fetch_mixture
    lib = {}
    peak_tolerance = 0.1 # TODO: Add this as param to user
    peak_range = -> (position) { (position - peak_tolerance)..(position + peak_tolerance) }

    # Grab the first spectrum associated with each structure
    # We do this so that we can limit the amount of duplicate results, and cut down on the amount of joins we need to do
    ids = NmrOneD.joins(:structure, :database_registrations)
                 .where(nucleus: nucleus, exported: 1, peak_counter: -Float::INFINITY..no_of_peaks)
                 .where(database_registrations: { source_id: database_id }) unless database_id.nil?
    ids = ids.group(:structure_id).minimum(:id).values.first(15000) # try to remove this limit later (not enough time right now AHHHHH)

    # Grab the peaks associated with each of the nmr_one_d ids we grabbed above
    # We are going to check if any peaks match before so that we don't need to waste time and memory
    # calculating scores for spectra that don't have any matching peaks
    # Note: The joins here speeds up the query for some reason? Perhaps due to some SQL optimization from the first step?
    base_query = NmrOneDPeak
                   .joins(:nmr_one_d)
                   .select(:structure_id)
                   .where(nmr_one_d: { id: ids })

    # Accommodate tolerance for peak_position_ppm into the query
    spectra = base_query.where(peak_position_ppm: peak_range.call(query_spec[0]))
    query_spec.drop(1).each do |query_peak_position|
      spectra = spectra.or(base_query
                             .where(peak_position_ppm: peak_range.call(query_peak_position)))
    end

    spectra = spectra.distinct

    # Finally pull the actual data we need to calculate the Jaccard Score
    spectra = NmrOneDPeak.select('id', 'nmr_one_d_id', 'peak_position_ppm')
                         .where(nmr_one_d_id: spectra.pluck(:nmr_one_d_id))
                         .group_by(&:nmr_one_d_id)

    spectra.uniq.each do |id, lib_spec|
      unless lib_spec.any? { |peak| peak.peak_position_ppm.nil? } # Some spectra are filled out improperly in nmr_one_d_peaks table, we won't use those in the search
        # prepping the input format for the JaccardScore method
        lib[id] = JaccardMethods.optimize_spectrum(lib_spec)
      end
    end
    lib
  end

  def fetch
    # Grabs all the nmr_one_d_peaks based on the request
    Rails.cache.fetch(cache_key, expires_in: 12.hours) do
      if search_type == 'pure_compound'
        fetch_pure
      elsif search_type == 'mixture'
        fetch_mixture
      else
        # this shouldn't happen (unless someone posts to moldb manually)
        raise "Unsupported Search Type"
      end
    end
  end

  def fetch_meta_data
    # Grabs db ids and nmr_one_d_ids
    Rails.cache.fetch("/nmr_one_d_search_lib/meta_data/#{cache_key.split('/').last}", expires_in: 12.hours) do
      meta_data_lib = {}

      # Note: We used to load properties here, but I opted to load them in the calling DB instead

      if database_id.nil?
        database_registrations = DatabaseRegistration
                                   .select('nmr_one_d.id AS nmr_one_d_id',
                                           'sources.name AS database_name', 'database_id')
                                   .joins(:source, structure: [:nmr_one_d])
                                   .where(nmr_one_d: { id: ids })

        database_registrations.each do |row|
          nmr_one_d = meta_data_lib[row.nmr_one_d_id] ||= {}
          nmr_one_d['id'] = row.nmr_one_d_id
          registrations = nmr_one_d[:database_ids] ||= {}
          database = registrations[row.database_name] ||= []
          database << row.database_id
        end
      else
        # Reduce joins and fetching when we know that database
        database_registrations = DatabaseRegistration
                                   .select('nmr_one_d.id AS nmr_one_d_id', 'database_id')
                                   .joins('INNER JOIN nmr_one_d ON nmr_one_d.structure_id = database_registrations.structure_id')
                                   .where(nmr_one_d: { id: ids }, source_id: database_id)
        database_registrations.each do |row|
          nmr_one_d = meta_data_lib[row.nmr_one_d_id] ||= {}
          nmr_one_d['id'] = row.nmr_one_d_id
          registrations = nmr_one_d[:database_ids] ||= {}
          database = registrations[self.database] ||= []
          database << row.database_id
        end
      end

      meta_data_lib
    end
  end
end
