require 'set'
require 'digest/md5'
require 'jaccard_methods'

class NmrOneDSearch
  include JaccardMethods

  DEFAULT_NUCLEUS = '1H'
  DEFAULT_TOLERANCE = 0.10

  attr_reader :tolerance, :query_spec, :intensities, :nucleus, :no_of_peaks, :database, :frequency,
              :search_type
  attr_writer :results

  def self.clear_cache!
    Rails.cache.delete_matched(/nmr_one_d_search\/\w+/)
  end

  def self.fetch_from_cache(cache_id)
    cache_id = "nmr_one_d_search/#{cache_id}"
    data = Rails.cache.fetch(cache_id)
    return nil if data.nil?

    search = new query_spec: data[:query_spec], intensities: data[:intensities], tolerance: data[:tolerance],
                 nucleus: data[:nucleus],
                 no_of_peaks: data[:no_of_peaks], database: data[:database],
                 frequency: data[:frequency], search_type: data[:search_type]
    search.results = data[:results]
    search
  end

  def initialize(options = {})
    unless options[:nucleus].nil? || NmrOneDSearchLib::ALLOWED_NUCLEI.include?(options[:nucleus].to_s)
      raise "nucleus must be one of #{ALLOWED_NUCLEI.join("\n")}"
    end

    @nucleus = (options[:nucleus]).to_s
    @search_type = options[:search_type]
    @tolerance = options[:tolerance].present? ? options[:tolerance].to_f : DEFAULT_TOLERANCE
    @frequency = options[:frequency].present? ? options[:frequency].to_i : nil
    @query_spec = options[:query_spec]
    @intensities = options[:intensities]
    @no_of_peaks = options[:no_of_peaks]
    @database = options[:database]
  end

  def scaled_tolerance
    @scaled_tolerance ||= JaccardMethods.scale_to_int(tolerance)
  end

  def scaled_query_spec
    @scaled_query_spec ||= JaccardMethods.optimize_peak_list(query_spec)
  end

  # used to build the lib_spectrum param for the scoring methods (Jaccard or Dot, based on the search type)
  def build_lib_spectrum_param
    if search_type == 'pure_compound'
      max_intensity = intensities.max # get maximum intensity
      max_intensity = max_intensity || 1 # avoid divide by zero
      scaled_intensities = intensities.map{|v| (v/max_intensity).to_f}
      query_spec.each_with_index.map { |peak_position, index| [peak_position, scaled_intensities[index]] }
    else
      @scaled_query_spec ||= JaccardMethods.optimize_peak_list(query_spec)
    end
  end

  def peaks
    query_spec
  end

  def lib
    @lib ||= NmrOneDSearchLib
               .new(nucleus: nucleus, no_of_peaks: no_of_peaks,
                    query_spec: query_spec, database: database,
                    frequency: frequency,
                    search_type: search_type)
               .lib
  end

  def meta_data
    @lib_meta_data ||= NmrOneDSearchLib
                         .meta_data(nucleus: nucleus, no_of_peaks: no_of_peaks,
                                    query_spec: query_spec, database: database,
                                    ids: lib.map { |peak_info| peak_info.first })
  end

  # Compare query_spec to another spectrum and return the match
  # information.
  def compare_to_peak_list(other_spec, tolerance = nil)
    tolerance ||= @tolerance
    jaccard(@query_spec, other_spec, tolerance)
  end

  #def compare_to_library_spectrum(library_id)
  # get library spectrum
  #library_id
  #jaccard(lib_spec.dup, @query_spec, tolerance)
  #end

  # Seach the library for the closest spectrum to the query spectrum
  def rebuild_results!
    clear_cached_results
    fetch_data
    true
  end

  def clear_cached_results
    Rails.cache.delete(cache_key)
  end

  def build_pure_results
    begin
      scores = Hash.new
      lib.each do |id, lib_spec|
        scores[id] = ScoringMethods.dot_product(lib_spec, build_lib_spectrum_param, tolerance)
      end
      scores = scores.keep_if { |k, v| v[:dot_score] > 0.9}.# TODO: Sort out low scoring results?
      sort_by { |k, v| v[:dot_score] }.reverse!
      # Add meta data on to the scores
      scores.each do |id, data|
        meta_data = self.meta_data[id]
        next if meta_data.nil?

        data.merge!(meta_data)
      end
    end
  end

  def build_mixture_results
    begin
      scores = Hash.new
      lib.each do |id, lib_spec|
        scores[id] = JaccardMethods.fast_jaccard(lib_spec, scaled_query_spec, scaled_tolerance)
      end
      scores = scores.keep_if { |k, v| v[:jaccard_index] > 0}.# TODO: Sort out low scoring results? Not sure if needed for mixture
      sort_by { |k, v| v[:jaccard_index] }.reverse!
      # Add meta data on to the scores
      scores.each do |id, data|
        meta_data = self.meta_data[id]
        next if meta_data.nil?

        data.merge!(meta_data)
      end
    end
  end

  def build_results
    @results ||= if search_type == 'pure_compound'
                   build_pure_results
                 elsif search_type == 'mixture'
                   build_mixture_results
                 else
                   raise 'Unsupported Search Type'
                 end
  end

  def fetch_data
    # @data ||= Rails.cache.fetch(self.cache_key, expires_in: 24.hours) do
    @data ||= Rails.cache.fetch(cache_key) do
      {
        query_spec: query_spec,
        intensities: intensities,
        tolerance: tolerance,
        frequency: frequency,
        nucleus: nucleus,
        no_of_peaks: no_of_peaks,
        database: database,
        search_type: search_type,
        results: build_results
      }
    end
  end

  def results
    @results ||= fetch_data[:results]
  end

  def cache_key
    @cache_key ||=
      begin
        # Stringifying the params to create uniq cache key
        md5 =
          Digest::MD5.hexdigest(
            "#{search_type}|#{frequency}|#{tolerance}|#{nucleus}|#{query_spec.join(",")}|#{intensities.join(",")}|#{database}}"
          )
        "nmr_one_d_search/#{md5}"
      end
  end

  def to_param
    @as_param ||= cache_key.split('/').last
  end

end
