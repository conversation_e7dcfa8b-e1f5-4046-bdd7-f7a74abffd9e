class NmrOneDPeak < ApplicationRecord
  belongs_to :nmr_one_d, counter_cache: :peak_counter, optional: true

  # validates :chemical_shift, presence: true, numericality: true
  # validates :intensity, numericality: true, allow_nil: true
  # validates :chemical_shift_error, numericality: true, allow_nil: true
  # validates :intensity_error, numericality: true, allow_nil: true

  def unique_values
    [unique_0, unique_1, unique_2, unique_3, unique_4]
  end
end
