class NmrTwoDResult
  attr_accessor :spectrum, :peaks, :score

  delegate :id, :inchikey, :notes, :peak_counter, to: :spectrum

  def initialize(spectrum, peaks, score)
    @spectrum, @peaks, @score = spectrum, peaks, score
  end

  def matches
    "#{self.peaks.length}/#{self.spectrum.peak_counter}"
  end

  def matching_peak_ratio
    self.peaks.length.to_f/self.spectrum.peak_counter.to_f
  end

  def <=>(b)
    if b.matching_peak_ratio > self.matching_peak_ratio ||
       b.score > self.score ||
       b.peaks.length.to_f > self.peaks.length.to_f
      1
    else
      -1
    end
  end
end
