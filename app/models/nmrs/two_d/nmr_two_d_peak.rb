class NmrTwoDPeak < ApplicationRecord
  belongs_to :nmr_two_d, counter_cache: :peak_counter, optional: true

  validates_presence_of :chemical_shift_x
  validates_numericality_of :chemical_shift_x
  #validates_presence_of :chemical_shift_y
  #validates_numericality_of :chemical_shift_y

  def uniq_level
    if self.unique_4 == 0
      6
    elsif self.unique_3 == 0
      5
    elsif self.unique_2 == 0
      4
    elsif self.unique_1 == 0
      3
    elsif self.unique_0 == 0
      2
    else
      1
    end
  end
end
