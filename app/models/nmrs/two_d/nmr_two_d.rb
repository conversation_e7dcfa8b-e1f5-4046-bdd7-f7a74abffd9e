class NmrTwoD < ApplicationRecord
  include Spectrum

  has_many :peaks,
    class_name: "NmrTwoDPeak",
    dependent: :destroy,
    source: :nmr_two_d_peaks

  #has_many :nmr_one_d_assignments, dependent: :destroy
  has_many :nmr_one_d_assignments

  def to_export_xml
    super do |xml|
      # Only add the exported peaks to the xml
      peaks.to_xml(
        builder: xml,
        skip_instruct: true,
        skip_types: true,
        except: [ :unique_0, :unique_1, :unique_2,
          :unique_3, :unique_4]
      )
    end
  end

  def self.stats(options = {})
    counts = NmrTwoD.for_database(options[:database]).
      group(:nucleus_x, :nucleus_y).
      count
    counts.map do |key, value|
      name = "[#{key[0].upcase},#{key[1].upcase}] 2D NMR Spectrum"
      Statistic.new(name: name, value: value)
    end
  end

  # Supports Toscy (1H,1H) and HSQC (1H,13C) only
  def self.search(nucleus_x, nucleus_y, chemical_shifts = [], options = {})
    return [] if nucleus_x.blank? || nucleus_y.blank? ||
                 chemical_shifts.blank? || options[:database].blank?
    raise "SearchNotSupported" if nucleus_x != "1H" && (nucleus_y != "1H" || nucleus_y != "13C")

    options[:x_tolerance] ||= 0.02
    options[:y_tolerance] ||= 0.02

    # Non symmetrical peaks are considered noises
    chemical_shifts = symmetry_clean(chemical_shifts) if nucleus_y == "1H"

    results = []
    spectra = NmrTwoD.for_database(options[:database]).
      where(nucleus_x: nucleus_x, nucleus_y: nucleus_y, searchable: true)

    spectra.includes(:peaks).each do |spectrum|
      mpeaks, score = compare_mixture(spectrum, chemical_shifts,
                                      options[:x_tolerance].to_f,
                                      options[:y_tolerance].to_f,
                                      nucleus_y == "1H").to_a
      results << NmrTwoDResult.new(spectrum, mpeaks, score) if mpeaks.length > 0
    end
    results.sort
  end

  protected

  def self.compare_mixture(nmr, qpeaks, thresh_x, thresh_y, is_h)
    matched_lib_peaks = Set.new
    uniq_score = 0
    nmr.peaks.each do |peak|
      max_scope_x = max_scope_h(peak.uniq_level ) #X always proton
      adjusted_thresh_x = (thresh_x > max_scope_x) ? thresh_x : max_scope_x
      max_scope_y = is_h ? max_scope_h(peak.uniq_level) : max_scope_c(peak.uniq_level)
      adjusted_thresh_y = (thresh_y > max_scope_y) ? thresh_y : max_scope_y

      qpeaks.each do |q_peak|
        x = peak.chemical_shift_x - q_peak[0]
        y = peak.chemical_shift_y - q_peak[1]
        if ((x.abs <= adjusted_thresh_x) && (y.abs <= adjusted_thresh_y))
          matched_lib_peaks.add(peak)
          uniq_score += peak.uniq_level
          break
        end
      end
    end

    return matched_lib_peaks, uniq_score
  end

  def self.identify_compounds(result)
    result.each_index do |i|
      result[i].push(check_compound(result[i][0],result[i][1]))
    end
    result
  end

  def self.check_compound(res, matched_lib_cells)
    uniq_4_count = 0
    uniq_3_count = 0
    uniq_2_count = 0
    uniq_1_count = 0
    uniq_0_count = 0

    matched_lib_cells.each do |peak|
      if peak.unique_4.to_f == 0
        uniq_4_count += 1
      elsif peak.unique_3.to_f == 0
        uniq_3_count += 1
      elsif peak.unique_2.to_f == 0
        uniq_2_count += 1
      elsif peak.unique_1.to_f == 0
        uniq_1_count += 1
      elsif peak.unique_0.to_f == 0
        uniq_0_count += 1
      end
    end

    if uniq_4_count > 1
      true
    elsif uniq_4_count > 0 && (matched_lib_cells.length.to_f/res.nmr_peaks.count.to_f) > 0.5
      true
    elsif uniq_3_count > 3 || uniq_2_count > 4 || uniq_1_count > 5
      true
    elsif (matched_lib_cells.length.to_f/res.peak_counter.to_f) > 0.5 && matched_lib_cells.length > 4
      true
    else
      false
    end
  end

  def self.get_thresh(i)
    case i
    when 0 then 0.01
    when 1 then 0.02
    when 2 then 0.03
    when 3 then 0.04
    when 4 then 0.05
    else 0.02
    end
  end

  def self.max_scope_h(i)
    case i
    when 1 then 0.00
    when 2 then 0.01
    when 3 then 0.02
    when 4 then 0.03
    when 5 then 0.04
    when 6 then 0.05
    else 0.02
    end
  end

  def self.max_scope_c(i)
    case i
    when 1 then 0.00
    when 2 then 0.05
    when 3 then 0.10
    when 4 then 0.15
    when 5 then 0.20
    when 6 then 0.25
    else 0.10
    end
  end

  def self.symmetry_clean(query)
    sym_vec = [] # NMRPeak2D

    # first - split diagVec into up, and down off-diagonal
    up_vec = []
    down_vec = []
    dg_vec = []

    query.each do |pd|
      x = pd[0]
      y = pd[1]
      if (x - y > 0.04)
        up_vec.push(pd)
      elsif (x - y < 0.04)
        down_vec.push(pd)
      else
        dg_vec.push(pd)
      end
    end

    # second - found symmetrical peak b/w up and down diagonal peaks
    sym_map = {}
    up_vec.each do |pd1|
      down_vec.each do |pd2|
        if (is_symmetrical?(pd1, pd2))
          sym_map[pd1] = true
          sym_map[pd2] = true
        end
      end
    end

    # now process the diagnal peaks
    dg_vec.each do |p|
      sym_vec.push(p)
    end

    sym_map.keys.each do |p|
      sym_vec.push(p)
    end

    sym_vec
  end

  def self.is_symmetrical?(up_cell, down_cell)
    a = up_cell[1] - down_cell[0]
    b = up_cell[0] - down_cell[1]
    ((a.abs < 0.04) && (b.abs < 0.04))
  end
end
