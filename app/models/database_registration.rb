class DatabaseRegistration < ApplicationRecord
  belongs_to :structure, optional: true
  belongs_to :source, optional: true
  # belongs_to :structure, optional: true

  scope :exported, -> { where(exported: true) }

  # before_update :remap_registrations

  validates :structure_id, presence: true
  validates :source_id, presence: true
  validates :database_id,
    presence: true,
    length: { maximum: 255 },
    uniqueness: true
  validate do
    errors.add(:database_id, 'must not be InChI key') if database_id.to_s.inchikey?
  end

  def self.from_param(id)
    self.find_by(database_id: id)
  end

  def self.from_param!(id)
    self.from_param(id) || raise(ActiveRecord::RecordNotFound)
  end

  def to_param
    self.database_id
  end

  def to_sdf
    formats = {}
    structure.formats.each { |f| formats[f.name.to_sym] = f.value }
    props = {}
    structure.properties.each { |p| props["#{p.source}_#{p.name}".to_sym] = p.value }

    Sdf.new(formats[:mol_text]).generate do |sdf|
      sdf.tag('DATABASE_ID', self.database_id)
      sdf.tag('DATABASE_NAME', self.source.name)
      sdf.tag('SMILES', formats[:smiles])
      sdf.tag('INCHI_IDENTIFIER', formats[:inchi])
      sdf.tag('INCHI_KEY', self.structure.inchikey)
      sdf.tag('FORMULA', self.structure.formula)
      sdf.tag('MOLECULAR_WEIGHT', self.structure.molecular_weight.to_s)
      sdf.tag('EXACT_MASS', self.structure.exact_mass.to_s)
      props.each do |prop, value|
        sdf.tag(prop.to_s.upcase, value)
      end
    end
  end

  private

  # Re-map all database registration on structure update, save history
  def remap_registrations
    previous_record = DatabaseRegistration.find_by(database_id: self.database_id)
    previous_structure = previous_record.structure
    return if self.structure_id == previous_structure.id

    RegistrationHistory.create!(database_registration_id: previous_record.id, structure_id: previous_structure.id, exported: previous_record.exported)
    return if self.source.name == "smpdb" || self.source.name == "drugbank"
    
    DatabaseRegistration.where(structure_id: previous_structure.id).each do |registration|
      next if registration.source.name == "drugbank"
      if registration.structure_id != self.structure_id
        RegistrationHistory.create!(database_registration_id: registration.id, structure_id: registration.structure_id, exported: registration.exported)
      end
      registration.update_column(:structure_id, self.structure_id)
      puts registration.database_id
    end

    # Copy experimental spectra by class, if newly mapped structure does not have spectra
    all_spectra = previous_structure.spectra
    spectra_classes = all_spectra.chunk{ |s| s.class }

    spectra_classes.each do |spectra_class, spectra|
      # Skip copy if new structure already has spectra in any of these classes
      if ["NmrOneD", "NmrTwoD", "EiMs"].include?(spectra_class.to_s)
        next if spectra_class.where(structure_id: self.structure_id).any?
      else # Skip copy if new structure already has experimental spectra
        next if spectra_class.where(structure_id: self.structure_id, predicted: 0).any?
      end

      spectra.each do |spectrum|
        # Skip predicted spectra
        next if spectrum.has_attribute?(:predicted) && spectrum.predicted

        if ["NmrOneD", "NmrTwoD","EiMs"].include?(spectrum.class.to_s) # have no peak assignments
          new_spectrum = spectrum.deep_clone include: [ :peaks, :references, :spectra_sops ]
        else
          new_spectrum = spectrum.deep_clone include: [ { peaks: :peak_assignments }, :references, :spectra_sops ]
        end

        new_spectrum.structure_id = self.structure_id
        new_spectrum.save!
        spectra_class.reset_counters(new_spectrum.id, :peaks) # Reset peak counter

        # Attach documents (need to copy Paperclip attachment; deep clone is not sufficient)
        spectrum.documents.each do |document|
          if Document.find_by(description: document.description, spectra_id: new_spectrum.id, spectra_type: spectra_class.to_s).blank?
            new_document = document.dup
            new_document.file = document.file # Copy PaperClip attachment
            new_spectrum.documents << new_document
          end
        end
      end
    end
  end

end
