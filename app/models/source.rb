class Source < ApplicationRecord
  # has_many :structures, through: :database_registrations
  has_many :database_registrations, dependent: :restrict_with_error
  has_many :structures, through: :database_registrations
  has_many :synonym_sources
  has_many :synonyms, through: :synonym_sources

  validates :name, presence: true, uniqueness: true, length: { maximum: 255 }
  
  scope :active, -> { where(active: true) }
  
  def self.names
    self.active.uniq.order(:name).pluck(:name)
  end
end
