class Adduct < ApplicationRecord
  belongs_to :structure, required: true
  has_many :database_registrations, through: :structure
  has_many :sources, through: :database_registrations

  validates :adduct, inclusion: { in: Proc.new { self.allowed_adducts } }
  validates :adduct_type, inclusion: { in: %w(+ - 0) }
  validates :adduct_mass, presence: true

  # It calculates the adducts taking into account if the compound is charged or not
  # The calculation for adducts with multiple charge is not performed yet. 
  # To perform it is necessary to change data model and/or duplicate lists 
  # here, changing completely the algorithms used. 
  # As much as the users do not work with charged compounds for LC/MS, 
  # their specific mass is not calculated yet.

  # Takes a structure and builds all of the associated adducts
  def self.create_or_update_for_structure(structure)
    return if structure.exact_mass.blank?
    compound_mass = structure.exact_mass
    compound_id = structure.id
    total_charge = nil
    smiles = nil
    structure.formats.each do |format|
      if format.name == "smiles"
        smiles = format.value.to_s
      end
    end
    if !smiles.nil?
      positive_charges_count = 0
      negative_charges_count = 0
      smiles_string = smiles.scan(/(\+)([0-9])*\]/m)
      smiles_string.each do |a|
        if !a[1].nil?
          positive_charges_count+= a[1].to_i
        else
          positive_charges_count+= 1
        end
      end
      smiles_string = smiles.scan(/(-)([0-9])*\]/m)
      smiles_string.each do |a|
        if !a[1].nil?
          negative_charges_count+= a[1].to_i
        else
          negative_charges_count+= 1
        end
      end

      total_charge = positive_charges_count - negative_charges_count
    end

    extra_mass = 0
    if !total_charge.nil?
      proton_mass = 1.007276
      extra_mass = total_charge*proton_mass
    end    

    #fixing deadlock error, source: https://stackoverflow.com/questions/27246003/
    # mysql2error-deadlock-found-when-trying-to-get-lock-try-restarting-transactio?
    # utm_medium=organic&utm_source=google_rich_qa&utm_campaign=google_rich_qa
    #Also, see this: https://github.com/qertoip/transaction_retry
    retries = 0
    begin
      transaction do
        self.adduct_groups_positive.each do |type, adducts|
          adducts.each do |adduct, formula|
            if self.dimer_positive_adducts.keys.include? adduct
              new_mass = formula.call(compound_mass) - (2*extra_mass)
              adducted = structure.adducts.find_by(adduct: adduct)
              if adducted
                if adducted.adduct_mass != new_mass
                  adducted.update!(adduct_mass: new_mass)
                end
              else
                structure.adducts.create!(adduct: adduct, adduct_mass: new_mass,
                  adduct_type: type)
              end
            else
              new_mass = formula.call(compound_mass) - extra_mass
              adducted = structure.adducts.find_by(adduct: adduct)
              if adducted
                if adducted.adduct_mass != new_mass
                  adducted.update!(adduct_mass: new_mass)
                end
              else
                structure.adducts.create!(adduct: adduct, adduct_mass: new_mass,
                  adduct_type: type)
              end
            end
          end
        end
        self.adduct_groups_negative.each do |type, adducts|
          adducts.each do |adduct, formula|
            if self.dimer_negative_adducts.keys.include? adduct
              new_mass = formula.call(compound_mass) - (2*extra_mass)
              adducted = structure.adducts.find_by(adduct: adduct)
              if adducted
                if adducted.adduct_mass != new_mass
                  adducted.update!(adduct_mass: new_mass)
                end
              else
                structure.adducts.create!(adduct: adduct, adduct_mass: new_mass,
                  adduct_type: type)
              end
            elsif self.trimer_negative_adducts.keys.include? adduct
              new_mass = formula.call(compound_mass) - (3*extra_mass)
              adducted = structure.adducts.find_by(adduct: adduct)
              if adducted
                if adducted.adduct_mass != new_mass
                  adducted.update!(adduct_mass: new_mass)
                end
              else
                structure.adducts.create!(adduct: adduct, adduct_mass: new_mass,
                  adduct_type: type)
              end
            else
              new_mass = formula.call(compound_mass) - extra_mass
              adducted = structure.adducts.find_by(adduct: adduct)
              if adducted
                if adducted.adduct_mass != new_mass
                  adducted.update!(adduct_mass: new_mass)
                end
              else
                structure.adducts.create!(adduct: adduct, adduct_mass: new_mass,
                  adduct_type: type)
              end
            end
          end
        end
        self.adduct_groups_neutral.each do |type, adducts|
          adducts.each do |adduct, formula|
            new_mass = formula.call(compound_mass)
            adducted = structure.adducts.find_by(adduct: adduct)
            if adducted
              if adducted.adduct_mass != new_mass
                adducted.update!(adduct_mass: new_mass)
              end
            else
              structure.adducts.create!(adduct: adduct, adduct_mass: new_mass,
                adduct_type: type)
            end
          end
        end
      end
    rescue ActiveRecord::StatementInvalid => ex
      if ex.message =~ /Deadlock found when trying to get lock/
        retries += 1   
        raise ex if retries > 3  ## max 3 retries 
        sleep 10
        retry
      else
        raise ex
      end
    end
  end

  def self.single_positive_adducts
    MASS_TO_POSITIVE_ION_MASS_SINGLE_CHARGE
  end

  def self.dimer_positive_adducts
    MASS_TO_POSITIVE_ION_MASS_DIMER
  end

  def self.double_positive_adducts
    MASS_TO_POSITIVE_ION_MASS_DOUBLE_CHARGE
  end

  def self.triple_positive_adducts
    MASS_TO_POSITIVE_ION_MASS_TRIPLE_CHARGE
  end

  def self.single_negative_adducts
    MASS_TO_NEGATIVE_ION_MASS_SINGLE_CHARGE
  end

  def self.dimer_negative_adducts
    MASS_TO_NEGATIVE_ION_MASS_DIMER
  end

  def self.trimer_negative_adducts
    MASS_TO_NEGATIVE_ION_MASS_TRIMER
  end

  def self.double_negative_adducts
    MASS_TO_NEGATIVE_ION_MASS_DOUBLE_CHARGE
  end

  def self.triple_negative_adducts
    MASS_TO_NEGATIVE_ION_MASS_TRIPLE_CHARGE
  end

  def self.positive_adducts
    self.single_positive_adducts.merge(self.dimer_positive_adducts).merge(self.double_positive_adducts).merge(self.triple_positive_adducts)
  end  

  def self.negative_adducts
    self.single_negative_adducts.merge(self.dimer_negative_adducts).merge(self.trimer_negative_adducts).merge(self.double_negative_adducts).merge(triple_negative_adducts)
  end

  def self.neutral_adducts
    MASS_TO_NEUTRAL_MASS
  end


  def self.adduct_groups_positive
    {
      '+' => self.positive_adducts
    }
  end

  def self.adduct_groups_negative
    {
      '-' => self.negative_adducts
    }
  end

  def self.adduct_groups_neutral
    {
      '0' => self.neutral_adducts
    }
  end

  def self.allowed_adducts
     self.neutral_adducts.keys +
     self.positive_adducts.keys +
     self.negative_adducts.keys
  end

  #   MASS_TO_POSITIVE_ION_MASS = {
  #   "M+H"            => lambda { |mass| mass + 1.007276    },
  #   "M-2H2O+H"       => lambda { |mass| mass - 35.0127     },
  #   "M-H2O+H"        => lambda { |mass| mass - 17.0027     },
  #   "M-H2O+NH4"      => lambda { |mass| mass + 0.0227      },
  #   "M+Li"           => lambda { |mass| mass + 7.0160      },
  #   "M+NH4"          => lambda { |mass| mass + 18.033823   },
  #   "M+Na"           => lambda { |mass| mass + 22.989218   },
  #   "M+CH3OH+H"      => lambda { |mass| mass + 33.033489   },
  #   "M+K"            => lambda { |mass| mass + 38.963158   },
  #   "M+ACN+H"        => lambda { |mass| mass + 42.033823   },
  #   "M+2Na-H"        => lambda { |mass| mass + 44.971160   },
  #   "M+IsoProp+H"    => lambda { |mass| mass + 61.06534    },
  #   "M+ACN+Na"       => lambda { |mass| mass + 64.015765   },
  #   "M+2K-H"         => lambda { |mass| mass + 76.919040   },
  #   "M+DMSO+H"       => lambda { |mass| mass + 79.02122    },
  #   "M+2ACN+H"       => lambda { |mass| mass + 83.060370   },
  #   "M+IsoProp+Na+H" => lambda { |mass| mass + 84.05511    },
  #   "2M+H"           => lambda { |mass| 2*mass + 1.007276  },
  #   "2M+NH4"         => lambda { |mass| 2*mass + 18.033823 },
  #   "2M+Na"          => lambda { |mass| 2*mass + 22.989218 },
  #   "2M+3H2O+2H"     => lambda { |mass| 2*mass + 28.02312  },
  #   "2M+K"           => lambda { |mass| 2*mass + 38.963158 },
  #   "2M+ACN+H"       => lambda { |mass| 2*mass + 42.033823 },
  #   "2M+ACN+Na"      => lambda { |mass| 2*mass + 64.015765 },
  #   "M+2H"           => lambda { |mass| mass/2 + 1.007276  },
  #   "M+H+NH4"        => lambda { |mass| mass/2 + 9.520550  },
  #   "M+H+Na"         => lambda { |mass| mass/2 + 11.998247 },
  #   "M+H+K"          => lambda { |mass| mass/2 + 19.985217 },
  #   "M+ACN+2H"       => lambda { |mass| mass/2 + 21.520550 },
  #   "M+2Na"          => lambda { |mass| mass/2 + 22.989218 },
  #   "M+2ACN+2H"      => lambda { |mass| mass/2 + 42.033823 },
  #   "M+3ACN+2H"      => lambda { |mass| mass/2 + 62.547097 },
  #   "M+3H"           => lambda { |mass| mass/3 + 1.007276  },
  #   "M+2H+Na"        => lambda { |mass| mass/3 + 8.334590  },
  #   "M+H+2Na"        => lambda { |mass| mass/3 + 15.7661904},
  #   "M+3Na"          => lambda { |mass| mass/3 + 22.989218 }
  # }.freeze

  # MASS_TO_NEGATIVE_ION_MASS = {
  #   "M-H"            => lambda { |mass| mass - 1.007276    },
  #   "M-H20-H"        => lambda { |mass| mass - 19.01839    },
  #   "M+F"            => lambda { |mass| mass + 18.9984     },
  #   "M+Na-2H"        => lambda { |mass| mass + 20.974666   },
  #   "M+Cl"           => lambda { |mass| mass + 34.969402   },
  #   "M+K-2H"         => lambda { |mass| mass + 36.948606   },
  #   "M+FA-H"         => lambda { |mass| mass + 44.998201   },
  #   "M+Hac-H"        => lambda { |mass| mass + 59.013851   },
  #   "M+Br"           => lambda { |mass| mass + 78.918885   },
  #   "M+TFA-H"        => lambda { |mass| mass + 112.985586  },
  #   "2M-H"           => lambda { |mass| 2*mass - 1.007276  },
  #   "2M+FA-H"        => lambda { |mass| 2*mass + 44.998201 },
  #   "2M+Hac-H"       => lambda { |mass| 2*mass + 59.013851 },
  #   "3M-H"           => lambda { |mass| 3*mass - 1.007276  },
  #   "M-2H"           => lambda { |mass| mass/2 - 1.007276  },
  #   "M-3H"           => lambda { |mass| mass/3 - 1.007276  }
  # }.freeze

  MASS_TO_NEUTRAL_MASS = {
    "M"               => lambda { |mass| mass }
  }.freeze

  MASS_TO_POSITIVE_ION_MASS_SINGLE_CHARGE = {
    "M+H"            => lambda { |mass| mass + 1.007276    },
    "M+H-2H2O"       => lambda { |mass| mass - 35.0127     },
    "M+H-H2O"        => lambda { |mass| mass - 17.0027     },
    "M+NH4-H2O"      => lambda { |mass| mass + 0.0227      },
    "M+Li"           => lambda { |mass| mass + 7.0160      },
    "M+NH4"          => lambda { |mass| mass + 18.033823   },
    "M+Na"           => lambda { |mass| mass + 22.989218   },
    "M+CH3OH+H"      => lambda { |mass| mass + 33.033489   },
    "M+K"            => lambda { |mass| mass + 38.963158   },
    "M+ACN+H"        => lambda { |mass| mass + 42.033823   },
    "M+2Na-H"        => lambda { |mass| mass + 44.971160   },
    "M+IsoProp+H"    => lambda { |mass| mass + 61.06534    },
    "M+ACN+Na"       => lambda { |mass| mass + 64.015765   },
    "M+2K-H"         => lambda { |mass| mass + 76.919040   },
    "M+DMSO+H"       => lambda { |mass| mass + 79.02122    },
    "M+2ACN+H"       => lambda { |mass| mass + 83.060370   },
    "M+IsoProp+Na+H" => lambda { |mass| mass + 84.05511    },
    "M+H+HCOONa"     => lambda { |mass| mass + 68.9946     }
  }.freeze
  MASS_TO_POSITIVE_ION_MASS_DIMER = {
    "2M+H"           => lambda { |mass| 2*mass + 1.007276  },
    "2M+NH4"         => lambda { |mass| 2*mass + 18.033823 },
    "2M+Na"          => lambda { |mass| 2*mass + 22.989218 },
    "2M+2H+3H2O"     => lambda { |mass| 2*mass + 28.02312  },
    "2M+K"           => lambda { |mass| 2*mass + 38.963158 },
    "2M+ACN+H"       => lambda { |mass| 2*mass + 42.033823 },
    "2M+ACN+Na"      => lambda { |mass| 2*mass + 64.015765 },
    "2M+H-H2O"       => lambda { |mass| 2*mass - 17.0032   }
  }.freeze
  MASS_TO_POSITIVE_ION_MASS_DOUBLE_CHARGE = {
    "M+2H"           => lambda { |mass| mass/2 + 1.007276  },
    "M+H+NH4"        => lambda { |mass| mass/2 + 9.520550  },
    "M+H+Na"         => lambda { |mass| mass/2 + 11.998247 },
    "M+H+K"          => lambda { |mass| mass/2 + 19.985217 },
    "M+ACN+2H"       => lambda { |mass| mass/2 + 21.520550 },
    "M+2Na"          => lambda { |mass| mass/2 + 22.989218 },
    "M+2ACN+2H"      => lambda { |mass| mass/2 + 42.033823 },
    "M+3ACN+2H"      => lambda { |mass| mass/2 + 62.547097 }
  }.freeze
  MASS_TO_POSITIVE_ION_MASS_TRIPLE_CHARGE = {
    "M+3H"           => lambda { |mass| mass/3 + 1.007276  },
    "M+2H+Na"        => lambda { |mass| mass/3 + 8.334590  },
    "M+H+2Na"        => lambda { |mass| mass/3 + 15.7661904},
    "M+3Na"          => lambda { |mass| mass/3 + 22.989218 },
    "M+H+2K"         => lambda { |mass| mass/3 + 26.3112   }
  }.freeze
  MASS_TO_NEGATIVE_ION_MASS_SINGLE_CHARGE = {
    "M-H"            => lambda { |mass| mass - 1.007276    },
    "M-H20-H"        => lambda { |mass| mass - 19.01839    },
    "M+F"            => lambda { |mass| mass + 18.9984     },
    "M+Na-2H"        => lambda { |mass| mass + 20.974666   },
    "M+Cl"           => lambda { |mass| mass + 34.969402   },
    "M+K-2H"         => lambda { |mass| mass + 36.948606   },
    "M+FA-H"         => lambda { |mass| mass + 44.998201   },
    "M+Hac-H"        => lambda { |mass| mass + 59.013851   },
    "M+Br"           => lambda { |mass| mass + 78.918885   },
    "M+TFA-H"        => lambda { |mass| mass + 112.985586  },
    "M-H+HCOONa"     => lambda { |mass| mass + 66.9802     }
  }.freeze
  MASS_TO_NEGATIVE_ION_MASS_DIMER = {
    "2M-H"           => lambda { |mass| 2*mass - 1.007276  },
    "2M+FA-H"        => lambda { |mass| 2*mass + 44.998201 },
    "2M+Hac-H"       => lambda { |mass| 2*mass + 59.013851 }
  }.freeze
  MASS_TO_NEGATIVE_ION_MASS_TRIMER = {
    "3M-H"           => lambda { |mass| 3*mass - 1.007276  }
  }.freeze
  MASS_TO_NEGATIVE_ION_MASS_DOUBLE_CHARGE = {
    "M-2H"           => lambda { |mass| mass/2 - 1.007276  }
  }.freeze
  MASS_TO_NEGATIVE_ION_MASS_TRIPLE_CHARGE = {
    "M-3H"           => lambda { |mass| mass/3 - 1.007276  }
  }.freeze
end
