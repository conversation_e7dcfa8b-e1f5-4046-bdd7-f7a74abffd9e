class EiMs < ApplicationRecord
  include Spectrum

  has_many :peaks,
    dependent: :destroy,
    class_name: "EiMsPeak",
    source: :ei_ms_peaks
  scope :predicted, -> { where(predicted: true) }
  scope :experimental, -> { where(predicted: false) }
  def self.stats(options = {})
    counts = EiMs.for_database(options[:database]).group(:instrument_type).count
    counts.map do |key, value|
      name = key.present? ? "#{key} EI/MS Spectrum" : "EI/MS Spectrum"
      Statistic.new(name: name, value: value)
    end
  end

  def splash
    return if splash_key.nil?
    SpectrumHash.from_splash_string splash_key
  end

  def regenerate_splash_key
    peaks = self.peaks.map do |p|
      [p.mass_charge.to_f,p.intensity.to_f]
    end
    self.splash_key = SpectrumHash.from_peaks(peaks)
  end

  def to_export_xml
    super do |xml|
      # Only add the exported peaks to the xml
      peaks.to_xml(builder: xml, skip_types: true, skip_instruct: true)
    end
  end
end
