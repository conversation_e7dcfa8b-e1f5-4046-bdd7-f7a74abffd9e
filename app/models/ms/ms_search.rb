class MsSearch
  ALLOWED_TOLERANCE_UNITS = ['Da', 'ppm']
  ALLOWED_DIRECTIONS = %w[up down]
  ALLOWED_COLUMNS = %w[compound name formula adduct adduct_mass compound_mass delta]
  PER_PAGE_LIMIT = 2000
  MAX_TOLERANCE = 1.0

  DEFAULT_PER_PAGE = 10

  DBL_SCALE = 6

  attr_accessor :results, :query_mass, :mode,
    :tolerance_units, :search, :adducts, :delta_columns
  attr_writer :tolerance
  attr_reader :total, :sort_order, :sort_col

  def self.search(*args)
    self.new(*args)
  end

  def self.allowed_adducts
    Adduct.allowed_adducts + ['Unknown']
  end

  def self.allowed_databases
    Rails.cache.fetch('ms_search/allowed_dbs', expires_in: 12.hours) do
      Source.pluck(:name).map(&:downcase)
    end
  end

  def initialize(query_mass, mode, tolerance, tolerance_units, options={})
    @tolerance       = tolerance.blank? ? 0.1 : BigDecimal.new(tolerance)
    @page            = options[:page] || 1
    @per_page        = options[:per_page] || DEFAULT_PER_PAGE

    unless ALLOWED_TOLERANCE_UNITS.include?(tolerance_units)
      tolerance_units = ALLOWED_TOLERANCE_UNITS.first
    end
    @tolerance_units = tolerance_units

    @adduct_types =
      unless options[:adduct_type].blank? || options[:adduct_type].include?('Unknown')
        adduct_types = options[:adduct_type].map(&:chomp)
        adduct_types & MsSearch.allowed_adducts
      else
        nil
      end

    @mode =
      case mode
      when 'neutral'  then '0'
      when 'positive' then '+'
      when 'negative' then '-'
      else '0'
      end

    @sort_order =
      case options[:sort_order]
      when 'up'   then 'ASC'
      when 'down' then 'DESC'
      else 'ASC'
      end

    @database =
      unless options[:database].nil?
        if MsSearch.allowed_databases.include?(options[:database].downcase)
          options[:database].downcase
        else
          raise "#{options[:database]} is not in allowed databases: #{MsSearch.allowed_databases}"
        end
      else
        nil
      end

    @sort_col = options[:sort_col] || "delta"
    @delta_columns = {}

    if query_mass.blank?
      @query_mass = 175.0
      @results    = []
    else
      @query_mass = BigDecimal.new(query_mass)
      self.get_results(@query_mass)
    end
  end

  def tolerance
    return @converted_tolerance if @converted_tolerance.present?

    # Formula for mass difference from PPM is ppm*query_mass/10^6
    @converted_tolerance = \
      if self.tolerance_units == 'ppm'
        self.class.ppm_to_dalton(@tolerance, @query_mass)
      else
        BigDecimal.new(@tolerance)
      end
    @converted_tolerance = MAX_TOLERANCE if @converted_tolerance > MAX_TOLERANCE
    @converted_tolerance
  end

  protected

  def get_results(query_mass)
    min = (query_mass - self.tolerance).round(DBL_SCALE)
    max = (query_mass + self.tolerance).round(DBL_SCALE)

    ids = Adduct.where("adduct_mass BETWEEN ? AND ? AND adduct_type = ?", min, max, @mode).pluck(:id)
    query = Adduct.select("adduct, adduct_type, adduct_mass,
                          abs(adduct_mass - #{query_mass}) AS delta,
                          (abs(adduct_mass - #{query_mass})/adduct_mass)*1000000 AS ppm,
                          database_registrations.database_id AS database_id,
                          structures.exact_mass, structures.formula, structures.id").
                   joins(:sources).
                   where(id: ids)
    query = query.where(sources: { name: @database }) if @database.present?

    if @adduct_types.present?
      query = query.where(adduct: @adduct_types.map { |a| a.gsub(/(^\[|\][+-]$)/, "") })
    end

    @results = query.
      order("#{@sort_col} #{@sort_order}").
      limit(PER_PAGE_LIMIT)
    @total = @results.length
  end

  private

  def self.ppm_to_dalton(ppm, mass)
    BigDecimal.new(mass) * BigDecimal.new(ppm) / 1_000_000
  end
end
