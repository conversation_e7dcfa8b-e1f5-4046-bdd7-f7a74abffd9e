json_to_html = (container, obj) ->
  ul = $("<ul>")
  for prop of obj
    li = $("<li>")
    li.append "<span class=\"json-key\">" + prop + ": </span>"
    if typeof obj[prop] is "object"
      json_to_html li, obj[prop]
    else
      li.append "<span class=\"json-value\">" + obj[prop] + "</span>"
    ul.append li
  container.append ul
  return

$ ->
  if $(".wrangler-result").length
    $(".wrangler-result").each ->
      json_to_html($(this), $(this).data("json"))
