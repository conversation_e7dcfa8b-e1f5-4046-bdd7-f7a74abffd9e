# Place all the behaviors and hooks related to the matching controller here.
# All this logic will automatically be available in application.js.
# You can use CoffeeScript in this file: http://jashkenas.github.com/coffee-script/

 
class @SpectrumChart

	x_title: "X title"
	y_title: "Y title"
	x_getter: (d) -> +d.x
	y_getter: (d) -> +d.y

	# Returns an array of Chart objects
	@buildAll: (select_string, options={}) ->
		klass = this
		d3.selectAll(select_string).map (temp) -> new klass(d3.select(this))
	
	
	# Override this to change the set up for the sidebar	
	setup_chart_info: ->
		@peak_assignment_list = @chart_info.append("div")
			.attr("class","peak-assignment-list")

	# Override this method to change the behavior that
	# occurs when you mouseover a peak
	mouseover_peak: (peak) =>
		@draw_peak_assignments(peak)

	# Draw the assignments
	draw_peak_assignments: (peak) =>
		peak.fetch_assignments (data) =>
			assignments = @peak_assignment_list
				.selectAll("div.peak-assignment")
                # need to specify a key for the data otherwise
                # it just uses index and doesn't update properly
				.data(data, (d) -> d.id)	
				
			assignments.enter().append("div")
				.attr("class","peak-assignment")
				.append("img").attr("src", (d) -> d.path )

			assignments.exit().remove()
			

	# Override this method to change the orientation of
	# the graph. 
	orient_canvas: (canvas) =>
		canvas.attr("transform", "translate(" + (@width + @margin.left) + "," + ( @height + @margin.top ) + ") scale(-1,-1)")
	
	orient_x_rules: (x_rules) =>
		x_rules.attr("transform","scale(1,-1)")
	
	orient_x_rule_numbers: (x_rule_numbers) =>
		  x_rule_numbers.attr("transform", "scale(-1,1)")

	constructor: (@chart) ->
		@width  = 500 
		@height = 300
		@zoom_window_height = 80
		@margin = 
			top:    10
			right:  10
			bottom: 60
			left:   60

		@peak_assignments = {}

		@setup_chart()
		@setup_chart_info()
		
		# Grab the data-url from the spectrum-chart div
		@data_url = @chart.attr("data-url")

		@fetch_data @data_url, @x_getter, @y_getter, @draw_chart


	fetch_data: (data_url,x_getter,y_getter, draw_callback) ->
		drawer = this
		d3.csv @peaks_data_url(), (data) ->
			# Convert strings to numbers.
			data.forEach (peak,i) ->
				peak.index = i
				peak.x     = +x_getter(peak)
				peak.y     = +y_getter(peak)
				peak.fetch_assignments = (fetch_callback) ->
					drawer.peak_assignments_fetcher(peak, fetch_callback)

			drawer.peaks = data
			draw_callback.call(drawer)


	# This method is added to the peak object
	peak_assignments_fetcher: (peak,fetch_callback) =>
		if not peak.assignments?
			if peak.has_assignments == "true"
				# This functions asynchronously so we need to have
				# a callback for when it is done fetching, but 
				# just call it right away after it is fetched
				d3.csv @peak_assignments_data_url(peak), (data) ->
					peak.assignments = data
					fetch_callback( peak.assignments )			
			else peak.assignments = []
		else fetch_callback( peak.assignments )
	

	peaks_data_url: ->
		@data_url + "/peaks.csv"

	peak_assignments_data_url: (peak) ->
		@data_url + "/peaks/" + peak.specdb_id + "/assignments.csv"


	# Setup the container elements for the chart
	setup_chart: ->
		@chart_title = @chart.text()
		@chart.text("")
		@specdiv = @chart.append("div")
			.attr("class","spectrum")
		@specdiv.append("h1").text(@chart_title)

		@specsvg = @specdiv
			.append("svg")
			.style("padding-left", "30px")
			.attr("width",  @width  + @margin.left + @margin.right)
			.attr("height", @height + @margin.top  + @margin.bottom)
		
		@y_rules_container = @specsvg.append("g")

		# Draw the peaks here
		@canvas = @specsvg	
			.append("g")
			.call(@orient_canvas)

		# Add a container for the toolbar
		@append_toolbar()

		# Add a container for the chart info
		@append_chart_info()

	append_chart_info: ->
		# A container for info about the spectrum
		@chart_info = @chart.append("div")
			.attr("class","chart-info")

	append_toolbar: ->
		@char_toolbar = @chart.append("div")
			.attr("class","toolbar")
			

	draw_chart: (data) ->
		x = 
			max: d3.max( @peaks, (d) -> d.x )
			min: d3.min( @peaks, (d) -> d.x )
		
		x.scale = d3.scale.linear()
			.range([0, @width])
			.domain([x.min,x.max])
			.nice()

		y = 
			max: d3.max( @peaks, (d) -> d.y )
			min: 0

		y.scale = d3.scale.linear()
			.range([0, @height - 40])
			.domain([y.min, y.max])
			.nice()
		
		# Always include 0 on the chemical shift scale
		x.min = if x.min < 0.0 then x.min else 0.0

		@draw_y_axis(y)
		@draw_x_axis(x)
		@draw_peaks(@peaks,x,y)
	
	draw_peaks: (data,x,y) ->
		# A container to hold the spectrum peaks.
		@spectrum_peaks = @canvas.append("g")

		# Draw a line for each peak
		@spectrum_peaks.selectAll(".peak")
		  .data(data).enter().append("line")
		  .attr("class","peak")
		  .attr("y1", y.min)
		  .attr("y2", (d) => y.scale( d.y ) )
		  .attr("x1", (d) => x.scale( d.x ) )
		  .attr("x2", (d) => x.scale( d.x ) )
		  .on("mouseover", @mouseover_peak)

	draw_x_axis: (x) ->
		# Create an axis generator
		x_axis_generator = d3.svg.axis()
			.orient("bottom")
			.scale(x.scale)

		# A container to hold the x-axis rules
		@x_rules = @canvas.append("g")
			.attr("class", "axis")
			.call(@orient_x_rules)
			.call(x_axis_generator)

		# unmirror the title labels  
		@x_rules.selectAll("text")
			.call(@orient_x_rule_numbers)
		
		# add a title for the x-axis  
		@specsvg.append("text")
		  .attr("class","title")
		  .attr("x",  @width/2 + @margin.left)
		  .attr("dy", @height  + @margin.top + @margin.bottom/2 + 10)
		  .attr("text-anchor", "middle")
		  .text(@x_title)


	draw_y_axis: (y) ->
		# A container to hold the y-axis rules
		@y_rules_container
			.attr("transform", "translate("+@margin.left+","+(@<EMAIL>)+") scale(1,-1)" ) 

		# Add rules to show the intensity values.
		# add a group for the rules
		@y_rules = @y_rules_container.selectAll(".rule")
		  .data( y.scale.ticks(10) )
		  .enter().append("g")
		  .attr("class", "rule")
		  .attr("transform", (d) -> "translate(0," + y.scale(d) + ")")
	
		# add horizontal rule lines
		@y_rules.append("line")
		  .attr("x2",@width)
	
		# add numbering on the left
		@y_rules.append("text")
			.attr("x",-6)
			.attr("dy",".35em")
			.attr("text-anchor", "end")
			.text( (d) -> d )
			.attr("transform","scale(1,-1)")
	
		# Add a title for the y axis
		@specsvg.append("text")
			.attr("class","title")
			.attr("transform","rotate(-90)")
			.attr("y",@margin.left/2 - 10)   # actually the x on the graph because of rotation
			.attr("x", -@height/2 - @margin.top ) # actually the y on the graph because of rotation
			.attr("text-anchor", "middle")
			.text(@y_title)


class @MsMsChart extends @SpectrumChart
	x_title: "Mass Charge (m/z)"
	y_title: "Intensity"
	x_getter: (d) -> +d.mass_charge
	y_getter: (d) -> +d.intensity

	orient_canvas: (canvas) =>
		canvas.attr("transform", "translate(" + (@margin.left) + "," + ( @height + @margin.top ) + ") scale(1,-1)")
	
	orient_x_rules: (x_rules) =>
		x_rules.attr("transform","scale(1,-1)")
	
	orient_x_rule_numbers: (x_rule_numbers) =>



class @NmrOneDChart extends @SpectrumChart
	x_title: "Chemical Shift"
	y_title: "Intensity"
	x_getter: (d) -> +d.shift
	y_getter: (d) -> +d.intensity
	 


	
 
