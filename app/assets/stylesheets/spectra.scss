// Place all the styles related to the spectras controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/
div.spectrum-chart {
  margin: 30px;
  border: solid #CCC 1px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  overflow: hidden;
  width: 701px;
  /*background: #e7e7e7;*/

  .spectrum  {
    margin-left: 0;
    border-right: solid #CCC 1px;
    padding: 30px;
    height: 392px;
    width: 600px;
    -webkit-box-shadow: inset 0px 0px 20px #DDD;
    -moz-box-shadow: inset 0px 0px 20px #DDD;
    box-shadow: inset 0px 0px 20px #DDD;
    background: #fff;
    float: left;

    h1 {
      line-height:18px;
      margin:0;
    }

    .rule {
      stroke: #eee;
    }

    .rule text, .axis text {
      stroke: black;
      stroke-width: 0;
      color: black;
      font-weight: normal;
      font: 10px sans-serif;
    }

    line.peak  {
      stroke: #2C17B1;
    }

    line.peak:hover {
      stroke-width: 4;
      stroke: #2DD7DD;
    }

    .brush rect.extent {
      fill: steelblue;
      fill-opacity: .125;
    }

    .brush .resize path {
      fill: #eee;
      stroke: #666;
    }

    .axis path,
    .axis line {
      fill: none;
      stroke: black;
      shape-rendering: crispEdges;
    }

    text.title {
      font-size: 12px;
    }
  }

  .toolbar {
    float: left;
    width: 40px;
    height: 452px;
    position: relative;
    background: #eee;
    -webkit-border-top-right-radius: 5px;
    -moz-border-top-right-radius: 5px;
    border-top-right-radius: 5px;
  }

  .chart-info {
    clear: both;
    border-top: 1px solid #CCC;
    //height: 200px;

    table { margin-right: -10px; }

    td {
      min-width: 60px;
      text-align: right;
    }

    th {text-align: left;}

  }

  div.peak-assignment-list  {
    padding: 10px;
    overflow: hidden;

    .peak-assignment {
      float: left;
      margin: 10px;
      border: 1px solid #CCC;
      padding: 10px;
      -moz-border-radius: 5px;
      -webkit-border-radius: 5px;
      border-radius: 5px;
      -moz-box-shadow: inset 0 0 10px 2px #EEE;
      -webkit-box-shadow: inset 0 0 10px 2px #EEE;
      box-shadow: inset 0 0 10px 2px #EEE;

      img {
        height: 128px;
      }

    }
  }

}
