.well.search
  = form_tag simple_search_path, class: 'form-horizontal' do
    .form-group
      label.col-sm-2.control-label Resource
      .col-sm-3
        = select_tag "database", 
            options_for_select(Source.pluck(:name).sort, params[:database]), 
            include_blank: true, 
            class: 'form-control'
    .form-group
      label.col-sm-2.control-label Structure
      .col-sm-6
        = text_area_tag "structure", params[:structure], class: 'form-control'
    .form-group
      label.col-sm-2.control-label Format
      .col-sm-1
        = select_tag :format, 
            options_for_select([:html, :json], params[:format]), 
            class: 'form-control'
    .form-group
      label.col-sm-2.control-label Search type
      .col-sm-6
        - st = params[:search_type].to_s.to_sym
        - st = :similarity if st.blank?
        .radio
          = label_tag :type_similarity, class: 'radio-inline' do
            = radio_button_tag 'search_type', 'similarity', st == :similarity,
                id: 'type_similarity'
            ' Similarity
        .radio
          = label_tag :type_substructure, class: 'radio-inline' do
            = radio_button_tag 'search_type', 'substructure', st == :substructure,
                id: 'type_substructure'
            ' Substructure
        .radio
          = label_tag :type_exact, class: 'radio-inline' do
            = radio_button_tag 'search_type', 'exact', st == :exact,
                id: 'type_exact'
            ' Exact
    .form-group
      .col-sm-offset-2.col-sm-10 = submit_tag 'Search', class: 'btn btn-primary'
