json.molecules @database_registrations do |database_registration|
  json.id database_registration.database_id
  json.resource database_registration.source.name
  json.tags database_registration.structure.tag_list
  
  if similarity = @hits[database_registration.structure.jchem_id][:similarity]
    json.similarity similarity
  end

  json.partial! '/database_registrations/structure', 
    structure: database_registration.structure
  json.partial! '/database_registrations/formats', 
    structure: database_registration.structure
  json.partial! '/database_registrations/properties', 
    structure: database_registration.structure
end
json.total @total_count
