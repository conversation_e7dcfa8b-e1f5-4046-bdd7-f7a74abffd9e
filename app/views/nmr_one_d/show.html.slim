
.page-header
  h1 = title("Showing NMR 1D spectrum")

h2 Spectrum details
dl.dl-horizontal
  dt Inchi key
  dd = @spectrum.inchi_key
  dt Structure
  /dd = moldb_thumbnail(@spectrum.structure.try(:to_param))
  dt Notes
  dd = @spectrum.notes
  dt Sample Concentration
  dd #{@spectrum.sample_concentration} #{@spectrum.sample_concentration_units}
  dt Sample Mass
  dd #{@spectrum.sample_mass} #{@spectrum.sample_mass_units}
  dt Sample Assessment
  dd = @spectrum.sample_assessment
  dt Spectra Assessment
  dd = @spectrum.spectra_assessment
  dt Sample Source
  dd = @spectrum.sample_source
  dt Collection Date
  dd = @spectrum.collection_date
  dt Instrument Type
  dd = @spectrum.instrument_type
  dt Sample Temperature
  dd #{@spectrum.sample_temperature} #{@spectrum.sample_temperature_units}
  dt Nucleus
  dd = @spectrum.nucleus
  dt Frequency
  dd = @spectrum.frequency
  dt Chemical shift reference
  dd = @spectrum.chemical_shift_reference
  dt Sample pH
  dd = @spectrum.sample_ph
  dt Solvent
  dd = @spectrum.solvent

h2 References
- @spectrum.references.each do |reference|
  blockquote
    ' #{reference.database} #{reference.database_id}
    br
    ' #{bio_link_out(:pubmed, reference.pubmed_id)}

h2 Documents
- @spectrum.documents.each do |document|
  blockquote
    = document.description
    br
    = link_to "Download file", document.file.url

h2 Peaks
table.table-condensed.table-striped
  thead
    th Shift
    th Intensity
  tbody
    - @spectrum.peaks.each do |peak|
      tr
        td = peak.chemical_shift
        td = peak.intensity
