json.extract! spectrum,
  :id, :inchi_key, :notes, :sample_concentration, :solvent, :sample_mass,
  :sample_assessment, :spectra_assessment, :sample_source,
  :collection_date, :instrument_type, :peak_counter,
  :nucleus, :frequency, :sample_ph, :predicted,
  :sample_temperature, :chemical_shift_reference, :searchable,
  :sample_concentration_units, :sample_mass_units,
  :sample_temperature_units, :structure_id, :exported, :genus, :species,
  :data_source_type,  :spectrometer_frequency_MHz, :fid_complex_points,
  :spectrum_real_points, :linewidth, :fid_first_point_multiplier,
  :center_frequency_ppm, :sweep_width_ppm, :simulated, :created_at, :updated_at
