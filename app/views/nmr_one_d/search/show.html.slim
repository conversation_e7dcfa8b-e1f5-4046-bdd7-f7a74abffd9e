h1 NMR 1D Spectrum Search

= render 'new_form'

/= debug @search

p
  = link_to "Download results as CSV", nmr_one_d_search_path(id: @search.to_param, format: :csv)

table.table.table-striped
  thead
    tr
      th Spectrum ID
      th Match Ratio
      th Jaccard Index
  tbody
    - @search.results.each do |id,r|
      tr
        td = link_to id, nmr_one_d_path(id: id)
        td = "#{r[:intersect_count]}/#{r[:union_count]}"
        td = r[:jaccard_index].round(3)

