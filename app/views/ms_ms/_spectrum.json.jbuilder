json.extract! spectrum,
  :id, :inchi_key, :notes, :sample_concentration, :solvent,
  :sample_mass, :sample_assessment, :spectra_assessment, :splash_key,
  :sample_source, :collection_date, :instrument_type,
  :peak_counter, :mono_mass, :energy_field, :collision_energy_level,
  :collision_energy_voltage, :ionization_mode,
  :sample_concentration_units, :sample_mass_units,
  :structure_id, :predicted, :created_at, :updated_at
