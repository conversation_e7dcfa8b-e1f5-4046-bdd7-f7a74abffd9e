doctype html
html
  head
    meta name="viewport" content="width=device-width, initial-scale=1.0"
    meta content="text/html; charset=UTF-8" http-equiv="Content-Type"
    title = site_title(content_for(:title))
    = stylesheet_link_tag "application", media: 'all', 
        "data-turbolinks-track" => true
    = javascript_include_tag "application", media: 'all', 
        "data-turbolinks-track" => true
    = csrf_meta_tags
  body[class="#{params[:controller].parameterize.dasherize}-c \
              #{params[:action].parameterize}-a"
       data-c=params[:controller].parameterize 
       data-a=params[:action].parameterize
      data-spy="scroll" 
      data-target=".sidenav"]
    header == render 'layouts/application/navigation'
    main role="main"
      == render '/layouts/application/notice'
      == render '/layouts/application/messages'
      == yield
    footer
      = wishart_support_3