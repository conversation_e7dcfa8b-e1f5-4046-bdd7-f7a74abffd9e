# json.extract! @curation, :created_at, :updated_at
# json.molecule_id @database_registration.database_id
# json.exported @database_registration.exported
# json.identifiers @result['identifiers']
# json.synonyms @combined_synonyms
# json.classifications @result['classifications']
# json.descriptions @result['descriptions']
# json.references @result['references']
# json.cs_descriptions @result['cs_descriptions']
json.extract! @curation, :created_at, :updated_at
json.molecule_id @database_registration.database_id
json.exported @database_registration.exported
json.identifiers @curation.as_json['identifiers']
json.synonyms @combined_synonyms
json.classifications @curation.as_json['classifications']
json.descriptions @curation.as_json['descriptions']
json.references @curation.as_json['references']
json.citations @curation.as_json['citations']
json.goodscents_occurrences @curation.as_json['goodscents_occurrences']
json.goodscents_properties @curation.as_json['goodscents_properties']
json.knapsack_organisms @curation.as_json['knapsack_organisms']
json.cs_descriptions @curation.as_json['cs_descriptions']
