.page-header: h1
  = title("Showing curation #{@result['identifiers']['name']}")
table.curation-table.content-table.table-condensed
  tbody
    = table_header_divider  "Record Information"
    tr
      th Version
      td = Rails.application.config.version
    tr
      th Creation date
      td =  @curation["created_at"]
    tr
      th Update date
      td = @curation["updated_at"]
    tr
      th Primary ID
      td: strong = @database_registration.database_id

    = table_header_divider "Metabolite Identification", id: 'identification'
    tr
      th MolDB Name
      td: strong = @result["identifiers"]["name"]
    tr
      th Description
      td = nah @result["cs_description"]
    tr
      th CAS Number
      td = nah @result["identifiers"]["cas"]
    tr
      th Structure
      td = image_tag "http://moldb.np-mrd.org/molecules/#{@result["structures"]["inchikey"]}/image.png"
    tr
      th Synonyms
      td
        table.display.simple-datatable
          td.data-table-container
            table.table-inner
              thead
                tr
                  th.head-large Value
                  th Source
              tbody
                - @combined_synonyms.each do |r|
                  tr
                    td = r["name"]
                    td = r["source"]
      tr
        th Chemical Formula
        - if @result["basic_properties"].select{|property| property["type"] == "formula"}.any? && !@result["basic_properties"].select{|property| property["type"] == "formula"}[0]["value"].nil?
          td = @result["basic_properties"].select{|property| property["type"] == "formula"}[0]["value"]
        - else
          td = nah
      tr
        th Average Molecular Weight
        - if @result["basic_properties"].select{|property| property["type"] == "average_mass"}.any? && !@result["basic_properties"].select{|property| property["type"] == "average_mass"}[0]["value"].nil?
          td = @result["basic_properties"].select{|property| property["type"] == "average_mass"}[0]["value"]
        - else
          td = nah
      tr
        th Monoisotopic Molecular Weight
        - if @result["basic_properties"].select{|property| property["type"] == "mono_mass"}.any? && !@result["basic_properties"].select{|property| property["type"] == "mono_mass"}[0]["value"].nil?
          td = @result["basic_properties"].select{|property| property["type"] == "mono_mass"}[0]["value"]
        - else
          td = nah
      tr
        th IUPAC Name
        td = nah @result["identifiers"]["iupac_name"]
      tr
        th CAS Registry Number
        td = nah @result["identifiers"]["cas"]
      tr
        th SMILES
        td
          .wrap= @result["structures"]["smiles"]
      tr
        th InChI Identifier
        td: .wrap = @result["structures"]["inchi"]
      tr
        th InChI Key
        td = nah @result["structures"]["inchikey"]
    = table_header_divider "Chemical Taxonomy", id: 'taxonomy'
      - if @result["classifications"].any?
        tr
          th Description
          - if @result["classifications"][0]["classyfire_description"].present?
            td = nah @result["identifiers"]["name"] + " " +@result["classifications"][0]["classyfire_description"]
          - else
            td = nah nil
        tr
          th Kingdom
          - if @result["classifications"][0]["kingdom"].present?
            td = nah link_to(@result["classifications"][0]["kingdom"]["name"],@result["classifications"][0]["kingdom"]["url"]) 
          - else
            td = nah nil
        tr
          th Super Class
          - if @result["classifications"][0]["superklass"].present?
            td = nah link_to(@result["classifications"][0]["superklass"]["name"],@result["classifications"][0]["superklass"]["url"]) 
          - else
            td = nah nil
        tr
          th Class
          - if @result["classifications"][0]["klass"].present?
            td = nah link_to(@result["classifications"][0]["klass"]["name"],@result["classifications"][0]["klass"]["url"]) 
          - else
            td = nah nil
        tr
          th Sub Class
          - if @result["classifications"][0]["subklass"].present?
            td = nah link_to(@result["classifications"][0]["subklass"]["name"],@result["classifications"][0]["subklass"]["url"]) 
          - else
            td = nah nil
        tr
          th Direct Parent
          - if @result["classifications"][0]["direct_parent"].present?
            td = nah link_to(@result["classifications"][0]["direct_parent"]["name"],@result["classifications"][0]["direct_parent"]["url"])  
          - else
            td = nah nil
        tr
          th Alternative Parents
          td
            = list(@result["classifications"][0]["alternative_parents"]) do |alt_parent|
              = alt_parent["name"]
        tr
          th Substituents
          td
            = list(@result["classifications"][0]["substituents"]) do |sub|
              = sub
        tr
          th Molecular Framework
          td = @result["classifications"][0]["molecular_framework"]  
        tr
          th External Descriptors
          - if @result["classifications"][0]["external_descriptors"].present?
            td: ul
              - @result["classifications"][0]["external_descriptors"].each do |ed|
                - ed["annotations"].each do |annotation|
                  li #{annotation} (#{ed["id"]})
          - else
            td = nah       
      - else
        tr
          th Classification
          td = nah nil, 'Not classified'
    
    = table_header_divider "Physical Properties", id: 'physical_properties'
      tr
        th State
        td = nah nah @result["properties"]["state"]
      tr
        th Experimental Properties
        td
          table class="table table-bordered"
            col width = "250"
            col width = "250"
            col width = "500"
            thead
              tr
                th Property
                th Value
            tbody
              tr
                td Melting Point
                td = nah @result["properties"]["melting_point"]
              tr
                td Boiling Point
                td = nah @result["properties"]["boiling_point"]
              tr
                td Water Solubility
                td = nah @result["properties"]["solubility"]
      tr
        th Predicted Properties
        td
          table class="table table-bordered"
            col width = "250"
            col width = "250"
            col width = "500"
            thead
              tr
                th Property
                th Value
                th Source
            tbody
              - if @result["basic_properties"].select{|property| property["type"] == "solubility"}.any? && !@result["basic_properties"].select{|property| property["type"] == "solubility"}[0]["value"].nil?
                tr
                  td Water Solubility
                  td= @result["basic_properties"].select{|property| property["type"] = "solubility"}[0]["value"] + " g/L"
                  td= @result["basic_properties"].select{|property| property["type"] = "solubility"}[0]["source"]
              - if @result["basic_properties"].select{|property| property["type"] == "logp"}.any? && !@result["basic_properties"].select{|property| property["type"] == "logp"}[0]["value"].nil?
                tr
                  td logP
                  td= @result["basic_properties"].select{|property| property["type"] == "logp"}[0]["value"].to_f.smart_round_to_s
                  td= @result["basic_properties"].select{|property| property["type"] == "logp"}[0]["source"]
              - if @result["basic_properties"].select{|property| property["type"] == "logs"}.any? && !@result["basic_properties"].select{|property| property["type"] == "logs"}[0]["value"].nil?
                tr
                  td logS
                  td= @result["basic_properties"].select{|property| property["type"] == "logs"}[0]["value"].to_f.smart_round_to_s
                  td= @result["basic_properties"].select{|property| property["type"] == "logs"}[0]["source"]
              - if @result["basic_properties"].select{|property| property["type"] == "pka_strongest_acidic"}.any? && !@result["basic_properties"].select{|property| property["type"] == "pka_strongest_acidic"}[0]["value"].nil?
                tr
                  td pKa (Strongest Acidic)
                  td= @result["basic_properties"].select{|property| property["type"] == "pka_strongest_acidic"}[0]["value"].to_f.smart_round_to_s
                  td= @result["basic_properties"].select{|property| property["type"] == "pka_strongest_acidic"}[0]["source"]
              - if @result["basic_properties"].select{|property| property["type"] == "pka_strongest_basic"}.any? && !@result["basic_properties"].select{|property| property["type"] == "pka_strongest_basic"}[0]["value"].nil?
                tr
                  td pKa (Strongest Basic)
                  td= @result["basic_properties"].select{|property| property["type"] == "pka_strongest_basic"}[0]["value"].to_f.smart_round_to_s
                  td= @result["basic_properties"].select{|property| property["type"] == "pka_strongest_basic"}[0]["source"]
              - if @result["basic_properties"].select{|property| property["type"] == "physiological_charge"}.any? && !@result["basic_properties"].select{|property| property["type"] == "physiological_charge"}[0]["value"].nil?
                tr
                  td Physiological Charge
                  td= @result["basic_properties"].select{|property| property["type"] == "physiological_charge"}[0]["value"]
                  td= @result["basic_properties"].select{|property| property["type"] == "physiological_charge"}[0]["source"]
              - if @result["basic_properties"].select{|property| property["type"] == "acceptor_count"}.any? && !@result["basic_properties"].select{|property| property["type"] == "acceptor_count"}[0]["value"].nil?
                tr
                  td Hydrogen Acceptor Count
                  td= @result["basic_properties"].select{|property| property["type"] == "acceptor_count"}[0]["value"]
                  td= @result["basic_properties"].select{|property| property["type"] == "acceptor_count"}[0]["source"]
              - if @result["basic_properties"].select{|property| property["type"] == "donor_count"}.any? && !@result["basic_properties"].select{|property| property["type"] == "donor_count"}[0]["value"].nil?
                tr
                  td Hydrogen Donor Count
                  td= @result["basic_properties"].select{|property| property["type"] == "donor_count"}[0]["value"]
                  td= @result["basic_properties"].select{|property| property["type"] == "donor_count"}[0]["source"]
              - if @result["basic_properties"].select{|property| property["type"] == "polar_surface_area"}.any? && !@result["basic_properties"].select{|property| property["type"] == "polar_surface_area"}[0]["value"].nil?
                tr
                  td Polar Surface Area
                  td= @result["basic_properties"].select{|property| property["type"] == "polar_surface_area"}[0]["value"].to_f.smart_round_to_s
                  td= @result["basic_properties"].select{|property| property["type"] == "polar_surface_area"}[0]["source"]
              - if @result["basic_properties"].select{|property| property["type"] == "rotatable_bond_count"}.any? && !@result["basic_properties"].select{|property| property["type"] == "rotatable_bond_count"}[0]["value"].nil?
                tr
                  td Rotatable Bond Count
                  td= @result["basic_properties"].select{|property| property["type"] == "rotatable_bond_count"}[0]["value"]
                  td= @result["basic_properties"].select{|property| property["type"] == "rotatable_bond_count"}[0]["source"]
              - if @result["basic_properties"].select{|property| property["type"] == "polarizability"}.any? && !@result["basic_properties"].select{|property| property["type"] == "polarizability"}[0]["value"].nil?
                tr
                  td Polarizability
                  td= @result["basic_properties"].select{|property| property["type"] == "polarizability"}[0]["value"].to_f.smart_round_to_s + " Å³"
                  td= @result["basic_properties"].select{|property| property["type"] == "polarizability"}[0]["source"]
              - if @result["basic_properties"].select{|property| property["type"] == "refractivity"}.any? && !@result["basic_properties"].select{|property| property["type"] == "refractivity"}[0]["value"].nil?
                tr
                  td Refractivity
                  td= @result["basic_properties"].select{|property| property["type"] == "refractivity"}[0]["value"].to_f.smart_round_to_s + " m³·mol⁻¹"
                  td= @result["basic_properties"].select{|property| property["type"] == "refractivity"}[0]["source"]
              - if @result["basic_properties"].select{|property| property["type"] == "number_of_rings"}.any? && !@result["basic_properties"].select{|property| property["type"] == "number_of_rings"}[0]["value"].nil?
                tr
                  td Number of Rings
                  td= @result["basic_properties"].select{|property| property["type"] == "number_of_rings"}[0]["value"]
                  td= @result["basic_properties"].select{|property| property["type"] == "number_of_rings"}[0]["source"]
              - if @result["basic_properties"].select{|property| property["type"] == "bioavailability"}.any? && !@result["basic_properties"].select{|property| property["type"] == "bioavailability"}[0]["value"].nil?
                tr
                  td Bioavailability
                  td= @result["basic_properties"].select{|property| property["type"] == "bioavailability"}[0]["value"].to_f.smart_round_to_s
                  td= @result["basic_properties"].select{|property| property["type"] == "bioavailability"}[0]["source"]
              - if @result["basic_properties"].select{|property| property["type"] == "rule_of_five"}.any? && !@result["basic_properties"].select{|property| property["type"] == "rule_of_five"}[0]["value"].nil?
                tr
                  td Rule of Five
                  td= @result["basic_properties"].select{|property| property["type"] == "rule_of_five"}[0]["value"]
                  td= @result["basic_properties"].select{|property| property["type"] == "rule_of_five"}[0]["source"]
              - if @result["basic_properties"].select{|property| property["type"] == "ghose_filter"}.any? && !@result["basic_properties"].select{|property| property["type"] == "ghose_filter"}[0]["value"].nil?
                tr
                  td Ghose Filter
                  td= @result["basic_properties"].select{|property| property["type"] == "ghose_filter"}[0]["value"]
                  td= @result["basic_properties"].select{|property| property["type"] == "ghose_filter"}[0]["source"]
              - if @result["basic_properties"].select{|property| property["type"] == "veber_rule"}.any? && !@result["basic_properties"].select{|property| property["type"] == "veber_rule"}[0]["value"].nil?
                tr
                  td Veber Rule
                  td= @result["basic_properties"].select{|property| property["type"] == "veber_rule"}[0]["value"]
                  td= @result["basic_properties"].select{|property| property["type"] == "veber_rule"}[0]["source"]
              - if @result["basic_properties"].select{|property| property["type"] == "mddr_like_rule"}.any? && !@result["basic_properties"].select{|property| property["type"] == "mddr_like_rule"}[0]["value"].nil?
                tr
                  td MDDR-like Rule
                  td= @result["basic_properties"].select{|property| property["type"] == "mddr_like_rule"}[0]["value"]
                  td= @result["basic_properties"].select{|property| property["type"] == "mddr_like_rule"}[0]["source"]

