table.curation-table.content-table.table-condensed
  tbody
=begin  
    = show_header "Record Information", curation
    tr
      th Version
      td = Rails.application.config.version
    tr
      th Creation date
      td = curation.created_at
    tr
      th Update date
      td = curation.updated_at
    tr
      th Primary ID
      td: strong = curation.public_id
    tr
    = show_header 'Chemical Information', curation

    tr
      th MolDB Name
      td: strong = curation["identifiers"]["name"]
    tr
      th Description
      td = nah curation["cs_description"]
    tr
      th CAS Number
      td = nah curation["identifiers"]["cas"]
   
    tr
      th Synonyms
      td
        td.data-table-container
        table.table-inner
          thead
            tr
              th.head-large Value
              th Source
          tbody
            - result["synonyms"].each do .each do |k, v| 
              tr
                td = nah k
                td = nah v

    tr
      th Predicted Properties
        - if result["basic_properties"].any?
            td.data-table-container 
            table class="table table-bordered"
            col width = "250"
            col width = "250"
            col width = "500"
            thead
              tr
                th Property
                th Value
                th Source
            tbody
              - if result["basic_properties"].select{|property| property.type == "solubility"}.any? && !result["basic_properties"].select{|property| property.type == "solubility"}[0].value.nil?
                tr
                  td Water Solubility
                  td= result["basic_properties"].select{|property| property.type = "solubility"}[0].value + " g/L"
                  td= f result["basic_properties"].select{|property| property.type = "solubility"}[0].source
             - if result["basic_properties"].select{|property| property.type == "logp"}.any? && !result["basic_properties"].select{|property| property.type == "logp"}[0].value.nil?
                tr
                  td logP
                  td= result["basic_properties"].select{|property| property.type = "logp"}[0].value
                  td= f result["basic_properties"].select{|property| property.type = "logp"}[0].source
             - if result["basic_properties"].select{|property| property.type == "logs"}.any? && !result["basic_properties"].select{|property| property.type == "logs"}[0].value.nil?
                tr
                  td logS
                  td= result["basic_properties"].select{|property| property.type = "logs"}[0].value
                  td= f result["basic_properties"].select{|property| property.type = "logs"}[0].source
             - if result["basic_properties"].select{|property| property.type == "pka_strongest_acidic"}.any? && !result["basic_properties"].select{|property| property.type == "pka_strongest_acidic"}[0].value.nil?
                tr
                  td pKa (Strongest Acidic)
                  td= result["basic_properties"].select{|property| property.type == "pka_strongest_acidic"}[0].value
                  td= f result["basic_properties"].select{|property| property.type == "pka_strongest_acidic"}[0].source
             - if result["basic_properties"].select{|property| property.type == "pka_strongest_basic"}.any? && !result["basic_properties"].select{|property| property.type == "pka_strongest_basic"}[0].value.nil?
                tr
                  td pKa (Strongest Basic)
                  td= result["basic_properties"].select{|property| property.type == "pka_strongest_basic"}[0].value
                  td= f result["basic_properties"].select{|property| property.type == "pka_strongest_basic"}[0].source
             - if result["basic_properties"].select{|property| property.type == "physiological_charge"}.any? && !result["basic_properties"].select{|property| property.type == "physiological_charge"}[0].value.nil?
                tr
                  td Physiological Charge
                  td= result["basic_properties"].select{|property| property.type == "physiological_charge"}[0].value
                  td= f result["basic_properties"].select{|property| property.type == "physiological_charge"}[0].source
              - if record.respond_to?(:moldb_acceptor_count) && !record.moldb_acceptor_count.nil?
                tr
                  td Hydrogen Acceptor Count
                  td= record.moldb_acceptor_count
                  td= bio_link_to_chemaxon :ha_count
              - if record.respond_to?(:moldb_donor_count) && !record.moldb_donor_count.nil?
                tr
                  td Hydrogen Donor Count
                  td= record.moldb_donor_count
                  td= bio_link_to_chemaxon :hd_count
              - if record.respond_to?(:moldb_polar_surface_area) && !record.moldb_polar_surface_area.nil?
                tr
                  td Polar Surface Area
                  td= record.moldb_polar_surface_area.to_f.smart_round_to_s + " Å²"
                  td= bio_link_to_chemaxon :psa
              - if record.respond_to?(:moldb_rotatable_bond_count) && !record.moldb_rotatable_bond_count.nil?
                tr
                  td Rotatable Bond Count
                  td= record.moldb_rotatable_bond_count
                  td= bio_link_to_chemaxon :rb_count
              - if record.respond_to?(:moldb_refractivity) && !record.moldb_refractivity.nil?
                tr
                  td Refractivity
                  td= record.moldb_refractivity.to_f.smart_round_to_s + " m³·mol⁻¹"
                  td= bio_link_to_chemaxon :refractivity
              - if record.respond_to?(:moldb_polarizability) && !record.moldb_polarizability.nil?
                tr
                  td Polarizability
                  td= record.moldb_polarizability.to_f.smart_round_to_s + " Å³"
                  td= bio_link_to_chemaxon :polarizability
              - if record.respond_to?(:moldb_number_of_rings) && !record.moldb_number_of_rings.nil?
                tr
                  td Number of Rings
                  td= record.moldb_number_of_rings
                  td= bio_link_to_chemaxon :rb_count
              - if record.respond_to?(:moldb_bioavailability) && !record.moldb_bioavailability.nil?
                tr
                  td Bioavailability
                  td= record.moldb_bioavailability.to_f.smart_round_to_s
                  td= bio_link_to_chemaxon :polarizability
              - if record.respond_to?(:moldb_rule_of_five) && !record.moldb_rule_of_five.nil?
                tr
                  td Rule of Five
                  td= human_boolean(record.moldb_rule_of_five)
                  td= bio_link_to_chemaxon :polarizability
              - if record.respond_to?(:moldb_ghose_filter) && !record.moldb_ghose_filter.nil?
                tr
                  td Ghose Filter
                  td= human_boolean(record.moldb_ghose_filter)
                  td= bio_link_to_chemaxon :polarizability
              - if record.respond_to?(:moldb_veber_rule) && !record.moldb_veber_rule.nil?
                tr
                  td Veber's Rule
                  td= human_boolean(record.moldb_veber_rule)
                  td= bio_link_to_chemaxon :polarizability
              - if record.respond_to?(:moldb_mddr_like_rule) && !record.moldb_mddr_like_rule.nil?
                tr
                  td MDDR-like Rule
                  td= human_boolean(record.moldb_mddr_like_rule)
                  td= bio_link_to_chemaxon :polarizability
        - else
          td = nah
      tr
        th Chemical Formula
        td = curation.moldb_formula
      tr
        th IUPAC name
        td = curation.moldb_iupac
      tr
        th InChI Identifier
        td = curation.moldb_inchi
      tr
        th InChI Key
        td = curation.moldb_inchikey
      tr
        th Isomeric SMILES
        td = curation.moldb_smiles
      tr
        th Average Molecular Weight
        td = curation.moldb_average_mass
      tr
        th Monoisotopic Molecular Weight
        td = curation.moldb_mono_mass
=begin
    = show_header 'Classification', curation

    - if curation.classyfired?
      = moldb_classification_table(curation)
    - else 
      tr
        th Classification
        td = nah nil, 'Not classified'

    = show_header 'Physico-Chemical Properties - Experimental', curation
    tr
      th Physico-Chemical Properties - Experimental
      td
        table.display.simple-datatable
          thead
            tr
              th Property
              th Value
              th Reference
          tbody
            tr
              th Physical state
              td = nah curation.state
              td =
            tr
              th Physical Description
              td = nah curation.physical_description
              td = curation.physical_description_reference
            tr
              th Mass Composition
              td = nah curation.percent_composition
              td = curation.percent_composition_reference
            tr
              th Melting Point
              td = nah curation.melting_point
              td = curation.melting_point_reference
            tr
              th Boiling Point
              td = nah curation.boiling_point
              td = curation.boiling_point_reference
            tr
              th Experimental Water Solubility
              td = nah curation.experimental_solubility
              td = curation.experimental_solubility_reference
            tr
              th Experimental logP
              td = nah curation.experimental_logp
              td = curation.experimental_logp_reference
            tr
              th Experimental pKa
              td = nah curation.experimental_pka
              td = curation.experimental_pka_reference
            tr
              th Isoelectric point
              td = nah curation.isoelectric_point
              td = curation.isoelectric_point_reference
            tr
              th Charge
              td = nah curation.charge
              td = curation.charge_reference
            tr
              th Optical Rotation
              td = nah curation.optical_rotation
              td = curation.optical_rotation_reference
            tr
              th Spectroscopic UV Data
              td = nah curation.uv_index
              td = curation.uv_index_reference
            tr
              th Density
              td = nah curation.density
              td = curation.density_reference
            tr
              th Refractive Index
              td = nah curation.refractive_index
              td = curation.refractive_index_reference
    = table_header_divider 'Spectra'
    tr
      th Spectra
      - if @curation.moldb_inchikey
        td = specdb_spectra_list(@curation)
      - else
        td = nah

    = show_header 'External Links', curation
    tr
      th ChemSpider ID
      td = nah bio_link_out :chemspider, curation.chemspider_id
    tr
      th ChEMBL ID
      td = nah bio_link_out :chembl, curation.chembl_id
    tr
      th KEGG curation ID
      td = nah bio_link_out :kegg_curation, curation.kegg_curation_id
    tr
      th Pubchem curation ID
      td = nah bio_link_out :pubchem_curation, curation.pubchem_curation_id
    tr
      th Pubchem Substance ID
      td = nah bio_link_out :pubchem_substance, curation.pubchem_substance_id
    tr
      th ChEBI ID
      td = nah bio_link_out :chebi, curation.chebi_id
    tr
      th Phenol-Explorer ID
      td = nah bio_link_out :phenol_curation, curation.phenolexplorer_id
    tr
      th DrugBank ID
      td = nah bio_link_out :drugbank, curation.drugbank_id
    tr
      th HMDB ID
      td = nah bio_link_out :hmdb, curation.hmdb_id
    tr
      th CRC / DFC (Dictionary of Food curations) ID
      td = nah bio_link_out :dfc, curation.dfc_id
    tr
      th EAFUS ID
      td = nah bio_link_out :eafus, curation.eafus_id
    tr
      th Dr. Duke ID
      td = nah bio_link_out :duke, curation.duke_id
    tr
      th BIGG ID
      td = nah bio_link_out :bigg, curation.bigg_id
    tr
      th KNApSAcK ID
      td = nah bio_link_out :knapsack, curation.knapsack_id
    tr
      th HET ID
      td = nah bio_link_out :het, curation.het_id

    - if curation.protein?
      tr
        th | UniProt ID
        td = nah bio_link_out :uniprot, curation.uniprot_id
      tr
        th | UniProt name
        td = nah bio_link_out :uniprot, curation.uniprot_name
      tr
        th | Genbank ID
        td = nah bio_link_out :genbank, curation.genbank_id
      tr
        th | PDB identifiers
        td
          = list(curation.pdb_identifiers) { |id| bio_link_out(:pdb, id.pdb_id) }
    tr
      th Flavornet ID
      td = nah bio_link_out :flavornet, curation.flavornet_id
    tr
      th GoodScent ID
      td = nah bio_link_out :goodscent, curation.goodscent_id
    tr
      th SuperScent ID
      td = nah bio_link_out :superscent, curation.superscent_id
    tr
      th Wikipedia ID
      td = nah bio_link_out :wikipedia, curation.wikipedia_id
    tr
      th Phenol-Explorer Metabolite ID
      td
        = nah bio_link_out :phenol_metabolite, curation.phenolexplorer_metabolite_id
    tr
      th Duplicate IDS
      td
        = list(curation.duplicate_id.to_s.split('|')) do |id| 
          = id.to_s.split(/:/).first.humanize + ": "
          = bio_link_out id.to_s.split(/:/).first, id.to_s.split(/:/).second
    tr
      th Old DFC IDS
      td = nah curation.old_dfc_id

    = show_header 'Associated Foods', curation
    tr
      - if !afcdb
        td colspan=2
          table.display.food-contents[data-source=dt_food_index_curation_contents_path(curation)]
            thead
              tr
                th Food
                th.no-sort Content Range 
                th Average
                th Reference
            tfoot
              tr
                th Food
                th 
                th
                th Reference
            tbody
      - else
        td colspan=2
          table.display.food-contents[data-source=dt_afcdb_food_index_curation_contents_path(curation)]
            thead
              tr
                th Food
                th.no-sort Content Range 
                th Average
                th Reference
            tfoot
              tr
                th Food
                th 
                th
                th Reference
            tbody

    = show_header 'Biological Effects and Interactions', curation
    tr
      th Health Effects / Bioactivities
      td.data-table-container
        - if @curation.curations_health_effects.blank?
          = nah
        - else
          table.display.simple-datatable
            thead
              tr
                th Descriptor
                th ID
                th Definition
                th Reference
            tbody
              - curation.curations_health_effects.each do |he|
                tr
                  td = link_to he.health_effect.name, health_effect_path(he.health_effect)
                  td = bio_link_out :chebi, he.health_effect.chebi_id
                  td = he.health_effect.chebi_definition
                  td = display_citation(he.citation)
    / tr
    /   th Health effects description
    /   td = nah
    / tr
    /   th Health benefits
    /   td = nah
    / tr
    /   th Adverse effects
    /   td = nah
    / tr
    /   th Mechanisms of action
    /   td = nah
    tr
      th Enzymes
      td.data-table-container
        - if @curation.curations_enzymes.blank?
          = nah
        - else
          table.display.simple-datatable
            thead
              tr
                th Name
                th Gene Name
                th UniProt ID
            tbody
              - curation.curations_enzymes.each do |e|
                tr
                  td = e.enzyme.name
                  td = e.enzyme.gene_name
                  td = bio_link_out :uniprot, e.enzyme.uniprot_id
    tr
      th Pathways
      td.data-table-container
        - if @curation.pathways.blank?
          = nah
        - else
          table.display.simple-datatable
            thead
              tr
                th Name
                th SMPDB Link
                th KEGG Link
            tbody
              - @curation.pathways.each do |p|
                tr
                  td = nah p.name
                  td
                    - if p.smpdb_id.present?
                      = link_to "#{p.smpdb_id} <span class=\"glyphicon glyphicon-new-window\"> </span>".html_safe, bio_url(:smpdb, p.smpdb_id) + "?highlight[curations][]=#{@curation.hmdb_id}", class: 'wishart-link-out', target: "_blank"
                    - else
                      = nah
                  td
                    - if p.kegg_map_id.present?
                      = bio_link_out :kegg_map, p.kegg_map_id
                    - else
                      = nah
    / tr
    /   th Biosynthesis
    /   td = nah
    / tr
    /   th Absorption
    /   td = nah
    / tr
    /   th Distribution
    /   td = nah
    tr
      th Metabolism
      td = nah curation.metabolism
    tr
      th Biosynthesis
      td = nah
    / tr
    /   th Excretion
    /   td = nah

    = show_header 'Organoleptic Properties', curation
    tr
      th Flavours
      td
        - if @curation.flavors.blank?
          = nah
        - else
          table.display.simple-datatable
            thead
              tr
                th Flavor
                th Citations
            tbody
              - curationsFlavor.where(source_id: curation.id, source_type: "curation").each do |bond|
                tr
                  td = link_to bond.flavor.name, flavor_path(bond.flavor) 
                  td == textilize bond.citations
    / tr
    /   th Role in foods
    /   td = nah
    / tr
    /   th Aroma
    /   td = nah
    / tr
    /   th Taste
    /   td = nah

    / = show_header 'Targets', curation
    / = show_header 'Transporters', curation
    / = show_header 'Metabolites', curation
    / = show_header 'Bioavailability / Pharmacokinetic values', curation
    / tr
    /   th CMax
    /   td = nah
    / tr
    /   th tMax
    /   td = nah
    / tr
    /   th t1/2
    /   td = nah
    / tr
    /   th AUC
    /   td = nah
    / tr
    /   th Urinary excretion
    /   td = nah
    / tr
    /   th Fecal excretion
    /   td = nah

    = show_header 'Files', curation
    tr
      th MSDS
      td
        - if curation.msds?
          = link_to 'show', curation.msds.url
        - else 
         = nah

    = table_header_divider 'References', id: 'references'
    tr
      th Synthesis Reference
      td = nah curation.synthesis_citations
    tr
      th General Reference
      td == nah textilize curation.general_citations
    tr
      th Content Reference
      td 
        - curation.contents.select('distinct citation').map do |c|
          = link_citation(c.citation)
=end