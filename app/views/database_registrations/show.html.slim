.page-header
  .actions
    = link_to 'JSON', molecule_path(@database_registration, format: :json), class: 'primary'
    = link_to 'Similar structures',
        simple_search_path(database_id: @database_registration.database_id,
                           search_type: :similarity),
        class: 'primary'
    = link_to 'Back', molecules_path, class: 'general'
  h1 = title("Showing #{@database_registration.source.name} record #{@database_registration.database_id}")

= render '/structures/identification', structure: @structure
= render '/structures/database_registrations', structure: @structure
= render '/structures/formats', structure: @structure
= render '/structures/properties', structure: @structure
= link_to 'Back', structures_path
= render '/structures/structure_modal', structure: @structure
