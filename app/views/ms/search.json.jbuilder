json.array! @hits do |hit|
  json.query_mass hit.query_mass
  json.tolerance hit.tolerance
  json.adducts hit.adducts
  json.sort_order hit.sort_order
  json.sort_col hit.sort_col
  json.total hit.total

  json.results hit.results do |adduct|
    json.compound_mass adduct.exact_mass
    json.formula adduct.formula
    json.adduct adduct.adduct
    json.adduct_type adduct.adduct_type
    json.adduct_mass adduct.adduct_mass
    json.delta adduct.delta
    json.compound_id adduct.database_id
  end
end
