json.extract! spectrum,
  :id, :inchi_key, :notes, :sample_concentration, :solvent, :sample_mass,
  :sample_assessment, :spectra_assessment, :sample_source,
  :collection_date, :instrument_type, :peak_counter,
  :nucleus_x, :nucleus_y, :frequency, :sample_ph, :sample_temperature,
  :chemical_shift_reference, :sample_concentration_units,
  :sample_mass_units, :sample_temperature_units,
  :structure_id, :searchable, :predicted, :exported,
  :created_at, :updated_at
