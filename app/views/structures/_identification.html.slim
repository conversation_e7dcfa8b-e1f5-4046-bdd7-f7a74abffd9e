h2 Identification
.well.identification-well
  .details
    dl.dl-horizontal
      dt MolDB ID:
      dd = structure.id
      dt Tags:
      dd == structure.tags.map { |t| content_tag(:span, t.name, class: :tag) }.join(' ')
      dt InChI Key:
      dd = structure.inchikey
      dt Formula:
      dd = structure.formula
      dt Molecular weight:
      dd = structure.molecular_weight
      dt Exact mass:
      dd = structure.exact_mass

      dt Created:
      dd = structure.created_at
      dt Updated:
      dd = structure.updated_at
  .structure
    = image_tag(thumb_molecule_path(structure.inchikey, format: :png),
                class: 'thumbnail')
    button data-target="#structure-zoom" data-toggle="modal" type="button" Zoom In
