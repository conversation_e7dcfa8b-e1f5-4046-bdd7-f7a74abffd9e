# Load our properties into a hash for faster access
props = {}
structure.properties.each { |p| props["#{p.source}_#{p.name}".to_sym] = p.value }

json.formula structure.formula
json.iupac props[:jchem_iupac]
json.traditional_iupac props[:jchem_traditional_iupac]
json.alogps_solubility props[:alogps_solubility]
json.alogps_logp props[:alogps_logp]
json.alogps_logs props[:alogps_logs]
json.average_mass structure.molecular_weight
json.mono_mass structure.exact_mass
json.pka props[:jchem_pka]
json.pka_strongest_acidic props[:jchem_pka_strongest_acidic]
json.pka_strongest_basic props[:jchem_pka_strongest_basic]
json.logp props[:jchem_logp]
json.acceptor_count props[:jchem_acceptor_count]
json.donor_count props[:jchem_donor_count]
json.rotatable_bond_count props[:jchem_rotatable_bond_count]
json.polar_surface_area props[:jchem_polar_surface_area]
json.refractivity props[:jchem_refractivity]
json.polarizability props[:jchem_average_polarizability]
json.formal_charge props[:jchem_formal_charge]
json.physiological_charge props[:jchem_physiological_charge]
json.number_of_rings props[:jchem_number_of_rings]
json.rule_of_five props[:jchem_rule_of_five]
json.bioavailability props[:jchem_bioavailability]
json.ghose_filter props[:jchem_ghose_filter]
json.veber_rule props[:jchem_veber_rule]
json.mddr_like_rule props[:jchem_mddr_like_rule]
