h2 Database Registrations
.well.registrations-well
  table.table.table-striped.table-condensed
    thead
      tr
        th Database
        th Database ID
        th Registered on
        th
    tbody
      - structure.database_registrations.each do |database_registration|
        tr
          td = database_registration.source.name
          td = database_registration.database_id
          td = database_registration.created_at
          td = link_to 'details', molecule_path(database_registration),
                  class: 'btn btn-default btn-xs'
