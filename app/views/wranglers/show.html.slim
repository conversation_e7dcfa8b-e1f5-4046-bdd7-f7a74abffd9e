.panel.panel-default
  .panel-heading Wrangler Details
  .panel-body
    dl.dl-horizontal
      dt Name:
      dd = @wrangler.name
      dt Runner:
      dd= @wrangler.runner
      dt Query:
      dd= @wrangler.query
      dt Complete:
      dd= human_boolean(@wrangler.complete?)
      dt Started at:
      dd= @wrangler.started_at
      dt Finished at:
      dd= @wrangler.finished_at

    = link_to 'Back', wranglers_path

.panel.panel-default
  .panel-heading Wrangler Results
  .panel-body
    - @wrangler.wrangler_curations.each do |wrangler_curation|
      h2 Query: #{wrangler_curation.query}
      - if wrangler_curation.curation.present?
        .wrangler-result data-json=wrangler_curation.curation.result
      - else
        .alert.alert-info Still wrangling!