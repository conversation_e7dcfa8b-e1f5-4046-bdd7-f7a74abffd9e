if spectrum.all_documents.present?
  json.documents do
    spectrum.documents.each do |document|
      json.child! do
        json.name document.file_file_name
        json.description document.description
        json.url "http://moldb.np-mrd.org#{document.file.url}"
      end
    end

    spectrum.sops.each do |sop|
      json.child! do
        json.name sop.file_file_name
        json.description sop.name
        json.url "http://moldb.np-mrd.org#{sop.file.url}"
      end
    end

    # BMRB is only for NMR
    # if ["NmrOneD", "NmrTwoD"].include? spectrum.class.name
    #   spectrum.bmrb_records.each do |bmrb_record|
    #     json.child! do
    #       json.name bmrb_record.name
    #       json.description bmrb_record.description
    #       json.url bmrb_record.url
    #     end
    #   end
    # end
  end
else
  json.documents []
end
