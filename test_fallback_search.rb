#!/usr/bin/env ruby

# Test the fallback search implementation
require_relative 'lib/chem_search'

puts "Testing ChemSearch fallback implementation..."

# Test structure from the user's request
test_structure = "[H]C1=C([H])C([H])=C2C(=C1[H])N1C(=O)C([H])([H])[C@]3([H])OC([H])([H])C([H])=C4C([H])([H])N5C([H])([H])C([H])([H])[C@]22[C@]5([H])C([H])([H])[C@]4([H])[C@]3([H])[C@]12[H]"

# Test parameters
test_params = {
  'search_type' => 'similarity',
  'similarity' => '0.7',
  'resource' => 'natural_products',
  'max_results' => '100'
}

puts "\n=== Testing Fallback Search ==="
puts "Test structure: #{test_structure[0..50]}..."
puts "Search type: #{test_params['search_type']}"

# Test RDKit availability check
puts "\n1. Testing RDKit availability:"
available = ChemSearch.rdkit_available?
puts "  RDKit available: #{available}"

# Test fallback search directly
puts "\n2. Testing fallback search method:"
begin
  # Create a mock structure scope (we'll use an empty relation for testing)
  mock_scope = Struct.new(:find_each) do
    def find_each
      # Mock some test structures
      test_structures = [
        OpenStruct.new(
          id: 1,
          jchem_id: 1001,
          original_structure: test_structure,
          formula: "C20H24N2O2",
          molecular_weight: 324.42
        ),
        OpenStruct.new(
          id: 2,
          jchem_id: 1002,
          original_structure: "CCO",
          formula: "C2H6O",
          molecular_weight: 46.07
        )
      ]
      
      test_structures.each { |s| yield s }
    end
  end.new
  
  results = ChemSearch.fallback_search(test_structure, mock_scope, { search_type: 'exact' })
  puts "  Fallback search results: #{results.length} hits"
  
  if results.any?
    puts "  First result:"
    puts "    Structure ID: #{results.first[:structure_id]}"
    puts "    JChem ID: #{results.first[:jchem_id]}"
    puts "    Similarity: #{results.first[:similarity]}"
  end
rescue => e
  puts "  Error: #{e.message}"
end

puts "\n=== Test Complete ==="
