to_recalculate = [ :inchi, :inchikey ]
Molecule.joins(:formats).where("formats.name = 'inchi' AND formats.value NOT LIKE 'InChI=1S%'").each do |m|
  begin
    inchi = ChemConvert.convert(m.structure, MOLECULE_FORMATS[:inchi])
    if inchi =~ /InChI=1\//
      puts "#{m.source.name} #{m.database_id}"
      next
    end
    next if inchi =~ /InChI=1\//
    inchikey = ChemConvert.convert(m.structure, MOLECULE_FORMATS[:inchikey])

    m.formats.where("name = 'inchi' OR name = 'inchikey'").delete_all
    m.formats.create!(name: 'inchi', value: inchi)
    m.formats.create!(name: 'inchikey', value: inchikey)

    puts "Saved inchi/inchikey for #{m.database_id} to #{inchi}"
  rescue Savon::Error => error
    puts error.to_s
  end
end