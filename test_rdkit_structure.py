#!/usr/bin/env python3

import sys
from rdkit import Chem
from rdkit.Chem import rdMolDescriptors
from rdkit import DataStructs

# Test structure from the user's request
test_structure = "[H]C1=C([H])C([H])=C2C(=C1[H])N1C(=O)C([H])([H])[C@]3([H])OC([H])([H])C([H])=C4C([H])([H])N5C([H])([H])C([H])([H])[C@]22[C@]5([H])C([H])([H])[C@]4([H])[C@]3([H])[C@]12[H]"

print("=== RDKit Structure Test ===")
print(f"Testing structure: {test_structure[:50]}...")

def parse_structure(structure):
    """Try to parse structure using different RDKit methods"""
    print(f"\nTrying to parse: {structure[:100]}...")
    
    # Try SMILES first
    print("1. Trying as SMILES...")
    mol = Chem.MolFromSmiles(structure)
    if mol is not None:
        print("   ✓ Successfully parsed as SMILES")
        canonical = Chem.MolToSmiles(mol, canonical=True)
        print(f"   Canonical SMILES: {canonical}")
        return mol
    else:
        print("   ✗ Failed to parse as SMILES")
    
    # Try MOL block
    print("2. Trying as MOL block...")
    mol = Chem.MolFromMolBlock(structure)
    if mol is not None:
        print("   ✓ Successfully parsed as MOL block")
        return mol
    else:
        print("   ✗ Failed to parse as MOL block")
    
    # Try InChI
    print("3. Trying as InChI...")
    try:
        mol = Chem.MolFromInchi(structure)
        if mol is not None:
            print("   ✓ Successfully parsed as InChI")
            return mol
        else:
            print("   ✗ Failed to parse as InChI")
    except:
        print("   ✗ Exception when parsing as InChI")
    
    print("   ✗ All parsing methods failed")
    return None

# Test parsing
mol = parse_structure(test_structure)

if mol is not None:
    print(f"\n=== Molecule Analysis ===")
    print(f"Number of atoms: {mol.GetNumAtoms()}")
    print(f"Number of bonds: {mol.GetNumBonds()}")
    print(f"Molecular formula: {Chem.rdMolDescriptors.CalcMolFormula(mol)}")
    
    # Test fingerprint generation
    print(f"\n=== Fingerprint Generation ===")
    try:
        fp = rdMolDescriptors.GetMorganFingerprintAsBitVect(mol, 2, nBits=2048)
        print(f"Morgan fingerprint generated: {fp.GetNumBits()} bits")
        print(f"Number of set bits: {fp.GetNumOnBits()}")
    except Exception as e:
        print(f"Error generating fingerprint: {e}")
    
    # Test canonical SMILES
    print(f"\n=== Canonical SMILES ===")
    try:
        canonical = Chem.MolToSmiles(mol, canonical=True)
        print(f"Canonical SMILES: {canonical}")
    except Exception as e:
        print(f"Error generating canonical SMILES: {e}")
        
else:
    print("\n=== Analysis Failed ===")
    print("Could not parse the structure with any method")
    
    # Let's try to understand what type of structure this might be
    print(f"\nStructure analysis:")
    print(f"Length: {len(test_structure)}")
    print(f"Contains '[H]': {'[H]' in test_structure}")
    print(f"Contains '@': {'@' in test_structure}")
    print(f"Contains '=': {'=' in test_structure}")
    print(f"Starts with: {test_structure[:20]}")
    print(f"Ends with: {test_structure[-20:]}")

print("\n=== Test Complete ===")
