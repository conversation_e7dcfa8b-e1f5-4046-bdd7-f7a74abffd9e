# Introduction
MolDB (AKA "the structure server") is a Ruby on Rails web application designed to act as the central hub for small molecule structures in the lab.

# Accessing MolDB via REST
MolDB is designed to be a RESTful web service. In order to hook it up to your application, you need to use a RESTful client such as [Rails ActiveResource](http://api.rubyonrails.org/files/vendor/rails/activeresource/README.html) or [CPAN ActiveResource](http://search.cpan.org/~gugod/ActiveResource-0.01/lib/ActiveResource.pm) (Perl).

Standard CRUD (Create Read Update Delete) operations are supported via the standard REST calls (POST, GET, PUT, DELETE). Additionally, a structure search interface is provided.

The easiest way to get started (in Rails applications) is to install [MolDBi](https://bitbucket.org/wishartlab/moldbi).

# Accessing the JSON Interface
Each entry can be accessed in XML format using a standard URL (in the example below, the DrugBank structure with DrugBank ID DB000316 is used):

http://moldb.wishartlab.com/molecules/DB00316.json

Or perform similarity searches and get the results in JSON:

http://moldb.wishartlab.com/molecules/search.json?smiles=CCCC&search_type=3&similarity=0.7&database=drugbank

Note you can specify which database you want to search with the "database" parameter. If you leave this parameter off, all databases are searched.

## Accessing structures with Perl
Below is an example of how to get and create a structure using Perl and basic LWP tools:

``` perl
#!/usr/bin/perl
use LWP::UserAgent;
use LWP::Simple;

# Initialize the UserAgent:
my $ua = LWP::UserAgent->new;

# Grabbing a structure (with a unique ID):
my $get_req = HTTP::Request->new( GET => 'http://moldb.wishartlab.com/molecules/DB00316.xml');
$get_req->header('Content-Type' => 'text/xml');
my $get_response = $ua->request($get_req);
if ($get_response->is_success) {
    print $get_response->decoded_content;  # or whatever
}
else {
    die $get_response->content;
}

# Setting a structure for a new compound
my $mol = get('http://moldb.wishartlab.com/molecules/DB01048.mol'); # Here you would open up your mol file to import
my $database_id = "BLARRG25";
my $xml = "<molecule><database_name>test</database_name><database_id>${database_id}</database_id><structure>${mol}</structure></molecule>";
my $post_req = HTTP::Request->new( POST => 'http://moldb.wishartlab.com/molecules.xml');
$post_req->authorization_basic('molecules', 'sjApx14!');
$post_req->header('Content-Type' => 'text/xml');
$post_req->content($xml);
my $post_response = $ua->request($post_req);
if ($post_response->is_success) {
    print $post_response->decoded_content;  # or whatever
}
else {
    die $post_response->content;
}

# Deleting a structure
my $del_req = HTTP::Request->new( DELETE => 'http://moldb.wishartlab.com/molecules/BLARRG25.xml');
$del_req->header('Content-Type' => 'text/xml');
$del_req->authorization_basic('molecules', 'sjApx14!');
my $del_response = $ua->request($del_req);
if ($del_response->is_success) {
    print "Delete successful!\n" # or whatever
}
else {
    die $del_response->content;
}
``` 

# Downloading Structures
The structures for MolDB all follow the same format (in the examples below, the DrugBank structure with DrugBank ID DB000316 is used):

* Mol File: http://moldb.wishartlab.com/molecules/DB00316.mol
* SDF File: http://moldb.wishartlab.com/molecules/DB00316.sdf
* PDB File: http://moldb.wishartlab.com/molecules/DB00316.pdb
* Structure image (thumbnail): http://moldb.wishartlab.com/molecules/DB00316/image.png
* Structure image: http://moldb.wishartlab.com/molecules/DB00316/thumb.png

# Upgrading JChem Web Services (REST) on the server