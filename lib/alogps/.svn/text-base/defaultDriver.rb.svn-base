require 'alogps/default.rb'
require 'alogps/defaultMappingRegistry.rb'
require 'soap/rpc/driver'

class VccAlogpsService < ::SOAP::RPC::Driver
  DefaultEndpointUrl = "http://www.vcclab.org/web/services/ALOGPS"

  Methods = [
    [ "",
      "getAlogpsResults",
      [ ["in", "part", ["::SOAP::SOAPElement", "http://DefaultNamespace", "getAlogpsResults"]],
        ["out", "getAlogpsResultsReturn", ["::anyType", nil, "getAlogpsResultsReturn"]] ],
      { :request_style =>  :document, :request_use =>  :encoded,
        :response_style => :document, :response_use => :encoded,
        :faults => {} }
    ]
  ]

  def initialize(endpoint_url = nil)
    endpoint_url ||= DefaultEndpointUrl
    super(endpoint_url, nil)
    self.mapping_registry = DefaultMappingRegistry::EncodedRegistry
    self.literal_mapping_registry = DefaultMappingRegistry::LiteralRegistry
    init_methods
  end

private

  def init_methods
    Methods.each do |definitions|
      opt = definitions.last
      if opt[:request_style] == :document
        add_document_operation(*definitions)
      else
        add_rpc_operation(*definitions)
        qname = definitions[0]
        name = definitions[2]
        if qname.name != name and qname.name.capitalize == name.capitalize
          ::SOAP::Mapping.define_singleton_method(self, qname.name) do |*arg|
            __send__(name, *arg)
          end
        end
      end
    end
  end
end

