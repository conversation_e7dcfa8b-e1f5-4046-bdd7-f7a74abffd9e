class AlogpsResult
  require 'rexml/document'
  
  # REQUIRED_ELEMENTS = [ 'INPUTDATA', 'SMILES', 'LOGP', 'LOGPERR', 'LOGS' ]
  attr_accessor :input_data, :smiles, :logp, :logp_error, :logs, :logs_error, :solubility, :solubility_units
  
  def process_xml_result(xml)
    doc = REXML::Document.new(xml)
  
    # puts xml

    doc.root.elements.each("soapenv:Body/RESULTS/BATCH/MOLECULE") do |molecule|
      @input_data = molecule.elements["INPUTDATA"].text
      @smiles = molecule.elements["SMILES"].text
      
      if molecule.elements["LOGP"]
        @logp = molecule.elements["LOGP"].text
        @logp_error = molecule.elements["LOGPERR"].text
      end
      
      if molecule.elements["LOGS"]
        @logs = molecule.elements["LOGS"].text
        @logs_error = molecule.elements["LOGSERR"].text
      end
      
      if molecule.elements["SOLUBILITY"]
        @solubility = molecule.elements["SOLUBILITY"].text
        @solubility_units = molecule.elements["SOLUBILITY"].attributes["UNITS"]     
      end
    end
  end
  
end