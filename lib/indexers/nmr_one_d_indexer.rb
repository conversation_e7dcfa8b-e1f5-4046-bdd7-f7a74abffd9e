# Utility class for building indexes of simillar peaks for Nmr 1D
# spectra. The 
class NmrOneDIndexer
  attr_reader :cached_values, :nucleus

  # The bin widths are configured here, and all other pieces of code
  # that require these values should use the class methods
  # 
  # NmrOneDIndexer.bin_widths_for_nucleus '1H'
  #
  #  or
  #
  # NmrOneDIndexer.width_for_bin '1H', 0
  #
  PEAK_MATCH_BIN_WIDTHS = {
    '1H'  => [ 0.01, 0.02, 0.03, 0.04, 0.05 ].freeze,
    '13C' => [ 0.02, 0.05, 0.10, 0.15, 0.20 ].freeze
  }.freeze

  def initialize(nucleus)
    @cached_values = Hash.new
    # Raise an exception if the nucleus is not valid
    self.class.bin_widths_for_nucleus(nucleus)
    @nucleus       = nucleus
  end

  def self.bin_widths_for_nucleus(nucleus)
    PEAK_MATCH_BIN_WIDTHS[nucleus] || raise("Invalid nucleus: '#{nucleus}'")
  end

  def self.width_for_bin(nucleus,bin_number)
    bin_widths_for_nucleus(nucleus)[bin_number] ||
      raise( "Invalid peak match bin '#{i}'" )
  end

  def bin_widths
    self.class.bin_widths_for_nucleus(nucleus)
  end

  def bin_width(bin_number)
    self.class.width_for_bin(nucleus,bin_number)
  end

  def get_spectra_and_peaks
    NmrOneD.where( nmr_one_d: { nucleus: nucleus } ).includes(:peaks)
  end

  # Count the number of peaks within a given threshold
  def count_peaks_in_bin(chemical_shift, bin_width)
    chemical_shift = chemical_shift.to_f
    lower_bound    = chemical_shift - (bin_width.to_f/2.0)
    upper_bound    = chemical_shift + (bin_width.to_f/2.0)

    # Find the peaks that are within the width of the bin centered
    # on the peak
    NmrOneDPeak.joins(:nmr_one_d).where(
      nmr_one_d_peaks: { chemical_shift: lower_bound..upper_bound },
      nmr_one_d: { nucleus: nucleus }
    ).count - 1
  end

  def fill_bin_counts_with_cached_values(peak)
    # Use the cached bin counts if we have already seen this shift values
    cached_values[peak.chemical_shift].each.with_index do |bin_count,i|
      peak.send "unique_#{i}=", bin_count
    end
  end

  def fill_bin_counts_from_database_query(peak)
    # Count the number of peaks that fall into each bin tolerance and store
    # the count in unique_0, unique_1, ... , unique_4
    bin_widths.each.with_index do |tolerance,i|
      peak.send "unique_#{i}=", count_peaks_in_bin( peak.chemical_shift.to_f, tolerance )
    end

    # Cache the values to speed up further operations
    cached_values[peak.chemical_shift] = peak.unique_values
  end

  # Given a peak, counts the number of simlar peaks that fall into each bin
  # tolerance and stores the count in unique_0, unique_1, ... , unique_4
  def fill_bin_counts_for_peak(peak)
    unless cached_values[peak.chemical_shift].nil?
      fill_bin_counts_with_cached_values peak
    else
      fill_bin_counts_from_database_query peak
    end
  end

  def index!
    get_spectra_and_peaks.find_each do |spectrum|
      spectrum.peaks.each do |peak|
        #puts peak.id
        fill_bin_counts_for_peak peak
        peak.save!
      end
      # Mark the spectrum as searchable after processing the
      # associated peaks
      spectrum.searchable = true
      puts "Saving spectrum: #{spectrum.id}, #{spectrum.save}, #{spectrum.searchable}"
    end
  end
end
