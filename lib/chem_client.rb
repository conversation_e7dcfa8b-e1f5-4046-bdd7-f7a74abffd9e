module ChemClient
  def self.create_structure(structure)
    # Use RDKit to validate and standardize the structure
    script = <<~PYTHON
      import sys
      import json
      from rdkit import Chem
      
      with open(sys.argv[1], 'r') as f:
          structure = f.read().strip()
      
      # Validate structure
      mol = None
      if structure.startswith('InChI='):
          mol = Chem.MolFromInchi(structure)
      else:
          mol = Chem.MolFromSmiles(structure)
      
      result = {}
      if mol:
          # Generate a unique ID (using hash of canonical SMILES)
          smiles = Chem.MolToSmiles(mol, isomericSmiles=True)
          structure_id = abs(hash(smiles)) % (10 ** 8)  # Limit to 8 digits
          result = {
              "cd_id": structure_id,
              "message": "Structure inserted successfully"
          }
      else:
          result = {
              "message": "Invalid structure"
          }
      
      with open(sys.argv[2], 'w') as f:
          f.write(json.dumps(result))
    PYTHON
    
    result = Jchem.run_rdkit(script, structure)
    entry = JSON.parse(result)
    
    if entry['message'] =~ /inserted successfully/
      entry['cd_id'].abs
    else
      nil
    end
  end
  
  def self.update_structure(structure_id, structure)
    # For RDKit implementation, we'll validate the structure
    # and return success if valid
    script = <<~PYTHON
      import sys
      import json
      from rdkit import Chem
      
      with open(sys.argv[1], 'r') as f:
          data = f.read().strip().split('\\n')
          structure_id = int(data[0])
          structure = data[1]
      
      # Validate structure
      mol = None
      if structure.startswith('InChI='):
          mol = Chem.MolFromInchi(structure)
      else:
          mol = Chem.MolFromSmiles(structure)
      
      result = {}
      if mol:
          result = {
              "message": "Structure updated successfully"
          }
      else:
          result = {
              "message": "Invalid structure"
          }
      
      with open(sys.argv[2], 'w') as f:
          f.write(json.dumps(result))
    PYTHON
    
    result = Jchem.run_rdkit(script, "#{structure_id}\n#{structure}")
    JSON.parse(result)['message'].include?('updated successfully')
  end

  def self.delete_structure(structure_id)
    # Since we're not using a database with RDKit,
    # we'll just return success
    script = <<~PYTHON
      import sys
      import json
      
      with open(sys.argv[1], 'r') as f:
          structure_id = int(f.read().strip())
      
      result = {
          "message": "Structure deleted successfully"
      }
      
      with open(sys.argv[2], 'w') as f:
          f.write(json.dumps(result))
    PYTHON
    
    result = Jchem.run_rdkit(script, structure_id.to_s)
    JSON.parse(result)['message'].include?('deleted successfully')
  end

  def self.get_structure(structure_id, included_fields:[], additional_fields:{})
    script = <<~PYTHON
      import sys
      import json
      from rdkit import Chem
      from rdkit.Chem import Descriptors, rdMolDescriptors
      
      with open(sys.argv[1], 'r') as f:
          data = json.loads(f.read().strip())
          structure_id = data["structure_id"]
          included_fields = data["included_fields"]
          additional_fields = data["additional_fields"]
      
      # In a real implementation, you would retrieve the structure from storage
      # Here we're simulating with a placeholder structure
      # You would need to implement structure storage/retrieval
      
      # Placeholder - in production you'd retrieve the actual structure
      smiles = "CCO"  # Example structure
      mol = Chem.MolFromSmiles(smiles)
      
      result = {
          "id": structure_id,
          "structure": smiles
      }
      
      # Process included fields
      for field in included_fields:
          if field == "cd_formula":
              result["formula"] = rdMolDescriptors.CalcMolFormula(mol)
          elif field == "cd_molweight":
              result["molweight"] = Descriptors.MolWt(mol)
          # Add other fields as needed
      
      # Process additional fields (chemical terms)
      for field, term in additional_fields.items():
          if "mass()" in term:
              result[field] = Descriptors.MolWt(mol)
          elif "exactMass()" in term:
              result[field] = Descriptors.ExactMolWt(mol)
          elif "formula()" in term:
              result[field] = rdMolDescriptors.CalcMolFormula(mol)
          elif "atomCount()" in term:
              result[field] = mol.GetNumAtoms()
          # Add other chemical terms as needed
      
      with open(sys.argv[2], 'w') as f:
          f.write(json.dumps(result))
    PYTHON
    
    # Prepare input data
    input_data = {
      "structure_id" => structure_id,
      "included_fields" => included_fields,
      "additional_fields" => additional_fields
    }
    
    result = Jchem.run_rdkit(script, input_data.to_json)
    data = JSON.parse(result)
    
    # Convert to expected format
    entry = ActiveSupport::HashWithIndifferentAccess.new
    entry[:id] = data['id']
    entry[:structure] = data['structure']
    
    included_fields.each do |field|
      clean_field = field.sub(/cd_/, '')
      entry[clean_field] = data[clean_field]
    end
    
    additional_fields.keys.each do |field|
      entry[field] = data[field]
    end
    
    # JChem returns atom_count as -1 for R/S group structures...
    entry['atom_count'] = nil if entry['atom_count'].to_i < 0
    
    entry
  end
  
  def self.get_image(structure_id, width: 200, height: 200)
    script = <<~PYTHON
      import sys
      import json
      import base64
      from rdkit import Chem
      from rdkit.Chem import Draw
      
      with open(sys.argv[1], 'r') as f:
          data = json.loads(f.read().strip())
          structure_id = data["structure_id"]
          width = data["width"]
          height = data["height"]
      
      # In a real implementation, you would retrieve the structure from storage
      # Here we're simulating with a placeholder structure
      
      # Placeholder - in production you'd retrieve the actual structure
      smiles = "CCO"  # Example structure
      mol = Chem.MolFromSmiles(smiles)
      
      # Generate image
      img = Draw.MolToImage(mol, size=(width, height))
      
      # Convert to base64
      import io
      buffer = io.BytesIO()
      img.save(buffer, format="PNG")
      img_str = base64.b64encode(buffer.getvalue()).decode()
      
      with open(sys.argv[2], 'w') as f:
          f.write(img_str)
    PYTHON
    
    # Prepare input data
    input_data = {
      "structure_id" => structure_id,
      "width" => width,
      "height" => height
    }
    
    Jchem.run_rdkit(script, input_data.to_json)
  end
  
  # Initialize the config for moldb
  def self.initialize
    script = <<~PYTHON
      import sys
      import json
      
      # Simulate initialization
      result = {
          "dbConfigName": "#{JCHEM_CONFIG[:config]}",
          "message": "Initialization successful"
      }
      
      with open(sys.argv[2], 'w') as f:
          f.write(json.dumps(result))
    PYTHON
    
    result = Jchem.run_rdkit(script, "")
    entry = JSON.parse(result)
    
    if entry['dbConfigName'] == JCHEM_CONFIG[:config]
      sleep 10
      self.delete_structure(1)
    else
      raise 'could not create structure on initialization!'
    end
  end

  # Upgrade tables to the current version of JChem Web Services
  def self.upgrade
    script = <<~PYTHON
      import sys
      import json
      
      # Simulate upgrade
      result = {
          "message": "Upgrade successful"
      }
      
      with open(sys.argv[2], 'w') as f:
          f.write(json.dumps(result))
    PYTHON
    
    Jchem.run_rdkit(script, "")
  end
end
