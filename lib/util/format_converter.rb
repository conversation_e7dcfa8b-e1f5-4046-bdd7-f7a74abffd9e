module FormatConverter
  # CONVERTER_URL = "#{WS_CONFIG[:url]}/util/calculate/stringMolExport"

  ConversionResults = Struct.new(:results, :errors)

  def self.smiles_to_sdf(smiles)
    self.convert(self.clean_smiles(smiles), :sdf)
  end

  def self.smiles_to_inchi(smiles)
    self.convert(self.clean_smiles(smiles), "inchi:AuxNone")
  end

  def self.smiles_to_smiles_without_chirality(smiles)
    self.convert(self.clean_smiles(smiles), 'smiles:0')
  end

  def self.smiles_to_inchikey(smiles)
    self.convert(self.clean_smiles(smiles), 'inchikey')
  end

  def self.smiles_to_iupac_name(smiles)
    self.convert(self.clean_smiles(smiles), 'name:i')
  end

  def self.smiles_to_mol(smiles)
    self.convert(self.clean_smiles(smiles), :mol)
  end

  def self.smiles_to_canonical_smiles(smiles)
    self.convert(self.clean_smiles(smiles), 'smiles:u')
  end

  def self.smiles_to_smiles_with_explicit_hydrogens(smiles)
    self.convert(self.clean_smiles(smiles), 'smiles:H')
  end

  def self.inchi_to_smiles(inchi)
    if inchi =~ /InChI=/
      self.convert(inchi, 'smiles')
    else
      ConversionResults.new(nil,["The input is not an InChI"])
    end
  end

  def self.inchi_to_inchikey(inchi)
    if inchi =~ /InChI=/
      self.convert(inchi, 'inchikey')
    else
      ConversionResults.new(nil,["The input is not an InChI"])
    end
  end

  # Remove radical groups and atoms from the smiles (A,R)
  def self.clean_smiles(smiles)
    smiles.to_s.gsub("([R])","").
                gsub("(R)","").
                gsub("[R]","").
                gsub("[*]","").
                gsub("(*)","").
                gsub("*","").
                gsub(" ","")
  end

  protected

  def self.convert(input, format)
    options = { "structure" => input, "parameters" => format.to_s }
    format_conversion = ConversionResults.new(nil,[])
    rest_results = RestClient.post "#{WS_CONFIG[:url]}/util/calculate/stringMolExport", options.to_json, content_type: :json, accept: :json
    format_conversion.results = rest_results
    format_conversion
  end
end
