require 'set'
module <PERSON>accardMethods
  INT_SCALING = 10_000

  # Scale the value by INT_SCALING and then convert the value to an int
  def self.scale_to_int(value)
    if value == nil
      return 0
    else
      (value * INT_SCALING).to_i
    end
  end

  # Expects a list of obejcts with a peak_position_ppm method that
  # returns the chemical shift. Returns a set of peak_position_ppms scaled
  # by INT_SCALING and converted to integers. This is useful for
  # preparing spectra for the fast_jaccard method
  def self.optimize_spectrum(spectrum)
    spectrum.map{|v| scale_to_int(v.peak_position_ppm) }.sort.to_set
  end

  # Expects a list of peak_position_ppms as floats
  # Returns a set of peak_position_ppms scaled
  # by INT_SCALING and converted to integers. This is useful for
  # preparing spectra for the fast_jaccard method
  def self.optimize_peak_list(spectrum)
    spectrum.map{|v| scale_to_int(v) }.sort.to_set
  end

  # Jaccard index is defined as the size of the intersection divided by the size
  # of the union of the sample sets.
  #
  # If both are empty, we define the index as 1
  #
  # We match a library peak to a query peak if the difference between the peaks
  # is less than the tolerance. We can only match a peak once.
  #
  # Use fast_jaccard for search through the whole library of spectra. It is
  # more optimized and a bit faster than the regular jaccard.
  #
  # Use jaccard to return the matched peaks for a full comparison.
  #

  # Fast jaccard expects the lib_spectrum and query_spectrum to be ordered sets of
  # ints. (Int comparisons are faster than float comparisons)
  #
  # This comparison does not store the matches to futher increase the speed
  # of the calculation. To get the matched peaks back, use the normal jaccard
  # function.
  #
  #
  def self.fast_jaccard(lib_spectrum, query_spectrum, tolerance)
    # If both are empty, we define the index as 1
    return 1 if lib_spectrum.empty? && query_spectrum.empty?

    # Set up some data structures to store data
    # missed_peaks = 0
    # matches = 0
    missed_peaks  = Set.new
    matches = Hash.new{|h,k| h[k] = Array.new}
    # lib_spectrum = lib_spectrum.dup
    lib_spectrum   = lib_spectrum.sort.to_set
    query_spectrum = query_spectrum.sort.to_set

    query_spectrum.each do |query_peak|
      lib_spectrum.each do |lib_peak|
        if (lib_peak - query_peak).abs <= tolerance
          # if the difference between the query peak and
          # the lib peak is less than tolerance then it is
          # a match
          # matches += 1
          matches[query_peak] << lib_peak
          # we only match a query peak once, os we remove it from
          # the set once it is matched
          # lib_spectrum.delete(lib_peak)
          lib_spectrum.delete(lib_peak)
          # we only match a library peak once, so we can break
          # at this point
          break

        elsif query_peak > lib_peak
          # we go through the query_peaks and the lib_peaks in acsending
          # order so we can be sure that when the lib peak is larger than the
          # lib_peak that no other lib peaks will match it so we can remove
          # it from the set
          # missed_peaks += 1
          missed_peaks << lib_peak
          # lib_spectrum.delete(lib_peak)
          lib_spectrum.delete(lib_peak)

        else
          # if query_peak > lib_peak
          # we go through the query_peaks and the lib_peaks in acsending
          # order so we can be sure that when the query peak is larger than the
          # lib_peak and we don't have a match, that we can stop looking
          # for a match for this peak
          break
        end
      end
    end

    # intersect_count = matches
    # union_count     = missed_peaks + query_spectrum.count + lib_spectrum.count
    # jaccard_index   = intersect_count.to_f / union_count.to_f
    intersect_count = matches.keys.count
    union_count     = missed_peaks.count + query_spectrum.count + lib_spectrum.count
    # union_count     = (query_spectrum.count + lib_spectrum.count)-intersect_count
    jaccard_index   = intersect_count.to_f / union_count.to_f
    #return jaccard_index
    return {
      intersect_count: intersect_count,
      union_count:     union_count,
      jaccard_index:   jaccard_index,
    }
    # return {
    #   ratio: "#{intersect_count}/#{union_count}",
    #   matches: matches,
    #   missed_query_peaks: (missed_peaks+query_spectrum).to_a,
    #   missed_lib_peaks: (lib_spectrum-matches.keys).to_a,
    #   intersect_count: intersect_count,
    #   union_count: union_count,
    #   jaccard_index: jaccard_index
    # }
  end

  def self.jaccard(lib_spectrum, query_spectrum, tolerance, options={})
    return 1 if lib_spectrum.empty? && query_spectrum.empty?

    # Sort the input peaks and turn them into sets for efficient
    # delete operations
    lib_spectrum   = lib_spectrum.sort.to_set
    query_spectrum = query_spectrum.sort.to_set

    # Set up some data structures to store data
    missed_peaks  = Set.new
    matches = Hash.new{|h,k| h[k] = Array.new}

    query_spectrum.each do |query_peak|
      lib_spectrum.each do |lib_peak|
        if (lib_peak - query_peak).abs <= tolerance
          # if the difference between the query peak and
          # the lib peak is less than tolerance then it is
          # a match
          matches[query_peak] << lib_peak
          # we only match a query peak once, os we remove it from
          # the set once it is matched
          lib_spectrum.delete(lib_peak)
          # we only match a library peak once, so we can break
          # at this point
          break

        elsif query_peak > lib_peak
          # we go through the query_peaks and the lib_peaks in acsending
          # order so we can be sure that when the lib peak is larger than the
          # lib_peak that no other lib peaks will match it so we can remove
          # it from the set
          missed_peaks << lib_peak
          lib_spectrum.delete(lib_peak)

        else
          # if query_peak > lib_peak
          # we go through the query_peaks and the lib_peaks in acsending
          # order so we can be sure that when the query peak is larger than the
          # lib_peak and we don't have a match, that we can stop looking
          # for a match for this peak
          break
        end
      end
    end

    intersect_count = matches.keys.count
    union_count     = missed_peaks.count + query_spectrum.count + lib_spectrum.count
    # union_count     = (query_spectrum.count + lib_spectrum.count)-intersect_count
    jaccard_index   = intersect_count.to_f / union_count.to_f

    return {
      ratio: "#{intersect_count}/#{union_count}",
      matches: matches,
      missed_query_peaks: (missed_peaks+query_spectrum).to_a,
      missed_lib_peaks: (lib_spectrum-matches.keys).to_a,
      intersect_count: intersect_count,
      union_count: union_count,
      jaccard_index: jaccard_index
    }

  end




end
