require 'set'
module ScoringMethods

  def self.peak_matching_dice(query_spectrum, lib_spectrum, tolerance)
    return 1 if lib_spectrum.empty? && query_spectrum.empty?

    # Sort the input peaks and turn them into sets for efficient
    # delete operations
    lib_spectrum   = lib_spectrum.sort.to_set
    query_spectrum = query_spectrum.sort.to_set

    # Set up some data structures to store data
    missed_peaks  = Set.new
    matches = Hash.new{|h,k| h[k] = Array.new}

    query_spectrum.each do |query_peak|
      lib_spectrum.each do |lib_peak|
        if lib_peak[0].nil?
          break
        elsif (lib_peak[0].to_f - query_peak[0].to_f).abs <= tolerance
          # if the difference between the query peak and
          # the lib peak is less than tolerance then it is
          # a match
          matches[query_peak] << lib_peak
          # we only match a query peak once, so we remove it from
          # the set once it is matched
          lib_spectrum.delete(lib_peak)
          # we only match a library peak once, so we can break
          # at this point
          break

        elsif query_peak[0] > lib_peak[0]
          # we go through the query_peaks and the lib_peaks in acsending
          # order so we can be sure that when the lib peak is larger than the
          # lib_peak that no other lib peaks will match it so we can remove
          # it from the set
          missed_peaks << lib_peak
          lib_spectrum.delete(lib_peak)

        else
          # if query_peak > lib_peak
          # we go through the query_peaks and the lib_peaks in acsending
          # order so we can be sure that when the query peak is larger than the
          # lib_peak and we don't have a match, that we can stop looking
          # for a match for this peak
          break
        end
      end
    end

    return matches.keys.count
  end

  def self.peak_matching_dot(query_spectrum, lib_spectrum, tolerance)
    return 1 if lib_spectrum.empty? && query_spectrum.empty?

    # Sort the input peaks and turn them into sets for efficient
    # delete operations
    lib_spectrum   = lib_spectrum.sort.to_set
    query_spectrum = query_spectrum.sort.to_set

    # Set up some data structures to store data
    missed_peaks  = Set.new
    matches = Hash.new{|h,k| h[k] = Array.new}
    term1 = Set.new

    query_spectrum.each do |query_peak|
      lib_spectrum.each do |lib_peak|
        if lib_peak[0].nil?
          break
        elsif (lib_peak[0].to_f - query_peak[0].to_f).abs <= tolerance
          # if the difference between the query peak and
          # the lib peak is less than tolerance then it is
          # a match
          matches[query_peak] << lib_peak
          
          term1 << Math.sqrt((lib_peak[0]*query_peak[0]).abs)*Math.sqrt((lib_peak[1]*query_peak[1]).abs)
          # we only match a query peak once, so we remove it from
          # the set once it is matched
          lib_spectrum.delete(lib_peak)
          # we only match a library peak once, so we can break
          # at this point
          break

        elsif query_peak[0] > lib_peak[0]
          # we go through the query_peaks and the lib_peaks in acsending
          # order so we can be sure that when the lib peak is larger than the
          # lib_peak that no other lib peaks will match it so we can remove
          # it from the set
          missed_peaks << lib_peak
          lib_spectrum.delete(lib_peak)

        else
          # if query_peak > lib_peak
          # we go through the query_peaks and the lib_peaks in acsending
          # order so we can be sure that when the query peak is larger than the
          # lib_peak and we don't have a match, that we can stop looking
          # for a match for this peak
          break
        end
      end
    end

    return term1

  end

  def self.dice_product(query_spectrum, lib_spectrum, tolerance)
    lib_spectrum = lib_spectrum.uniq
    matched_peaks = peak_matching_dice(query_spectrum, lib_spectrum, tolerance)

    matching_count = matched_peaks
    total_peaks     = lib_spectrum.count + query_spectrum.count
    dice  = (matching_count.to_f / total_peaks.to_f)*2
    # puts "Matched #{matching_count}"
    # puts "DICE #{dice}"
    dice

  end

  def self.dot_product(query_spectrum, lib_spectrum, tolerance)
    lib_spectrum = lib_spectrum.uniq
    matched_peaks = peak_matching_dot(query_spectrum, lib_spectrum, tolerance)
    
    # dot product = term1/(term2*term3)
    # term one = sum of sqrt(mz_a, mz_b) *  sqrt( intensity_a,  intensity_b) for all matched pairs
    term1 = 0
    matched_peaks.each do |peak|
        term1+=peak
    end
        

    # # term two = sum of sqrt(Mz, intensity) for spectra A
    term2 = 0
    query_spectrum.each do |peak|
      term2+=peak[0]*peak[1]
    end

    term3 = 0
    lib_spectrum.each do |peak|
        term3+=peak[0]*peak[1]
    end

    dot = term1*term1/(term2*term3)
    # puts "Matched #{matched_peaks}"
    # puts "Dot #{dot}"
    {dot_score: dot, match_ratio: "#{matched_peaks.length}/#{lib_spectrum.length}"}

  end

  def self.dot_optimize_spectrum(spectrum)
    max_intensity = spectrum.max_by{ |s| s.intensity.nil? ? 1 : s.intensity }.intensity # get maximum intensity
    max_intensity = max_intensity || 1
    spectrum.map do |v|
      position = v.peak_position_ppm # Get the position of the spectrum
      intensity = v.intensity || max_intensity # Assume maximum intensity if not provided? TODO: Run this through the NMR people
      scaled_intensity = (intensity/max_intensity).to_f
      [position, scaled_intensity] # return as a prepared tuple for the dot search
    end.sort.to_set
  end

end
