class Sdf
  def initialize(sdf)
    @sdf = sdf
  end

  def generate(&block)
    if block_given?
      @sdf = @sdf.sub(/\$\$\$\$\s*/, '')
      block.call(self)
    end
    @sdf << "$$$$\n"
    @sdf
  end

  def tag(tag, value=nil)
    new_tag = if value.present?
      "> <#{tag}>\n" <<
        (value.kind_of?(Array) ? value.join('; ') : value.strip) <<
        "\n\n"
    else
      ""
    end
    @sdf << new_tag
  end
end
