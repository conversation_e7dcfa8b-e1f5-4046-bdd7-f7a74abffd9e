module ChemStandardize
  extend Savon::Model

  BASE_URL   = "http://#{JCHEM_CONFIG[:domain]}/axis2/services"

  client wsdl: "#{BASE_URL}/StandardizerWS?wsdl"
  global :log_level, Rails.env.production? ? :error : :info
  operations :standardize
  
  def standardize(structure, format="mol", config=M<PERSON>ECULE_STANDARDIZE)
    ChemStandardize.standardize(structure, format, config)
  end

  def self.standardize(structure, format="mol", config=MOLECULE_STANDARDIZE)
    response = super(message: { target_structure: structure, config: config, 
                                output_format: format })
    standardized = response.body[:standardize_response][:return].to_s

    # Remove trailing whitespace if it's a value that is only one line long
    if standardized.present? && standardized.split(/\n/).size <= 1
      standardized.strip!
    end

    standardized = nil if standardized.strip == 'null'
    standardized.blank? ? nil : standardized
  end
end