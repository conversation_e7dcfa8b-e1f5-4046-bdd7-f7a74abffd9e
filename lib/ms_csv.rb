require 'csv'

class MsCsv
  #HEADER = %w[compound_id compound_name formula compound_mass adduct
    #adduct_type adduct_mass delta]

  HEADER = %w[compound_id compound_name kegg_id formula monoisotopic_mass adduct
    adduct_type adduct_m/z delta(ppm)]

  def initialize(search_array)
    @search_array =
      search_array.is_a?(Array) ? search_array : [search_array]
  end

  def build
    CSV.generate do |csv|
      csv << ['query_mass'] + HEADER
      @search_array.each do |search|
        search.results.each do |adduct|
          s = Structure.find(adduct.id)
          if !s.curation.nil?
            c_name = JSON.parse(s.curation.result)["identifiers"]["name"]
            if JSON.parse(s.curation.result)["identifiers"]["kegg_id"].nil?
              c_kegg_id = "n/a"
            else
              c_kegg_id = JSON.parse(s.curation.result)["identifiers"]["kegg_id"]
            end
            csv << [search.query_mass, adduct.database_id,
                    c_name, c_kegg_id, adduct.formula, adduct.exact_mass,
                    adduct.adduct, adduct.adduct_type,
                    adduct.adduct_mass, (adduct.ppm).round]
          end
        end
      end
    end
  end
end
