module ChemSearch
  DEFAULT_PER_PAGE = 30
  DEFAULT_MAX      = 100
  DEFAULT_MAX_TIME = 60000
  MIN_SIMILARITY   = 0.3

  # Search types
  TYPE_EXACT          = 'FULL_FRAGMENT'
  TYPE_SIMILARITY     = 'SIMILARITY'
  TYPE_SUBSTRUCTURE   = 'SUBSTRUCTURE'
  TYPE_SUPERSTRUCTURE = 'SUPERSTRUCTURE'

  def self.search(structure, source_id=nil, options={})
    Struct.new(:hits, :total_count).new([], 0) if structure.blank?

    search_url = "#{JCHEM_CONFIG[:url]}/data/#{JCHEM_CONFIG[:config]}/table/#{JCHEM_CONFIG[:table]}/search"

    display = { include: [:cd_id, :cd_formula, :cd_molweight, :cd_structure] }

    options = {
        'searchOptions' => self.search_options(structure, options),
        'filter'        => self.filter(options, source_id),
        'paging'        => self.paging(options),
        'display'       => display
      }

    result = RestClient.post search_url, options.to_json,
               content_type: :json, accept: :json

    jchem_hits = JSON.parse(result, symbolize_names: true)
    hits = jchem_hits[:data]
    total = jchem_hits[:total]

    # We add a score for non-similarity search here so we can still
    # keep the sort order.
    counter = hits.length
    hits.each do |hit|
      hit[:similarity] ||= counter
      counter -= 1
    end

    Struct.new(:hits, :total_count).new(hits, total)
  end

  # Process the incoming search options into a hash
  def self.search_options(structure, options)
    search_type = options[:search_type]
    similarity  = options[:similarity]
    max_results = options[:max_results] || DEFAULT_MAX

    query_options = { 'queryStructure' => structure }

    # Set the search type
    case search_type.to_s.to_sym
      when :exact
        query_options['searchType'] = TYPE_EXACT
        query_options['stereoSearchType'] = 'EXACT'
      when :similarity
        query_options['searchType'] = TYPE_SIMILARITY
        threshold = similarity.to_f < MIN_SIMILARITY ? MIN_SIMILARITY : similarity
        query_options['similarity'] = { 'threshold' => threshold }
      when :substructure
        query_options['searchType'] = TYPE_SUBSTRUCTURE
      when :substructure
        query_options['searchType'] = TYPE_SUPERSTRUCTURE
    end

    # Set the max # of hits and other query options
    query_options['general'] = { 'timeoutLimitMilliseconds' => DEFAULT_MAX_TIME,
                                 'maxResultCount' => max_results.to_i }
    query_options
  end

  def self.filter(options, source_id=nil)
    max_weight  = options[:max_weight]
    min_weight  = options[:min_weight]
    id_filter   = options[:id_filter]

    filter = { conditions: {} }
    if source_id.present?
      cd_ids = Source.find(source_id).structures.pluck(:jchem_id)
      filter[:conditions][:cd_id] = { '$in' => cd_ids }
    end
    if max_weight.to_f > 0 || min_weight.to_f > 0
      max_weight = 1.gigabyte if max_weight.to_f <= 0
      min_weight = 0 unless min_weight.to_f > 0
      filter[:conditions][:cd_molweight] = { "$between" => [min_weight.to_f, max_weight.to_f] }
    end
    if id_filter.present? # Note this overrides filter from source_id
      cd_ids = DatabaseRegistration.joins(:structure).
                select("structures.jchem_id").
                where(database_id: id_filter.split('|')).
                uniq.
                pluck(:jchem_id)
      filter[:conditions][:cd_id] = { '$in' => cd_ids }
    end
    filter
  end

  def self.paging(options)
    paging_options = {}

    # Pagination options
    if options[:page].present?
      page        = options[:page] || 1
      per_page    = options[:per_page] || DEFAULT_PER_PAGE

      size = per_page.to_i
      page = page.to_i
      offset = (page - 1) * size

      paging_options['offset'] = offset
      paging_options['limit'] = size
    else
      paging_options['offset'] = 0
      paging_options['limit'] = DatabaseRegistration.count
    end

    paging_options
  end
end
