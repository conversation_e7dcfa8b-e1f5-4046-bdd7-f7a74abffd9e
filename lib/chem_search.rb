module ChemSearch
  DEFAULT_PER_PAGE = 30
  DEFAULT_MAX      = 100
  DEFAULT_MAX_TIME = 60000
  MIN_SIMILARITY   = 0.3

  # Search types
  TYPE_EXACT          = 'exact'
  TYPE_SIMILARITY     = 'similarity'
  TYPE_SUBSTRUCTURE   = 'substructure'
  TYPE_SUPERSTRUCTURE = 'superstructure'

  def self.search(structure, source_id=nil, options={})
    return Struct.new(:hits, :total_count).new([], 0) if structure.blank?

    search_type = options[:search_type]
    similarity_threshold = options[:similarity]
    max_results = options[:max_results] || DEFAULT_MAX

    # Get filtered structure IDs based on source and other filters
    structure_scope = get_filtered_structures(source_id, options)

    # Perform the chemical search using RDKit
    case search_type.to_s.to_sym
    when :exact
      hits = exact_search(structure, structure_scope, max_results)
    when :similarity
      threshold = similarity_threshold.to_f < MIN_SIMILARITY ? MIN_SIMILARITY : similarity_threshold.to_f
      hits = similarity_search(structure, structure_scope, threshold, max_results)
    when :substructure
      hits = substructure_search(structure, structure_scope, max_results)
    when :superstructure
      hits = superstructure_search(structure, structure_scope, max_results)
    else
      hits = []
    end

    # Apply pagination
    total_count = hits.length
    hits = apply_pagination(hits, options)

    # Convert to expected format with jchem_id compatibility
    formatted_hits = hits.map.with_index do |hit, index|
      {
        cd_id: hit[:jchem_id],
        cd_formula: hit[:formula],
        cd_molweight: hit[:molecular_weight],
        cd_structure: hit[:original_structure],
        similarity: hit[:similarity] || (hits.length - index)
      }
    end

    Struct.new(:hits, :total_count).new(formatted_hits, total_count)
  end

  # Get filtered structures based on source and other criteria
  def self.get_filtered_structures(source_id, options)
    scope = Structure.all

    # Filter by source if specified
    if source_id.present?
      scope = scope.joins(:database_registrations).where(database_registrations: { source_id: source_id })
    end

    # Filter by molecular weight range
    max_weight = options[:max_weight]
    min_weight = options[:min_weight]
    if max_weight.to_f > 0 || min_weight.to_f > 0
      max_weight = 1.gigabyte if max_weight.to_f <= 0
      min_weight = 0 unless min_weight.to_f > 0
      scope = scope.where(molecular_weight: min_weight.to_f..max_weight.to_f)
    end

    # Filter by database IDs if specified
    if options[:id_filter].present?
      database_ids = options[:id_filter].split('|')
      scope = scope.joins(:database_registrations).where(database_registrations: { database_id: database_ids })
    end

    scope
  end

  # Exact structure matching using canonical SMILES comparison
  def self.exact_search(query_structure, structure_scope, max_results)
    script = <<~PYTHON
      import sys
      import json
      from rdkit import Chem

      def get_canonical_smiles(structure):
          try:
              # Try parsing as SMILES first
              mol = Chem.MolFromSmiles(structure)
              if mol is None:
                  # Try parsing as MOL block
                  mol = Chem.MolFromMolBlock(structure)
              if mol is None:
                  return None
              return Chem.MolToSmiles(mol, canonical=True)
          except:
              return None

      # Read input data
      with open(sys.argv[1], 'r') as f:
          data = json.load(f)

      query_smiles = get_canonical_smiles(data['query_structure'])
      results = []

      if query_smiles:
          for structure in data['structures']:
              target_smiles = get_canonical_smiles(structure['original_structure'])
              if target_smiles and target_smiles == query_smiles:
                  results.append({
                      'structure_id': structure['id'],
                      'jchem_id': structure['jchem_id'],
                      'formula': structure['formula'],
                      'molecular_weight': structure['molecular_weight'],
                      'original_structure': structure['original_structure'],
                      'similarity': 1.0
                  })

      # Write results
      with open(sys.argv[2], 'w') as f:
          json.dump(results[:#{max_results}], f)
    PYTHON

    execute_rdkit_search(script, query_structure, structure_scope)
  end

  # Similarity search using Morgan fingerprints and Tanimoto coefficient
  def self.similarity_search(query_structure, structure_scope, threshold, max_results)
    script = <<~PYTHON
      import sys
      import json
      from rdkit import Chem
      from rdkit.Chem import rdMolDescriptors
      from rdkit import DataStructs

      def get_morgan_fingerprint(structure):
          try:
              # Try parsing as SMILES first
              mol = Chem.MolFromSmiles(structure)
              if mol is None:
                  # Try parsing as MOL block
                  mol = Chem.MolFromMolBlock(structure)
              if mol is None:
                  return None
              return rdMolDescriptors.GetMorganFingerprintAsBitVect(mol, 2, nBits=2048)
          except:
              return None

      # Read input data
      with open(sys.argv[1], 'r') as f:
          data = json.load(f)

      query_fp = get_morgan_fingerprint(data['query_structure'])
      results = []

      if query_fp:
          for structure in data['structures']:
              target_fp = get_morgan_fingerprint(structure['original_structure'])
              if target_fp:
                  similarity = DataStructs.TanimotoSimilarity(query_fp, target_fp)
                  if similarity >= data['threshold']:
                      results.append({
                          'structure_id': structure['id'],
                          'jchem_id': structure['jchem_id'],
                          'formula': structure['formula'],
                          'molecular_weight': structure['molecular_weight'],
                          'original_structure': structure['original_structure'],
                          'similarity': similarity
                      })

      # Sort by similarity (descending) and limit results
      results.sort(key=lambda x: x['similarity'], reverse=True)

      # Write results
      with open(sys.argv[2], 'w') as f:
          json.dump(results[:#{max_results}], f)
    PYTHON

    execute_rdkit_search(script, query_structure, structure_scope, { threshold: threshold })
  end

  # Substructure search - find structures that contain the query as a substructure
  def self.substructure_search(query_structure, structure_scope, max_results)
    script = <<~PYTHON
      import sys
      import json
      from rdkit import Chem

      def parse_structure(structure):
          try:
              # Try parsing as SMILES first
              mol = Chem.MolFromSmiles(structure)
              if mol is None:
                  # Try parsing as MOL block
                  mol = Chem.MolFromMolBlock(structure)
              return mol
          except:
              return None

      # Read input data
      with open(sys.argv[1], 'r') as f:
          data = json.load(f)

      query_mol = parse_structure(data['query_structure'])
      results = []

      if query_mol:
          for structure in data['structures']:
              target_mol = parse_structure(structure['original_structure'])
              if target_mol and target_mol.HasSubstructMatch(query_mol):
                  results.append({
                      'structure_id': structure['id'],
                      'jchem_id': structure['jchem_id'],
                      'formula': structure['formula'],
                      'molecular_weight': structure['molecular_weight'],
                      'original_structure': structure['original_structure'],
                      'similarity': 1.0
                  })

      # Write results
      with open(sys.argv[2], 'w') as f:
          json.dump(results[:#{max_results}], f)
    PYTHON

    execute_rdkit_search(script, query_structure, structure_scope)
  end

  # Superstructure search - find structures that are contained within the query
  def self.superstructure_search(query_structure, structure_scope, max_results)
    script = <<~PYTHON
      import sys
      import json
      from rdkit import Chem

      def parse_structure(structure):
          try:
              # Try parsing as SMILES first
              mol = Chem.MolFromSmiles(structure)
              if mol is None:
                  # Try parsing as MOL block
                  mol = Chem.MolFromMolBlock(structure)
              return mol
          except:
              return None

      # Read input data
      with open(sys.argv[1], 'r') as f:
          data = json.load(f)

      query_mol = parse_structure(data['query_structure'])
      results = []

      if query_mol:
          for structure in data['structures']:
              target_mol = parse_structure(structure['original_structure'])
              if target_mol and query_mol.HasSubstructMatch(target_mol):
                  results.append({
                      'structure_id': structure['id'],
                      'jchem_id': structure['jchem_id'],
                      'formula': structure['formula'],
                      'molecular_weight': structure['molecular_weight'],
                      'original_structure': structure['original_structure'],
                      'similarity': 1.0
                  })

      # Write results
      with open(sys.argv[2], 'w') as f:
          json.dump(results[:#{max_results}], f)
    PYTHON

    execute_rdkit_search(script, query_structure, structure_scope)
  end

  # Execute RDKit search script with structure data
  def self.execute_rdkit_search(script, query_structure, structure_scope, extra_params = {})
    # Prepare structure data for RDKit processing
    structures_data = structure_scope.select(:id, :jchem_id, :original_structure, :formula, :molecular_weight)
                                    .limit(10000) # Reasonable limit for performance
                                    .map do |structure|
      {
        id: structure.id,
        jchem_id: structure.jchem_id,
        original_structure: structure.original_structure,
        formula: structure.formula,
        molecular_weight: structure.molecular_weight
      }
    end

    input_data = {
      query_structure: query_structure,
      structures: structures_data
    }.merge(extra_params)

    begin
      result = Jchem.run_rdkit(script, input_data.to_json)
      JSON.parse(result).map(&:with_indifferent_access)
    rescue => e
      Rails.logger.error "RDKit search error: #{e.message}"
      []
    end
  end

  # Apply pagination to search results
  def self.apply_pagination(hits, options)
    return hits unless options[:page].present?

    page = options[:page].to_i || 1
    per_page = options[:per_page].to_i || DEFAULT_PER_PAGE
    offset = (page - 1) * per_page

    hits[offset, per_page] || []
  end
end
