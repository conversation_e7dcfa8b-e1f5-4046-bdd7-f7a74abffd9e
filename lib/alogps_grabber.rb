require 'alogps/alogps_result.rb'

module <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  def self.prediction_from_smiles(smiles)
    return nil if smiles.strip.blank?
    
    request = HTTPI::Request.new("http://www.vcclab.org/web/services/ALOGPS")
    request.body = 
    "<?xml version=\"1.0\" encoding=\"utf-8\" ?>
    <env:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"
        xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\"
        xmlns:env=\"http://schemas.xmlsoap.org/soap/envelope/\">
      <env:Body>
        <MOLECULES FORMAT='smi'><MOLECULE>#{smiles}</MOLECULE></MOLECULES>
      </env:Body>
    </env:Envelope>"
    
    request.headers = { "Accept-Charset" => "utf-8", 'Content-Type' => 'text/xml;charset=UTF-8', 'SOAPAction' => 'getAlogpsResults' }
    response = HTTPI.post request, :httpclient

    result = AlogpsResult.new
    result.process_xml_result(response.body)
    result
  end
end