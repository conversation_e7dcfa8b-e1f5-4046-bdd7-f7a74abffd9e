require "csv"
require 'nokogiri'

namespace :hmdb do
	desc "generate descriptions for predicted metabolites to be uploaded to hmdb from Yannick's BioTransformer sdf files"
	task generate_desc: [:environment] do

		SDF_FILE_IN_PATH = File.expand_path('~/Desktop/<PERSON>nick_sdf_2', File.dirname(__FILE__))
		EC_MAP_FILE = File.expand_path('~/Data/ec_uniprot_mapping.tsv', File.dirname(__FILE__))
		OUTPUT_FILE_PATH = File.expand_path('~/Desktop/descriptions/inchikey_inchi_with_descriptions.tsv', File.dirname(__FILE__))

		enzymes = Array.new
		biosystems = Array.new

		$enzyme_attr = {
		'CYP1A2'=>'a Cytochrome P450 1A2 (P05177)', 'CYP2A6'=>'a Cytochrome P450 2A6 (P11509)', 'CYP2B6'=>'a Cytochrome P450 2B6 (P20813)',
		'CYP2C8'=>'a Cytochrome P450 2C8 (P10632)', 'CYP2C9'=>'a Cytochrome P450 2C9 (P11712)', 'CYP2C19'=>'a Cytochrome P450 2C19 (P33261)',
		'CYP2E1'=>'a Cytochrome P450 2E1 (P05181)', 'CYP2D6'=>'a Cytochrome P450 2D6 (P10635)', 'CYP3A4'=>'a Cytochrome P450 3A4 (P08684)',
		'EC_2_4_1_17'=>'a UDP-glucuronosyltransferase 1-1',
		'EC_3_1_1_20'=>'a Tannin acylhydrolase',
		'EC_3_2_1_20'=>'a Maltodextrin glucosidase',
		'EC_3_2_1_21'=>'a Periplasmic beta-glucosidase',
	 	'EC_3_2_1_23'=>'a Beta-galactosidase', 
	 	'EC_3_2_1_31'=>'a Beta-glucuronidase',
	 	'EC_3_2_1_40'=>'an Alpha-L-rhamnosidase (Fusobacterium K-60, and Lactobacillus plantarum from the gut)',
	 	'EC_3_2_1_147'=>'an unknown gut bacterial thioglucosidase', 
	 	'EC_1_6_99_1'=>'an NADPH dehydrogenase',
		'UNSPECIFIED_GUTMICRO'=>'an unspecified gut bacterial', 
		'ABKAR1'=>'an Alpha,beta-ketoalkene double bond reductase 1',
		'VINYL_PHENOL_REDUCTASE'=>'a bacterial vinylphenol reductase',
	 	'FLAVONOID_C_GLYCOSIDASE'=>'an unspecified gut bacterial C-C lyase',
	 	'DECARBOXYLASE'=>'an unspecified gut bacterial decarboxylase (EC 4.1.1.X)',
	 	'DEMETHYLASE'=>'an unspecified bacterial demethylase',
	 	'DEHYDROXYLASE'=>'an unspecified bacterial aryl demethylase', 
	 	'LACTONASE'=>'an unspecified bacterial lactonase', 
	 	'HYDROXYCINNAMATE_DECARBOXYLASE'=>'a 4-Hydroxycinnamate decarboxylase',
	 	}

		outputfile = File.open(OUTPUT_FILE_PATH, "a+")

		file_count = 0
		compound_count_all = 0

		unique_metabolites = Array.new

		Dir.foreach(SDF_FILE_IN_PATH) do |file|
			next if file == '.' or file == '..'
			puts "############################################################################"
			puts "working on: " + file
			puts "############################################################################"

			file_count += 1 
			compound_count_file = 0

			File.open(SDF_FILE_IN_PATH+"/"+file) do |item|

				while(line = item.gets) != nil
					if line.include?("<Enzymes>")
						while(line = item.gets) != nil
							if line != "\n"
								enzyme = line.chomp
								if !$enzyme_attr[enzyme].nil?
									enzymes << $enzyme_attr[enzyme]
								else
									if enzyme.include?("EC_")
										enzyme = enzyme.gsub("_", ".")
										ec = nil
										uniprot_id = nil

										CSV.open(EC_MAP_FILE, "r", :col_sep => "\t", :quote_char => "\x00").each do |row|
											if !row[0].nil?
												ec = 'EC.' + row[0].split(" (")[0]
												if ec == enzyme
													uniprot_id = row[1][0..5]
													if uniprot_id == "ADDITI"
														uniprot_id = nil
													end
												end
											end
										end
										if !uniprot_id.nil?
											ec_name = nil
											begin
												#retries ||= 0
												xml = open("http://www.uniprot.org/uniprot/#{uniprot_id}.xml").read
												if !xml.nil?
													data = Nokogiri::XML(xml)
													data.remove_namespaces!
													data.xpath("//protein/recommendedName/fullName").each do |_name_|
														ec_name = _name_.text
													end
													if ec_name.nil?
														enzymes << uniprot_id
													else
														if ['a', 'e', 'i', 'o', 'u'].include?(ec_name[0].downcase)
															enzymes << 'an '+ ec_name + " (" + uniprot_id + ")"
														else
															enzymes << 'a ' + ec_name + " (" + uniprot_id + ")"
														end
													end
												else
													enzymes << 'the ' + uniprot_id
												end
											rescue OpenURI::HTTPError => error
												$stderr.puts "WARNING: could not retrieve uniprot name for the uniprot_id: #{uniprot_id}, in compound number: " + (compound_count_file.to_i+1).to_s
												#raise error
												#retry if (retries += 1) < 3
												enzymes << 'the ' + uniprot_id
											end
										else
											enzymes << 'the ' + enzyme
										end
										

									elsif enzyme[-4..-1].downcase.include?("ase")
										if enzyme == 'ACETYLTRANSFERASE' || enzyme == 'SULFOTRANSFERASE'
											if enzymes.length > 0
											else
												if enzyme == 'ACETYLTRANSFERASE'
													enzymes << 'an ' + enzyme.downcase.capitalize
												else
													enzymes << 'a ' + enzyme.downcase.capitalize
												end
											end
										else
											if ['a', 'e', 'i', 'o', 'u'].include?(enzyme[0].downcase)
												enzymes << 'an ' + enzyme.downcase.gsub("_", "-").capitalize
											else
												enzymes << 'a ' + enzyme.downcase.gsub("_", "-").capitalize
											end
										end
									elsif enzyme.downacase.include?("ase")
										if ['a', 'e', 'i', 'o', 'u'].include?(enzyme[0].downcase)
											enzymes << 'an ' + enzyme.downcase.gsub("_", "-").capitalize
										else
											enzymes << 'a ' + enzyme.downcase.gsub("_", "-").capitalize
										end
									else
										enzymes << 'the ' + enzyme.gsub("_", "-")
									end
								end
							else
								break
							end
						end
						
					# gets the substrate inchikey
					elsif line.include?("<Precursor InChIKey>")
						while(line = item.gets) != nil
							prec_inchikey = line.chomp
							break
						end

					# gets the substrate inchi
					elsif line.include?("<Precursor InChI>")
						while(line = item.gets) != nil
							prec_inchi = line.chomp
							retries = 0
							begin
								jc_substrate_name = DataWrangler::JChem::Convert.inchi_to_name(prec_inchi)
							rescue => e
								puts inchi
								retries += 1
								if retries < 3
									retry
								else
									$stderr.puts "WARNING: could not retrieve IUPAC for substrate: #{prec_inchi}, in compound number: " + (compound_count_file.to_i+1).to_s
								end
							end
							break
						end

					elsif line.include?("<BioSystem>")
						while(line = item.gets) != nil
							if line != "\n"
								if line.chomp == "HUMAN"
									biosystems << "humans"
								elsif line.chomp == "GUTMICRO"
									biosystems << "human gut microbiota"
								else
									biosystems << line.chomp.downcase
								end
							else
								break
							end
						end

					# gets the reaction
			    	elsif line.include?("<Reactions>")
						while(line = item.gets) != nil
							reaction = line.chomp
							if reaction[0] == '_'
								reaction = reaction[1..-1]
							end
							break
						end
					# gets the product inchikey
			    	elsif line.include?("<InChIKey>")
						while(line = item.gets) != nil
							inchikey = line.chomp
							if !unique_metabolites.include?(inchikey)
								unique_metabolites << inchikey
								unique_metabolite = true
							end
							break
						end

					# gets the product inchi
			    	elsif line.include?("<InChI>")
						while(line = item.gets) != nil
							inchi = line.chomp
								retries = 0
							begin
								jc_product_name = DataWrangler::JChem::Convert.inchi_to_name(inchi)
							rescue => e
								puts inchi
								retries += 1
								if retries < 3
									retry
								else
									$stderr.puts "WARNING: could not retrieve IUPAC for product: #{inchi}, in compound number: " + (compound_count_file.to_i+1).to_s
								end
							end
							break
						end

					# when it is the end of a compound, write in outputfile and clear data in arrays
					elsif line.include?("$$$$")

						compound_count_all += 1
						compound_count_file += 1
						
						puts "file #: #{file_count.to_s}, file compound #: #{compound_count_file.to_s}, compound #: #{compound_count_all.to_s}"

						# get datawrangler compound by inchikey
						#dw_product = DataWrangler::Annotate::Compound.by_inchikey(inchikey)
						#dw_substrate = DataWrangler::Annotate::Compound.by_inchikey(prec_inchikey)
						dw_product = nil
						dw_substrate = nil

						# control for singular or plural enzymes for fill-in-the-blank in desc
						if enzymes.length > 1
							enzyme_s = "enzymes"
						else
							enzyme_s = "enzyme"
						end

						# control for a/an before reaction name
						if ['a', 'e', 'i', 'o', 'u'].include?(reaction[0].downcase)
							pre_reac = 'an'
						else
							pre_reac = 'a'
						end

						if !jc_product_name.nil?
							product_name = jc_product_name
						else
							product_name = inchikey
						end

						if !jc_substrate_name.nil?
							substrate_name = jc_substrate_name
						else
							substrate_name = prec_inchikey
						end

						dw_desc = nil
						product_direct_parent = nil
						direct_parent_desc = nil
						klass_def = nil

						if !dw_product.nil?
							dw_product_desc = dw_product.cs_description.name
							dw_product_name = dw_product.identifiers.name
							if !dw_product_desc.nil?
								dw_desc = dw_product_desc.gsub("\n\n", ' ')
							end

							if !dw_product_name.nil?
								prodcut_name = dw_product_name
							end

							if !dw_product.classifications.nil?
								if !dw_product.classifications[0].nil?
									classyfire_desc = dw_product.classifications[0].classyfire_description
									if !classyfire_desc.nil? || classyfire_desc != ""
										klass_def = classyfire_desc
									end

									if !dw_product.classifications[0].direct_parent.nil?
										if !dw_product.classifications[0].direct_parent.name.nil?
											product_direct_parent =  dw_product.classifications[0].direct_parent.name
										end
									end
								end
							end
						end

						if !dw_substrate.nil?
							dw_substrate_name = dw_substrate.identifiers.name
							if !dw_substrate_name.nil?
								substrate_name = dw_substrate_name
							end
						end

						# not used but left here because we use classyfire description for now, maybe later we can use this...
						if !product_direct_parent.nil?
							direct_parent_desc = "#{product_name.capitalize} is a member of the compound class #{product_direct_parent.downcase}."
						end


=begin
						# use ClassyFire API straight without the need for datawrangler to get classification description 
						begin
            				open("http://classyfire.wishartlab.com/entities/"+inchikey.to_s+".json") do |f| 
              					data = JSON.load(f.read)
              				end
              				if !data.nil?
              					classification = data["entities"]["description"]
              				else
              					classification = ""
              				end
          				rescue Exception => e
            				#$stderr.puts "WARNING #{e.message} #{e.backtrace}"
        				end
=end


						# INPUT FROM YANNICK:
						# "octanoic acid is a #{ClassyFire/Chemosummarizer/DataWrangler/Metbuilder description}.
						# It was predicted by BioTransformer to be a metabolite of #{Reactant name} upon #{reaction name},
						# a reaction that is catalyzed by #{enzyme name}.""

						# INPUT FROM DR. WISHART:
						# "Name of molecule" is a predicted metabolite that is produced by the metabolism
						# of "Starting molecule name" that is catalyzed by the "Name of enzyme or enzymes or
						# EC numbers" enzymes. "Name of molecule" is a "ClassyFire Class name". "ClassyFire
						# Class name" is a [use definition from ChemOnt]."

						# SECOND INPUT FROM DR. WISHART
						# "3,4,5-trihydroxy-6-(octanoyloxy)oxane-2-carboxylic acid is a predicted metabolite generated
						# by BioTransformer (reference) that is produced by the metabolism of caprylic acid. It is generated
						# by UDP-glucuronosyltransferase (P22309) enzyme via an aliphatic-acyl-O-glucuronidation reaction. 
						# This aliphatic-acyl-O-glucuronidation reaction occurs in humans. This compound belongs to the class
						# of organic compounds known as O-glucuronides. These are glucuronides in which the aglycone is linked
						# to the carbohydrate unit through an O-glycosidic bond."


						# desc for both cases, if datawrangler returned a description or not
						generated_desc = "#{product_name.capitalize} is a predicted metabolite generated by BioTransformer¹ " +
						"that is produced by the metabolism of #{substrate_name.downcase}. It is generated by #{enzymes.to_sentence(two_words_connector: ' or ', last_word_connector: ' or ')} #{enzyme_s} " + 
						"via #{pre_reac} #{reaction.downcase.gsub("_", "-").gsub("-o-","-O-").gsub("-oh-", "-OH-")} reaction."

						# for biosystems
						biosystem = "This #{reaction.downcase.gsub("_", "-").gsub("-o-","-O-").gsub("-oh-", "-OH-")} occurs in #{biosystems.to_sentence}."

						# finally, check what we have and write in output file
						#if dw_desc != nil
						#	outputfile.write("#{inchikey}\t#{inchi}\t#{dw_desc} #{generated_desc}\n")
						#else
						#	outputfile.write("#{inchikey}\t#{inchi}\t#{generated_desc} #{biosystem} #{klass_def}\n")
						#end 

						if unique_metabolite == true
							outputfile.write("#{inchikey}\t#{inchi}\t#{generated_desc} #{biosystem}\n")
						end

						# clear to refill with the next reaction
						enzymes.clear
						biosystems.clear
						unique_metabolite = false
					end
				end
			end
		end
		outputfile.close
	end
end




# just a note for parsing sdf files, this might be useful, thought I'd share:
# sdf_list = File.open(file).read.split(/(?<=[$]{4})/).each(&:strip!).select{|i| !i.empty? }
# sdf_list.each
#...
#..
#.