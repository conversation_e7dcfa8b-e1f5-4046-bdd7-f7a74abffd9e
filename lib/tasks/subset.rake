require 'builder'
require 'admin_mailer'
require 'progress_bar'

databases = ["http://hmdb.ca/in_biofluid.json".freeze,
		"http://bmdb.wishartlab.com/in_biofluid.json".freeze
]

namespace :subset do
  desc "Add all Subsets to Subset Tables"
  task :add_all_subsets => [:environment] do
    subsets = ["Blood", "Saliva", "Urine", "Cerebrospinal Fluid (CSF)", "Feces", "Sweat", "Milk"]
    Subset.delete_all
    subsets.each do |s|
    	puts s
        subset = Subset.new{}
        subset.name = s
    	subset.save!
    end
  end

  desc "Sync biofluid subsets in MolDB from HMDB and BMDB"
  task :sync_all_biofluids => [:environment] do
   jsons = []
   databases.each do |url|
   		data = nil
   		open(url) {|f| data = JSON.load(f.read)}
   		jsons.append(data)
   end
   jsons.flatten!
   puts jsons[0]["biofluid"]
   jsons.each do |json|
   	subset_id = Subset.find_by_name(json["biofluid"]).id
   	json["compounds"].each do |compound|
   		database_id = compound["database_id"]
   		ss = StructureSubset.new()
   		ss.structure_id = DatabaseRegistration.find_by(database_id: database_id).structure_id
   		ss.subset_id = subset_id
   		ss.save!
   	end
   end
  end

end