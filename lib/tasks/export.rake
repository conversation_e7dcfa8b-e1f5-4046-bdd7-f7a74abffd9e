require 'zip/zip'
require 'builder'
require 'admin_mailer'
require 'active_record'
def spectrum_filename(id, document, status)
  match = /\.(\w+)$/.match(document.file_file_name)
  if !status.nil?
    filetype = match.nil? ? "" : match.captures.last
    "#{id}_#{document.spectra_type.downcase}_#{document.spectra_id}_#{document.id}_#{status}.#{filetype}"
  else
    filetype = match.nil? ? "" : match.captures.last
    "#{id}_#{document.spectra_type.downcase}_#{document.spectra_id}_#{document.id}.#{filetype}"
  end
end

# This is a general method for exporting documents into a
# zipfile, it takes the database_name and the zipdirname and a selector condition
# and will grab all the documents, rename using the spectrum_filename method
# and then save to the zipdi in public/downloads/exports
def export_document(database_name = nil, zipdirname, selector)
  source =
    if database_name.present? && database_name != 'all'
      Source.find_by!(name: database_name)
    else
      nil
    end
  # filename = source.present? ? source.name.parameterize(separator: "_") : 'all'
  filename = source.present? ? source.name : 'all'

  match = /_peak_/.match(zipdirname)
  zipdir = nil
  if match.nil?
    zipdir = File.join Rails.root, *%w[public downloads exports hmdb], zipdirname
  else
    zipdir = File.join Rails.root, *%w[public downloads exports hmdb peak_lists_txt]
  end

  status = nil
  match_predicted = /predicted_/.match(zipdirname)
  match_experimental = /experimental_/.match(zipdirname)
  if !match_predicted.nil?
    status = 'predicted'
  elsif !match_experimental.nil?
    status = 'experimental'
  end

  FileUtils.mkpath zipdir
  new_zipfile_name   = File.join zipdir, "#{filename}_#{zipdirname}.new.zip"
  final_zipfile_name = new_zipfile_name.remove(/.new/)

  begin
    # Remove partialy complete zipfile if it exists
    File.delete(new_zipfile_name) if File.exists?(new_zipfile_name)

    Zip::ZipFile.open(new_zipfile_name, Zip::ZipFile::CREATE) do |zipfile|

      # Only get the items registered to the specified database
      document_scope = Document.where(selector).
        includes(spectra: [:database_registrations])

      puts "Building #{final_zipfile_name} with #{document_scope.count} potential documents."
      progress = ProgressBar.new document_scope.count

      document_scope.find_each do |document|
        type = document.spectra_type.downcase
        if type == "cms" || type == "msms"
          predicted_spectrum = nil
          spectra_klass = document.spectra_type.classify.constantize
          predicted = spectra_klass.where(id: document.spectra_id).first.predicted
          predicted_spectrum = predicted ? 'predicted' : 'experimental'
          if predicted_spectrum != status
            progress.increment!
            next
          else
            registrations =
              if source.present?
                document.spectra.database_registrations.
                  select { |r| r.source_id == source.id && r.exported }

              else
                document.spectra.database_registrations.
                  select { |r| r.exported }
              end

            next unless registrations.length > 0

            registrations.each do |registration|
              id = source.nil? ? registration.structure.inchikey : registration.database_id
              file_to_zip  = document.file.path
              new_filename = spectrum_filename(id, document, predicted_spectrum)
              begin
                zipfile.add(new_filename, file_to_zip)
              rescue Exception => e
                puts e
              end
            end
            progress.increment!
          end
        else
          registrations =
            if source.present?
              document.spectra.database_registrations.
                select { |r| r.source_id == source.id && r.exported }

            else
              document.spectra.database_registrations.
                select { |r| r.exported }
            end

          next unless registrations.length > 0

          registrations.each do |registration|
            id = source.nil? ? registration.structure.inchikey : registration.database_id
            file_to_zip  = document.file.path
            new_filename = spectrum_filename(id, document, nil)
            begin
              zipfile.add(new_filename, file_to_zip)
            rescue Exception => e
              puts e
            end
          end
          progress.increment!
        end
      end
      progress.count = progress.max
      progress.write
      puts "Zipping file ..."
    end

    # Overwrite old file after succesfull build
    FileUtils.mv new_zipfile_name, final_zipfile_name
    # Make it readable to anyone
    `chmod a+r #{final_zipfile_name}`

  ensure
    # Remove partialy complete zipfile if it exists
    File.delete(new_zipfile_name) if File.exists?(new_zipfile_name)
    status = nil
  end
end

def export_xml(database_name = nil, zipdirname)
  # exports using the to_export_xml method
  source =
    if database_name.present? && database_name != 'all'
      Source.find_by!(name: database_name)
    else
      nil
    end
  # filename = source.present? ? source.name.parameterize(separator: "_") : 'all'
  filename = source.present? ? source.name : 'all'

  status = nil
  match_predicted = /predicted_/.match(zipdirname)
  match_experimental = /experimental_/.match(zipdirname)
  if !match_predicted.nil?
    status = 'predicted'
  elsif !match_experimental.nil?
    status = 'experimental'
  end

  # Set up filenames and directories.
  zipdir = File.join Rails.root, *%w[public downloads exports hmdb spectra_xml]
  FileUtils.mkpath(zipdir)
  new_zipfile_name = File.join zipdir, "#{filename}_#{zipdirname}.new.zip"
  final_zipfile_name = new_zipfile_name.remove(/.new/)

  begin
    # Set up the scopes
    scopes = nil
    if zipdirname == "all_spectra"
      scopes = Spectrum.all_spectra_types
    elsif zipdirname == "predicted_msms_spectra" || zipdirname == "experimental_msms_spectra"
      scopes = Spectrum.msms_spectra
    elsif zipdirname == "predicted_cms_spectra" || zipdirname == "experimental_cms_spectra"
      scopes = Spectrum.cms_spectra
    elsif zipdirname == "nmr_spectra"
      scopes = Spectrum.nmr_spectra
    end
      
    scopes = scopes.map do |klass|
      scope =
        if source.present?
          klass.where(database_registrations: { source: source }).merge(DatabaseRegistration.exported)
        else
          klass
        end
      scope.joins(:database_registrations).includes(:references, :peaks).merge(DatabaseRegistration.exported)
    end

    # Set up the progress bar
    total_count = scopes.map(&:count).sum
    puts "Building #{final_zipfile_name} with #{total_count} potential documents."
    progress = ProgressBar.new total_count

    # Remove partially complete zipfile if it exists
    File.delete(new_zipfile_name) if File.exists?(new_zipfile_name)

    Zip::ZipFile.open(new_zipfile_name, Zip::ZipFile::CREATE) do |zipfile|
      scopes.each do |scope|
        selector = "#{scope.klass.table_name}.*, database_registrations.database_id AS database_id"
        scope.select(selector).find_each do |spectrum|
          id = source.nil? ? spectrum.inchi_key : spectrum.database_id
          type = scope.klass.table_name.downcase
          if type == "c_ms" || type == "ms_ms"
            predicted_spectrum = spectrum.predicted ? 'predicted' : 'experimental'
            if predicted_spectrum != status
              next
            else
              filename = "#{id}_#{scope.klass.table_name}_spectrum_#{spectrum.id}_#{predicted_spectrum}.xml"
            end
          else
            filename = "#{id}_#{scope.klass.table_name}_spectrum_#{spectrum.id}.xml"
          end
          zipfile.get_output_stream(filename) { |f| f.puts spectrum.to_export_xml }
          progress.increment!
        end
      end

      progress.count = progress.max
      progress.write
      puts "Zipping file ..."
    end

    # Overwrite old file after succesful build
    FileUtils.mv new_zipfile_name, final_zipfile_name
    `chmod a+r #{final_zipfile_name}`
  ensure
    # Remove partialy complete zipfile if it exists
    File.delete(new_zipfile_name) if File.exists?(new_zipfile_name)
    status = nil
  end
end
=begin
def export_mzml(database_name = nil, zipdirname)
  # exports using the to_export_xml method
  source =
    if database_name.present? && database_name != 'all'
      Source.find_by!(name: database_name)
    else
      nil
    end
  # filename = source.present? ? source.name.parameterize(separator: "_") : 'all'
  filename = source.present? ? source.name : 'all'

  status = nil
  match_predicted = /predicted_/.match(zipdirname)
  match_experimental = /experimental_/.match(zipdirname)
  if !match_predicted.nil?
    status = 'predicted'
  elsif !match_experimental.nil?
    status = 'experimental'
  end

  # Set up filenames and directories.
  zipdir = File.join Rails.root, *%w[public downloads exports hmdb spectra_mzml]
  FileUtils.mkpath(zipdir)
  new_zipfile_name = File.join zipdir, "#{filename}_#{zipdirname}.new.zip"
  final_zipfile_name = new_zipfile_name.remove(/.new/)

  begin
    # Set up the scopes
    scopes = nil
    if zipdirname == "predicted_msms_spectra" || zipdirname == "experimental_msms_spectra"
      scopes = Spectrum.msms_spectra
    elsif zipdirname == "predicted_cms_spectra" || zipdirname == "experimental_cms_spectra"
      scopes = Spectrum.cms_spectra
    end
      
    scopes = scopes.map do |klass|
      scope =
        if source.present?
          klass.where(database_registrations: { source: source }).merge(DatabaseRegistration.exported)
        else
          klass
        end
      scope.joins(:database_registrations).includes(:references, :peaks).merge(DatabaseRegistration.exported)
    end

    # Set up the progress bar
    total_count = scopes.map(&:count).sum
    puts "Building #{final_zipfile_name} with #{total_count} potential documents."
    progress = ProgressBar.new total_count

    # Remove partially complete zipfile if it exists
    File.delete(new_zipfile_name) if File.exists?(new_zipfile_name)

    Zip::ZipFile.open(new_zipfile_name, Zip::ZipFile::CREATE) do |zipfile|
      scopes.each do |scope|
        selector = "#{scope.klass.table_name}.*, database_registrations.database_id AS database_id"
        scope.select(selector).find_each do |spectrum|
          id = source.nil? ? spectrum.inchi_key : spectrum.database_id
          type = scope.klass.table_name.downcase
          if type == "c_ms" || type == "ms_ms"
            predicted_spectrum = spectrum.predicted ? 'predicted' : 'experimental'
            if predicted_spectrum != status
              next
            else
              filename = "#{id}_#{scope.klass.table_name}_spectrum_#{spectrum.id}_#{predicted_spectrum}.mzml"
            end
          else
            filename = "#{id}_#{scope.klass.table_name}_spectrum_#{spectrum.id}.mzml"
          end
          zipfile.get_output_stream(filename) { |f| f.puts spectrum.generate_mzml }
          progress.increment!
        end
      end

      progress.count = progress.max
      progress.write
      puts "Zipping file ..."
    end

    # Overwrite old file after succesful build
    FileUtils.mv new_zipfile_name, final_zipfile_name
    `chmod a+r #{final_zipfile_name}`
  ensure
    # Remove partialy complete zipfile if it exists
    File.delete(new_zipfile_name) if File.exists?(new_zipfile_name)
    status = nil
  end
end
=end
def export_all_zip_files(database_name = nil, folder_name)
  source =
  if database_name.present? && database_name != 'all'
    Source.find_by!(name: database_name)
  else
    nil
  end

  filename = source.present? ? source.name : 'all'
  dir_name = "#{filename.downcase}_#{folder_name}"

  xml_path = "../../shared/public/downloads/exports/#{filename.downcase}/spectra_xml"
  txt_path = "../../shared/public/downloads/exports/#{filename.downcase}/peak_lists_txt"
  mzml_path = "../../shared/public/downloads/exports/#{filename.downcase}/spectra_mzml"
  path = nil
  match_xml = /spectra/.match(folder_name)
  match_txt = /peak_lists/.match(folder_name)

  if !match_xml.nil?
    path = xml_path
  elsif !match_txt.nil?
    path = txt_path
  end

  if !path.nil?
    begin
      Dir.chdir(path) do
        system "unzip '*.zip' -d #{dir_name}"
        system "zip -r #{dir_name}.zip #{dir_name}"
        system "rm -r #{dir_name}"
      end
    rescue => e
      e
    end
  end
end


namespace :export do
  task spectra_fragments: [:environment] do
    outfile = File.open("z_list","w")
    Dir.glob("c_ms/*").each do |file|
      basename = File.basename(file)
      c_ms_id = basename.split("_")[0]
      spectrum = CMs.find_by(id: c_ms_id)

      structure = spectrum.structure.smiles
      outfile.puts basename+"\t"+structure
    end
    outfile.close
  end

  task export_hmdb_registrations: [:environment] do |t,args|

    # DatabaseRegistration.update_all(exported: 0)
    hmdb_ids = File.read("exported_hmdb_ids.txt").split("\n")
    hmdb_ids.each do |id|
      if db_registration = DatabaseRegistration.find_by(database_id: id)
        # puts id
        db_registration.exported = 1
        db_registration.save!
      else
        puts id
      end
    end
  end

  desc "Export all of the items for downloads, optionally specify the database"
  task all_databases: [:environment] do |t,args|
    mailer = AdminMailer.build "SpecDB",
      subject: "XML export",
      message: "XML export of things in the database"

    begin
      databases = Source.all.pluck(:name)

      databases.each do |database|
        task("export:all_for").invoke(database)
      end
      mailer.notify_success!
    rescue => e
      mailer.notify_error! e
    end
  end


  task :export_tms_info => [:environment] do
    output = File.open("tms_info.csv","w")
    database_registrations = DatabaseRegistration.where('database_id like "HMDB%"')
    database_registrations.each do |met|
      c_ms_s = CMs.where(structure_id: met.structure_id)
      c_ms_s.each do |c_ms|
        puts met.database_id
        next if !c_ms.derivative_type.present?
        if c_ms.derivative_type.include?("TMS")
          if c_ms.derivative_exact_mass.present?
            output.puts(met.database_id + ","+c_ms.derivative_type+","+c_ms.derivative_mw.to_s+","+c_ms.derivative_exact_mass.to_s)
          else
            output.puts(met.database_id + ","+c_ms.derivative_type+","+c_ms.derivative_mw.to_s)
          end
        end
      end
    end
    output.close
  end

  desc "Export all spectra files for the specified database"
  task :all_for, [:database] => [:environment] do |t,args|
    mailer = AdminMailer.build "moldb",
      subject: "Spectra files",
      message: "Export all spectra files for the specified database"

    database = args[:database] || raise( "Need to specify a database" )
    export_tasks = [:cms_peak_lists_txt_predicted, :cms_peak_lists_txt_experimental, 
                :msms_peak_lists_txt_predicted, :msms_peak_lists_txt_experimental,
                :cms_spectra_xml_predicted, :cms_spectra_xml_experimental, 
                :msms_spectra_xml_predicted, :msms_spectra_xml_experimental,
                :nmr_peak_lists_txt, :nmr_spectra_xml,
                :all_peak_lists_txt, :all_spectra_xml,
                :image_files, :fid_files, :cms_spectra_mzml_predicted, :cms_spectra_mzml_experimental, 
                :msms_spectra_mzml_predicted, :msms_spectra_mzml_experimental ] #':jcamp_files' also used to be here but it is not used anymore so no need to download it!
    begin
      export_tasks.each do |taskname|
        task("export:#{taskname}").invoke(database)
      end
      mailer.notify_success!
    rescue StandardError => e
      msg = "[ Export Error #{Time.now} ] export:#{taskname}[#{database}] has failed: #{e.message}"
      $stderr.puts msg
      mailer.notify_error! msg
    end
  end

  desc "Export all NMR Spectra FID Files, Raw NMR Spectra Peaklist Files (TXT), NMR Spectra Files (XML) for the specified database"
  task :all_for_three_files, [:database] => [:environment] do |t,args|
    mailer = AdminMailer.build "moldb",
      subject: "Spectra files",
      message: "Export all spectra files for the specified database"

    database = args[:database] || raise( "Need to specify a database" )
    export_tasks = [:nmr_peak_lists_txt, :nmr_spectra_xml,
                :fid_files
                ] #':jcamp_files' also used to be here but it is not used anymore so no need to download it!
    begin
      export_tasks.each do |taskname|
        puts("task_name = #{taskname}")
        Rake::Task["export:#{taskname}"].invoke(database)
      end
      mailer.notify_success!
    rescue StandardError => e
      msg = "[ Export Error #{Time.now} ] export:#{taskname}[#{database}] has failed: #{e.message}"
      $stderr.puts msg
      mailer.notify_error! msg
    end
  end

  desc "Export the MassBank Records into a zipfile"
  task :massbank_records, [:database] => [:environment] do |t,args|
    selector = {
      description: ["MassBank Records"]
    }
    export_document args[:database], "massbank_records", selector
  end

  task :golm_msl_record, [:database] => [:environment] do |t,args|
    selector = {
      description: ["Golm MSL Record"]
    }
    export_document args[:database], "golm_msl_record", selector
  end

  task :protocols, [:database] => [:environment] do |t,args|
    selector = {
      description: ["Sample Preparation Protocol", "Mass Spectra Collection Protocol"]
    }
    export_document args[:database], "protocols", selector
  end

  desc "Export the spectrm image files into a zipfile"
  task :image_files, [:database] => [:environment] do |t,args|
    selector = {
      description: ["Raw Spectrum Image", "Spectra image with peak assignments"]
    }
    export_document args[:database], "image_files", selector
  end

  desc "Export FID files into a zipfile"
  task :fid_files, [:database] => [:environment] do |t,args|
    selector = {
      description: ["Raw Free Induction Decay file for spectral processing"]
    }
    export_document args[:database], "fid_files" ,selector
  end

  desc "Export the JCamp Files into a zipfile"
  task :jcamp_files, [:database] => [:environment] do |t,args|
    selector = {
      description: ["JCAMP file"]
    }
    export_document args[:database], "jcamp_files", selector
  end

  desc "Export the NMR Spectra peak lists as txt"
  task :nmr_peak_lists_txt, [:database] => [:environment] do |t,args|
    selector = {
      description: ["List of chemical shift values for the spectrum","peak assignments"]
    }
    export_document args[:database], "nmr_peak_lists", selector
  end

  desc "Export the predicted GC-MS peak lists as txt"
  task :cms_peak_lists_txt_predicted, [:database] => [:environment] do |t,args|
    selector = {
      description: ["List of m/z values for the spectrum"],
      spectra_type: "CMs"
    }
    export_document args[:database], "predicted_cms_peak_lists", selector
  end

  desc "Export the predicted MS-MS peak lists as txt"
  task :msms_peak_lists_txt_predicted, [:database] => [:environment] do |t,args|
    selector = {
      description: ["List of m/z values for the spectrum"],
      spectra_type: "MsMs"
    }
    export_document args[:database], "predicted_msms_peak_lists", selector
  end

  desc "Export the experimental GC-MS peak lists as txt"
  task :cms_peak_lists_txt_experimental, [:database] => [:environment] do |t,args|
    selector = {
      description: ["List of m/z values for the spectrum"],
      spectra_type: "CMs"
    }
    export_document args[:database], "experimental_cms_peak_lists", selector
  end

  desc "Export the experimental MS-MS peak lists as txt"
  task :msms_peak_lists_txt_experimental, [:database] => [:environment] do |t,args|
    selector = {
      description: ["List of m/z values for the spectrum"],
      spectra_type: "MsMs"
    }
    export_document args[:database], "experimental_msms_peak_lists", selector
  end

  desc "Export all peak Lists as txt"
  task :all_peak_lists_txt, [:database] => [:environment] do |t,args|
    export_all_zip_files args[:database], "all_peak_lists"
  end

  desc "Export NMR spectra records as xml"
  task :nmr_spectra_xml, [:database] => [:environment] do |t,args|
    export_xml args[:database], "nmr_spectra"
  end

  desc "Export predicted GC-MS spectra records as xml"
  task :cms_spectra_xml_predicted, [:database] => [:environment] do |t,args|
    export_xml args[:database], "predicted_cms_spectra"
  end

  desc "Export predicted MS-MS spectra records as xml"
  task :msms_spectra_xml_predicted, [:database] => [:environment] do |t,args|
    export_xml args[:database], "predicted_msms_spectra"
  end

  desc "Export experimental GC-MS spectra records as xml"
  task :cms_spectra_xml_experimental, [:database] => [:environment] do |t,args|
    export_xml args[:database], "experimental_cms_spectra"
  end

  desc "Export experimental MS-MS spectra records as xml"
  task :msms_spectra_xml_experimental, [:database] => [:environment] do |t,args|
    export_xml args[:database], "experimental_msms_spectra"
  end

  desc "Export all the spectra records as xml"
  task :all_spectra_xml, [:database] => [:environment] do |t,args|
    export_all_zip_files args[:database], "all_spectra"
  end
=begin
  desc "Export all spectra as mzml"
  task :all_spectra_mzml, [:database] => [:environment] do |t,args|
    export_mzml args[:database], "predicted_cms_spectra"
    export_mzml args[:database], "predicted_msms_spectra"
    export_mzml args[:database], "experimental_cms_spectra"
    export_mzml args[:database], "experimental_msms_spectra"
  end

  desc "Export predicted GC-MS spectra records as mzml"
  task :cms_spectra_mzml_predicted, [:database] => [:environment] do |t,args|
    export_mzml args[:database], "predicted_cms_spectra"
  end

  desc "Export predicted MS-MS spectra records as mzml"
  task :msms_spectra_mzml_predicted, [:database] => [:environment] do |t,args|
    export_mzml args[:database], "predicted_msms_spectra"
  end

  desc "Export experimental GC-MS spectra records as mzml"
  task :cms_spectra_mzml_experimental, [:database] => [:environment] do |t,args|
    export_mzml args[:database], "experimental_cms_spectra"
  end

  desc "Export experimental MS-MS spectra records as mzml"
  task :msms_spectra_mzml_experimental, [:database] => [:environment] do |t,args|
    export_mzml args[:database], "experimental_msms_spectra"
  end

  desc "Export all the spectra records as xml"
  task :all_spectra_xml, [:database] => [:environment] do |t,args|
    export_mzml args[:database], "all_spectra"
  end
=end
end