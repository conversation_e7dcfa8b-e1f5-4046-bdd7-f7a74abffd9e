namespace :nmr_one_d do

  namespace :proton do
    desc "Build svg images highlighting assignments"
    task :assignment_svg => [:environment] do
      NmrOneD.where(:nucleus => "1H").each do |spectra|
        assignment_count = spectra.peak_assignments.
          where("assignment_view_file_name is NULL").count
        next unless assignment_count > 0
        spectra.build_assignment_views
      end
    end

    desc "Index 1H-NMR peaks for similarity search."
    task :index => [:environment] do
      NmrOneDIndexer.new('1H').index!
    end
  end

  namespace :carbon do
    desc "Index 13C-NMR peaks for similarity search."
    task :index => [:environment] do
      NmrOneDIndexer.new('13C').index!
    end
  end

  desc "copy doc file of BMRB from npmrd moldb"
  task :copy_doc_file => [:environment] do
    input_file = "/apps/moldb/project/current/data/bmrb_doc.csv"
    CSV.open(input_file, 'r', :col_sep => ",", :quote_char => "|").each do |row|
      puts("started document = #{row[0].to_i}")
      doc = Document.find(row[0].to_i)
      url = doc.file.url
      puts("url = #{url}")
      system "scp -r moldb@34.136.195.198:project/current/public#{url.split("?")[0]} /apps/moldb/project/current/data/bmrb_doc/"
    end
  end
  
  desc "Fill the distinct_peaks column on all nmr_one_d's for NMR Search prefiltering"
  task :fill_distinct_peaks => [:environment] do
    progress = ProgressBar.new(NmrOneD.count)
    NmrOneD.find_each do |nmr|
      nmr.set_distinct_peaks
      progress.increment!
    end
  end

  desc "Update the peak_counter column to ensure it is correct for prefiltering"
  task :update_peak_counter => [:environment] do
    progress = ProgressBar.new(NmrOneD.count)
    NmrOneD.find_each do |nmr|
      nmr.update(peak_counter: nmr.peaks.count)
      progress.increment!
    end
  end

  desc "Populate the peak_position_ppm column in cases where the chemical_shift column contains the correct value"
  task :peak_position_correction => [:environment] do
    progress = ProgressBar.new(NmrOneDPeak.count)
    NmrOneDPeak.find_each do |peak|
      if peak.peak_position_ppm.nil?
        if peak.chemical_shift.present?
          peak.update(peak_position_ppm: peak.chemical_shift)
        elsif peak.chemical_shift_true.present?
          peak.update(peak_position_ppm: peak.chemical_shift_true)
        else
          puts "Unclear peak position for nmr_one_d_peak #{peak.id}"
        end
      end
      progress.increment!
    end
  end

end
