namespace :cms do
  # Just to check if any of the GC-MS spectra have peak assignments
  task :check_for_peak_assignments => [:environment] do
    count = 0
    with_assignments = []
    MsMs.all.each do |spectrum|
      if spectrum.instrument_type =~ /^GC/
        count = count + 1
        puts spectrum.instrument_type
        spectrum.peaks.each do |peak|
          if peak.peak_assignments.count > 0
            with_assignments << spectrum
            break
          end
        end
      end
    end
    puts "Total GC-MS spectra: " + count.to_s
    puts "GC-MS spectra with assignments: " + with_assignments.uniq.count.to_s
  end

  task :set_predicted => [:environment] do
    CMs.all.each do |cms|
      cms.predicted = false
      cms.save!
    end

  end

  desc "add monoisotopic mass for tms derivative"
  task :tms_mono => [:environment] do 
    CSV.open("public/tms_derivatives_without_exact_mass.tsv", "r", :col_sep => "\t", :quote_char => "\x00").each do |row|
      c = CMs.where(structure_id: row[0], derivative_formula: row[1], derivative_type: row[2], derivative_mw: row[3]).first
      puts "started for #{c.id}"
      c.derivative_exact_mass = row[4].to_f
      c.save!
      puts "#{c.derivative_exact_mass}"
    end
  end

  # Fix GC-MS table
  task :create_from_ms_ms => [:environment] do

    # Remove all GC-MS spectra from HMDB that are currently in the table, they suck
    CMs.destroy_all

    # Move all spectra from the MsMs table that have GC instruments, these are clearly in the wrong place
    MsMs.all.each do |msms|
      if msms.instrument_type =~ /^GC/
        puts "Moving MsMs: " + msms.id.to_s
        cms = CMs.new
        cms.inchi_key = msms.inchi_key
        cms.notes = msms.notes
        cms.sample_concentration = msms.sample_concentration
        cms.solvent = msms.solvent
        cms.sample_mass = msms.sample_mass
        cms.sample_assessment = msms.sample_assessment
        cms.spectra_assessment = msms.spectra_assessment
        cms.sample_source = msms.sample_source
        cms.collection_date = msms.collection_date
        cms.instrument_type = msms.instrument_type
        cms.peak_counter = msms.peak_counter
        cms.chromatography_type = "GC"
        cms.sample_concentration_units = msms.sample_concentration_units
        cms.sample_mass_units = msms.sample_mass_units
        cms.molecule_id = msms.molecule_id
        cms.ionization_mode = msms.ionization_mode

        # Also move peaks
        msms.peaks.each do |msms_peak|
          cms_peak = CMsPeak.new
          cms_peak.mass_charge = msms_peak.mass_charge
          cms_peak.intensity = msms_peak.intensity
          cms.peaks << cms_peak
        end

        # Create documents
        msms.documents.each do |msms_doc|
          cms_doc = Document.new()
          cms_doc.spectra_type = "CMs"
          cms_doc.description = msms_doc.description

          # Import data from documents
          if msms_doc.description == "MassBank Record"
            cms = parse_massbank(cms, msms_doc)
          elsif msms_doc.description == "Golm MSL Record"
            cms = parse_golm(cms, msms_doc)
          end

          cms_doc.file = msms_doc.file
          cms.documents << cms_doc
        end

        # Create references
        msms.references.each do |msms_ref|
          cms_ref = Reference.new(msms_ref.attributes.except("id", "spectra_id", "spectra_type", "created_at", "updated_at"))
          cms_ref.spectra_type = "CMs"
          cms.references << cms_ref
        end

        # Create spectra_molecule_link
        cms_link = SpectraMoleculeLink.new(msms.spectra_molecule_link.attributes.except("id", "spectra_id", "spectra_type", "created_at", "updated_at"))
        cms_link.spectra_type = "CMs"
        cms.spectra_molecule_link = cms_link

        # Create spectra_sops
        msms.spectra_sops.each do |msms_sop|
          cms_sop = SpectraSop.new(msms_sop.attributes.except("id", "spectra_id", "spectra_type", "created_at", "updated_at"))
          cms_sop.spectra_type = "CMs"
          cms.spectra_sops << cms_sop
        end

        # FINALLY SAVE IT
        cms.save!

        # Remove original MsMs record and it's associations
        msms.destroy

      end
    end
  end
end

# Parse info from MassBank record
def parse_massbank(cms, doc)
  File.open(doc.file.path, "r").each_line do |line|
    if line.strip =~ /^AC\$CHROMATOGRAPHY: COLUMN_NAME ([^\n\r\f]+)$/
      cms.column_type = $1
    elsif line.strip =~ /^AC\$CHROMATOGRAPHY: RETENTION_INDEX ([\d\.]+)$/
      cms.retention_index = $1.to_f
    elsif line.strip =~ /^AC\$CHROMATOGRAPHY: RETENTION_TIME ([\d\.]+)/
      cms.retention_time = $1.to_f
    elsif line.strip =~ /^MS\$FOCUSED_ION: BASE_PEAK ([\d]+)$/
      cms.base_peak = $1.to_i
    elsif line.strip =~ /^MS\$FOCUSED_ION: DERIVATIVE_TYPE ([^\n\r\f]+)$/
      cms.derivative_type = $1
    elsif line.strip =~ /^MS\$FOCUSED_ION: DERIVATIVE_FORM (\S+)$/
      cms.derivative_formula = $1
    elsif line.strip =~ /^MS\$FOCUSED_ION: DERIVATIVE_MASS ([\d\.]+)$/
      cms.derivative_mw = $1.to_f
    end
  end
  return cms
end

# Parse info from Golm record
def parse_golm(cms, doc)
  File.open(doc.file.path, "r").each_line do |line|
    if line.strip =~ /^MST N: .* \((\d)MEOX\) \((\d)TMS\)/
      cms.derivative_type = $1 + " MEOX; " + $2 + " TMS"
    elsif line.strip =~ /^MST N: .* \((\d)TMS\)/
      cms.derivative_type = $1 + " TMS"
    elsif line.strip =~ /^RI: ([\d\.]+)$/
      cms.retention_index = $1.to_f
    elsif line.strip =~ /^RI VAR5 ALK: TRUE$/ || line.strip =~ /^RI VAR5 ALK: PRED$/
      cms.ri_type = "based on 9 n-alkanes (C10–C36)"
      cms.column_type = "5%-phenyl-95%-dimethylpolysiloxane capillary column"
    elsif line.strip =~ /^RI MDN35 FAME: TRUE$/ || line.strip =~ /^RI MDN35 FAME: PRED$/
      cms.ri_type = "based on 13 fatty acid methyl esters (C8 ME–C30 ME)"
      cms.column_type = "35%-phenyl-65%-dimethylpolysiloxane capillary column"
    elsif line.strip =~ /^RI VAR5 FAME: TRUE$/ || line.strip =~ /^RI VAR5 FAME: PRED$/
      cms.ri_type = "based on 13 fatty acid methyl esters (C8 ME–C30 ME)"
      cms.column_type = "5%-phenyl-95%-dimethylpolysiloxane capillary column"
    elsif line.strip =~ /^RI MDN35 ALK: TRUE$/ || line.strip =~ /^RI MDN35 ALK: TRUE$/
      cms.ri_type = "based on 9 n-alkanes (C10–C36)"
      cms.column_type = "35%-phenyl-65%-dimethylpolysiloxane capillary column"
    elsif line.strip =~ /^FORM: (\S+)$/
      if cms.derivative_type.present?
        cms.derivative_formula = $1
      end
    elsif line.strip =~ /^MW: ([\d,]+)$/
      if cms.derivative_type.present?
        cms.derivative_mw = $1.gsub(",", ".").to_f
      end
    end
  end
  return cms
end

