namespace :update_ids do
  desc "copy paste ids from database_registrations table to curation.results[identifiers]"
  task :copy_identifiers_to_curation => [:environment] do
    count = 0
    Structure.find_each do |s|
      # s = Structure.find_by(id: 21952)
      # hmdb_id = nil
      exported_in_ymdb = false
      exported_in_ecmdb = false
      exported_in_bmdb = false
      ymdb_id = nil
      ecmdb_id = nil
      bmdb_id = nil
      count+= 1
      puts "#{count} >> #{s.id}"
      # if s.database_registrations.where("database_id LIKE 'YMDB%' OR database_id LIKE 'ECMDB%' OR database_id LIKE 'BMDB%'").present?
      if s.database_registrations.where("database_id LIKE 'PW_C%'").present?
        # if s.database_registrations.where("database_id LIKE 'HMDB%'").present?
        #   hmdb_id = s.database_registrations.where("database_id LIKE 'HMDB%'").pluck(:database_id)
        # end
        if s.database_registrations.where("database_id LIKE 'YMDB%'").present?
          if s.database_registrations.where("database_id LIKE 'YMDB%'").exported == 1
            exported_in_ymdb = true
            ymdb_id = s.database_registrations.where("database_id LIKE 'YMDB%'").pluck(:database_id)
          end
        end
        if s.database_registrations.where("database_id LIKE 'ECMDB%'").present?
          if s.database_registrations.where("database_id LIKE 'ECMDB%'").exported == 1
            exported_in_ecmdb = true
            ecmdb_id = s.database_registrations.where("database_id LIKE 'ECMDB%'").pluck(:database_id)
          end
        end
        if s.database_registrations.where("database_id LIKE 'BMDB%'").present?
          if s.database_registrations.where("database_id LIKE 'BMDB%'").exported == 1
            exported_in_bmdb = true
            bmdb_id = s.database_registrations.where("database_id LIKE 'BMDB%'").pluck(:database_id)
          end
        end

        if !s.curation.nil?
          s.curation.json_to_class
          curation = s.curation
          ids = curation.identifiers
          # ids["hmdb_id"] = hmdb_id[0] if !hmdb_id.nil?
          ids["ymdb_id"] = (exported_in_ymdb ? ymdb_id[0] : nil) if !ymdb_id.nil?
          ids["ecmdb_id"] = (exported_in_ecmdb ? ecmdb_id[0] : nil) if !ecmdb_id.nil?
          ids["bmdb_id"] = (exported_in_bmdb ? bmdb_id[0] : nil) if !bmdb_id.nil?

          s.curation.identifiers = ids
          s.curation.save!
        end
      end
    end
  end

desc "Take csv from CL and delete where they do not join(foodb)"
  task :delete_ids_for_foodb => [:environment] do |t, arg|
    foodb_ids = []
    moldb_foodb_ids = DatabaseRegistration.where("database_id LIKE 'FDB%'").map{|dr| dr.database_id}
    csv = CSV.foreach("data/foodb_ids.csv", :headers => true) do |row|
      foodb_ids << row["public_id"]
    end
    mismatch1 = moldb_foodb_ids - foodb_ids
    print "Mismatch 1 on #{moldb_foodb_ids - foodb_ids}"
    print "Mismatch 2 on #{foodb_ids - moldb_foodb_ids}"

    puts "Mismatch 1 count = #{(moldb_foodb_ids - foodb_ids).length}"
    puts "Mismatch 2 count = #{(foodb_ids - moldb_foodb_ids).length}"

    #mismatch1.each do |mis_id| 
      dr = DatabaseRegistration.find_by(database_id: "FDB088764")
    #  if mis_id = "FDB073432"
        dr.destroy!
    #  end
    #end
  end

end