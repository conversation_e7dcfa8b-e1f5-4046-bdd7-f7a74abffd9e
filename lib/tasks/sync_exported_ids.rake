require 'builder'
require 'admin_mailer'

URLs = {
  "hmdb" => "http://hmdb.ca/exported_in_hmdb.json".freeze,
  "foodb" => "http://foodb.ca/exported_in_foodb.json".freeze,
  "ymdb" => "http://ymdb.ca/exported_in_ymdb.json".freeze,
  "ecmdb" => "http://m2mdb.wishartlab.com/exported_in_ecmdb.json".freeze,
  "bmdb" => "http://bmdb.wishartlab.com/exported_in_bmdb.json".freeze
}

def sync_ids(database_name)
  choices = nil
  if database_name == 'all'
    choices = URLs
  else
    choices = URLs.select { |key, value| key == database_name.downcase }
  end
  prefix = nil
  choices.each do |k,v|
    if k == "hmdb"
      prefix = "HMDB"
    elsif k == "foodb"
      prefix = "FDB"
    elsif k == "ymdb"
      prefix = "YMDB"
    elsif k == "ecmdb"
      prefix = "ECMDB"
    elsif k == "bmdb"
      prefix = "BMDB"
    end
    mailer = AdminMailer.build "moldb", 
      subject: "sync exported #{k} ids", 
      message: "Synchronize the exported #{k} ids in #{k} with moldb."

    max_retries = 3
    times_retried = 0

    begin
      # reset all to 0 first
      DatabaseRegistration.where('database_id LIKE ?', "#{prefix}%").update_all(exported: 0)

      # then update 
      open(v) {|f| @data = JSON.load(f.read)}
      count = 0
      @data.each do | entry |
        c = DatabaseRegistration.find_by(database_id: entry["exported_id"].chomp)
        if !c.nil?
          count+= 1
          c.exported = 1
          c.save!
        end
      end
      msg = count.to_s + " exported #{k} ids found."
      puts msg
      mailer.notify_success!
    rescue => e
      puts "#{e.backtrace}"
      if times_retried < max_retries
        times_retried+= 1
        puts "Failed to sync, retry #{times_retried}/#{max_retries}"
        retry
      else
        puts "Exiting script. #{k} might be down!"
        raise if Rails.env.development?
        mailer.notify_error! e
      end
    end
  end
end

namespace :sync_with_moldb do
	desc "Sync ids that are exported in other databases with moldb"
	task :exported_ids_for, [:database] => [:environment] do |t, arg|
    database = arg[:database] || raise( "Need to specify a database" )
    sync_ids(database)
  end
end