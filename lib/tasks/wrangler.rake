require 'memory_profiler'
namespace :wrangler do
  desc "Run Data-Wrangler on any structures that haven't had
        the pleasure of being wrangled"
  task run: [:environment] do
    Structure.find_each do |structure|
      CurationJob.perform_later(structure.id, false)
    end
  end

###################################################################################################
  task rerun: [:environment] do
    STDOUT.sync = true
    STDERR.sync =  true
    ActiveSupport::Deprecation.silenced = true 
    count = 0
    DatabaseRegistration.find(:batch_size => 100) do |reg|
      next if !reg.database_id.include? "HMDB"
      count += 1
      $stdout.puts "STRUCTURE_ID = #{reg.structure_id}"
      $stdout.puts "DATABASE_ID = #{reg.database_id}"
      #structure = Structure.find_by(id: reg.structure_id)
      CurationJob.perform_now(reg.structure_id, false,true)
      GC.start if count % 10 == 0
      sleep(10) if count % 150 ==0
    end
  end

  task recache_NP_curations: [:environment] do
    Rails.cache.delete_matched("NP*/curation.json")
  end

  task run_curation_by_struct_id: [:environment] do
    curation = Curation.find_by(structure_id: ENV['struct_id'])
    #curation.result.gsub!("GlcNAc", "NAcGln")
    CurationJob.perform_now(ENV['struct_id'], false)
    curation = Curation.find_by(structure_id: ENV['struct_id'])
    puts curation.result["identifiers"]
    curation.save!
    puts curation.result.synonyms
  end

  task pipelined_moldb_curations: [:environment] do
    STDOUT.sync = true
    STDERR.sync =  true
    ActiveSupport::Deprecation.silenced = true
    count = 0
    batch = 0
    Moldb::SynonymSource
    Moldb::Source
    Moldb::Synonym
    Moldb::Curation
    Moldb::CurationJob
    DatabaseRegistration.where(["database_registrations.exported = ? and database_registrations.database_id LIKE '\%NP\%' and database_registrations.created_at > '2019-10-01'",  "1"]).find_in_batches(batch_size: 32) do |group|
      start = Time.now
      puts "Processing Group #{batch}"
      puts "Inchikeys:"
      group.map!{|reg| reg = Structure.find(reg.structure_id)}
      group.each{|struct| puts struct.inchikey}
      puts "Symbol Size: #{Symbol.all_symbols.size}"
      puts "Symbol ByteSize (KB): #{(Symbol.all_symbols.to_s.bytesize / 1000).round(3)}"
      puts "Group ByteSize (KB): #{(group.to_s.bytesize / 1000).round(3)}"
      child_pid_1 = fork {CurationJob.perform_now(group[0..7], false)}
      sleep 0.1
      child_pid_2 = fork {CurationJob.perform_now(group[8..15], false)}
      sleep 0.1
      child_pid_3 = fork {CurationJob.perform_now(group[16..23], false)}
      sleep 0.1
      child_pid_4 = fork {CurationJob.perform_now(group[24..31], false)}
      sleep 0.1
      Process.wait(child_pid_1)
      Process.wait(child_pid_2)
      Process.wait(child_pid_3)
      Process.wait(child_pid_4)
      GC.start
      finish = Time.now
      puts "Time for 32 Descriptions is: #{(finish-start).round(3)}"
      puts "TIme Per Description is :#{((finish-start)/32).round(3)}"
      count += 1
      batch += 1
      if count == 8
        puts "Going to sleep for 10 seconds"
        sleep (10)
        count = 0
      end
      ApplicationRecord.connection.reconnect!
      #ApplicationRecord.clear_active_connections!
    end
  end


###################################################################################################

  task check_csv: [:environment] do
    STDOUT.sync = true
    STDERR.sync =  true
    ActiveSupport::Deprecation.silenced = true 
    count = 0
    compounds = []
    CSV.foreach("bmdb.csv", :headers => true) do |row|
      compounds << [row['bmdb_id'],row['moldb_inchikey']]
    end
    bad_compounds =[]
    compounds.each do |row|
      count += 1
      reg = DatabaseRegistration.find_by(database_id: row[0])
      $stdout.puts "#{count}/#{compounds.length}"
      $stdout.puts "STRUCTURE_ID = #{reg.structure_id}"
      $stdout.puts "DATABASE_ID = #{reg.database_id}"
      result = Curation.where(structure_id: reg.structure_id).pluck(:result)
      if result[0].nil?
        bad_compounds.append([row[0],row[1]])
      else
        cs_desc  = JSON.parse(result[0])["cs_descriptions"]
        if cs_desc.nil?
          bad_compounds.append([row[0],row[1]])
        else
          if cs_desc["5"].nil?
             bad_compounds.append([row[0],row[1]])
          end
        end
      end
    end
    print bad_compounds
  end

  task run_csv_curation: [:environment] do
    STDOUT.sync = true
    STDERR.sync =  true
    ActiveSupport::Deprecation.silenced = true 
    count = 0
    compounds = []
    CSV.foreach("input.csv", :headers => true) do |row|
      compounds << [row['bmdb_id']]
    end
    compounds.each do |row|
      count += 1
      reg = DatabaseRegistration.find_by(database_id: row[0])
      $stdout.puts "#{count}/#{compounds.length}"
      $stdout.puts "STRUCTURE_ID = #{reg.structure_id}"
      $stdout.puts "DATABASE_ID = #{reg.database_id}"
      #structure = Structure.find_by(id: reg.structure_id)
      CurationJob.perform_now(reg.structure_id, false,true)
      GC.start if count % 10 == 0
      sleep(10) if count % 150 ==0
    end
  end
   task check_csv: [:environment] do
    STDOUT.sync = true
    STDERR.sync =  true
    ActiveSupport::Deprecation.silenced = true 
    count = 0
    compounds = []
    CSV.foreach("input.csv", :headers => true) do |row|
      compounds << [row['bmdb_id']]
    end
    bad_compounds =[]
    compounds.each do |row|
      count += 1
      reg = DatabaseRegistration.find_by(database_id: row[0])
      $stdout.puts "#{count}/#{compounds.length}"
      $stdout.puts "STRUCTURE_ID = #{reg.structure_id}"
      $stdout.puts "DATABASE_ID = #{reg.database_id}"
      result = Curation.where(structure_id: reg.structure_id).pluck(:result)
      if result[0].nil?
        bad_compounds.append(row[0])
      else
        cs_desc  = JSON.parse(result[0])["cs_descriptions"]
        if cs_desc.nil?
          bad_compounds.append(row[0])
        else
          if cs_desc["5"].nil?
             bad_compounds.append(row[0])
          end
        end
      end
    end
    print bad_compounds
  end

  task get_phytohub: [:environment] do
    compounds = []
    CSV.foreach("input.csv", :headers => true) do |row|
        compounds << [row['ORIGINAL NAME'],row['DATAWRANGLER NAME'],row['IUPAC NAME'],row['ORIGINAL SMILES'], row['DATAWRANGLER SMILES'],
              row['INCHIKEY'],row['INCHI'],row['HMDB_ID'],row['DRUGBANK_ID'],
              row['PUBCHEM_CID'],row['FOODB_ID'],row['KEGG_ID'],row['CHEBI_ID'],"",row['SYNONYMS']]
    end
   CSV.open("output.csv", "wb") do |csv|
      compounds.each do |row|
        begin
          puts row[5]
          puts Structure.find_by(inchikey: row[5]).inspect
          dbs = DatabaseRegistration.where(:structure_id => Structure.find_by(inchikey: row[5]).id).pluck(:database_id)
          row[13] = dbs.select{ |db| (db.include? "PHUB")&&(!db.include? "TEST")}.first if dbs.present?
      
        rescue Exception => e
          puts e
        end
        csv << row
      end   
    end
  end

  desc "Destroy recurring cron jobs built earlier for example IdentifierWorker"
  task destroy_cron_jobs: [:environment] do
    Sidekiq::Cron::Job.all.each do |job|
      job.destroy
    end
  end

  desc "delete synonym(s) that are found to be wrong, or do not belong to the metabolite"
  task delete_syns: [:environment] do
    db_id = "HMDB0031243"
    delete_list = ["2-Methylpropanal oxime", "Isobutyraldehyde oxime"]

    # delete synonyms from curation
    str_id = DatabaseRegistration.find_by(database_id: db_id).structure_id
    structure = Structure.find_by(id: str_id)
    structure.curation.json_to_class
    curation = structure.curation
    syns = curation.synonyms
    # syns is an Array
    syns.each do |syn|
      if delete_list.include? syn["name"]
        indx = syns.index(syn)
        syns.delete_at(indx)
      end
    end
    structure.curation.synonyms = syns
    structure.curation.save!

    # delete synonyms from synonyms table
    syns_ids_to_delete = []
    delete_list.each do |syn|
      this = Synonym.where(structure_id: str_id, name: syn).first
      if !this.nil?
        syns_ids_to_delete << this.id
        puts "Found #{this.id}; #{syn}"
      else
        puts "#{syn} not found!"
      end
    end
    if syns_ids_to_delete.any?
      syns_ids_to_delete.each do |i|
        Synonym.find(i).destroy
        puts "Deleted: #{i}"
      end
    else
     puts "Nothing to delete!"
    end
  end

  # before running it test with singel compund at first
  desc "update the cs_description part only in curation"
  task update_cs_description: [:environment] do
    # CSV.open('public/description_Hasan.csv', 'r', :col_sep => ",", :quote_char => "\x00").each do | hmdbid |
    registrations = DatabaseRegistration.where('database_id like "NP%" and database_id > \'NP0000179\' and exported = 1')
    count = 0
    registrations.each do |db|
      count+=1
      # db = DatabaseRegistration.find_by_database_id(hmdbid[0])
      # puts "hmdb_id = #{hmdbid[0]}"
      #database_registrations = DatabaseRegistration.where("database_id like 'HMDB%'").each do |db|
      # if !db.nil?

        structure = Structure.find_by(id: db.structure_id)
        #structures.each do |structure|
        UpdateCsDescriptionJob.perform_now(structure)
        puts "#{count}) Done for: #{db.database_id}"
      # else
      #   puts "#{db.database_id} is not present"
      # end
    end
  end

  desc "update the cs_description part only in curation"
  task update_cs_description_nonlipds: [:environment] do
    registrations = DatabaseRegistration.where('database_id like "HMDB%" and database_id > \'HMDB0012102\' and exported = 1')
    count = 0
    registrations.each do |r|
      count+=1
      structure = Structure.find_by(id: r.structure_id)
      is_lipid = false
      structure.curation.json_to_class
      curation = structure.curation
      if curation.classifications.present?
        klass = curation.classifications[0]["superklass"]["name"]
        if not klass.blank?
          if klass.downcase == "lipids and lipid-like molecules"
            is_lipid = true
          end
        end
      end
      if not is_lipid
        UpdateCsDescriptionJob.perform_now(structure)
        puts "#{count}) Done for: #{r.database_id}"
      else
        puts "#{count}) #{r.database_id} is a lipid, no description was generated!"
      end
    end
  end

  desc "update the cs_description part only in curation"
  task update_cs_description_format: [:environment] do
    registrations = DatabaseRegistration.where('database_id like "HMDB%"')
    registrations.each do |db|
      next if !db.structure.curation
      db.structure.curation.json_to_class
      c = db.structure.curation
      # Curation.all.each do |c|
      # result = c.result
      next if !c.cs_description
      if c.cs_description["name"]
        # puts "CS inside Name: "
        # puts c.cs_description["name"]
      else
        puts db.database_id
        puts c.structure_id

        new_desc = Hash.new
        new_desc["name"] = c.cs_description
        new_desc["source"] = "ChemoSummarizer"
        new_desc["kind"] = "Description"
        unless c.cs_description.nil?
          c.cs_description = new_desc
          c.save!
        end
      end
      # end

      if c.cs_description.key?("name")
        if c.cs_description["name"].key?("name")
          puts "Duplicate: "+db.database_id + " "+structure.id.to_s
        end
      end
      
    end
  end

  desc "Run DataWrangler on FooDB compounds and create cs_hash in curation"
    task update_cs_description_for_foods: [:environment] do
      registrations = DatabaseRegistration.where('database_id like "FDB%"')
      count = 0
      registrations.each do |db|
      count +=1 
      next if !db.structure.curation

   

      structure = Structure.find_by(id: db.structure_id)
      is_lipid = false
      structure.curation.json_to_class
      curation = structure.curation
      if curation.classifications.present?
        klass = curation.classifications[0]["superklass"]["name"]
        if not klass.blank?
          if klass.downcase == "lipids and lipid-like molecules"
            is_lipid = true
          end
        end
        if count == 100
          false_count = 0
          while !checkDataWranglerJob.perform_now("QTBSBXVTEAMEQO-UHFFFAOYSA-N") # check if acetic acid checks out
            sleep(30.hour)
            false_count+=1
            if false_count == 24
              exit(1)
            end
          end
          count = 0
        end
        if not is_lipid
          UpdateCsHashJob.perform_now(structure)
          puts "#{count}) Done for: #{r.database_id}"
        else
          puts "#{count}) #{r.database_id} is a lipid, no description was generated!"
        end
      end
    end
  end




  desc "Run DataWrangler on empty classifications"
  # Required format: First column should be HMDB ID, second column should be InChIKey (may need to be without 'InChIKey=' prefix)
  task classify: [:environment] do
     Structure.find_in_batches.each do |group|
      group.each do |structure|
        ClassificationJob.perform_now(structure, false)
        puts "Done for #{structure.inchikey}"
      end 
    end
  end
  desc "count unclassified compounds in moldb"
  task count_unclassified: [:environment] do
    unclassified_count = 0
     Structure.find_in_batches.each do |group|
      group.each do |structure|
         if structure.curation.present?
          structure.curation.json_to_class
          unclassified_count += 1 if structure.curation.classifications.empty?
        else
          unclassified_count += 1
        end
      end
      puts unclassified_count 
    end
  end


  desc "Run DataWrangler to fetch metbuilder synonyms and add to curation"
  task metbuilder_synonyms: [:environment] do
    CSV.open('public/Lipid_HMDB_Hasan.tsv', 'r', :col_sep => "\t", :quote_char => "\x00").each do | s |
      structure = Structure.find_by_inchikey(s[2])
      puts "started #{structure.inchikey}"
      UpdateSynonymMetbuilderJob.perform_later(structure)
    end
  end

  desc "Reclassify compounds from a list"
  task reclassify: [:environment] do
    count = 0
    CSV.open("public/metabolites_without_classifications.tsv", 'r', :col_sep => "\t", :quote_char => "\x00").each do | s |
      count += 1
      database_registrations = DatabaseRegistration.where(database_id: ["#{s[0]}"])
      if database_registrations.empty? || database_registrations.nil?
        puts "Could not find #{s[0]}"
      else
        structures = Structure.where(id: database_registrations.pluck(:structure_id))
        if structures.nil? || structures.empty?
          puts "Could not find structure for #{s[0]}"
        else
          structures.each do |structure|
            ClassificationJob.perform_later(structure, false)
            puts "Done for: #{s[0]}, count: #{count}"
          end
        end
      end
    end
  end

  desc "Run DataWrangler on empty classifications form screen_2"
  task classify_1: [:environment] do
    CSV.open('public/HMDB_no_chem_classification_11.tsv', 'r', :col_sep => "\t", :quote_char => "\x00").each do | s |
    #CSV.open('public/test_2.tsv', 'r', :col_sep => "\t", :quote_char => "\x00").each do | s |
    #structure = Structure.find_by_inchikey("XFNJVJPLKCPIBV-UHFFFAOYSA-N")
    structure = Structure.find_by_inchikey(s[1])
    #Structure.find_each do |structure|
      #ClassificationJob.perform_later(structure, false)
      ClassificationJob.perform_later(structure, false)
      puts "done for #{s[0]} #{s[1]}"
      sleep 10
    end
  end

  desc "Run Datawrangler to submit query and fetch the query id"
  task submit_query_classyfire: [:environment] do
    #f = File.open('public/job_id_hmdb.tsv', "w")
    f = File.open('public/job_id_hmdb_final_checking.tsv', "w")
    #CSV.open('public/HMDB_no_chem_classification_with_smiles.tsv', 'r', :col_sep => "\t", :quote_char => "\x00").each do | query |
    CSV.open('public/HMDB_no_chem_classification_final_check.tsv', 'r', :col_sep => "\t", :quote_char => "\x00").each do | query |
      classify = DataWrangler::Model::ClassyFireCompound.new
      id = classify.post_json("http://classyfire.wishartlab.com/queries.json", query[1])
      f.write("#{query[0]}\t#{id}\t#{query[1]}\t#{query[2]}\n")
      puts "id is #{id} for #{query[1]}"
    
    end
    f.close
  end

  desc "Run Datawrangler to fetch classify with query id"
  task add_classification: [:environment] do
    #CSV.open('public/job_id_hmdb_with_newline.tsv', 'r', :col_sep => "\t", :quote_char => "\x00").each do | row |
    #CSV.open('public/HMDB_no_chem_classification_from_job.tsv', 'r', :col_sep => "\t", :quote_char => "\x00").each do | row |
    CSV.open('public/hmdb_id_job_id.tsv', 'r', :col_sep => "\t", :quote_char => "\x00").each do | row |
      dr = DatabaseRegistration.where("database_id" => row[0].strip).first
      structure= Structure.find_by_id(dr.structure_id)
      structure.curation.json_to_class
      curation = structure.curation
      puts "structure for id #{row[0]} before #{curation.structure.smiles}\t#{curation.structure.inchikey}"
      classify = DataWrangler::Model::ClassyFireCompound.new
      id = row[1]
      open("http://classyfire.wishartlab.com/queries/"+id.to_s+".json") {|f| 
              @data = JSON.load(f.read)}
      wrangler = classify.parse_query(@data)
      curation.classifications = JSON.parse(wrangler.classifications.to_json)
      curation.save! 
      puts "structure for id #{row[0]} after #{curation.structure.smiles}\t#{curation.structure.inchikey}"
      #puts "#{curation.classifications} is saved"
      structure.bust_cache
    end
  end




  desc "Run DataWrangler on empty classifications form screen_3"
  task classify_2: [:environment] do
    CSV.open('public/HMDB_no_chem_classification_12.tsv', 'r', :col_sep => "\t", :quote_char => "\x00").each do | s |
    #CSV.open('public/test_2.tsv', 'r', :col_sep => "\t", :quote_char => "\x00").each do | s |
    #structure = Structure.find_by_inchikey("XFNJVJPLKCPIBV-UHFFFAOYSA-N")
    structure = Structure.find_by_inchikey(s[1])
    #Structure.find_each do |structure|
      #ClassificationJob.perform_later(structure, false)
      ClassificationJob.perform_later(structure, false)
      puts "done for #{s[0]} #{s[1]}"
      sleep 10
    end
  end





  #desc "Run DataWrangler on empty classifications for HMDB"
  desc "Run DataWrangler on empty classifications for DrugBank"
  task classify_DrugB: [:environment] do
    CSV.open('public/DrugB_no_chem_classification.tsv', 'r', :col_sep => "\t", :quote_char => "\x00").each do | structure_id |
      structure = Structure.find(structure_id[2].to_i)
      ClassificationJob.perform_later(structure, false)
    end
  end

  desc "Run DataWrangler on any Drugbank drugs for getting 3d structures"
  task get_3d_sdf: [:environment] do
    DatabaseRegistration.where("database_id like 'DB%'").each do |db|
      StructureJob.perform_later(db.structure, true)
    end
  end

  desc "propogate those structures that were fixed in hmdb to all other databases"
  task reset_structure: [:environment] do
    count = 0
    not_fixed = 0
    outfile = File.open('public/foodb_structure_backup.tsv', 'w')
    not_found_ids = File.open('public/not_found.txt', 'w')
    outfile.write("foodb_id" + "\t" + "old_strucutre_id" + "\t" + "new_structure_id" + "\n")
    CSV.open('public/hmdb_foodb.tsv', 'r', :col_sep => "\t", :quote_char => "\x00").each do | row |
      count+= 1
      dbr_hmdb = DatabaseRegistration.where(database_id: row[0].strip).first
      dbr_foodb = DatabaseRegistration.find_by(database_id: row[1].strip)
      if !dbr_hmdb.nil? && !dbr_foodb.nil?
        hmdb_structure_id = dbr_hmdb.structure_id
        dbr_foodb_old_structure_id = dbr_foodb.structure_id
        dbr_foodb.update_attributes(:structure_id => hmdb_structure_id)
        dbr_foodb_new_structure_id = dbr_foodb.structure_id
        outfile.write(row[1] + "\t" + dbr_foodb_old_structure_id.to_s + "\t" + dbr_foodb_new_structure_id.to_s + "\n")
        puts "#{count}) Done for #{row[1]}"
      else
        not_fixed+= 1
        puts "#{count}) >> Record(s) not found! <<"
        if dbr_hmdb.nil? && !dbr_foodb.nil? 
          not_found_ids.write("#{row[0]}" + "\n")
        elsif dbr_foodb.nil? && !dbr_hmdb.nil?
          not_found_ids.write("#{row[1]}" + "\n")
        else
          not_found_ids.write("#{row[0]} and #{row[1]}" + "\n")
        end
      end
    end
    puts "#{not_fixed} records were not fixed!"
  end

  task reannotate: [:environment] do
    file = File.open("public/reannotate.txt", "r")
    count = 0
    file.each do |line|
      count+= 1
      db = DatabaseRegistration.where(database_id: line.strip).first
      if !db.nil?
        puts "#{count}) Working on: #{line}"
        StructureJob.perform_now(db.structure, true)
      else
        puts "#{line} not found"
      end
    end
  end

  task recalculate_adducts: [:environment] do
    file = File.open("public/recalculate_adducts.txt", "r")
    count = 0
    file.each do |line|
      count+= 1
      db = DatabaseRegistration.where(database_id: line.strip).first
      if !db.nil?
        puts "#{count}) Working on: #{line}"
        AdductJob.perform_now(db.structure_id)
      else
        puts "#{line} not found"
      end
    end
  end

  desc "Update identifiers table based on contents of curations table"
  task update_identifiers: [:environment] do
    Structure.find_in_batches.each do |group|
      group.each do |structure|
        c = structure.curation

        if c.present?

          if (i = Identifier.where("structure_id = " + c.structure_id.to_s).first).nil?
            i = Identifier.new(structure_id: c.structure_id.to_i)
          end

          c.json_to_class

          i.kegg_id = c.identifiers["kegg_id"]
          i.chemspider_id = c.identifiers["chemspider_id"]
          i.name = c.identifiers["name"]
          i.kegg_drug_id = c.identifiers["kegg_drug_id"]
          i.chembl_id = c.identifiers["chembl_id"]
          i.drugbank_id = c.identifiers["drugbank_id"]
          i.iuphar_id = c.identifiers["iuphar_id"]
          i.chebi_id = c.identifiers["chebi_id"]
          i.emolecules_id = c.identifiers["emolecules_id"]
          i.ibm_patent_id = c.identifiers["ibm_patent_id"]
          i.patent_id = c.identifiers["patent_id"]
          i.surechem_id = c.identifiers["surechem_id"]
          i.pubchem_thomson_id = c.identifiers["pubchem_thomson_id"]
          i.pubchem_id = c.identifiers["pubchem_id"]
          i.molport_id = c.identifiers["molport_id"]
          i.nikkaji_id = c.identifiers["nikkaji_id"]
          i.ymdb_id = c.identifiers["ymdb_id"]
          i.pdbe_id = c.identifiers["pdbe_id"]
          i.fda_srs_id = c.identifiers["fda_srs_id"]
          i.hmdb_id = c.identifiers["hmdb_id"]
          i.actor_id = c.identifiers["actor_id"]
          i.bindingdb_id = c.identifiers["bindingdb_id"]
          i.zinc_id = c.identifiers["zinc_id"]
          i.mcule_id = c.identifiers["mcule_id"]
          i.recon_id = c.identifiers["recon_id"]
          i.t3db_id = c.identifiers["t3db_id"]
          i.pharmkgb_id = c.identifiers["pharmkgb_id"]
          i.nmrshiftdb_id = c.identifiers["nmrshiftdb_id"]
          i.foodb_id = c.identifiers["foodb_id"]
          i.ecmdb_id = c.identifiers["ecmdb_id"]
          i.cas = c.identifiers["cas"]
          i.selleck_id = c.identifiers["selleck_id"]
          i.lincs_id = c.identifiers["lincs_id"]
          i.pubchem_dotf_id = c.identifiers["pubchem_dotf_id"]
          i.atlas_id = c.identifiers["atlas_id"]
          i.nih_id = c.identifiers["nih_id"]
          i.ligand_expo_id = c.identifiers["ligand_expo_id"]
          i.phenol_id = c.identifiers["phenol_id"]
          i.meta_cyc_id = c.identifiers["meta_cyc_id"]
          i.wikipedia_id = c.identifiers["wikipedia_id"]
          i.knapsack_id = c.identifiers["knapsack_id"]
          i.bigg_id = c.identifiers["bigg_id"]
          i.nugowiki_id = c.identifiers["nugowiki_id"]
          i.metagene_id = c.identifiers["metagene_id"]
          i.metlin_id = c.identifiers["metlin_id"]
          i.iupac_name = c.identifiers["iupac_name"]

          i.save!

        end
      end
    end
  end

  desc "Print both pubchem references + moldb references"
  task pubchem_refs: [:environment] do
    file = File.open('moldb_references', 'r')
    output = File.open('all_references', 'w')
    file.readlines.each do |line|
      row = line.split("\t")
      if row[2].present?
        compound = DataWrangler::Model::PubchemCompound.get_refs_by_id(row[2].to_s)
        references = ""
        compound.references.each do |ref|
          references = references + ref.pubmed_id.to_s + ";"
        end
        output.write(row[0] + "\t" + row[1] + "\t" + row[2].to_s + "\t" + references +
           "\t" + compound.references.count.to_s + "\n")
      else
        output.write(row[0] + "\t" + row[1] + "\t" + row[2].to_s + "\n")
      end
      puts row[0]
    end
    output.close
  end

  desc "Print from curations table references"
  task print_references: [:environment] do
    f = File.open("refernces", 'w')
    Structure.all.each do |structure|
      #group.each do |structure|
        c = structure.curation
        #id = structure.database_registrations.first.database_id
        if structure.database_registrations.first.nil?
          id = ''
        else
          id = structure.database_registrations.first.database_id
        end

        if c.present?
          c.json_to_class
          f.write(id.to_s "\t" + structure.inchikey + "\t" + c.identifiers[:pubchem_id].to_s + "\t" + c.references.to_json + "\n")
        end
      #end
    end
  end
end
