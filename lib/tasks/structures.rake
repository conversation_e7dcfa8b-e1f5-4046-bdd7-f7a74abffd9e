require 'csv'

namespace :structures do

  # Given a list of structure ids, output a list of structure ids and corresponding SMILES
  # strings.
  #
  # Usage:
  #   bundle exec rake structures:get_smiles['infile.csv','outfile.tsv']
  desc "Obtain SMILES strings for a list of structure ids"
  task :get_smiles, [:infile, :outfile] => [:environment] do |t, args|

    if args[:infile].nil? or args[:outfile].nil?
      puts "Usage: bundle exec rake structures:get_smiles['infile.csv','outfile.tsv']."
      exit
    end

    out = File.open(args[:outfile], "w")
    CSV.open(args[:infile], "r", :col_sep => ",").each do |row|
      structure_id = row[0].strip
      structure = Structure.find(structure_id)
      format = structure.formats.find_by(name: 'smiles')
      if format.nil?
        STDERR.puts "Missing SMILES: " + structure_id
      else
        out.write(structure_id + "\t" + format.value + "\n")
      end
    end
    out.close
  end
  
  task :create_fingerprints => [:environment] do
    Structure.find_each do |structure|
      structure.create_fingerprints
    end
  end

  task :delete_struct =>[:environment] do
    Structure.find("987195").destroy!
    Structure.find("987196").destroy!
    Structure.find("987198").destroy!
  end

  task :test_chem_client =>[:environment] do
    puts ChemClient.create_structure("KUJDTEXVBGIHGN-UHFFFAOYSA-N")
  end


  task :bust_cache_structure_by_csv, [:csv_file] => [:environment] do |t,args|
    CSV.open(args[:csv_file],"r", :col_sep => ",").each do |row|
      structure_id = row[0].strip
      begin
        database_id =  row[1].strip
        Rails.cache.delete_matched("*#{database_id}*")
        puts "Database registration cache busted"
      rescue Exception => e
        puts e.message
      end

      begin
        structure = Structure.find(structure_id)
        structure.bust_cache
        puts "Structure #{structure_id} cache has been busted"
      rescue Exception => e
        Rails.cache.delete_matched("*structure*/#{structure_id}*")
      end
    end
  end


  task :bust_cache_for_database, [:database_id_prefix] => [:environment] do |t,args|
    Rails.cache.delete_matched("*#{args[:database_id_prefix]}*")
  end

end

