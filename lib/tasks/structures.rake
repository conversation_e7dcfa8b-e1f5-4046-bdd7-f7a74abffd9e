require 'csv'

namespace :structures do

  # Given a list of structure ids, output a list of structure ids and corresponding SMILES
  # strings.
  #
  # Usage:
  #   bundle exec rake structures:get_smiles['infile.csv','outfile.tsv']
  desc "Obtain SMILES strings for a list of structure ids"
  task :get_smiles, [:infile, :outfile] => [:environment] do |t, args|

    if args[:infile].nil? or args[:outfile].nil?
      puts "Usage: bundle exec rake structures:get_smiles['infile.csv','outfile.tsv']."
      exit
    end

    out = File.open(args[:outfile], "w")
    CSV.open(args[:infile], "r", :col_sep => ",").each do |row|
      structure_id = row[0].strip
      structure = Structure.find(structure_id)
      format = structure.formats.find_by(name: 'smiles')
      if format.nil?
        STDERR.puts "Missing SMILES: " + structure_id
      else
        out.write(structure_id + "\t" + format.value + "\n")
      end
    end
    out.close
  end
  
  desc "Generate Morgan fingerprints for all structures using RDKit"
  task :create_fingerprints => [:environment] do
    puts "Starting Morgan fingerprint generation for all structures..."

    total_count = Structure.count
    processed_count = 0
    success_count = 0
    error_count = 0

    puts "Total structures to process: #{total_count}"

    Structure.find_each(batch_size: 100) do |structure|
      processed_count += 1

      begin
        # Generate Morgan fingerprint using RDKit
        fingerprint = generate_morgan_fingerprint(structure.original_structure)

        if fingerprint
          structure.update_column(:morgan_fingerprint, fingerprint)
          success_count += 1
          puts "✓ [#{processed_count}/#{total_count}] Structure #{structure.id} (#{structure.inchikey}): fingerprint generated"
        else
          error_count += 1
          puts "✗ [#{processed_count}/#{total_count}] Structure #{structure.id} (#{structure.inchikey}): failed to parse structure"
        end

      rescue => e
        error_count += 1
        puts "✗ [#{processed_count}/#{total_count}] Structure #{structure.id} (#{structure.inchikey}): error - #{e.message}"
      end

      # Progress update every 100 structures
      if processed_count % 100 == 0
        puts "Progress: #{processed_count}/#{total_count} processed (#{success_count} success, #{error_count} errors)"
      end
    end

    puts "\n=== Fingerprint Generation Complete ==="
    puts "Total processed: #{processed_count}"
    puts "Successful: #{success_count}"
    puts "Errors: #{error_count}"
    puts "Success rate: #{(success_count.to_f / processed_count * 100).round(2)}%"
  end

  # Helper method to generate Morgan fingerprint using RDKit
  def generate_morgan_fingerprint(structure)
    script = <<~PYTHON
      import sys
      import json
      from rdkit import Chem
      from rdkit.Chem import rdMolDescriptors

      def parse_structure(structure):
          try:
              # Debug: Show structure format info
              print(f"Structure length: {len(structure)}", file=sys.stderr)
              print(f"First 100 chars: {repr(structure[:100])}", file=sys.stderr)

              # Check if it looks like a MOL block
              is_mol_like = any(marker in structure for marker in ['V2000', 'V3000', 'M  END'])
              print(f"Appears to be MOL block: {is_mol_like}", file=sys.stderr)

              # Try parsing as MOL block first (most common format in original_structure)
              if is_mol_like or len(structure) > 50:  # MOL blocks are typically longer
                  mol = Chem.MolFromMolBlock(structure, sanitize=False)
                  if mol is not None:
                      try:
                          Chem.SanitizeMol(mol)
                          print(f"Parsed as MOL block: {mol.GetNumAtoms()} atoms", file=sys.stderr)
                          return mol
                      except Exception as sanitize_error:
                          print(f"MOL block parsed but sanitization failed: {sanitize_error}", file=sys.stderr)
                          # Try without sanitization
                          mol = Chem.MolFromMolBlock(structure, sanitize=False)
                          if mol is not None and mol.GetNumAtoms() > 0:
                              print(f"Using unsanitized MOL: {mol.GetNumAtoms()} atoms", file=sys.stderr)
                              return mol

              # Try parsing as SMILES (strip whitespace for SMILES)
              smiles_candidate = structure.strip()
              if len(smiles_candidate) < 200 and not '\n' in smiles_candidate:  # SMILES are typically short and single-line
                  mol = Chem.MolFromSmiles(smiles_candidate)
                  if mol is not None:
                      print(f"Parsed as SMILES: {mol.GetNumAtoms()} atoms", file=sys.stderr)
                      return mol

              # Try parsing as InChI
              if structure.strip().startswith('InChI='):
                  mol = Chem.MolFromInchi(structure.strip())
                  if mol is not None:
                      print(f"Parsed as InChI: {mol.GetNumAtoms()} atoms", file=sys.stderr)
                      return mol

              print("Failed to parse structure with any format", file=sys.stderr)
              return None
          except Exception as e:
              print(f"Error parsing structure: {e}", file=sys.stderr)
              return None

      # Read structure from input file
      with open(sys.argv[1], 'r') as f:
          structure = f.read()  # Don't strip - MOL blocks need exact formatting

      # Parse the structure
      mol = parse_structure(structure)

      if mol is not None:
          try:
              # Generate Morgan fingerprint as bit vector
              fp = rdMolDescriptors.GetMorganFingerprintAsBitVect(mol, 2, nBits=2048)

              # Convert to hex string for storage
              fp_hex = fp.ToBitString()

              with open(sys.argv[2], 'w') as f:
                  f.write(fp_hex)
          except Exception as e:
              print(f"Error generating fingerprint: {e}", file=sys.stderr)
              with open(sys.argv[2], 'w') as f:
                  f.write("")
      else:
          with open(sys.argv[2], 'w') as f:
              f.write("")
    PYTHON

    begin
      result = Jchem.run_rdkit(script, structure)
      return result.strip.empty? ? nil : result.strip
    rescue => e
      Rails.logger.error "Morgan fingerprint generation error: #{e.message}"
      return nil
    end
  end

  desc "Update Morgan fingerprints for structures missing fingerprints"
  task :update_missing_fingerprints => [:environment] do
    puts "Updating Morgan fingerprints for structures with missing fingerprints..."

    missing_structures = Structure.where(morgan_fingerprint: [nil, ""])
    total_count = missing_structures.count

    if total_count == 0
      puts "No structures missing fingerprints found."
      return
    end

    puts "Found #{total_count} structures missing fingerprints"

    processed_count = 0
    success_count = 0
    error_count = 0

    missing_structures.find_each(batch_size: 100) do |structure|
      processed_count += 1

      begin
        fingerprint = generate_morgan_fingerprint(structure.original_structure)

        if fingerprint
          structure.update_column(:morgan_fingerprint, fingerprint)
          success_count += 1
          puts "✓ [#{processed_count}/#{total_count}] Structure #{structure.id}: fingerprint generated"
        else
          error_count += 1
          puts "✗ [#{processed_count}/#{total_count}] Structure #{structure.id}: failed to parse structure"
        end

      rescue => e
        error_count += 1
        puts "✗ [#{processed_count}/#{total_count}] Structure #{structure.id}: error - #{e.message}"
      end

      if processed_count % 50 == 0
        puts "Progress: #{processed_count}/#{total_count} processed"
      end
    end

    puts "\n=== Missing Fingerprints Update Complete ==="
    puts "Total processed: #{processed_count}"
    puts "Successful: #{success_count}"
    puts "Errors: #{error_count}"
  end

  desc "Regenerate Morgan fingerprint for a specific structure by ID"
  task :regenerate_fingerprint, [:structure_id] => [:environment] do |t, args|
    if args[:structure_id].blank?
      puts "Usage: bundle exec rake structures:regenerate_fingerprint[STRUCTURE_ID]"
      exit
    end

    structure = Structure.find(args[:structure_id])
    puts "Regenerating fingerprint for structure #{structure.id} (#{structure.inchikey})"

    fingerprint = generate_morgan_fingerprint(structure.original_structure)

    if fingerprint
      old_fingerprint = structure.morgan_fingerprint
      structure.update_column(:morgan_fingerprint, fingerprint)
      puts "✓ Fingerprint updated successfully"
      puts "  Old: #{old_fingerprint ? old_fingerprint[0..50] + '...' : 'nil'}"
      puts "  New: #{fingerprint[0..50]}..."
    else
      puts "✗ Failed to generate fingerprint for this structure"
    end
  rescue ActiveRecord::RecordNotFound
    puts "✗ Structure with ID #{args[:structure_id]} not found"
  rescue => e
    puts "✗ Error: #{e.message}"
  end

  desc "Show Morgan fingerprint statistics"
  task :fingerprint_stats => [:environment] do
    puts "=== Morgan Fingerprint Statistics ==="

    total_structures = Structure.count
    with_fingerprints = Structure.where.not(morgan_fingerprint: [nil, ""]).count
    without_fingerprints = total_structures - with_fingerprints

    puts "Total structures: #{total_structures}"
    puts "With fingerprints: #{with_fingerprints}"
    puts "Without fingerprints: #{without_fingerprints}"
    puts "Coverage: #{total_structures > 0 ? (with_fingerprints.to_f / total_structures * 100).round(2) : 0}%"

    if with_fingerprints > 0
      # Sample fingerprint lengths
      sample_fingerprints = Structure.where.not(morgan_fingerprint: [nil, ""]).limit(10).pluck(:morgan_fingerprint)
      if sample_fingerprints.any?
        lengths = sample_fingerprints.map(&:length)
        puts "\nSample fingerprint lengths: #{lengths.join(', ')}"
        puts "Average length: #{(lengths.sum.to_f / lengths.length).round(2)}"
      end
    end

    puts "\n=== Recent Updates ==="
    recent_updates = Structure.where.not(morgan_fingerprint: [nil, ""]).order(updated_at: :desc).limit(5)
    recent_updates.each do |structure|
      puts "Structure #{structure.id} (#{structure.inchikey}): #{structure.morgan_fingerprint[0..30]}..."
    end
  end

  task :delete_struct =>[:environment] do
    Structure.find("987195").destroy!
    Structure.find("987196").destroy!
    Structure.find("987198").destroy!
  end

  task :test_chem_client =>[:environment] do
    puts ChemClient.create_structure("KUJDTEXVBGIHGN-UHFFFAOYSA-N")
  end


  task :bust_cache_structure_by_csv, [:csv_file] => [:environment] do |t,args|
    CSV.open(args[:csv_file],"r", :col_sep => ",").each do |row|
      structure_id = row[0].strip
      begin
        database_id =  row[1].strip
        Rails.cache.delete_matched("*#{database_id}*")
        puts "Database registration cache busted"
      rescue Exception => e
        puts e.message
      end

      begin
        structure = Structure.find(structure_id)
        structure.bust_cache
        puts "Structure #{structure_id} cache has been busted"
      rescue Exception => e
        Rails.cache.delete_matched("*structure*/#{structure_id}*")
      end
    end
  end


  task :bust_cache_for_database, [:database_id_prefix] => [:environment] do |t,args|
    Rails.cache.delete_matched("*#{args[:database_id_prefix]}*")
  end

end

