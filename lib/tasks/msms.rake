namespace :msms do

  desc "Delete paperclip images from assignments"
  task delete_msms_images: [:environment] do

    MsMsPeakAssignment.all.find_in_batches do |group|
      sleep(50)
      group.each do |assignment|
        smiles = assignment.smiles
        if assignment.image_url.nil?
          inchikey = ChemConvert.inchikey(smiles)
          @structure = Structure.find_or_create_by!(inchikey: inchikey) do |structure|
            structure.original_structure = smiles
          end
          assignment.structure = @structure
          assignment.image_url = 'http://moldb.np-mrd.org/structures/' + @structure.inchikey + '/' + 'image.png'
        end
        if assignment.assignment_view.present?
          assignment.assignment_view = nil
        end
        assignment.save!
      end
    end
  end


  desc "Import csv spectra record"
  task import_csv: [:environment] do
    CSV.foreach("data/britz-mckibbin_spectra.csv", :headers => true) do |spectrum|
      msms = MsMs.new
      inchikey = spectrum["Inchikey"]
      smiles = spectrum["Smiles"]
      msms.structure_id = Structure.find_by(inchikey: inchikey).id
      msms.instrument_type = spectrum["Instrument"]
      msms.ionization_mode = spectrum["Ionization Mode"]
      msms.collision_energy_level = spectrum["Collision Energy Level"]
      msms.collision_energy_voltage = spectrum["Collision Voltage"]
      msms.collection_date = spectrum["Date"]
      msms.solvent = spectrum["Solvent"]
      msms.sample_source = spectrum["Sample Source"]
      msms.sample_concentration = spectrum["Sample Concentration"]
      msms.sample_mass = spectrum["Sample Mass"]
      msms.sample_concentration_units = spectrum["Sample Concentration Units"]
      msms.sample_mass_units = spectrum["Sample Mass Units"]
      msms.references << Reference.new(ref_text: spectrum["Reference"])
      m_z = spectrum["m/z"].split(",")
      intensity = spectrum["intensity"].split(",")
      (0..m_z.length-1).each do |i|
        puts "#{m_z[i]} #{intensity[i]}"
        msms.peaks << MsMsPeak.new(mass_charge: m_z[i], intensity: intensity[i])
      end
      msms.save!
    end
  end

  desc "Generate missing splash keys"
  task generate_splash: [:environment] do
    MsMs.where(splash_key: 1).find_each().each do |msms|
      msms.splash_key = nil
      msms.save!
    end
    MsMs.where(splash_key: nil).find_each().each do |msms|
      msms.regenerate_splash_key
      #msms.save!
    end
  end

  namespace :massbank do
    desc "Import massbank folder"
    task :import_folder,[:folder] => [:environment] do |t,args|
      Dir.new(args.folder).each do |filename|
        if filename =~ /\.txt/i
          mb = MassBankRecord.new
          mb.record = File.new("#{args.folder}/#{filename}").read
          mb.record.each_line do |line|
            line.chomp!
            if line =~ /ACCESSION: (.*)/
              mb.accession = $1
            elsif line =~ /CH\$IUPAC: (.*)/
              inchikey = `molconvert inchikey -s "#{$1}"{inchi}`
              inchikey.strip!
              mb.inchi_key = inchikey
            end
          end
          mb.save!
        end
      end
    end



    desc "Import Mass Back Records"
    task import: [:environment] do
      MassBankRecord.transaction do
        MassBankRecord.all.each do |mbr|
          puts "Adding #{mbr.accession}"

          msms = MsMs.new
          msms.references << Reference.new(pubmed_id: ********, database: "MassBank", database_id: mbr.accession)

          f = File.new("/tmp/#{mbr.accession}.txt", "w")
          f.write(mbr.record)
          f.flush
          f.close

          d = Document.new
          d.description = "MassBank Record"
          f = File.open(f)
          d.file = f
          f.close

          msms.documents << d

          species = nil
          sample = nil
          instrument_type = nil
          instrument = nil
          mbr.record.each_line do |line|
            line.chomp!
            case
            when line.match(/ACCESSION:\s(.*)/)
              # accession = $1
            when line.match(/CH\$IUPAC: (InChI=.*)/)
              inchi = $1
              inchikey = FormatConverter.inchi_to_inchikey(inchi).results.sub(/InChIKey=/,"")
              msms.inchi_key = inchikey
            when line.match(/PUBLICATION:\s(.*)/)
              publication = $1
              if publication =~ /PMID:\s?(\d+)/
                msms.references << Reference.new(pubmed_id: $1)
              else
                msms.references << Reference.new(ref_text: publication)
              end
            when line.match(/SP\$SCIENTIFIC_NAME\s(.*)/)
              species = $1
            when line.match(/SP\$SAMPLE\s(.*)/)
              sample = $1
            when line.match(/AC\$INSTRUMENT:\s(.*)/)
              instrument = $1
            when line.match(/AC\$INSTRUMENT_TYPE:\s(.*)/)
              instrument_type = $1
            when line.match(/AC\$MASS_SPECTROMETRY:\s(.*)/)
              sub_line = $1
              case
              when sub_line.match(/MS_TYPE\s(.*)/)
                ms_type = $1
              when sub_line.match(/ION_MODE\s(.*)/)
                msms.ionization_mode = $1.capitalize
              when sub_line.match(/COLLISION_ENERGY\s(\d+\.?\d*) V/)
                msms.collision_energy_voltage = $1
              end
            when line.match(/^\s\s?(\d+\.?\d*)\s(\d+\.?\d*e?\d*)\s(\d+)/)
              # puts line
              msms.peaks << MsMsPeak.new(mass_charge: $1, intensity: ($3.to_f/999))
            else

            end
          end

          if MsMs.joins(:references).where(
            inchi_key: msms.inchi_key, references: {database: "MassBank"}).any?
            puts "Found #{mbr.accession} already"
            next
          end

          msms.sample_source = "#{species} -- #{sample}" if species && sample
          msms.sample_source = species if species && !sample
          msms.sample_source = sample if !species && sample

          msms.instrument_type = "#{instrument_type} (#{instrument})" if instrument_type || instrument

          msms.save!
        end

      end
    end
  end
  namespace :golm do
    namespace :import do
      desc "Import Golm MSL file typ"
      task :msl, [:filename] => [:environment] do |t,args|
        record = ""
        File.new(args.filename).each_line do |line|
          record += line
          if line.blank?
            parse_single_msl(record)
            record = ""
          end
        end
      end
    end
  end
end



def parse_single_msl(record)
  inchi = ""
  inchikey = ""
  peaks = []
  id = ""
  record.each_line do |line|
    line.chomp!
    if line =~ /MET_INCHI: (.*)/
      inchi = $1
    elsif line =~ /MET_INCHIKEY: (.*)/
      inchikey = $1
    elsif line =~ /DB\#: (.*)/
      id = $1
    elsif line =~ /^\(\d+\s+\d+\)/
      # puts line
      line.scan(/\(\d+\s+\d+\)/).each do |segment|
        if segment =~ /\((\d+)\s+(\d+)\)/
          peaks.push([$1,$2])
        end
      end
    end
  end
  return if MsMs.joins(:references).where("`references`.database = 'Golm' AND `references`.database_id = '#{id}'").count > 0
  return if inchi.blank?

  puts "Adding #{id}"
  ms = MsMs.new
  ms.instrument_type = "GC-MS"
  ms.inchi_key = inchikey

  m = Molecule.find_by_moldb_inchi(inchi)
  if m.nil?
    m = Molecule.new
    m.structure = inchi
    m.save!
  end

  ms.molecule = m

  ms.references << Reference.new(pubmed_id: 20526350, database: "Golm", database_id: id)

  peaks.each do |peak|
    # puts peak
    ms.peaks << MsMsPeak.new(mass_charge: peak[0], intensity: (peak[1].to_f/1000))
  end

  f = File.new("#{id}.txt", "w")
  f.write(record)
  f.flush

  d = Document.new
  d.description = "Golm MSL Record"
  d.file = f

  ms.documents << d

  ms.save!
end
