require 'sidekiq/api'

namespace :sidekiq_tasks do
  
  # Delete jobs from the Retry queue that failed because of a specific error.
  # This task can be run on the production server using:
  #   RAILS_ENV=production bundle exec rake sidekiq_tasks:delete_classification_jobs
  # See https://github.com/mperham/sidekiq/wiki/API
  # See http://www.rubydoc.info/github/mperham/sidekiq/Sidekiq/RetrySet
  # See https://github.com/mperham/sidekiq/wiki/Error-Handling
  task delete_classification_jobs: [:environment] do
    r = Sidekiq::RetrySet.new
    r.select do |retri|
      #puts "#{retri.args[0]["job_class"]} #{retri.args[0]["job_id"]} #{retri["error_message"]}"
      if retri.args[0]["job_class"] == "ClassificationJob" && retri["error_message"] == "undefined method `curation' for nil:NilClass"
        puts "Deleting #{retri.args[0]["job_class"]} #{retri.args[0]["job_id"]} #{retri["error_message"]}"
        retri.delete
      end
    end
  end
  
end
