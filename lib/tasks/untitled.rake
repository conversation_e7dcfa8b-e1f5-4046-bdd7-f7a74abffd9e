require 'builder'
require 'admin_mailer'
require 'progress_bar'

namespace :nmr do
 	desc "Fix improper temperature type"
 	task :fix_celcius => [:environment] do
 		NmrOneD.where(:sample_temperature_units => "Celcius").each do |one_d|
 			one_d.sample_temperature_units = "Celsius"
 			one_d.save!
 		end
 		NmrTwoD.where(:sample_temperature_units => "Celcius").each do |two_d|
 			two_d.sample_temperature_units = "Celsius"
 			two_d.save!
 		end
 	end
end

namespace :cache do

	desc "Clear cache for EiMs"
	task :clear_eims => [:environment] do
		EiMs.each do |eims|
			EiMs.save!
		end
	end
end