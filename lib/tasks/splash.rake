namespace :splash do

  desc "Generate the splash keys for spectra without a splash key yet"
  task generate: [:environment] do
    MsMs.transaction do
      [MsMs,EiMs,CMs].each do |klass|
        klass.where(splash_key:nil).each do |spectrum|
          begin
            # puts klass
            spectrum.regenerate_splash_key
            spectrum.save!
            # spectrum.reload    
          rescue
            puts "NOT Generated"
            next
          end
        end
      end
    end
    # end
  end

  desc "Regenerate the splash keys for all spectra"
  task regenerate: [:environment] do
    MsMs.transaction do
      [MsMs,EiMs,CMs].each do |klass|
        klass.all.each do |spectrum|
          spectrum.save!
        end
      end
    end
  end

  desc "Test mzml"
  task mzml: [:environment] do
    spectrum = MsMs.find_by(id: 4)
    puts spectrum.to_mz_ml(request)
  end

  desc "Test Splash Generation"
  task test_splash:  [:environment] do
    peaks = [[57.06987671, 1.261473083], [71.01275576, 0.6191804732], [71.08552677, 1.26922643], [85.02840582, 0.7530235725], [85.10117684, 0.6895142304], [99.04405588, 0.7236359028], [113.132477, 0.7593345745], [123.1168269, 0.5360538517], [127.148127, 1.163550368], [141.1637771, 1.17126413], [155.1066561, 0.7134982535], [155.1794272, 0.6829844181], [169.1223062, 0.7166497941], [181.1950772, 0.5391271825], [183.2107273, 0.7649345903], [197.2263773, 1.168008026], [207.2107273, 0.8742538294], [211.2420274, 1.157114032], [225.1849065, 0.7185219963], [225.2576775, 1.432865099], [237.221292, 0.6529147649], [239.2005565, 0.7166497941], [249.2576775, 1.784133536], [251.2733275, 9.530124602], [253.2162066, 0.9231948613], [253.2889776, 1.548968123], [255.2318567, 1.309798809], [261.2576775, 0.7719109789], [279.2682422, 18.42944671], [281.2475067, 0.7127750583], [297.2788068, 23.18389007]]
    puts SpectrumHash.from_peaks(peaks).splash
  end
end
