require 'csv'

namespace :cfmid do
  # Classes handled by rules-based method
  CLASSES_HANDLED = ['1-monoacylglycerols', '2-monoacylglycerols', '1,2-diacylglycerols', 'Triacylglycerols', '1,2-diacylglycerol-3-phosphates', 'Phosphatidylcholines', 'Phosphatidylethanolamines', 'Lysophosphatidylcholines', 'Lysophosphatidic acids', 'Phosphatidylserines', 'Ceramides', 'Sphingomyelins', 'Cardiolipins', 'Phosphatidylglycerols', 'Lysophosphatidylglycerols', '1-alkyl,2-acylglycero-3-phosphocholines', '1-(1Z-alkenyl)-glycero-3-phosphocholines', 'Monoalkylglycerophosphocholines', '1-(1Z-alkenyl),2-acyl-glycerophosphocholines', 'Phosphatidylinositols', 'Lysophosphatidylinositols']

  # ClassyFire levels to search in
  CLASSYFIRE_LEVELS = ["ct_parent", "subklass", "klass", "superklass", "direct_parent"]

  # Requires data/pathbank_compounds.txt
  desc "Gets structure ID and SMILES of all structures in MolDB that belong to a class in the hash"
  task get_rules_based_structures: [:environment] do
    exported_pathbank_compounds_file = "data/pathbank_compounds.txt" # Input file: list of exported pathbank compound pw_ids (one per line)
    structure_ids_missing_curations_file = "structure_ids_missing_curations.txt" # Output file
    curation_ids_missing_classifications_file = "curation_ids_missing_classifications.txt" # Output file
    rules_based_structures_file = "rules_based_structures.tsv" # Output file: InChIKey | SMILES

    # Get list of structure ids that are missing curations (write to file)
    structure_ids = get_exported_structure_ids(exported_pathbank_compounds_file)
    structure_ids_with_curations = Curation.where(:structure_id => structure_ids).pluck(:structure_id).uniq.sort
    structure_ids_missing_curations = structure_ids - structure_ids_with_curations
    print_array_to_file(structure_ids_missing_curations_file, structure_ids_missing_curations)
    
    # Process curations
    curation_ids_missing_classifications = []
    rules_based_structures = []
    classes_handled_hash = Hash[CLASSES_HANDLED.map {|x| [x, 0]}] # Counts the occurrence of each class
    curations_count = structure_ids_with_curations.size
    percentage_displayed = 0 # Keeps track of percentage completed

    Curation.where(:structure_id => structure_ids_with_curations).find_each(:batch_size => 1000).with_index do |curation, i|
      json_hash = JSON.parse(curation.result)
      classifications = json_hash["classifications"]

      if classifications.empty?
        curation_ids_missing_classifications.push(curation.id)
      else
        classifications.each do |classification|
          classes_handled_hash, rules_based_structures = process_classification(curation.structure_id, classification, classes_handled_hash, rules_based_structures)
        end
      end

      percentage_displayed = percent_complete(curations_count, percentage_displayed, i)
    end

    # Write results to files
    rules_based_structures = grab_structure_data(rules_based_structures)
    print_array_to_file(curation_ids_missing_classifications_file, curation_ids_missing_classifications)
    print_nested_array_to_tsv(rules_based_structures_file, rules_based_structures)
    puts("Number of Rule-Based Structures: #{rules_based_structures.size}")
    print_hash_to_screen(classes_handled_hash)
  end

  desc "Gets structure ID and SMILES of all structures in MolDB that do not have predicted spectra in the ms-ms table"
  task get_structures_without_esi: [:environment] do
    exported_pathbank_compounds_file = "data/pathbank_compounds.txt" # Input file: list of exported pathbank compound pw_ids
    structures_without_esi_file = "structures_without_esi.tsv" # Output file: InChIKey | SMILES

    # Subtract structure ids with spectra from total structure ids
    structure_ids = get_exported_structure_ids(exported_pathbank_compounds_file)
    structure_ids_with_spectra = MsMs.where(:structure_id => structure_ids, :predicted => 1).pluck(:structure_id).uniq.sort
    structure_ids_missing_spectra = (structure_ids - structure_ids_with_spectra).uniq.sort

    # Write results to files
    structures_without_esi = grab_structure_data(structure_ids_missing_spectra)
    print_nested_array_to_tsv(structures_without_esi_file, structures_without_esi)
    puts("Number of Structures Without Predicted ESI Spectra: #{structure_ids_missing_spectra.size}")
  end

  desc "Gets exported structure ids from MolDB"
  task get_exported_structures: [:environment] do
    exported_pathbank_compounds_file = "data/pathbank_compounds.txt" # Input file: list of exported pathbank compound pw_ids
    exported_structures_file = "exported_structures.txt" # Output file: list of exported structure ids

    structure_ids = get_exported_structure_ids(exported_pathbank_compounds_file)
    print_array_to_file(exported_structures_file, structure_ids)
    puts("Number of Exported Structures: #{structure_ids.size}")
  end

  desc "Gets experimental MolDB spectra for exported structures and certain instruments"
  task get_cfmid_experimental_spectra: [:environment] do
    exported_pathbank_compounds_file = "data/pathbank_compounds.txt" # Input file: list of exported pathbank compound pw_ids
    lcms_instruments_file = "data/lcms_instruments.txt" # Input file: list of LC-MS instruments
    gcms_instruments_file = "data/gcms_instruments.txt" # Input file: list of GC-MS instruments

    drugbank = Source.find_by(name: "drugbank").id
    chemdb = Source.find_by(name: "chemdb").id
    hmdb = Source.find_by(name: "hmdb").id
    lcms = File.readlines(lcms_instruments_file).map(&:strip)
    gcms = File.readlines(gcms_instruments_file).map(&:strip)
    structure_ids = get_exported_structure_ids(exported_pathbank_compounds_file)

    CSV.open("cfmid_experimental.csv", "w") do |csv|
      csv << ["structure_id", "drugbank_id", "chemdb_id", "hmdb_id", "name",
              "inchi", "inchikey", "smiles", "mass", "derivative_type",
              "c_ms_experimental",
              "ms_ms_experimental"]
      structure_ids.each do |structure_id|
        structure = Structure.find(structure_id)
        next if structure.nil?
        curation = structure.curation
        name = curation.present? ? JSON.parse(curation.result).dig("identifiers", "name") : structure.traditional_name

        drugbank_mol = structure.database_registrations.where(source_id: drugbank).first
        chemdb_mol = structure.database_registrations.where(source_id: chemdb).first
        hmdb_mol = structure.database_registrations.where(source_id: hmdb).first
        # TO DO: Filter these by instrument
        c_ms = structure.c_ms.experimental.where(derivative_type: nil).where(ionization_mode: ["positive", "negative"])
        c_ms.to_a.select! { |s| gcms.include?(s.instrument_type.gsub(/\([^\)]+\)/, "").strip) }
        # Only take ms_ms where we know the collision energy
        ms_ms = structure.ms_ms.experimental.where('collision_energy_voltage IS NOT NULL').where(ionization_mode: ["positive", "negative"])
        ms_ms.to_a.select! { |s| lcms.include?(s.instrument_type.gsub(/\([^\)]+\)/, "").strip) }

        if c_ms.present? || ms_ms.present?
          csv << [structure.id,
                  drugbank_mol&.database_id,
                  chemdb_mol&.database_id,
                  hmdb_mol&.database_id,
                  name,
                  structure.inchi,
                  structure.inchikey,
                  structure.smiles,
                  structure.exact_mass,
                  nil,
                  c_ms.map(&:id),
                  ms_ms.map(&:id)]
        end

        structure.c_ms.experimental.where('derivative_type IS NOT NULL').each do |ds|
          # We can only take derivative spectra if the structure and mass is provided
          if ds.derivative_smiles.present? && ds.derivative_exact_mass.present?
            csv << [structure.id,
                    drugbank_mol&.database_id,
                    chemdb_mol&.database_id,
                    hmdb_mol&.database_id,
                    structure.name,
                    nil,
                    nil,
                    ds.derivative_smiles,
                    ds.derivative_exact_mass,
                    "(#{ds.derivative_type.split(";").map(&:strip).join(") (")})",
                    [ds.id],
                    []]
          end
        end
      end
    end
  end

  # Get a list of structure ids that are exported in certain databases
  def get_exported_structure_ids(exported_pathbank_compounds_file)
    sources_to_exclude = [10, 14, 22, 39] # 10 = test; 14 = SpecDB; 22 = PhytoMap (unpublished db); 39 = PathBank (no distinction b/t exported and unexported compounds)

    # Get source id list
    source_ids = Source.where(active: 1).pluck(:id) - sources_to_exclude

    # Get structure id list (combine PathBank IDs)
    pathbank_database_ids = extract_from_file_to_array(exported_pathbank_compounds_file)
    pathbank_structure_ids = DatabaseRegistration.where(:database_id => pathbank_database_ids).pluck(:structure_id)
    exported_structure_ids = DatabaseRegistration.where(:source_id => source_ids, :exported => 1).pluck(:structure_id)
    structure_ids = (pathbank_structure_ids + exported_structure_ids).uniq.sort

    return structure_ids
  end

  # Checks if a classification contains a handled class
  # Returns updated counts (i.e. classes_handled_hash) and rules-based structures
  def process_classification(structure_id, classification, classes_handled_hash, rules_based_structures)
    matched = false

    CLASSYFIRE_LEVELS.each do |level|
      if !classification[level].blank? # Check if classification level exists
        if classes_handled_hash.keys.include? classification[level]["name"]
          rules_based_structures.push(structure_id)
          classes_handled_hash[classification[level]["name"]] += 1
          matched = true
          break
        end
      end
    end

    # Look through intermediate nodes as well
    if matched == false
      classification["intermediate_nodes"].each do |intermediate_node|
        if classes_handled_hash.keys.include? intermediate_node["name"]
          rules_based_structures.push(structure_id)
          classes_handled_hash[intermediate_node["name"]] += 1
          matched = true
          break
        end
      end
    end

    # Look through alternative parents as well
    if matched == false
      classification["alternative_parents"].each do |alternative_parent|
        if classes_handled_hash.keys.include? alternative_parent["name"]
          rules_based_structures.push(structure_id)
          classes_handled_hash[alternative_parent["name"]] += 1
          break
        end
      end
    end

    return classes_handled_hash, rules_based_structures
  end

  # Retrieve additional data from structures
  def grab_structure_data(structure_ids)
    structures_with_data = []

    Structure.where(:id => structure_ids).find_each(:batch_size => 1000) do |structure|
      structures_with_data.append(["InChIKey="+structure.inchikey,structure.smiles])
    end

    return structures_with_data
  end

  # Prints task completion percentage
  def percent_complete(total, percentage_displayed, current_index)
    percent_done = (current_index.to_f / total.to_f) * 100.0

    if percent_done.round > percentage_displayed
      puts("#{percent_done.round}% completed")
      return percent_done.round
    else
      return percentage_displayed
    end
  end

  # Extract items from a text file (1 item per line) into an array
  def extract_from_file_to_array(filename)
    item_array = []
    File.open(filename, "r").each_line do |item|
      item_array.push(item.chomp!)
    end

    return item_array
  end

  # Export items in an array to a text file (1 item per line)
  def print_array_to_file(filename, item_array)
    output_file = File.open(filename, "w")
    item_array.each do |item|
      output_file.write("#{item}\n")
    end
    output_file.close
  end

  # Export array of arrays to a TSV file
  def print_nested_array_to_tsv(filename, nested_array)
    CSV.open(filename, "w", {:col_sep => "\t"}) do |csv|
      nested_array.each do |row|
        csv << row
      end
    end
  end

  # Export array of arrays to a TSV file
  def print_hash_to_screen(hash_to_print)
    hash_to_print.keys.each do |key|
      puts("#{key}: #{hash_to_print[key]}")
    end
  end
end
