namespace :jchem do
  task reimport: [:environment] do
    Structure.where("inchikey IS NULL OR inchikey = ''").limit(2).each do |s|
      begin
        s.structure_resource
      rescue ActiveRecord::RecordNotFound
        structure_id = ChemClient.create_structure(s.original_structure)
        s.update_column(:jchem_id, structure_id)
      end

      begin
        s.reload
        s.save
        puts "Saved #{s.id}"
      rescue Exception
        puts "Error for #{s.id}"
      end
    end
  end

  task rebuild: [:environment] do
    start = 9999
    Structure.all.each do |s|
      s.update_column(:jchem_id, start)
      start = start + 1
    end

    Structure.all.each do |s|
      structure_id = ChemClient.create_structure(s.original_structure)
      s.update_column(:jchem_id, structure_id)
      s.reload
      s.refresh!
      puts "Saved #{s.id}"
    end
  end

  desc "Setup the JChem Web Services database and configuration"
  task setup: [:environment] do
    ChemClient.initialize
  end

  desc "Upgrade after installing a new JChem version"
  task upgrade: [:environment] do
    ChemClient.upgrade
  end

  namespace :recalculate do
    desc "Refresh chemical formats. Formats to refresh are passed in via
          the formats param, for example formats=smiles,inchi"
    task formats: [:environment] do

      to_recalculate = []
      if ENV['formats'].to_s.blank?
        raise ArgumentError, "Must provide a format: #{STRUCTURE_FORMATS.keys.join(',')}"
      elsif ENV['formats'].to_s == 'all'
        to_recalculate = STRUCTURE_FORMATS.keys
      else
        ENV['formats'].to_s.split(/,/).each do |k|
          k.strip
          if STRUCTURE_FORMATS[k.to_sym].present?
            to_recalculate << k.to_sym
          else
            raise ArgumentError, "Format '#{k}' does not exist"
          end
        end
      end

      # Structure.transaction do
        Structure.find_each do |structure|
          to_recalculate.each do |format|
            prev_format = structure.formats.where(name: format).take
            begin
              value = ChemConvert.convert(structure.structure, STRUCTURE_FORMATS[format])
              if prev_format.present?
                next if value.present? && value == prev_format.value
                prev_format.delete
              end
              if value.present?
                structure.formats.create!(name: format, value: value)
                puts "Saved #{format} for #{structure.id}"
              else
                puts "Could not get #{format} for #{structure.id}"
              end
            rescue Savon::Error => error
              puts error.to_s
            end
          end
        end
      # end
    end

    desc "Recalculate the given chemical terms
         (for example: rake jchem:recalculate:terms terms=physiological_charge)"
    task terms: [:environment] do

      to_recalculate = []
      if ENV['terms'].to_s.blank?
        raise ArgumentError, "Must provide a term: #{MOLECULE_TERMS.keys.join(',')}"
      elsif ENV['terms'].to_s == 'all'
        to_recalculate = MOLECULE_TERMS.keys
      else
        ENV['terms'].to_s.split(/,/).each do |k|
          k.strip
          if MOLECULE_TERMS[k.to_sym].present?
            to_recalculate << k.to_sym
          else
            raise ArgumentError, "Term '#{k}' does not exist"
          end
        end
      end

      puts "Recalculating #{to_recalculate.join(', ')}..."
      Structure.find_each do |structure|
        if structure.smiles.blank?
          puts "Recalculation of terms for #{structure.database_id} FAILED - SMILES is blank"
          next
        end

        to_recalculate.each do |term|
          structure.properties.where(name: term).delete_all
          (1..5).to_a.each do
            begin
              val = ChemCalculator.term(structure.smiles, MOLECULE_TERMS[term])
              next if val.blank?
              structure.properties.create(name: term, value: val, source: 'jchem')
              break
            rescue Savon::Error => error
              puts "#{error.to_s} - retrying"
            rescue RestClient::InternalServerError => error
              puts "#{error.to_s} - could not calculate - skipping"
              break
            rescue RestClient::RequestTimeout => error
              puts "#{error.to_s} - retrying"
            end
            sleep 5
          end
        end
        puts "Recalculation of terms for #{structure.database_id} complete"
      end
    end

    desc "Re-create images"
    task images: [:environment] do
      Format.where("name = 'structure_thumb' OR name = 'structure_full'").includes(:molecule).find_each do |f|
        image = ChemConvert.convert(f.molecule.structure, STRUCTURE_FORMATS[f.name.to_sym])
        f.update_attribute(:value, image)
        puts "Updated #{f.name} for #{f.molecule.database_id}"
      end
    end
  end

  desc "Standardize the original structure for all structures in the database"
  task standardize: [:environment] do
    Structure.find_each do |structure|
      begin
        puts "Standardizing #{structure.database_id}"
        structure.standardize!("dearomatize..removeexplicitH")
      rescue Exception
        puts "Error standardizing!"
      end
    end
  end
end
