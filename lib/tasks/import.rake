# This prevents problems with Paperclip documents and  mime types
require 'paperclip/media_type_spoof_detector'
module Paperclip
  class MediaTypeSpoofDetector
    def spoofed?
      false
    end
  end
end

require 'csv'
include ActionView::Helpers::NumberHelper

namespace :import do
  task set_structure: [:environment] do
    # db_r = DatabaseRegistration.find_by(database_id: "HMDB0000001") #8997
    # db_r.structure_id = 9963 #HMDB0008276
    # db_r.save!
    
    # db_r = DatabaseRegistration.find_by(database_id: "HMDB0000002") #8997
    # db_r.structure_id = 199280
    # db_r.save!

    curation = Curation.find_by(structure_id: 346332)
    curation.json_to_class
    curation.cs_description = {"name"=>"Farnesyl acetate, also known as farnesylacetic acid, belongs to the class of organic compounds known as sesquiterpenoids. These are terpenes with three consecutive isoprene units. Farnesyl acetate has an oily and wax taste.","source"=>"ChemoSummarizer","kind"=>"Description"}
    curation.save!
    curation = Curation.find_by(structure_id: 10889)
    curation.json_to_class
    curation.cs_description = {"name"=>"N'-formylkynurenine belongs to the class of organic compounds known as alkyl-phenylketones. These are aromatic compounds containing a ketone substituted by one alkyl group, and a phenyl group. N'-formylkynurenine is slightly soluble (in water) and a moderately acidic compound (based on its pKa). Within the cell, n'-formylkynurenine is primarily located in the cytoplasm. N'-formylkynurenine exists in all eukaryotes, ranging from yeast to humans. N'-formylkynurenine participates in a number of enzymatic reactions. In particular, N'-formylkynurenine can be biosynthesized from L-tryptophan through its interaction with the enzyme tryptophan 2,3-dioxygenase. Furthermore, N'-formylkynurenine can be converted into formylanthranilic acid and L-alanine through the action of the enzyme kynureninase. Furthermore, N'-formylkynurenine can be converted into formic acid and L-kynurenine; which is mediated by the enzyme kynurenine formamidase. Furthermore, N'-formylkynurenine can be biosynthesized from L-tryptophan through its interaction with the enzyme indoleamine 2,3-dioxygenase. Furthermore, N'-formylkynurenine can be biosynthesized from L-tryptophan; which is mediated by the enzyme indoleamine 2,3-dioxygenase. Finally, N'-formylkynurenine can be converted into formic acid and L-kynurenine through its interaction with the enzyme kynurenine formamidase. In humans, n'-formylkynurenine is involved in the tryptophan metabolism pathway.","source"=>"ChemoSummarizer","kind"=>"Description"}
    curation.save!

    # db_r = DatabaseRegistration.new
    # db_r.database_id = "HMDB0000000"
    # db_r.structure_id = 9963
    # db_r.source_id = 1
    # db_r.save!
  end

  task set_fragment_formulas: [:environment] do
    [MsMsPeakAssignment,CMsPeakAssignment].each do |klass|
      klass.find_in_batches do |group|
        group.each do |assignment|
          next if assignment.formula.present?
          if assignment.structure
            begin
              assignment.formula = assignment.structure.formula
              assignment.save!
            rescue
              puts "Cannot calculate formula for: #{klass} #{assignment.id.to_s}"
              next
            end
          else
            begin
              assignment.formula = ChemCalculator.formula(assignment.smiles)
              assignment.save!
            rescue
              puts "Cannot calculate formula for: #{klass} #{assignment.id.to_s}"
              next
            end
          end
          puts klass
          puts assignment.id
          puts assignment.formula
        end
      end
    end
  end

  task delete_fragment_structures: [:environment] do
    [MsMsPeakAssignment,CMsPeakAssignment].each do |klass|
      klass.find_in_batches do |group|
        group.each do |assignment|
          if structure = assignment.structure
            assignment.structure_id = nil
            assignment.save!
            if !DatabaseRegistration.find_by(structure_id: structure.id)
              begin
                structure.destroy!
              rescue
                puts "Cannot delete structure #{assignment.smiles} #{structure.id}"
                next
              end
            end
          end
        end
      end
    end
  end

  task re_map_compounds: [:environment] do
    CSV.foreach("lmdb_hmdb_mapping_Ana.csv", :headers => true) do |row|
      lmdb_id = row["LMDB id"]
      hmdb_id = row["HMDB id"]
      next if !(lmdb_id && hmdb_id)
      db_hmdb = DatabaseRegistration.find_by(database_id: hmdb_id)
      db_lmdb = DatabaseRegistration.find_by(database_id: lmdb_id)
      if !db_lmdb
        puts lmdb_id
        next
      end
      if !db_hmdb
        puts hmdb_id
        next
      end

      if db_lmdb.structure_id != db_hmdb.structure_id
        puts "Updating structure for #{lmdb_id}"
        db_lmdb.structure_id = db_hmdb.structure_id
        db_lmdb.save!
      end
    end
  end

  task check_spectra: [:environment] do

    threshold = 0.01
    # spectra = MsMs.all.select{ |m| m.peaks.count>1000000 }
    # spectra.each do |ms|
    #   puts "Many Peaks:"
    #   puts ms.id
    #   puts ms.peaks.count
    # end

    CMs.all.each do |c_ms|
      next if c_ms.peaks.count < 1000
      # 2 deleted so far
      # if ms.peaks.count > 1000000
      #   puts ms.id
      #   # ms.destroy!
      # end
      # delete_count = 0
      peaks = c_ms.peaks.order("intensity DESC")
      high_intensity = peaks.first.intensity
      factor = high_intensity*threshold
      puts "Old count"
      puts peaks.count
      peaks.each do |p|
        if p.intensity < factor && peaks.count>1000
          delete_count += 1

          p.destroy!
          peaks.delete(p)
        end
      end

      puts c_ms.id
      puts "Deleted peaks"
      puts delete_count
      puts "New count"
      puts peaks.count

    end
  end

  namespace :msms do
    desc "MS MS fragments"
    task msms_frag: [:environment] do
      Dir.foreach(".") do |entry|
        next unless entry =~ /^(HMDB\d{5})/
        hmdb_id = $1
        msms = nil
        File.open(entry).each do |line|
          if line =~ /(low|med|high)/
            msms = MsMs.joins(:references).where("references.database = 'HMDB' AND references.database_id = '#{hmdb_id}' AND collision_energy_level = '#{$1}'").first
            puts msms.id if !msms.nil?
            puts hmdb_id if msms.nil?
          elsif !msms.nil? && line =~ /^(\d+\.?\d*)\s\d+\s(.*)/
            msms.peaks.where("mass_charge BETWEEN #{$1.to_f - 0.005} AND #{$1.to_f + 0.005} AND export = 1").each do |peak|
              next if peak.peak_assignments.count > 0
              $2.split(" ").each do |smiles|

                # svg = `molconvert svg -s \"#{smiles}\"{smiles}`
                #
                #
                # svg_file = Tempfile.new([msms.molecule.specdb_id + "_" + peak.mass_charge.to_s.gsub(/\./,'_'),'.svg'])
                # svg_file.write(svg)
                # svg_file.flush

                pa = MsMsPeakAssignment.new
                # pa.assignment_view = svg_file
                pa.smiles = smiles
                pa.peak = peak
                pa.save!
              end
            end
          else
            msms = nil
          end
        end
      end
    end
    desc "msms_metlin"
    task metlin: [:environment] do
      con_labm = Mysql2::Client.new(host: '***************',socket: '3306', username: 'readonly', password: 'tardis', database: 'labm', reconnect: true)
      Dir.foreach(".") do |entry|
        next unless entry =~ /^(HMDB\d{5})_Metlin_MSMS_(\d+)/
        hmdb_id = $1
        metlin_id = $2
        msms = nil
        File.open(entry).each do |line|
          if line =~ /(low|med|high)/

          end
        end
      end
    end
    desc "Import MS MS from hmdb"
    task msms_hmdb: [:environmnet] do
      con_hmdb = Mysql2::Client.new(host: '***************',socket: '3306', username: 'readonly', password: 'tardis', database: 'hmdb', reconnect: true)
      con_labm = Mysql2::Client.new(host: '***************',socket: '3306', username: 'readonly', password: 'tardis', database: 'labm', reconnect: true)
      count = 0
      con_labm.query("select id, inchi from tbl_chemical where export_hmdb = 'Yes' ").each(symbolize_keys: true) do |hmdb|
        # puts hmdb[:id]
        # count += 1
        # break if count > 2
        inchikey = `molconvert inchikey -s "#{hmdb[:inchi]}"{inchi}`
        con_hmdb.query("select experimental_mass_spectrum_file from metabolites where hmdb_id='#{hmdb[:id]}'").each(symbolize_keys: true) do |row|
          if(row[:experimental_mass_spectrum_file] =~ /.*?:::.*?:::(\d+)/)
            # puts $1
            parent_id = nil
            con_labm.query("SELECT parent_id FROM tbl_ms_file WHERE tbl_ms_file.id='#{$1}'").each(symbolize_keys: true) do |r|
              parent_id = r[:parent_id]
            end
            # puts parent_id
            con_labm.query("SELECT * FROM tbl_ms WHERE id='#{parent_id}'").each(symbolize_keys: true) do |r|
              # next if r[:delivery] == "LC"
              [["low",10],["med",25],["high",40]].each do |type|
                next if MsMs.joins(:references).where("references.database = 'HMDB' AND references.database_id = '#{hmdb[:id]}' AND inchi_key = '#{inchikey}' AND collision_energy_level = '#{type[0]}'").count > 0

                spec = MsMs.new(collision_energy_level: type[0], collision_energy_voltage: type[1], inchi_key: inchikey)
                spec.references << Reference.new(pubmed_id: 18953024, database: "HMDB", database_id: hmdb[:id])


                spectra_file = r[:spectra] if type[0] == "low"
                spectra_file = r[:spectraM] if type[0] == "med"
                spectra_file = r[:spectraH] if type[0] == "high"

                if(spectra_file =~ /.*?:::.*?:::(\d+)/)
                  doc = Document.new
                  doc.file = get_file_hmdb(con_labm,"tbl_ms_file", $1)
                  doc.description = "Spectra image with peak assignments"
                  spec.documents << doc
                end

                # if(r[:spectra] =~ /.*?:::.*?:::(\d+)/)
                #   d = Document.new
                #   d.description = "Raw Spectrum Image"
                #   d.file = get_file_hmdb(con_labm,"#{table}_file", $1)
                #   spec.documents << d
                # end
                # if(r[:spectra_fids] =~ /.*?:::.*?:::(\d+)/)
                #   d = Document.new
                #   d.description = "Raw Free Induction Decay file for spectral processing"
                #   d.file = get_file_hmdb(con_labm,"#{table}_file", $1)
                #   spec.documents << d
                # end
                spectra_file = r[:spectra_peaklist] if type[0] == "low"
                spectra_file = r[:spectraM_peaklist] if type[0] == "med"
                spectra_file = r[:spectraH_peaklist] if type[0] == "high"

                if(spectra_file =~ /.*?:::.*?:::(\d+)/)
                  # puts $1
                  d = Document.new
                  d.description = "List of m/z values for the spectrum"
                  f = get_file_hmdb(con_labm,"tbl_ms_file", $1, "ASCII-8BIT")
                  d.file = f
                  spec.documents << d

                  fail = false
                  max = 0
                  File.open(f).each do |line|
                    # puts "'#{line}'"
                    if line =~ /^\s*\d+\s+(\-?\d+\.?\d*)\s+(\-?\d+\.?\d*)\s+\-?\d+\.?\d*/
                      spec.peaks << MsMsPeak.new(mass_charge: $1, intensity: $2)
                      max = $2.to_f if $2.to_f > max
                    elsif line =~ /^\d+\s+(\-?\d+\.?\d*)\s+(\-?\d+\.?\d*)/
                      spec.peaks << MsMsPeak.new(mass_charge: $1, intensity: $2)
                      max = $2.to_f if $2.to_f > max
                    elsif line =~ /^(\-?\d+\.?\d*)\s+(\-?\d+\.?\d*)/
                      spec.peaks << MsMsPeak.new(mass_charge: $1, intensity: $2)
                      max = $2.to_f if $2.to_f > max
                    end
                  end
                  if spec.peaks.size <= 0 || max == 0

                    # if hmdb[:id] == "HMDB00226"
                    #   spec.peaks << NmrOneDPeak.new(chemical_shift: 6.18)
                    # else
                    puts "WARNING #{hmdb[:id]} peak list currupt"
                    puts File.open(f).read
                    # end

                  else
                    spec.peaks.each do |peak|
                      peak.intensity = 100 * peak.intensity / max
                    end
                  end
                else
                  puts "WARNING #{hmdb[:id]} has no peak list"
                  next
                end
                if(!r[:sample_preparation].nil? && r[:sample_preparation] =~ /^\d+$/)
                  sop = get_sop(con_labm, r[:sample_preparation])
                  spec.sops << sop if sop.present?
                end
                if(!r[:data_collection].nil? && r[:data_collection] =~ /^\d+$/)
                  sop = get_sop(con_labm, r[:data_collection])
                  spec.sops << sop if sop.present?
                end

                if !r[:sample_concentration].nil? && r[:sample_concentration] =~ /^(\d+\.?\d*)/
                  spec.sample_concentration = $1
                  spec.sample_concentration_units = "mM"
                end
                if !r[:mass_value].nil? && !r[:mass_unit].nil? && r[:mass_value] =~ /^(\d+\.?\d*)/
                  spec.sample_mass = $1
                  spec.sample_mass_units =  r[:mass_unit]
                end

                spec.sample_assessment = r[:sample_assessment]
                spec.spectra_assessment = r["ms_assessment".to_sym]



                # spec.sample_ph = r[:sample_ph]
                # if !r[:sample_temp].nil? && r[:sample_temp] =~ /^(\d+\.?\d*)/
                #   spec.sample_temperature = $1
                #   spec.sample_temperature_units = "Celcius"
                # end

                # spec.chemical_shift_reference = r[:sample_shift]
                # spec.solvent = r[:solvent]
                spec.instrument_type = r[:manufacturer]
                # spec.frequency = r[:freq] + " MHz" if !r[:freq].nil?
                spec.ionization_mode = r[:ionization]
                # if table =~ /tbl_(.*)/
                spec.collection_date = r["ms_collection_date".to_sym]
                # end
                spec.notes = "delivery=#{r[:delivery]}\nanalyzer=#{r[:analyzer]}"

                molecule = Molecule.find_by_moldb_inchikey(inchikey)
                if molecule.nil?
                  molecule = Molecule.new
                  molecule.structure = hmdb[:inchi]
                  molecule.save!
                end
                spec.molecule = molecule

                # Generate SPLASH key on saving a new spectrum
                spec.regenerate_splash_key
                spec.save!

              end
            end
          end
        end
      end
    end
    desc "Build new MS MS spectra"
    task :new, [:filename] => [:environment] do |t, args|
      inchi = ""
      type = ""
      m = nil
      CSV.foreach(args.filename, headers: true, header_converters: :symbol) do |row|
        if inchi != row[:moldb_inchikey] || type != row[:spectra_type]
          puts "IMPORTING #{row[:moldb_inchikey]} #{row[:spectra_type]}"
          m = MsMs.new
          m.inchi_key = row[:moldb_inchikey]
          m.mono_mass = row[:mono_mass]
          m.collision_energy_level = row[:energy]
          m.notes = row[:spectra_type]
          m.sample_concentration = row[:sample_conc]
          m.sample_mass = row[:sample_mass]
          m.sample_assessment = row[:sample_assessment]
          m.spectra_assessment = row[:spectra_assessment]
          m.instrument_type = row[:instrument_type]
          m.ionization_mode = row[:ionization]
          m.save!
        end
        mp = MsMsPeak.new
        mp.mass_charge = row[:mz]
        mp.intensity = row[:ri]
        m.ms_ms_peaks << mp

        inchi = row[:moldb_inchikey]
        type = row[:spectra_type]

        # Generate SPLASH key on saving a new spectrum
        m.regenerate_splash_key
        m.save!
      end
    end
    desc "build new MS MS from metlin"
    task :new_metlin, [:filename] => [:environment] do |t, args|
      msms = nil
      hmdb_id = nil
      metlin_id = nil
      inchi = nil
      if args.filename =~ /(HMD\d{5})_Metlin_MSMS_(\d+)/
        hmdb_id = $1
        metlin_id = $2
      end
      con_labm = Mysql2::Client.new(host: '***************',socket: '3306', username: 'readonly', password: 'tardis', database: 'labm', reconnect: true,cache_rows: false)
      con_labm.query("SELECT InChi FROM tbl_chemical WHERE id = '#{hmdb_id}'").each(symbolize_keys: true) do |r|
        inchi = r[:inchi]
      end

      File.open(args.filename).each do |line|
        if line =~ /(\d+)\s(\w+)/
          new_msms = false
          if !msms.nil?
            new_msms = true


            molecule = Molecule.find_by_inchi(inchi)
            if molecule.nil?
              molecule = Molecule.new
              molecule.structure = inchi
            end
            msms.molecule = molecule



            msms.save!
            msms.structure = inchi
            msms.structure.save!
          end
          msms = MsMs.new(collision_energy_voltage: $1, ionization_mode: $2)
          msms.molecule = Molecule.new
        elsif line =~ /(\d+\.?\d+?)\s(\d+\.?\d*?)\s(\w+)/
          msms.ms_ms_peaks << MsMsPeak.new(mass_charge: $1, intensity: $2)
        end
        if new_msms

          # Generate SPLASH key on saving a new spectrum
          msms.regenerate_splash_key
          msms.save!
        end
      end
    end

    # This task imports spectra predicted by cfm-id!
    # Currently assumes new spectra files are in public/import/<positive or negative>
    # MAKE SURE to remove existing files in the import directories so
    # you don't import duplicates
    # Modify the directories depending on where your spectra files are
    # For phytomap, the identifier="PMC", for contaminandtDB, identifier="CHEM"
    desc "Import Predicted MS-MS from CFMID"
    task :cfmid_predicted, [:dir, :identifier] =>  [:environment] do |t, args|
      unless args.dir.present? && Dir.exist?(args.dir)
        puts "Directory does not exist: #{args.dir}"
        exit
      end
      log = File.open("spectra_upload.log","w")
      tmp_dir = File.join(args.dir, 'temp')
      FileUtils.mkdir_p(tmp_dir)

      ["Positive", "Negative"].each do |type|
        spectra_path = File.join(args.dir, type.downcase)
        Dir.foreach(spectra_path) do |entry|
          next unless entry =~ /(#{args.identifier}\d+)\.log/
          db_id = $1
          begin
          structure_id = DatabaseRegistration.find_by_database_id(db_id).structure_id
          rescue
            log.puts("NOT SAVED #{db_id}")
            next
          end
          next if MsMs.exists?(predicted: true, structure_id: structure_id, ionization_mode: type)
          # if existing_spectra = MsMs.where(predicted: true, structure_id: structure_id, ionization_mode: type)
          # if existing_spectra = MsMs.where(predicted: true, structure_id: structure_id)
          #   existing_spectra.each do |s|
          #     s.destroy!
          #   end
          # end
          puts db_id

          step = 1 # Indicate whether we are reading the spectra or the assignments
          msms = nil # The current MS-MS spectra
          assignments = {} # Hash to hold IDs mapping peaks to assignment molecules
          File.open(spectra_path + "/" + entry).each do |line|
            if line =~ /^energy(\d)/ # Energy level header
              if $1 == "0"
                msms = MsMs.new(collision_energy_voltage: "10",
                                collision_energy_level: "low",
                                structure_id: structure_id)
              elsif $1 == "1"
                msms.save!
                # Make peak list file
                file_path = "#{tmp_dir}/#{db_id}-low.txt"
                File.open(file_path, 'w') do |f|
                  msms.peaks.each do |peak|
                    f.puts peak.mass_charge.to_s + " " + peak.intensity.to_s
                  end
                end
                doc = Document.new()
                doc.spectra_id = msms.id
                doc.spectra_type = "MsMs"
                doc.description = "List of m/z values for the spectrum"
                doc.file = File.open(file_path)
                doc.save!
                File.delete(file_path)
                msms = MsMs.new(collision_energy_voltage: "20",
                                collision_energy_level: "med",
                                structure_id: structure_id)
              elsif $1 == "2"
                msms.save!
                # Make peak list file
                file_path = "#{tmp_dir}/#{db_id}-med.txt"
                File.open(file_path, 'w') do |f|
                  msms.peaks.each do |peak|
                    f.puts peak.mass_charge.to_s + " " + peak.intensity.to_s
                  end
                end
                doc = Document.new()
                doc.spectra_id = msms.id
                doc.spectra_type = "MsMs"
                doc.description = "List of m/z values for the spectrum"
                doc.file = File.open(file_path)
                doc.save!
                File.delete(file_path)
                msms = MsMs.new(collision_energy_voltage: "40",
                                collision_energy_level: "high",
                                structure_id: structure_id)
              end
              msms.ionization_mode = type
              msms.predicted = 1
              msms.notes = "Predicted by CFM-ID"
              next
            elsif line =~ /^\s*$/ # Blank line, this means the peak lists are done and we are starting the assignments
              msms.save!
              # puts msms.id
              # Make peak list file
              file_path = "#{tmp_dir}/#{db_id}-high.txt"
              File.open(file_path, 'w') do |f|
                msms.peaks.each do |peak|
                  f.puts peak.mass_charge.to_s + " " + peak.intensity.to_s
                end
              end
              doc = Document.new()
              doc.spectra_id = msms.id
              doc.spectra_type = "MsMs"
              doc.description = "List of m/z values for the spectrum"
              doc.file = File.open(file_path)
              doc.save!
              File.delete(file_path)
              step = 2
              next
            elsif step == 1 # Create peak
              line_data = line.split(/\s/).map(&:strip)
              top_assignment = line_data[2..-1].find { |x| !x.blank? }
               if !top_assignment
                top_assignment = ""
              end
              # puts top_assignment

              peak = MsMsPeak.new(mass_charge: line_data[0], intensity: line_data[1])
              peak.save!
              msms.peaks << peak
              if assignments[top_assignment].blank?
                assignments[top_assignment] = []
              end
              assignments[top_assignment] << peak.id # Store peak assignment mapping
            elsif step == 2 # Create assignment
              line_data = line.split(/\s/).map(&:strip)
              assignment_id = line_data[0]
              smiles = line_data[2]
              if assignments[assignment_id].present?
                assignments[assignment_id].each do |peak_id|
                  assignment = MsMsPeakAssignment.new()
                  assignment.ms_ms_peak_id = peak_id
                  assignment.smiles = smiles
                  begin
                    # file_path = "public/import/temp/" + db_id.to_s + "-" + peak_id.to_s + "-" + line_data[0].to_s + ".png"

                    # Old way of saving a paperclip attachment to assignments.
                    # Now just save the moldb url 

                    # file_path = "#{tmp_dir}/#{db_id}-#{peak_id}-#{line_data[0]}.png"
                    # Jchem.structure_to_png_file(smiles, file_path)
                    # structure_file = File.open(file_path)
                    # assignment.assignment_view = structure_file
                    # structure_file.close
                    # File.delete(file_path)

                    assignment.save!
                  rescue => e
                    # If the structure fails, oh well
                    puts "Error adding assignment view"
                    puts e.message
                    puts e.backtrace.join("\n")
                  end
                end
              end
            end

            # Generate SPLASH key on saving a new spectrum
            # begin
            #   if ms_ms
            #     ms_ms.regenerate_splash_key
            #     ms_ms.save!
            #   end
            # rescue
            #   puts "Splash key NOT generated"
            # end
          end
        end
      end
      log.close
    end
  end

  namespace :gcms do
    desc "GC MS Spectra from hmdb"
    task hmdb: [:environment] do
      con_labm = Mysql2::Client.new(host: '***************',socket: '3306', username: 'readonly', password: 'tardis', database: 'labm', reconnect: true,cache_rows: false)
      con_labm.query("select tbl_chemical.id as hmdb_id, inchi, gcms_deposit_date, derivatized_name, parent_mass, retention_index, peaklist, intensity from tbl_chemical, tbl_gcMS where export_hmdb = 'Yes' AND tbl_chemical.id=tbl_gcMs.hmdb_id").each(symbolize_keys: true) do |row|
        inchikey = `molconvert inchikey -s "#{row[:inchi]}"{inchi}`
        inchikey.strip!
        inchikey.sub!(/InChIKey=/,"")
        next if CMs.joins(:references).where("references.database = 'HMDB' AND references.database_id = '#{row[:hmdb_id]}' AND inchi_key = '#{inchikey}' AND chromatography_type = 'Gas'").count > 0
        c = CMs.new
        c.references << Reference.new(pubmed_id: 18953024, database: "HMDB", database_id: row[:hmdb_id])
        c.chromatography_type = "Gas"
        c.collection_date = row[:gcms_deposit_date]
        c.retention_index = row[:retention_index]
        c.notes = "derivatized_name=#{row[:derivatized_name]}\n"
        c.notes += "parent_mass=#{row[:parent_mass]}"
        c.inchi_key = inchikey
        i = Array.new
        mz = row[:peaklist].slice(1..-2).split(',')
        i = row[:intensity].slice(1..-2).split(',') if row[:intensity].present?

        mz.each_index do |k|
          mp = CMsPeak.new
          mp.mass_charge = mz[k]
          mp.intensity = i[k] if mz.size == i.size
          c.peaks << mp
        end
        c.molecule = Molecule.new

        # Generate SPLASH key on saving a new spectrum
        c.regenerate_splash_key
        c.save!
        c.molecule.structure = row[:inchi]
        c.molecule.save!
      end
    end

    # This task imports spectra predicted by GC-MS via CFM-ID/EI!
    # Currently assumes new spectra files are in public/import/ei
    # MAKE SURE to remove existing files in the import directories so
    # you don't import duplicates
    desc "Import Predicted GC-MS from CFMID-EI"
    task :gcms_predicted, [:dir, :identifier] =>  [:environment] do |t, args|
      unless args.dir.present? && Dir.exist?(args.dir)
        puts "Directory does not exist: #{args.dir}"
        exit
      end
      tmp_dir = File.join(args.dir, 'temp')
      FileUtils.mkdir_p(tmp_dir)

      ["Ei"].each do |type|
        spectra_path = File.join(args.dir, type.downcase)
        Dir.foreach(spectra_path) do |entry|
          next unless entry =~ /(#{args.identifier}\d+)\.log/
          db_id = $1
          structure_id = DatabaseRegistration.find_by_database_id(db_id).structure_id
          structure = Structure.find_by(id: structure_id)

          # if CMs.exists?(predicted: true, structure_id: structure_id, ionization_mode: type)
          if !CMs.where(predicted: true, structure_id: structure_id, ionization_mode: type).where("derivative_type is null").empty?
            puts db_id + " Already Exists"
            next
          end
          next if !structure.exact_mass.present?
          next if structure.exact_mass>500

          puts db_id

          step = 1 # Indicate whether we are reading the spectra or the assignments
          gcms = nil # The current GC-MS spectra
          assignments = {} # Hash to hold IDs mapping peaks to assignment molecules
          File.open(spectra_path + "/" + entry).each do |line|
            if line =~ /^energy(\d)/ # Energy level header
              if $1 == "0"
                gcms = CMs.new(structure_id: structure_id, chromatography_type: "GC", instrument_type: "GC-MS")
                
                # gcms = MsMs.new(collision_energy_voltage: "10",
                #                 collision_energy_level: "low",
                #                 structure_id: structure_id)

              # elsif $1 == "1"
              #   gcms.save!
              #   # Make peak list file
              #   file_path = "#{tmp_dir}/#{db_id}-low.txt"
              #   File.open(file_path, 'w') do |f|
              #     gcms.peaks.each do |peak|
              #       f.puts peak.mass_charge.to_s + " " + peak.intensity.to_s
              #     end
              #   end
              #   doc = Document.new()
              #   doc.spectra_id = msms.id
              #   doc.spectra_type = "MsMs"
              #   doc.description = "List of m/z values for the spectrum"
              #   doc.file = File.open(file_path)
              #   doc.save!
              #   File.delete(file_path)
              #   gcms = MsMs.new(collision_energy_voltage: "20",
              #                   collision_energy_level: "med",
              #                   structure_id: structure_id)
              # elsif $1 == "2"
              #   gcms.save!
              #   # Make peak list file
              #   file_path = "#{tmp_dir}/#{db_id}-med.txt"
              #   File.open(file_path, 'w') do |f|
              #     gcms.peaks.each do |peak|
              #       f.puts peak.mass_charge.to_s + " " + peak.intensity.to_s
              #     end
              #   end
              #   doc = Document.new()
              #   doc.spectra_id = msms.id
              #   doc.spectra_type = "MsMs"
              #   doc.description = "List of m/z values for the spectrum"
              #   doc.file = File.open(file_path)
              #   doc.save!
              #   File.delete(file_path)
              #   gcms = MsMs.new(collision_energy_voltage: "40",
              #                   collision_energy_level: "high",
              #                   structure_id: structure_id)
              end
              gcms.ionization_mode = type
              gcms.predicted = 1
              gcms.notes = "Predicted by CFMID-EI, energy0"
              next
            elsif line =~ /^\s*$/ # Blank line, this means the peak lists are done and we are starting the assignments
              gcms.save!
              # Make peak list file
              file_path = "#{tmp_dir}/#{db_id}-high.txt"
              File.open(file_path, 'w') do |f|
                gcms.peaks.each do |peak|
                  f.puts peak.mass_charge.to_s + " " + peak.intensity.to_s
                end
              end
              doc = Document.new()
              doc.spectra_id = gcms.id
              doc.spectra_type = "cms"
              doc.description = "List of m/z values for the spectrum"
              doc.file = File.open(file_path)
              doc.save!
              File.delete(file_path)
              step = 2
              next
            elsif step == 1 # Create peak
              line_data = line.split(/\s/).map(&:strip)
              top_assignment = line_data[2]
              peak = CMsPeak.new(mass_charge: line_data[0], intensity: line_data[1])
              peak.save!
              gcms.peaks << peak
              if assignments[top_assignment].blank?
                assignments[top_assignment] = []
              end
              assignments[top_assignment] << peak.id # Store peak assignment mapping
            elsif step == 2 # Create assignment
              line_data = line.split(/\s/).map(&:strip)
              assignment_id = line_data[0]
              smiles = line_data[2]
              if assignments[assignment_id].present?
                assignments[assignment_id].each do |peak_id|
                  assignment = CMsPeakAssignment.new()
                  assignment.c_ms_peak_id = peak_id
                  assignment.smiles = smiles
                  begin
                    # file_path = "public/import/temp/" + db_id.to_s + "-" + peak_id.to_s + "-" + line_data[0].to_s + ".png"

                    # Old way of saving a paperclip attachment to assignments.
                    # Now just save the moldb url 

                    # file_path = "#{tmp_dir}/#{db_id}-#{peak_id}-#{line_data[0]}.png"
                    # Jchem.structure_to_png_file(smiles, file_path)
                    # structure_file = File.open(file_path)
                    # assignment.assignment_view = structure_file
                    # structure_file.close
                    # File.delete(file_path)
                    assignment.save!
                  rescue => e
                    # If the structure fails, oh well
                    puts "Error adding assignment view"
                    puts e.message
                    puts e.backtrace.join("\n")
                  end
                end
              end
            end

            # Generate SPLASH key on saving a new spectrum
            # gcms.regenerate_splash_key
            gcms.save!
          end
        end
      end
    end

    desc "Import Predicted GC-MS fragments from CFMID-Annotate"
    task :gcms_fragments, [:dir, :identifier] =>  [:environment] do |t, args|
      unless args.dir.present? && Dir.exist?(args.dir)
        puts "Directory does not exist: #{args.dir}"
        exit
      end
      tmp_dir = File.join(args.dir, 'temp')
      FileUtils.mkdir_p(tmp_dir)
      log_file = File.open("gcms_assignments_upload.log","w")

      ["Ei"].each do |type|
        spectra_path = File.join(args.dir, type.downcase)
        puts spectra_path
        Dir.foreach(spectra_path) do |entry|
          # next unless entry =~ /(#{args.identifier}\d+)\.log/
          next unless entry =~ /.log/
          # db_id = $1

          spectrum_id = entry.split("_")[0]
          spectrum = CMs.find_by(id: spectrum_id)
          log_file.puts "Spectrum:"
          log_file.puts entry
          log_file.puts spectrum.id.to_s
          puts "Spectrum:"
          puts entry
          puts spectrum.id
          
          step = 1 # Indicate whether we are reading the spectra or the assignments
          # gcms = nil # The current GC-MS spectra
          assignments = {} # Hash to hold IDs mapping peaks to assignment molecules
          File.open(spectra_path + "/" + entry).each do |line|
            if line =~ /^energy(\d)/ # Energy level header
              # if $1 == "0"
              #   gcms = CMs.new(structure_id: structure_id, chromatography_type: "GC", instrument_type: "GC-MS")
              # end
              # gcms.ionization_mode = type
              # gcms.predicted = 1
              # gcms.notes = "Predicted by CFMID-EI, energy0"
              next
            elsif line =~ /^\s*$/ # Blank line, this means the peak lists are done and we are starting the assignments
              # gcms.save!
              # Make peak list file
              # file_path = "#{tmp_dir}/#{db_id}-high.txt"
              # File.open(file_path, 'w') do |f|
              #   gcms.peaks.each do |peak|
              #     f.puts peak.mass_charge.to_s + " " + peak.intensity.to_s
              #   end
              # end
              # doc = Document.new()
              # doc.spectra_id = gcms.id
              # doc.spectra_type = "cms"
              # doc.description = "List of m/z values for the spectrum"
              # doc.file = File.open(file_path)
              # doc.save!
              # File.delete(file_path)
              step = 2
              next
            elsif step == 1 # Create peak
              line_data = line.split(/\s/).map(&:strip)
              top_assignment = line_data[2]
              # peak = CMsPeak.new(mass_charge: line_data[0], intensity: line_data[1])
              # peak.save!
              # gcms.peaks << peak

              # Find by mass_charge only (does not repeat for spectrum id)
              peak = CMsPeak.find_by(c_ms_id: spectrum.id, mass_charge: line_data[0])#, intensity: line_data[1])
              log_file.puts line_data[0].to_s
              log_file.puts line_data[1].to_s
              puts line_data[0]
              puts line_data[1]

              if assignments[top_assignment].blank?
                assignments[top_assignment] = []
              end
              assignments[top_assignment] << peak.id # Store peak assignment mapping
            elsif step == 2 # Create assignment
              line_data = line.split(/\s/).map(&:strip)
              assignment_id = line_data[0]
              smiles = line_data[2]
              puts "Assignments:"
              if assignments[assignment_id].present?
                assignments[assignment_id].each do |peak_id|
                  if !CMsPeakAssignment.find_by(c_ms_peak_id: peak_id)
                    log_file.puts peak_id.to_s
                    log_file.puts smiles
                    puts peak_id
                    puts smiles
                    assignment = CMsPeakAssignment.new
                    assignment.c_ms_peak_id = peak_id
                    assignment.smiles = smiles
                    begin
                      assignment.save!
                    rescue => e
                      # If the structure fails, oh well
                      log_file.puts "Error adding assignment view"
                      log_file.puts e.message
                      log_file.puts e.backtrace.join("\n")
                      puts "Error adding assignment view"
                      puts e.message
                      puts e.backtrace.join("\n")
                    end
                  else
                    log_file.puts "WARNING: #{peak_id.to_s} already has assignment"
                    puts "WARNING: #{peak_id.to_s} already has assignment"
                  end

                end
              end
            end

          end
        end

        # break
      end
    end

    desc "Import Predicted GC-MS from CFMID-EI for TMS"
    task :gcms_tms_predicted, [:dir, :identifier] =>  [:environment] do |t, args|
      unless args.dir.present? && Dir.exist?(args.dir)
        puts "Directory does not exist: #{args.dir}"
        exit
      end
      tmp_dir = File.join(args.dir, 'temp')
      FileUtils.mkdir_p(tmp_dir)

      # ["Ei"].each do |type|
      File.open("Sep21_only_max.tsv").each_line do |line|
        line.strip!
        tms_file = line.split("\t")[0..-2].join("_")+".log"
        tms_struct = line.split("\t")[-1]

        # puts tms_file
        # puts tms_struct

        # spectra_path = File.join(args.dir, type.downcase)
        # Dir.foreach(spectra_path) do |entry|
        entry = File.join(args.dir, tms_file)
          # next unless entry =~ /(#{args.identifier}\d+)\.log/
          # db_id = $1
          db_id = line.split("\t")[0]
          structure_id = DatabaseRegistration.find_by_database_id(db_id).structure_id

          if CMs.exists?(predicted: true, structure_id: structure_id, tms: true, ionization_mode: "Ei")
            puts db_id + " Already Exists"
            next
          end
          puts db_id

          step = 1 # Indicate whether we are reading the spectra or the assignments
          gcms = nil # The current GC-MS spectra
          assignments = {} # Hash to hold IDs mapping peaks to assignment molecules
          # File.open(spectra_path + "/" + entry).each do |line|
          if !File.exist?(entry)
            puts "File does not exist " + tms_file
            next
          end

          File.open(entry).each do |line|
            if line =~ /^energy(\d)/ # Energy level header
              if $1 == "0"
                gcms = CMs.new(structure_id: structure_id, chromatography_type: "GC", instrument_type: "GC-MS", tms: 1)
                
                # gcms = MsMs.new(collision_energy_voltage: "10",
                #                 collision_energy_level: "low",
                #                 structure_id: structure_id)

              # elsif $1 == "1"
              #   gcms.save!
              #   # Make peak list file
              #   file_path = "#{tmp_dir}/#{db_id}-low.txt"
              #   File.open(file_path, 'w') do |f|
              #     gcms.peaks.each do |peak|
              #       f.puts peak.mass_charge.to_s + " " + peak.intensity.to_s
              #     end
              #   end
              #   doc = Document.new()
              #   doc.spectra_id = msms.id
              #   doc.spectra_type = "MsMs"
              #   doc.description = "List of m/z values for the spectrum"
              #   doc.file = File.open(file_path)
              #   doc.save!
              #   File.delete(file_path)
              #   gcms = MsMs.new(collision_energy_voltage: "20",
              #                   collision_energy_level: "med",
              #                   structure_id: structure_id)
              # elsif $1 == "2"
              #   gcms.save!
              #   # Make peak list file
              #   file_path = "#{tmp_dir}/#{db_id}-med.txt"
              #   File.open(file_path, 'w') do |f|
              #     gcms.peaks.each do |peak|
              #       f.puts peak.mass_charge.to_s + " " + peak.intensity.to_s
              #     end
              #   end
              #   doc = Document.new()
              #   doc.spectra_id = msms.id
              #   doc.spectra_type = "MsMs"
              #   doc.description = "List of m/z values for the spectrum"
              #   doc.file = File.open(file_path)
              #   doc.save!
              #   File.delete(file_path)
              #   gcms = MsMs.new(collision_energy_voltage: "40",
              #                   collision_energy_level: "high",
              #                   structure_id: structure_id)
              end
              # gcms.ionization_mode = type
              gcms.ionization_mode = "Ei"
              gcms.predicted = 1
              gcms.notes = "Predicted by CFMID-EI, energy0, fully TMS-derivatized (structure: #{tms_struct})"
              next
            elsif line =~ /^\s*$/ # Blank line, this means the peak lists are done and we are starting the assignments
              gcms.save!
              # Make peak list file
              file_path = "#{tmp_dir}/#{db_id}-high.txt"
              File.open(file_path, 'w') do |f|
                gcms.peaks.each do |peak|
                  f.puts peak.mass_charge.to_s + " " + peak.intensity.to_s
                end
              end
              doc = Document.new()
              doc.spectra_id = gcms.id
              doc.spectra_type = "CMs"
              doc.description = "List of m/z values for the spectrum"
              doc.file = File.open(file_path)
              doc.save!
              File.delete(file_path)
              step = 2
              next
            elsif step == 1 # Create peak
              line_data = line.split(/\s/).map(&:strip)
              top_assignment = line_data[2..-1].find { |x| !x.blank? }
              if !top_assignment
                top_assignment = ""
              end

              peak = CMsPeak.new(mass_charge: line_data[0], intensity: line_data[1])
              peak.save!
              gcms.peaks << peak
              if assignments[top_assignment].blank?
                assignments[top_assignment] = []
              end
              assignments[top_assignment] << peak.id # Store peak assignment mapping
            elsif step == 2 # Create assignment
              line_data = line.split(/\s/).map(&:strip)
              assignment_id = line_data[0]
              smiles = line_data[2]
              if assignments[assignment_id].present?
                assignments[assignment_id].each do |peak_id|
                  assignment = CMsPeakAssignment.new()
                  assignment.c_ms_peak_id = peak_id
                  assignment.smiles = smiles
                  begin
                    # file_path = "public/import/temp/" + db_id.to_s + "-" + peak_id.to_s + "-" + line_data[0].to_s + ".png"

                    # Old way of saving a paperclip attachment to assignments.
                    # Now just save the moldb url 

                    # file_path = "#{tmp_dir}/#{db_id}-#{peak_id}-#{line_data[0]}.png"
                    # Jchem.structure_to_png_file(smiles, file_path)
                    # structure_file = File.open(file_path)
                    # assignment.assignment_view = structure_file
                    # structure_file.close
                    # File.delete(file_path)

                    assignment.save!
                  rescue => e
                    # If the structure fails, oh well
                    puts "Error adding assignment view"
                    puts e.message
                    puts e.backtrace.join("\n")
                  end
                end
              end
            end

            # Generate SPLASH key on saving a new spectrum
            # gcms.regenerate_splash_key
            gcms.tms_smiles = tms_struct
            gcms.tms = 1
            gcms.save!
          end
        # end
      end
    end
    desc "Import Info for Predicted GC-MS from CFMID-EI for TMS"
    task :gcms_tms_update => [:environment] do
      # tms_info = File.read("Sep21_only_max_with_formulae_and_exact_masses.tsv").split("\n")
      # tms_info.each do |tms_struct|
      #   split_info = tms_struct.split("\t")
      #   groups = split_info[1]
      #   molecular_weight = split_info[2]
      #   exact_mass = split_info[3]
      #   smiles = split_info[4]
      #   chemical_formula = split_info[5]

      #   if c_ms = CMs.find_by(tms_smiles: smiles)
      #     c_ms.tms_molecular_weight = molecular_weight
      #     c_ms.tms_exact_mass = exact_mass
      #     c_ms.tms_groups = groups
      #     c_ms.tms_chemical_formula = chemical_formula

      #     c_ms.save!
      #     puts c_ms.tms_smiles
      #   end
      # end
      cmss = CMs.where(tms: 1)
      cmss.each do |cms|
        cms.derivative_type = cms.tms_groups.to_s + " TMS"
        cms.derivative_formula = cms.tms_chemical_formula
        cms.derivative_mw = cms.tms_molecular_weight
        cms.derivative_exact_mass = cms.tms_exact_mass
        cms.save!

      end
    end

  end
  namespace :nmr1d do

    # dir: directory containing formatted SDBS data
    # http://sdbs.db.aist.go.jp/sdbs/cgi-bin/cre_index.cgi
    # First scrape the data with the "sdbs_scrape_1hnmr.rb"
    # Format the scraped data with "sdbs_format_1hnmr.rb"
    # This task will skip an import if an nmr1d from SDBS already exists
    # Edit the reference text as appropriate.
    # Any temperatures are assumed to be Celcius.
    # CSV file Format:
    #    CAS,SDBS,frequency,concentration_string,solvent,nucleus
    #    50-00-0,9410,300 MHz,5 % in TMS,TMS,1H
    # After importing run the folowing task to build the uniques indices
    # bundle exec rake nmr_one_d:proton:index RAILS_ENV=production
    # bundle exec rake nmr_one_d:carbon:index RAILS_ENV=production

    desc "import liping data"
    task :liping => [:environment] do
        m = NmrOneD.find("5226")
        m.sample_mass = "0.227"
        m.save!
        m = NmrTwoD.find("2049")
        m.documents.each do |doc|
          doc.destroy!
        end
        m.sample_mass = "0.277"
        m.save!
        doc = Document.new
        doc.file = File.open("data/nmr_two_d.png", 'rb')
        doc.description = "spectrum image"
        m.documents << doc
        doc = Document.new
        doc.file = File.open("data/peak_assignments.txt", 'rb')
        doc.description = "peak assignments"
        m.documents << doc
    



    end


    desc "Import 1H or 13C NMR from Spectral Database for Organic Compounds (SDBS) data"
    task :sdbs , [:dir] => [:environment] do |t, args|
      ref_text = 'SDBSWeb : http://sdbs.riodb.aist.go.jp (National Institute of Advanced Industrial Science and Technology, March 1, 2015)'
      csv_file_name = 'sdbs_table.csv'
      peak_file_dir = 'peak_files'

      if !Dir.exists?(args.dir)
        puts "Directory does not exist: #{args.dir}"
        exit
      end

      # Read CSV file
      CSV.foreach(File.join(args.dir, csv_file_name), headers: true, header_converters: :symbol) do |row|
        cas_id = row[:cas]
        puts "Importing: #{cas_id}"
        spec = NmrOneD.new

        # Get InChIKey
        inchikey = ''
        url = "https://cactus.nci.nih.gov/chemical/structure/#{cas_id}/stdinchikey"
        begin
          inchikey = Net::HTTP.get_response(URI.parse(url)).body
          inchikey.strip!
          inchikey.sub!(/InChIKey=/,"")
        rescue => e
          puts "Error getting InChIKey for CAS '#{cas_id}': #{e.message}"
          next
        end

        # Get structure_id corresponding to inchikey
        structure = Structure.find_by(inchikey: inchikey)
        if structure.nil?
          puts "Could not find structure for InChIKey '#{inchikey}' for CAS '#{cas_id}'"
          next
        end
        structure_id = structure.id
        spec.structure_id = structure_id

        # Check if there is already an entry
        nucleus = row[:nucleus]
        if !['1H', '13C'].include?(nucleus)
          puts "Nucleus must be '1H' or '13C' but was '#{nucleus}'"
          next
        end
        if NmrOneD.joins(:references).where('references.database = "SDBS"').where(nucleus: nucleus).find_by_structure_id(structure_id)
          puts "There is already a SDBS '#{nucleus}' NmrOneD spectra for this cas_id: #{cas_id}"
          next
        end
        # How to delete:
        # NmrOneD.joins(:references).where('references.database = "SDBS"').where('nucleus = "1H"').destroy_all
        # NmrOneD.joins(:references).where('references.database = "SDBS"').where('nucleus = "13C"').destroy_all

        # Add other meta data
        spec.nucleus = nucleus
        spec.solvent = row[:solvent]                      if row[:solvent].present?
        spec.frequency = row[:frequency]                  if row[:frequency].present?
        spec.chemical_shift_reference = row[:cs_standard] if row[:cs_standard].present?
        spec.instrument_type = row[:instrument_type]      if row[:instrument_type].present?
        spec.sample_ph = row[:ph]                         if row[:ph].present?
        if row[:temp].present?
          spec.sample_temperature = row[:temp]
          spec.sample_temperature_units = 'Celsius'
        end

        spec.searchable = true

        # Get peaks from peak file
        peak_file_path = File.join(args.dir, peak_file_dir, "#{cas_id}.csv")
        unless File.exists?(peak_file_path)
          puts "Skipping '#{cas_id}', no peak file found"
          next
        end
        CSV.foreach(peak_file_path, headers: true, header_converters: :symbol) do |peak_row|
          peak = NmrOneDPeak.new(chemical_shift: peak_row[:ppm], intensity: peak_row[:intensity])
          spec.peaks << peak
        end

        # Add reference
        ref = Reference.new
        ref.database = 'SDBS'
        ref.database_id = row[:sdbs]
        ref.ref_text = ref_text
        spec.references << ref

        # Add Peak file
        doc = Document.new
        doc.description = 'Peak List'
        doc.file = File.open(peak_file_path)
        spec.documents << doc

        spec.save!

      end
    end


    desc "Import 1H from HMDB"
    task h_hmdb: [:environment] do
      con_hmdb = Mysql2::Client.new(host: '***************',socket: '3306', username: 'readonly', password: 'tardis', database: 'hmdb', reconnect: true,cache_rows: false)
      con_labm = Mysql2::Client.new(host: '***************',socket: '3306', username: 'readonly', password: 'tardis', database: 'labm', reconnect: true,cache_rows: false)
      # count = 0

      con_hmdb.query("select metabolite_id, type, image_file, fids_file, assignment_file from spectra where type = 'hnmr' ").each(symbolize_keys: true) do |row|
        con_labm.query("select id, inchi from tbl_chemical where export_hmdb = 'Yes' AND id = '#{row[:metabolite_id]}' ").each(symbolize_keys: true) do |hmdb|
          # count += 1
          # break if count > 2
          inchikey = `molconvert inchikey -s "#{hmdb[:inchi]}"{inchi}`
          inchikey.strip!
          inchikey.sub!(/InChIKey=/,"")
          if(row[:image_file] =~ /(.*?):::.*?:::(\d+)/ || row[:fids_file] =~ /(.*?):::.*?:::(\d+)/ || row[:assignment_file] =~ /(.*?):::.*?:::(\d+)/)
            table = $1
            parent_id = nil
            con_labm.query("SELECT parent_id FROM #{table}_file WHERE #{table}_file.id='#{$2}'").each(symbolize_keys: true) do |r|
              parent_id = r[:parent_id]
            end
            con_labm.query("SELECT * FROM #{table} WHERE id='#{parent_id}'").each(symbolize_keys: true) do |r|
              next if NmrOneD.joins(:references).where("references.database = 'HMDB' AND references.database_id = '#{hmdb[:id]}' AND inchi_key = '#{inchikey}' AND nucleus = '1H'").count > 0

              m = Molecule.find_by_moldb_inchikey("InChIKey=#{inchikey}")
              if m.nil?
                m = Molecule.new
                m.structure = hmdb[:inchi]
                m.save!
              end

              spec = NmrOneD.new(nucleus: "1H", inchi_key: inchikey)
              spec.references << Reference.new(pubmed_id: 18953024, database: "HMDB", database_id: hmdb[:id])
              spec.molecule = m

              if(r[:assignment] =~ /.*?:::.*?:::(\d+)/)
                doc = Document.new
                doc.file = get_file_hmdb(con_labm,"#{table}_file", $1)
                doc.description = "Spectra image with peak assignments"
                spec.documents << doc
              end
              if(r[:spectra] =~ /.*?:::.*?:::(\d+)/)
                d = Document.new
                d.description = "Raw Spectrum Image"
                d.file = get_file_hmdb(con_labm,"#{table}_file", $1)
                spec.documents << d
              end
              if(r[:spectra_fids] =~ /.*?:::.*?:::(\d+)/)
                d = Document.new
                d.description = "Raw Free Induction Decay file for spectral processing"
                d.file = get_file_hmdb(con_labm,"#{table}_file", $1)
                spec.documents << d
              end
              if(r[:spectra_jdx] =~ /.*?:::.*?:::(\d+)/)
                d = Document.new
                d.description = "JCAMP file"
                d.file = get_file_hmdb(con_labm,"#{table}_file", $1)
                spec.documents << d
              end
              if(r[:spectra_peaklist] =~ /.*?:::.*?:::(\d+)/)
                d = Document.new
                d.description = "List of chemical shift values for the spectrum"
                f = get_file_hmdb(con_labm,"#{table}_file", $1, "ASCII-8BIT")
                d.file = f
                spec.documents << d
                flag = false
                ppm_index = nil
                height_index = nil
                fail = false
                max = 0
                File.open(f).each do |line|
                  #puts line
                  if !flag && line =~ /ppm.*height/i
                    flag = true
                    #puts line
                    line.gsub!(/Table of Peaks/i,'')
                    header = line.split(/\s+/)
                    header.each_index do |i|
                      header[i].strip!
                      header[i].gsub!(/^\(/,'')
                      header[i].gsub!(/\)$/,'')
                      header[i].downcase!

                      if header[i] == "ppm"
                        ppm_index = i
                      elsif header[i] == 'height'
                        height_index = i
                      end
                    end
                    if ppm_index.nil? || height_index.nil?
                      fail = true
                      puts "#{ppm_index} #{height_index}"
                      break
                    end
                    next
                  end

                  if flag && line =~ /\d+/
                    #puts line
                    data = line.split(/\s+/)
                    if data[ppm_index] =~ /\d+\.?\d*/ && data[height_index] =~ /\d+\.?\d*/
                      spec.peaks << NmrOneDPeak.new(chemical_shift: data[ppm_index], intensity: data[height_index])
                      max = data[height_index].to_f if data[height_index].to_f > max
                    else
                      fail = true
                      puts "#{ppm_index} #{height_index}"
                      break
                    end

                  elsif flag
                    break
                  end
                end
                if fail || max == 0

                  if hmdb[:id] == "HMDB00226"
                    spec.peaks << NmrOneDPeak.new(chemical_shift: 6.18)
                  else
                    puts "WARNING #{hmdb[:id]} peak list currupt"
                    puts File.open(f).read
                  end
                  # next
                else
                  spec.peaks.each do |peak|
                    peak.intensity = 100 * peak.intensity / max
                  end
                end
              else
                puts "WARNING #{hmdb[:id]} has no peak list"
                next
              end
              if(!r[:sample_preparation].nil? && r[:sample_preparation] =~ /^\d+$/)
                sop = get_sop(con_labm, r[:sample_preparation])
                spec.sops << sop if sop.present?
              end
              if(!r[:data_collection].nil? && r[:data_collection] =~ /^\d+$/)
                sop = get_sop(con_labm, r[:data_collection])
                spec.sops << sop if sop.present?
              end

              if !r[:sample_concentration].nil? && r[:sample_concentration] =~ /^(\d+\.?\d*)/
                spec.sample_concentration = $1
                spec.sample_concentration_units = "mM"
              end
              if !r[:mass_value].nil? && !r[:mass_unit].nil? && r[:mass_value] =~ /^(\d+\.?\d*)/
                spec.sample_mass = $1
                spec.sample_mass_units =  r[:mass_unit]
              end

              spec.sample_assessment = r[:sample_assessment]
              if table =~ /tbl_(.*)/
                spec.spectra_assessment = r["#{$1}_assessment".to_sym]
              end


              spec.sample_ph = r[:sample_ph]
              if !r[:sample_temp].nil? && r[:sample_temp] =~ /^(\d+\.?\d*)/
                spec.sample_temperature = $1
                spec.sample_temperature_units = "Celcius"
              end

              spec.chemical_shift_reference = r[:sample_shift]
              spec.solvent = r[:solvent]
              spec.instrument_type = r[:manufacturer]
              spec.frequency = r[:freq] + " MHz" if !r[:freq].nil?

              if table =~ /tbl_(.*)/
                spec.collection_date = r["#{$1}_collection_date".to_sym]
              end
              spec.save!
            end
          end
        end
      end

    end

    desc "Import 13C from HMDB"
    task c_hmdb: [:environment] do
      con_hmdb = Mysql2::Client.new(host: '***************',socket: '3306', username: 'readonly', password: 'tardis', database: 'hmdb', reconnect: true,cache_rows: false)
      con_labm = Mysql2::Client.new(host: '***************',socket: '3306', username: 'readonly', password: 'tardis', database: 'labm', reconnect: true,cache_rows: false)
      # count = 0

      con_hmdb.query("select metabolite_id, type, image_file, fids_file, assignment_file from spectra where type = 'cnmr' ").each(symbolize_keys: true) do |row|
        con_labm.query("select id, inchi from tbl_chemical where export_hmdb = 'Yes' AND id = '#{row[:metabolite_id]}' ").each(symbolize_keys: true) do |hmdb|
          # count += 1
          # break if count > 2
          inchikey = `molconvert inchikey -s "#{hmdb[:inchi]}"{inchi}`
          inchikey.strip!
          inchikey.sub!(/InChIKey=/,"")


          if(row[:image_file] =~ /(.*?):::.*?:::(\d+)/ || row[:fids_file] =~ /(.*?):::.*?:::(\d+)/ || row[:assignment_file] =~ /(.*?):::.*?:::(\d+)/)
            table = $1
            parent_id = nil
            con_labm.query("SELECT parent_id FROM #{table}_file WHERE #{table}_file.id='#{$2}'").each(symbolize_keys: true) do |r|
              parent_id = r[:parent_id]
            end
            con_labm.query("SELECT * FROM #{table} WHERE id='#{parent_id}'").each(symbolize_keys: true) do |r|
              next if NmrOneD.joins(:references).where("references.database = 'HMDB' AND references.database_id = '#{hmdb[:id]}' AND inchi_key = '#{inchikey}' AND nucleus = '13C'").count > 0

              spec = NmrOneD.new(nucleus: "13C", inchi_key: inchikey)
              spec.references << Reference.new(pubmed_id: 18953024, database: "HMDB", database_id: hmdb[:id])
              molecule = Molecule.find_by_moldb_inchikey("InChIKey=#{inchikey}")
              if molecule.nil?
                molecule = Molecule.new
                molecule.structure = hmdb[:inchi]
                molecule.save!
              end
              spec.molecule = molecule
              if(r[:assignment] =~ /.*?:::.*?:::(\d+)/)
                doc = Document.new
                doc.file = get_file_hmdb(con_labm,"#{table}_file", $1)
                doc.description = "Spectra image with peak assignments"
                spec.documents << doc
              end
              if(r[:spectra] =~ /.*?:::.*?:::(\d+)/)
                d = Document.new
                d.description = "Raw Spectrum Image"
                d.file = get_file_hmdb(con_labm,"#{table}_file", $1)
                spec.documents << d
              end
              if(r[:spectra_fids] =~ /.*?:::.*?:::(\d+)/)
                d = Document.new
                d.description = "Raw Free Induction Decay file for spectral processing"
                d.file = get_file_hmdb(con_labm,"#{table}_file", $1)
                spec.documents << d
              end
              if(r[:spectra_jdx] =~ /.*?:::.*?:::(\d+)/)
                d = Document.new
                d.description = "JCAMP file"
                d.file = get_file_hmdb(con_labm,"#{table}_file", $1)
                spec.documents << d
              end
              if(r[:spectra_peaklist] =~ /.*?:::.*?:::(\d+)/)
                d = Document.new
                d.description = "List of chemical shift values for the spectrum"
                f = get_file_hmdb(con_labm,"#{table}_file", $1, "ASCII-8BIT")
                d.file = f
                spec.documents << d
                flag = false
                ppm_index = nil
                height_index = nil
                fail = false
                File.open(f).each do |line|
                  if line =~ /^\s+\d+\s+\-?\d+\.\d+\s+\-?\d+\.\d+\s+(\-?\d+\.\d+)\s+(\-?\d+\.\d+)/
                    spec.peaks << NmrOneDPeak.new(chemical_shift: $1, intensity: $2)
                  end
                end
                if fail

                  # if hmdb[:id] == "HMDB00226"
                  #   spec.peaks << NmrOneDPeak.new(chemical_shift: 6.18)
                  # else
                  puts "WARNING #{hmdb[:id]} peak list currupt"
                  puts File.open(f).read
                  # end

                end
              else
                puts "WARNING #{hmdb[:id]} has no peak list"
                next
              end
              if(!r[:sample_preparation].nil? && r[:sample_preparation] =~ /^\d+$/)
                sop = get_sop(con_labm, r[:sample_preparation])
                spec.sops << sop if sop.present?
              end
              if(!r[:data_collection].nil? && r[:data_collection] =~ /^\d+$/)
                sop = get_sop(con_labm, r[:data_collection])
                spec.sops << sop if sop.present?
              end

              if !r[:sample_concentration].nil? && r[:sample_concentration] =~ /^(\d+\.?\d*)/
                spec.sample_concentration = $1
                spec.sample_concentration_units = "mM"
              end
              if !r[:mass_value].nil? && !r[:mass_unit].nil? && r[:mass_value] =~ /^(\d+\.?\d*)/
                spec.sample_mass = $1
                spec.sample_mass_units =  r[:mass_unit]
              end

              spec.sample_assessment = r[:sample_assessment]
              if table =~ /tbl_(.*)/
                spec.spectra_assessment = r["#{$1}_assessment".to_sym]
              end


              spec.sample_ph = r[:sample_ph]
              if !r[:sample_temp].nil? && r[:sample_temp] =~ /^(\d+\.?\d*)/
                spec.sample_temperature = $1
                spec.sample_temperature_units = "Celcius"
              end

              spec.chemical_shift_reference = r[:sample_shift]
              spec.solvent = r[:solvent]
              spec.instrument_type = r[:manufacturer]
              spec.frequency = r[:freq] + " MHz" if !r[:freq].nil?

              if table =~ /tbl_(.*)/
                spec.collection_date = r["#{$1}_collection_date".to_sym]
              end
              spec.save!
            end
          end
        end
      end

    end
    desc "Import JCAMP"
    task jcamp: [:environment] do
      con_hmdb = Mysql2::Client.new(host: '***************',socket: '3306', username: 'readonly', password: 'tardis', database: 'hmdb', reconnect: true,cache_rows: false)
      con_labm = Mysql2::Client.new(host: '***************',socket: '3306', username: 'readonly', password: 'tardis', database: 'labm', reconnect: true,cache_rows: false)
      count = 0
      con_labm.query("select id, inchi from tbl_chemical where export_hmdb = 'Yes' ").each(symbolize_keys: true) do |hmdb|
        # count += 1
        # break if count > 2
        inchikey = `molconvert inchikey -s "#{hmdb[:inchi]}"{inchi}`
        inchikey.strip!
        inchikey.sub!(/InChIKey=/,"")

        con_hmdb.query("select type, image_file, fids_file, assignment_file from spectra where metabolite_id='#{hmdb[:id]}' and type = 'cnmr' ").each(symbolize_keys: true) do |row|
          if(row[:image_file] =~ /(.*?):::.*?:::(\d+)/ || row[:fids_file] =~ /(.*?):::.*?:::(\d+)/ || row[:assignment_file] =~ /(.*?):::.*?:::(\d+)/)
            table = $1
            parent_id = nil
            con_labm.query("SELECT parent_id FROM #{table}_file WHERE #{table}_file.id='#{$2}'").each(symbolize_keys: true) do |r|
              parent_id = r[:parent_id]
            end
            con_labm.query("SELECT * FROM #{table} WHERE id='#{parent_id}'").each(symbolize_keys: true) do |r|
              spec = NmrOneD.select("nmr_one_d.*").joins(:references).where("references.database = 'HMDB' AND references.database_id = '#{hmdb[:id]}' AND inchi_key = '#{inchikey}' AND nucleus = '13C'").first
              next if spec.nil?
              if(r[:spectra_jdx] =~ /.*?:::.*?:::(\d+)/)
                d = Document.new
                d.description = "JCAMP file"
                d.file = get_file_hmdb(con_labm,"#{table}_file", $1)
                spec.documents << d
              end
              spec.save!
            end
          end
        end

        con_hmdb.query("select type, image_file, fids_file, assignment_file from spectra where metabolite_id='#{hmdb[:id]}' and type = 'hnmr' ").each(symbolize_keys: true) do |row|
          if(row[:image_file] =~ /(.*?):::.*?:::(\d+)/ || row[:fids_file] =~ /(.*?):::.*?:::(\d+)/ || row[:assignment_file] =~ /(.*?):::.*?:::(\d+)/)
            table = $1
            parent_id = nil
            con_labm.query("SELECT parent_id FROM #{table}_file WHERE #{table}_file.id='#{$2}'").each(symbolize_keys: true) do |r|
              parent_id = r[:parent_id]
            end
            con_labm.query("SELECT * FROM #{table} WHERE id='#{parent_id}'").each(symbolize_keys: true) do |r|
              spec = NmrOneD.select("nmr_one_d.*").joins(:references).where("references.database = 'HMDB' AND references.database_id = '#{hmdb[:id]}' AND inchi_key = '#{inchikey}' AND nucleus = '1H'").first
              next if spec.nil?
              if(r[:spectra_jdx] =~ /.*?:::.*?:::(\d+)/)
                d = Document.new
                d.description = "JCAMP file"
                d.file = get_file_hmdb(con_labm,"#{table}_file", $1)
                spec.documents << d
              end
              spec.save!
            end
          end
        end
      end
    end

    desc "Build new NMR Spectra"
    task :new, [:filename] => [:environment] do |t, args|
      inchi = ""
      type = ""
      m = nil
      CSV.foreach(args.filename, headers: true, header_converters: :symbol) do |row|
        if inchi != row[:moldb_inchikey] || type != row[:spectra_type]
          puts "IMPORTING #{row[:moldb_inchikey]} #{row[:spectra_type]}"
          m = NmrOneD.new
          m.inchi_key = row[:moldb_inchikey]
          m.notes = row[:spectra_type]
          if row[:spectra_type] =~ /\b1h\b/i
            m.nucleus = "1H"
          elsif row[:spectra_type] =~ /\b13c\b/i
            m.nucleus = "13C"
          end
          m.sample_concentration = row[:sample_conc]
          m.sample_temperature = row[:sample_temp]
          m.sample_ph = row[:sample_ph]
          m.sample_mass = row[:sample_mass]
          m.sample_assessment = row[:sample_assessment]
          m.spectra_assessment = row[:spectra_assessment]
          m.frequency = row[:frequency]
          m.instrument_type = row[:manufacturer]
          m.solvent = row[:solvent]
          m.chemical_shift_reference = row[:chemical_shift_ref]

          m.save!
        end
        mp = NmrOneDPeak.new
        mp.chemical_shift = row[:peak]
        uniques = row[:uniques].split(",")
        mp.unique_0 = uniques[0]
        mp.unique_1 = uniques[1]
        mp.unique_2 = uniques[2]
        mp.unique_3 = uniques[3]
        mp.unique_4 = uniques[4]

        m.nmr_1d_peaks << mp

        inchi = row[:moldb_inchikey]
        type = row[:spectra_type]
      end
    end
    desc "Task description"
    task :new_yml, [:filename] => [:environment] do |t, args|
      NmrOneDPeakAssignment.destroy_all
      NmrOneDPeakAssignmentLink.destroy_all
      data = YAML.load_file(args.filename)

      data.each do |spec|
        # puts spec["molecules_attributes"][0]["moldb_inchikey"]
        # puts spec[:molecules_attributes]
        puts spec["name"]
        # nmr1d = NmrOneD.find_or_create_by_inchi_key(spec["molecules_attributes"][0]["moldb_inchikey"])
        # nmr1d = NmrOneD.find_by_inchi_key(spec["molecules_attributes"][0]["moldb_inchikey"])
        nmr1d = NmrOneD.select("nmr_one_d.*").joins(:references).where("references.database = 'HMDB' AND references.database_id = '#{spec["name"]}' AND nucleus = '1H'").first
        if nmr1d.nil?
          puts "#{spec["name"]} Not Found"
          next



        end
        next if nmr1d.peak_assignments.count > 0
        fail = false
        #
        # nmr1d.instrument_type = spec["spectrometer_manufacturer"]
        # nmr1d.solvent = spec["solvent"]
        # nmr1d.sample_ph = spec["ph"]
        # nmr1d.sample_temperature = spec["temperature"]
        # nmr1d.chemical_shift_reference = spec["chemical_shift_reference"]
        # nmr1d.molecule = Molecule.new()
        # #nmr1d.molecule.structure = spec["molecules_attributes"][0]["moldb_inchi"]
        # spec["molecules_attributes"][0]["bonds_attributes"].each do |bond|
        #   nmr1d.molecule.bonds << Bond.new(bond)
        # end
        # spec["molecules_attributes"][0]["atoms_attributes"].each do |atom|
        #   nmr1d.molecule.atoms << Atom.new(atom)
        # end
        atoms = Hash.new
        spec["molecules_attributes"][0]["atoms_attributes"].each do |atom|
          atoms[atom["id"]] = atom["atom_number"]
        end

        spec["peaks_attributes"].each do |peak|
          next if peak["multiplicity"].blank?
          assignment = NmrOneDPeakAssignment.new
          assignment.multiplicity = peak["multiplicity"]
          assignment.atom = nmr1d.molecule.atoms.find_by_atom_number(atoms[peak["atom_id"]])
          peak["chemical_shifts_attributes"].each do |cs|
            next unless cs["value"] =~ /\d+/
            p = nmr1d.peaks.find_by_chemical_shift(cs["value"])
            # p.chemical_shift = cs["value"]
            if p.nil?
              p = nmr1d.peaks.where("chemical_shift BETWEEN #{(cs["value"].to_f - 0.01).round(2)} AND #{(cs["value"].to_f + 0.01).round(2)}").first
              if p.nil?
                puts "FAIL: #{spec["name"]}"
                # puts "#{(cs["value"].to_f - 0.01).round(2)} AND #{(cs["value"].to_f + 0.01).round(2)}"
                # puts "peak: #{cs["value"]}"
                # puts "nmr: #{nmr1d.id}"
                fail = true
              end
            end
            break if fail
            assignment.peaks << p
            # p.peak_assignments << assignment
            # nmr1d.nmr_one_d_peaks << p
          end
          break if fail
          assignment.save
        end
        puts "SUCCESS: #{spec["name"]}" if !fail
        # nmr1d.peak_assignments.delete_all if fail
        # nmr1d.molecule.save!
        # nmr1d.save!
        # nmr1d.molecule.structure = spec["molecules_attributes"][0]["moldb_inchi"]
        # nmr1d.molecule.save!
      end
    end
  end
  namespace :nmr2d do
    desc "hmdb HSQC"
    task hsqc_hmdb: [:environment] do
      con_hmdb = Mysql2::Client.new(host: '***************',socket: '3306', username: 'readonly', password: 'tardis', database: 'hmdb', reconnect: true,cache_rows: false)
      con_labm = Mysql2::Client.new(host: '***************',socket: '3306', username: 'readonly', password: 'tardis', database: 'labm', reconnect: true,cache_rows: false)
      count = 0

      con_hmdb.query("select metabolite_id, type, image_file, fids_file, assignment_file from spectra where type = 'hsqc2d' ").each(symbolize_keys: true) do |row|
        con_labm.query("select id, inchi from tbl_chemical where export_hmdb = 'Yes' AND id = '#{row[:metabolite_id]}' ").each(symbolize_keys: true) do |hmdb|
          # count += 1
          # break if count > 2
          inchikey = `molconvert inchikey -s "#{hmdb[:inchi]}"{inchi}`
          inchikey.strip!
          inchikey.sub!(/InChIKey=/,"")


          if(row[:image_file] =~ /(.*?):::.*?:::(\d+)/ || row[:fids_file] =~ /(.*?):::.*?:::(\d+)/ || row[:assignment_file] =~ /(.*?):::.*?:::(\d+)/)
            table = $1
            parent_id = nil
            con_labm.query("SELECT parent_id FROM #{table}_file WHERE #{table}_file.id='#{$2}'").each(symbolize_keys: true) do |r|
              parent_id = r[:parent_id]
            end
            con_labm.query("SELECT * FROM #{table} WHERE id='#{parent_id}'").each(symbolize_keys: true) do |r|
              next if NmrTwoD.joins(:references).where("references.database = 'HMDB' AND references.database_id = '#{hmdb[:id]}' AND inchi_key = '#{inchikey}' AND nucleus_x = '1H' AND nucleus_y = '13C'").count > 0
              next if hmdb[:id] == "HMDB00294"
              puts hmdb[:id]
              spec = NmrTwoD.new(nucleus_x: "1H", nucleus_y: "13C", notes: "HSQC", inchi_key: inchikey)
              spec.references << Reference.new(pubmed_id: 18953024, database: "HMDB", database_id: hmdb[:id])

              molecule = Molecule.find_by_moldb_inchikey("InChIKey=#{inchikey}")
              if molecule.nil?
                molecule = Molecule.new
                molecule.structure = hmdb[:inchi]
              end
              spec.molecule = molecule

              if(r[:assignment] =~ /.*?:::.*?:::(\d+)/)
                doc = Document.new
                doc.file = get_file_hmdb(con_labm,"#{table}_file", $1)
                doc.description = "Spectra image with peak assignments"
                spec.documents << doc
              end
              if(r[:spectra] =~ /.*?:::.*?:::(\d+)/)
                d = Document.new
                d.description = "Raw Spectrum Image"
                d.file = get_file_hmdb(con_labm,"#{table}_file", $1)
                spec.documents << d
              end
              if(r[:spectra_fids] =~ /.*?:::.*?:::(\d+)/)
                d = Document.new
                d.description = "Raw Free Induction Decay file for spectral processing"
                d.file = get_file_hmdb(con_labm,"#{table}_file", $1)
                spec.documents << d
              end
              if(r[:spectra_jdx] =~ /.*?:::.*?:::(\d+)/)
                d = Document.new
                d.description = "JCAMP file"
                d.file = get_file_hmdb(con_labm,"#{table}_file", $1)
                spec.documents << d
              end
              if(r[:spectra_peaklist] =~ /.*?:::.*?:::(\d+)/)
                d = Document.new
                d.description = "List of chemical shift values for the spectrum"
                f = get_file_hmdb(con_labm,"#{table}_file", $1, "ASCII-8BIT")
                d.file = f
                spec.documents << d

                # # F2[ppm] F1[ppm] Intensity Annotation
                #
                # 1 3.7753  53.5590 1513984.00
                # 2 1.4903  19.0295 1992912.00
                # 3 1.4365  19.0122 2180144.00
                max = 0
                File.open(f).each do |line|
                  puts line
                  if line =~ /^\s*\d*\s+(\d+\.\d+)\s+(\d+\.\d+)\s+(\d+\.?\d*)/
                    spec.peaks << NmrTwoDPeak.new(chemical_shift_x: $1, chemical_shift_y: $2, intensity: $3)
                    max = $3.to_f if max < $3.to_f
                  end
                end

                spec.peaks.each do |p|
                  p.intensity = p.intensity / max
                end
              else
                puts "WARNING #{hmdb[:id]} has no peak list"
                next
              end
              if(!r[:sample_preparation].nil? && r[:sample_preparation] =~ /^\d+$/)
                sop = get_sop(con_labm, r[:sample_preparation])
                spec.sops << sop if sop.present?
              end
              if(!r[:data_collection].nil? && r[:data_collection] =~ /^\d+$/)
                sop = get_sop(con_labm, r[:data_collection])
                spec.sops << sop if sop.present?
              end

              if !r[:sample_concentration].nil? && r[:sample_concentration] =~ /^(\d+\.?\d*)/
                spec.sample_concentration = $1
                spec.sample_concentration_units = "mM"
              end
              if !r[:mass_value].nil? && !r[:mass_unit].nil? && r[:mass_value] =~ /^(\d+\.?\d*)/
                spec.sample_mass = $1
                spec.sample_mass_units =  r[:mass_unit]
              end

              spec.sample_assessment = r[:sample_assessment]
              if table =~ /tbl_(.*)/
                spec.spectra_assessment = r["#{$1}_assessment".to_sym]
              end


              spec.sample_ph = r[:sample_ph]
              if !r[:sample_temp].nil? && r[:sample_temp] =~ /^(\d+\.?\d*)/
                spec.sample_temperature = $1
                spec.sample_temperature_units = "Celcius"
              end

              spec.chemical_shift_reference = r[:sample_shift]
              spec.solvent = r[:solvent]
              spec.instrument_type = r[:manufacturer]
              spec.frequency = r[:freq] + " MHz" if !r[:freq].nil?

              if table =~ /tbl_(.*)/
                spec.collection_date = r["#{$1}_collection_date".to_sym]
              end
              spec.save!

            end
          end
        end
      end
    end

    desc "hmdb TOCSY"
    task tocsy_hmdb: [:environment] do
      ApplicationRecord.transaction do
        con_hmdb = Mysql2::Client.new(host: '***************',socket: '3306', username: 'readonly', password: 'tardis', database: 'hmdb', reconnect: true,cache_rows: false)
        con_labm = Mysql2::Client.new(host: '***************',socket: '3306', username: 'readonly', password: 'tardis', database: 'labm', reconnect: true,cache_rows: false)

        con_hmdb.query("select metabolite_id, simplified_image_file, bmrb_image_file from tocsy_spectra").each(symbolize_keys: true) do |row|
          con_labm.query("select id, inchi from tbl_chemical where export_hmdb = 'Yes' AND id = '#{row[:metabolite_id]}'").each(symbolize_keys: true) do |hmdb|
            puts hmdb[:id]
            inchikey = `molconvert inchikey -s "#{hmdb[:inchi]}"{inchi}`
            inchikey.strip!
            inchikey.sub!(/InChIKey=/,"")


            if(row[:simplified_image_file] =~ /(.*?):::.*?:::(\d+)/ || row[:bmrb_image_file] =~ /(.*?):::.*?:::(\d+)/)

              table = $1
              parent_id = nil
              con_labm.query("SELECT parent_id FROM #{table}_file WHERE #{table}_file.id='#{$2}'").each(symbolize_keys: true) do |r|
                parent_id = r[:parent_id]
              end
              result = con_labm.query("SELECT * FROM #{table} WHERE id='#{parent_id}'").each(symbolize_keys: true) do |r|
                raise if inchikey.blank?
                next if NmrTwoD.joins(:references).where("references.database = 'BMRB' AND references.database_id = '#{hmdb[:id]}' AND inchi_key = '#{inchikey}' AND nucleus_x = '1H' AND nucleus_y = '1H'").count > 0
                spec = NmrTwoD.new(nucleus_x: "1H", nucleus_y: "1H", notes: "TOCSY", inchi_key: inchikey)
                spec.references << Reference.new(database: "BMRB", database_id: hmdb[:id])


                molecule = Molecule.find_by_moldb_inchikey("InChIKey=#{inchikey}")
                if molecule.nil?
                  molecule = Molecule.new
                  molecule.structure = hmdb[:inchi]
                end
                spec.molecule = molecule


                if(r[:spectra_peaklist] =~ /.*?:::.*?:::(\d+)/)
                  doc = Document.new
                  f = get_file_hmdb(con_labm,"#{table}_file", $1)
                  doc.file = f
                  doc.description = "List of chemical shift values for the spectrum (Simplified)"
                  spec.documents << doc

                  # max = 0
                  File.open(f).each do |line|
                    puts line
                  end
                  # spec.peaks.each do |p|
                  #   p.intensity = p.intensity / max
                  # end
                end

                if(r[:spectra] =~ /.*?:::.*?:::(\d+)/)
                  doc = Document.new
                  doc.file = get_file_hmdb(con_labm,"#{table}_file", $1)
                  doc.description = "Spectra image (Simplified)"
                  spec.documents << doc
                end
                puts
                if(r[:bmrb_peaklist] =~ /.*?:::.*?:::(\d+)/)

                  doc = Document.new
                  f = get_file_hmdb(con_labm,"#{table}_file", $1)
                  File.open(f).each do |line|
                    puts line
                    if line =~ /(\d+\.\d+)\s+(\d+\.\d+)/
                      spec.peaks << NmrTwoDPeak.new(chemical_shift_x: $1, chemical_shift_y: $2)
                      # max = $3.to_f if max < $3.to_f
                    else
                      raise "FAILED to Process #{hmdb[:id]}!!!!!"
                    end

                  end
                  doc.file = f
                  doc.description = "List of chemical shift values for the spectrum (Original)"
                  spec.documents << doc
                end
                if(r[:bmrb_spectra] =~ /.*?:::.*?:::(\d+)/)
                  doc = Document.new
                  doc.file = get_file_hmdb(con_labm,"#{table}_file", $1)
                  doc.description = "Spectra image with peak assignments (Original)"
                  spec.documents << doc
                end
                spec.save!
              end
            end
          end
        end
      end
    end
  end
  def get_sop(con, id, encoding = "UTF-8")
    sop = nil
    # puts "select sop_name, sop_file from tblsop where sopid = '#{id}'"
    result = con.query("select sop_name, sop_file from tblsop where sopid = '#{id}'")
    # sleep 1
    result.each(symbolize_keys: true) do |row|
      name = row[:sop_name]
      name.sub!('.doc','')

      sop = Sop.find_by_name(name)
      if sop.nil?
        sop = Sop.new(name: name)
        f = File.new("#{row[:sop_name]}","w+")
        f.write(row[:sop_file].force_encoding(encoding))
        f.flush
        sop.file = f
        sop.save!
      end
    end
    sop
  end
  def get_file_hmdb(con, tbl, id, encoding = "UTF-8")
    file = nil
    #con_labm = Mysql2::Client.new(host: 'hmdb.ca',socket: '3306', username: 'readonly', password: 'tardis', database: 'labm')
    # puts "select filename, file from #{tbl} where id='#{id}'"
    result = con.query("select filename, file from #{tbl} where id = '#{id}'")
    # sleep 1
    result.each(symbolize_keys: true) do |row|
      if(row[:filename] =~ /(.*)(\..*)/)
        file = Tempfile.new([$1, $2])
      else
        file = Tempfile.new(row[:filename])
      end
      # row[:file] #if !row[:file].valid_encoding?
      # puts row[:file].force_encoding("UTF-8")
      if encoding == "ASCII-8BIT"
        encoding_options = {
          invalid:           :replace,  # Replace invalid byte sequences
          undef:             :replace,  # Replace anything not defined in ASCII
          replace:           '',        # Use a blank for those replacements
          universal_newline: true       # Always break lines with \n
        }
        row[:file].force_encoding(encoding)
        row[:file].gsub!(/(\r\n)|(\r)/,"\n")
        row[:file].gsub!(/[^\w\s\d\.\-\%\*\(\)\n]/,'')
        file.write(row[:file].encode(Encoding.find('ASCII'), encoding_options))

        dump = File.new("dump.txt","w+")
        dump.write(row[:file].encode(Encoding.find('ASCII'), encoding_options))
        dump.close

      else
        file.write(row[:file].force_encoding(encoding))
      end
      file.flush

    end
    return file
  end

  namespace :eims do

    # dir: directory containing JCAMPDX (*.jdx) files from the NIST Chemistry WebBook
    # http://webbook.nist.gov/chemistry/
    # These files can be retrieved using the "nist_scrape_eims.rb" script
    # This task will skip the file if an NIST EIMS already exists with the same CAS number.
    # Edit the reference text as appropriate
    desc "Electron Ionization MS from NIST (JCAMPDX files)"
    task :nist, [:dir] => [:environment] do |t, args|

      ref_text = 'NIST Mass Spec Data Center, S.E. Stein, director, "Mass Spectra" in NIST Chemistry WebBook, NIST Standard Reference Database Number 69, Eds. P.J. Linstrom and W.G. Mallard, National Institute of Standards and Technology, Gaithersburg MD, 20899, http://webbook.nist.gov, (retrieved March 1, 2015).'

      if !Dir.exists?(args.dir)
        puts "Directory does not exist: #{args.dir}"
        exit
      end

      Dir.glob(File.join(args.dir, '*.jdx')) do |file|
        puts "Importing: #{File.basename(file)}"
        jcamp = File.read(file)
        cas_id = (jcamp =~ /##CAS REGISTRY NO=([\d-]+)/) ? $1.strip : ''
        next unless cas_id.present?
        # nist_id = (jcamp =~ /##\$NIST MASS SPEC NO=(\d+)/) ? $1 : ''

        # Get InChIKey
        inchikey = ''
        url = "http://cactus.nci.nih.gov/chemical/structure/#{cas_id}/stdinchikey"
        begin
          inchikey = Net::HTTP.get_response(URI.parse(url)).body
          inchikey.strip!
          inchikey.sub!(/InChIKey=/,"")
        rescue => e
          puts "Error getting InChIKey for CAS '#{cas_id}': #{e.message}"
          next
        end

        # Check if there is already an entry
        if EiMs.joins(:references).where('references.database = "NIST"').find_by_inchi_key(inchikey)
          puts "There is already an EiMS spectra for this cas_id: #{cas_id}"
          next
        end

        spec = EiMs.new
        spec.inchi_key = inchikey

        # Get Peak data
        peak_text = (jcamp =~ /##PEAK TABLE=\(XY..XY\)([\s\S]+)##END/) ? $1.strip : ''
        peaks_xy = peak_text.split(/\s+/).map { |p| p.split(/,/) }
        peaks_xy.each do |peak_xy|
          peak = EiMsPeak.new(mass_charge: peak_xy.first, intensity: peak_xy.last)
          spec.peaks << peak
        end

        # Add reference
        ref = Reference.new
        ref.database = 'NIST'
        ref.database_id = cas_id # NIST uses cas id in URL
        ref.ref_text = ref_text
        spec.references << ref

        # Add JCAMP-DX file as document
        doc = Document.new
        doc.description = 'JCAMP-DX file'
        doc.file = File.open(file)
        spec.documents << doc

        # Generate SPLASH key on saving a new spectrum
        spec.regenerate_splash_key
        spec.save!
      end

    end

  end

  namespace :nmrml do

    # This task will import nmrML files from the supplied directory. Any
    # previous nmrML files for spectra will be removed.
    # The file names must have the followin format:  specdb_id.nmrML
    desc "Import nmrML files for 1D NMR spectra"
    task :nmr_1d => [:environment] do
      input_dir = ENV['INPUT_DIR']
      unless input_dir
        puts "INPUT_DIR must be set"
        exit
      end

      # nmroned_spectra = NmrOneD.all
      # nmroned_spectra.each do |spectrum|
      #   doc = spectrum.documents.find{|x| /nmrML/i.match x.description }
      #   if doc
      #     doc.destroy!
      #   end
      # end

      files = Dir.glob(File.join(input_dir, '*.nmrML'))
      files.each do |file|
        basename = File.basename(file)
        if basename =~ /^(\d+)\.nmrML$/
          specdb_id = $1
          puts "Importing nmrML for '#{specdb_id}'"
          spectrum = NmrOneD.find_by_id(specdb_id)
          unless spectrum
            puts "There is no 1D NMR spectrum with the ID '#{specdb_id}'"
            next
          end

          doc = spectrum.documents.find{|x| /nmrML/i.match x.description }
          if doc
            puts "Overwriting old nmrML..."
          else
            doc = Document.new
            doc.description = 'nmrML'
            doc.spectra = spectrum
          end
          doc.file = File.open(file)
          doc.save!
        end
      end
    end
  end


  #desc "Build new MS MS spectra from MoNA (NEG)"
  #task :new_LC_MS_NEG_spectra_MoNA => [:environment] do
  #desc "Build new MS MS spectra from MoNA (POS)"
  desc "Build new MS MS spectra from GNPS (LC-MS)"
  task :new_LC_MS_POS_spectra_MoNA => [:environment] do
    f = File.open('public/MoNA_LC_spectra_id_added.tsv', "w")
    control_var = 0
    #CSV.open('public/Mona_splash0_inchi1_LC_NEG_uniq_final.tsv', "r", :col_sep => "\t", :quote_char => "\x00").each do |row|
    #CSV.open('public/Mona_splash0_inchi1_LC_POS_uniq_final_filtered_spectra.tsv', "r", :col_sep => "\t", :quote_char => "\x00").each do |row|
    #CSV.open('public/GNPS_LC_NEG_uniq_final.tsv', "r", :col_sep => "\t", :quote_char => "\x00").each do |row|
    #CSV.open('public/GNPS_LC_POS_uniq_final.tsv', "r", :col_sep => "\t", :quote_char => "\x00").each do |row|
    #CSV.open('public/Sumner_msms_NEG_uniq.tsv', "r", :col_sep => "\t", :quote_char => "\x00").each do |row|
    #CSV.open('public/Sumner_msms_file_2_uniq.tsv', "r", :col_sep => "\t", :quote_char => "\x00").each do |row|
    #CSV.open('public/Sumner_msms_file_1_uniq.tsv', "r", :col_sep => "\t", :quote_char => "\x00").each do |row|
    #CSV.open('public/Sumner_msms_confirmed_nmr_1_uniq.tsv.tsv', "r", :col_sep => "\t", :quote_char => "\x00").each do |row|
    #CSV.open('public/GNPS_LC_POS_Mol_NEG_uniq_final.tsv', "r", :col_sep => "\t", :quote_char => "\x00").each do |row|
    #CSV.open('public/GNPS_LC_NEG_Mol_POS_uniq_final.tsv', "r", :col_sep => "\t", :quote_char => "\x00").each do |row|
    CSV.open('public/Mona_LC_POS_NEG_uniq_1.tsv', "r", :col_sep => "\t", :quote_char => "\x00").each do |row|
      #peaks = JSON.parse(row[7]) if !row[7].nil?
      #peaks = JSON.parse(row[2]) if !row[2].nil?
      peaks = JSON.parse(row[7]) if !row[7].nil?
      if peaks.present?
        m = MsMs.new
        m.structure_id = row[2].to_i if !row[2].nil?
        #m.structure_id = row[1].to_i if !row[1].nil?
        puts "struture id = #{row[2].to_i}"
        m.splash_key = row[0] if !row[0].nil?
        puts "spalsh_key = #{row[0]}"
        m.notes = row[3] if !row[3].nil?
        puts "note = #{row[3]}"
        m.instrument_type = row[4] if !row[4].nil?
        #m.instrument_type = row[5] if !row[5].nil?
        #m.instrument_type = row[3] if !row[3].nil?
        puts "instrument_type = #{row[4]}"
        #m.ionization_mode = row[5] if !row[5].nil?
        #m.ionization_mode = row[4] if !row[4].nil?
        m.ionization_mode = row[6] if !row[6].nil?
        #if !row[8].nil? && row[8] == "neg"
          #m.ionization_mode = "Negative"
        #elsif !row[8].nil? && row[8] == "pos"
          #m.ionization_mode = "Positive"
        #end
        #m.ionization_mode = "Positive"
        #m.collision_energy_voltage = row[4].strip.to_i if row[4] != "N"
        #puts "#{row[4].strip}"
        #puts " ionization_mode = #{row[4]}"
      #peaks = JSON.parse(row[7]) if !row[7].nil?
      #if peaks.present?
        for i in 0..(peaks.length-1)
        #$j = 0
        #for i in 0..(peaks.length/2-1)
          value_x_y = peaks[i].split(":") 
          #value_x_y = peaks[i].split("\t")     
          mp = MsMsPeak.new
          mp.mass_charge = value_x_y[0].strip.to_f
          #mp.mass_charge = peaks[$j].strip.to_f
          mp.intensity = value_x_y[1].strip.to_f
          #mp.intensity = peaks[$j+1].strip.to_f
          puts " peak #{i}'s x= #{value_x_y[0].strip.to_f} and y= #{value_x_y[1].strip.to_f}"
          #puts " peak #{i}'s x= #{peaks[$j].strip.to_f} and y= #{peaks[$j+1].strip.to_f}"
          mp.save!
          m.peaks << mp
          #$j = $j+2
        end

        # Generate SPLASH key on saving a new spectrum
        #m.regenerate_splash_key!
        m.save!
        puts "#{m.id} has been saved"
        f.write("#{m.id}")
        reference = Reference.new
        reference.spectra_id = m.id
        reference.spectra_type = MsMs
        #reference.ref_text = row[6]
        reference.ref_text = row[8]
        #reference.database = row[7] 
        reference.database = "MoNA"
        #reference.database_id = row[4]
        reference.database_id = row[7]
        reference.save!
        puts "reference saved"
        puts "#{reference.id} saved for #{m.id}"
        control_var += 1
      end
    end
    f.close
    puts "total #{control_var} is saved"
  end

  desc "Check uniq spectra file from Gnps whether peaks are decimal(15,10)"
  task :checking_peaks_gnps_spectra => [:environment] do
  f = File.open('public/GNPS_LC_uniq_final_1.tsv', 'w')
  CSV.open('public/GNPS_LC_uniq_1.tsv', "r", :col_sep => "\t", :quote_char => "\x00").each do |row|
    $control_var = 0
    peaks = JSON.parse(row[4]) if !row[4].nil?
    if peaks.present?
      for i in 1..(peaks.length-1)
        value_x_y = peaks[i].split("\t") 
        value_x_before_point = value_x_y[0].split(".")[0]
        value_x_after_point = value_x_y[0].split(".")[1] 
        value_y_before_point = value_x_y[1].split(".")[0]  
        value_y_after_point = value_x_y[1].split(".")[1]  
        if value_x_before_point.length <= 5 && value_x_after_point.length <=10 && value_y_before_point.length <= 5 && value_y_after_point.length <= 10
          $control_var += 1
        end
      end
      puts $control_var
      puts peaks.length
      if $control_var == peaks.length
        f.write("#{row[0]}\t#{row[1]}\t#{row[2]}\t#{row[3]}\t#{row[4]}\t#{row[5]}\t#{row[6]}\t#{row[7]}\t#{row[8]}\n")
        puts "#{row[0]}\t#{row[1]}\t#{row[2]}\t#{row[3]}\t#{row[4]}\t#{row[5]}\t#{row[6]}\t#{row[7]}\t#{row[8]}"
      end
      #puts " peak #{i}'s x= #{value_x_y[0].strip.to_f} and y= #{value_x_y[1].strip.to_f}"
    end
  end
  f.close
end

  desc "Build new MS MS spectra from GNPS Library"
  task :add_spectra_from_GNPS => [:environment] do
    f = File.open('public/GNPS_LC_spectra_id_added.tsv', "w")
    control_var = 0
    CSV.open('public/GNPS_LC_uniq_final_1.tsv', "r", :col_sep => "\t", :quote_char => "\x00").each do |row|
      peaks = JSON.parse(row[4]) if !row[4].nil?
      if peaks.present?
        m = MsMs.new
        m.structure_id = row[1].to_i if !row[1].nil?
        puts "struture id = #{row[1].to_i}"
        m.notes = "From GNPS Library"
        puts "m.notes"
        m.instrument_type = row[2] if !row[2].nil?
        puts "instrument_type = #{row[2]}"
        m.ionization_mode = row[3] if !row[3].nil?
        puts "ionization_mode = #{row[3]}"
        for i in 0..(peaks.length-1)
          value_x_y = peaks[i].split("\t")     
          mp = MsMsPeak.new
          mp.mass_charge = value_x_y[0].strip.to_f
          mp.intensity = value_x_y[1].strip.to_f
          puts " peak #{i}'s x= #{value_x_y[0].strip.to_f} and y= #{value_x_y[1].strip.to_f}"
          mp.save!
          m.peaks << mp
        end
        # Generate SPLASH key on saving a new spectrum
        #m.regenerate_splash_key!
        m.save!
        puts "#{m.id} has been saved"
        reference = Reference.new
        reference.spectra_id = m.id
        reference.spectra_type = MsMs
        reference.ref_text = row[7] if !row[7].nil?
        reference.database = row[8] if !row[8].nil?
        reference.database_id = row[5] if !row[5].nil?
        reference.save!
        puts "reference saved"
        puts "#{reference.id} saved for #{m.id}"
        f.write("spectrum id = #{m.id} and reference id =#{reference.id}\n")
        control_var += 1
      end
    end
    f.close
    puts "total #{control_var} is saved"
  end

  desc "Build new MS MS spectra from Sumner Library"
  task :add_spectra_from_sumner => [:environment] do
    f = File.open('public/Sumner_LC_spectra_id_added.tsv', "w")
    control_var = 0
    CSV.open('public/Sumner_LC_uniq_1.tsv', "r", :col_sep => "\t", :quote_char => "\x00").each do |row|
      peaks = JSON.parse(row[5]) if !row[5].nil?
      if peaks.present?
        m = MsMs.new
        m.structure_id = row[1].to_i if !row[1].nil?
        puts "struture id = #{row[1].to_i}"
        m.notes = "From Sumner Library"
        puts "#{m.notes}"
        m.instrument_type = row[2] if !row[2].nil?
        puts "instrument_type = #{row[2]}"
        if !row[3].nil? && row[3] == "neg"
          m.ionization_mode = "Negative"
        elsif !row[3].nil? && row[3] == "pos"
          m.ionization_mode = "Positive"
        end
        m.collision_energy_voltage = row[4].strip.to_i if row[4] != "N"
        puts " ionization_mode = #{row[4]}"
        $j = 0
        for i in 0..(peaks.length/2-1)  
          mp = MsMsPeak.new
          mp.mass_charge = peaks[$j].strip.to_f
          mp.intensity = peaks[$j+1].strip.to_f
          puts " peak #{i}'s x= #{peaks[$j].strip.to_f} and y= #{peaks[$j+1].strip.to_f}"
          mp.save!
          m.peaks << mp
          $j = $j+2
        end
        # Generate SPLASH key on saving a new spectrum
        #m.regenerate_splash_key!
        m.save!
        puts "#{m.id} has been saved"
        reference = Reference.new
        reference.spectra_id = m.id
        reference.spectra_type = MsMs
        reference.ref_text = row[7] if !row[7].nil? 
        reference.database = row[8] if !row[8].nil? 
        reference.save!
        puts "reference saved"
        puts "#{reference.id} saved for #{m.id}"
        f.write("spectrum id = #{m.id} and reference id =#{reference.id}\n")
        control_var += 1
      end
    end
    f.close
    puts "total #{control_var} is saved"
  end


  desc "Build new MS MS spectra from MoNA (GC-MS)"
  task :add_GC_spectra_from_MoNA => [:environment] do
    f = File.open('public/MoNA_GC_spectra_id_added.tsv', "w")
    control_var = 0
    CSV.open('public/Mona_GC_uniq_final.tsv', "r", :col_sep => "\t", :quote_char => "\x00").each do |row|
      peaks = JSON.parse(row[7]) if !row[7].nil?
      if peaks.present?
        m = CMs.new
        m.structure_id = row[2].to_i if !row[2].nil?
        puts "struture id = #{row[2].to_i}"
        m.splash_key = row[0] if !row[0].nil?
        puts "spalsh_key = #{row[0]}"
        m.notes = row[3] if !row[3].nil?
        puts "note = #{row[3]}"
        m.instrument_type = row[4] if !row[4].nil?
        puts "instrument_type = #{row[4]}"
        m.ionization_mode = row[6] if !row[6].nil?
        puts " ionization_mode = #{row[6]}"
        #m.retention_index = number_with_precision(row[6].to_f, precision: 5) if !row[6].nil?
        #puts " ionization_mode = #{row[6]}"
        m.chromatography_type = "GC"
        puts " chromatography_type = #{m.chromatography_type}"
        for i in 0..(peaks.length-1)
          value_x_y = peaks[i].split(":")   
          mp = CMsPeak.new
          mp.mass_charge = value_x_y[0].strip.to_f
          mp.intensity = value_x_y[1].strip.to_f
          puts " peak #{i}'s x= #{value_x_y[0].strip.to_f} and y= #{value_x_y[1].strip.to_f}"
          mp.save!
          m.peaks << mp
        end

        # Generate SPLASH key on saving a new spectrum
        #m.regenerate_splash_key
        m.save!
        puts "#{m.id} has been saved"
        reference = Reference.new
        reference.spectra_id = m.id
        reference.spectra_type = "GC Ms"
        reference.ref_text = row[9] if !row[9].nil? 
        reference.database = "MoNA"
        reference.database_id = row[8] if !row[8].nil?
        reference.save!
        puts "reference saved"
        puts "#{reference.id} saved for #{m.id}"
        f.write("spectrum id = #{m.id} and reference id =#{reference.id}\n")
        control_var += 1
      end
    end
    f.close
    puts "total #{control_var} is saved"
  end

  desc "Build new MS MS spectra from MoNA (LC-MS)"
  task :add_LC_spectra_from_MoNA => [:environment] do
    f = File.open('public/MoNA_LC_spectra_id_added.tsv', "w")
    control_var = 0
    CSV.open('public/Mona_POS_NEG_uniq_final_1_combined_with_2nd_version.tsv', "r", :col_sep => "\t", :quote_char => "\x00").each do |row|
      peaks = JSON.parse(row[7]) if !row[7].nil?
      if peaks.present?
        m = MsMs.new
        m.structure_id = row[2].to_i if !row[2].nil?
        puts "struture id = #{row[2].to_i}"
        m.splash_key = row[0] if !row[0].nil?
        puts "spalsh_key = #{row[0]}"
        m.notes = row[3] if !row[3].nil?
        puts "note = #{row[3]}"
        m.instrument_type = row[4] if !row[4].nil?
        puts "instrument_type = #{row[4]}"
        m.ionization_mode = row[6] if !row[6].nil?
        for i in 0..(peaks.length-1)
          value_x_y = peaks[i].split(":")     
          mp = MsMsPeak.new
          mp.mass_charge = value_x_y[0].strip.to_f
          mp.intensity = value_x_y[1].strip.to_f
          puts " peak #{i}'s x= #{value_x_y[0].strip.to_f} and y= #{value_x_y[1].strip.to_f}"
          mp.save!
          m.peaks << mp
        end

        # Generate SPLASH key on saving a new spectrum
        #m.regenerate_splash_key!
        m.save!
        puts "#{m.id} has been saved"
        f.write("#{m.id}")
        reference = Reference.new
        reference.spectra_id = m.id
        reference.spectra_type = MsMs
        reference.ref_text = row[9] if !row[9].nil?
        reference.database = "MoNA"
        reference.database_id = row[8] if !row[8].nil?
        reference.save!
        puts "reference saved"
        puts "#{reference.id} saved for #{m.id}"
        control_var += 1
      end
    end
    f.close
    puts "total #{control_var} is saved"
  end

  desc "update_reference"
  task :update_reference  => [:environment] do
    #CSV.open('public/pubmed_ref_text.tsv', "r", :col_sep => "\t").each do |row|
    CSV.open('public/moldb_citation_update.tsv', "r", :col_sep => "\t").each do |row|
      #row = row.gsub /^$\n/, ''
      id, spectra_id, spectra_type, pubmed_id, ref_text, ref_text_new = row
      
      puts "#{id} #{spectra_id} #{ref_text}"
      #reference = Reference.find_by_spectra_id(spectra_id.to_i)
      reference = Reference.find_by_id(id.to_i)
      #pid = reference.pubmed_id
      rtext = reference.ref_text
      if rtext.include? "PlantBiology"
        reference.ref_text = ref_text_new
        reference.save!
      else
        puts "Wrong id and ref_text"
      end
    end
  end

  desc "Add reference to predicted MS MS spectra"
  task :add_reference_to_spectra_ref_text  => [:environment] do
    CSV.open('public/predicted_msms.tsv', "r", :col_sep => "\t").each do |row|
      spectra_id, notes = row
      
      ref_text = "Allen, F., Greiner, R., Allen, F. (2015) Competitive fragmentation modeling of ESI-MS/MS spectra for putative metabolite identification. Metabolomics. 11(1):98–110"
      
      #ms_ms = MsMs.find(spectra_id.to_i)
      puts "#{spectra_id}"
      spectra_type = "MsMs"

      #ms_ms.references << Reference.new(pubmed_id: 18953024)
      # c_ms.references << Reference.new(ref_text: reference, spectra_type: "GC Ms")
      # ms_ms.references << Reference.new(ref_text: reference)
      # ms_ms.save!

      reference = Reference.new
      reference.spectra_id = spectra_id
      reference.spectra_type = "MsMs"
      reference.ref_text = ref_text
      reference.save!

    end
  end

  desc "Add reference to predicted GC MS spectra"
  task :add_reference_to_spectra_pubmed  => [:environment] do
    CSV.open('public/predicted_c_ms.tsv', "r", :col_sep => "\t").each do |row|
      spectra_id, notes = row
      
      pubmed_id="27381172"
      #c_ms = CMs.find(spectra_id.to_i)
      puts "#{spectra_id}"
      spectra_type = "GC Ms"

      # #c_ms.references << Reference.new(pubmed_id: 18953024)
      # c_ms.references << Reference.new(ref_text: reference, spectra_type: "GC Ms")
      # #c_ms.references << Reference.new(ref_text: reference)
      # c_ms.save!

      reference = Reference.new
      reference.spectra_id = spectra_id
      reference.spectra_type = spectra_type
      reference.pubmed_id = pubmed_id
      reference.save!

    end
  end



  desc "Destroy predicted GC-MS spectra for compounds with MW>700 Da"
  task :destroy_GC_MS_predicted => [:environment] do
    CSV.open('public/gc_ms_to_remove.tsv', "r", :col_sep => "\t").each do |row|
      mw, spectra_id, notes, instrument_type, chromatography_type = row
      puts "#{spectra_id}"
      c_ms = CMs.find(spectra_id.to_i)
      peak = c_ms.peaks
      peak.each do |each_peak|
        puts "#{each_peak}"
        each_peak.destroy
      end
      c_ms.destroy
      puts "destroyed #{spectra_id}"
    end
  end


  desc "Destroy Duplicate MS MS spectra from MoNA (NEG) from local"
  task :destroy_LC_MS_NEG_spectra_MoNA => [:environment] do
    CSV.open('public/delete_ms_ms_lc_neg.tsv', "r").each do | id |
      ms_ms = MsMs.find(id[0].to_i)
      ms_ms.destroy
      puts "destroyed #{id}"
    end
  end
      
  desc "Destroy Duplicate MS MS spectra from MoNA (NEG) added on 27th of april"
  task :destroy_duplicate_LC_MS_NEG_spectra_MoNA => [:environment] do
    CSV.open('public/delete_duplicate_lc_msms_neg.tsv', "r").each do | id |
      ms_ms = MsMs.find(id[0].to_i)
      ms_ms.destroy
      puts "destroyed #{id}"
    end
  end


  desc "Destroy Duplicate MS MS spectra from MoNA (POS) added on 27th and 28th of april"
  task :destroy_duplicate_LC_MS_POS_spectra_MoNA => [:environment] do
    CSV.open('public/Delete_duplicate_LS_MS_POS_ON_20170428_2.tsv', "r").each do | id |
      ms_ms = MsMs.find(id[0].to_i)
      peak = ms_ms.peaks
      peak.each do |each_peak|
        each_peak.destroy
        puts "#{ms_ms.id}s peak destroyed" 
      end
      ms_ms.destroy
      puts "destroyed #{id}"
    end
  end



  desc "Destroy Duplicate MS MS spectra from MoNA (POS) added on 27th and 28th of april"
  task :destroy_duplicate_LC_MS_POS_spectra_MoNA_05 => [:environment] do
    CSV.open('public/Delete_duplicate_LS_MS_POS_ON_20170515.tsv', "r").each do | id |
      ms_ms = MsMs.find(id[0].to_i)
      peak = ms_ms.peaks
      peak.each do |each_peak|
        each_peak.destroy
      end
      ms_ms.destroy
      puts "destroyed #{id}"
    end
  end

  desc "Destroy Spectra from MoNA GNPS Sumner as Naama found some bugs, will add again"
  task :destroy_MS_MS_MONA_GNPS_SUMNER => [:environment] do
    CSV.open('public/LCMoNA_GNPS_SUMNER_spectra_to_be_deleted.tsv', "r").each do | id |
      ms_ms = MsMs.find(id[0].to_i)
      peak = ms_ms.peaks
      peak.each do |each_peak|
        each_peak.destroy
      end
      ms_ms.destroy
      puts "destroyed #{id}"
    end
  end

  desc "Destroy References from MoNA GNPS Sumner as Naama found some bugs, will add again"
  task :destroy_reference_MONA_GNPS_SUMNER => [:environment] do
    CSV.open('public/References_to_be_deleted.tsv', "r").each do | id |
      r = Reference.find(id[0].to_i)
      r.destroy
      puts "destroyed #{id}"
    end
  end

  desc "Destroy Spectra from GC MoNA  as Naama found some bugs, will add again"
  task :destroy_GC_MS_MONA_ => [:environment] do
    CSV.open('public/GCMona_to_be_destroyed.tsv', "r").each do | id |
      c_ms = CMs.find(id[0].to_i)
      peak = c_ms.peaks
      peak.each do |each_peak|
        each_peak.destroy
      end
      c_ms.destroy
      puts "destroyed #{id}"
    end
  end

  desc "Collect all the database compounds with external ids"
  task :collect_ext_ids => [:environment] do
    f = File.open('public/database_compounds_with_ids.tsv', "w")
    f.write("InChIKey\tSmiles\tName\tKegg_id\tKegg_drug_id\tChebi_id\tChembl_id\tChemspider_id\tPubchem_id\tLigand_expo_id\tT3db_id\tHmdb_id\tFoodb_id\tDrugbank_id\tEcmdb_id\tYmdb_id\tPhenol_id\tMeta_cyc_id\tWikipedia_id\tknapsack_id\tBigg_id\tBugowiki_id\tMetagene_id\tMetlin_id\tThreed_met_id\tReaxys\tGmelin\tBeilstein\tHsdb_id\tDfc_id\tMona_id\tIcsc_id\tPdbe_id\tIuphar_id\tPubchem_dotf_id\tNih_id\tZinc_id\tEmolecules_id\tIbm_patent_id\tAtlas_id\tPatent_id\tfda_srs_id\tSurechem_id\tPharmkgb_id\tSelleck_id\tPubchem_thomson_id\tMcule_id\tNmrshiftdb_id\tLincs_id\tActor_id\tRecon_id\tMolport_id\tNikkaji_id\tBindingdb_id\n\n")
    Structure.all.each do |s|
      if s.database_registrations.where("database_id LIKE 'DB%' OR database_id LIKE 'CHEM%' OR database_id LIKE 'HMDB%' OR database_id LIKE 'FDB%' OR database_id LIKE 'T3D%' OR database_id LIKE 'YMDB%' OR database_id LIKE 'RMDB%' OR database_id LIKE 'PE%' OR database_id LIKE 'DBMET%' OR database_id LIKE 'BMOL%' OR database_id LIKE 'PW_C%' OR database_id LIKE 'BMDB%' OR database_id LIKE 'M2MDB%' OR database_id LIKE 'EE%' OR database_id LIKE 'LMDB%' OR database_id LIKE 'PHUB%' OR database_id LIKE 'PMC%'").present?
        puts "if passed"
        if !s.curation.nil?
        r = JSON.parse(s.curation.result)
        puts "parse passed"
        if r["structures"]["inchikey"] != nil 
          f.write("#{r["structures"]["inchikey"]}\t") 
        else
          f.write("NULL\t") 
        end
        if r["structures"]["smiles"] != nil
          f.write("#{r["structures"]["smiles"]}\t")
        else
         f.write("NULL\t") 
        end 
        if r["identifiers"]["name"] != nil
          f.write("#{r["identifiers"]["name"]}\t") 
        else
          f.write("NULL\t") 
        end
        if r["identifiers"]["kegg_id"] != nil
          f.write("#{r["identifiers"]["kegg_id"]}\t") 
        else
          f.write("NULL\t")  
        end
        if r["identifiers"]["kegg_drug_id"] != nil
          f.write("#{r["identifiers"]["kegg_drug_id"]}\t") 
        else
          f.write("NULL\t") 
        end
        if r["identifiers"]["chebi_id"] != nil
          f.write("#{r["identifiers"]["chebi_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["chembl_id"] != nil
          f.write("#{r["identifiers"]["chembl_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["chemspider_id"] != nil
          f.write("#{r["identifiers"]["chemspider_id"]}\t")
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["pubchem_id"] != nil
          f.write("#{r["identifiers"]["pubchem_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["ligand_expo_id"] != nil
          f.write("#{r["identifiers"]["ligand_expo_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["t3db_id"] != nil
          f.write("#{r["identifiers"]["t3db_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["hmdb_id"] != nil
          f.write("#{r["identifiers"]["hmdb_id"]}\t")
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["foodb_id"] != nil
          f.write("#{r["identifiers"]["foodb_id"]}\t")
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["drugbank_id"] != nil
          f.write("#{r["identifiers"]["drugbank_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["ecmdb_id"] != nil
          f.write("#{r["identifiers"]["ecmdb_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["ymdb_id"] != nil
          f.write("#{r["identifiers"]["ymdb_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["phenol_id"] != nil
          f.write("#{r["identifiers"]["phenol_id"]}\t")
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["meta_cyc_id"] != nil
          f.write("#{r["identifiers"]["meta_cyc_id"]}\t")
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["wikipedia_id"] != nil
          f.write("#{r["identifiers"]["wikipedia_id"]}\t")
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["knapsack_id"] != nil
          f.write("#{r["identifiers"]["knapsack_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["bigg_id"] != nil
          f.write("#{r["identifiers"]["bigg_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["nugowiki_id"] != nil
          f.write("#{r["identifiers"]["nugowiki_id"]}\t")
        else
         f.write("NULL\t")
        end 
        if r["identifiers"]["metagene_id"] != nil
          f.write("#{r["identifiers"]["metagene_id"]}\t") 
        else
          f.write("NULL\t")
        end 
        if r["identifiers"]["metlin_id"] != nil
          f.write("#{r["identifiers"]["metlin_id"]}\t") 
        else
          f.write("NULL\t")
        end 
        if r["identifiers"]["threed_met_id"] != nil
          f.write("#{r["identifiers"]["threed_met_id"]}\t")
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["reaxys"] != nil
          f.write("#{r["identifiers"]["reaxys"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["gmelin"] != nil
          f.write("#{r["identifiers"]["gmelin"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["beilstein"] != nil
          f.write("#{r["identifiers"]["beilstein"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["hsdb_id"] != nil
          f.write("#{r["identifiers"]["hsdb_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["dfc_id"] != nil
          f.write("#{r["identifiers"]["dfc_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["mona_id"] != nil
          f.write("#{r["identifiers"]["mona_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["icsc_id"] != nil
          f.write("#{r["identifiers"]["icsc_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["pdbe_id"] != nil
          f.write("#{r["identifiers"]["pdbe_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["iuphar_id"] != nil
          f.write("#{r["identifiers"]["iuphar_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["pubchem_dotf_id"] != nil
          f.write("#{r["identifiers"]["pubchem_dotf_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["nih_id"] != nil
          f.write("#{r["identifiers"]["nih_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["zinc_id"] != nil
          f.write("#{r["identifiers"]["zinc_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["emolecules_id"] != nil
          f.write("#{r["identifiers"]["emolecules_id"]}\t") 
        else
          f.write("NULL\t")
        end 
        if r["identifiers"]["ibm_patent_id"] != nil
          f.write("#{r["identifiers"]["ibm_patent_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["atlas_id"] != nil
          f.write("#{r["identifiers"]["atlas_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["patent_id"] != nil
          f.write("#{r["identifiers"]["patent_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["fda_srs_id"] != nil
          f.write("#{r["identifiers"]["fda_srs_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["surechem_id"] != nil
          f.write("#{r["identifiers"]["surechem_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["pharmkgb_id"] != nil
          f.write("#{r["identifiers"]["pharmkgb_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["selleck_id"] != nil
          f.write("#{r["identifiers"]["selleck_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["pubchem_thomson_id"] != nil
          f.write("#{r["identifiers"]["pubchem_thomson_id"]}\t")
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["mcule_id"] != nil
          f.write("#{r["identifiers"]["mcule_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["nmrshiftdb_id"] != nil
          f.write("#{r["identifiers"]["nmrshiftdb_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["lincs_id"] != nil
          f.write("#{r["identifiers"]["lincs_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["actor_id"] != nil
          f.write("#{r["identifiers"]["actor_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["recon_id"] != nil
          f.write("#{r["identifiers"]["recon_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["molport_id"] != nil
          f.write("#{r["identifiers"]["molport_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["nikkaji_id"] != nil
          f.write("#{r["identifiers"]["nikkaji_id"]}\t") 
        else
          f.write("NULL\t")
        end
        if r["identifiers"]["bindingdb_id"] != nil
          f.write("#{r["identifiers"]["bindingdb_id"]}\n") 
        else
         f.write("NULL\n")
        end 
        puts "#{r["structures"]["inchikey"]} written to the file"
      end
    end
    end
    f.close
    puts "Done"
  end


  desc "find number of peaks for ms_ms experimental spectra"
  task :peaks_ms_ms_experimental_spectra => [:environment] do
    f = File.open('public/MolDB_LC_POS_EXperimental_with_number_of_peak.tsv', 'w')
    CSV.open('public/MolDB_LC_POS_EXperimental.tsv', "r", :col_sep => "\t", :quote_char => "\x00").each do |row|
      puts row[2].to_i
      ms_ms = MsMs.find(row[2].to_i)
      number_of_peak = ms_ms.peaks.count
      f.write("#{row[0]}\t#{row[1]}\t#{row[2]}\t#{number_of_peak}\n")
    end
    puts "done"
    f.close
  end


  


desc " adding references for msms spectra"
  task :ref_gnps => [:environment] do
    s_id = []
    #File.readlines('public/sumner_added_spectra.tsv').each do |row|
    #File.readlines('public/gnps_added_spectra.tsv').each do |row|
    File.readlines('public/gnps_added_spectra_2.tsv').each do |row|
      s_id.push(row.gsub("\n",""))
    end
    puts s_id
    $j = 0
    #CSV.open('public/Sumner_msms_NEG_uniq.tsv', "r", :col_sep => "\t", :quote_char => "\x00").each do |row|
    #CSV.open('public/GNPS_LC_POS_uniq_final.tsv', "r", :col_sep => "\t", :quote_char => "\x00").each do |row|
    CSV.open('public/GNPS_LC_NEG_uniq_final.tsv', "r", :col_sep => "\t", :quote_char => "\x00").each do |row|
      reference = Reference.new
      reference.spectra_id = s_id[$j].to_i
      reference.spectra_type = row[5]
      reference.ref_text = row[6]
      reference.database = row[7] 
      reference.database_id = row[4]
      reference.save!
      $j += 1
      puts "done for "
    end
  end

  desc "delete wrong reference"
  task :del_ref => [:environment] do
    File.readlines('public/reference_id.tsv').each do |row|
      r = Reference.find(row.to_i)
      r.destroy
      puts "#{row} destroyed"
    end
  end


  desc "delete duplicate reference and ms_ms"
  task :del_ref_msms => [:environment] do
    CSV.open('public/ref_ms_id.tsv', "r", :col_sep => "\t", :quote_char => "\x00").each do |row|
      r = Reference.find(row[0].to_i)
      r.destroy
      msms = MsMs.find(row[1].to_i)
      msms.destroy
      puts "#{row[0]} reference and  #{row[1]} msms are destroyed"
    end
  end


  desc "changes ids for for database registration table"
  task :change_hmdb_id_registrations => :environment do
    log_filename = 'public/update_hmdb_ids_databaseregistration_20170815_log.tsv'

    log = File.open(log_filename, "w") 
    registrations = DatabaseRegistration.where('database_id LIKE ?', 'HMDB%')

    registrations.each do |registration|
      begin
        new_hmdb_id = registration.database_id.gsub('HMDB', 'HMDB00')

        registration.database_id = new_hmdb_id
        registration.save!

        puts("UPDATED: " + new_hmdb_id)
      rescue
        puts("ERROR: " + registration.database_id)
        log.write(registration.id + "\n")
      end
    end
  end

  desc "changes ids for for identifiers table"
  task :change_hmdb_id_identifiers => :environment do
    log_filename = 'public/update_hmdb_ids_identifiers_20170815_log.txt'

    log = File.open(log_filename, "w") 
    identifiers = Identifier.where.not(hmdb_id: nil)

    identifiers.each do |identifier|
      begin
        new_hmdb_id = identifier.hmdb_id.gsub('HMDB', 'HMDB00')

        identifier.hmdb_id = new_hmdb_id
        identifier.save!

        puts("UPDATED: " + new_hmdb_id)
      rescue
        puts("ERROR: " + identifier.hmdb_id)
        log.write(identifier.id + "\n")
      end
    end
  end

  desc "Import compound synonyms from SMPDB"
  # Input format: CSV with headers. Required columns are name, synonyms (multiple synonyms
  # separated by semicolons), and inchikey.
  task :synonyms_smpdb , [:csv_file_name] => [:environment] do |t, args|
    CSV.foreach(args.csv_file_name, headers: true, header_converters: :symbol) do |row|
      name = row[:name]
      synonyms_str = row[:synonyms]
      inchikey = row[:inchikey]

      puts inchikey
      structure = Structure.find_by_inchikey(inchikey)
      if structure.nil?
        puts 'No stucture exists in MolDB for ' + inchikey
        next
      end

      # Synonyms are separated by semicolons. One drawback is that sometimes chemical
      # names can include semicolons.
      synonyms = synonyms_str.split(/; */)
      synonyms.push(name)
      synonyms.each { |synonym|
        next if synonym.nil? or synonym.match(/^\s*$/)
        synonym = synonym.strip
        moldb_synonyms = structure.synonyms # Associated synonyms in the 'synonyms' MySQL DB table.
        next if moldb_synonyms.nil?
        match = false
        moldb_synonyms.each { |moldb_synonym|
          if !moldb_synonym.name.nil? and moldb_synonym.name.downcase == synonym.downcase
            match = true
            break
          end
        }
        if !match
          new_synonym = Synonym.new
          new_synonym.structure_id = structure.id
          new_synonym.name = synonym
          new_synonym.source = 'SMPDB'
          #new_synonym.kind = 'Synonym'
          #new_synonym.occurrence = 1
          new_synonym.save!
        end
      }

    end
  end

  task :delete_duplicate_synonyms => [:environment] do
    grouped_synonyms = Synonym.all.group_by{ |s| [s.name,s.structure_id] }
    grouped_synonyms.values.each do |duplicates|
      first_one = duplicates.shift
      duplicates.each do |d|
        puts d.structure_id
        puts d.name
        d.destroy!
      end
    end
  end

  desc "Import compound synonyms generated by Lipid Annotator"
  # Format: TSV file. First column in InChIKey, other columns are synonyms (tab-delimited).
  # Lipid Annotator is a Wishart Lab project, with a repo in BitBucket.
  task :synonyms_lipid_annotator , [:csv_file_name] => [:environment] do |t, args|
    CSV.foreach(args.csv_file_name, headers: false, :col_sep => "\t") do |row|
      inchikey = row[0]

      structure = Structure.find_by_inchikey(inchikey)
      if structure.nil?
        puts 'No stucture exists in MolDB for ' + inchikey
        next
      end
      puts inchikey

      i = 1
      while i < row.length do
        synonym = row[i]
        next if synonym.nil? or synonym.match(/^\s*$/)
        synonym = synonym.strip
        moldb_synonyms = structure.synonyms # Associated synonyms in the 'synonyms' MySQL DB table.
        next if moldb_synonyms.nil?
        match = false
        moldb_synonyms.each { |moldb_synonym|
          if !moldb_synonym.name.nil? and moldb_synonym.name.downcase == synonym.downcase
            match = true
            break
          end
        }
        if !match
          puts synonym
          new_synonym = Synonym.new
          new_synonym.structure_id = structure.id
          new_synonym.name = synonym
          new_synonym.source = 'Lipid Annotator'
          #new_synonym.kind = 'Synonym'
          #new_synonym.occurrence = 1
          new_synonym.save!
          puts new_synonym.id
        end

        i += 1
      end

    end
  end
end