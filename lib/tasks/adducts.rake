require 'builder'
require 'admin_mailer'
require 'progress_bar'

namespace :adducts do
  desc "Clear adducts table"
  task clear: [:environment] do
    Adduct.connection.execute("TRUNCATE TABLE adducts;")
  end

  desc "Rebuild the adducts table (caution, this deletes all adducts)"
  task rebuild: [:clear, :update]

  desc "Add adducts for new structures"
  task update: [:environment] do
    mailer = AdminMailer.build "moldb",
      subject: "Add adducts",
      message: "Add adducts for new structures."

    begin
      cores = 4
      progress = ProgressBar.new(Structure.count)
      Structure.includes(:properties).find_in_batches(batch_size: cores) do |group|
        threads = []
        group.each do |structure|
          threads << Thread.new do
            ApplicationRecord.connection_pool.with_connection do |conn|
              Adduct.create_or_update_for_structure(structure)
            end
          end
        end
        threads.map(&:join)
        progress.increment! cores
      end
      mailer.notify_success!
    rescue => e
      raise if Rails.env.development?
      mailer.notify_error! e
    end
  end

  desc "fix adducts"
  task fix: [:environment] do
    adducts_to_fix = {'M-2H2O+H' => 'M+H-2H2O', 'M-H2O+H' => 'M+H-H2O', 'M-H2O+NH4' => 'M+NH4-H2O', '2M+3H2O+2H' => '2M+2H+3H2O'}
    adducts_to_fix.each do |k,v|
      count = 0
      Adduct.where(adduct: k).find_each do |r|
        r.update_column(:adduct, v)
        count += 1 
      end
      puts "Fixed #{count} of #{k} to #{v}"
    end
  end
end
