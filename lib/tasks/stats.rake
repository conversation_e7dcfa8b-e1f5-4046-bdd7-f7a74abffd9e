require 'csv'

namespace :stats do

  task phytobank: [:environment] do
    compounds = DatabaseRegistration.where('database_id LIKE "PMC%"')
    i = 0
    compounds.first.structure.curation.json_to_class
    compounds.each do |c|
      i = i + 1 if c.structure.curation.identifiers["chemspider_id"].nil?
    end
    puts i
  end

  desc "checking chemical classification"
  task HMDB_cehm_classification: [:environment] do
  #task DrugB_cehm_classification: [:environment] do
    compounds = DatabaseRegistration.where('database_id LIKE "HMDB%"')
    #compounds = DatabaseRegistration.where('database_id LIKE "DB%"')
    #f = File.open("public/HMDB_no_chem_classification_2.tsv", "w+")
    f = File.open("public/HMDB_no_chem_classification_final_check.tsv", "w+")
    #f = File.open("public/DrugB_no_chem_classification.tsv", "w+")
    compounds[0..100].each do |c|
      puts "#{c.database_id}"
      if c.structure.curation.nil?
        f.write("#{c.database_id}\t#{c.structure.smiles}\t#{c.structure.inchikey}\t#{c.structure.inchi}\t#{c.structure.id}\t curation is nil\n")
        puts "curation was nil for #{c.database_id}"
      elsif !c.structure.curation.result.present?
        f.write("#{c.database_id}\t#{c.structure.smiles}\t#{c.structure.inchikey}\t#{c.structure.inchi}\t#{c.structure.id}\t result is not present\n")
         puts "result was nil for #{c.database_id}"
      elsif c.structure.curation.result.present?
        c.structure.curation.json_to_class
        if !c.structure.curation.classifications.present?
          puts "result present but classification was nil for #{c.database_id}"
          f.write("#{c.database_id}\t#{c.structure.smiles}\t#{c.structure.inchikey}\t#{c.structure.inchi}\t#{c.structure.id}\t classification is not present\n")
          puts "done for #{c.database_id}"
        end
      end
    end
    f.close   
  end

  desc "checking chemical classification from the job"
  task HMDB_cehm_classification_from_job_file: [:environment] do
    f = File.open("public/HMDB_no_chem_classification_from_job.tsv", "w+")
    CSV.open('public/job_id_hmdb_with_newline.tsv', 'r', :col_sep => "\t", :quote_char => "\x00").each do | row |
      c = DatabaseRegistration.where("database_id" => row[0].strip).first
      puts "#{c.database_id}"
      if c.structure.curation.nil?
        f.write("#{row[0]}\t#{row[1]}\t#{row[2]}\t#{row[3]}\t#{c.structure.id}\t curation is nil\n")
        puts "curation was nil for #{c.database_id}"
      elsif !c.structure.curation.result.present?
        f.write("#{row[0]}\t#{row[1]}\t#{row[2]}\t#{row[3]}\t#{c.structure.id}\t result is not present\n")
         puts "result was nil for #{c.database_id}"
      elsif c.structure.curation.result.present?
        c.structure.curation.json_to_class
        if !c.structure.curation.classifications.present?
          puts "result present but classification was nil for #{c.database_id}"
          f.write("#{row[0]}\t#{row[1]}\t#{row[2]}\t#{row[3]}\t#{c.structure.id}\t classification is not present\n")
          puts "done for #{c.database_id}"
        end
      end
    end
    f.close   
  end

  desc "checking identifiers"
  task HMDB_identifiers: [:environment] do
    compounds = DatabaseRegistration.where('database_id LIKE "HMDB%"')
    f = File.open("public/HMDB_no_identifiers.tsv", "w+")
    compounds.each do |c|
      puts "#{c.database_id}"
      if c.structure.curation.nil?
        puts "curation was nil for #{c.database_id}"
      elsif !c.structure.curation.result.present?
        puts "result was nil for #{c.database_id}"
      elsif c.structure.curation.result.present?
        c.structure.curation.json_to_class
        if !c.structure.curation.identifiers.present?
          puts "result present but identifiers was nil for #{c.database_id}"
          f.write("#{c.database_id}\t#{c.structure.inchikey}\t#{c.structure.id}\n")
          puts "done for #{c.database_id}"
        end
      end
    end
    f.close
  end

  desc "checking under-identified compounds"
  task HMDB_underidentified: [:environment] do
    compounds = DatabaseRegistration.where('database_id LIKE "HMDB%"')
    f1 = File.open("public/HMDB_underidentified_hmdb_id_inchikey_struc_id.tsv", "w+")
    f2 = File.open("public/HMDB_underidentified_hmdb_id_inchi.tsv", "w+")
    f3 = File.open("public/HMDB_curation_is_null.tsv", "w+")
    compounds.each do |c|
      puts "#{c.database_id}"
      if c.structure.curation.nil?
        puts "curation is nil for: #{c.database_id}"
        f3.write("#{c.database_id}\n")
      else
        if c.structure.curation.result.present?
          c.structure.curation.json_to_class
          result_ = JSON.parse(c.structure.curation.result)
          pubchem_id_ = result_["identifiers"]["pubchem_id"]
          #puts pubchem_id_
          chebi_id_ = result_["identifiers"]["chebi_id"]
          #puts chebi_id_
          kegg_id_ = result_["identifiers"]["kegg_id"]
          #puts kegg_id_
          if pubchem_id_.nil? and (chebi_id_.nil? or kegg_id_.nil?)
            f1.write("#{c.database_id}\t#{c.structure.inchikey}\t#{c.structure.id}\n")
            f2.write("#{c.database_id}\t#{c.structure.inchi}\t\n")
            puts "pubchem_id is null for #{c.database_id}"
          elsif chebi_id_.nil? and (pubchem_id_.nil? or kegg_id_.nil?)
            f1.write("#{c.database_id}\t#{c.structure.inchikey}\t#{c.structure.id}\n")
            f2.write("#{c.database_id}\t#{c.structure.inchi}\t\n")
            puts "chebi_id is null for #{c.database_id}"
          elsif kegg_id_.nil? and (chebi_id_.nil? or pubchem_id_.nil?)
            f1.write("#{c.database_id}\t#{c.structure.inchikey}\t#{c.structure.id}\n")
            f2.write("#{c.database_id}\t#{c.structure.inchi}\t\n")
            puts "kegg_id is null for #{c.database_id}"
          end
        end
      end
    end
    f1.close
    f2.close
    f3.close
  end


  desc " fetching hmdb ids with structures"
  task HMDB_structures: [:environment] do
    compounds = DatabaseRegistration.where('database_id LIKE "HMDB%"')
     f = File.open("public/HMDB_structures.tsv", "w+")
     compounds.each do | c |
      structure = c.structure
      inchikey = structure.inchikey
      parese_result = JSON.parse(structure.curation.result)
      if !parese_result["structures"]["inchi"].present?
        inchi = "N/A"
        puts inchi
      else
       inchi = parese_result["structures"]["inchi"]
      end
      if !parese_result["structures"]["smiles"].present?
        smiles = "N/A"
        puts smiles
      else
        smiles = parese_result["structures"]["smiles"]
      end
      f.write("#{c.database_id}\t#{inchi}\t#{smiles}\t#{inchikey}\n")
    end
   f.close
  end

  # Count the number of synonyms based on the curations table, for the given list of
  # input database IDs.
  #
  # Input should be a CSV file with one column having the header 'hmdb_id' (or edit the
  # code below to change the name). Elements in this column should be database IDs such as
  # HMDB IDs. No other columns are needed.
  #
  # The second argument is the output file, which will list the number of synonyms for
  # each database ID.
  #
  # The task will also print the total number of synonyms after it is done.
  #
  # To run this rake task (no space after comma!):
  #   bundle exec rake stats:synonyms_in_curations['input.csv','synonym_counts.csv']
  task :synonyms_in_curations , [:csv_file_name, :outfile] => [:environment] do |t, args|
    total_compounds = 0
    total_compounds_not_found = 0
    total_synonyms = 0
    
    File.open(args.outfile, 'w') { |out|
      CSV.foreach(args.csv_file_name, headers: true, header_converters: :symbol) do |row|
        hmdb_id = row[:hmdb_id]
        total_compounds = total_compounds + 1
      
        c = DatabaseRegistration.find_by("database_id" => hmdb_id)
        if c.nil?
          puts "Could not find #{hmdb_id}"
          total_compounds_not_found = total_compounds_not_found + 1
          next
        end
      
        #puts "#{c.database_id}"
        if c.structure.curation.nil?
          puts "curation was nil for #{c.database_id}"
        elsif !c.structure.curation.result.present?
           puts "result was nil for #{c.database_id}"
        elsif c.structure.curation.result.present?
          c.structure.curation.json_to_class
          if !c.structure.curation.synonyms.present?
            puts "result present but synonyms was nil for #{c.database_id}"
            puts "done for #{c.database_id}"
          else
            total_synonyms = total_synonyms + c.structure.curation.synonyms.count
            out.write("#{c.database_id}\t#{c.structure.curation.synonyms.count}\n")
  #           c.structure.curation.synonyms.each do |synonym|
  #             puts synonym
  #           end
          end
        end
      
      end
    }
    
    puts "Total #{total_synonyms} synonyms for #{total_compounds} compounds."
    puts "#{total_compounds_not_found} of these compounds could not be found."
    
  end

  desc "checking chemical classification"
  task count_synonyms: [:environment] do
    output = File.open("all_synonyms.txt","w")
    compounds = DatabaseRegistration.where('database_id LIKE "HMDB%"')
    total = 0
    compounds.each do |c|
      puts c.database_id
      if curation = Curation.find_by(structure_id: c.structure_id)
        result = JSON.parse(curation.result)
        synonyms = result["synonyms"]
        total += synonyms.count
        synonyms.each do |syn|
          # puts syn["name"]
          output.puts syn["name"]
        end

      end
    end
    puts total
    output.puts total
    output.close
  end

  desc "set human name for sources"
  task source_synonyms: [:environment] do
    names = Hash.new
    names["drugbank"] = "DrugBank"
    names["hmdb"] = "HMDB"
    names["t3db"] = "T3DB"
    names["foodb"] = "FooDB"
    names["phenol_explorer"] = "Phenol-Explorer"
    names["Specdb"] = "SpecDB"
    names["PhytoHUB"] = "PhytoHub"
    names["smpdb"] = "SMPDB"
    names["bmdb"] = "BMDB"
    names["phytomap"] = "Phyto Map"
    names["ExposomeExplorer"] = "Exposome-Explorer"
    names["chemdb"] = "ChemDB"
    names["PhytoHUB 2"] = "PhytoHub 2"

    # Source.all.each do |s|
    #   if names.key?(s.name)
    #     s.human_name = names[s.name]
    #   else
    #     s.human_name = s.name
    #   end
    #   s.save!
    # end

    # Synonym.all.each do |s|
    #   if !source = Source.find_by(human_name: s.source)
    #     if !source = Source.find_by(name: s.source)
    #       source = Source.new
    #       source.name = s.source
    #       source.human_name = s.source
    #       source.save!
    #     end
    #   end

    #   link = SynonymSource.new
    #   link.synonym_id = s.id
    #   link.source_id = source.id
    #   link.save!
    # end
    Curation.all.each do |curation|
      puts curation.structure_id
      # curation.save_synonyms
      curation.save!
    end

  end

  task test: [:environment] do
    ms_ms_spectra = MsMs.all
    ms_ms_spectra.each do |spectrum|


    end
    0.001
  end

end