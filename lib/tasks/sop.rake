namespace :sop do
  desc "MsMs Recfactor SOPs out"
  task :msms => [:environment] do
    MsMs.includes(:documents).each do |msms|
      msms.documents.where("description = 'Sample Preparation Protocol' or description = 'Mass Spectra Collection Protocol'").each do |d|
        sop = Sop.find_by_file_file_name(d.file_file_name)
        if sop.nil?
          sop = Sop.new
          sop.name = d.file_file_name.sub('.doc','')
          sop.file = d.file
          sop.save!
        end
        msms.sops << sop if msms.sops.where(:id => sop.id).empty?

      end
    end
  end
  desc "Clean msms SOPs"
  task :msms_clean => [:environment] do
    MsMs.includes(:documents).each do |msms|
      msms.documents.where("description = 'Sample Preparation Protocol' or description = 'Mass Spectra Collection Protocol'").each do |d|
        d.destroy
      end
    end
  end
end
