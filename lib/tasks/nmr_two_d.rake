namespace :nmr_two_d do
  # def isClose(p1, p2, thresh_x, thresh_y)
  #   x = p1.chemical_shift_x.to_f - p2.chemical_shift_x.to_f
  #   y = p1.chemical_shift_y.to_f - p2.chemical_shift_y.to_f
  #   return ((x.abs <= thresh_x) && (y.abs <= thresh_y))
  # end
  namespace :toscy do
    desc "Build uniqueness values"
    task :uniques => [:environment] do
      NmrTwoDPeak.includes(:nmr_two_d).where("nmr_two_d.nucleus_x = '1H' AND nmr_two_d.nucleus_y = '1H'").find_each do |i|
        puts i.id

        i.unique_0 = NmrTwoDPeak.joins(:nmr_two_d).where("nmr_two_d.nucleus_x = '1H' AND nmr_two_d.nucleus_y = '1H' AND chemical_shift_x #{between(i.chemical_shift_x.to_f,0.01)} AND chemical_shift_y #{between(i.chemical_shift_y.to_f,0.01)}").count - 1
        i.unique_1 = NmrTwoDPeak.joins(:nmr_two_d).where("nmr_two_d.nucleus_x = '1H' AND nmr_two_d.nucleus_y = '1H' AND chemical_shift_x #{between(i.chemical_shift_x.to_f,0.02)} AND chemical_shift_y #{between(i.chemical_shift_y.to_f,0.02)}").count - 1
        i.unique_2 = NmrTwoDPeak.joins(:nmr_two_d).where("nmr_two_d.nucleus_x = '1H' AND nmr_two_d.nucleus_y = '1H' AND chemical_shift_x #{between(i.chemical_shift_x.to_f,0.03)} AND chemical_shift_y #{between(i.chemical_shift_y.to_f,0.03)}").count - 1
        i.unique_3 = NmrTwoDPeak.joins(:nmr_two_d).where("nmr_two_d.nucleus_x = '1H' AND nmr_two_d.nucleus_y = '1H' AND chemical_shift_x #{between(i.chemical_shift_x.to_f,0.04)} AND chemical_shift_y #{between(i.chemical_shift_y.to_f,0.04)}").count - 1
        i.unique_4 = NmrTwoDPeak.joins(:nmr_two_d).where("nmr_two_d.nucleus_x = '1H' AND nmr_two_d.nucleus_y = '1H' AND chemical_shift_x #{between(i.chemical_shift_x.to_f,0.05)} AND chemical_shift_y #{between(i.chemical_shift_y.to_f,0.05)}").count - 1

        # i.unique_0 = 0
        # i.unique_1 = 0
        # i.unique_2 = 0
        # i.unique_3 = 0
        # i.unique_4 = 0

        # NmrTwoDPeak.joins(:nmr_two_d).select("nmr_two_d_peaks.*").where("nmr_two_d.nucleus_x = '1H' AND nmr_two_d.nucleus_y = '1H'").find_each do |j|
        #   next if i.id == j.id
        #   if (isClose(i, j, 0.01, 0.01))
        #     i.unique_0 += 1
        #     i.unique_1 += 1
        #     i.unique_2 += 1
        #     i.unique_3 += 1
        #     i.unique_4 += 1
        #   elsif (isClose(i, j, 0.02, 0.02))
        #     i.unique_1 += 1
        #     i.unique_2 += 1
        #     i.unique_3 += 1
        #     i.unique_4 += 1
        #   elsif (isClose(i, j, 0.03,0.03))
        #     i.unique_2 += 1
        #     i.unique_3 += 1
        #     i.unique_4 += 1
        #   elsif (isClose(i, j, 0.04,0.04))
        #     i.unique_3 += 1
        #     i.unique_4 += 1
        #   elsif (isClose(i, j, 0.05,0.05))
        #     i.unique_4 += 1
        #   end
        # end
        i.nmr_two_d.searchable = true
        i.save!
        # i.nmr_two_d.save!
      end
    end
  end
  namespace :hsqc do

    desc "Build uniqueness values"
    task :uniques => [:environment] do
      NmrTwoDPeak.joins(:nmr_two_d).select("nmr_two_d_peaks.*").where("nmr_two_d.nucleus_x = '1H' AND nmr_two_d.nucleus_y = '13C'").find_each do |i|
        puts i.id
        i.unique_0 = NmrTwoDPeak.joins(:nmr_two_d).where("nmr_two_d.nucleus_x = '1H' AND nmr_two_d.nucleus_y = '13C' AND chemical_shift_x #{between(i.chemical_shift_x.to_f,0.01)} AND chemical_shift_y #{between(i.chemical_shift_y.to_f,0.05)}").count - 1
        i.unique_1 = NmrTwoDPeak.joins(:nmr_two_d).where("nmr_two_d.nucleus_x = '1H' AND nmr_two_d.nucleus_y = '13C' AND chemical_shift_x #{between(i.chemical_shift_x.to_f,0.02)} AND chemical_shift_y #{between(i.chemical_shift_y.to_f,0.10)}").count - 1
        i.unique_2 = NmrTwoDPeak.joins(:nmr_two_d).where("nmr_two_d.nucleus_x = '1H' AND nmr_two_d.nucleus_y = '13C' AND chemical_shift_x #{between(i.chemical_shift_x.to_f,0.03)} AND chemical_shift_y #{between(i.chemical_shift_y.to_f,0.15)}").count - 1
        i.unique_3 = NmrTwoDPeak.joins(:nmr_two_d).where("nmr_two_d.nucleus_x = '1H' AND nmr_two_d.nucleus_y = '13C' AND chemical_shift_x #{between(i.chemical_shift_x.to_f,0.04)} AND chemical_shift_y #{between(i.chemical_shift_y.to_f,0.20)}").count - 1
        i.unique_4 = NmrTwoDPeak.joins(:nmr_two_d).where("nmr_two_d.nucleus_x = '1H' AND nmr_two_d.nucleus_y = '13C' AND chemical_shift_x #{between(i.chemical_shift_x.to_f,0.05)} AND chemical_shift_y #{between(i.chemical_shift_y.to_f,0.25)}").count - 1


        # i.unique_0 = 0
        # i.unique_1 = 0
        # i.unique_2 = 0
        # i.unique_3 = 0
        # i.unique_4 = 0

        # NmrTwoDPeak.joins(:nmr_two_d).select("nmr_two_d_peaks.*").where("nmr_two_d.nucleus_x = '1H' AND nmr_two_d.nucleus_y = '13C'").find_each do |j|
        #   next if i.id == j.id
        #   if (isClose(i, j, 0.01, 0.05))
        #     i.unique_0 += 1
        #     i.unique_1 += 1
        #     i.unique_2 += 1
        #     i.unique_3 += 1
        #     i.unique_4 += 1
        #   elsif (isClose(i, j, 0.02, 0.10))
        #     i.unique_1 += 1
        #     i.unique_2 += 1
        #     i.unique_3 += 1
        #     i.unique_4 += 1
        #   elsif (isClose(i, j, 0.03,0.15))
        #     i.unique_2 += 1
        #     i.unique_3 += 1
        #     i.unique_4 += 1
        #   elsif (isClose(i, j, 0.04,0.20))
        #     i.unique_3 += 1
        #     i.unique_4 += 1
        #   elsif (isClose(i, j, 0.05,0.25))
        #     i.unique_4 += 1
        #   end
        # end
        i.nmr_two_d.searchable = true
        i.save!
        # i.nmr_two_d.save!
      end
    end
  end

  def isClose(p1, p2, thresh_x, thresh_y)
    x = p1.chemical_shift_x - p2.chemical_shift_x
    y = p1.chemical_shift_y - p2.chemical_shift_y
    return ((x.abs <= thresh_x) && (y.abs <= thresh_y))
  end
end

def symetry_clean(nmr)
  sym_vec = Array.new #NMRPeak2D

  #first - split diagVec into up, and down off-diagonal
  up_vec = Array.new #Vector<NMRPeak2D>();
  down_vec = Array.new #Vector<NMRPeak2D>();
  dg_vec = Array.new #Vector<NMRPeak2D>();

  nmr.nmr2d_peaks.each do |pd|
    x = pd.chemical_shift_x
    y = pd.chemical_shift_y
    if (x - y > 0.04)
      up_vec.push(pd)
    elsif (x - y < 0.04)
      down_vec.push(pd);
    else
      dg_vec.push(pd);
    end
  end

  #second - found symmetrical peak b/w up and down diagonal peaks
  sym_map = Hash.new #HashMap<NMRPeak2D, Boolean>();
  up_vec.each do |pd1|
    down_vec.each do |pd2|
      if (is_symmetrical(pd1, pd2))
        sym_map[pd1] = true
        sym_map[pd2] = true
      end
    end
  end

  #now process the diagnal peaks
  dg_vec.each do |p|
    sym_map[p] = true
  end

  #delete all non symetrical peaks
  nmr.nmr2d_peaks.each do |p|
    p.destroy if sym_map[p].nil?
  end
end

def is_symmetrical(upCell, downCell)
  a = upCell.chemical_shift_y-downCell.chemical_shift_x
  b = upCell.chemical_shift_x-downCell.chemical_shift_y
  return ((a.abs< 0.04) && (b.abs< 0.04))
end

def between(peak, thresh)
  "BETWEEN #{peak - (thresh/2)} AND #{peak + (thresh/2)}"
end
