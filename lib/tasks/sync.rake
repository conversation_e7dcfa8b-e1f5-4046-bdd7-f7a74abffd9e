namespace :sync do
  PUBLIC_DIR = File.expand_path "../../../public", __FILE__
  SYSTEM_DIR = File.join PUBLIC_DIR, "system"

  desc 'Sync all files from production to local'
  task :all_files => [:mk_system_dir,:documents,:nmr_assignments,:ms_ms_assignments]

  desc 'Sync documents from production to local'
  task :documents => :mk_system_dir do
    dir = File.join SYSTEM_DIR, "documents"
    FileUtils.mkdir(dir) unless File.exist?( dir )
    system( "rsync -rvaz --progress  --delete <EMAIL>:/apps/specdb/project/shared/system/documents/ #{dir}" )
  end

  desc 'Sync NMR assignment files from production to local'
  task :nmr_assignments => :mk_system_dir do
    dir = File.join SYSTEM_DIR, "nmr_one_d_peak_assignments"
    FileUtils.mkdir(dir) unless File.exist?( dir )
    system( "rsync -rvaz --progress  --delete <EMAIL>:/apps/specdb/project/shared/system/nmr_one_d_peak_assignments/ #{dir}" )
  end

  desc 'Sync msms assignment files from production to local'
  task :ms_ms_assignments => :mk_system_dir do
    dir = File.join SYSTEM_DIR, "ms_ms_peak_assignments"
    FileUtils.mkdir(dir) unless File.exist?( dir )
    system( "rsync -rvaz --progress --delete <EMAIL>:/apps/specdb/project/shared/system/ms_ms_peak_assignments/ #{dir}" )
  end

  desc 'Setup system dir'
  task :mk_system_dir do
    FileUtils.mkdir( SYSTEM_DIR ) unless File.exist?( SYSTEM_DIR )
  end

end
