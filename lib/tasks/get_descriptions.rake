namespace :get do
	desc 'get descriptions for database'
	task descriptions: [:environment] do
		count = 0
		csv_file = File.open('data/bmdb_descriptions.csv', 'w')
		header = "bmdb_id\tdescription\n"
		csv_file.write(header)
		DatabaseRegistration.where("database_id like 'BMDB%' and exported = 1").each do |dbr|
		  count+=1
			bmdb_id = dbr.database_id
			str_id = dbr.structure_id
			puts "#{count} - working on: #{bmdb_id}"
			c = Curation.find_by(structure_id: str_id)
			bmdb_desc = nil
			if c.present? and !c.result.nil?
				curation_result = c.result
				my_json = JSON.parse(curation_result)
				if my_json["cs_descriptions"].present? and my_json["cs_descriptions"]["5"].present?
					bmdb_desc = my_json["cs_descriptions"]["5"]
				end
			end
			csv_file.write("#{bmdb_id}\t#{bmdb_desc}\n")
		end
	end
end
