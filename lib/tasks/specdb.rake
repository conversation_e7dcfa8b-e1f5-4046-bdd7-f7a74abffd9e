require 'tempfile'
require 'objspace'
namespace :specdb do

  desc "transfer molecules"
  task :molecules => [:environment] do
    CMs.all.each do |s|
      puts "#{s.inchi_key}"
      s.new_molecule = Molecule.where(:moldb_inchikey => "InChIKey=#{s.inchi_key}").limit(1).first
      s.save!
    end
    MsMs.all.each do |s|
      puts "#{s.inchi_key}"
      s.new_molecule = Molecule.where(:moldb_inchikey => "InChIKey=#{s.inchi_key}").limit(1).first
      s.save!
    end
    NmrOneD.all.each do |s|
      puts "#{s.inchi_key}"
      s.new_molecule = Molecule.where(:moldb_inchikey => "InChIKey=#{s.inchi_key}").limit(1).first
      s.save!
    end
    NmrTwoD.all.each do |s|
      puts "#{s.inchi_key}"
      s.new_molecule = Molecule.where(:moldb_inchikey => "InChIKey=#{s.inchi_key}").limit(1).first
      s.save!
    end
  end

  namespace :ms_ms do
    desc "Build assignment images"
    task :assignment_png => [:environment] do
      progress = ProgressBar.new MsMsPeakAssignment.count
      MsMsPeakAssignment.transaction do
        MsMsPeakAssignment.all.each do |pa|
          next if pa.assignment_view.present? && pa.assignment_view.path[-3..-1] == "png"
          tmpfile = "/tmp/" + pa.peak.ms_ms.molecule.specdb_id + "_" + pa.peak.mass_charge.to_s.gsub(/\./,'_') + '.png'
          Jchem.structure_to_png_file(pa.smiles,tmpfile)
          pa.assignment_view = File.open(tmpfile)
          pa.save!
          File.delete tmpfile
          progress.increment!
        end
      end
      progress.count = progress.max
      progress.write
    end
  end

  namespace :mzml do
    desc "Create Peak List Assignment Documents for all Spectra"
    task :create_peak_docs => [:environment] do 
      CMs.find_each(batch_size: 1000) do |gcms|
        file_path = "cms-#{gcms.id}-mz-values-"
        f = Tempfile.new([file_path,'.txt'])
        puts "gcms id: " + gcms.id.to_s
        puts gcms.peaks.length
        gcms.peaks.each do |peak|
          f.write(peak.mass_charge.to_s + " " + peak.intensity.to_s + "\n")
        end
        puts f.inspect
        f.flush
        #Document.delete_all(spectra_id: gcms.id, spectra_type: "CMs", description: "Generated list of m/z values for the spectrum")
        doc = Document.new()
        doc.spectra_id = gcms.id
        doc.spectra_type = "CMs"
        doc.description = "Generated list of m/z values for the spectrum"
        doc.file = File.open(f.path)
        f.unlink
        doc.save!
        puts doc.inspect
      end
      MsMs.find_each(batch_size: 1000)do |msms|
        file_path = "msms-#{msms.id}-mz-values.txt-"
        f = Tempfile.new(file_path)
        puts "msms id: " + msms.id.to_s
        puts msms.peaks.length
        msms.peaks.each do |peak|
          f.write(peak.mass_charge.to_s + " " + peak.intensity.to_s + "\n")
        end
        puts f.inspect
        f.flush
        #Document.delete_all(spectra_id: msms.id, spectra_type: "MsMs", description: "Generated list of m/z values for the spectrum")
        doc = Document.new()
        doc.spectra_id = msms.id
        doc.spectra_type = "MsMs"
        doc.description = "Generated list of m/z values for the spectrum"
        doc.file = File.open(f.path)
        f.unlink
        doc.save!
        puts doc.inspect
      end
      EiMs.find_each(batch_size: 1000)do |eims|
        file_path = "ms-#{eims.id}-mz-values.txt-"
        f = Tempfile.new(file_path)
        puts "eims id: " + eims.id.to_s
        puts eims.peaks.length
        eims.peaks.each do |peak|
          f.write(peak.mass_charge.to_s + " " + peak.intensity.to_s + "\n")
        end
        puts f.inspect
        f.flush
        #Document.delete_all(spectra_id: eims.id, spectra_type: "EiMs", description: "Generated list of m/z values for the spectrum")
        doc = Document.new()
        doc.spectra_id = eims.id
        doc.spectra_type = "EiMs"
        doc.description = "Generated list of m/z values for the spectrum"
        doc.file = File.open(f.path)
        f.unlink
        doc.save!
        puts doc.inspect
      end
    end
    desc "Delete all Generate peak lists"
    task :delete_peak_docs => [:environment] do
       Document.delete_all(description: "Generated list of m/z values for the spectrum")
    end

    desc "Generate Peak Lists for MSMS by Array of spectra IDS"
    task :create_peak_msms => [:environment]do
      ids = JSON.parse(ENV["spectra_ids"])
      ids.each do |id|
        msms = MsMs.find(id)
        file_path = "msms-#{msms.id}-mz-values.txt-"
        f = Tempfile.new(file_path)
        puts "msms id: " + msms.id.to_s
        puts msms.peaks.length
        msms.peaks.each do |peak|
          f.write(peak.mass_charge.to_s + " " + peak.intensity.to_s + "\n")
        end
        puts f.inspect
        f.flush
        #Document.delete_all(spectra_id: msms.id, spectra_type: "MsMs", description: "Generated list of m/z values for the spectrum")
        doc = Document.new()
        doc.spectra_id = msms.id
        doc.spectra_type = "MsMs"
        doc.description = "Generated list of m/z values for the spectrum"
        doc.file = File.open(f.path)
        f.unlink
        doc.save!
        puts doc.inspect
      end
    end
  end
end
