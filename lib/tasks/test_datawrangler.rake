namespace :test_datawrangler do
  task test00: [:environment] do
    output = DataWrangler::Annotate::Compound.annotate_by_name("1-Methylhistidine")
    puts output.properties.molecular_weight
    puts output.properties.average_mass
    puts output.properties.monoisotopic_mass
    puts output.properties.nominal_mass

  end

  task test01: [:environment] do
    output = DataWrangler::Model::ChemspiderCompound.get_by_inchikey("BRMWTNUJHUMWMS-LURJTMIESA-N")
    puts output.properties.molecular_weight
    puts output.properties.average_mass
    puts output.properties.monoisotopic_mass
    puts output.properties.nominal_mass
  end



  task test_curation: [:environment] do
    curation = Curation.find_by_id('824754')
    puts curation.json_to_class 
  end


  task test_sdf: [:environment] do
    Structure.find_in_batches() do |batch|
      batch.each do |s|
        puts s.try!(:curation).try!(:sdf_3d).present?
      end
    end
  end

  task datawrangler_duplicates:[:environment] do
    csv = CSV.open("output.csv","wb+", :headers=>true)
    #bad = CSV.open("hasan_bad.csv", "wb+")
    count = 0 
    CSV.foreach("input.csv") do |row|
      original_inchi = row[0]
      count += 1
      next if count < 804
      inchikey = DataWrangler::JChem::Convert.inchi_to_inchikey(original_inchi)
      hmdb_compound = DataWrangler::Model::HMDBCompound.get_by_inchikey(inchikey) if inchikey
      hmdb_id = hmdb_compound.identifiers.hmdb_id if hmdb_compound  
      hmdb_name = hmdb_compound.identifiers.name if hmdb_id
      JSON.parse(row[1]).each do |name,public_id|
        print (name)
        purveyor = nil
        purveyor_id = nil
        comment = nil
        status = nil
        inchi = nil
        if inchi.nil?
          comp = DataWrangler::Model::PubchemCompound.get_by_name(name)
          inchi =  comp.structures.inchi
          purveyor  = "Pubchem" if inchi
          purveyor_id = comp.identifiers.pubchem_id
        end
        if inchi.nil?
          inchi = DataWrangler::Model::CTSCompound.get_by_name(name).structures.inchi
          purveyor = "Chemical Translation Service" if inchi
          purveyor_id = nil
        end

        if inchi.nil?
          comp = DataWrangler::Model::MetaCycCompound.get_by_name(name)
          inchi = comp.structures.inchi
          purveyor = "MetaCyc"
          purveyor_id = comp.identifiers.meta_cyc_id
        end
        if original_inchi == inchi
          status = "Correct Structure"
          comment = "#{purveyor} #{purveyor_id} found this structure to equal it's moldb_inchi."
        elsif (original_inchi != inchi) && (inchi)
          status = "Wrong Structure"
          comment = "#{purveyor} #{purveyor_id} found this structure to not be correct with it's moldb_inchi."
        else
          status = "Undetermined Structure"
          comment = "Via Name lookup the structure for this compound could not be found."
        end
        if hmdb_name == name
          status = "Correct Structure"
          comment += " This compound is found in HMDB #{hmdb_id} matching on NAME."
        end
        moldb = DataWrangler::Model::MolDBCompound.get_by_id(public_id)
        actual_moldb_inchi = moldb.structures.inchi if moldb
        if (original_inchi != actual_moldb_inchi) && (actual_moldb_inchi)
          status = "Incorrect Structure"
          comment = "Moldb Miscommunication"
          inchi = actual_moldb_inchi
        end
        j_inchi = nil
        jchem = DataWrangler::JChem::Convert.name_to_inchi(name)
        j_inchi = jchem.body if jchem
        
        if j_inchi
          if j_inchi == original_inchi
            status = "Correct Structure"
            comment += "Moldb inchi matches up with Jchem Inchi"
            inchi = j_inchi
          else
            status = "Incorrect Structure"
            comment += "Moldb inchi does not match up with Jchem Inchi"
            inchi = j_inchi
          end
        end
        print [count, original_inchi, public_id, inchi, name, status, comment]
        puts nil
        csv << [count, original_inchi, public_id, inchi, name, status, comment]
      end
      puts count
      #exit(1)
    end
  end
end

