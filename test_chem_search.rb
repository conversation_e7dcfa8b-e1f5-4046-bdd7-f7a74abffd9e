#!/usr/bin/env ruby

# Simple test script for ChemSearch module
require_relative 'config/environment'

puts "Testing ChemSearch module..."

# Test 1: Empty structure
puts "\n1. Testing empty structure:"
result = ChemSearch.search("", nil, {})
puts "  Empty structure result: #{result.total_count} hits"

# Test 2: Nil structure
puts "\n2. Testing nil structure:"
result = ChemSearch.search(nil, nil, {})
puts "  Nil structure result: #{result.total_count} hits"

# Test 3: Check if we have any structures in the database
puts "\n3. Checking database structures:"
structure_count = Structure.count
puts "  Total structures in database: #{structure_count}"

if structure_count > 0
  # Get a sample structure
  sample_structure = Structure.first
  puts "  Sample structure ID: #{sample_structure.id}"
  puts "  Sample structure SMILES: #{sample_structure.original_structure}"
  puts "  Sample structure formula: #{sample_structure.formula}"
  
  # Test 4: Try exact search with sample structure
  puts "\n4. Testing exact search with sample structure:"
  begin
    result = ChemSearch.search(sample_structure.original_structure, nil, { search_type: :exact })
    puts "  Exact search result: #{result.total_count} hits"
    
    if result.hits.any?
      hit = result.hits.first
      puts "  First hit cd_id: #{hit[:cd_id]}"
      puts "  First hit formula: #{hit[:cd_formula]}"
      puts "  First hit similarity: #{hit[:similarity]}"
    end
  rescue => e
    puts "  Error in exact search: #{e.message}"
    puts "  This might be expected if RDKit is not properly configured"
  end
  
  # Test 5: Try similarity search
  puts "\n5. Testing similarity search:"
  begin
    result = ChemSearch.search(sample_structure.original_structure, nil, { 
      search_type: :similarity, 
      similarity: 0.5 
    })
    puts "  Similarity search result: #{result.total_count} hits"
  rescue => e
    puts "  Error in similarity search: #{e.message}"
  end
  
  # Test 6: Test filtering
  puts "\n6. Testing molecular weight filtering:"
  begin
    result = ChemSearch.search(sample_structure.original_structure, nil, { 
      search_type: :exact,
      min_weight: 50,
      max_weight: 200
    })
    puts "  Filtered search result: #{result.total_count} hits"
  rescue => e
    puts "  Error in filtered search: #{e.message}"
  end
else
  puts "  No structures found in database - cannot test search functionality"
end

# Test 7: Check if Jchem.run_rdkit is available
puts "\n7. Testing RDKit availability:"
begin
  script = <<~PYTHON
    import sys
    from rdkit import Chem
    print("RDKit is available")
  PYTHON
  
  result = Jchem.run_rdkit(script, "")
  puts "  RDKit test result: #{result}"
rescue => e
  puts "  RDKit test error: #{e.message}"
  puts "  This indicates RDKit may not be properly configured"
end

puts "\nChemSearch testing complete!"
