#!/usr/bin/env ruby

# Simple test to check if RDKit is available through the Jchem.run_rdkit method
require_relative 'lib/jchem'

puts "Testing RDKit availability through Jchem.run_rdkit..."

# Test 1: Basic RDKit import
script1 = <<~PYTHON
  try:
      from rdkit import Chem
      print("RDKit import successful")
  except ImportError as e:
      print(f"RDKit import failed: {e}")
PYTHON

puts "\n1. Testing RDKit import:"
begin
  result1 = Jchem.run_rdkit(script1, "")
  puts "Result: #{result1}"
rescue => e
  puts "Error: #{e.message}"
end

# Test 2: Test structure parsing
test_structure = "[H]C1=C([H])C([H])=C2C(=C1[H])N1C(=O)C([H])([H])[C@]3([H])OC([H])([H])C([H])=C4C([H])([H])N5C([H])([H])C([H])([H])[C@]22[C@]5([H])C([H])([H])[C@]4([H])[C@]3([H])[C@]12[H]"

script2 = <<~PYTHON
  import sys
  try:
      from rdkit import Chem
      
      structure = "#{test_structure}"
      print(f"Testing structure: {structure[:50]}...")
      
      # Try to parse as SMILES
      mol = Chem.MolFromSmiles(structure)
      if mol is not None:
          print("Successfully parsed as SMILES")
          canonical = Chem.MolToSmiles(mol, canonical=True)
          print(f"Canonical SMILES: {canonical}")
      else:
          print("Failed to parse as SMILES")
          
          # Try as MOL block
          mol = Chem.MolFromMolBlock(structure)
          if mol is not None:
              print("Successfully parsed as MOL block")
          else:
              print("Failed to parse as MOL block")
              print("Structure format not recognized")
              
  except Exception as e:
      print(f"Error: {e}")
PYTHON

puts "\n2. Testing structure parsing:"
begin
  result2 = Jchem.run_rdkit(script2, "")
  puts "Result: #{result2}"
rescue => e
  puts "Error: #{e.message}"
end

# Test 3: Test a simple known SMILES
script3 = <<~PYTHON
  try:
      from rdkit import Chem
      from rdkit.Chem import rdMolDescriptors
      
      # Test with a simple known SMILES
      simple_smiles = "CCO"  # Ethanol
      print(f"Testing simple SMILES: {simple_smiles}")
      
      mol = Chem.MolFromSmiles(simple_smiles)
      if mol is not None:
          print("Simple SMILES parsed successfully")
          canonical = Chem.MolToSmiles(mol, canonical=True)
          print(f"Canonical: {canonical}")
          
          # Test fingerprint
          fp = rdMolDescriptors.GetMorganFingerprintAsBitVect(mol, 2, nBits=2048)
          print(f"Fingerprint generated: {fp.GetNumOnBits()} bits set")
      else:
          print("Failed to parse simple SMILES")
          
  except Exception as e:
      print(f"Error: {e}")
PYTHON

puts "\n3. Testing simple SMILES:"
begin
  result3 = Jchem.run_rdkit(script3, "")
  puts "Result: #{result3}"
rescue => e
  puts "Error: #{e.message}"
end

puts "\nTest complete!"
